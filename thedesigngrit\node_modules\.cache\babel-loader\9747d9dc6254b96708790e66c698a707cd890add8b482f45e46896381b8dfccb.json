{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\brandingPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Dialog, Menu, MenuItem, TextField, IconButton, Backdrop, Typography, Button, CircularProgress } from \"@mui/material\";\nimport axios from \"axios\";\nimport { useVendor } from \"../../utils/vendorContext\";\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\nimport { MdOutlineModeEdit } from \"react-icons/md\";\nimport <PERSON><PERSON><PERSON> from \"react-easy-crop\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getCroppedImg = (imageSrc, croppedAreaPixels) => {\n  return new Promise(resolve => {\n    const image = new Image();\n    image.src = imageSrc;\n    image.onload = () => {\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = croppedAreaPixels.width;\n      canvas.height = croppedAreaPixels.height;\n      const ctx = canvas.getContext(\"2d\");\n      ctx.drawImage(image, croppedAreaPixels.x, croppedAreaPixels.y, croppedAreaPixels.width, croppedAreaPixels.height, 0, 0, croppedAreaPixels.width, croppedAreaPixels.height);\n      canvas.toBlob(blob => resolve(blob), \"image/jpeg\");\n    };\n  });\n};\nconst BrandingPage = () => {\n  _s();\n  const [catalogs, setCatalogs] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedCatalog, setSelectedCatalog] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [brandData, setBrandData] = useState(null);\n  const [logoFile, setLogoFile] = useState(null);\n  const [coverFile, setCoverFile] = useState(null);\n  const [openLogoModal, setOpenLogoModal] = useState(false);\n  const [openCoverModal, setOpenCoverModal] = useState(false);\n  const [previewLogo, setPreviewLogo] = useState(null);\n  const [previewCover, setPreviewCover] = useState(null);\n  const [formData, setFormData] = useState({\n    title: \"\",\n    year: \"\",\n    model: \"\",\n    type: \"\",\n    pdf: null,\n    image: null\n  });\n\n  // Add new state variables for cropping\n  const [showCropModal, setShowCropModal] = useState(false);\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [crop, setCrop] = useState({\n    x: 0,\n    y: 0\n  });\n  const [zoom, setZoom] = useState(1);\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\n  const [currentCropType, setCurrentCropType] = useState(null); // 'logo' or 'cover'\n\n  const {\n    vendor\n  } = useVendor();\n  const brandId = vendor === null || vendor === void 0 ? void 0 : vendor.brandId;\n  useEffect(() => {\n    axios.get(`https://api.thedesigngrit.com/api/catalogs/${brandId}`).then(res => setCatalogs(res.data)).catch(err => console.error(\"Error fetching catalogs:\", err));\n    // Fetch brand data (logo and cover)\n    axios.get(`https://api.thedesigngrit.com/api/brand/${brandId}`).then(res => {\n      setBrandData(res.data);\n      if (res.data.brandlogo) {\n        setPreviewLogo(`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${res.data.brandlogo}`);\n      }\n      if (res.data.coverPhoto) {\n        setPreviewCover(`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${res.data.coverPhoto}`);\n      }\n    }).catch(err => console.error(\"Error fetching brand data:\", err));\n  }, [brandId, brandData]);\n  const handleOpenDialog = () => setOpenDialog(true);\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCatalog(null);\n    setFormData({\n      title: \"\",\n      year: \"\",\n      model: \"\",\n      type: \"\",\n      pdf: null,\n      image: null\n    });\n  };\n  const handleOpenLogoModal = () => setOpenLogoModal(true);\n  const handleCloseLogoModal = () => setOpenLogoModal(false);\n  const handleOpenCoverModal = () => setOpenCoverModal(true);\n  const handleCloseCoverModal = () => setOpenCoverModal(false);\n  const handleLogoChange = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = () => {\n      setSelectedImageSrc(reader.result);\n      setPendingFile(file);\n      setCurrentCropType(\"logo\");\n      setShowCropModal(true);\n      setOpenLogoModal(true);\n    };\n    reader.readAsDataURL(file);\n  };\n  const handleCoverChange = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = () => {\n      setSelectedImageSrc(reader.result);\n      setPendingFile(file);\n      setCurrentCropType(\"cover\");\n      setShowCropModal(true);\n      setOpenCoverModal(true);\n    };\n    reader.readAsDataURL(file);\n  };\n  const handleCropComplete = async () => {\n    if (!selectedImageSrc || !croppedAreaPixels) return;\n    try {\n      const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\n      const croppedFile = new File([blob], pendingFile.name, {\n        type: \"image/jpeg\"\n      });\n      const croppedUrl = URL.createObjectURL(blob);\n      if (currentCropType === \"logo\") {\n        setLogoFile(croppedFile);\n        setPreviewLogo(croppedUrl);\n      } else {\n        setCoverFile(croppedFile);\n        setPreviewCover(croppedUrl);\n      }\n      setShowCropModal(false);\n      setPendingFile(null);\n      setSelectedImageSrc(null);\n      setCurrentCropType(null);\n    } catch (error) {\n      console.error(\"Error cropping image:\", error);\n    }\n  };\n  const handleBrandImageUpload = async () => {\n    if (!logoFile && !coverFile) return;\n    const formData = new FormData();\n    if (logoFile) formData.append(\"brandlogo\", logoFile);\n    if (coverFile) formData.append(\"coverPhoto\", coverFile);\n    try {\n      setLoading(true);\n      const res = await axios.put(`https://api.thedesigngrit.com/api/brand/brands/${brandId}/update-images`, formData, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      setBrandData(res.data);\n      setLogoFile(null);\n      setCoverFile(null);\n      handleCloseLogoModal();\n      handleCloseCoverModal();\n    } catch (error) {\n      console.error(\"Failed to update brand images:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFileChange = (e, type) => {\n    const file = e.target.files[0];\n    if (!file) return;\n    setFormData(prevData => ({\n      ...prevData,\n      [type]: file\n    }));\n  };\n  const handleInputChange = e => {\n    setFormData(prevData => ({\n      ...prevData,\n      [e.target.name]: e.target.value\n    }));\n  };\n  const handleUpload = async () => {\n    const data = new FormData();\n    if (formData.pdf) data.append(\"pdf\", formData.pdf);\n    if (formData.image) data.append(\"image\", formData.image);\n    data.append(\"title\", formData.title);\n    data.append(\"year\", formData.year);\n    data.append(\"model\", formData.model);\n    data.append(\"type\", formData.type);\n    data.append(\"brandId\", brandId);\n    setLoading(true);\n    const url = selectedCatalog ? `https://api.thedesigngrit.com/api/catalogs/${selectedCatalog.id}` : `https://api.thedesigngrit.com/api/catalogs/upload`;\n    const method = selectedCatalog ? \"put\" : \"post\";\n    try {\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      if (selectedCatalog) {\n        setCatalogs(catalogs.map(c => c.id === selectedCatalog.id ? response.data.catalog : c));\n      } else {\n        setCatalogs([...catalogs, {\n          ...formData,\n          id: response.data.id\n        }]);\n      }\n      handleCloseDialog();\n    } catch (error) {\n      console.error(\"Upload failed:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMenuOpen = (event, catalog) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedCatalog(catalog);\n  };\n  const handleMenuClose = () => setAnchorEl(null);\n  const handleEdit = () => {\n    setFormData(selectedCatalog);\n    setOpenDialog(true);\n    handleMenuClose();\n  };\n  const handleDelete = async () => {\n    try {\n      await axios.delete(`https://api.thedesigngrit.com/api/catalogs/${selectedCatalog.id}`);\n      setCatalogs(catalogs.filter(c => c.id !== selectedCatalog.id));\n    } catch (error) {\n      console.error(\"Error deleting catalog:\", error);\n    }\n    handleMenuClose();\n  };\n\n  // Update the Logo Edit Modal\n  const LogoEditModal = /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openLogoModal,\n    onClose: handleCloseLogoModal,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        width: \"400px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2\n        },\n        children: \"Edit Brand Logo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), previewLogo && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: previewLogo,\n        alt: \"Current Logo\",\n        style: {\n          width: \"100%\",\n          height: \"auto\",\n          maxHeight: \"300px\",\n          objectFit: \"contain\",\n          marginBottom: \"16px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: handleLogoChange,\n        style: {\n          marginBottom: \"16px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseLogoModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleBrandImageUpload,\n          disabled: !logoFile || loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 24\n          }, this) : \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n\n  // Update the Cover Edit Modal\n  const CoverEditModal = /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openCoverModal,\n    onClose: handleCloseCoverModal,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        width: \"400px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2\n        },\n        children: \"Edit Cover Photo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), previewCover && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: previewCover,\n        alt: \"Current Cover\",\n        style: {\n          width: \"100%\",\n          height: \"auto\",\n          maxHeight: \"300px\",\n          objectFit: \"contain\",\n          marginBottom: \"16px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: handleCoverChange,\n        style: {\n          marginBottom: \"16px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseCoverModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleBrandImageUpload,\n          disabled: !coverFile || loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 24\n          }, this) : \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"branding-page-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header-title\",\n      style: {\n        marginBottom: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          flexDirection: \"row\",\n          gap: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Branding\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"12px\",\n          fontFamily: \"Montserrat\"\n        },\n        children: \"Home > Branding\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        backgroundColor: \"#fff\",\n        p: 3,\n        mb: 3,\n        borderRadius: \"10px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontFamily: \"Horizon\",\n          mb: 2\n        },\n        children: \"Brand Logo & Cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"relative\",\n          height: \"300px\",\n          width: \"100%\",\n          borderRadius: \"8px\",\n          mb: 8\n        },\n        children: [previewCover ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: previewCover,\n          alt: \"Cover\",\n          style: {\n            width: \"100%\",\n            height: \"100%\",\n            objectFit: \"cover\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: \"100%\",\n            height: \"100%\",\n            backgroundColor: \"#f0f2f5\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            children: \"No cover photo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          sx: {\n            position: \"absolute\",\n            top: 16,\n            right: 16,\n            backgroundColor: \"rgba(255,255,255,0.8)\",\n            \"&:hover\": {\n              backgroundColor: \"#2d2d2d !important\"\n            }\n          },\n          onClick: handleOpenCoverModal,\n          children: /*#__PURE__*/_jsxDEV(MdOutlineModeEdit, {\n            color: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"absolute\",\n            bottom: -64,\n            left: 24,\n            width: 128,\n            height: 128,\n            borderRadius: \"50%\",\n            border: \"4px solid white\",\n            backgroundColor: \"white\"\n          },\n          children: [previewLogo ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: previewLogo,\n            alt: \"Logo\",\n            style: {\n              width: \"100%\",\n              height: \"100%\",\n              borderRadius: \"50%\",\n              objectFit: \"cover\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: \"100%\",\n              height: \"100%\",\n              backgroundColor: \"#f0f2f5\",\n              borderRadius: \"50%\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              children: \"No logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            sx: {\n              position: \"absolute\",\n              bottom: 8,\n              right: 8,\n              backgroundColor: \"rgba(255,255,255,0.8)\",\n              \"&:hover\": {\n                backgroundColor: \"#2d2d2d !important\"\n              }\n            },\n            onClick: handleOpenLogoModal,\n            children: /*#__PURE__*/_jsxDEV(MdOutlineModeEdit, {\n              color: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        backgroundColor: \"#fff\",\n        p: 3,\n        mb: 3,\n        borderRadius: \"10px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontFamily: \"Horizon\",\n          mb: 2\n        },\n        children: \"Catalogs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"submit-btn\",\n        onClick: handleOpenDialog,\n        style: {\n          \"&:hover\": {\n            backgroundColor: \"#2d2d2d !important\"\n          }\n        },\n        children: \"Upload Catalog\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          gap: \"10px\",\n          overflowX: \"auto\",\n          marginTop: \"20px\"\n        },\n        children: catalogs.map(catalog => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: `https://pub-8c9ce55fbad6475eb1afe9472bd396e0.r2.dev/${catalog.pdf}`,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: \"180px\",\n                height: \"220px\",\n                backgroundImage: `url(https://pub-8c9ce55fbad6475eb1afe9472bd396e0.r2.dev/${catalog.image})`,\n                backgroundSize: \"cover\",\n                borderRadius: \"8px\",\n                boxShadow: \"0 4px 10px rgba(0,0,0,0.2)\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: \"#fff\",\n                  color: \"#2d2d2d\",\n                  padding: \"5px 10px\",\n                  borderRadius: \"5px\",\n                  fontSize: \"14px\",\n                  fontFamily: \"Montserrat\",\n                  boxShadow: \"0 2px 6px rgba(0,0,0,0.2)\"\n                },\n                children: catalog.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            sx: {\n              position: \"absolute\",\n              top: 5,\n              right: 5,\n              \"&:hover\": {\n                backgroundColor: \"#2d2d2d !important\"\n              }\n            },\n            onClick: e => handleMenuOpen(e, catalog),\n            children: /*#__PURE__*/_jsxDEV(BsThreeDotsVertical, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this)]\n        }, catalog.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3,\n          width: \"400px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            textAlign: \"center\",\n            fontFamily: \"Horizon\",\n            color: \"#2d2d2d\"\n          },\n          children: selectedCatalog ? \"Edit Catalog\" : \"Upload Catalog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Title\",\n          name: \"title\",\n          value: formData.title,\n          onChange: handleInputChange,\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Year\",\n          name: \"year\",\n          value: formData.year,\n          onChange: handleInputChange,\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Model\",\n          name: \"model\",\n          value: formData.model,\n          onChange: handleInputChange,\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Type\",\n          name: \"type\",\n          value: formData.type,\n          onChange: handleInputChange,\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Upload PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            accept: \"application/pdf\",\n            onChange: e => handleFileChange(e, \"pdf\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Upload Cover Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: e => handleFileChange(e, \"image\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"submit-btn\",\n          onClick: handleUpload,\n          children: selectedCatalog ? \"Save Changes\" : \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 7\n    }, this), LogoEditModal, CoverEditModal, showCropModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: \"rgba(0, 0, 0, 0.75)\",\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        zIndex: 9999 // Higher than the Dialog component\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: \"white\",\n          padding: \"20px\",\n          borderRadius: \"8px\",\n          width: \"90%\",\n          maxWidth: \"800px\",\n          maxHeight: \"90vh\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"relative\",\n            height: \"400px\",\n            width: \"100%\",\n            background: \"#333\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Cropper, {\n            image: selectedImageSrc,\n            crop: crop,\n            zoom: zoom,\n            aspect: currentCropType === \"logo\" ? 1 : 16 / 5,\n            onCropChange: setCrop,\n            onZoomChange: setZoom,\n            onCropComplete: (_, area) => setCroppedAreaPixels(area)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"flex-end\",\n            gap: \"10px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCropComplete,\n            style: {\n              padding: \"8px 16px\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              fontFamily: \"Montserrat, sans-serif\",\n              backgroundColor: \"#8A9A5B\",\n              color: \"white\"\n            },\n            children: \"Crop Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowCropModal(false);\n              setPendingFile(null);\n              setSelectedImageSrc(null);\n              setCurrentCropType(null);\n            },\n            style: {\n              padding: \"8px 16px\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              fontFamily: \"Montserrat, sans-serif\",\n              backgroundColor: \"#f0f0f0\",\n              color: \"#333\"\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleEdit,\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleDelete,\n        children: \"Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Backdrop, {\n      sx: {\n        color: \"#fff\",\n        zIndex: 9999\n      },\n      open: loading,\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        color: \"inherit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 751,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 750,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n};\n_s(BrandingPage, \"9y+5IwW6KYHWC8+yIRAUDd/32Hk=\", false, function () {\n  return [useVendor];\n});\n_c = BrandingPage;\nexport default BrandingPage;\nvar _c;\n$RefreshReg$(_c, \"BrandingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Dialog", "<PERSON><PERSON>", "MenuItem", "TextField", "IconButton", "Backdrop", "Typography", "<PERSON><PERSON>", "CircularProgress", "axios", "useVendor", "BsThreeDotsVertical", "MdOutlineModeEdit", "C<PERSON>per", "jsxDEV", "_jsxDEV", "getCroppedImg", "imageSrc", "croppedAreaPixels", "Promise", "resolve", "image", "Image", "src", "onload", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "drawImage", "x", "y", "toBlob", "blob", "BrandingPage", "_s", "catalogs", "setCatalogs", "openDialog", "setOpenDialog", "anchorEl", "setAnchorEl", "selectedCatalog", "setSelectedCatalog", "loading", "setLoading", "brandData", "setBrandData", "logoFile", "setLogoFile", "coverFile", "setCoverFile", "openLogoModal", "setOpenLogoModal", "openCoverModal", "setOpenCoverModal", "previewLogo", "setPreviewLogo", "previewCover", "setPreviewCover", "formData", "setFormData", "title", "year", "model", "type", "pdf", "showCropModal", "setShowCropModal", "selectedImageSrc", "setSelectedImageSrc", "pendingFile", "setPendingFile", "crop", "setCrop", "zoom", "setZoom", "setCroppedAreaPixels", "currentCropType", "setCurrentCropType", "vendor", "brandId", "get", "then", "res", "data", "catch", "err", "console", "error", "brandlogo", "coverPhoto", "handleOpenDialog", "handleCloseDialog", "handleOpenLogoModal", "handleCloseLogoModal", "handleOpenCoverModal", "handleCloseCoverModal", "handleLogoChange", "e", "file", "target", "files", "reader", "FileReader", "result", "readAsDataURL", "handleCoverChange", "handleCropComplete", "croppedFile", "File", "name", "croppedUrl", "URL", "createObjectURL", "handleBrandImageUpload", "FormData", "append", "put", "headers", "handleFileChange", "prevData", "handleInputChange", "value", "handleUpload", "url", "id", "method", "response", "map", "c", "catalog", "handleMenuOpen", "event", "currentTarget", "handleMenuClose", "handleEdit", "handleDelete", "delete", "filter", "LogoEditModal", "open", "onClose", "children", "sx", "p", "variant", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alt", "style", "maxHeight", "objectFit", "marginBottom", "accept", "onChange", "display", "justifyContent", "gap", "onClick", "disabled", "size", "CoverEditModal", "className", "alignItems", "flexDirection", "fontSize", "fontFamily", "backgroundColor", "borderRadius", "position", "top", "right", "color", "bottom", "left", "border", "overflowX", "marginTop", "href", "rel", "backgroundImage", "backgroundSize", "boxShadow", "padding", "textAlign", "fullWidth", "label", "mt", "zIndex", "background", "max<PERSON><PERSON><PERSON>", "aspect", "onCropChange", "onZoomChange", "onCropComplete", "_", "area", "cursor", "Boolean", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/brandingPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Dialog,\r\n  Menu,\r\n  MenuItem,\r\n  TextField,\r\n  IconButton,\r\n  Backdrop,\r\n  Typography,\r\n  Button,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport axios from \"axios\";\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\r\nimport { MdOutlineModeEdit } from \"react-icons/md\";\r\nimport Cropper from \"react-easy-crop\";\r\n\r\nconst getCroppedImg = (imageSrc, croppedAreaPixels) => {\r\n  return new Promise((resolve) => {\r\n    const image = new Image();\r\n    image.src = imageSrc;\r\n    image.onload = () => {\r\n      const canvas = document.createElement(\"canvas\");\r\n      canvas.width = croppedAreaPixels.width;\r\n      canvas.height = croppedAreaPixels.height;\r\n      const ctx = canvas.getContext(\"2d\");\r\n\r\n      ctx.drawImage(\r\n        image,\r\n        croppedAreaPixels.x,\r\n        croppedAreaPixels.y,\r\n        croppedAreaPixels.width,\r\n        croppedAreaPixels.height,\r\n        0,\r\n        0,\r\n        croppedAreaPixels.width,\r\n        croppedAreaPixels.height\r\n      );\r\n\r\n      canvas.toBlob((blob) => resolve(blob), \"image/jpeg\");\r\n    };\r\n  });\r\n};\r\n\r\nconst BrandingPage = () => {\r\n  const [catalogs, setCatalogs] = useState([]);\r\n  const [openDialog, setOpenDialog] = useState(false);\r\n  const [anchorEl, setAnchorEl] = useState(null);\r\n  const [selectedCatalog, setSelectedCatalog] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [brandData, setBrandData] = useState(null);\r\n  const [logoFile, setLogoFile] = useState(null);\r\n  const [coverFile, setCoverFile] = useState(null);\r\n  const [openLogoModal, setOpenLogoModal] = useState(false);\r\n  const [openCoverModal, setOpenCoverModal] = useState(false);\r\n  const [previewLogo, setPreviewLogo] = useState(null);\r\n  const [previewCover, setPreviewCover] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    title: \"\",\r\n    year: \"\",\r\n    model: \"\",\r\n    type: \"\",\r\n    pdf: null,\r\n    image: null,\r\n  });\r\n\r\n  // Add new state variables for cropping\r\n  const [showCropModal, setShowCropModal] = useState(false);\r\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\r\n  const [pendingFile, setPendingFile] = useState(null);\r\n  const [crop, setCrop] = useState({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\r\n  const [currentCropType, setCurrentCropType] = useState(null); // 'logo' or 'cover'\r\n\r\n  const { vendor } = useVendor();\r\n  const brandId = vendor?.brandId;\r\n\r\n  useEffect(() => {\r\n    axios\r\n      .get(`https://api.thedesigngrit.com/api/catalogs/${brandId}`)\r\n      .then((res) => setCatalogs(res.data))\r\n      .catch((err) => console.error(\"Error fetching catalogs:\", err));\r\n    // Fetch brand data (logo and cover)\r\n    axios\r\n      .get(`https://api.thedesigngrit.com/api/brand/${brandId}`)\r\n      .then((res) => {\r\n        setBrandData(res.data);\r\n        if (res.data.brandlogo) {\r\n          setPreviewLogo(\r\n            `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${res.data.brandlogo}`\r\n          );\r\n        }\r\n        if (res.data.coverPhoto) {\r\n          setPreviewCover(\r\n            `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${res.data.coverPhoto}`\r\n          );\r\n        }\r\n      })\r\n      .catch((err) => console.error(\"Error fetching brand data:\", err));\r\n  }, [brandId, brandData]);\r\n\r\n  const handleOpenDialog = () => setOpenDialog(true);\r\n  const handleCloseDialog = () => {\r\n    setOpenDialog(false);\r\n    setSelectedCatalog(null);\r\n    setFormData({\r\n      title: \"\",\r\n      year: \"\",\r\n      model: \"\",\r\n      type: \"\",\r\n      pdf: null,\r\n      image: null,\r\n    });\r\n  };\r\n  const handleOpenLogoModal = () => setOpenLogoModal(true);\r\n  const handleCloseLogoModal = () => setOpenLogoModal(false);\r\n  const handleOpenCoverModal = () => setOpenCoverModal(true);\r\n  const handleCloseCoverModal = () => setOpenCoverModal(false);\r\n  const handleLogoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      setSelectedImageSrc(reader.result);\r\n      setPendingFile(file);\r\n      setCurrentCropType(\"logo\");\r\n      setShowCropModal(true);\r\n      setOpenLogoModal(true);\r\n    };\r\n    reader.readAsDataURL(file);\r\n  };\r\n\r\n  const handleCoverChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      setSelectedImageSrc(reader.result);\r\n      setPendingFile(file);\r\n      setCurrentCropType(\"cover\");\r\n      setShowCropModal(true);\r\n      setOpenCoverModal(true);\r\n    };\r\n    reader.readAsDataURL(file);\r\n  };\r\n\r\n  const handleCropComplete = async () => {\r\n    if (!selectedImageSrc || !croppedAreaPixels) return;\r\n\r\n    try {\r\n      const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\r\n      const croppedFile = new File([blob], pendingFile.name, {\r\n        type: \"image/jpeg\",\r\n      });\r\n      const croppedUrl = URL.createObjectURL(blob);\r\n\r\n      if (currentCropType === \"logo\") {\r\n        setLogoFile(croppedFile);\r\n        setPreviewLogo(croppedUrl);\r\n      } else {\r\n        setCoverFile(croppedFile);\r\n        setPreviewCover(croppedUrl);\r\n      }\r\n\r\n      setShowCropModal(false);\r\n      setPendingFile(null);\r\n      setSelectedImageSrc(null);\r\n      setCurrentCropType(null);\r\n    } catch (error) {\r\n      console.error(\"Error cropping image:\", error);\r\n    }\r\n  };\r\n\r\n  const handleBrandImageUpload = async () => {\r\n    if (!logoFile && !coverFile) return;\r\n\r\n    const formData = new FormData();\r\n    if (logoFile) formData.append(\"brandlogo\", logoFile);\r\n    if (coverFile) formData.append(\"coverPhoto\", coverFile);\r\n\r\n    try {\r\n      setLoading(true);\r\n      const res = await axios.put(\r\n        `https://api.thedesigngrit.com/api/brand/brands/${brandId}/update-images`,\r\n        formData,\r\n        { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n      );\r\n      setBrandData(res.data);\r\n      setLogoFile(null);\r\n      setCoverFile(null);\r\n      handleCloseLogoModal();\r\n      handleCloseCoverModal();\r\n    } catch (error) {\r\n      console.error(\"Failed to update brand images:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handleFileChange = (e, type) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    setFormData((prevData) => ({ ...prevData, [type]: file }));\r\n  };\r\n  const handleInputChange = (e) => {\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      [e.target.name]: e.target.value,\r\n    }));\r\n  };\r\n\r\n  const handleUpload = async () => {\r\n    const data = new FormData();\r\n    if (formData.pdf) data.append(\"pdf\", formData.pdf);\r\n    if (formData.image) data.append(\"image\", formData.image);\r\n    data.append(\"title\", formData.title);\r\n    data.append(\"year\", formData.year);\r\n    data.append(\"model\", formData.model);\r\n    data.append(\"type\", formData.type);\r\n    data.append(\"brandId\", brandId);\r\n    setLoading(true);\r\n\r\n    const url = selectedCatalog\r\n      ? `https://api.thedesigngrit.com/api/catalogs/${selectedCatalog.id}`\r\n      : `https://api.thedesigngrit.com/api/catalogs/upload`;\r\n\r\n    const method = selectedCatalog ? \"put\" : \"post\";\r\n\r\n    try {\r\n      const response = await axios({\r\n        method,\r\n        url,\r\n        data,\r\n        headers: { \"Content-Type\": \"multipart/form-data\" },\r\n      });\r\n\r\n      if (selectedCatalog) {\r\n        setCatalogs(\r\n          catalogs.map((c) =>\r\n            c.id === selectedCatalog.id ? response.data.catalog : c\r\n          )\r\n        );\r\n      } else {\r\n        setCatalogs([...catalogs, { ...formData, id: response.data.id }]);\r\n      }\r\n\r\n      handleCloseDialog();\r\n    } catch (error) {\r\n      console.error(\"Upload failed:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleMenuOpen = (event, catalog) => {\r\n    setAnchorEl(event.currentTarget);\r\n    setSelectedCatalog(catalog);\r\n  };\r\n\r\n  const handleMenuClose = () => setAnchorEl(null);\r\n\r\n  const handleEdit = () => {\r\n    setFormData(selectedCatalog);\r\n    setOpenDialog(true);\r\n    handleMenuClose();\r\n  };\r\n\r\n  const handleDelete = async () => {\r\n    try {\r\n      await axios.delete(\r\n        `https://api.thedesigngrit.com/api/catalogs/${selectedCatalog.id}`\r\n      );\r\n      setCatalogs(catalogs.filter((c) => c.id !== selectedCatalog.id));\r\n    } catch (error) {\r\n      console.error(\"Error deleting catalog:\", error);\r\n    }\r\n    handleMenuClose();\r\n  };\r\n\r\n  // Update the Logo Edit Modal\r\n  const LogoEditModal = (\r\n    <Dialog open={openLogoModal} onClose={handleCloseLogoModal}>\r\n      <Box sx={{ p: 3, width: \"400px\" }}>\r\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>\r\n          Edit Brand Logo\r\n        </Typography>\r\n\r\n        {previewLogo && (\r\n          <img\r\n            src={previewLogo}\r\n            alt=\"Current Logo\"\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"auto\",\r\n              maxHeight: \"300px\",\r\n              objectFit: \"contain\",\r\n              marginBottom: \"16px\",\r\n            }}\r\n          />\r\n        )}\r\n\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleLogoChange}\r\n          style={{ marginBottom: \"16px\" }}\r\n        />\r\n\r\n        <Box sx={{ display: \"flex\", justifyContent: \"flex-end\", gap: 2 }}>\r\n          <Button onClick={handleCloseLogoModal}>Cancel</Button>\r\n          <Button\r\n            variant=\"contained\"\r\n            onClick={handleBrandImageUpload}\r\n            disabled={!logoFile || loading}\r\n          >\r\n            {loading ? <CircularProgress size={24} /> : \"Save\"}\r\n          </Button>\r\n        </Box>\r\n      </Box>\r\n    </Dialog>\r\n  );\r\n\r\n  // Update the Cover Edit Modal\r\n  const CoverEditModal = (\r\n    <Dialog open={openCoverModal} onClose={handleCloseCoverModal}>\r\n      <Box sx={{ p: 3, width: \"400px\" }}>\r\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>\r\n          Edit Cover Photo\r\n        </Typography>\r\n\r\n        {previewCover && (\r\n          <img\r\n            src={previewCover}\r\n            alt=\"Current Cover\"\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"auto\",\r\n              maxHeight: \"300px\",\r\n              objectFit: \"contain\",\r\n              marginBottom: \"16px\",\r\n            }}\r\n          />\r\n        )}\r\n\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleCoverChange}\r\n          style={{ marginBottom: \"16px\" }}\r\n        />\r\n\r\n        <Box sx={{ display: \"flex\", justifyContent: \"flex-end\", gap: 2 }}>\r\n          <Button onClick={handleCloseCoverModal}>Cancel</Button>\r\n          <Button\r\n            variant=\"contained\"\r\n            onClick={handleBrandImageUpload}\r\n            disabled={!coverFile || loading}\r\n          >\r\n            {loading ? <CircularProgress size={24} /> : \"Save\"}\r\n          </Button>\r\n        </Box>\r\n      </Box>\r\n    </Dialog>\r\n  );\r\n\r\n  return (\r\n    <div className=\"branding-page-form\">\r\n      <div className=\"dashboard-header-title\" style={{ marginBottom: \"20px\" }}>\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            flexDirection: \"row\",\r\n            gap: \"10px\",\r\n          }}\r\n        >\r\n          <h2>Branding</h2>\r\n        </div>\r\n        <p style={{ fontSize: \"12px\", fontFamily: \"Montserrat\" }}>\r\n          Home &gt; Branding\r\n        </p>\r\n      </div>\r\n      {/* Brand Logo and Cover */}\r\n      <Box sx={{ backgroundColor: \"#fff\", p: 3, mb: 3, borderRadius: \"10px\" }}>\r\n        <Typography variant=\"h5\" sx={{ fontFamily: \"Horizon\", mb: 2 }}>\r\n          Brand Logo & Cover\r\n        </Typography>\r\n\r\n        <Box\r\n          sx={{\r\n            position: \"relative\",\r\n            height: \"300px\",\r\n            width: \"100%\",\r\n            borderRadius: \"8px\",\r\n            mb: 8,\r\n          }}\r\n        >\r\n          {/* Cover Photo */}\r\n          {previewCover ? (\r\n            <img\r\n              src={previewCover}\r\n              alt=\"Cover\"\r\n              style={{\r\n                width: \"100%\",\r\n                height: \"100%\",\r\n                objectFit: \"cover\",\r\n              }}\r\n            />\r\n          ) : (\r\n            <Box\r\n              sx={{\r\n                width: \"100%\",\r\n                height: \"100%\",\r\n                backgroundColor: \"#f0f2f5\",\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\",\r\n              }}\r\n            >\r\n              <Typography>No cover photo</Typography>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Edit Cover Button */}\r\n          <IconButton\r\n            sx={{\r\n              position: \"absolute\",\r\n              top: 16,\r\n              right: 16,\r\n              backgroundColor: \"rgba(255,255,255,0.8)\",\r\n              \"&:hover\": {\r\n                backgroundColor: \"#2d2d2d !important\",\r\n              },\r\n            }}\r\n            onClick={handleOpenCoverModal}\r\n          >\r\n            <MdOutlineModeEdit color=\"white\" />\r\n          </IconButton>\r\n\r\n          {/* Logo */}\r\n          <Box\r\n            sx={{\r\n              position: \"absolute\",\r\n              bottom: -64,\r\n              left: 24,\r\n              width: 128,\r\n              height: 128,\r\n              borderRadius: \"50%\",\r\n              border: \"4px solid white\",\r\n              backgroundColor: \"white\",\r\n            }}\r\n          >\r\n            {previewLogo ? (\r\n              <img\r\n                src={previewLogo}\r\n                alt=\"Logo\"\r\n                style={{\r\n                  width: \"100%\",\r\n                  height: \"100%\",\r\n                  borderRadius: \"50%\",\r\n                  objectFit: \"cover\",\r\n                }}\r\n              />\r\n            ) : (\r\n              <Box\r\n                sx={{\r\n                  width: \"100%\",\r\n                  height: \"100%\",\r\n                  backgroundColor: \"#f0f2f5\",\r\n                  borderRadius: \"50%\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                }}\r\n              >\r\n                <Typography>No logo</Typography>\r\n              </Box>\r\n            )}\r\n\r\n            {/* Edit Logo Button */}\r\n            <IconButton\r\n              sx={{\r\n                position: \"absolute\",\r\n                bottom: 8,\r\n                right: 8,\r\n                backgroundColor: \"rgba(255,255,255,0.8)\",\r\n                \"&:hover\": {\r\n                  backgroundColor: \"#2d2d2d !important\",\r\n                },\r\n              }}\r\n              onClick={handleOpenLogoModal}\r\n            >\r\n              <MdOutlineModeEdit color=\"white\" />\r\n            </IconButton>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Catalogs Section */}\r\n      <Box sx={{ backgroundColor: \"#fff\", p: 3, mb: 3, borderRadius: \"10px\" }}>\r\n        <Typography variant=\"h5\" sx={{ fontFamily: \"Horizon\", mb: 2 }}>\r\n          Catalogs\r\n        </Typography>\r\n        <button\r\n          className=\"submit-btn\"\r\n          onClick={handleOpenDialog}\r\n          style={{\r\n            \"&:hover\": {\r\n              backgroundColor: \"#2d2d2d !important\",\r\n            },\r\n          }}\r\n        >\r\n          Upload Catalog\r\n        </button>\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            gap: \"10px\",\r\n            overflowX: \"auto\",\r\n            marginTop: \"20px\",\r\n          }}\r\n        >\r\n          {catalogs.map((catalog) => (\r\n            <div key={catalog.id} style={{ position: \"relative\" }}>\r\n              <a\r\n                href={`https://pub-8c9ce55fbad6475eb1afe9472bd396e0.r2.dev/${catalog.pdf}`}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n              >\r\n                <div\r\n                  style={{\r\n                    width: \"180px\",\r\n                    height: \"220px\",\r\n                    backgroundImage: `url(https://pub-8c9ce55fbad6475eb1afe9472bd396e0.r2.dev/${catalog.image})`,\r\n                    backgroundSize: \"cover\",\r\n                    borderRadius: \"8px\",\r\n                    boxShadow: \"0 4px 10px rgba(0,0,0,0.2)\",\r\n                    display: \"flex\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                  }}\r\n                >\r\n                  <span\r\n                    style={{\r\n                      backgroundColor: \"#fff\",\r\n                      color: \"#2d2d2d\",\r\n                      padding: \"5px 10px\",\r\n                      borderRadius: \"5px\",\r\n                      fontSize: \"14px\",\r\n                      fontFamily: \"Montserrat\",\r\n                      boxShadow: \"0 2px 6px rgba(0,0,0,0.2)\",\r\n                    }}\r\n                  >\r\n                    {catalog.title}\r\n                  </span>\r\n                </div>\r\n              </a>\r\n              <IconButton\r\n                sx={{\r\n                  position: \"absolute\",\r\n                  top: 5,\r\n                  right: 5,\r\n                  \"&:hover\": {\r\n                    backgroundColor: \"#2d2d2d !important\",\r\n                  },\r\n                }}\r\n                onClick={(e) => handleMenuOpen(e, catalog)}\r\n              >\r\n                <BsThreeDotsVertical />\r\n              </IconButton>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </Box>\r\n\r\n      {/* Upload/Edit Dialog */}\r\n      <Dialog open={openDialog} onClose={handleCloseDialog}>\r\n        <Box sx={{ p: 3, width: \"400px\" }}>\r\n          <h3\r\n            style={{\r\n              textAlign: \"center\",\r\n              fontFamily: \"Horizon\",\r\n              color: \"#2d2d2d\",\r\n            }}\r\n          >\r\n            {selectedCatalog ? \"Edit Catalog\" : \"Upload Catalog\"}\r\n          </h3>\r\n\r\n          <TextField\r\n            fullWidth\r\n            label=\"Title\"\r\n            name=\"title\"\r\n            value={formData.title}\r\n            onChange={handleInputChange}\r\n            sx={{ mt: 2 }}\r\n          />\r\n          <TextField\r\n            fullWidth\r\n            label=\"Year\"\r\n            name=\"year\"\r\n            value={formData.year}\r\n            onChange={handleInputChange}\r\n            sx={{ mt: 2 }}\r\n          />\r\n          <TextField\r\n            fullWidth\r\n            label=\"Model\"\r\n            name=\"model\"\r\n            value={formData.model}\r\n            onChange={handleInputChange}\r\n            sx={{ mt: 2 }}\r\n          />\r\n          <TextField\r\n            fullWidth\r\n            label=\"Type\"\r\n            name=\"type\"\r\n            value={formData.type}\r\n            onChange={handleInputChange}\r\n            sx={{ mt: 2 }}\r\n          />\r\n          <Box sx={{ mt: 2 }}>\r\n            <label>Upload PDF</label>\r\n            <input\r\n              type=\"file\"\r\n              accept=\"application/pdf\"\r\n              onChange={(e) => handleFileChange(e, \"pdf\")}\r\n            />\r\n            <br />\r\n            <label>Upload Cover Image</label>\r\n            <input\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={(e) => handleFileChange(e, \"image\")}\r\n            />\r\n          </Box>\r\n\r\n          <button className=\"submit-btn\" onClick={handleUpload}>\r\n            {selectedCatalog ? \"Save Changes\" : \"Upload\"}\r\n          </button>\r\n        </Box>\r\n      </Dialog>\r\n      {LogoEditModal}\r\n      {CoverEditModal}\r\n\r\n      {/* Add Crop Modal */}\r\n      {showCropModal && (\r\n        <div\r\n          style={{\r\n            position: \"fixed\",\r\n            top: 0,\r\n            left: 0,\r\n            right: 0,\r\n            bottom: 0,\r\n            backgroundColor: \"rgba(0, 0, 0, 0.75)\",\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            zIndex: 9999, // Higher than the Dialog component\r\n          }}\r\n        >\r\n          <div\r\n            style={{\r\n              background: \"white\",\r\n              padding: \"20px\",\r\n              borderRadius: \"8px\",\r\n              width: \"90%\",\r\n              maxWidth: \"800px\",\r\n              maxHeight: \"90vh\",\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              gap: \"20px\",\r\n            }}\r\n          >\r\n            <div\r\n              style={{\r\n                position: \"relative\",\r\n                height: \"400px\",\r\n                width: \"100%\",\r\n                background: \"#333\",\r\n              }}\r\n            >\r\n              <Cropper\r\n                image={selectedImageSrc}\r\n                crop={crop}\r\n                zoom={zoom}\r\n                aspect={currentCropType === \"logo\" ? 1 : 16 / 5}\r\n                onCropChange={setCrop}\r\n                onZoomChange={setZoom}\r\n                onCropComplete={(_, area) => setCroppedAreaPixels(area)}\r\n              />\r\n            </div>\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                justifyContent: \"flex-end\",\r\n                gap: \"10px\",\r\n              }}\r\n            >\r\n              <button\r\n                onClick={handleCropComplete}\r\n                style={{\r\n                  padding: \"8px 16px\",\r\n                  border: \"none\",\r\n                  borderRadius: \"4px\",\r\n                  cursor: \"pointer\",\r\n                  fontFamily: \"Montserrat, sans-serif\",\r\n                  backgroundColor: \"#8A9A5B\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                Crop Image\r\n              </button>\r\n              <button\r\n                onClick={() => {\r\n                  setShowCropModal(false);\r\n                  setPendingFile(null);\r\n                  setSelectedImageSrc(null);\r\n                  setCurrentCropType(null);\r\n                }}\r\n                style={{\r\n                  padding: \"8px 16px\",\r\n                  border: \"none\",\r\n                  borderRadius: \"4px\",\r\n                  cursor: \"pointer\",\r\n                  fontFamily: \"Montserrat, sans-serif\",\r\n                  backgroundColor: \"#f0f0f0\",\r\n                  color: \"#333\",\r\n                }}\r\n              >\r\n                Cancel\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <Menu\r\n        anchorEl={anchorEl}\r\n        open={Boolean(anchorEl)}\r\n        onClose={handleMenuClose}\r\n      >\r\n        <MenuItem onClick={handleEdit}>Edit</MenuItem>\r\n        <MenuItem onClick={handleDelete}>Delete</MenuItem>\r\n      </Menu>\r\n\r\n      <Backdrop sx={{ color: \"#fff\", zIndex: 9999 }} open={loading}>\r\n        <CircularProgress color=\"inherit\" />\r\n      </Backdrop>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BrandingPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,OAAO,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEC,iBAAiB,KAAK;EACrD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAACE,GAAG,GAAGN,QAAQ;IACpBI,KAAK,CAACG,MAAM,GAAG,MAAM;MACnB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,KAAK,GAAGV,iBAAiB,CAACU,KAAK;MACtCH,MAAM,CAACI,MAAM,GAAGX,iBAAiB,CAACW,MAAM;MACxC,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MAEnCD,GAAG,CAACE,SAAS,CACXX,KAAK,EACLH,iBAAiB,CAACe,CAAC,EACnBf,iBAAiB,CAACgB,CAAC,EACnBhB,iBAAiB,CAACU,KAAK,EACvBV,iBAAiB,CAACW,MAAM,EACxB,CAAC,EACD,CAAC,EACDX,iBAAiB,CAACU,KAAK,EACvBV,iBAAiB,CAACW,MACpB,CAAC;MAEDJ,MAAM,CAACU,MAAM,CAAEC,IAAI,IAAKhB,OAAO,CAACgB,IAAI,CAAC,EAAE,YAAY,CAAC;IACtD,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACvCoE,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,IAAI;IACThD,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+E,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC;IAAEoC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChD,MAAM,CAAC4C,IAAI,EAAEC,OAAO,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACqB,iBAAiB,EAAE8D,oBAAoB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACoF,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE9D,MAAM;IAAEsF;EAAO,CAAC,GAAGzE,SAAS,CAAC,CAAC;EAC9B,MAAM0E,OAAO,GAAGD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,OAAO;EAE/BtF,SAAS,CAAC,MAAM;IACdW,KAAK,CACF4E,GAAG,CAAC,8CAA8CD,OAAO,EAAE,CAAC,CAC5DE,IAAI,CAAEC,GAAG,IAAK/C,WAAW,CAAC+C,GAAG,CAACC,IAAI,CAAC,CAAC,CACpCC,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC,CAAC;IACjE;IACAjF,KAAK,CACF4E,GAAG,CAAC,2CAA2CD,OAAO,EAAE,CAAC,CACzDE,IAAI,CAAEC,GAAG,IAAK;MACbrC,YAAY,CAACqC,GAAG,CAACC,IAAI,CAAC;MACtB,IAAID,GAAG,CAACC,IAAI,CAACK,SAAS,EAAE;QACtBjC,cAAc,CACZ,uDAAuD2B,GAAG,CAACC,IAAI,CAACK,SAAS,EAC3E,CAAC;MACH;MACA,IAAIN,GAAG,CAACC,IAAI,CAACM,UAAU,EAAE;QACvBhC,eAAe,CACb,uDAAuDyB,GAAG,CAACC,IAAI,CAACM,UAAU,EAC5E,CAAC;MACH;IACF,CAAC,CAAC,CACDL,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC,CAAC;EACrE,CAAC,EAAE,CAACN,OAAO,EAAEnC,SAAS,CAAC,CAAC;EAExB,MAAM8C,gBAAgB,GAAGA,CAAA,KAAMrD,aAAa,CAAC,IAAI,CAAC;EAClD,MAAMsD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BtD,aAAa,CAAC,KAAK,CAAC;IACpBI,kBAAkB,CAAC,IAAI,CAAC;IACxBkB,WAAW,CAAC;MACVC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,IAAI;MACThD,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,MAAM4E,mBAAmB,GAAGA,CAAA,KAAMzC,gBAAgB,CAAC,IAAI,CAAC;EACxD,MAAM0C,oBAAoB,GAAGA,CAAA,KAAM1C,gBAAgB,CAAC,KAAK,CAAC;EAC1D,MAAM2C,oBAAoB,GAAGA,CAAA,KAAMzC,iBAAiB,CAAC,IAAI,CAAC;EAC1D,MAAM0C,qBAAqB,GAAGA,CAAA,KAAM1C,iBAAiB,CAAC,KAAK,CAAC;EAC5D,MAAM2C,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;IAEX,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAAClF,MAAM,GAAG,MAAM;MACpBiD,mBAAmB,CAACiC,MAAM,CAACE,MAAM,CAAC;MAClCjC,cAAc,CAAC4B,IAAI,CAAC;MACpBrB,kBAAkB,CAAC,MAAM,CAAC;MAC1BX,gBAAgB,CAAC,IAAI,CAAC;MACtBf,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC;IACDkD,MAAM,CAACG,aAAa,CAACN,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMO,iBAAiB,GAAIR,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;IAEX,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAAClF,MAAM,GAAG,MAAM;MACpBiD,mBAAmB,CAACiC,MAAM,CAACE,MAAM,CAAC;MAClCjC,cAAc,CAAC4B,IAAI,CAAC;MACpBrB,kBAAkB,CAAC,OAAO,CAAC;MAC3BX,gBAAgB,CAAC,IAAI,CAAC;MACtBb,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC;IACDgD,MAAM,CAACG,aAAa,CAACN,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMQ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACvC,gBAAgB,IAAI,CAACtD,iBAAiB,EAAE;IAE7C,IAAI;MACF,MAAMkB,IAAI,GAAG,MAAMpB,aAAa,CAACwD,gBAAgB,EAAEtD,iBAAiB,CAAC;MACrE,MAAM8F,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC7E,IAAI,CAAC,EAAEsC,WAAW,CAACwC,IAAI,EAAE;QACrD9C,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAM+C,UAAU,GAAGC,GAAG,CAACC,eAAe,CAACjF,IAAI,CAAC;MAE5C,IAAI6C,eAAe,KAAK,MAAM,EAAE;QAC9B7B,WAAW,CAAC4D,WAAW,CAAC;QACxBpD,cAAc,CAACuD,UAAU,CAAC;MAC5B,CAAC,MAAM;QACL7D,YAAY,CAAC0D,WAAW,CAAC;QACzBlD,eAAe,CAACqD,UAAU,CAAC;MAC7B;MAEA5C,gBAAgB,CAAC,KAAK,CAAC;MACvBI,cAAc,CAAC,IAAI,CAAC;MACpBF,mBAAmB,CAAC,IAAI,CAAC;MACzBS,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAM0B,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI,CAACnE,QAAQ,IAAI,CAACE,SAAS,EAAE;IAE7B,MAAMU,QAAQ,GAAG,IAAIwD,QAAQ,CAAC,CAAC;IAC/B,IAAIpE,QAAQ,EAAEY,QAAQ,CAACyD,MAAM,CAAC,WAAW,EAAErE,QAAQ,CAAC;IACpD,IAAIE,SAAS,EAAEU,QAAQ,CAACyD,MAAM,CAAC,YAAY,EAAEnE,SAAS,CAAC;IAEvD,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,GAAG,GAAG,MAAM9E,KAAK,CAACgH,GAAG,CACzB,kDAAkDrC,OAAO,gBAAgB,EACzErB,QAAQ,EACR;QAAE2D,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MAAE,CACvD,CAAC;MACDxE,YAAY,CAACqC,GAAG,CAACC,IAAI,CAAC;MACtBpC,WAAW,CAAC,IAAI,CAAC;MACjBE,YAAY,CAAC,IAAI,CAAC;MAClB4C,oBAAoB,CAAC,CAAC;MACtBE,qBAAqB,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM2E,gBAAgB,GAAGA,CAACrB,CAAC,EAAElC,IAAI,KAAK;IACpC,MAAMmC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,IAAI,EAAE;IACXvC,WAAW,CAAE4D,QAAQ,KAAM;MAAE,GAAGA,QAAQ;MAAE,CAACxD,IAAI,GAAGmC;IAAK,CAAC,CAAC,CAAC;EAC5D,CAAC;EACD,MAAMsB,iBAAiB,GAAIvB,CAAC,IAAK;IAC/BtC,WAAW,CAAE4D,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACX,CAACtB,CAAC,CAACE,MAAM,CAACU,IAAI,GAAGZ,CAAC,CAACE,MAAM,CAACsB;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMvC,IAAI,GAAG,IAAI+B,QAAQ,CAAC,CAAC;IAC3B,IAAIxD,QAAQ,CAACM,GAAG,EAAEmB,IAAI,CAACgC,MAAM,CAAC,KAAK,EAAEzD,QAAQ,CAACM,GAAG,CAAC;IAClD,IAAIN,QAAQ,CAAC1C,KAAK,EAAEmE,IAAI,CAACgC,MAAM,CAAC,OAAO,EAAEzD,QAAQ,CAAC1C,KAAK,CAAC;IACxDmE,IAAI,CAACgC,MAAM,CAAC,OAAO,EAAEzD,QAAQ,CAACE,KAAK,CAAC;IACpCuB,IAAI,CAACgC,MAAM,CAAC,MAAM,EAAEzD,QAAQ,CAACG,IAAI,CAAC;IAClCsB,IAAI,CAACgC,MAAM,CAAC,OAAO,EAAEzD,QAAQ,CAACI,KAAK,CAAC;IACpCqB,IAAI,CAACgC,MAAM,CAAC,MAAM,EAAEzD,QAAQ,CAACK,IAAI,CAAC;IAClCoB,IAAI,CAACgC,MAAM,CAAC,SAAS,EAAEpC,OAAO,CAAC;IAC/BpC,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMgF,GAAG,GAAGnF,eAAe,GACvB,8CAA8CA,eAAe,CAACoF,EAAE,EAAE,GAClE,mDAAmD;IAEvD,MAAMC,MAAM,GAAGrF,eAAe,GAAG,KAAK,GAAG,MAAM;IAE/C,IAAI;MACF,MAAMsF,QAAQ,GAAG,MAAM1H,KAAK,CAAC;QAC3ByH,MAAM;QACNF,GAAG;QACHxC,IAAI;QACJkC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MACnD,CAAC,CAAC;MAEF,IAAI7E,eAAe,EAAE;QACnBL,WAAW,CACTD,QAAQ,CAAC6F,GAAG,CAAEC,CAAC,IACbA,CAAC,CAACJ,EAAE,KAAKpF,eAAe,CAACoF,EAAE,GAAGE,QAAQ,CAAC3C,IAAI,CAAC8C,OAAO,GAAGD,CACxD,CACF,CAAC;MACH,CAAC,MAAM;QACL7F,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE;UAAE,GAAGwB,QAAQ;UAAEkE,EAAE,EAAEE,QAAQ,CAAC3C,IAAI,CAACyC;QAAG,CAAC,CAAC,CAAC;MACnE;MAEAjC,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuF,cAAc,GAAGA,CAACC,KAAK,EAAEF,OAAO,KAAK;IACzC1F,WAAW,CAAC4F,KAAK,CAACC,aAAa,CAAC;IAChC3F,kBAAkB,CAACwF,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM9F,WAAW,CAAC,IAAI,CAAC;EAE/C,MAAM+F,UAAU,GAAGA,CAAA,KAAM;IACvB3E,WAAW,CAACnB,eAAe,CAAC;IAC5BH,aAAa,CAAC,IAAI,CAAC;IACnBgG,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMnI,KAAK,CAACoI,MAAM,CAChB,8CAA8ChG,eAAe,CAACoF,EAAE,EAClE,CAAC;MACDzF,WAAW,CAACD,QAAQ,CAACuG,MAAM,CAAET,CAAC,IAAKA,CAAC,CAACJ,EAAE,KAAKpF,eAAe,CAACoF,EAAE,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;IACA8C,eAAe,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMK,aAAa,gBACjBhI,OAAA,CAACf,MAAM;IAACgJ,IAAI,EAAEzF,aAAc;IAAC0F,OAAO,EAAE/C,oBAAqB;IAAAgD,QAAA,eACzDnI,OAAA,CAAChB,GAAG;MAACoJ,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAExH,KAAK,EAAE;MAAQ,CAAE;MAAAsH,QAAA,gBAChCnI,OAAA,CAACT,UAAU;QAAC+I,OAAO,EAAC,IAAI;QAACF,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAExC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ/F,WAAW,iBACV5C,OAAA;QACEQ,GAAG,EAAEoC,WAAY;QACjBgG,GAAG,EAAC,cAAc;QAClBC,KAAK,EAAE;UACLhI,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdgI,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE,SAAS;UACpBC,YAAY,EAAE;QAChB;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,eAED3I,OAAA;QACEqD,IAAI,EAAC,MAAM;QACX4F,MAAM,EAAC,SAAS;QAChBC,QAAQ,EAAE5D,gBAAiB;QAC3BuD,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAO;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEF3I,OAAA,CAAChB,GAAG;QAACoJ,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBAC/DnI,OAAA,CAACR,MAAM;UAAC8J,OAAO,EAAEnE,oBAAqB;UAAAgD,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtD3I,OAAA,CAACR,MAAM;UACL8I,OAAO,EAAC,WAAW;UACnBgB,OAAO,EAAE/C,sBAAuB;UAChCgD,QAAQ,EAAE,CAACnH,QAAQ,IAAIJ,OAAQ;UAAAmG,QAAA,EAE9BnG,OAAO,gBAAGhC,OAAA,CAACP,gBAAgB;YAAC+J,IAAI,EAAE;UAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CACT;;EAED;EACA,MAAMc,cAAc,gBAClBzJ,OAAA,CAACf,MAAM;IAACgJ,IAAI,EAAEvF,cAAe;IAACwF,OAAO,EAAE7C,qBAAsB;IAAA8C,QAAA,eAC3DnI,OAAA,CAAChB,GAAG;MAACoJ,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAExH,KAAK,EAAE;MAAQ,CAAE;MAAAsH,QAAA,gBAChCnI,OAAA,CAACT,UAAU;QAAC+I,OAAO,EAAC,IAAI;QAACF,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAExC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ7F,YAAY,iBACX9C,OAAA;QACEQ,GAAG,EAAEsC,YAAa;QAClB8F,GAAG,EAAC,eAAe;QACnBC,KAAK,EAAE;UACLhI,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdgI,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE,SAAS;UACpBC,YAAY,EAAE;QAChB;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,eAED3I,OAAA;QACEqD,IAAI,EAAC,MAAM;QACX4F,MAAM,EAAC,SAAS;QAChBC,QAAQ,EAAEnD,iBAAkB;QAC5B8C,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAO;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEF3I,OAAA,CAAChB,GAAG;QAACoJ,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBAC/DnI,OAAA,CAACR,MAAM;UAAC8J,OAAO,EAAEjE,qBAAsB;UAAA8C,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvD3I,OAAA,CAACR,MAAM;UACL8I,OAAO,EAAC,WAAW;UACnBgB,OAAO,EAAE/C,sBAAuB;UAChCgD,QAAQ,EAAE,CAACjH,SAAS,IAAIN,OAAQ;UAAAmG,QAAA,EAE/BnG,OAAO,gBAAGhC,OAAA,CAACP,gBAAgB;YAAC+J,IAAI,EAAE;UAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CACT;EAED,oBACE3I,OAAA;IAAK0J,SAAS,EAAC,oBAAoB;IAAAvB,QAAA,gBACjCnI,OAAA;MAAK0J,SAAS,EAAC,wBAAwB;MAACb,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAb,QAAA,gBACtEnI,OAAA;QACE6I,KAAK,EAAE;UACLM,OAAO,EAAE,MAAM;UACfQ,UAAU,EAAE,QAAQ;UACpBC,aAAa,EAAE,KAAK;UACpBP,GAAG,EAAE;QACP,CAAE;QAAAlB,QAAA,eAEFnI,OAAA;UAAAmI,QAAA,EAAI;QAAQ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACN3I,OAAA;QAAG6I,KAAK,EAAE;UAAEgB,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAa,CAAE;QAAA3B,QAAA,EAAC;MAE1D;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN3I,OAAA,CAAChB,GAAG;MAACoJ,EAAE,EAAE;QAAE2B,eAAe,EAAE,MAAM;QAAE1B,CAAC,EAAE,CAAC;QAAEE,EAAE,EAAE,CAAC;QAAEyB,YAAY,EAAE;MAAO,CAAE;MAAA7B,QAAA,gBACtEnI,OAAA,CAACT,UAAU;QAAC+I,OAAO,EAAC,IAAI;QAACF,EAAE,EAAE;UAAE0B,UAAU,EAAE,SAAS;UAAEvB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAE/D;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3I,OAAA,CAAChB,GAAG;QACFoJ,EAAE,EAAE;UACF6B,QAAQ,EAAE,UAAU;UACpBnJ,MAAM,EAAE,OAAO;UACfD,KAAK,EAAE,MAAM;UACbmJ,YAAY,EAAE,KAAK;UACnBzB,EAAE,EAAE;QACN,CAAE;QAAAJ,QAAA,GAGDrF,YAAY,gBACX9C,OAAA;UACEQ,GAAG,EAAEsC,YAAa;UAClB8F,GAAG,EAAC,OAAO;UACXC,KAAK,EAAE;YACLhI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiI,SAAS,EAAE;UACb;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEF3I,OAAA,CAAChB,GAAG;UACFoJ,EAAE,EAAE;YACFvH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdiJ,eAAe,EAAE,SAAS;YAC1BZ,OAAO,EAAE,MAAM;YACfQ,UAAU,EAAE,QAAQ;YACpBP,cAAc,EAAE;UAClB,CAAE;UAAAjB,QAAA,eAEFnI,OAAA,CAACT,UAAU;YAAA4I,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACN,eAGD3I,OAAA,CAACX,UAAU;UACT+I,EAAE,EAAE;YACF6B,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,EAAE;YACPC,KAAK,EAAE,EAAE;YACTJ,eAAe,EAAE,uBAAuB;YACxC,SAAS,EAAE;cACTA,eAAe,EAAE;YACnB;UACF,CAAE;UACFT,OAAO,EAAElE,oBAAqB;UAAA+C,QAAA,eAE9BnI,OAAA,CAACH,iBAAiB;YAACuK,KAAK,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGb3I,OAAA,CAAChB,GAAG;UACFoJ,EAAE,EAAE;YACF6B,QAAQ,EAAE,UAAU;YACpBI,MAAM,EAAE,CAAC,EAAE;YACXC,IAAI,EAAE,EAAE;YACRzJ,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXkJ,YAAY,EAAE,KAAK;YACnBO,MAAM,EAAE,iBAAiB;YACzBR,eAAe,EAAE;UACnB,CAAE;UAAA5B,QAAA,GAEDvF,WAAW,gBACV5C,OAAA;YACEQ,GAAG,EAAEoC,WAAY;YACjBgG,GAAG,EAAC,MAAM;YACVC,KAAK,EAAE;cACLhI,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdkJ,YAAY,EAAE,KAAK;cACnBjB,SAAS,EAAE;YACb;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF3I,OAAA,CAAChB,GAAG;YACFoJ,EAAE,EAAE;cACFvH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdiJ,eAAe,EAAE,SAAS;cAC1BC,YAAY,EAAE,KAAK;cACnBb,OAAO,EAAE,MAAM;cACfQ,UAAU,EAAE,QAAQ;cACpBP,cAAc,EAAE;YAClB,CAAE;YAAAjB,QAAA,eAEFnI,OAAA,CAACT,UAAU;cAAA4I,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACN,eAGD3I,OAAA,CAACX,UAAU;YACT+I,EAAE,EAAE;cACF6B,QAAQ,EAAE,UAAU;cACpBI,MAAM,EAAE,CAAC;cACTF,KAAK,EAAE,CAAC;cACRJ,eAAe,EAAE,uBAAuB;cACxC,SAAS,EAAE;gBACTA,eAAe,EAAE;cACnB;YACF,CAAE;YACFT,OAAO,EAAEpE,mBAAoB;YAAAiD,QAAA,eAE7BnI,OAAA,CAACH,iBAAiB;cAACuK,KAAK,EAAC;YAAO;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3I,OAAA,CAAChB,GAAG;MAACoJ,EAAE,EAAE;QAAE2B,eAAe,EAAE,MAAM;QAAE1B,CAAC,EAAE,CAAC;QAAEE,EAAE,EAAE,CAAC;QAAEyB,YAAY,EAAE;MAAO,CAAE;MAAA7B,QAAA,gBACtEnI,OAAA,CAACT,UAAU;QAAC+I,OAAO,EAAC,IAAI;QAACF,EAAE,EAAE;UAAE0B,UAAU,EAAE,SAAS;UAAEvB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAE/D;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3I,OAAA;QACE0J,SAAS,EAAC,YAAY;QACtBJ,OAAO,EAAEtE,gBAAiB;QAC1B6D,KAAK,EAAE;UACL,SAAS,EAAE;YACTkB,eAAe,EAAE;UACnB;QACF,CAAE;QAAA5B,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3I,OAAA;QACE6I,KAAK,EAAE;UACLM,OAAO,EAAE,MAAM;UACfE,GAAG,EAAE,MAAM;UACXmB,SAAS,EAAE,MAAM;UACjBC,SAAS,EAAE;QACb,CAAE;QAAAtC,QAAA,EAED3G,QAAQ,CAAC6F,GAAG,CAAEE,OAAO,iBACpBvH,OAAA;UAAsB6I,KAAK,EAAE;YAAEoB,QAAQ,EAAE;UAAW,CAAE;UAAA9B,QAAA,gBACpDnI,OAAA;YACE0K,IAAI,EAAE,uDAAuDnD,OAAO,CAACjE,GAAG,EAAG;YAC3EmC,MAAM,EAAC,QAAQ;YACfkF,GAAG,EAAC,qBAAqB;YAAAxC,QAAA,eAEzBnI,OAAA;cACE6I,KAAK,EAAE;gBACLhI,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACf8J,eAAe,EAAE,2DAA2DrD,OAAO,CAACjH,KAAK,GAAG;gBAC5FuK,cAAc,EAAE,OAAO;gBACvBb,YAAY,EAAE,KAAK;gBACnBc,SAAS,EAAE,4BAA4B;gBACvC3B,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,QAAQ;gBACxBO,UAAU,EAAE;cACd,CAAE;cAAAxB,QAAA,eAEFnI,OAAA;gBACE6I,KAAK,EAAE;kBACLkB,eAAe,EAAE,MAAM;kBACvBK,KAAK,EAAE,SAAS;kBAChBW,OAAO,EAAE,UAAU;kBACnBf,YAAY,EAAE,KAAK;kBACnBH,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,YAAY;kBACxBgB,SAAS,EAAE;gBACb,CAAE;gBAAA3C,QAAA,EAEDZ,OAAO,CAACrE;cAAK;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACJ3I,OAAA,CAACX,UAAU;YACT+I,EAAE,EAAE;cACF6B,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,CAAC;cACNC,KAAK,EAAE,CAAC;cACR,SAAS,EAAE;gBACTJ,eAAe,EAAE;cACnB;YACF,CAAE;YACFT,OAAO,EAAG/D,CAAC,IAAKiC,cAAc,CAACjC,CAAC,EAAEgC,OAAO,CAAE;YAAAY,QAAA,eAE3CnI,OAAA,CAACJ,mBAAmB;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA,GA9CLpB,OAAO,CAACL,EAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+Cf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3I,OAAA,CAACf,MAAM;MAACgJ,IAAI,EAAEvG,UAAW;MAACwG,OAAO,EAAEjD,iBAAkB;MAAAkD,QAAA,eACnDnI,OAAA,CAAChB,GAAG;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAExH,KAAK,EAAE;QAAQ,CAAE;QAAAsH,QAAA,gBAChCnI,OAAA;UACE6I,KAAK,EAAE;YACLmC,SAAS,EAAE,QAAQ;YACnBlB,UAAU,EAAE,SAAS;YACrBM,KAAK,EAAE;UACT,CAAE;UAAAjC,QAAA,EAEDrG,eAAe,GAAG,cAAc,GAAG;QAAgB;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAEL3I,OAAA,CAACZ,SAAS;UACR6L,SAAS;UACTC,KAAK,EAAC,OAAO;UACb/E,IAAI,EAAC,OAAO;UACZY,KAAK,EAAE/D,QAAQ,CAACE,KAAM;UACtBgG,QAAQ,EAAEpC,iBAAkB;UAC5BsB,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF3I,OAAA,CAACZ,SAAS;UACR6L,SAAS;UACTC,KAAK,EAAC,MAAM;UACZ/E,IAAI,EAAC,MAAM;UACXY,KAAK,EAAE/D,QAAQ,CAACG,IAAK;UACrB+F,QAAQ,EAAEpC,iBAAkB;UAC5BsB,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF3I,OAAA,CAACZ,SAAS;UACR6L,SAAS;UACTC,KAAK,EAAC,OAAO;UACb/E,IAAI,EAAC,OAAO;UACZY,KAAK,EAAE/D,QAAQ,CAACI,KAAM;UACtB8F,QAAQ,EAAEpC,iBAAkB;UAC5BsB,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF3I,OAAA,CAACZ,SAAS;UACR6L,SAAS;UACTC,KAAK,EAAC,MAAM;UACZ/E,IAAI,EAAC,MAAM;UACXY,KAAK,EAAE/D,QAAQ,CAACK,IAAK;UACrB6F,QAAQ,EAAEpC,iBAAkB;UAC5BsB,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF3I,OAAA,CAAChB,GAAG;UAACoJ,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAAhD,QAAA,gBACjBnI,OAAA;YAAAmI,QAAA,EAAO;UAAU;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzB3I,OAAA;YACEqD,IAAI,EAAC,MAAM;YACX4F,MAAM,EAAC,iBAAiB;YACxBC,QAAQ,EAAG3D,CAAC,IAAKqB,gBAAgB,CAACrB,CAAC,EAAE,KAAK;UAAE;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACF3I,OAAA;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN3I,OAAA;YAAAmI,QAAA,EAAO;UAAkB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC3I,OAAA;YACEqD,IAAI,EAAC,MAAM;YACX4F,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAG3D,CAAC,IAAKqB,gBAAgB,CAACrB,CAAC,EAAE,OAAO;UAAE;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3I,OAAA;UAAQ0J,SAAS,EAAC,YAAY;UAACJ,OAAO,EAAEtC,YAAa;UAAAmB,QAAA,EAClDrG,eAAe,GAAG,cAAc,GAAG;QAAQ;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACRX,aAAa,EACbyB,cAAc,EAGdlG,aAAa,iBACZvD,OAAA;MACE6I,KAAK,EAAE;QACLoB,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNI,IAAI,EAAE,CAAC;QACPH,KAAK,EAAE,CAAC;QACRE,MAAM,EAAE,CAAC;QACTN,eAAe,EAAE,qBAAqB;QACtCZ,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBO,UAAU,EAAE,QAAQ;QACpByB,MAAM,EAAE,IAAI,CAAE;MAChB,CAAE;MAAAjD,QAAA,eAEFnI,OAAA;QACE6I,KAAK,EAAE;UACLwC,UAAU,EAAE,OAAO;UACnBN,OAAO,EAAE,MAAM;UACff,YAAY,EAAE,KAAK;UACnBnJ,KAAK,EAAE,KAAK;UACZyK,QAAQ,EAAE,OAAO;UACjBxC,SAAS,EAAE,MAAM;UACjBK,OAAO,EAAE,MAAM;UACfS,aAAa,EAAE,QAAQ;UACvBP,GAAG,EAAE;QACP,CAAE;QAAAlB,QAAA,gBAEFnI,OAAA;UACE6I,KAAK,EAAE;YACLoB,QAAQ,EAAE,UAAU;YACpBnJ,MAAM,EAAE,OAAO;YACfD,KAAK,EAAE,MAAM;YACbwK,UAAU,EAAE;UACd,CAAE;UAAAlD,QAAA,eAEFnI,OAAA,CAACF,OAAO;YACNQ,KAAK,EAAEmD,gBAAiB;YACxBI,IAAI,EAAEA,IAAK;YACXE,IAAI,EAAEA,IAAK;YACXwH,MAAM,EAAErH,eAAe,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAE;YAChDsH,YAAY,EAAE1H,OAAQ;YACtB2H,YAAY,EAAEzH,OAAQ;YACtB0H,cAAc,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK3H,oBAAoB,CAAC2H,IAAI;UAAE;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3I,OAAA;UACE6I,KAAK,EAAE;YACLM,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,UAAU;YAC1BC,GAAG,EAAE;UACP,CAAE;UAAAlB,QAAA,gBAEFnI,OAAA;YACEsJ,OAAO,EAAEtD,kBAAmB;YAC5B6C,KAAK,EAAE;cACLkC,OAAO,EAAE,UAAU;cACnBR,MAAM,EAAE,MAAM;cACdP,YAAY,EAAE,KAAK;cACnB6B,MAAM,EAAE,SAAS;cACjB/B,UAAU,EAAE,wBAAwB;cACpCC,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE;YACT,CAAE;YAAAjC,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3I,OAAA;YACEsJ,OAAO,EAAEA,CAAA,KAAM;cACb9F,gBAAgB,CAAC,KAAK,CAAC;cACvBI,cAAc,CAAC,IAAI,CAAC;cACpBF,mBAAmB,CAAC,IAAI,CAAC;cACzBS,kBAAkB,CAAC,IAAI,CAAC;YAC1B,CAAE;YACF0E,KAAK,EAAE;cACLkC,OAAO,EAAE,UAAU;cACnBR,MAAM,EAAE,MAAM;cACdP,YAAY,EAAE,KAAK;cACnB6B,MAAM,EAAE,SAAS;cACjB/B,UAAU,EAAE,wBAAwB;cACpCC,eAAe,EAAE,SAAS;cAC1BK,KAAK,EAAE;YACT,CAAE;YAAAjC,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED3I,OAAA,CAACd,IAAI;MACH0C,QAAQ,EAAEA,QAAS;MACnBqG,IAAI,EAAE6D,OAAO,CAAClK,QAAQ,CAAE;MACxBsG,OAAO,EAAEP,eAAgB;MAAAQ,QAAA,gBAEzBnI,OAAA,CAACb,QAAQ;QAACmK,OAAO,EAAE1B,UAAW;QAAAO,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eAC9C3I,OAAA,CAACb,QAAQ;QAACmK,OAAO,EAAEzB,YAAa;QAAAM,QAAA,EAAC;MAAM;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAEP3I,OAAA,CAACV,QAAQ;MAAC8I,EAAE,EAAE;QAAEgC,KAAK,EAAE,MAAM;QAAEgB,MAAM,EAAE;MAAK,CAAE;MAACnD,IAAI,EAAEjG,OAAQ;MAAAmG,QAAA,eAC3DnI,OAAA,CAACP,gBAAgB;QAAC2K,KAAK,EAAC;MAAS;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACpH,EAAA,CApsBID,YAAY;EAAA,QA+BG3B,SAAS;AAAA;AAAAoM,EAAA,GA/BxBzK,YAAY;AAssBlB,eAAeA,YAAY;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}