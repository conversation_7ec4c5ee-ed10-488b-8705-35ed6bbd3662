{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\onSale.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Grid, Typography, CircularProgress, useMediaQuery } from \"@mui/material\";\nimport axios from \"axios\";\nimport Header from \"../Components/navBar\";\nimport ProductCards from \"../Components/Products/Productsgrid\";\nimport FilterSection from \"../Components/Products/filters\";\nimport TopFilter from \"../Components/Products/TopFilters\";\nimport Footer from \"../Components/Footer\";\nimport PageDescription from \"../Components/Topheader\";\nimport { Helmet } from \"react-helmet-async\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction OnSale() {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [filteredProducts, setFilteredProducts] = useState([]);\n  const [sortOption, setSortOption] = useState(\"Newest\");\n  const [isLoading, setIsLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    brands: [],\n    colors: [],\n    tags: [],\n    priceRange: [0, 600000] // Wider range initially\n  });\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n\n  // 🟢 Fetch products\n  useEffect(() => {\n    const fetchReadyToShipProducts = async () => {\n      setIsLoading(true);\n      try {\n        const {\n          data\n        } = await axios.get(\"https://api.thedesigngrit.com/api/products/getproducts/\");\n        // Filter products that have salePrice\n        const onSaleProducts = data.filter(product => product.salePrice && product.status === true && product.promotionApproved === true && product.brandId.status === \"active\");\n        setProducts(onSaleProducts);\n        setFilteredProducts(onSaleProducts);\n      } catch (error) {\n        console.error(\"Error fetching products:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchReadyToShipProducts();\n  }, []);\n\n  // 🟢 Apply filters and sorting\n  useEffect(() => {\n    const applyFiltersAndSorting = () => {\n      let filtered = [...products];\n      console.log(\"Initial products:\", products); // Add this\n      products.forEach((product, index) => {\n        console.log(`Product ${index}:`, {\n          name: product.name,\n          brand: product.brandId.brandName,\n          colors: product.colors,\n          tags: product.tags,\n          price: product.price,\n          salePrice: product.salePrice,\n          createdAt: product.createdAt\n        });\n      });\n      if (filters.hasCAD) {\n        filtered = filtered.filter(product => product.cadFile);\n      }\n\n      // Sale Price filter\n      if (filters.hasSalePrice) {\n        filtered = filtered.filter(product => product.salePrice);\n      }\n      // Brand filter\n      if (filters.brands.length > 0) {\n        filtered = filtered.filter(product => product.brandId && product.brandId.brandName && filters.brands.includes(product.brandId.brandName));\n      }\n\n      // Color filter\n      if (filters.colors.length > 0) {\n        filtered = filtered.filter(product => {\n          var _product$colors;\n          return (_product$colors = product.colors) === null || _product$colors === void 0 ? void 0 : _product$colors.some(color => filters.colors.includes(color));\n        });\n      }\n\n      // Tags filter\n      if (filters.tags.length > 0) {\n        filtered = filtered.filter(product => {\n          var _product$tags;\n          return (_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.some(tag => filters.tags.includes(tag));\n        });\n      }\n\n      // Price filter\n      filtered = filtered.filter(product => {\n        const price = product.salePrice || product.price;\n        return price >= filters.priceRange[0] && price <= filters.priceRange[1];\n      });\n\n      // Sorting\n      switch (sortOption) {\n        case \"Newest\":\n          filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n          break;\n        case \"Price: Low to High\":\n          filtered.sort((a, b) => (a.salePrice || a.price) - (b.salePrice || b.price));\n          break;\n        case \"Price: High to Low\":\n          filtered.sort((a, b) => (b.salePrice || b.price) - (a.salePrice || a.price));\n          break;\n        case \"Alphabetical: A-Z\":\n          filtered.sort((a, b) => a.name.localeCompare(b.name));\n          break;\n        case \"Alphabetical: Z-A\":\n          filtered.sort((a, b) => b.name.localeCompare(a.name));\n          break;\n        default:\n          break;\n      }\n      console.log(\"Filtered products:\", filtered); // Add this\n\n      setFilteredProducts(filtered);\n    };\n    applyFiltersAndSorting();\n  }, [products, filters, sortOption]);\n  const handleFilterChange = newFilters => {\n    setFilters(newFilters);\n  };\n  const handleCADFilterChange = value => {\n    setFilters(prev => ({\n      ...prev,\n      hasCAD: value\n    }));\n  };\n\n  // Handle Sale Price filter toggle\n  const handleSalePriceFilterChange = value => {\n    setFilters(prev => ({\n      ...prev,\n      hasSalePrice: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: \"100vh\",\n      display: \"flex\",\n      flexDirection: \"column\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"On Sale Furniture | TheDesignGrit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Explore premium Egyptian-designed furniture at discounted prices. Limited-time offers on stylish and high-quality products at TheDesignGrit.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"keywords\",\n        content: \"furniture sale, discounts, TheDesignGrit, Egyptian furniture, modern deals, home decor sale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"robots\",\n        content: \"index, follow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"link\", {\n        rel: \"canonical\",\n        href: \"https://thedesigngrit.com/products/onsale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:title\",\n        content: \"On Sale Furniture | TheDesignGrit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:description\",\n        content: \"Browse our collection of furniture on sale. Elevate your home with elegant, affordable pieces crafted by top Egyptian designers.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:image\",\n        content: \"https://thedesigngrit.com/Assets/sale-banner.webp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:url\",\n        content: \"https://thedesigngrit.com/products/onsale\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:type\",\n        content: \"website\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:card\",\n        content: \"summary_large_image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:title\",\n        content: \"On Sale Furniture | TheDesignGrit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"twitter:description\",\n        content: \"Discounted designer furniture from Egypt's top brands. Shop now at TheDesignGrit.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageDescription, {\n      name: \"On Sale Products\",\n      description: \"Discover products that are in Sale and hurry up!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: {\n          xs: \"none\",\n          md: \"flex\"\n        },\n        justifyContent: \"flex-end\",\n        px: {\n          xs: 2,\n          md: 3\n        },\n        py: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(TopFilter, {\n        sortOption: sortOption,\n        setSortOption: setSortOption,\n        onCADFilterChange: handleCADFilterChange,\n        onSalePriceFilterChange: handleSalePriceFilterChange,\n        hasCAD: filters.hasCAD,\n        hasSalePrice: filters.hasSalePrice\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        flex: 1,\n        px: {\n          xs: 2,\n          md: 3\n        },\n        pb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        md: 3,\n        sx: {\n          paddingLeft: {\n            xs: 0,\n            md: 0,\n            lg: 0,\n            xl: 0\n          },\n          \"@media (min-width:1025px)\": {\n            paddingLeft: \"66px !important\"\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(FilterSection, {\n          onFilterChange: handleFilterChange,\n          products: products,\n          currentFilters: filters,\n          sortOption: sortOption,\n          setSortOption: setSortOption,\n          onCADFilterChange: handleCADFilterChange,\n          onSalePriceFilterChange: handleSalePriceFilterChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 9,\n        children: isLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"300px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 60,\n            thickness: 4,\n            sx: {\n              color: \"#6b7b58\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this) : filteredProducts.length > 0 ? /*#__PURE__*/_jsxDEV(ProductCards, {\n          products: filteredProducts\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            textAlign: \"center\",\n            mt: 4\n          },\n          children: \"No products have sale at the moment.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            textAlign: \"center\",\n            mt: 4\n          },\n          children: \"No products match your filters. Try adjusting your criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n}\n_s(OnSale, \"eHxYEXfHwUmXO3bZvGykT8JApjw=\", false, function () {\n  return [useMediaQuery];\n});\n_c = OnSale;\nexport default OnSale;\nvar _c;\n$RefreshReg$(_c, \"OnSale\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Typography", "CircularProgress", "useMediaQuery", "axios", "Header", "ProductCards", "FilterSection", "TopFilter", "Footer", "PageDescription", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "OnSale", "_s", "products", "setProducts", "filteredProducts", "setFilteredProducts", "sortOption", "setSortOption", "isLoading", "setIsLoading", "filters", "setFilters", "brands", "colors", "tags", "priceRange", "isMobile", "fetchReadyToShipProducts", "data", "get", "onSaleProducts", "filter", "product", "salePrice", "status", "promotionApproved", "brandId", "error", "console", "applyFiltersAndSorting", "filtered", "log", "for<PERSON>ach", "index", "name", "brand", "brandName", "price", "createdAt", "hasCAD", "cadFile", "hasSalePrice", "length", "includes", "_product$colors", "some", "color", "_product$tags", "tag", "sort", "a", "b", "Date", "localeCompare", "handleFilterChange", "newFilters", "handleCADFilterChange", "value", "prev", "handleSalePriceFilterChange", "sx", "minHeight", "display", "flexDirection", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "rel", "href", "property", "description", "xs", "md", "justifyContent", "px", "py", "onCADFilterChange", "onSalePriceFilterChange", "container", "spacing", "flex", "pb", "item", "sm", "paddingLeft", "lg", "xl", "onFilterChange", "currentFilters", "alignItems", "size", "thickness", "variant", "textAlign", "mt", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/onSale.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Typography,\r\n  CircularProgress,\r\n  useMediaQuery,\r\n} from \"@mui/material\";\r\nimport axios from \"axios\";\r\nimport Header from \"../Components/navBar\";\r\nimport ProductCards from \"../Components/Products/Productsgrid\";\r\nimport FilterSection from \"../Components/Products/filters\";\r\nimport TopFilter from \"../Components/Products/TopFilters\";\r\nimport Footer from \"../Components/Footer\";\r\nimport PageDescription from \"../Components/Topheader\";\r\nimport { Helmet } from \"react-helmet-async\";\r\nfunction OnSale() {\r\n  const [products, setProducts] = useState([]);\r\n  const [filteredProducts, setFilteredProducts] = useState([]);\r\n  const [sortOption, setSortOption] = useState(\"Newest\");\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [filters, setFilters] = useState({\r\n    brands: [],\r\n    colors: [],\r\n    tags: [],\r\n    priceRange: [0, 600000], // Wider range initially\r\n  });\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n\r\n  // 🟢 Fetch products\r\n  useEffect(() => {\r\n    const fetchReadyToShipProducts = async () => {\r\n      setIsLoading(true);\r\n      try {\r\n        const { data } = await axios.get(\r\n          \"https://api.thedesigngrit.com/api/products/getproducts/\"\r\n        );\r\n        // Filter products that have salePrice\r\n        const onSaleProducts = data.filter(\r\n          (product) =>\r\n            product.salePrice &&\r\n            product.status === true &&\r\n            product.promotionApproved === true &&\r\n            product.brandId.status === \"active\"\r\n        );\r\n        setProducts(onSaleProducts);\r\n        setFilteredProducts(onSaleProducts);\r\n      } catch (error) {\r\n        console.error(\"Error fetching products:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchReadyToShipProducts();\r\n  }, []);\r\n\r\n  // 🟢 Apply filters and sorting\r\n  useEffect(() => {\r\n    const applyFiltersAndSorting = () => {\r\n      let filtered = [...products];\r\n      console.log(\"Initial products:\", products); // Add this\r\n      products.forEach((product, index) => {\r\n        console.log(`Product ${index}:`, {\r\n          name: product.name,\r\n          brand: product.brandId.brandName,\r\n          colors: product.colors,\r\n          tags: product.tags,\r\n          price: product.price,\r\n          salePrice: product.salePrice,\r\n          createdAt: product.createdAt,\r\n        });\r\n      });\r\n      if (filters.hasCAD) {\r\n        filtered = filtered.filter((product) => product.cadFile);\r\n      }\r\n\r\n      // Sale Price filter\r\n      if (filters.hasSalePrice) {\r\n        filtered = filtered.filter((product) => product.salePrice);\r\n      }\r\n      // Brand filter\r\n      if (filters.brands.length > 0) {\r\n        filtered = filtered.filter(\r\n          (product) =>\r\n            product.brandId &&\r\n            product.brandId.brandName &&\r\n            filters.brands.includes(product.brandId.brandName)\r\n        );\r\n      }\r\n\r\n      // Color filter\r\n      if (filters.colors.length > 0) {\r\n        filtered = filtered.filter((product) =>\r\n          product.colors?.some((color) => filters.colors.includes(color))\r\n        );\r\n      }\r\n\r\n      // Tags filter\r\n      if (filters.tags.length > 0) {\r\n        filtered = filtered.filter((product) =>\r\n          product.tags?.some((tag) => filters.tags.includes(tag))\r\n        );\r\n      }\r\n\r\n      // Price filter\r\n      filtered = filtered.filter((product) => {\r\n        const price = product.salePrice || product.price;\r\n        return price >= filters.priceRange[0] && price <= filters.priceRange[1];\r\n      });\r\n\r\n      // Sorting\r\n      switch (sortOption) {\r\n        case \"Newest\":\r\n          filtered.sort(\r\n            (a, b) => new Date(b.createdAt) - new Date(a.createdAt)\r\n          );\r\n          break;\r\n        case \"Price: Low to High\":\r\n          filtered.sort(\r\n            (a, b) => (a.salePrice || a.price) - (b.salePrice || b.price)\r\n          );\r\n          break;\r\n        case \"Price: High to Low\":\r\n          filtered.sort(\r\n            (a, b) => (b.salePrice || b.price) - (a.salePrice || a.price)\r\n          );\r\n          break;\r\n        case \"Alphabetical: A-Z\":\r\n          filtered.sort((a, b) => a.name.localeCompare(b.name));\r\n          break;\r\n        case \"Alphabetical: Z-A\":\r\n          filtered.sort((a, b) => b.name.localeCompare(a.name));\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n      console.log(\"Filtered products:\", filtered); // Add this\r\n\r\n      setFilteredProducts(filtered);\r\n    };\r\n\r\n    applyFiltersAndSorting();\r\n  }, [products, filters, sortOption]);\r\n\r\n  const handleFilterChange = (newFilters) => {\r\n    setFilters(newFilters);\r\n  };\r\n\r\n  const handleCADFilterChange = (value) => {\r\n    setFilters((prev) => ({ ...prev, hasCAD: value }));\r\n  };\r\n\r\n  // Handle Sale Price filter toggle\r\n  const handleSalePriceFilterChange = (value) => {\r\n    setFilters((prev) => ({ ...prev, hasSalePrice: value }));\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ minHeight: \"100vh\", display: \"flex\", flexDirection: \"column\" }}>\r\n      <Helmet>\r\n        <title>On Sale Furniture | TheDesignGrit</title>\r\n        <meta\r\n          name=\"description\"\r\n          content=\"Explore premium Egyptian-designed furniture at discounted prices. Limited-time offers on stylish and high-quality products at TheDesignGrit.\"\r\n        />\r\n        <meta\r\n          name=\"keywords\"\r\n          content=\"furniture sale, discounts, TheDesignGrit, Egyptian furniture, modern deals, home decor sale\"\r\n        />\r\n        <meta name=\"robots\" content=\"index, follow\" />\r\n        <link\r\n          rel=\"canonical\"\r\n          href=\"https://thedesigngrit.com/products/onsale\"\r\n        />\r\n\r\n        {/* Open Graph for Facebook/LinkedIn/etc. */}\r\n        <meta property=\"og:title\" content=\"On Sale Furniture | TheDesignGrit\" />\r\n        <meta\r\n          property=\"og:description\"\r\n          content=\"Browse our collection of furniture on sale. Elevate your home with elegant, affordable pieces crafted by top Egyptian designers.\"\r\n        />\r\n        <meta\r\n          property=\"og:image\"\r\n          content=\"https://thedesigngrit.com/Assets/sale-banner.webp\"\r\n        />\r\n        <meta\r\n          property=\"og:url\"\r\n          content=\"https://thedesigngrit.com/products/onsale\"\r\n        />\r\n        <meta property=\"og:type\" content=\"website\" />\r\n\r\n        {/* Twitter Card (optional but good practice) */}\r\n        <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n        <meta\r\n          name=\"twitter:title\"\r\n          content=\"On Sale Furniture | TheDesignGrit\"\r\n        />\r\n        <meta\r\n          name=\"twitter:description\"\r\n          content=\"Discounted designer furniture from Egypt's top brands. Shop now at TheDesignGrit.\"\r\n        />\r\n      </Helmet>\r\n\r\n      <Header />\r\n      <PageDescription\r\n        name=\"On Sale Products\"\r\n        description=\"Discover products that are in Sale and hurry up!\"\r\n      />\r\n\r\n      {/* TopFilter - hidden on mobile, visible on desktop */}\r\n      <Box\r\n        sx={{\r\n          display: { xs: \"none\", md: \"flex\" },\r\n          justifyContent: \"flex-end\",\r\n          px: { xs: 2, md: 3 },\r\n          py: 2,\r\n        }}\r\n      >\r\n        <TopFilter\r\n          sortOption={sortOption}\r\n          setSortOption={setSortOption}\r\n          onCADFilterChange={handleCADFilterChange}\r\n          onSalePriceFilterChange={handleSalePriceFilterChange}\r\n          hasCAD={filters.hasCAD}\r\n          hasSalePrice={filters.hasSalePrice}\r\n        />\r\n      </Box>\r\n\r\n      <Grid\r\n        container\r\n        spacing={2}\r\n        sx={{\r\n          flex: 1,\r\n          px: { xs: 2, md: 3 },\r\n          pb: 4,\r\n        }}\r\n      >\r\n        <Grid\r\n          item\r\n          xs={12}\r\n          sm={4}\r\n          md={3}\r\n          sx={{\r\n            paddingLeft: { xs: 0, md: 0, lg: 0, xl: 0 },\r\n            \"@media (min-width:1025px)\": {\r\n              paddingLeft: \"66px !important\",\r\n            },\r\n          }}\r\n        >\r\n          <FilterSection\r\n            onFilterChange={handleFilterChange}\r\n            products={products}\r\n            currentFilters={filters}\r\n            sortOption={sortOption}\r\n            setSortOption={setSortOption}\r\n            onCADFilterChange={handleCADFilterChange}\r\n            onSalePriceFilterChange={handleSalePriceFilterChange}\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} md={9}>\r\n          {isLoading ? (\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                minHeight: \"300px\",\r\n              }}\r\n            >\r\n              <CircularProgress\r\n                size={60}\r\n                thickness={4}\r\n                sx={{ color: \"#6b7b58\" }}\r\n              />\r\n            </Box>\r\n          ) : filteredProducts.length > 0 ? (\r\n            <ProductCards products={filteredProducts} />\r\n          ) : products.length === 0 ? (\r\n            <Typography variant=\"h6\" sx={{ textAlign: \"center\", mt: 4 }}>\r\n              No products have sale at the moment.\r\n            </Typography>\r\n          ) : (\r\n            <Typography variant=\"h6\" sx={{ textAlign: \"center\", mt: 4 }}>\r\n              No products match your filters. Try adjusting your criteria.\r\n            </Typography>\r\n          )}\r\n        </Grid>\r\n      </Grid>\r\n      <Footer />\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default OnSale;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,gBAAgB,EAChBC,aAAa,QACR,eAAe;AACtB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC5C,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,QAAQ,CAAC;EACtD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC;IACrC6B,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAE;EAC3B,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG3B,aAAa,CAAC,mBAAmB,CAAC;;EAEnD;EACAL,SAAS,CAAC,MAAM;IACd,MAAMiC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;MAC3CR,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QACF,MAAM;UAAES;QAAK,CAAC,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAC9B,yDACF,CAAC;QACD;QACA,MAAMC,cAAc,GAAGF,IAAI,CAACG,MAAM,CAC/BC,OAAO,IACNA,OAAO,CAACC,SAAS,IACjBD,OAAO,CAACE,MAAM,KAAK,IAAI,IACvBF,OAAO,CAACG,iBAAiB,KAAK,IAAI,IAClCH,OAAO,CAACI,OAAO,CAACF,MAAM,KAAK,QAC/B,CAAC;QACDrB,WAAW,CAACiB,cAAc,CAAC;QAC3Bf,mBAAmB,CAACe,cAAc,CAAC;MACrC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD,CAAC,SAAS;QACRlB,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDQ,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjC,SAAS,CAAC,MAAM;IACd,MAAM6C,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIC,QAAQ,GAAG,CAAC,GAAG5B,QAAQ,CAAC;MAC5B0B,OAAO,CAACG,GAAG,CAAC,mBAAmB,EAAE7B,QAAQ,CAAC,CAAC,CAAC;MAC5CA,QAAQ,CAAC8B,OAAO,CAAC,CAACV,OAAO,EAAEW,KAAK,KAAK;QACnCL,OAAO,CAACG,GAAG,CAAC,WAAWE,KAAK,GAAG,EAAE;UAC/BC,IAAI,EAAEZ,OAAO,CAACY,IAAI;UAClBC,KAAK,EAAEb,OAAO,CAACI,OAAO,CAACU,SAAS;UAChCvB,MAAM,EAAES,OAAO,CAACT,MAAM;UACtBC,IAAI,EAAEQ,OAAO,CAACR,IAAI;UAClBuB,KAAK,EAAEf,OAAO,CAACe,KAAK;UACpBd,SAAS,EAAED,OAAO,CAACC,SAAS;UAC5Be,SAAS,EAAEhB,OAAO,CAACgB;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI5B,OAAO,CAAC6B,MAAM,EAAE;QAClBT,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAEC,OAAO,IAAKA,OAAO,CAACkB,OAAO,CAAC;MAC1D;;MAEA;MACA,IAAI9B,OAAO,CAAC+B,YAAY,EAAE;QACxBX,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAEC,OAAO,IAAKA,OAAO,CAACC,SAAS,CAAC;MAC5D;MACA;MACA,IAAIb,OAAO,CAACE,MAAM,CAAC8B,MAAM,GAAG,CAAC,EAAE;QAC7BZ,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CACvBC,OAAO,IACNA,OAAO,CAACI,OAAO,IACfJ,OAAO,CAACI,OAAO,CAACU,SAAS,IACzB1B,OAAO,CAACE,MAAM,CAAC+B,QAAQ,CAACrB,OAAO,CAACI,OAAO,CAACU,SAAS,CACrD,CAAC;MACH;;MAEA;MACA,IAAI1B,OAAO,CAACG,MAAM,CAAC6B,MAAM,GAAG,CAAC,EAAE;QAC7BZ,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAEC,OAAO;UAAA,IAAAsB,eAAA;UAAA,QAAAA,eAAA,GACjCtB,OAAO,CAACT,MAAM,cAAA+B,eAAA,uBAAdA,eAAA,CAAgBC,IAAI,CAAEC,KAAK,IAAKpC,OAAO,CAACG,MAAM,CAAC8B,QAAQ,CAACG,KAAK,CAAC,CAAC;QAAA,CACjE,CAAC;MACH;;MAEA;MACA,IAAIpC,OAAO,CAACI,IAAI,CAAC4B,MAAM,GAAG,CAAC,EAAE;QAC3BZ,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAEC,OAAO;UAAA,IAAAyB,aAAA;UAAA,QAAAA,aAAA,GACjCzB,OAAO,CAACR,IAAI,cAAAiC,aAAA,uBAAZA,aAAA,CAAcF,IAAI,CAAEG,GAAG,IAAKtC,OAAO,CAACI,IAAI,CAAC6B,QAAQ,CAACK,GAAG,CAAC,CAAC;QAAA,CACzD,CAAC;MACH;;MAEA;MACAlB,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAEC,OAAO,IAAK;QACtC,MAAMe,KAAK,GAAGf,OAAO,CAACC,SAAS,IAAID,OAAO,CAACe,KAAK;QAChD,OAAOA,KAAK,IAAI3B,OAAO,CAACK,UAAU,CAAC,CAAC,CAAC,IAAIsB,KAAK,IAAI3B,OAAO,CAACK,UAAU,CAAC,CAAC,CAAC;MACzE,CAAC,CAAC;;MAEF;MACA,QAAQT,UAAU;QAChB,KAAK,QAAQ;UACXwB,QAAQ,CAACmB,IAAI,CACX,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACb,SAAS,CAAC,GAAG,IAAIc,IAAI,CAACF,CAAC,CAACZ,SAAS,CACxD,CAAC;UACD;QACF,KAAK,oBAAoB;UACvBR,QAAQ,CAACmB,IAAI,CACX,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAAC3B,SAAS,IAAI2B,CAAC,CAACb,KAAK,KAAKc,CAAC,CAAC5B,SAAS,IAAI4B,CAAC,CAACd,KAAK,CAC9D,CAAC;UACD;QACF,KAAK,oBAAoB;UACvBP,QAAQ,CAACmB,IAAI,CACX,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC5B,SAAS,IAAI4B,CAAC,CAACd,KAAK,KAAKa,CAAC,CAAC3B,SAAS,IAAI2B,CAAC,CAACb,KAAK,CAC9D,CAAC;UACD;QACF,KAAK,mBAAmB;UACtBP,QAAQ,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChB,IAAI,CAACmB,aAAa,CAACF,CAAC,CAACjB,IAAI,CAAC,CAAC;UACrD;QACF,KAAK,mBAAmB;UACtBJ,QAAQ,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACjB,IAAI,CAACmB,aAAa,CAACH,CAAC,CAAChB,IAAI,CAAC,CAAC;UACrD;QACF;UACE;MACJ;MACAN,OAAO,CAACG,GAAG,CAAC,oBAAoB,EAAED,QAAQ,CAAC,CAAC,CAAC;;MAE7CzB,mBAAmB,CAACyB,QAAQ,CAAC;IAC/B,CAAC;IAEDD,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAAC3B,QAAQ,EAAEQ,OAAO,EAAEJ,UAAU,CAAC,CAAC;EAEnC,MAAMgD,kBAAkB,GAAIC,UAAU,IAAK;IACzC5C,UAAU,CAAC4C,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;IACvC9C,UAAU,CAAE+C,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEnB,MAAM,EAAEkB;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAME,2BAA2B,GAAIF,KAAK,IAAK;IAC7C9C,UAAU,CAAE+C,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEjB,YAAY,EAAEgB;IAAM,CAAC,CAAC,CAAC;EAC1D,CAAC;EAED,oBACE1D,OAAA,CAACd,GAAG;IAAC2E,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACxEjE,OAAA,CAACF,MAAM;MAAAmE,QAAA,gBACLjE,OAAA;QAAAiE,QAAA,EAAO;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChDrE,OAAA;QACEmC,IAAI,EAAC,aAAa;QAClBmC,OAAO,EAAC;MAA8I;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvJ,CAAC,eACFrE,OAAA;QACEmC,IAAI,EAAC,UAAU;QACfmC,OAAO,EAAC;MAA6F;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtG,CAAC,eACFrE,OAAA;QAAMmC,IAAI,EAAC,QAAQ;QAACmC,OAAO,EAAC;MAAe;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CrE,OAAA;QACEuE,GAAG,EAAC,WAAW;QACfC,IAAI,EAAC;MAA2C;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAGFrE,OAAA;QAAMyE,QAAQ,EAAC,UAAU;QAACH,OAAO,EAAC;MAAmC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxErE,OAAA;QACEyE,QAAQ,EAAC,gBAAgB;QACzBH,OAAO,EAAC;MAAkI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3I,CAAC,eACFrE,OAAA;QACEyE,QAAQ,EAAC,UAAU;QACnBH,OAAO,EAAC;MAAmD;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACFrE,OAAA;QACEyE,QAAQ,EAAC,QAAQ;QACjBH,OAAO,EAAC;MAA2C;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACFrE,OAAA;QAAMyE,QAAQ,EAAC,SAAS;QAACH,OAAO,EAAC;MAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG7CrE,OAAA;QAAMmC,IAAI,EAAC,cAAc;QAACmC,OAAO,EAAC;MAAqB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DrE,OAAA;QACEmC,IAAI,EAAC,eAAe;QACpBmC,OAAO,EAAC;MAAmC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACFrE,OAAA;QACEmC,IAAI,EAAC,qBAAqB;QAC1BmC,OAAO,EAAC;MAAmF;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAETrE,OAAA,CAACR,MAAM;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVrE,OAAA,CAACH,eAAe;MACdsC,IAAI,EAAC,kBAAkB;MACvBuC,WAAW,EAAC;IAAkD;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGFrE,OAAA,CAACd,GAAG;MACF2E,EAAE,EAAE;QACFE,OAAO,EAAE;UAAEY,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAO,CAAC;QACnCC,cAAc,EAAE,UAAU;QAC1BC,EAAE,EAAE;UAAEH,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACpBG,EAAE,EAAE;MACN,CAAE;MAAAd,QAAA,eAEFjE,OAAA,CAACL,SAAS;QACRY,UAAU,EAAEA,UAAW;QACvBC,aAAa,EAAEA,aAAc;QAC7BwE,iBAAiB,EAAEvB,qBAAsB;QACzCwB,uBAAuB,EAAErB,2BAA4B;QACrDpB,MAAM,EAAE7B,OAAO,CAAC6B,MAAO;QACvBE,YAAY,EAAE/B,OAAO,CAAC+B;MAAa;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENrE,OAAA,CAACb,IAAI;MACH+F,SAAS;MACTC,OAAO,EAAE,CAAE;MACXtB,EAAE,EAAE;QACFuB,IAAI,EAAE,CAAC;QACPN,EAAE,EAAE;UAAEH,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACpBS,EAAE,EAAE;MACN,CAAE;MAAApB,QAAA,gBAEFjE,OAAA,CAACb,IAAI;QACHmG,IAAI;QACJX,EAAE,EAAE,EAAG;QACPY,EAAE,EAAE,CAAE;QACNX,EAAE,EAAE,CAAE;QACNf,EAAE,EAAE;UACF2B,WAAW,EAAE;YAAEb,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEa,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAC;UAC3C,2BAA2B,EAAE;YAC3BF,WAAW,EAAE;UACf;QACF,CAAE;QAAAvB,QAAA,eAEFjE,OAAA,CAACN,aAAa;UACZiG,cAAc,EAAEpC,kBAAmB;UACnCpD,QAAQ,EAAEA,QAAS;UACnByF,cAAc,EAAEjF,OAAQ;UACxBJ,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7BwE,iBAAiB,EAAEvB,qBAAsB;UACzCwB,uBAAuB,EAAErB;QAA4B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPrE,OAAA,CAACb,IAAI;QAACmG,IAAI;QAACX,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,EACtBxD,SAAS,gBACRT,OAAA,CAACd,GAAG;UACF2E,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACfc,cAAc,EAAE,QAAQ;YACxBgB,UAAU,EAAE,QAAQ;YACpB/B,SAAS,EAAE;UACb,CAAE;UAAAG,QAAA,eAEFjE,OAAA,CAACX,gBAAgB;YACfyG,IAAI,EAAE,EAAG;YACTC,SAAS,EAAE,CAAE;YACblC,EAAE,EAAE;cAAEd,KAAK,EAAE;YAAU;UAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,GACJhE,gBAAgB,CAACsC,MAAM,GAAG,CAAC,gBAC7B3C,OAAA,CAACP,YAAY;UAACU,QAAQ,EAAEE;QAAiB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC1ClE,QAAQ,CAACwC,MAAM,KAAK,CAAC,gBACvB3C,OAAA,CAACZ,UAAU;UAAC4G,OAAO,EAAC,IAAI;UAACnC,EAAE,EAAE;YAAEoC,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEbrE,OAAA,CAACZ,UAAU;UAAC4G,OAAO,EAAC,IAAI;UAACnC,EAAE,EAAE;YAAEoC,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPrE,OAAA,CAACJ,MAAM;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACnE,EAAA,CApRQD,MAAM;EAAA,QAWIX,aAAa;AAAA;AAAA6G,EAAA,GAXvBlG,MAAM;AAsRf,eAAeA,MAAM;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}