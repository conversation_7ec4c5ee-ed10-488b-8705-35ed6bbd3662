{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\home\\\\bestSeller.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useContext } from \"react\";\nimport axios from \"axios\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Navigation, Pagination, Keyboard, Mousewheel } from \"swiper/modules\";\nimport \"swiper/css\";\nimport \"swiper/css/navigation\";\nimport \"swiper/css/pagination\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { UserContext } from \"../../utils/userContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductSlider = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [favorites, setFavorites] = useState({});\n  const navigate = useNavigate();\n  const {\n    userSession\n  } = useContext(UserContext);\n  const [windowWidth, setWindowWidth] = useState(window.innerWidth);\n  const swiperRef = useRef(null);\n  const sliderContainerRef = useRef(null);\n\n  // Update window width on resize\n  useEffect(() => {\n    const handleResize = () => {\n      setWindowWidth(window.innerWidth);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const isMobile = windowWidth <= 768;\n  const isTablet = windowWidth > 768 && windowWidth <= 1024;\n  const visibleCount = isMobile ? 1 : isTablet ? 3 : 5;\n\n  // Fetch the user's favorite products on component mount\n  useEffect(() => {\n    const fetchFavorites = async () => {\n      if (!userSession) return;\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/favorites/${userSession.id}`);\n        if (response.ok) {\n          const favoritesData = await response.json();\n          const favoriteIds = favoritesData.map(prod => prod._id);\n          const favoritesMap = {};\n          favoriteIds.forEach(id => {\n            favoritesMap[id] = true;\n          });\n          setFavorites(favoritesMap);\n        }\n      } catch (error) {\n        console.error(\"Error fetching favorites:\", error);\n      }\n    };\n    fetchFavorites();\n  }, [userSession]);\n\n  // Toggle the favorite status\n  const toggleFavorite = async (event, productId) => {\n    event.stopPropagation(); // Prevent triggering card click\n\n    if (!userSession) return; // If there's no user session, prevent posting\n\n    const isFavorite = favorites[productId];\n    const endpoint = isFavorite ? \"/remove\" : \"/add\";\n    const requestPayload = {\n      userSession,\n      productId: productId\n    };\n    try {\n      const response = await fetch(`https://api.thedesigngrit.com/api/favorites${endpoint}`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestPayload)\n      });\n      if (response.ok) {\n        setFavorites(prev => ({\n          ...prev,\n          [productId]: !isFavorite\n        }));\n      } else {\n        console.error(\"Error: Unable to update favorite status.\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  const fetchBestSellers = useCallback(async () => {\n    try {\n      setIsLoading(true);\n      const response = await axios.get(\"https://api.thedesigngrit.com/api/orders/bestsellers\");\n      const data = response.data;\n      setProducts(data);\n    } catch (error) {\n      console.error(\"Error fetching bestsellers:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchBestSellers();\n  }, [fetchBestSellers]);\n\n  // Handle wheel events for the entire slider container\n  useEffect(() => {\n    const handleWheel = event => {\n      if (!swiperRef.current || !sliderContainerRef.current) return;\n      const swiper = swiperRef.current;\n      const {\n        deltaX,\n        deltaY\n      } = event;\n\n      // Determine if we should scroll horizontally or vertically\n      const isHorizontalScroll = Math.abs(deltaX) > Math.abs(deltaY);\n      const isAtBeginning = swiper.isBeginning;\n      const isAtEnd = swiper.isEnd;\n\n      // If scrolling horizontally or within the slider's bounds\n      if (isHorizontalScroll || !isAtBeginning && !isAtEnd) {\n        // Let the swiper handle horizontal scrolling\n        swiper.mousewheel.handleMouseWheel(event);\n        event.preventDefault();\n      }\n      // Otherwise, let the page scroll naturally (vertical scrolling)\n    };\n    const container = sliderContainerRef.current;\n    if (container) {\n      container.addEventListener(\"wheel\", handleWheel, {\n        passive: false\n      });\n    }\n    return () => {\n      if (container) {\n        container.removeEventListener(\"wheel\", handleWheel);\n      }\n    };\n  }, []);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"slider-container-home\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"slider-title\",\n        children: \"BEST SELLERS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-placeholder\",\n        style: {\n          height: \"400px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"slider-container-home\",\n    ref: sliderContainerRef,\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"slider-title\",\n      children: \"BEST SELLERS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Swiper, {\n      modules: [Navigation, Pagination, Keyboard, Mousewheel],\n      spaceBetween: isMobile ? 10 : 20,\n      slidesPerView: visibleCount,\n      navigation: !isMobile,\n      pagination: isMobile ? {\n        clickable: true\n      } : false,\n      keyboard: {\n        enabled: true\n      },\n      mousewheel: {\n        enabled: true,\n        forceToAxis: true,\n        sensitivity: 1,\n        thresholdDelta: 50,\n        thresholdTime: 300\n      },\n      grabCursor: true,\n      loop: true,\n      className: \"bestseller-swiper\",\n      onSwiper: swiper => {\n        swiperRef.current = swiper;\n      }\n      // breakpoints={{\n      //   // When window width is >= 0px (mobile)\n      //   0: {\n      //     slidesPerView: 1,\n      //     spaceBetween: 10,\n      //   },\n      //   426: {\n      //     slidesPerView: 2,\n      //     spaceBetween: 10,\n      //   },\n      //   769: {\n      //     slidesPerView: 3,\n      //     spaceBetween: 20,\n      //   },\n      //   961: {\n      //     slidesPerView: 3,\n      //     spaceBetween: 30,\n      //   },\n      //   // When window width is >= 1024px (desktop)\n      //   1024: {\n      //     slidesPerView: 4,\n      //     spaceBetween: 20,\n      //   },\n      //   1025: {\n      //     slidesPerView: 5,\n      //     spaceBetween: 20,\n      //   },\n      // }}\n      ,\n      children: products.filter(product => product && product._id && product.mainImage && product.name).map((product, index) => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-card-bestseller\",\n          style: {\n            cursor: \"pointer\",\n            position: \"relative\"\n          },\n          onClick: () => navigate(`/product/${product._id}`),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: \"absolute\",\n              top: \"8px\",\n              right: \"8px\",\n              zIndex: 10,\n              backgroundColor: \"#fff\",\n              borderRadius: \"50%\",\n              width: \"32px\",\n              height: \"32px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              boxShadow: \"0px 2px 4px rgba(0, 0, 0, 0.1)\",\n              cursor: \"pointer\"\n            },\n            onClick: event => toggleFavorite(event, product._id),\n            children: favorites[product._id] ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {\n              style: {\n                color: \"red\",\n                fontSize: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n              style: {\n                color: \"#000\",\n                fontSize: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-image-home\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}?width=250&height=200&format=webp`,\n              alt: product.name,\n              loading: index < visibleCount ? \"eager\" : \"lazy\",\n              width: \"300\",\n              height: \"200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"related-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"related-category\",\n              children: product === null || product === void 0 ? void 0 : product.brandName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"related-name\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this), product.salePrice ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"related-price\",\n                style: {\n                  textDecoration: \"line-through\",\n                  color: \"#999\"\n                },\n                children: [product.price.toLocaleString(\"en-US\", {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                }), \" \", \"E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"related-price\",\n                style: {\n                  color: \"#e74c3c\",\n                  fontWeight: \"bold\"\n                },\n                children: [product.salePrice.toLocaleString(\"en-US\", {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                }), \" \", \"E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"related-price\",\n              children: [product.price.toLocaleString(\"en-US\", {\n                minimumFractionDigits: 2,\n                maximumFractionDigits: 2\n              }), \" \", \"E\\xA3\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this)\n      }, product._id || index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductSlider, \"eeSgZ1qMQFh+SBk8qEdFVcBUcD4=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductSlider;\nexport default ProductSlider;\nvar _c;\n$RefreshReg$(_c, \"ProductSlider\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useContext", "axios", "useNavigate", "Swiper", "SwiperSlide", "Navigation", "Pagination", "Keyboard", "Mousewheel", "FavoriteBorderIcon", "FavoriteIcon", "UserContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductSlider", "_s", "products", "setProducts", "isLoading", "setIsLoading", "favorites", "setFavorites", "navigate", "userSession", "windowWidth", "setW<PERSON>owWidth", "window", "innerWidth", "swiperRef", "sliderContainerRef", "handleResize", "addEventListener", "removeEventListener", "isMobile", "isTablet", "visibleCount", "fetchFavorites", "response", "fetch", "id", "ok", "favoritesData", "json", "favoriteIds", "map", "prod", "_id", "favoritesMap", "for<PERSON>ach", "error", "console", "toggleFavorite", "event", "productId", "stopPropagation", "isFavorite", "endpoint", "requestPayload", "method", "headers", "body", "JSON", "stringify", "prev", "fetchBestSellers", "get", "data", "handleWheel", "current", "swiper", "deltaX", "deltaY", "isHorizontalScroll", "Math", "abs", "isAtBeginning", "isBeginning", "isAtEnd", "isEnd", "mousewheel", "handleMouseWheel", "preventDefault", "container", "passive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "height", "ref", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "navigation", "pagination", "clickable", "keyboard", "enabled", "forceToAxis", "sensitivity", "thresholdDel<PERSON>", "thresholdTime", "grabCursor", "loop", "onSwiper", "filter", "product", "mainImage", "name", "index", "cursor", "position", "onClick", "top", "right", "zIndex", "backgroundColor", "borderRadius", "width", "display", "alignItems", "justifyContent", "boxShadow", "color", "fontSize", "src", "alt", "loading", "brandName", "salePrice", "textDecoration", "price", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "fontWeight", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/home/<USER>"], "sourcesContent": ["import React, {\r\n  useState,\r\n  useEffect,\r\n  useRef,\r\n  useCallback,\r\n  useContext,\r\n} from \"react\";\r\nimport axios from \"axios\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport { Navigation, Pagination, Keyboard, Mousewheel } from \"swiper/modules\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/navigation\";\r\nimport \"swiper/css/pagination\";\r\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport { UserContext } from \"../../utils/userContext\";\r\n\r\nconst ProductSlider = () => {\r\n  const [products, setProducts] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [favorites, setFavorites] = useState({});\r\n  const navigate = useNavigate();\r\n  const { userSession } = useContext(UserContext);\r\n  const [windowWidth, setWindowWidth] = useState(window.innerWidth);\r\n  const swiperRef = useRef(null);\r\n  const sliderContainerRef = useRef(null);\r\n\r\n  // Update window width on resize\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setWindowWidth(window.innerWidth);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const isMobile = windowWidth <= 768;\r\n  const isTablet = windowWidth > 768 && windowWidth <= 1024;\r\n  const visibleCount = isMobile ? 1 : isTablet ? 3 : 5;\r\n\r\n  // Fetch the user's favorite products on component mount\r\n  useEffect(() => {\r\n    const fetchFavorites = async () => {\r\n      if (!userSession) return;\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/favorites/${userSession.id}`\r\n        );\r\n        if (response.ok) {\r\n          const favoritesData = await response.json();\r\n          const favoriteIds = favoritesData.map((prod) => prod._id);\r\n          const favoritesMap = {};\r\n          favoriteIds.forEach((id) => {\r\n            favoritesMap[id] = true;\r\n          });\r\n          setFavorites(favoritesMap);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching favorites:\", error);\r\n      }\r\n    };\r\n    fetchFavorites();\r\n  }, [userSession]);\r\n\r\n  // Toggle the favorite status\r\n  const toggleFavorite = async (event, productId) => {\r\n    event.stopPropagation(); // Prevent triggering card click\r\n\r\n    if (!userSession) return; // If there's no user session, prevent posting\r\n\r\n    const isFavorite = favorites[productId];\r\n    const endpoint = isFavorite ? \"/remove\" : \"/add\";\r\n    const requestPayload = {\r\n      userSession,\r\n      productId: productId,\r\n    };\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/favorites${endpoint}`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(requestPayload),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        setFavorites((prev) => ({\r\n          ...prev,\r\n          [productId]: !isFavorite,\r\n        }));\r\n      } else {\r\n        console.error(\"Error: Unable to update favorite status.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchBestSellers = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await axios.get(\r\n        \"https://api.thedesigngrit.com/api/orders/bestsellers\"\r\n      );\r\n      const data = response.data;\r\n      setProducts(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching bestsellers:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchBestSellers();\r\n  }, [fetchBestSellers]);\r\n\r\n  // Handle wheel events for the entire slider container\r\n  useEffect(() => {\r\n    const handleWheel = (event) => {\r\n      if (!swiperRef.current || !sliderContainerRef.current) return;\r\n\r\n      const swiper = swiperRef.current;\r\n      const { deltaX, deltaY } = event;\r\n\r\n      // Determine if we should scroll horizontally or vertically\r\n      const isHorizontalScroll = Math.abs(deltaX) > Math.abs(deltaY);\r\n      const isAtBeginning = swiper.isBeginning;\r\n      const isAtEnd = swiper.isEnd;\r\n\r\n      // If scrolling horizontally or within the slider's bounds\r\n      if (isHorizontalScroll || (!isAtBeginning && !isAtEnd)) {\r\n        // Let the swiper handle horizontal scrolling\r\n        swiper.mousewheel.handleMouseWheel(event);\r\n        event.preventDefault();\r\n      }\r\n      // Otherwise, let the page scroll naturally (vertical scrolling)\r\n    };\r\n\r\n    const container = sliderContainerRef.current;\r\n    if (container) {\r\n      container.addEventListener(\"wheel\", handleWheel, { passive: false });\r\n    }\r\n\r\n    return () => {\r\n      if (container) {\r\n        container.removeEventListener(\"wheel\", handleWheel);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"slider-container-home\">\r\n        <h1 className=\"slider-title\">BEST SELLERS</h1>\r\n        <div className=\"loading-placeholder\" style={{ height: \"400px\" }} />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"slider-container-home\" ref={sliderContainerRef}>\r\n      <h2 className=\"slider-title\">BEST SELLERS</h2>\r\n\r\n      <Swiper\r\n        modules={[Navigation, Pagination, Keyboard, Mousewheel]}\r\n        spaceBetween={isMobile ? 10 : 20}\r\n        slidesPerView={visibleCount}\r\n        navigation={!isMobile}\r\n        pagination={isMobile ? { clickable: true } : false}\r\n        keyboard={{ enabled: true }}\r\n        mousewheel={{\r\n          enabled: true,\r\n          forceToAxis: true,\r\n          sensitivity: 1,\r\n          thresholdDelta: 50,\r\n          thresholdTime: 300,\r\n        }}\r\n        grabCursor={true}\r\n        loop={true}\r\n        className=\"bestseller-swiper\"\r\n        onSwiper={(swiper) => {\r\n          swiperRef.current = swiper;\r\n        }}\r\n        // breakpoints={{\r\n        //   // When window width is >= 0px (mobile)\r\n        //   0: {\r\n        //     slidesPerView: 1,\r\n        //     spaceBetween: 10,\r\n        //   },\r\n        //   426: {\r\n        //     slidesPerView: 2,\r\n        //     spaceBetween: 10,\r\n        //   },\r\n        //   769: {\r\n        //     slidesPerView: 3,\r\n        //     spaceBetween: 20,\r\n        //   },\r\n        //   961: {\r\n        //     slidesPerView: 3,\r\n        //     spaceBetween: 30,\r\n        //   },\r\n        //   // When window width is >= 1024px (desktop)\r\n        //   1024: {\r\n        //     slidesPerView: 4,\r\n        //     spaceBetween: 20,\r\n        //   },\r\n        //   1025: {\r\n        //     slidesPerView: 5,\r\n        //     spaceBetween: 20,\r\n        //   },\r\n        // }}\r\n      >\r\n        {products\r\n          .filter(\r\n            (product) =>\r\n              product && product._id && product.mainImage && product.name\r\n          )\r\n          .map((product, index) => (\r\n            <SwiperSlide key={product._id || index}>\r\n              <div\r\n                className=\"product-card-bestseller\"\r\n                style={{\r\n                  cursor: \"pointer\",\r\n                  position: \"relative\",\r\n                }}\r\n                onClick={() => navigate(`/product/${product._id}`)}\r\n              >\r\n                {/* Favorite Icon */}\r\n                <div\r\n                  style={{\r\n                    position: \"absolute\",\r\n                    top: \"8px\",\r\n                    right: \"8px\",\r\n                    zIndex: 10,\r\n                    backgroundColor: \"#fff\",\r\n                    borderRadius: \"50%\",\r\n                    width: \"32px\",\r\n                    height: \"32px\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    justifyContent: \"center\",\r\n                    boxShadow: \"0px 2px 4px rgba(0, 0, 0, 0.1)\",\r\n                    cursor: \"pointer\",\r\n                  }}\r\n                  onClick={(event) => toggleFavorite(event, product._id)}\r\n                >\r\n                  {favorites[product._id] ? (\r\n                    <FavoriteIcon style={{ color: \"red\", fontSize: \"20px\" }} />\r\n                  ) : (\r\n                    <FavoriteBorderIcon\r\n                      style={{ color: \"#000\", fontSize: \"20px\" }}\r\n                    />\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"product-image-home\">\r\n                  <img\r\n                    src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}?width=250&height=200&format=webp`}\r\n                    alt={product.name}\r\n                    loading={index < visibleCount ? \"eager\" : \"lazy\"}\r\n                    width=\"300\"\r\n                    height=\"200\"\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"related-info\">\r\n                  <p className=\"related-category\">{product?.brandName}</p>\r\n                  <h3 className=\"related-name\">{product.name}</h3>\r\n                  {product.salePrice ? (\r\n                    <>\r\n                      <p\r\n                        className=\"related-price\"\r\n                        style={{\r\n                          textDecoration: \"line-through\",\r\n                          color: \"#999\",\r\n                        }}\r\n                      >\r\n                        {product.price.toLocaleString(\"en-US\", {\r\n                          minimumFractionDigits: 2,\r\n                          maximumFractionDigits: 2,\r\n                        })}{\" \"}\r\n                        E£\r\n                      </p>\r\n                      <p\r\n                        className=\"related-price\"\r\n                        style={{ color: \"#e74c3c\", fontWeight: \"bold\" }}\r\n                      >\r\n                        {product.salePrice.toLocaleString(\"en-US\", {\r\n                          minimumFractionDigits: 2,\r\n                          maximumFractionDigits: 2,\r\n                        })}{\" \"}\r\n                        E£\r\n                      </p>\r\n                    </>\r\n                  ) : (\r\n                    <p className=\"related-price\">\r\n                      {product.price.toLocaleString(\"en-US\", {\r\n                        minimumFractionDigits: 2,\r\n                        maximumFractionDigits: 2,\r\n                      })}{\" \"}\r\n                      E£\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </SwiperSlide>\r\n          ))}\r\n      </Swiper>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductSlider;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,QAAQ,EACRC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,UAAU,QACL,OAAO;AACd,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAC7E,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM4B,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAY,CAAC,GAAGzB,UAAU,CAACW,WAAW,CAAC;EAC/C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAACgC,MAAM,CAACC,UAAU,CAAC;EACjE,MAAMC,SAAS,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMiC,kBAAkB,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMmC,YAAY,GAAGA,CAAA,KAAM;MACzBL,cAAc,CAACC,MAAM,CAACC,UAAU,CAAC;IACnC,CAAC;IAEDD,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAM;MACXJ,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,QAAQ,GAAGT,WAAW,IAAI,GAAG;EACnC,MAAMU,QAAQ,GAAGV,WAAW,GAAG,GAAG,IAAIA,WAAW,IAAI,IAAI;EACzD,MAAMW,YAAY,GAAGF,QAAQ,GAAG,CAAC,GAAGC,QAAQ,GAAG,CAAC,GAAG,CAAC;;EAEpD;EACAvC,SAAS,CAAC,MAAM;IACd,MAAMyC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACb,WAAW,EAAE;MAElB,IAAI;QACF,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+Cf,WAAW,CAACgB,EAAE,EAC/D,CAAC;QACD,IAAIF,QAAQ,CAACG,EAAE,EAAE;UACf,MAAMC,aAAa,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UAC3C,MAAMC,WAAW,GAAGF,aAAa,CAACG,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,GAAG,CAAC;UACzD,MAAMC,YAAY,GAAG,CAAC,CAAC;UACvBJ,WAAW,CAACK,OAAO,CAAET,EAAE,IAAK;YAC1BQ,YAAY,CAACR,EAAE,CAAC,GAAG,IAAI;UACzB,CAAC,CAAC;UACFlB,YAAY,CAAC0B,YAAY,CAAC;QAC5B;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IACDb,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM4B,cAAc,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,KAAK;IACjDD,KAAK,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEzB,IAAI,CAAC/B,WAAW,EAAE,OAAO,CAAC;;IAE1B,MAAMgC,UAAU,GAAGnC,SAAS,CAACiC,SAAS,CAAC;IACvC,MAAMG,QAAQ,GAAGD,UAAU,GAAG,SAAS,GAAG,MAAM;IAChD,MAAME,cAAc,GAAG;MACrBlC,WAAW;MACX8B,SAAS,EAAEA;IACb,CAAC;IAED,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8CkB,QAAQ,EAAE,EACxD;QACEE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,cAAc;MACrC,CACF,CAAC;MAED,IAAIpB,QAAQ,CAACG,EAAE,EAAE;QACfnB,YAAY,CAAE0C,IAAI,KAAM;UACtB,GAAGA,IAAI;UACP,CAACV,SAAS,GAAG,CAACE;QAChB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLL,OAAO,CAACD,KAAK,CAAC,0CAA0C,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMe,gBAAgB,GAAGnE,WAAW,CAAC,YAAY;IAC/C,IAAI;MACFsB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMkB,QAAQ,GAAG,MAAMtC,KAAK,CAACkE,GAAG,CAC9B,sDACF,CAAC;MACD,MAAMC,IAAI,GAAG7B,QAAQ,CAAC6B,IAAI;MAC1BjD,WAAW,CAACiD,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACR9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAENxB,SAAS,CAAC,MAAM;IACdqE,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACArE,SAAS,CAAC,MAAM;IACd,MAAMwE,WAAW,GAAIf,KAAK,IAAK;MAC7B,IAAI,CAACxB,SAAS,CAACwC,OAAO,IAAI,CAACvC,kBAAkB,CAACuC,OAAO,EAAE;MAEvD,MAAMC,MAAM,GAAGzC,SAAS,CAACwC,OAAO;MAChC,MAAM;QAAEE,MAAM;QAAEC;MAAO,CAAC,GAAGnB,KAAK;;MAEhC;MACA,MAAMoB,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACH,MAAM,CAAC;MAC9D,MAAMI,aAAa,GAAGN,MAAM,CAACO,WAAW;MACxC,MAAMC,OAAO,GAAGR,MAAM,CAACS,KAAK;;MAE5B;MACA,IAAIN,kBAAkB,IAAK,CAACG,aAAa,IAAI,CAACE,OAAQ,EAAE;QACtD;QACAR,MAAM,CAACU,UAAU,CAACC,gBAAgB,CAAC5B,KAAK,CAAC;QACzCA,KAAK,CAAC6B,cAAc,CAAC,CAAC;MACxB;MACA;IACF,CAAC;IAED,MAAMC,SAAS,GAAGrD,kBAAkB,CAACuC,OAAO;IAC5C,IAAIc,SAAS,EAAE;MACbA,SAAS,CAACnD,gBAAgB,CAAC,OAAO,EAAEoC,WAAW,EAAE;QAAEgB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;IAEA,OAAO,MAAM;MACX,IAAID,SAAS,EAAE;QACbA,SAAS,CAAClD,mBAAmB,CAAC,OAAO,EAAEmC,WAAW,CAAC;MACrD;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIjD,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKyE,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpC1E,OAAA;QAAIyE,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9C9E,OAAA;QAAKyE,SAAS,EAAC,qBAAqB;QAACM,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAQ;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC;EAEV;EAEA,oBACE9E,OAAA;IAAKyE,SAAS,EAAC,uBAAuB;IAACQ,GAAG,EAAE/D,kBAAmB;IAAAwD,QAAA,gBAC7D1E,OAAA;MAAIyE,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE9C9E,OAAA,CAACV,MAAM;MACL4F,OAAO,EAAE,CAAC1F,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,CAAE;MACxDwF,YAAY,EAAE7D,QAAQ,GAAG,EAAE,GAAG,EAAG;MACjC8D,aAAa,EAAE5D,YAAa;MAC5B6D,UAAU,EAAE,CAAC/D,QAAS;MACtBgE,UAAU,EAAEhE,QAAQ,GAAG;QAAEiE,SAAS,EAAE;MAAK,CAAC,GAAG,KAAM;MACnDC,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAE;MAC5BrB,UAAU,EAAE;QACVqB,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;MACjB,CAAE;MACFC,UAAU,EAAE,IAAK;MACjBC,IAAI,EAAE,IAAK;MACXtB,SAAS,EAAC,mBAAmB;MAC7BuB,QAAQ,EAAGtC,MAAM,IAAK;QACpBzC,SAAS,CAACwC,OAAO,GAAGC,MAAM;MAC5B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA;MAAAgB,QAAA,EAECrE,QAAQ,CACN4F,MAAM,CACJC,OAAO,IACNA,OAAO,IAAIA,OAAO,CAAC/D,GAAG,IAAI+D,OAAO,CAACC,SAAS,IAAID,OAAO,CAACE,IAC3D,CAAC,CACAnE,GAAG,CAAC,CAACiE,OAAO,EAAEG,KAAK,kBAClBrG,OAAA,CAACT,WAAW;QAAAmF,QAAA,eACV1E,OAAA;UACEyE,SAAS,EAAC,yBAAyB;UACnCM,KAAK,EAAE;YACLuB,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UACFC,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,YAAYuF,OAAO,CAAC/D,GAAG,EAAE,CAAE;UAAAuC,QAAA,gBAGnD1E,OAAA;YACE+E,KAAK,EAAE;cACLwB,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,KAAK;cACVC,KAAK,EAAE,KAAK;cACZC,MAAM,EAAE,EAAE;cACVC,eAAe,EAAE,MAAM;cACvBC,YAAY,EAAE,KAAK;cACnBC,KAAK,EAAE,MAAM;cACb9B,MAAM,EAAE,MAAM;cACd+B,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,SAAS,EAAE,gCAAgC;cAC3CZ,MAAM,EAAE;YACV,CAAE;YACFE,OAAO,EAAG/D,KAAK,IAAKD,cAAc,CAACC,KAAK,EAAEyD,OAAO,CAAC/D,GAAG,CAAE;YAAAuC,QAAA,EAEtDjE,SAAS,CAACyF,OAAO,CAAC/D,GAAG,CAAC,gBACrBnC,OAAA,CAACH,YAAY;cAACkF,KAAK,EAAE;gBAAEoC,KAAK,EAAE,KAAK;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE3D9E,OAAA,CAACJ,kBAAkB;cACjBmF,KAAK,EAAE;gBAAEoC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9E,OAAA;YAAKyE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjC1E,OAAA;cACEqH,GAAG,EAAE,uDAAuDnB,OAAO,CAACC,SAAS,mCAAoC;cACjHmB,GAAG,EAAEpB,OAAO,CAACE,IAAK;cAClBmB,OAAO,EAAElB,KAAK,GAAG7E,YAAY,GAAG,OAAO,GAAG,MAAO;cACjDsF,KAAK,EAAC,KAAK;cACX9B,MAAM,EAAC;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9E,OAAA;YAAKyE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B1E,OAAA;cAAGyE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEwB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB;YAAS;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD9E,OAAA;cAAIyE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEwB,OAAO,CAACE;YAAI;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC/CoB,OAAO,CAACuB,SAAS,gBAChBzH,OAAA,CAAAE,SAAA;cAAAwE,QAAA,gBACE1E,OAAA;gBACEyE,SAAS,EAAC,eAAe;gBACzBM,KAAK,EAAE;kBACL2C,cAAc,EAAE,cAAc;kBAC9BP,KAAK,EAAE;gBACT,CAAE;gBAAAzC,QAAA,GAEDwB,OAAO,CAACyB,KAAK,CAACC,cAAc,CAAC,OAAO,EAAE;kBACrCC,qBAAqB,EAAE,CAAC;kBACxBC,qBAAqB,EAAE;gBACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ9E,OAAA;gBACEyE,SAAS,EAAC,eAAe;gBACzBM,KAAK,EAAE;kBAAEoC,KAAK,EAAE,SAAS;kBAAEY,UAAU,EAAE;gBAAO,CAAE;gBAAArD,QAAA,GAE/CwB,OAAO,CAACuB,SAAS,CAACG,cAAc,CAAC,OAAO,EAAE;kBACzCC,qBAAqB,EAAE,CAAC;kBACxBC,qBAAqB,EAAE;gBACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eACJ,CAAC,gBAEH9E,OAAA;cAAGyE,SAAS,EAAC,eAAe;cAAAC,QAAA,GACzBwB,OAAO,CAACyB,KAAK,CAACC,cAAc,CAAC,OAAO,EAAE;gBACrCC,qBAAqB,EAAE,CAAC;gBACxBC,qBAAqB,EAAE;cACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;YAAA;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAtFUoB,OAAO,CAAC/D,GAAG,IAAIkE,KAAK;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuFzB,CACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC1E,EAAA,CA9SID,aAAa;EAAA,QAIAd,WAAW;AAAA;AAAA2I,EAAA,GAJxB7H,aAAa;AAgTnB,eAAeA,aAAa;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}