{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Popups\\\\CartOverlay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState, useEffect } from \"react\";\nimport { Box, Typography, Button, useMediaQuery, IconButton } from \"@mui/material\";\nimport { useCart } from \"../../Context/cartcontext\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { useNavigate } from \"react-router-dom\";\nimport CancelIcon from \"@mui/icons-material/Cancel\";\nimport { UserContext } from \"../../utils/userContext\";\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport DeleteIcon from \"@mui/icons-material/Delete\"; // Import DeleteIcon\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ShoppingCartOverlay = ({\n  open,\n  onClose\n}) => {\n  _s();\n  const {\n    cartItems,\n    removeFromCart,\n    lastAddedItem,\n    updateQuantity\n  } = useCart(); // Add updateQuantity\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n  const navigate = useNavigate();\n  const {\n    userSession\n  } = useContext(UserContext); // Access user session from context\n  const [highlightedItem, setHighlightedItem] = useState(null);\n\n  // Add handler for quantity changes\n  const handleQuantityChange = (itemId, newQuantity) => {\n    if (newQuantity >= 1) {\n      updateQuantity(itemId, newQuantity);\n    } else if (newQuantity === 0) {\n      // Remove item if quantity would be 0\n      removeFromCart(itemId);\n    }\n  };\n\n  // When cart opens or a new item is added, highlight that item\n  useEffect(() => {\n    if (open && lastAddedItem) {\n      setHighlightedItem(lastAddedItem.id);\n\n      // Remove highlight after 2 seconds\n      const timer = setTimeout(() => {\n        setHighlightedItem(null);\n      }, 2000);\n      return () => clearTimeout(timer);\n    }\n  }, [open, lastAddedItem]);\n  if (!open) return null; // Don't render if overlay is closed\n\n  const subtotal = cartItems.reduce((sum, item) => sum + item.unitPrice * item.quantity, 0);\n  const shippingFee = 0; // Example static shipping fee\n  const total = subtotal + shippingFee;\n  const handleCheckoutClick = () => {\n    navigate(\"/checkout\", {\n      state: {\n        cartItems,\n        subtotal,\n        shippingFee,\n        total\n      }\n    });\n    onClose(); // Close the overlay when navigating to checkout\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: \"fixed\",\n      top: 50,\n      right: isMobile ? 0 : 20,\n      width: \"400px\",\n      backgroundColor: \"white\",\n      boxShadow: \"-2px 0 5px rgba(0,0,0,0.2)\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      padding: \"16px\",\n      zIndex: 1000,\n      borderRadius: \"8px\",\n      maxHeight: \"80vh\",\n      animation: \"slideIn 0.3s ease-out\",\n      \"@keyframes slideIn\": {\n        \"0%\": {\n          transform: \"translateX(100%)\",\n          opacity: 0\n        },\n        \"100%\": {\n          transform: \"translateX(0)\",\n          opacity: 1\n        }\n      }\n    },\n    className: \"Cart-popup\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        paddingBottom: \"8px\",\n        borderBottom: \"1px solid #ddd\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Shopping Cart (\", cartItems.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CloseIcon, {\n        sx: {\n          cursor: \"pointer\",\n          fontSize: \"24px\",\n          color: \"#333\"\n        },\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), cartItems.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        textAlign: \"center\",\n        marginTop: \"20px\"\n      },\n      children: \"Your cart is empty.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1,\n          overflowY: \"auto\",\n          marginTop: \"10px\",\n          maxHeight: \"50vh\"\n        },\n        children: cartItems.map(item => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            padding: \"8px 0\",\n            borderBottom: \"1px solid #eee\",\n            backgroundColor: highlightedItem === item.id ? \"rgba(107, 123, 88, 0.1)\" : \"transparent\",\n            transition: \"background-color 0.3s ease\",\n            position: \"relative\",\n            borderRadius: \"4px\"\n          },\n          children: [highlightedItem === item.id && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: \"absolute\",\n              top: \"8px\",\n              right: \"8px\",\n              backgroundColor: \"#6B7B58\",\n              color: \"white\",\n              fontSize: \"10px\",\n              padding: \"2px 6px\",\n              borderRadius: \"10px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"4px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              sx: {\n                fontSize: 14\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Added\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${item.image}` || `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${item.mainImage}`,\n              alt: item.name,\n              width: \"120\",\n              height: \"100\",\n              style: {\n                borderRadius: \"5px\",\n                transition: \"transform 0.3s ease\",\n                transform: highlightedItem === item.id ? \"scale(1.05)\" : \"scale(1)\",\n                width: \"50%\",\n                height: \"110px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: \"5px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  fontFamily: \"Horizon\",\n                  fontSize: \"0.8rem !important\",\n                  fontWeight: highlightedItem === item.id ? \"bold\" : \"normal\"\n                },\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  marginTop: \"4px\",\n                  marginBottom: \"4px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => item.quantity === 1 ? removeFromCart(item.id) : handleQuantityChange(item.id, item.quantity - 1),\n                  sx: {\n                    padding: \"2px\",\n                    backgroundColor: item.quantity === 1 ? \"transparent\" : \"#6B7B58\",\n                    color: \"white\",\n                    \"&:hover\": {\n                      backgroundColor: item.quantity === 1 ? \"#2d2d2d\" : \"#5a6a47\",\n                      cursor: item.quantity === 1 ? \"pointer\" : \"pointer\"\n                    },\n                    width: \"20px\",\n                    height: \"20px\",\n                    minWidth: \"20px\"\n                  },\n                  children: item.quantity === 1 ? /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\",\n                    color: \"#ccc\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontFamily: \"Montserrat\",\n                    fontSize: \"14px\",\n                    minWidth: \"20px\",\n                    textAlign: \"center\"\n                  },\n                  children: item.quantity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleQuantityChange(item.id, item.quantity + 1),\n                  sx: {\n                    padding: \"2px\",\n                    backgroundColor: \"#6B7B58\",\n                    color: \"white\",\n                    \"&:hover\": {\n                      backgroundColor: \"#5a6a47\"\n                    },\n                    width: \"20px\",\n                    height: \"20px\",\n                    minWidth: \"20px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: \"#2d2d2d\",\n                  fontFamily: \"Montserrat\",\n                  fontWeight: \"bold\"\n                },\n                children: ` ${item.unitPrice.toLocaleString(\"en-US\", {\n                  minimumFractionDigits: 2,\n                  maximumFractionDigits: 2\n                })} E£`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this), item.color && item.color !== \"default\" && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: \"#6B7B58\",\n                  fontFamily: \"Montserrat\",\n                  fontSize: \"12px\"\n                },\n                children: [\"Color: \", item.color]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 23\n              }, this), item.size && item.size !== \"default\" && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: \"#6B7B58\",\n                  fontFamily: \"Montserrat\",\n                  fontSize: \"12px\"\n                },\n                children: [\"Size: \", item.size]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CancelIcon, {\n            onClick: () => removeFromCart(item.id),\n            sx: {\n              cursor: \"pointer\",\n              color: \"#999\",\n              \"&:hover\": {\n                color: \"#f44336\"\n              },\n              marginRight: highlightedItem === item.id ? \"30px\" : \"0\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 17\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          paddingTop: \"10px\",\n          paddingBottom: \"10px\",\n          borderTop: \"1px solid #ddd\",\n          textAlign: \"left\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontFamily: \"Montserrat\",\n            fontSize: \"18px\"\n          },\n          children: `Total: ${total.toLocaleString(\"en-US\", {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n          })} E£`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          gap: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          fullWidth: true,\n          sx: {\n            marginTop: \"10px\",\n            fontFamily: \"Horizon\",\n            backgroundColor: \"#6B7B58\",\n            \"&:hover\": {\n              backgroundColor: \"#5a6a47\"\n            }\n          },\n          onClick: userSession ? handleCheckoutClick : () => navigate(\"/login\"),\n          children: \"Checkout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(ShoppingCartOverlay, \"BkJ7Moz6funUcXQzdc6x9suF+LM=\", false, function () {\n  return [useCart, useMediaQuery, useNavigate];\n});\n_c = ShoppingCartOverlay;\nexport default ShoppingCartOverlay;\nvar _c;\n$RefreshReg$(_c, \"ShoppingCartOverlay\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "useMediaQuery", "IconButton", "useCart", "CloseIcon", "useNavigate", "CancelIcon", "UserContext", "CheckCircleIcon", "AddIcon", "RemoveIcon", "DeleteIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ShoppingCartOverlay", "open", "onClose", "_s", "cartItems", "removeFromCart", "lastAddedItem", "updateQuantity", "isMobile", "navigate", "userSession", "highlightedItem", "setHighlightedItem", "handleQuantityChange", "itemId", "newQuantity", "id", "timer", "setTimeout", "clearTimeout", "subtotal", "reduce", "sum", "item", "unitPrice", "quantity", "shippingFee", "total", "handleCheckoutClick", "state", "sx", "position", "top", "right", "width", "backgroundColor", "boxShadow", "display", "flexDirection", "padding", "zIndex", "borderRadius", "maxHeight", "animation", "transform", "opacity", "className", "children", "justifyContent", "alignItems", "paddingBottom", "borderBottom", "variant", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cursor", "fontSize", "color", "onClick", "textAlign", "marginTop", "flexGrow", "overflowY", "map", "transition", "gap", "src", "image", "mainImage", "alt", "name", "height", "style", "fontFamily", "fontWeight", "marginBottom", "size", "min<PERSON><PERSON><PERSON>", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "marginRight", "paddingTop", "borderTop", "fullWidth", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Popups/CartOverlay.jsx"], "sourcesContent": ["import React, { useContext, useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  useMediaQuery,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport { useCart } from \"../../Context/cartcontext\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport CancelIcon from \"@mui/icons-material/Cancel\";\r\nimport { UserContext } from \"../../utils/userContext\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\"; // Import DeleteIcon\r\n\r\nconst ShoppingCartOverlay = ({ open, onClose }) => {\r\n  const { cartItems, removeFromCart, lastAddedItem, updateQuantity } =\r\n    useCart(); // Add updateQuantity\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n  const navigate = useNavigate();\r\n  const { userSession } = useContext(UserContext); // Access user session from context\r\n  const [highlightedItem, setHighlightedItem] = useState(null);\r\n\r\n  // Add handler for quantity changes\r\n  const handleQuantityChange = (itemId, newQuantity) => {\r\n    if (newQuantity >= 1) {\r\n      updateQuantity(itemId, newQuantity);\r\n    } else if (newQuantity === 0) {\r\n      // Remove item if quantity would be 0\r\n      removeFromCart(itemId);\r\n    }\r\n  };\r\n\r\n  // When cart opens or a new item is added, highlight that item\r\n  useEffect(() => {\r\n    if (open && lastAddedItem) {\r\n      setHighlightedItem(lastAddedItem.id);\r\n\r\n      // Remove highlight after 2 seconds\r\n      const timer = setTimeout(() => {\r\n        setHighlightedItem(null);\r\n      }, 2000);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [open, lastAddedItem]);\r\n\r\n  if (!open) return null; // Don't render if overlay is closed\r\n\r\n  const subtotal = cartItems.reduce(\r\n    (sum, item) => sum + item.unitPrice * item.quantity,\r\n    0\r\n  );\r\n  const shippingFee = 0; // Example static shipping fee\r\n  const total = subtotal + shippingFee;\r\n\r\n  const handleCheckoutClick = () => {\r\n    navigate(\"/checkout\", {\r\n      state: {\r\n        cartItems,\r\n        subtotal,\r\n        shippingFee,\r\n        total,\r\n      },\r\n    });\r\n    onClose(); // Close the overlay when navigating to checkout\r\n  };\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        position: \"fixed\",\r\n        top: 50,\r\n        right: isMobile ? 0 : 20,\r\n        width: \"400px\",\r\n        backgroundColor: \"white\",\r\n        boxShadow: \"-2px 0 5px rgba(0,0,0,0.2)\",\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        padding: \"16px\",\r\n        zIndex: 1000,\r\n        borderRadius: \"8px\",\r\n        maxHeight: \"80vh\",\r\n        animation: \"slideIn 0.3s ease-out\",\r\n        \"@keyframes slideIn\": {\r\n          \"0%\": { transform: \"translateX(100%)\", opacity: 0 },\r\n          \"100%\": { transform: \"translateX(0)\", opacity: 1 },\r\n        },\r\n      }}\r\n      className=\"Cart-popup\"\r\n    >\r\n      {/* Header with Close Icon */}\r\n      <Box\r\n        sx={{\r\n          display: \"flex\",\r\n          justifyContent: \"space-between\",\r\n          alignItems: \"center\",\r\n          paddingBottom: \"8px\",\r\n          borderBottom: \"1px solid #ddd\",\r\n        }}\r\n      >\r\n        <Typography variant=\"h6\">Shopping Cart ({cartItems.length})</Typography>\r\n        <CloseIcon\r\n          sx={{ cursor: \"pointer\", fontSize: \"24px\", color: \"#333\" }}\r\n          onClick={onClose}\r\n        />\r\n      </Box>\r\n\r\n      {/* Cart Items List */}\r\n      {cartItems.length === 0 ? (\r\n        <Typography sx={{ textAlign: \"center\", marginTop: \"20px\" }}>\r\n          Your cart is empty.\r\n        </Typography>\r\n      ) : (\r\n        <>\r\n          <Box\r\n            sx={{\r\n              flexGrow: 1,\r\n              overflowY: \"auto\",\r\n              marginTop: \"10px\",\r\n              maxHeight: \"50vh\",\r\n            }}\r\n          >\r\n            {cartItems.map((item) => (\r\n              <Box\r\n                key={item.id}\r\n                sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"space-between\",\r\n                  alignItems: \"center\",\r\n                  padding: \"8px 0\",\r\n                  borderBottom: \"1px solid #eee\",\r\n                  backgroundColor:\r\n                    highlightedItem === item.id\r\n                      ? \"rgba(107, 123, 88, 0.1)\"\r\n                      : \"transparent\",\r\n                  transition: \"background-color 0.3s ease\",\r\n                  position: \"relative\",\r\n                  borderRadius: \"4px\",\r\n                }}\r\n              >\r\n                {/* New Item Indicator */}\r\n                {highlightedItem === item.id && (\r\n                  <Box\r\n                    sx={{\r\n                      position: \"absolute\",\r\n                      top: \"8px\",\r\n                      right: \"8px\",\r\n                      backgroundColor: \"#6B7B58\",\r\n                      color: \"white\",\r\n                      fontSize: \"10px\",\r\n                      padding: \"2px 6px\",\r\n                      borderRadius: \"10px\",\r\n                      display: \"flex\",\r\n                      alignItems: \"center\",\r\n                      gap: \"4px\",\r\n                    }}\r\n                  >\r\n                    <CheckCircleIcon sx={{ fontSize: 14 }} />\r\n                    <span>Added</span>\r\n                  </Box>\r\n                )}\r\n\r\n                {/* Left: Image and details */}\r\n                <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n                  <img\r\n                    src={\r\n                      `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${item.image}` ||\r\n                      `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${item.mainImage}`\r\n                    }\r\n                    alt={item.name}\r\n                    width=\"120\"\r\n                    height=\"100\"\r\n                    style={{\r\n                      borderRadius: \"5px\",\r\n                      transition: \"transform 0.3s ease\",\r\n                      transform:\r\n                        highlightedItem === item.id\r\n                          ? \"scale(1.05)\"\r\n                          : \"scale(1)\",\r\n                      width: \"50%\",\r\n                      height: \"110px\",\r\n                    }}\r\n                  />\r\n\r\n                  {/* Right: Name, Price, and Quantity controls */}\r\n                  <Box\r\n                    sx={{\r\n                      display: \"flex\",\r\n                      flexDirection: \"column\",\r\n                      gap: \"5px\",\r\n                    }}\r\n                  >\r\n                    <Typography\r\n                      variant=\"body1\"\r\n                      sx={{\r\n                        fontFamily: \"Horizon\",\r\n                        fontSize: \"0.8rem !important\",\r\n                        fontWeight:\r\n                          highlightedItem === item.id ? \"bold\" : \"normal\",\r\n                      }}\r\n                    >\r\n                      {item.name}\r\n                    </Typography>\r\n\r\n                    {/* Quantity controls */}\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: 1,\r\n                        marginTop: \"4px\",\r\n                        marginBottom: \"4px\",\r\n                      }}\r\n                    >\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={() =>\r\n                          item.quantity === 1\r\n                            ? removeFromCart(item.id)\r\n                            : handleQuantityChange(item.id, item.quantity - 1)\r\n                        }\r\n                        sx={{\r\n                          padding: \"2px\",\r\n                          backgroundColor:\r\n                            item.quantity === 1 ? \"transparent\" : \"#6B7B58\",\r\n                          color: \"white\",\r\n                          \"&:hover\": {\r\n                            backgroundColor:\r\n                              item.quantity === 1 ? \"#2d2d2d\" : \"#5a6a47\",\r\n                            cursor: item.quantity === 1 ? \"pointer\" : \"pointer\",\r\n                          },\r\n                          width: \"20px\",\r\n                          height: \"20px\",\r\n                          minWidth: \"20px\",\r\n                        }}\r\n                      >\r\n                        {item.quantity === 1 ? (\r\n                          <DeleteIcon fontSize=\"small\" color=\"#ccc\" />\r\n                        ) : (\r\n                          <RemoveIcon fontSize=\"small\" />\r\n                        )}\r\n                      </IconButton>\r\n\r\n                      <Typography\r\n                        sx={{\r\n                          fontFamily: \"Montserrat\",\r\n                          fontSize: \"14px\",\r\n                          minWidth: \"20px\",\r\n                          textAlign: \"center\",\r\n                        }}\r\n                      >\r\n                        {item.quantity}\r\n                      </Typography>\r\n\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={() =>\r\n                          handleQuantityChange(item.id, item.quantity + 1)\r\n                        }\r\n                        sx={{\r\n                          padding: \"2px\",\r\n                          backgroundColor: \"#6B7B58\",\r\n                          color: \"white\",\r\n                          \"&:hover\": { backgroundColor: \"#5a6a47\" },\r\n                          width: \"20px\",\r\n                          height: \"20px\",\r\n                          minWidth: \"20px\",\r\n                        }}\r\n                      >\r\n                        <AddIcon fontSize=\"small\" />\r\n                      </IconButton>\r\n                    </Box>\r\n\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        color: \"#2d2d2d\",\r\n                        fontFamily: \"Montserrat\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                    >\r\n                      {` ${item.unitPrice.toLocaleString(\"en-US\", {\r\n                        minimumFractionDigits: 2,\r\n                        maximumFractionDigits: 2,\r\n                      })} E£`}\r\n                    </Typography>\r\n\r\n                    {/* Existing color and size info... */}\r\n                    {item.color && item.color !== \"default\" && (\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          color: \"#6B7B58\",\r\n                          fontFamily: \"Montserrat\",\r\n                          fontSize: \"12px\",\r\n                        }}\r\n                      >\r\n                        Color: {item.color}\r\n                      </Typography>\r\n                    )}\r\n                    {item.size && item.size !== \"default\" && (\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          color: \"#6B7B58\",\r\n                          fontFamily: \"Montserrat\",\r\n                          fontSize: \"12px\",\r\n                        }}\r\n                      >\r\n                        Size: {item.size}\r\n                      </Typography>\r\n                    )}\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Remove Button */}\r\n                <CancelIcon\r\n                  onClick={() => removeFromCart(item.id)}\r\n                  sx={{\r\n                    cursor: \"pointer\",\r\n                    color: \"#999\",\r\n                    \"&:hover\": {\r\n                      color: \"#f44336\",\r\n                    },\r\n                    marginRight: highlightedItem === item.id ? \"30px\" : \"0\",\r\n                  }}\r\n                />\r\n              </Box>\r\n            ))}\r\n          </Box>\r\n\r\n          {/* Total Price Section */}\r\n          <Box\r\n            sx={{\r\n              paddingTop: \"10px\",\r\n              paddingBottom: \"10px\",\r\n              borderTop: \"1px solid #ddd\",\r\n              textAlign: \"left\",\r\n            }}\r\n          >\r\n            <Typography sx={{ fontFamily: \"Montserrat\", fontSize: \"18px\" }}>\r\n              {`Total: ${total.toLocaleString(\"en-US\", {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 2,\r\n              })} E£`}\r\n            </Typography>\r\n          </Box>\r\n\r\n          {/* Action Buttons */}\r\n          <Box sx={{ display: \"flex\", gap: \"10px\" }}>\r\n            {/* Checkout Button */}\r\n            <Button\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              fullWidth\r\n              sx={{\r\n                marginTop: \"10px\",\r\n                fontFamily: \"Horizon\",\r\n                backgroundColor: \"#6B7B58\",\r\n                \"&:hover\": {\r\n                  backgroundColor: \"#5a6a47\",\r\n                },\r\n              }}\r\n              onClick={\r\n                userSession ? handleCheckoutClick : () => navigate(\"/login\")\r\n              }\r\n            >\r\n              Checkout\r\n            </Button>\r\n          </Box>\r\n        </>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ShoppingCartOverlay;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,aAAa,EACbC,UAAU,QACL,eAAe;AACtB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,UAAU,MAAM,4BAA4B,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM;IAAEC,SAAS;IAAEC,cAAc;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAChEpB,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,MAAMqB,QAAQ,GAAGvB,aAAa,CAAC,mBAAmB,CAAC;EACnD,MAAMwB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAY,CAAC,GAAG/B,UAAU,CAACY,WAAW,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAMiC,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;IACpD,IAAIA,WAAW,IAAI,CAAC,EAAE;MACpBR,cAAc,CAACO,MAAM,EAAEC,WAAW,CAAC;IACrC,CAAC,MAAM,IAAIA,WAAW,KAAK,CAAC,EAAE;MAC5B;MACAV,cAAc,CAACS,MAAM,CAAC;IACxB;EACF,CAAC;;EAED;EACAjC,SAAS,CAAC,MAAM;IACd,IAAIoB,IAAI,IAAIK,aAAa,EAAE;MACzBM,kBAAkB,CAACN,aAAa,CAACU,EAAE,CAAC;;MAEpC;MACA,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BN,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMO,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAChB,IAAI,EAAEK,aAAa,CAAC,CAAC;EAEzB,IAAI,CAACL,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;EAExB,MAAMmB,QAAQ,GAAGhB,SAAS,CAACiB,MAAM,CAC/B,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,SAAS,GAAGD,IAAI,CAACE,QAAQ,EACnD,CACF,CAAC;EACD,MAAMC,WAAW,GAAG,CAAC,CAAC,CAAC;EACvB,MAAMC,KAAK,GAAGP,QAAQ,GAAGM,WAAW;EAEpC,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChCnB,QAAQ,CAAC,WAAW,EAAE;MACpBoB,KAAK,EAAE;QACLzB,SAAS;QACTgB,QAAQ;QACRM,WAAW;QACXC;MACF;IACF,CAAC,CAAC;IACFzB,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EAED,oBACEL,OAAA,CAACf,GAAG;IACFgD,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,EAAE;MACPC,KAAK,EAAEzB,QAAQ,GAAG,CAAC,GAAG,EAAE;MACxB0B,KAAK,EAAE,OAAO;MACdC,eAAe,EAAE,OAAO;MACxBC,SAAS,EAAE,4BAA4B;MACvCC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,uBAAuB;MAClC,oBAAoB,EAAE;QACpB,IAAI,EAAE;UAAEC,SAAS,EAAE,kBAAkB;UAAEC,OAAO,EAAE;QAAE,CAAC;QACnD,MAAM,EAAE;UAAED,SAAS,EAAE,eAAe;UAAEC,OAAO,EAAE;QAAE;MACnD;IACF,CAAE;IACFC,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAGtBlD,OAAA,CAACf,GAAG;MACFgD,EAAE,EAAE;QACFO,OAAO,EAAE,MAAM;QACfW,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,aAAa,EAAE,KAAK;QACpBC,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,gBAEFlD,OAAA,CAACd,UAAU;QAACqE,OAAO,EAAC,IAAI;QAAAL,QAAA,GAAC,iBAAe,EAAC3C,SAAS,CAACiD,MAAM,EAAC,GAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxE5D,OAAA,CAACT,SAAS;QACR0C,EAAE,EAAE;UAAE4B,MAAM,EAAE,SAAS;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAC3DC,OAAO,EAAE3D;MAAQ;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLrD,SAAS,CAACiD,MAAM,KAAK,CAAC,gBACrBxD,OAAA,CAACd,UAAU;MAAC+C,EAAE,EAAE;QAAEgC,SAAS,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAhB,QAAA,EAAC;IAE5D;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEb5D,OAAA,CAAAE,SAAA;MAAAgD,QAAA,gBACElD,OAAA,CAACf,GAAG;QACFgD,EAAE,EAAE;UACFkC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,MAAM;UACjBF,SAAS,EAAE,MAAM;UACjBrB,SAAS,EAAE;QACb,CAAE;QAAAK,QAAA,EAED3C,SAAS,CAAC8D,GAAG,CAAE3C,IAAI,iBAClB1B,OAAA,CAACf,GAAG;UAEFgD,EAAE,EAAE;YACFO,OAAO,EAAE,MAAM;YACfW,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,QAAQ;YACpBV,OAAO,EAAE,OAAO;YAChBY,YAAY,EAAE,gBAAgB;YAC9BhB,eAAe,EACbxB,eAAe,KAAKY,IAAI,CAACP,EAAE,GACvB,yBAAyB,GACzB,aAAa;YACnBmD,UAAU,EAAE,4BAA4B;YACxCpC,QAAQ,EAAE,UAAU;YACpBU,YAAY,EAAE;UAChB,CAAE;UAAAM,QAAA,GAGDpC,eAAe,KAAKY,IAAI,CAACP,EAAE,iBAC1BnB,OAAA,CAACf,GAAG;YACFgD,EAAE,EAAE;cACFC,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,KAAK;cACVC,KAAK,EAAE,KAAK;cACZE,eAAe,EAAE,SAAS;cAC1ByB,KAAK,EAAE,OAAO;cACdD,QAAQ,EAAE,MAAM;cAChBpB,OAAO,EAAE,SAAS;cAClBE,YAAY,EAAE,MAAM;cACpBJ,OAAO,EAAE,MAAM;cACfY,UAAU,EAAE,QAAQ;cACpBmB,GAAG,EAAE;YACP,CAAE;YAAArB,QAAA,gBAEFlD,OAAA,CAACL,eAAe;cAACsC,EAAE,EAAE;gBAAE6B,QAAQ,EAAE;cAAG;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzC5D,OAAA;cAAAkD,QAAA,EAAM;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACN,eAGD5D,OAAA,CAACf,GAAG;YAACgD,EAAE,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEY,UAAU,EAAE,QAAQ;cAAEmB,GAAG,EAAE;YAAE,CAAE;YAAArB,QAAA,gBACzDlD,OAAA;cACEwE,GAAG,EACD,uDAAuD9C,IAAI,CAAC+C,KAAK,EAAE,IACnE,uDAAuD/C,IAAI,CAACgD,SAAS,EACtE;cACDC,GAAG,EAAEjD,IAAI,CAACkD,IAAK;cACfvC,KAAK,EAAC,KAAK;cACXwC,MAAM,EAAC,KAAK;cACZC,KAAK,EAAE;gBACLlC,YAAY,EAAE,KAAK;gBACnB0B,UAAU,EAAE,qBAAqB;gBACjCvB,SAAS,EACPjC,eAAe,KAAKY,IAAI,CAACP,EAAE,GACvB,aAAa,GACb,UAAU;gBAChBkB,KAAK,EAAE,KAAK;gBACZwC,MAAM,EAAE;cACV;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGF5D,OAAA,CAACf,GAAG;cACFgD,EAAE,EAAE;gBACFO,OAAO,EAAE,MAAM;gBACfC,aAAa,EAAE,QAAQ;gBACvB8B,GAAG,EAAE;cACP,CAAE;cAAArB,QAAA,gBAEFlD,OAAA,CAACd,UAAU;gBACTqE,OAAO,EAAC,OAAO;gBACftB,EAAE,EAAE;kBACF8C,UAAU,EAAE,SAAS;kBACrBjB,QAAQ,EAAE,mBAAmB;kBAC7BkB,UAAU,EACRlE,eAAe,KAAKY,IAAI,CAACP,EAAE,GAAG,MAAM,GAAG;gBAC3C,CAAE;gBAAA+B,QAAA,EAEDxB,IAAI,CAACkD;cAAI;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eAGb5D,OAAA,CAACf,GAAG;gBACFgD,EAAE,EAAE;kBACFO,OAAO,EAAE,MAAM;kBACfY,UAAU,EAAE,QAAQ;kBACpBmB,GAAG,EAAE,CAAC;kBACNL,SAAS,EAAE,KAAK;kBAChBe,YAAY,EAAE;gBAChB,CAAE;gBAAA/B,QAAA,gBAEFlD,OAAA,CAACX,UAAU;kBACT6F,IAAI,EAAC,OAAO;kBACZlB,OAAO,EAAEA,CAAA,KACPtC,IAAI,CAACE,QAAQ,KAAK,CAAC,GACfpB,cAAc,CAACkB,IAAI,CAACP,EAAE,CAAC,GACvBH,oBAAoB,CAACU,IAAI,CAACP,EAAE,EAAEO,IAAI,CAACE,QAAQ,GAAG,CAAC,CACpD;kBACDK,EAAE,EAAE;oBACFS,OAAO,EAAE,KAAK;oBACdJ,eAAe,EACbZ,IAAI,CAACE,QAAQ,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;oBACjDmC,KAAK,EAAE,OAAO;oBACd,SAAS,EAAE;sBACTzB,eAAe,EACbZ,IAAI,CAACE,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;sBAC7CiC,MAAM,EAAEnC,IAAI,CAACE,QAAQ,KAAK,CAAC,GAAG,SAAS,GAAG;oBAC5C,CAAC;oBACDS,KAAK,EAAE,MAAM;oBACbwC,MAAM,EAAE,MAAM;oBACdM,QAAQ,EAAE;kBACZ,CAAE;kBAAAjC,QAAA,EAEDxB,IAAI,CAACE,QAAQ,KAAK,CAAC,gBAClB5B,OAAA,CAACF,UAAU;oBAACgE,QAAQ,EAAC,OAAO;oBAACC,KAAK,EAAC;kBAAM;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE5C5D,OAAA,CAACH,UAAU;oBAACiE,QAAQ,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC/B;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC,eAEb5D,OAAA,CAACd,UAAU;kBACT+C,EAAE,EAAE;oBACF8C,UAAU,EAAE,YAAY;oBACxBjB,QAAQ,EAAE,MAAM;oBAChBqB,QAAQ,EAAE,MAAM;oBAChBlB,SAAS,EAAE;kBACb,CAAE;kBAAAf,QAAA,EAEDxB,IAAI,CAACE;gBAAQ;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEb5D,OAAA,CAACX,UAAU;kBACT6F,IAAI,EAAC,OAAO;kBACZlB,OAAO,EAAEA,CAAA,KACPhD,oBAAoB,CAACU,IAAI,CAACP,EAAE,EAAEO,IAAI,CAACE,QAAQ,GAAG,CAAC,CAChD;kBACDK,EAAE,EAAE;oBACFS,OAAO,EAAE,KAAK;oBACdJ,eAAe,EAAE,SAAS;oBAC1ByB,KAAK,EAAE,OAAO;oBACd,SAAS,EAAE;sBAAEzB,eAAe,EAAE;oBAAU,CAAC;oBACzCD,KAAK,EAAE,MAAM;oBACbwC,MAAM,EAAE,MAAM;oBACdM,QAAQ,EAAE;kBACZ,CAAE;kBAAAjC,QAAA,eAEFlD,OAAA,CAACJ,OAAO;oBAACkE,QAAQ,EAAC;kBAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN5D,OAAA,CAACd,UAAU;gBACTqE,OAAO,EAAC,OAAO;gBACftB,EAAE,EAAE;kBACF8B,KAAK,EAAE,SAAS;kBAChBgB,UAAU,EAAE,YAAY;kBACxBC,UAAU,EAAE;gBACd,CAAE;gBAAA9B,QAAA,EAED,IAAIxB,IAAI,CAACC,SAAS,CAACyD,cAAc,CAAC,OAAO,EAAE;kBAC1CC,qBAAqB,EAAE,CAAC;kBACxBC,qBAAqB,EAAE;gBACzB,CAAC,CAAC;cAAK;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAGZlC,IAAI,CAACqC,KAAK,IAAIrC,IAAI,CAACqC,KAAK,KAAK,SAAS,iBACrC/D,OAAA,CAACd,UAAU;gBACTqE,OAAO,EAAC,OAAO;gBACftB,EAAE,EAAE;kBACF8B,KAAK,EAAE,SAAS;kBAChBgB,UAAU,EAAE,YAAY;kBACxBjB,QAAQ,EAAE;gBACZ,CAAE;gBAAAZ,QAAA,GACH,SACQ,EAACxB,IAAI,CAACqC,KAAK;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACb,EACAlC,IAAI,CAACwD,IAAI,IAAIxD,IAAI,CAACwD,IAAI,KAAK,SAAS,iBACnClF,OAAA,CAACd,UAAU;gBACTqE,OAAO,EAAC,OAAO;gBACftB,EAAE,EAAE;kBACF8B,KAAK,EAAE,SAAS;kBAChBgB,UAAU,EAAE,YAAY;kBACxBjB,QAAQ,EAAE;gBACZ,CAAE;gBAAAZ,QAAA,GACH,QACO,EAACxB,IAAI,CAACwD,IAAI;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA,CAACP,UAAU;YACTuE,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACkB,IAAI,CAACP,EAAE,CAAE;YACvCc,EAAE,EAAE;cACF4B,MAAM,EAAE,SAAS;cACjBE,KAAK,EAAE,MAAM;cACb,SAAS,EAAE;gBACTA,KAAK,EAAE;cACT,CAAC;cACDwB,WAAW,EAAEzE,eAAe,KAAKY,IAAI,CAACP,EAAE,GAAG,MAAM,GAAG;YACtD;UAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA1MGlC,IAAI,CAACP,EAAE;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2MT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5D,OAAA,CAACf,GAAG;QACFgD,EAAE,EAAE;UACFuD,UAAU,EAAE,MAAM;UAClBnC,aAAa,EAAE,MAAM;UACrBoC,SAAS,EAAE,gBAAgB;UAC3BxB,SAAS,EAAE;QACb,CAAE;QAAAf,QAAA,eAEFlD,OAAA,CAACd,UAAU;UAAC+C,EAAE,EAAE;YAAE8C,UAAU,EAAE,YAAY;YAAEjB,QAAQ,EAAE;UAAO,CAAE;UAAAZ,QAAA,EAC5D,UAAUpB,KAAK,CAACsD,cAAc,CAAC,OAAO,EAAE;YACvCC,qBAAqB,EAAE,CAAC;YACxBC,qBAAqB,EAAE;UACzB,CAAC,CAAC;QAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN5D,OAAA,CAACf,GAAG;QAACgD,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE+B,GAAG,EAAE;QAAO,CAAE;QAAArB,QAAA,eAExClD,OAAA,CAACb,MAAM;UACLoE,OAAO,EAAC,WAAW;UACnBQ,KAAK,EAAC,SAAS;UACf2B,SAAS;UACTzD,EAAE,EAAE;YACFiC,SAAS,EAAE,MAAM;YACjBa,UAAU,EAAE,SAAS;YACrBzC,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE;cACTA,eAAe,EAAE;YACnB;UACF,CAAE;UACF0B,OAAO,EACLnD,WAAW,GAAGkB,mBAAmB,GAAG,MAAMnB,QAAQ,CAAC,QAAQ,CAC5D;UAAAsC,QAAA,EACF;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtD,EAAA,CAxWIH,mBAAmB;EAAA,QAErBb,OAAO,EACQF,aAAa,EACbI,WAAW;AAAA;AAAAmG,EAAA,GAJxBxF,mBAAmB;AA0WzB,eAAeA,mBAAmB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}