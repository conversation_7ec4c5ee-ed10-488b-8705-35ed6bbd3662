{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\sideBarVendor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useVendor } from \"../../utils/vendorContext\";\nimport axios from \"axios\";\nimport { RiDashboard3Fill } from \"react-icons/ri\";\nimport { LuPackageOpen } from \"react-icons/lu\";\nimport { TbTruckDelivery } from \"react-icons/tb\";\nimport { FaMoneyBill } from \"react-icons/fa6\";\nimport { HiBuildingStorefront } from \"react-icons/hi2\";\nimport { MdAccountBalance } from \"react-icons/md\";\nimport { ImProfile } from \"react-icons/im\";\nimport { FaWpforms, FaBell, FaUsers, FaLock } from \"react-icons/fa\";\nimport LoadingScreen from \"../../Pages/loadingScreen\";\n\n// import { RiFileExcel2Fill } from \"react-icons/ri\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SidebarVendor = ({\n  setActivePage,\n  activePage\n}) => {\n  _s();\n  const {\n    vendor\n  } = useVendor();\n  const [isVendorLoaded, setIsVendorLoaded] = useState(false);\n  const [brandStatus, setBrandStatus] = useState(null);\n  useEffect(() => {\n    const checkVendorAndBrand = async () => {\n      if (vendor) {\n        setIsVendorLoaded(true);\n\n        // Check brand status if vendor has a brandId\n        if (vendor.brandId) {\n          try {\n            const response = await axios.get(`https://api.thedesigngrit.com/api/brand/${vendor.brandId}`);\n            setBrandStatus(response.data.status);\n          } catch (error) {\n            console.error(\"Error checking brand status:\", error);\n          }\n        }\n      } else {\n        // Fallback: Check if vendor data is available in localStorage\n        const storedVendor = JSON.parse(localStorage.getItem(\"vendor\"));\n        if (storedVendor) {\n          setIsVendorLoaded(true);\n\n          // Check brand status if stored vendor has a brandId\n          if (storedVendor.brandId) {\n            try {\n              const response = await axios.get(`https://api.thedesigngrit.com/api/brand/${storedVendor.brandId}`);\n              setBrandStatus(response.data.status);\n            } catch (error) {\n              console.error(\"Error checking brand status:\", error);\n            }\n          }\n        }\n      }\n    };\n    checkVendorAndBrand();\n  }, [vendor]);\n\n  // Function to handle active page highlighting\n  const getActiveClass = (page, locked) => {\n    let base = activePage === page ? \"sidebar-item-vendor active\" : \"sidebar-item-vendor\";\n    return locked ? base + \" locked\" : base;\n  };\n\n  // Helper to determine if an item should be locked\n  const isLocked = page => {\n    if (brandStatus === \"pending\") {\n      // Only BrandForm and BrandingPage are open\n      return !(page === \"BrandForm\" || page === \"BrandingPage\");\n    }\n    return false;\n  };\n\n  // Return a loading spinner or message until vendor data is loaded\n  if (!isVendorLoaded) {\n    return /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If brand is deactivated, don't render the sidebar\n  if (brandStatus === \"deactivated\") {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: \"sidebar-vendor\",\n    children: /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"sidebar-menu-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"dashboard\") && setActivePage(\"dashboard\"),\n        className: getActiveClass(\"dashboard\", isLocked(\"dashboard\")),\n        style: isLocked(\"dashboard\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(RiDashboard3Fill, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"dashboard\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"notifications\") && setActivePage(\"notifications\"),\n        className: getActiveClass(\"notifications\", isLocked(\"notifications\")),\n        style: isLocked(\"notifications\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaBell, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"notifications\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"allProducts\") && setActivePage(\"allProducts\"),\n        className: getActiveClass(\"allProducts\", isLocked(\"allProducts\")),\n        style: isLocked(\"allProducts\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(LuPackageOpen, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"All Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"allProducts\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"orderList\") && setActivePage(\"orderList\"),\n        className: getActiveClass(\"orderList\", isLocked(\"orderList\")),\n        style: isLocked(\"orderList\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbTruckDelivery, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Order List\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"orderList\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"quotationsList\") && setActivePage(\"quotationsList\"),\n        className: getActiveClass(\"quotationsList\", isLocked(\"quotationsList\")),\n        style: isLocked(\"quotationsList\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaMoneyBill, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Quotations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"quotationsList\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"ViewInStoreVendor\") && setActivePage(\"ViewInStoreVendor\"),\n        className: getActiveClass(\"ViewInStoreVendor\", isLocked(\"ViewInStoreVendor\")),\n        style: isLocked(\"ViewInStoreVendor\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(HiBuildingStorefront, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"View In Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"ViewInStoreVendor\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"promotionsPage\") && setActivePage(\"promotionsPage\"),\n        className: getActiveClass(\"promotionsPage\", isLocked(\"promotionsPage\")),\n        style: isLocked(\"promotionsPage\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaMoneyBill, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Promotions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"promotionsPage\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), (vendor === null || vendor === void 0 ? void 0 : vendor.tier) >= 3 && /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"BrandForm\"),\n        className: getActiveClass(\"BrandForm\", false),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaWpforms, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Brand Form\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this), (vendor === null || vendor === void 0 ? void 0 : vendor.tier) >= 3 && /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"BrandingPage\"),\n        className: getActiveClass(\"BrandingPage\", false),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(ImProfile, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Brand Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this), (vendor === null || vendor === void 0 ? void 0 : vendor.tier) >= 3 && /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"Accounting\") && setActivePage(\"Accounting\"),\n        className: getActiveClass(\"Accounting\", isLocked(\"Accounting\")),\n        style: isLocked(\"Accounting\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(MdAccountBalance, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Accounting\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"Accounting\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this), (vendor === null || vendor === void 0 ? void 0 : vendor.tier) >= 3 && /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => !isLocked(\"EmployeePage\") && setActivePage(\"EmployeePage\"),\n        className: getActiveClass(\"EmployeePage\", isLocked(\"EmployeePage\")),\n        style: isLocked(\"EmployeePage\") ? {\n          pointerEvents: \"none\",\n          opacity: 0.5\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-item-content\",\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n              className: \"sidebar-item-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sidebar-item-text\",\n              children: \"Employees\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: 24,\n              display: \"flex\",\n              justifyContent: \"center\"\n            },\n            children: isLocked(\"EmployeePage\") && /*#__PURE__*/_jsxDEV(FaLock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(SidebarVendor, \"Th6IOltentZcajJo5QY6Kis4keE=\", false, function () {\n  return [useVendor];\n});\n_c = SidebarVendor;\nexport default SidebarVendor;\nvar _c;\n$RefreshReg$(_c, \"SidebarVendor\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useVendor", "axios", "RiDashboard3Fill", "LuPackageOpen", "TbTruckDelivery", "FaMoneyBill", "HiBuildingStorefront", "MdAccountBalance", "ImProfile", "FaWpforms", "FaBell", "FaUsers", "FaLock", "LoadingScreen", "jsxDEV", "_jsxDEV", "SidebarVendor", "setActivePage", "activePage", "_s", "vendor", "isVendorLoaded", "setIsVendorLoaded", "brandStatus", "setBrandStatus", "checkVendorAndBrand", "brandId", "response", "get", "data", "status", "error", "console", "storedVendor", "JSON", "parse", "localStorage", "getItem", "getActiveClass", "page", "locked", "base", "isLocked", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "style", "pointerEvents", "opacity", "display", "alignItems", "justifyContent", "width", "tier", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/sideBarVendor.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\nimport axios from \"axios\";\r\nimport { RiDashboard3Fill } from \"react-icons/ri\";\r\nimport { LuPackageOpen } from \"react-icons/lu\";\r\nimport { TbTruckDelivery } from \"react-icons/tb\";\r\nimport { FaMoneyBill } from \"react-icons/fa6\";\r\nimport { HiBuildingStorefront } from \"react-icons/hi2\";\r\nimport { MdAccountBalance } from \"react-icons/md\";\r\nimport { ImProfile } from \"react-icons/im\";\r\nimport { FaWpforms, FaBell, FaUsers, FaLock } from \"react-icons/fa\";\r\nimport LoadingScreen from \"../../Pages/loadingScreen\";\r\n\r\n// import { RiFileExcel2Fill } from \"react-icons/ri\";\r\n\r\nconst SidebarVendor = ({ setActivePage, activePage }) => {\r\n  const { vendor } = useVendor();\r\n  const [isVendorLoaded, setIsVendorLoaded] = useState(false);\r\n  const [brandStatus, setBrandStatus] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const checkVendorAndBrand = async () => {\r\n      if (vendor) {\r\n        setIsVendorLoaded(true);\r\n\r\n        // Check brand status if vendor has a brandId\r\n        if (vendor.brandId) {\r\n          try {\r\n            const response = await axios.get(\r\n              `https://api.thedesigngrit.com/api/brand/${vendor.brandId}`\r\n            );\r\n            setBrandStatus(response.data.status);\r\n          } catch (error) {\r\n            console.error(\"Error checking brand status:\", error);\r\n          }\r\n        }\r\n      } else {\r\n        // Fallback: Check if vendor data is available in localStorage\r\n        const storedVendor = JSON.parse(localStorage.getItem(\"vendor\"));\r\n        if (storedVendor) {\r\n          setIsVendorLoaded(true);\r\n\r\n          // Check brand status if stored vendor has a brandId\r\n          if (storedVendor.brandId) {\r\n            try {\r\n              const response = await axios.get(\r\n                `https://api.thedesigngrit.com/api/brand/${storedVendor.brandId}`\r\n              );\r\n              setBrandStatus(response.data.status);\r\n            } catch (error) {\r\n              console.error(\"Error checking brand status:\", error);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    checkVendorAndBrand();\r\n  }, [vendor]);\r\n\r\n  // Function to handle active page highlighting\r\n  const getActiveClass = (page, locked) => {\r\n    let base =\r\n      activePage === page\r\n        ? \"sidebar-item-vendor active\"\r\n        : \"sidebar-item-vendor\";\r\n    return locked ? base + \" locked\" : base;\r\n  };\r\n\r\n  // Helper to determine if an item should be locked\r\n  const isLocked = (page) => {\r\n    if (brandStatus === \"pending\") {\r\n      // Only BrandForm and BrandingPage are open\r\n      return !(page === \"BrandForm\" || page === \"BrandingPage\");\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Return a loading spinner or message until vendor data is loaded\r\n  if (!isVendorLoaded) {\r\n    return <LoadingScreen />;\r\n  }\r\n\r\n  // If brand is deactivated, don't render the sidebar\r\n  if (brandStatus === \"deactivated\") {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <aside className=\"sidebar-vendor\">\r\n      <ul className=\"sidebar-menu-vendor\">\r\n        <li\r\n          onClick={() => !isLocked(\"dashboard\") && setActivePage(\"dashboard\")}\r\n          className={getActiveClass(\"dashboard\", isLocked(\"dashboard\"))}\r\n          style={\r\n            isLocked(\"dashboard\") ? { pointerEvents: \"none\", opacity: 0.5 } : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <RiDashboard3Fill className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">Dashboard</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"dashboard\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        <li\r\n          onClick={() =>\r\n            !isLocked(\"notifications\") && setActivePage(\"notifications\")\r\n          }\r\n          className={getActiveClass(\"notifications\", isLocked(\"notifications\"))}\r\n          style={\r\n            isLocked(\"notifications\")\r\n              ? { pointerEvents: \"none\", opacity: 0.5 }\r\n              : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <FaBell className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">Notifications</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"notifications\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        <li\r\n          onClick={() =>\r\n            !isLocked(\"allProducts\") && setActivePage(\"allProducts\")\r\n          }\r\n          className={getActiveClass(\"allProducts\", isLocked(\"allProducts\"))}\r\n          style={\r\n            isLocked(\"allProducts\")\r\n              ? { pointerEvents: \"none\", opacity: 0.5 }\r\n              : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <LuPackageOpen className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">All Products</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"allProducts\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        <li\r\n          onClick={() => !isLocked(\"orderList\") && setActivePage(\"orderList\")}\r\n          className={getActiveClass(\"orderList\", isLocked(\"orderList\"))}\r\n          style={\r\n            isLocked(\"orderList\") ? { pointerEvents: \"none\", opacity: 0.5 } : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <TbTruckDelivery className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">Order List</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"orderList\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        <li\r\n          onClick={() =>\r\n            !isLocked(\"quotationsList\") && setActivePage(\"quotationsList\")\r\n          }\r\n          className={getActiveClass(\r\n            \"quotationsList\",\r\n            isLocked(\"quotationsList\")\r\n          )}\r\n          style={\r\n            isLocked(\"quotationsList\")\r\n              ? { pointerEvents: \"none\", opacity: 0.5 }\r\n              : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <FaMoneyBill className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">Quotations</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"quotationsList\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        <li\r\n          onClick={() =>\r\n            !isLocked(\"ViewInStoreVendor\") && setActivePage(\"ViewInStoreVendor\")\r\n          }\r\n          className={getActiveClass(\r\n            \"ViewInStoreVendor\",\r\n            isLocked(\"ViewInStoreVendor\")\r\n          )}\r\n          style={\r\n            isLocked(\"ViewInStoreVendor\")\r\n              ? { pointerEvents: \"none\", opacity: 0.5 }\r\n              : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <HiBuildingStorefront className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">View In Store</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"ViewInStoreVendor\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        <li\r\n          onClick={() =>\r\n            !isLocked(\"promotionsPage\") && setActivePage(\"promotionsPage\")\r\n          }\r\n          className={getActiveClass(\r\n            \"promotionsPage\",\r\n            isLocked(\"promotionsPage\")\r\n          )}\r\n          style={\r\n            isLocked(\"promotionsPage\")\r\n              ? { pointerEvents: \"none\", opacity: 0.5 }\r\n              : {}\r\n          }\r\n        >\r\n          <span\r\n            className=\"sidebar-item-content\"\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"space-between\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n              <FaMoneyBill className=\"sidebar-item-icon\" />\r\n              <span className=\"sidebar-item-text\">Promotions</span>\r\n            </span>\r\n            <span\r\n              style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n            >\r\n              {isLocked(\"promotionsPage\") && <FaLock />}\r\n            </span>\r\n          </span>\r\n        </li>\r\n        {/* Render \"Brand Form\" only if vendor tier is 3 or higher */}\r\n        {vendor?.tier >= 3 && (\r\n          <li\r\n            onClick={() => setActivePage(\"BrandForm\")}\r\n            className={getActiveClass(\"BrandForm\", false)}\r\n          >\r\n            <span\r\n              className=\"sidebar-item-content\"\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n              }}\r\n            >\r\n              <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                <FaWpforms className=\"sidebar-item-icon\" />\r\n                <span className=\"sidebar-item-text\">Brand Form</span>\r\n              </span>\r\n              <span style={{ width: 24 }}></span>\r\n            </span>\r\n          </li>\r\n        )}\r\n        {/* Render \"Add Employee\" only if vendor tier is 3 or higher */}\r\n        {vendor?.tier >= 3 && (\r\n          <li\r\n            onClick={() => setActivePage(\"BrandingPage\")}\r\n            className={getActiveClass(\"BrandingPage\", false)}\r\n          >\r\n            <span\r\n              className=\"sidebar-item-content\"\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n              }}\r\n            >\r\n              <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                <ImProfile className=\"sidebar-item-icon\" />\r\n                <span className=\"sidebar-item-text\">Brand Profile</span>\r\n              </span>\r\n              <span style={{ width: 24 }}></span>\r\n            </span>\r\n          </li>\r\n        )}\r\n        {/* Render \"Add Employee\" only if vendor tier is 3 or higher */}\r\n        {vendor?.tier >= 3 && (\r\n          <li\r\n            onClick={() =>\r\n              !isLocked(\"Accounting\") && setActivePage(\"Accounting\")\r\n            }\r\n            className={getActiveClass(\"Accounting\", isLocked(\"Accounting\"))}\r\n            style={\r\n              isLocked(\"Accounting\")\r\n                ? { pointerEvents: \"none\", opacity: 0.5 }\r\n                : {}\r\n            }\r\n          >\r\n            <span\r\n              className=\"sidebar-item-content\"\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n              }}\r\n            >\r\n              <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                <MdAccountBalance className=\"sidebar-item-icon\" />\r\n                <span className=\"sidebar-item-text\">Accounting</span>\r\n              </span>\r\n              <span\r\n                style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n              >\r\n                {isLocked(\"Accounting\") && <FaLock />}\r\n              </span>\r\n            </span>\r\n          </li>\r\n        )}\r\n        {/* Render \"Employee Page\" only if vendor tier is 3 or higher */}\r\n        {vendor?.tier >= 3 && (\r\n          <li\r\n            onClick={() =>\r\n              !isLocked(\"EmployeePage\") && setActivePage(\"EmployeePage\")\r\n            }\r\n            className={getActiveClass(\"EmployeePage\", isLocked(\"EmployeePage\"))}\r\n            style={\r\n              isLocked(\"EmployeePage\")\r\n                ? { pointerEvents: \"none\", opacity: 0.5 }\r\n                : {}\r\n            }\r\n          >\r\n            <span\r\n              className=\"sidebar-item-content\"\r\n              style={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n              }}\r\n            >\r\n              <span style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                <FaUsers className=\"sidebar-item-icon\" />\r\n                <span className=\"sidebar-item-text\">Employees</span>\r\n              </span>\r\n              <span\r\n                style={{ width: 24, display: \"flex\", justifyContent: \"center\" }}\r\n              >\r\n                {isLocked(\"EmployeePage\") && <FaLock />}\r\n              </span>\r\n            </span>\r\n          </li>\r\n        )}\r\n      </ul>\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default SidebarVendor;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACnE,OAAOC,aAAa,MAAM,2BAA2B;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,aAAa,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM;IAAEC;EAAO,CAAC,GAAGpB,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACd,MAAM2B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAIL,MAAM,EAAE;QACVE,iBAAiB,CAAC,IAAI,CAAC;;QAEvB;QACA,IAAIF,MAAM,CAACM,OAAO,EAAE;UAClB,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAC9B,2CAA2CR,MAAM,CAACM,OAAO,EAC3D,CAAC;YACDF,cAAc,CAACG,QAAQ,CAACE,IAAI,CAACC,MAAM,CAAC;UACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACtD;QACF;MACF,CAAC,MAAM;QACL;QACA,MAAME,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAIJ,YAAY,EAAE;UAChBX,iBAAiB,CAAC,IAAI,CAAC;;UAEvB;UACA,IAAIW,YAAY,CAACP,OAAO,EAAE;YACxB,IAAI;cACF,MAAMC,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAC9B,2CAA2CK,YAAY,CAACP,OAAO,EACjE,CAAC;cACDF,cAAc,CAACG,QAAQ,CAACE,IAAI,CAACC,MAAM,CAAC;YACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;YACtD;UACF;QACF;MACF;IACF,CAAC;IAEDN,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMkB,cAAc,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;IACvC,IAAIC,IAAI,GACNvB,UAAU,KAAKqB,IAAI,GACf,4BAA4B,GAC5B,qBAAqB;IAC3B,OAAOC,MAAM,GAAGC,IAAI,GAAG,SAAS,GAAGA,IAAI;EACzC,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAIH,IAAI,IAAK;IACzB,IAAIhB,WAAW,KAAK,SAAS,EAAE;MAC7B;MACA,OAAO,EAAEgB,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,cAAc,CAAC;IAC3D;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,IAAI,CAAClB,cAAc,EAAE;IACnB,oBAAON,OAAA,CAACF,aAAa;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;;EAEA;EACA,IAAIvB,WAAW,KAAK,aAAa,EAAE;IACjC,OAAO,IAAI;EACb;EAEA,oBACER,OAAA;IAAOgC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC/BjC,OAAA;MAAIgC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBACjCjC,OAAA;QACEkC,OAAO,EAAEA,CAAA,KAAM,CAACP,QAAQ,CAAC,WAAW,CAAC,IAAIzB,aAAa,CAAC,WAAW,CAAE;QACpE8B,SAAS,EAAET,cAAc,CAAC,WAAW,EAAEI,QAAQ,CAAC,WAAW,CAAC,CAAE;QAC9DQ,KAAK,EACHR,QAAQ,CAAC,WAAW,CAAC,GAAG;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GAAG,CAAC,CACpE;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACb,gBAAgB;cAAC6C,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClD/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,WAAW,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACL/B,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,eAAe,CAAC,IAAIzB,aAAa,CAAC,eAAe,CAC5D;QACD8B,SAAS,EAAET,cAAc,CAAC,eAAe,EAAEI,QAAQ,CAAC,eAAe,CAAC,CAAE;QACtEQ,KAAK,EACHR,QAAQ,CAAC,eAAe,CAAC,GACrB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACL,MAAM;cAACqC,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxC/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,eAAe,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACL/B,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,aAAa,CAAC,IAAIzB,aAAa,CAAC,aAAa,CACxD;QACD8B,SAAS,EAAET,cAAc,CAAC,aAAa,EAAEI,QAAQ,CAAC,aAAa,CAAC,CAAE;QAClEQ,KAAK,EACHR,QAAQ,CAAC,aAAa,CAAC,GACnB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACZ,aAAa;cAAC4C,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/C/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,aAAa,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACL/B,OAAA;QACEkC,OAAO,EAAEA,CAAA,KAAM,CAACP,QAAQ,CAAC,WAAW,CAAC,IAAIzB,aAAa,CAAC,WAAW,CAAE;QACpE8B,SAAS,EAAET,cAAc,CAAC,WAAW,EAAEI,QAAQ,CAAC,WAAW,CAAC,CAAE;QAC9DQ,KAAK,EACHR,QAAQ,CAAC,WAAW,CAAC,GAAG;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GAAG,CAAC,CACpE;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACX,eAAe;cAAC2C,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,WAAW,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACL/B,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,gBAAgB,CAAC,IAAIzB,aAAa,CAAC,gBAAgB,CAC9D;QACD8B,SAAS,EAAET,cAAc,CACvB,gBAAgB,EAChBI,QAAQ,CAAC,gBAAgB,CAC3B,CAAE;QACFQ,KAAK,EACHR,QAAQ,CAAC,gBAAgB,CAAC,GACtB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACV,WAAW;cAAC0C,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,gBAAgB,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACL/B,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,mBAAmB,CAAC,IAAIzB,aAAa,CAAC,mBAAmB,CACpE;QACD8B,SAAS,EAAET,cAAc,CACvB,mBAAmB,EACnBI,QAAQ,CAAC,mBAAmB,CAC9B,CAAE;QACFQ,KAAK,EACHR,QAAQ,CAAC,mBAAmB,CAAC,GACzB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACT,oBAAoB;cAACyC,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtD/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,mBAAmB,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACL/B,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,gBAAgB,CAAC,IAAIzB,aAAa,CAAC,gBAAgB,CAC9D;QACD8B,SAAS,EAAET,cAAc,CACvB,gBAAgB,EAChBI,QAAQ,CAAC,gBAAgB,CAC3B,CAAE;QACFQ,KAAK,EACHR,QAAQ,CAAC,gBAAgB,CAAC,GACtB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACV,WAAW;cAAC0C,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,gBAAgB,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAEJ,CAAA1B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqC,IAAI,KAAI,CAAC,iBAChB1C,OAAA;QACEkC,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAAC,WAAW,CAAE;QAC1C8B,SAAS,EAAET,cAAc,CAAC,WAAW,EAAE,KAAK,CAAE;QAAAU,QAAA,eAE9CjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACN,SAAS;cAACsC,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACP/B,OAAA;YAAMmC,KAAK,EAAE;cAAEM,KAAK,EAAE;YAAG;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACL,EAEA,CAAA1B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqC,IAAI,KAAI,CAAC,iBAChB1C,OAAA;QACEkC,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAAC,cAAc,CAAE;QAC7C8B,SAAS,EAAET,cAAc,CAAC,cAAc,EAAE,KAAK,CAAE;QAAAU,QAAA,eAEjDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACP,SAAS;cAACuC,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACP/B,OAAA;YAAMmC,KAAK,EAAE;cAAEM,KAAK,EAAE;YAAG;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACL,EAEA,CAAA1B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqC,IAAI,KAAI,CAAC,iBAChB1C,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,YAAY,CAAC,IAAIzB,aAAa,CAAC,YAAY,CACtD;QACD8B,SAAS,EAAET,cAAc,CAAC,YAAY,EAAEI,QAAQ,CAAC,YAAY,CAAC,CAAE;QAChEQ,KAAK,EACHR,QAAQ,CAAC,YAAY,CAAC,GAClB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACR,gBAAgB;cAACwC,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClD/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,YAAY,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACL,EAEA,CAAA1B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqC,IAAI,KAAI,CAAC,iBAChB1C,OAAA;QACEkC,OAAO,EAAEA,CAAA,KACP,CAACP,QAAQ,CAAC,cAAc,CAAC,IAAIzB,aAAa,CAAC,cAAc,CAC1D;QACD8B,SAAS,EAAET,cAAc,CAAC,cAAc,EAAEI,QAAQ,CAAC,cAAc,CAAC,CAAE;QACpEQ,KAAK,EACHR,QAAQ,CAAC,cAAc,CAAC,GACpB;UAAES,aAAa,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAI,CAAC,GACvC,CAAC,CACN;QAAAJ,QAAA,eAEDjC,OAAA;UACEgC,SAAS,EAAC,sBAAsB;UAChCG,KAAK,EAAE;YACLG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEFjC,OAAA;YAAMmC,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAN,QAAA,gBACrDjC,OAAA,CAACJ,OAAO;cAACoC,SAAS,EAAC;YAAmB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzC/B,OAAA;cAAMgC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACP/B,OAAA;YACEmC,KAAK,EAAE;cAAEM,KAAK,EAAE,EAAE;cAAEH,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE;YAAS,CAAE;YAAAP,QAAA,EAE/DN,QAAQ,CAAC,cAAc,CAAC,iBAAI3B,OAAA,CAACH,MAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEZ,CAAC;AAAC3B,EAAA,CAhZIH,aAAa;EAAA,QACEhB,SAAS;AAAA;AAAA0D,EAAA,GADxB1C,aAAa;AAkZnB,eAAeA,aAAa;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}