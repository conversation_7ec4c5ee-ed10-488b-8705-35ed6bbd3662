{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\PromotionPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { CiCirclePlus } from \"react-icons/ci\";\nimport { AiOutlineDown, AiOutlineUp } from \"react-icons/ai\";\nimport axios from \"axios\";\nimport { useVendor } from \"../../utils/vendorContext\"; // Vendor context for brandId\nimport EditPromotionModal from \"./EditPromotionModal\"; // adjust path as needed\nimport CreatePromotionDialog from \"./CreatePromotionDialog\"; // adjust path\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PromotionsPage = () => {\n  _s();\n  const {\n    vendor\n  } = useVendor(); // Access vendor context for brandId\n  const [products, setProducts] = useState([]);\n  const [showFalseStatus, setShowFalseStatus] = useState(false); // Show products without promotion\n  const [showTrueStatus, setShowTrueStatus] = useState(true); // Show products with promotion\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [editModalOpen, setEditModalOpen] = useState(false);\n  const [currentPromotions, setCurrentPromotions] = useState([]); // Current promotions\n  const [pastPromotions, setPastPromotions] = useState([]); // Past promotions\n  const [pastPromotionMetrics, setPastPromotionMetrics] = useState([]); // Metrics for past promotions\n  const [upcomingPromotions, setUpcomingPromotions] = useState([]);\n  const [showUpcomingStatus, setShowUpcomingStatus] = useState(false); // Collapse control\n\n  useEffect(() => {\n    if (vendor) {\n      const {\n        brandId\n      } = vendor; // Get the brandId from vendor context\n      const fetchProducts = async () => {\n        try {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId}`);\n          setProducts(response.data); // Set fetched products\n          // Separate current and past promotions\n          const now = new Date();\n          const current = response.data.filter(product => {\n            return product.promotionStartDate && product.promotionEndDate && new Date(product.promotionStartDate) <= now && new Date(product.promotionEndDate) >= now;\n          });\n          const past = response.data.filter(product => {\n            return product.promotionStartDate && product.promotionEndDate && new Date(product.promotionEndDate) < now;\n          });\n          const upcoming = response.data.filter(product => {\n            return product.promotionStartDate && product.promotionEndDate && new Date(product.promotionStartDate) > now;\n          });\n          setUpcomingPromotions(upcoming);\n          setCurrentPromotions(current);\n          setPastPromotions(past);\n        } catch (error) {\n          console.error(\"Error fetching products:\", error);\n        }\n      };\n      // Fetch promotion metrics\n      const fetchPromotionMetrics = async () => {\n        try {\n          const response = await axios.get(\"https://api.thedesigngrit.com/api/products/past-promotions/metrics\");\n          setPastPromotionMetrics(response.data);\n        } catch (error) {\n          console.error(\"Error fetching promotion metrics\", error);\n        }\n      };\n      fetchProducts();\n      fetchPromotionMetrics();\n    }\n  }, [vendor, products]); // Re-fetch when vendor changes\n  const calculatePromotionMetrics = product => {\n    // Find the corresponding metrics for the product in past promotions\n    const metrics = pastPromotionMetrics.find(metric => metric.productId === product._id);\n    if (metrics) {\n      return {\n        salesDuringPromotion: metrics.salesDuringPromotion,\n        viewsDuringPromotion: metrics.viewsDuringPromotion,\n        turnoverIncrease: metrics.turnoverIncrease\n      };\n    }\n    return {\n      salesDuringPromotion: 0,\n      viewsDuringPromotion: 0,\n      turnoverIncrease: 0\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"promotions-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Promotions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \" Home > Promotions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-date-vendor\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateDialog(true),\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"5px\",\n            backgroundColor: \"#2d2d2d\",\n            color: \"white\",\n            padding: \"15px 15px\",\n            borderRadius: \"8px\",\n            border: \"none\",\n            cursor: \"pointer\",\n            fontSize: \"14px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(CiCirclePlus, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), \" Create Promotion\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        onClick: () => setShowTrueStatus(prev => !prev),\n        style: {\n          margin: \"30px 0\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Current Promotions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), showTrueStatus ? /*#__PURE__*/_jsxDEV(AiOutlineUp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(AiOutlineDown, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), showTrueStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-promotions-section\",\n        children: currentPromotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No products with active promotions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-grid\",\n          children: currentPromotions.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"promotion-card\",\n            onClick: () => {\n              setSelectedProduct(product);\n              setEditModalOpen(true);\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-image-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n                alt: product.name,\n                className: \"promotion-image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 23\n              }, this), product.discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"discount-badge\",\n                children: [product.discountPercentage, \"% OFF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"original-price\",\n                  children: [\"E\\xA3\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sale-price\",\n                  children: [\"E\\xA3\", product.salePrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-summary\",\n                children: [product.description.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metrics-container\",\n                style: {\n                  gap: \"20px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Discount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: [product.discountPercentage ? product.discountPercentage : \"No yet Discount\", \" \", \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                  style: {\n                    margin: \"10px 0\",\n                    color: \"#ddd\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: [new Date(product.promotionStartDate).toLocaleDateString(), \" \", \"-\", \" \", new Date(product.promotionEndDate).toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 21\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        onClick: () => setShowUpcomingStatus(prev => !prev),\n        style: {\n          margin: \"30px 0\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Upcoming Promotions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), showUpcomingStatus ? /*#__PURE__*/_jsxDEV(AiOutlineUp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(AiOutlineDown, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 51\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), showUpcomingStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upcoming-promotions-section\",\n        children: upcomingPromotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No upcoming promotions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-grid\",\n          children: upcomingPromotions.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"promotion-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-image-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n                alt: product.name,\n                className: \"promotion-image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 23\n              }, this), product.discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"discount-badge\",\n                children: [product.discountPercentage, \"% OFF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"original-price\",\n                  children: [\"E\\xA3\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sale-price\",\n                  children: [\"E\\xA3\", product.salePrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-summary\",\n                children: [product.description.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Starts Soon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metrics-container\",\n                style: {\n                  gap: \"20px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Start Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: new Date(product.promotionStartDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                  style: {\n                    margin: \"10px 0\",\n                    color: \"#ddd\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"End Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: new Date(product.promotionEndDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 21\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        onClick: () => setShowFalseStatus(prev => !prev),\n        style: {\n          margin: \"30px 0\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Past Promotions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), showFalseStatus ? /*#__PURE__*/_jsxDEV(AiOutlineUp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(AiOutlineDown, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 48\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), showFalseStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"past-promotions-section\",\n        children: pastPromotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No past promotions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-grid\",\n          children: pastPromotions.map(product => {\n            const metrics = calculatePromotionMetrics(product);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-image-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n                  alt: product.name,\n                  className: \"promotion-image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 25\n                }, this), product.discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"discount-badge\",\n                  children: [product.discountPercentage, \"% OFF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    children: [\"E\\xA3\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sale-price\",\n                    children: [\"E\\xA3\", product.salePrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"product-summary\",\n                  children: [product.description.substring(0, 100), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metrics-container\",\n                style: {\n                  padding: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: [\" \", \"Sales During Promotion:\", \" \"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: [\" \", metrics.salesDuringPromotion || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: [\"Views During Promotion:\", \" \"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: metrics.viewsDuringPromotion || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: [\"Turnover Increase:\", \" \"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: [metrics.turnoverIncrease || 0, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 23\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 21\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), selectedProduct && /*#__PURE__*/_jsxDEV(EditPromotionModal, {\n      open: editModalOpen,\n      onClose: () => setEditModalOpen(false),\n      product: selectedProduct,\n      onSave: updatedProduct => {\n        setCurrentPromotions(prev => prev.map(p => p._id === updatedProduct._id ? updatedProduct : p));\n        setEditModalOpen(false);\n      },\n      onEnd: productId => {\n        setCurrentPromotions(prev => prev.filter(p => p._id !== productId));\n        setEditModalOpen(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CreatePromotionDialog, {\n      open: showCreateDialog,\n      onClose: () => setShowCreateDialog(false),\n      brandId: vendor === null || vendor === void 0 ? void 0 : vendor.brandId,\n      onPromotionCreated: updatedProduct => {\n        // refresh logic if needed\n        setCurrentPromotions(prev => [...prev, updatedProduct]);\n        setShowCreateDialog(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(PromotionsPage, \"/OrhDk6e0ygZc6C4GL7kOuXdO7M=\", false, function () {\n  return [useVendor];\n});\n_c = PromotionsPage;\nexport default PromotionsPage;\nvar _c;\n$RefreshReg$(_c, \"PromotionsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "CiCirclePlus", "AiOutlineDown", "AiOutlineUp", "axios", "useVendor", "EditPromotionModal", "CreatePromotionDialog", "jsxDEV", "_jsxDEV", "PromotionsPage", "_s", "vendor", "products", "setProducts", "showFalseStatus", "setShowFalseStatus", "showTrueStatus", "setShowTrueStatus", "selectedProduct", "setSelectedProduct", "showCreateDialog", "setShowCreateDialog", "editModalOpen", "setEditModalOpen", "currentPromotions", "setCurrentPromotions", "pastPromotions", "setPastPromotions", "pastPromotionMetrics", "setPastPromotionMetrics", "upcomingPromotions", "setUpcomingPromotions", "showUpcomingStatus", "setShowUpcomingStatus", "brandId", "fetchProducts", "response", "get", "data", "now", "Date", "current", "filter", "product", "promotionStartDate", "promotionEndDate", "past", "upcoming", "error", "console", "fetchPromotionMetrics", "calculatePromotionMetrics", "metrics", "find", "metric", "productId", "_id", "salesDuringPromotion", "viewsDuringPromotion", "turnoverIncrease", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "display", "alignItems", "gap", "backgroundColor", "color", "padding", "borderRadius", "border", "cursor", "fontSize", "prev", "margin", "length", "map", "src", "mainImage", "alt", "name", "discountPercentage", "price", "salePrice", "description", "substring", "toLocaleDateString", "id", "open", "onClose", "onSave", "updatedProduct", "p", "onEnd", "onPromotionCreated", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/PromotionPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { CiCirclePlus } from \"react-icons/ci\";\r\nimport { AiOutlineDown, AiOutlineUp } from \"react-icons/ai\";\r\nimport axios from \"axios\";\r\nimport { useVendor } from \"../../utils/vendorContext\"; // Vendor context for brandId\r\nimport EditPromotionModal from \"./EditPromotionModal\"; // adjust path as needed\r\nimport CreatePromotionDialog from \"./CreatePromotionDialog\"; // adjust path\r\nconst PromotionsPage = () => {\r\n  const { vendor } = useVendor(); // Access vendor context for brandId\r\n  const [products, setProducts] = useState([]);\r\n  const [showFalseStatus, setShowFalseStatus] = useState(false); // Show products without promotion\r\n  const [showTrueStatus, setShowTrueStatus] = useState(true); // Show products with promotion\r\n  const [selectedProduct, setSelectedProduct] = useState(null);\r\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\r\n\r\n  const [editModalOpen, setEditModalOpen] = useState(false);\r\n  const [currentPromotions, setCurrentPromotions] = useState([]); // Current promotions\r\n  const [pastPromotions, setPastPromotions] = useState([]); // Past promotions\r\n  const [pastPromotionMetrics, setPastPromotionMetrics] = useState([]); // Metrics for past promotions\r\n  const [upcomingPromotions, setUpcomingPromotions] = useState([]);\r\n  const [showUpcomingStatus, setShowUpcomingStatus] = useState(false); // Collapse control\r\n\r\n  useEffect(() => {\r\n    if (vendor) {\r\n      const { brandId } = vendor; // Get the brandId from vendor context\r\n      const fetchProducts = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId}`\r\n          );\r\n          setProducts(response.data); // Set fetched products\r\n          // Separate current and past promotions\r\n          const now = new Date();\r\n          const current = response.data.filter((product) => {\r\n            return (\r\n              product.promotionStartDate &&\r\n              product.promotionEndDate &&\r\n              new Date(product.promotionStartDate) <= now &&\r\n              new Date(product.promotionEndDate) >= now\r\n            );\r\n          });\r\n\r\n          const past = response.data.filter((product) => {\r\n            return (\r\n              product.promotionStartDate &&\r\n              product.promotionEndDate &&\r\n              new Date(product.promotionEndDate) < now\r\n            );\r\n          });\r\n          const upcoming = response.data.filter((product) => {\r\n            return (\r\n              product.promotionStartDate &&\r\n              product.promotionEndDate &&\r\n              new Date(product.promotionStartDate) > now\r\n            );\r\n          });\r\n\r\n          setUpcomingPromotions(upcoming);\r\n          setCurrentPromotions(current);\r\n          setPastPromotions(past);\r\n        } catch (error) {\r\n          console.error(\"Error fetching products:\", error);\r\n        }\r\n      };\r\n      // Fetch promotion metrics\r\n      const fetchPromotionMetrics = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            \"https://api.thedesigngrit.com/api/products/past-promotions/metrics\"\r\n          );\r\n          setPastPromotionMetrics(response.data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching promotion metrics\", error);\r\n        }\r\n      };\r\n\r\n      fetchProducts();\r\n      fetchPromotionMetrics();\r\n    }\r\n  }, [vendor, products]); // Re-fetch when vendor changes\r\n  const calculatePromotionMetrics = (product) => {\r\n    // Find the corresponding metrics for the product in past promotions\r\n    const metrics = pastPromotionMetrics.find(\r\n      (metric) => metric.productId === product._id\r\n    );\r\n\r\n    if (metrics) {\r\n      return {\r\n        salesDuringPromotion: metrics.salesDuringPromotion,\r\n        viewsDuringPromotion: metrics.viewsDuringPromotion,\r\n        turnoverIncrease: metrics.turnoverIncrease,\r\n      };\r\n    }\r\n\r\n    return {\r\n      salesDuringPromotion: 0,\r\n      viewsDuringPromotion: 0,\r\n      turnoverIncrease: 0,\r\n    };\r\n  };\r\n\r\n  return (\r\n    <div className=\"promotions-page\">\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <h2>Promotions</h2>\r\n          <p> Home &gt; Promotions</p>\r\n        </div>\r\n        <div className=\"dashboard-date-vendor\">\r\n          <button\r\n            onClick={() => setShowCreateDialog(true)}\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              gap: \"5px\",\r\n              backgroundColor: \"#2d2d2d\",\r\n              color: \"white\",\r\n              padding: \"15px 15px\",\r\n              borderRadius: \"8px\",\r\n              border: \"none\",\r\n              cursor: \"pointer\",\r\n              fontSize: \"14px\",\r\n            }}\r\n          >\r\n            <CiCirclePlus /> Create Promotion\r\n          </button>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Section for current promotions */}\r\n      <div>\r\n        <div\r\n          className=\"section-header\"\r\n          onClick={() => setShowTrueStatus((prev) => !prev)}\r\n          style={{\r\n            margin: \"30px 0\",\r\n          }}\r\n        >\r\n          <span>Current Promotions</span>\r\n          {showTrueStatus ? <AiOutlineUp /> : <AiOutlineDown />}\r\n        </div>\r\n        {showTrueStatus && (\r\n          <div className=\"current-promotions-section\">\r\n            {currentPromotions.length === 0 ? (\r\n              <p>No products with active promotions.</p>\r\n            ) : (\r\n              <div className=\"product-grid\">\r\n                {currentPromotions.map((product) => (\r\n                  <div\r\n                    className=\"promotion-card\"\r\n                    onClick={() => {\r\n                      setSelectedProduct(product);\r\n                      setEditModalOpen(true);\r\n                    }}\r\n                    key={product.id}\r\n                  >\r\n                    <div className=\"promotion-image-container\">\r\n                      <img\r\n                        src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                        alt={product.name}\r\n                        className=\"promotion-image\"\r\n                      />\r\n                      {product.discountPercentage && (\r\n                        <div className=\"discount-badge\">\r\n                          {product.discountPercentage}% OFF\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"promotion-details\">\r\n                      <h3>{product.name}</h3>\r\n                      <div className=\"price-container\">\r\n                        <span className=\"original-price\">\r\n                          E£{product.price}\r\n                        </span>\r\n                        <span className=\"sale-price\">\r\n                          E£{product.salePrice}\r\n                        </span>\r\n                      </div>\r\n                      <p className=\"product-summary\">\r\n                        {product.description.substring(0, 100)}...\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"product-card-body\">\r\n                      <h5>Summary</h5>\r\n                      <div\r\n                        className=\"metrics-container\"\r\n                        style={{ gap: \"20px\" }}\r\n                      >\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">Discount</span>\r\n\r\n                          <span className=\"metric-value\">\r\n                            {product.discountPercentage\r\n                              ? product.discountPercentage\r\n                              : \"No yet Discount\"}{\" \"}\r\n                            %\r\n                          </span>\r\n                        </div>\r\n                        <hr style={{ margin: \"10px 0\", color: \"#ddd\" }} />\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">Date</span>\r\n                          <span className=\"metric-value\">\r\n                            {new Date(\r\n                              product.promotionStartDate\r\n                            ).toLocaleDateString()}{\" \"}\r\n                            -{\" \"}\r\n                            {new Date(\r\n                              product.promotionEndDate\r\n                            ).toLocaleDateString()}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n      {/* Section for upcoming promotions */}\r\n      <div>\r\n        <div\r\n          className=\"section-header\"\r\n          onClick={() => setShowUpcomingStatus((prev) => !prev)}\r\n          style={{\r\n            margin: \"30px 0\",\r\n          }}\r\n        >\r\n          <span>Upcoming Promotions</span>\r\n          {showUpcomingStatus ? <AiOutlineUp /> : <AiOutlineDown />}\r\n        </div>\r\n        {showUpcomingStatus && (\r\n          <div className=\"upcoming-promotions-section\">\r\n            {upcomingPromotions.length === 0 ? (\r\n              <p>No upcoming promotions.</p>\r\n            ) : (\r\n              <div className=\"product-grid\">\r\n                {upcomingPromotions.map((product) => (\r\n                  <div className=\"promotion-card\" key={product.id}>\r\n                    <div className=\"promotion-image-container\">\r\n                      <img\r\n                        src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                        alt={product.name}\r\n                        className=\"promotion-image\"\r\n                      />\r\n                      {product.discountPercentage && (\r\n                        <div className=\"discount-badge\">\r\n                          {product.discountPercentage}% OFF\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"promotion-details\">\r\n                      <h3>{product.name}</h3>\r\n                      <div className=\"price-container\">\r\n                        <span className=\"original-price\">\r\n                          E£{product.price}\r\n                        </span>\r\n                        <span className=\"sale-price\">\r\n                          E£{product.salePrice}\r\n                        </span>\r\n                      </div>\r\n                      <p className=\"product-summary\">\r\n                        {product.description.substring(0, 100)}...\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"product-card-body\">\r\n                      <h5>Starts Soon</h5>\r\n                      <div\r\n                        className=\"metrics-container\"\r\n                        style={{ gap: \"20px\" }}\r\n                      >\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">Start Date</span>\r\n                          <span className=\"metric-value\">\r\n                            {new Date(\r\n                              product.promotionStartDate\r\n                            ).toLocaleDateString()}\r\n                          </span>\r\n                        </div>\r\n                        <hr style={{ margin: \"10px 0\", color: \"#ddd\" }} />\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">End Date</span>\r\n                          <span className=\"metric-value\">\r\n                            {new Date(\r\n                              product.promotionEndDate\r\n                            ).toLocaleDateString()}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Section for past promotions */}\r\n      <div>\r\n        <div\r\n          className=\"section-header\"\r\n          onClick={() => setShowFalseStatus((prev) => !prev)}\r\n          style={{\r\n            margin: \"30px 0\",\r\n          }}\r\n        >\r\n          <span>Past Promotions</span>\r\n          {showFalseStatus ? <AiOutlineUp /> : <AiOutlineDown />}\r\n        </div>\r\n        {showFalseStatus && (\r\n          <div className=\"past-promotions-section\">\r\n            {pastPromotions.length === 0 ? (\r\n              <p>No past promotions.</p>\r\n            ) : (\r\n              <div className=\"product-grid\">\r\n                {pastPromotions.map((product) => {\r\n                  const metrics = calculatePromotionMetrics(product);\r\n                  return (\r\n                    <div className=\"promotion-card\" key={product.id}>\r\n                      <div className=\"promotion-image-container\">\r\n                        <img\r\n                          src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                          alt={product.name}\r\n                          className=\"promotion-image\"\r\n                        />\r\n                        {product.discountPercentage && (\r\n                          <div className=\"discount-badge\">\r\n                            {product.discountPercentage}% OFF\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"promotion-details\">\r\n                        <h3>{product.name}</h3>\r\n                        <div className=\"price-container\">\r\n                          <span className=\"original-price\">\r\n                            E£{product.price}\r\n                          </span>\r\n                          <span className=\"sale-price\">\r\n                            E£{product.salePrice}\r\n                          </span>\r\n                        </div>\r\n                        <p className=\"product-summary\">\r\n                          {product.description.substring(0, 100)}...\r\n                        </p>\r\n                      </div>\r\n\r\n                      <div\r\n                        className=\"metrics-container\"\r\n                        style={{ padding: \"10px\" }}\r\n                      >\r\n                        {/* Display metrics */}\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">\r\n                            {\" \"}\r\n                            Sales During Promotion:{\" \"}\r\n                          </span>\r\n                          <span className=\"metric-value\">\r\n                            {\" \"}\r\n                            {metrics.salesDuringPromotion || 0}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">\r\n                            Views During Promotion:{\" \"}\r\n                          </span>\r\n                          <span className=\"metric-value\">\r\n                            {metrics.viewsDuringPromotion || 0}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"metric\">\r\n                          <span className=\"metric-label\">\r\n                            Turnover Increase:{\" \"}\r\n                          </span>\r\n                          <span className=\"metric-value\">\r\n                            {metrics.turnoverIncrease || 0}%\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n      {selectedProduct && (\r\n        <EditPromotionModal\r\n          open={editModalOpen}\r\n          onClose={() => setEditModalOpen(false)}\r\n          product={selectedProduct}\r\n          onSave={(updatedProduct) => {\r\n            setCurrentPromotions((prev) =>\r\n              prev.map((p) =>\r\n                p._id === updatedProduct._id ? updatedProduct : p\r\n              )\r\n            );\r\n            setEditModalOpen(false);\r\n          }}\r\n          onEnd={(productId) => {\r\n            setCurrentPromotions((prev) =>\r\n              prev.filter((p) => p._id !== productId)\r\n            );\r\n            setEditModalOpen(false);\r\n          }}\r\n        />\r\n      )}\r\n      <CreatePromotionDialog\r\n        open={showCreateDialog}\r\n        onClose={() => setShowCreateDialog(false)}\r\n        brandId={vendor?.brandId}\r\n        onPromotionCreated={(updatedProduct) => {\r\n          // refresh logic if needed\r\n          setCurrentPromotions((prev) => [...prev, updatedProduct]);\r\n          setShowCreateDialog(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PromotionsPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,2BAA2B,CAAC,CAAC;AACvD,OAAOC,kBAAkB,MAAM,sBAAsB,CAAC,CAAC;AACvD,OAAOC,qBAAqB,MAAM,yBAAyB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAC7D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAO,CAAC,GAAGP,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC8B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtE,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErEC,SAAS,CAAC,MAAM;IACd,IAAIY,MAAM,EAAE;MACV,MAAM;QAAEuB;MAAQ,CAAC,GAAGvB,MAAM,CAAC,CAAC;MAC5B,MAAMwB,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAC9B,gEAAgEH,OAAO,EACzE,CAAC;UACDrB,WAAW,CAACuB,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;UAC5B;UACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;UACtB,MAAMC,OAAO,GAAGL,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,OAAO,IAAK;YAChD,OACEA,OAAO,CAACC,kBAAkB,IAC1BD,OAAO,CAACE,gBAAgB,IACxB,IAAIL,IAAI,CAACG,OAAO,CAACC,kBAAkB,CAAC,IAAIL,GAAG,IAC3C,IAAIC,IAAI,CAACG,OAAO,CAACE,gBAAgB,CAAC,IAAIN,GAAG;UAE7C,CAAC,CAAC;UAEF,MAAMO,IAAI,GAAGV,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,OAAO,IAAK;YAC7C,OACEA,OAAO,CAACC,kBAAkB,IAC1BD,OAAO,CAACE,gBAAgB,IACxB,IAAIL,IAAI,CAACG,OAAO,CAACE,gBAAgB,CAAC,GAAGN,GAAG;UAE5C,CAAC,CAAC;UACF,MAAMQ,QAAQ,GAAGX,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAEC,OAAO,IAAK;YACjD,OACEA,OAAO,CAACC,kBAAkB,IAC1BD,OAAO,CAACE,gBAAgB,IACxB,IAAIL,IAAI,CAACG,OAAO,CAACC,kBAAkB,CAAC,GAAGL,GAAG;UAE9C,CAAC,CAAC;UAEFR,qBAAqB,CAACgB,QAAQ,CAAC;UAC/BtB,oBAAoB,CAACgB,OAAO,CAAC;UAC7Bd,iBAAiB,CAACmB,IAAI,CAAC;QACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC;MACD;MACA,MAAME,qBAAqB,GAAG,MAAAA,CAAA,KAAY;QACxC,IAAI;UACF,MAAMd,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAC9B,oEACF,CAAC;UACDR,uBAAuB,CAACO,QAAQ,CAACE,IAAI,CAAC;QACxC,CAAC,CAAC,OAAOU,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAEDb,aAAa,CAAC,CAAC;MACfe,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACvC,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxB,MAAMuC,yBAAyB,GAAIR,OAAO,IAAK;IAC7C;IACA,MAAMS,OAAO,GAAGxB,oBAAoB,CAACyB,IAAI,CACtCC,MAAM,IAAKA,MAAM,CAACC,SAAS,KAAKZ,OAAO,CAACa,GAC3C,CAAC;IAED,IAAIJ,OAAO,EAAE;MACX,OAAO;QACLK,oBAAoB,EAAEL,OAAO,CAACK,oBAAoB;QAClDC,oBAAoB,EAAEN,OAAO,CAACM,oBAAoB;QAClDC,gBAAgB,EAAEP,OAAO,CAACO;MAC5B,CAAC;IACH;IAEA,OAAO;MACLF,oBAAoB,EAAE,CAAC;MACvBC,oBAAoB,EAAE,CAAC;MACvBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EAED,oBACEnD,OAAA;IAAKoD,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BrD,OAAA;MAAQoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACzCrD,OAAA;QAAKoD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCrD,OAAA;UAAAqD,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBzD,OAAA;UAAAqD,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACNzD,OAAA;QAAKoD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCrD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,IAAI,CAAE;UACzC8C,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,KAAK;YACVC,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,WAAW;YACpBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,gBAEFrD,OAAA,CAACR,YAAY;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTzD,OAAA;MAAAqD,QAAA,gBACErD,OAAA;QACEoD,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMjD,iBAAiB,CAAE6D,IAAI,IAAK,CAACA,IAAI,CAAE;QAClDX,KAAK,EAAE;UACLY,MAAM,EAAE;QACV,CAAE;QAAAlB,QAAA,gBAEFrD,OAAA;UAAAqD,QAAA,EAAM;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9BjD,cAAc,gBAAGR,OAAA,CAACN,WAAW;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACP,aAAa;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,EACLjD,cAAc,iBACbR,OAAA;QAAKoD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxCrC,iBAAiB,CAACwD,MAAM,KAAK,CAAC,gBAC7BxE,OAAA;UAAAqD,QAAA,EAAG;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE1CzD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BrC,iBAAiB,CAACyD,GAAG,CAAEtC,OAAO,iBAC7BnC,OAAA;YACEoD,SAAS,EAAC,gBAAgB;YAC1BM,OAAO,EAAEA,CAAA,KAAM;cACb/C,kBAAkB,CAACwB,OAAO,CAAC;cAC3BpB,gBAAgB,CAAC,IAAI,CAAC;YACxB,CAAE;YAAAsC,QAAA,gBAGFrD,OAAA;cAAKoD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCrD,OAAA;gBACE0E,GAAG,EAAE,uDAAuDvC,OAAO,CAACwC,SAAS,EAAG;gBAChFC,GAAG,EAAEzC,OAAO,CAAC0C,IAAK;gBAClBzB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACDtB,OAAO,CAAC2C,kBAAkB,iBACzB9E,OAAA;gBAAKoD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC5BlB,OAAO,CAAC2C,kBAAkB,EAAC,OAC9B;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAAqD,QAAA,EAAKlB,OAAO,CAAC0C;cAAI;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBzD,OAAA;gBAAKoD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrD,OAAA;kBAAMoD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,OAC7B,EAAClB,OAAO,CAAC4C,KAAK;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACPzD,OAAA;kBAAMoD,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,OACzB,EAAClB,OAAO,CAAC6C,SAAS;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNzD,OAAA;gBAAGoD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAC3BlB,OAAO,CAAC8C,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAAqD,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBzD,OAAA;gBACEoD,SAAS,EAAC,mBAAmB;gBAC7BO,KAAK,EAAE;kBAAEG,GAAG,EAAE;gBAAO,CAAE;gBAAAT,QAAA,gBAEvBrD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAE9CzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAC3BlB,OAAO,CAAC2C,kBAAkB,GACvB3C,OAAO,CAAC2C,kBAAkB,GAC1B,iBAAiB,EAAE,GAAG,EAAC,GAE7B;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNzD,OAAA;kBAAI2D,KAAK,EAAE;oBAAEY,MAAM,EAAE,QAAQ;oBAAEP,KAAK,EAAE;kBAAO;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDzD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1CzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAC3B,IAAIrB,IAAI,CACPG,OAAO,CAACC,kBACV,CAAC,CAAC+C,kBAAkB,CAAC,CAAC,EAAE,GAAG,EAAC,GAC3B,EAAC,GAAG,EACJ,IAAInD,IAAI,CACPG,OAAO,CAACE,gBACV,CAAC,CAAC8C,kBAAkB,CAAC,CAAC;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA1DDtB,OAAO,CAACiD,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2DZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENzD,OAAA;MAAAqD,QAAA,gBACErD,OAAA;QACEoD,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMjC,qBAAqB,CAAE6C,IAAI,IAAK,CAACA,IAAI,CAAE;QACtDX,KAAK,EAAE;UACLY,MAAM,EAAE;QACV,CAAE;QAAAlB,QAAA,gBAEFrD,OAAA;UAAAqD,QAAA,EAAM;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC/BjC,kBAAkB,gBAAGxB,OAAA,CAACN,WAAW;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACP,aAAa;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EACLjC,kBAAkB,iBACjBxB,OAAA;QAAKoD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EACzC/B,kBAAkB,CAACkD,MAAM,KAAK,CAAC,gBAC9BxE,OAAA;UAAAqD,QAAA,EAAG;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE9BzD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B/B,kBAAkB,CAACmD,GAAG,CAAEtC,OAAO,iBAC9BnC,OAAA;YAAKoD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrD,OAAA;cAAKoD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCrD,OAAA;gBACE0E,GAAG,EAAE,uDAAuDvC,OAAO,CAACwC,SAAS,EAAG;gBAChFC,GAAG,EAAEzC,OAAO,CAAC0C,IAAK;gBAClBzB,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACDtB,OAAO,CAAC2C,kBAAkB,iBACzB9E,OAAA;gBAAKoD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC5BlB,OAAO,CAAC2C,kBAAkB,EAAC,OAC9B;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAAqD,QAAA,EAAKlB,OAAO,CAAC0C;cAAI;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBzD,OAAA;gBAAKoD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrD,OAAA;kBAAMoD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,OAC7B,EAAClB,OAAO,CAAC4C,KAAK;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACPzD,OAAA;kBAAMoD,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,OACzB,EAAClB,OAAO,CAAC6C,SAAS;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNzD,OAAA;gBAAGoD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAC3BlB,OAAO,CAAC8C,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBAAAqD,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBzD,OAAA;gBACEoD,SAAS,EAAC,mBAAmB;gBAC7BO,KAAK,EAAE;kBAAEG,GAAG,EAAE;gBAAO,CAAE;gBAAAT,QAAA,gBAEvBrD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B,IAAIrB,IAAI,CACPG,OAAO,CAACC,kBACV,CAAC,CAAC+C,kBAAkB,CAAC;kBAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNzD,OAAA;kBAAI2D,KAAK,EAAE;oBAAEY,MAAM,EAAE,QAAQ;oBAAEP,KAAK,EAAE;kBAAO;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDzD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9CzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3B,IAAIrB,IAAI,CACPG,OAAO,CAACE,gBACV,CAAC,CAAC8C,kBAAkB,CAAC;kBAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAnD6BtB,OAAO,CAACiD,EAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoD1C,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzD,OAAA;MAAAqD,QAAA,gBACErD,OAAA;QACEoD,SAAS,EAAC,gBAAgB;QAC1BM,OAAO,EAAEA,CAAA,KAAMnD,kBAAkB,CAAE+D,IAAI,IAAK,CAACA,IAAI,CAAE;QACnDX,KAAK,EAAE;UACLY,MAAM,EAAE;QACV,CAAE;QAAAlB,QAAA,gBAEFrD,OAAA;UAAAqD,QAAA,EAAM;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC3BnD,eAAe,gBAAGN,OAAA,CAACN,WAAW;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzD,OAAA,CAACP,aAAa;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EACLnD,eAAe,iBACdN,OAAA;QAAKoD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACrCnC,cAAc,CAACsD,MAAM,KAAK,CAAC,gBAC1BxE,OAAA;UAAAqD,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE1BzD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BnC,cAAc,CAACuD,GAAG,CAAEtC,OAAO,IAAK;YAC/B,MAAMS,OAAO,GAAGD,yBAAyB,CAACR,OAAO,CAAC;YAClD,oBACEnC,OAAA;cAAKoD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrD,OAAA;gBAAKoD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCrD,OAAA;kBACE0E,GAAG,EAAE,uDAAuDvC,OAAO,CAACwC,SAAS,EAAG;kBAChFC,GAAG,EAAEzC,OAAO,CAAC0C,IAAK;kBAClBzB,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,EACDtB,OAAO,CAAC2C,kBAAkB,iBACzB9E,OAAA;kBAAKoD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAC5BlB,OAAO,CAAC2C,kBAAkB,EAAC,OAC9B;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNzD,OAAA;gBAAKoD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrD,OAAA;kBAAAqD,QAAA,EAAKlB,OAAO,CAAC0C;gBAAI;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBzD,OAAA;kBAAKoD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BrD,OAAA;oBAAMoD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAAC,OAC7B,EAAClB,OAAO,CAAC4C,KAAK;kBAAA;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACPzD,OAAA;oBAAMoD,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,OACzB,EAAClB,OAAO,CAAC6C,SAAS;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNzD,OAAA;kBAAGoD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAC3BlB,OAAO,CAAC8C,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzC;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENzD,OAAA;gBACEoD,SAAS,EAAC,mBAAmB;gBAC7BO,KAAK,EAAE;kBAAEM,OAAO,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,gBAG3BrD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAC3B,GAAG,EAAC,yBACkB,EAAC,GAAG;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACPzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAC3B,GAAG,EACHT,OAAO,CAACK,oBAAoB,IAAI,CAAC;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNzD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAC,yBACN,EAAC,GAAG;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACPzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC3BT,OAAO,CAACM,oBAAoB,IAAI;kBAAC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNzD,OAAA;kBAAKoD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBrD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAC,oBACX,EAAC,GAAG;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACPzD,OAAA;oBAAMoD,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAC3BT,OAAO,CAACO,gBAAgB,IAAI,CAAC,EAAC,GACjC;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA3D6BtB,OAAO,CAACiD,EAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4D1C,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACL/C,eAAe,iBACdV,OAAA,CAACH,kBAAkB;MACjBwF,IAAI,EAAEvE,aAAc;MACpBwE,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAAC,KAAK,CAAE;MACvCoB,OAAO,EAAEzB,eAAgB;MACzB6E,MAAM,EAAGC,cAAc,IAAK;QAC1BvE,oBAAoB,CAAEqD,IAAI,IACxBA,IAAI,CAACG,GAAG,CAAEgB,CAAC,IACTA,CAAC,CAACzC,GAAG,KAAKwC,cAAc,CAACxC,GAAG,GAAGwC,cAAc,GAAGC,CAClD,CACF,CAAC;QACD1E,gBAAgB,CAAC,KAAK,CAAC;MACzB,CAAE;MACF2E,KAAK,EAAG3C,SAAS,IAAK;QACpB9B,oBAAoB,CAAEqD,IAAI,IACxBA,IAAI,CAACpC,MAAM,CAAEuD,CAAC,IAAKA,CAAC,CAACzC,GAAG,KAAKD,SAAS,CACxC,CAAC;QACDhC,gBAAgB,CAAC,KAAK,CAAC;MACzB;IAAE;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eACDzD,OAAA,CAACF,qBAAqB;MACpBuF,IAAI,EAAEzE,gBAAiB;MACvB0E,OAAO,EAAEA,CAAA,KAAMzE,mBAAmB,CAAC,KAAK,CAAE;MAC1Ca,OAAO,EAAEvB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuB,OAAQ;MACzBiE,kBAAkB,EAAGH,cAAc,IAAK;QACtC;QACAvE,oBAAoB,CAAEqD,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEkB,cAAc,CAAC,CAAC;QACzD3E,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IAAE;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvD,EAAA,CA9ZID,cAAc;EAAA,QACCL,SAAS;AAAA;AAAAgG,EAAA,GADxB3F,cAAc;AAgapB,eAAeA,cAAc;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}