{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\successMsgs\\\\accountExists.jsx\";\nimport React from \"react\";\nimport { IoClose } from \"react-icons/io5\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport { faExclamationCircle } from \"@fortawesome/free-solid-svg-icons\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountExistsPopup = ({\n  show,\n  closePopup\n}) => {\n  if (!show) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Job-sent-popup-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Job-sent-popup-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"Job-sent-popup-close-icon\",\n        onClick: closePopup,\n        children: /*#__PURE__*/_jsxDEV(IoClose, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mail-icon\",\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faExclamationCircle,\n          className: \"animated-mail-icon\",\n          style: {\n            color: \"#ff6b6b\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"ACCOUNT ALREADY EXISTS!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"An account with this email address already exists.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), \"Please try logging in instead.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"Job-sent-popup-button\",\n        onClick: closePopup,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = AccountExistsPopup;\nexport default AccountExistsPopup;\nvar _c;\n$RefreshReg$(_c, \"AccountExistsPopup\");", "map": {"version": 3, "names": ["React", "IoClose", "FontAwesomeIcon", "faExclamationCircle", "jsxDEV", "_jsxDEV", "AccountExistsPopup", "show", "closePopup", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "style", "color", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/successMsgs/accountExists.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { IoClose } from \"react-icons/io5\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faExclamationCircle } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\nconst AccountExistsPopup = ({ show, closePopup }) => {\r\n  if (!show) return null;\r\n  return (\r\n    <div className=\"Job-sent-popup-overlay\">\r\n      <div className=\"Job-sent-popup-container\">\r\n        <div className=\"Job-sent-popup-close-icon\" onClick={closePopup}>\r\n          <IoClose />\r\n        </div>\r\n        <div className=\"mail-icon\">\r\n          <FontAwesomeIcon\r\n            icon={faExclamationCircle}\r\n            className=\"animated-mail-icon\"\r\n            style={{ color: \"#ff6b6b\" }}\r\n          />\r\n        </div>\r\n\r\n        <h1>ACCOUNT ALREADY EXISTS!</h1>\r\n        <p>\r\n          An account with this email address already exists.\r\n          <br />\r\n          Please try logging in instead.\r\n        </p>\r\n        <button className=\"Job-sent-popup-button\" onClick={closePopup}>\r\n          Close\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AccountExistsPopup;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,mBAAmB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAW,CAAC,KAAK;EACnD,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;EACtB,oBACEF,OAAA;IAAKI,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrCL,OAAA;MAAKI,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCL,OAAA;QAAKI,SAAS,EAAC,2BAA2B;QAACE,OAAO,EAAEH,UAAW;QAAAE,QAAA,eAC7DL,OAAA,CAACJ,OAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACNV,OAAA;QAAKI,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBL,OAAA,CAACH,eAAe;UACdc,IAAI,EAAEb,mBAAoB;UAC1BM,SAAS,EAAC,oBAAoB;UAC9BQ,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENV,OAAA;QAAAK,QAAA,EAAI;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCV,OAAA;QAAAK,QAAA,GAAG,oDAED,eAAAL,OAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,kCAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJV,OAAA;QAAQI,SAAS,EAAC,uBAAuB;QAACE,OAAO,EAAEH,UAAW;QAAAE,QAAA,EAAC;MAE/D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GA5BIb,kBAAkB;AA8BxB,eAAeA,kBAAkB;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}