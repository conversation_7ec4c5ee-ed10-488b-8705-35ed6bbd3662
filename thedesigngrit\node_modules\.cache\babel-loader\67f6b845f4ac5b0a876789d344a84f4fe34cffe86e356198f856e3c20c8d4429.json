{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Vendor-Profile\\\\Productscard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Navigation } from \"swiper/modules\";\nimport \"swiper/css\";\nimport \"swiper/css/navigation\";\nimport { Box, CircularProgress } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VendorProductsCard = ({\n  vendor,\n  products = []\n}) => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [swiperInstance, setSwiperInstance] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    const fetchCategories = async () => {\n      setIsLoading(true);\n      try {\n        const response = await fetch(\"https://api.thedesigngrit.com/api/categories/categories/\");\n        const data = await response.json();\n        setCategories(Array.isArray(data) ? data : []); // Ensure data is an array\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        setCategories([]); // Set to empty array on error\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchCategories();\n  }, []);\n\n  // Determine the appropriate slidesPerView based on product count and screen size\n  const getSlidesPerView = () => {\n    const isMobile = window.innerWidth <= 768;\n    if (!Array.isArray(products) || products.length === 0) {\n      return 1; // Default to 1 if no products or not an array\n    } else if (products.length === 1) {\n      return 1;\n    } else if (products.length === 2) {\n      return isMobile ? 1 : 2;\n    } else {\n      return isMobile ? 1 : 3;\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        minHeight: \"200px\",\n        width: \"100%\"\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        thickness: 4,\n        sx: {\n          color: \"#6b7b58\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle case where products is not an array or is empty\n  if (!Array.isArray(products) || products.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: \"#888\",\n        fontStyle: \"italic\",\n        textAlign: \"center\",\n        padding: \"40px 0\"\n      },\n      children: \"No products available for this vendor yet.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"related-products-container\",\n    style: {\n      padding: window.innerWidth <= 768 ? \"30px 25px\" : \"49px 110px\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Swiper, {\n      modules: [Navigation],\n      slidesPerView: getSlidesPerView(),\n      spaceBetween: window.innerWidth <= 768 ? 10 : 20,\n      navigation: products.length > 1,\n      loop: products.length > 2 // Only enable loop when there are more than 2 products\n      ,\n      className: \"related-swiper\",\n      onSwiper: setSwiperInstance,\n      centeredSlides: products.length === 1,\n      children: products.map(product => {\n        const category = Array.isArray(categories) ? categories.find(cat => cat._id === product.category) : undefined;\n        const categoryName = category ? category.name : \"Unknown Category\";\n        return /*#__PURE__*/_jsxDEV(SwiperSlide, {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: `/product/${product._id}`,\n            className: \"related-product-card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"related-product-image-container\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n                  alt: product.name,\n                  className: \"related-img\",\n                  style: {\n                    width: \"100%\",\n                    height: \"200px\",\n                    objectFit: \"cover\",\n                    borderRadius: \"8px\",\n                    border: \"1px solid #ddd\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"related-info\",\n                style: {\n                  marginTop: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"related-category\",\n                  style: {\n                    fontSize: \"14px\",\n                    color: \"#888\",\n                    marginBottom: \"4px\"\n                  },\n                  children: categoryName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"related-name\",\n                  style: {\n                    fontSize: \"16px\",\n                    fontWeight: \"bold\",\n                    marginBottom: \"4px\",\n                    color: \"#222\"\n                  },\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"related-price\",\n                  style: {\n                    fontSize: \"15px\",\n                    color: \"#2d2d2d\"\n                  },\n                  children: [product.salePrice ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      textDecoration: \"line-through\",\n                      marginRight: \"5px\"\n                    },\n                    children: [product.price.toLocaleString(\"en-US\"), \" E\\xA3\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 25\n                  }, this) : `${product.price.toLocaleString(\"en-US\")} E£`, product.salePrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: \"red\"\n                    },\n                    children: [product.salePrice.toLocaleString(\"en-US\"), \" E\\xA3\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(VendorProductsCard, \"xqnEcUaFaq26cvPR0rCbCV50fIM=\");\n_c = VendorProductsCard;\nexport default VendorProductsCard;\nvar _c;\n$RefreshReg$(_c, \"VendorProductsCard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Link", "Swiper", "SwiperSlide", "Navigation", "Box", "CircularProgress", "jsxDEV", "_jsxDEV", "VendorProductsCard", "vendor", "products", "_s", "categories", "setCategories", "swiperInstance", "setSwiperInstance", "isLoading", "setIsLoading", "fetchCategories", "response", "fetch", "data", "json", "Array", "isArray", "error", "console", "getSlidesPerView", "isMobile", "window", "innerWidth", "length", "sx", "display", "justifyContent", "alignItems", "minHeight", "width", "children", "size", "thickness", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontStyle", "textAlign", "padding", "className", "modules", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "navigation", "loop", "onSwiper", "centeredSlides", "map", "product", "category", "find", "cat", "_id", "undefined", "categoryName", "name", "to", "src", "mainImage", "alt", "height", "objectFit", "borderRadius", "border", "marginTop", "fontSize", "marginBottom", "fontWeight", "salePrice", "textDecoration", "marginRight", "price", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Vendor-Profile/Productscard.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport { Navigation } from \"swiper/modules\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/navigation\";\r\nimport { Box, CircularProgress } from \"@mui/material\";\r\n\r\nconst VendorProductsCard = ({ vendor, products = [] }) => {\r\n  const [categories, setCategories] = useState([]);\r\n  const [swiperInstance, setSwiperInstance] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      setIsLoading(true);\r\n      try {\r\n        const response = await fetch(\r\n          \"https://api.thedesigngrit.com/api/categories/categories/\"\r\n        );\r\n        const data = await response.json();\r\n        setCategories(Array.isArray(data) ? data : []); // Ensure data is an array\r\n      } catch (error) {\r\n        console.error(\"Error fetching categories:\", error);\r\n        setCategories([]); // Set to empty array on error\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  // Determine the appropriate slidesPerView based on product count and screen size\r\n  const getSlidesPerView = () => {\r\n    const isMobile = window.innerWidth <= 768;\r\n\r\n    if (!Array.isArray(products) || products.length === 0) {\r\n      return 1; // Default to 1 if no products or not an array\r\n    } else if (products.length === 1) {\r\n      return 1;\r\n    } else if (products.length === 2) {\r\n      return isMobile ? 1 : 2;\r\n    } else {\r\n      return isMobile ? 1 : 3;\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Box\r\n        sx={{\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"center\",\r\n          minHeight: \"200px\",\r\n          width: \"100%\",\r\n        }}\r\n      >\r\n        <CircularProgress size={60} thickness={4} sx={{ color: \"#6b7b58\" }} />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  // Handle case where products is not an array or is empty\r\n  if (!Array.isArray(products) || products.length === 0) {\r\n    return (\r\n      <p\r\n        style={{\r\n          color: \"#888\",\r\n          fontStyle: \"italic\",\r\n          textAlign: \"center\",\r\n          padding: \"40px 0\",\r\n        }}\r\n      >\r\n        No products available for this vendor yet.\r\n      </p>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"related-products-container\"\r\n      style={{\r\n        padding: window.innerWidth <= 768 ? \"30px 25px\" : \"49px 110px\",\r\n      }}\r\n    >\r\n      <Swiper\r\n        modules={[Navigation]}\r\n        slidesPerView={getSlidesPerView()}\r\n        spaceBetween={window.innerWidth <= 768 ? 10 : 20}\r\n        navigation={products.length > 1}\r\n        loop={products.length > 2} // Only enable loop when there are more than 2 products\r\n        className=\"related-swiper\"\r\n        onSwiper={setSwiperInstance}\r\n        centeredSlides={products.length === 1}\r\n      >\r\n        {products.map((product) => {\r\n          const category = Array.isArray(categories)\r\n            ? categories.find((cat) => cat._id === product.category)\r\n            : undefined;\r\n          const categoryName = category ? category.name : \"Unknown Category\";\r\n\r\n          return (\r\n            <SwiperSlide key={product._id}>\r\n              <Link\r\n                to={`/product/${product._id}`}\r\n                className=\"related-product-card\"\r\n              >\r\n                <div className=\"related-product-card\">\r\n                  <div className=\"related-product-image-container\">\r\n                    <img\r\n                      src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                      alt={product.name}\r\n                      className=\"related-img\"\r\n                      style={{\r\n                        width: \"100%\",\r\n                        height: \"200px\",\r\n                        objectFit: \"cover\",\r\n                        borderRadius: \"8px\",\r\n                        border: \"1px solid #ddd\",\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  <div className=\"related-info\" style={{ marginTop: \"10px\" }}>\r\n                    <p\r\n                      className=\"related-category\"\r\n                      style={{\r\n                        fontSize: \"14px\",\r\n                        color: \"#888\",\r\n                        marginBottom: \"4px\",\r\n                      }}\r\n                    >\r\n                      {categoryName}\r\n                    </p>\r\n                    <h3\r\n                      className=\"related-name\"\r\n                      style={{\r\n                        fontSize: \"16px\",\r\n                        fontWeight: \"bold\",\r\n                        marginBottom: \"4px\",\r\n                        color: \"#222\",\r\n                      }}\r\n                    >\r\n                      {product.name}\r\n                    </h3>\r\n                    <p\r\n                      className=\"related-price\"\r\n                      style={{ fontSize: \"15px\", color: \"#2d2d2d\" }}\r\n                    >\r\n                      {product.salePrice ? (\r\n                        <span\r\n                          style={{\r\n                            textDecoration: \"line-through\",\r\n                            marginRight: \"5px\",\r\n                          }}\r\n                        >\r\n                          {product.price.toLocaleString(\"en-US\")} E£\r\n                        </span>\r\n                      ) : (\r\n                        `${product.price.toLocaleString(\"en-US\")} E£`\r\n                      )}\r\n                      {product.salePrice && (\r\n                        <span style={{ color: \"red\" }}>\r\n                          {product.salePrice.toLocaleString(\"en-US\")} E£\r\n                        </span>\r\n                      )}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </Link>\r\n            </SwiperSlide>\r\n          );\r\n        })}\r\n      </Swiper>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VendorProductsCard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,SAASC,GAAG,EAAEC,gBAAgB,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAEhDD,SAAS,CAAC,MAAM;IACd,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClCD,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAC1B,0DACF,CAAC;QACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCT,aAAa,CAACU,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDZ,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;MACrB,CAAC,SAAS;QACRI,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG;IAEzC,IAAI,CAACP,KAAK,CAACC,OAAO,CAACd,QAAQ,CAAC,IAAIA,QAAQ,CAACqB,MAAM,KAAK,CAAC,EAAE;MACrD,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM,IAAIrB,QAAQ,CAACqB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIrB,QAAQ,CAACqB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAOH,QAAQ,GAAG,CAAC,GAAG,CAAC;IACzB,CAAC,MAAM;MACL,OAAOA,QAAQ,GAAG,CAAC,GAAG,CAAC;IACzB;EACF,CAAC;EAED,IAAIZ,SAAS,EAAE;IACb,oBACET,OAAA,CAACH,GAAG;MACF4B,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,OAAO;QAClBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,eAEF/B,OAAA,CAACF,gBAAgB;QAACkC,IAAI,EAAE,EAAG;QAACC,SAAS,EAAE,CAAE;QAACR,EAAE,EAAE;UAAES,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC;EAEV;;EAEA;EACA,IAAI,CAACtB,KAAK,CAACC,OAAO,CAACd,QAAQ,CAAC,IAAIA,QAAQ,CAACqB,MAAM,KAAK,CAAC,EAAE;IACrD,oBACExB,OAAA;MACEuC,KAAK,EAAE;QACLL,KAAK,EAAE,MAAM;QACbM,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE;MACX,CAAE;MAAAX,QAAA,EACH;IAED;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAER;EAEA,oBACEtC,OAAA;IACE2C,SAAS,EAAC,4BAA4B;IACtCJ,KAAK,EAAE;MACLG,OAAO,EAAEpB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG;IACpD,CAAE;IAAAQ,QAAA,eAEF/B,OAAA,CAACN,MAAM;MACLkD,OAAO,EAAE,CAAChD,UAAU,CAAE;MACtBiD,aAAa,EAAEzB,gBAAgB,CAAC,CAAE;MAClC0B,YAAY,EAAExB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,EAAE,GAAG,EAAG;MACjDwB,UAAU,EAAE5C,QAAQ,CAACqB,MAAM,GAAG,CAAE;MAChCwB,IAAI,EAAE7C,QAAQ,CAACqB,MAAM,GAAG,CAAE,CAAC;MAAA;MAC3BmB,SAAS,EAAC,gBAAgB;MAC1BM,QAAQ,EAAEzC,iBAAkB;MAC5B0C,cAAc,EAAE/C,QAAQ,CAACqB,MAAM,KAAK,CAAE;MAAAO,QAAA,EAErC5B,QAAQ,CAACgD,GAAG,CAAEC,OAAO,IAAK;QACzB,MAAMC,QAAQ,GAAGrC,KAAK,CAACC,OAAO,CAACZ,UAAU,CAAC,GACtCA,UAAU,CAACiD,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,GAAG,KAAKJ,OAAO,CAACC,QAAQ,CAAC,GACtDI,SAAS;QACb,MAAMC,YAAY,GAAGL,QAAQ,GAAGA,QAAQ,CAACM,IAAI,GAAG,kBAAkB;QAElE,oBACE3D,OAAA,CAACL,WAAW;UAAAoC,QAAA,eACV/B,OAAA,CAACP,IAAI;YACHmE,EAAE,EAAE,YAAYR,OAAO,CAACI,GAAG,EAAG;YAC9Bb,SAAS,EAAC,sBAAsB;YAAAZ,QAAA,eAEhC/B,OAAA;cAAK2C,SAAS,EAAC,sBAAsB;cAAAZ,QAAA,gBACnC/B,OAAA;gBAAK2C,SAAS,EAAC,iCAAiC;gBAAAZ,QAAA,eAC9C/B,OAAA;kBACE6D,GAAG,EAAE,uDAAuDT,OAAO,CAACU,SAAS,EAAG;kBAChFC,GAAG,EAAEX,OAAO,CAACO,IAAK;kBAClBhB,SAAS,EAAC,aAAa;kBACvBJ,KAAK,EAAE;oBACLT,KAAK,EAAE,MAAM;oBACbkC,MAAM,EAAE,OAAO;oBACfC,SAAS,EAAE,OAAO;oBAClBC,YAAY,EAAE,KAAK;oBACnBC,MAAM,EAAE;kBACV;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtC,OAAA;gBAAK2C,SAAS,EAAC,cAAc;gBAACJ,KAAK,EAAE;kBAAE6B,SAAS,EAAE;gBAAO,CAAE;gBAAArC,QAAA,gBACzD/B,OAAA;kBACE2C,SAAS,EAAC,kBAAkB;kBAC5BJ,KAAK,EAAE;oBACL8B,QAAQ,EAAE,MAAM;oBAChBnC,KAAK,EAAE,MAAM;oBACboC,YAAY,EAAE;kBAChB,CAAE;kBAAAvC,QAAA,EAED2B;gBAAY;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACJtC,OAAA;kBACE2C,SAAS,EAAC,cAAc;kBACxBJ,KAAK,EAAE;oBACL8B,QAAQ,EAAE,MAAM;oBAChBE,UAAU,EAAE,MAAM;oBAClBD,YAAY,EAAE,KAAK;oBACnBpC,KAAK,EAAE;kBACT,CAAE;kBAAAH,QAAA,EAEDqB,OAAO,CAACO;gBAAI;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACLtC,OAAA;kBACE2C,SAAS,EAAC,eAAe;kBACzBJ,KAAK,EAAE;oBAAE8B,QAAQ,EAAE,MAAM;oBAAEnC,KAAK,EAAE;kBAAU,CAAE;kBAAAH,QAAA,GAE7CqB,OAAO,CAACoB,SAAS,gBAChBxE,OAAA;oBACEuC,KAAK,EAAE;sBACLkC,cAAc,EAAE,cAAc;sBAC9BC,WAAW,EAAE;oBACf,CAAE;oBAAA3C,QAAA,GAEDqB,OAAO,CAACuB,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,QACzC;kBAAA;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,GAEP,GAAGc,OAAO,CAACuB,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,KACzC,EACAxB,OAAO,CAACoB,SAAS,iBAChBxE,OAAA;oBAAMuC,KAAK,EAAE;sBAAEL,KAAK,EAAE;oBAAM,CAAE;oBAAAH,QAAA,GAC3BqB,OAAO,CAACoB,SAAS,CAACI,cAAc,CAAC,OAAO,CAAC,EAAC,QAC7C;kBAAA;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GAlESc,OAAO,CAACI,GAAG;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmEhB,CAAC;MAElB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClC,EAAA,CAzKIH,kBAAkB;AAAA4E,EAAA,GAAlB5E,kBAAkB;AA2KxB,eAAeA,kBAAkB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}