{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\accountingAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useMemo } from \"react\";\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Select, MenuItem, FormControl, InputLabel, TextField, Button, CircularProgress, Typography, TableSortLabel, Dialog, DialogTitle, DialogContent, DialogActions, IconButton } from \"@mui/material\";\nimport { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from \"recharts\";\nimport axios from \"axios\";\nimport { format } from \"date-fns\";\nimport { FaMoneyBillWave, FaPiggyBank, FaCreditCard, FaHandHoldingUsd, FaChartBar } from \"react-icons/fa\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst columns = [{\n  id: \"orderId\",\n  label: \"Order ID\"\n}, {\n  id: \"brand\",\n  label: \"Brand\"\n}, {\n  id: \"total\",\n  label: \"Total (EGP)\",\n  money: true\n}, {\n  id: \"vat\",\n  label: \"VAT\",\n  money: true\n}, {\n  id: \"shippingFee\",\n  label: \"Shipping\",\n  money: true\n}, {\n  id: \"paymobFee\",\n  label: \"Paymob Fee\",\n  money: true\n}, {\n  id: \"commission\",\n  label: \"Commission\",\n  money: true\n}, {\n  id: \"brandPayout\",\n  label: \"Brand Payout\",\n  money: true\n}, {\n  id: \"netAdminProfit\",\n  label: \"Admin Profit\",\n  money: true\n}, {\n  id: \"date\",\n  label: \"Date\"\n}];\nconst sortOptions = [{\n  value: \"total\",\n  label: \"Total (EGP)\"\n}, {\n  value: \"netAdminProfit\",\n  label: \"Admin Profit\"\n}, {\n  value: \"brandPayout\",\n  label: \"Brand Payout\"\n}, {\n  value: \"date\",\n  label: \"Date\"\n}];\nfunction formatMoney(value) {\n  if (value === null || value === undefined || value === \"N/A\") return \"N/A\";\n  return `${Number(value).toLocaleString(\"en-US\", {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  })} E£`;\n}\nconst MoneyCell = ({\n  value\n}) => value === \"N/A\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n  children: \"N/A\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 77,\n  columnNumber: 5\n}, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n  style: {\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: 2\n  },\n  children: formatMoney(value)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 79,\n  columnNumber: 5\n}, this);\n_c = MoneyCell;\nconst calculatorOptions = [{\n  value: \"brandPayout\",\n  label: \"Brand Payout\"\n}, {\n  value: \"total\",\n  label: \"Total Sales\"\n}, {\n  value: \"commission\",\n  label: \"Commission\"\n}, {\n  value: \"netAdminProfit\",\n  label: \"Profit Admin\"\n}];\nfunction exportToCSV(data, brandName, calcType = null) {\n  if (!data.length) return;\n  const headers = [\"Order ID\", \"Brand\", \"Total (EGP)\", \"VAT\", \"Shipping\", \"Paymob Fee\", \"Commission\", \"Brand Payout\", \"Admin Profit\", \"Date\"];\n  const wrap = val => `\"${val}\"`;\n  const rows = data.map(log => {\n    var _log$orderId, _log$brandId, _log$total, _log$vat, _log$shippingFee, _log$paymobFee, _log$commission, _log$brandPayout, _log$netAdminProfit;\n    return [wrap(((_log$orderId = log.orderId) === null || _log$orderId === void 0 ? void 0 : _log$orderId._id) || \"N/A\"), wrap(((_log$brandId = log.brandId) === null || _log$brandId === void 0 ? void 0 : _log$brandId.brandName) || \"N/A\"), wrap((_log$total = log.total) !== null && _log$total !== void 0 ? _log$total : \"N/A\"), wrap((_log$vat = log.vat) !== null && _log$vat !== void 0 ? _log$vat : \"N/A\"), wrap((_log$shippingFee = log.shippingFee) !== null && _log$shippingFee !== void 0 ? _log$shippingFee : \"N/A\"), wrap((_log$paymobFee = log.paymobFee) !== null && _log$paymobFee !== void 0 ? _log$paymobFee : \"N/A\"), wrap((_log$commission = log.commission) !== null && _log$commission !== void 0 ? _log$commission : \"N/A\"), wrap((_log$brandPayout = log.brandPayout) !== null && _log$brandPayout !== void 0 ? _log$brandPayout : \"N/A\"), wrap((_log$netAdminProfit = log.netAdminProfit) !== null && _log$netAdminProfit !== void 0 ? _log$netAdminProfit : \"N/A\"), wrap(log.date ? new Date(log.date).toLocaleDateString() : \"N/A\")];\n  });\n\n  // Calculate totals for each money column\n  const sum = key => data.reduce((acc, log) => acc + (Number(log[key]) || 0), 0);\n  let totalsRow = [\"\",\n  // Order ID\n  \"TOTAL\",\n  // Brand\n  sum(\"total\"), sum(\"vat\"), sum(\"shippingFee\"), sum(\"paymobFee\"), sum(\"commission\"), sum(\"brandPayout\"), sum(\"netAdminProfit\"), \"\" // Date\n  ].map((val, idx) => {\n    // Format money columns\n    if ([2, 3, 4, 5, 6, 7, 8].includes(idx) && typeof val === \"number\") {\n      return val.toLocaleString(\"en-US\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    }\n    return val;\n  });\n\n  // If a calculation type is provided, add a label to the totals row for that column\n  if (calcType) {\n    const colIdx = {\n      total: 2,\n      vat: 3,\n      shippingFee: 4,\n      paymobFee: 5,\n      commission: 6,\n      brandPayout: 7,\n      netAdminProfit: 8\n    }[calcType];\n    if (colIdx !== undefined) {\n      totalsRow[colIdx] = `SUM: ${totalsRow[colIdx]}`;\n    }\n  }\n\n  // Ensure totalsRow matches header length and wrap all values\n  totalsRow = totalsRow.map(wrap);\n  while (totalsRow.length < headers.length) totalsRow.push(wrap(\"\"));\n  while (totalsRow.length > headers.length) totalsRow = totalsRow.slice(0, headers.length);\n  let csvContent = headers.map(wrap).join(\",\") + \"\\n\";\n  rows.forEach(row => {\n    csvContent += row.join(\",\") + \"\\n\";\n  });\n  csvContent += totalsRow.join(\",\") + \"\\n\";\n  const blob = new Blob([csvContent], {\n    type: \"text/csv;charset=utf-8;\"\n  });\n  const link = document.createElement(\"a\");\n  link.href = URL.createObjectURL(blob);\n  link.setAttribute(\"download\", `${brandName || \"all_brands\"}_financials.csv`);\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n}\nconst chartPeriods = [{\n  value: \"week\",\n  label: \"Weeks\"\n}, {\n  value: \"month\",\n  label: \"Months\"\n}, {\n  value: \"year\",\n  label: \"Years\"\n}];\nfunction groupLogsByPeriod(logs, period) {\n  // Returns [{ label, total }]\n  if (!logs.length) return [];\n  const result = {};\n  logs.forEach(log => {\n    if (!log.date) return;\n    const date = new Date(log.date);\n    let label = \"\";\n    if (period === \"week\") {\n      // Week of year: YYYY-WW\n      const year = date.getFullYear();\n      const firstJan = new Date(date.getFullYear(), 0, 1);\n      const week = Math.ceil(((date - firstJan) / 86400000 + firstJan.getDay() + 1) / 7);\n      label = `${year}-W${week}`;\n    } else if (period === \"month\") {\n      label = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}`;\n    } else if (period === \"year\") {\n      label = `${date.getFullYear()}`;\n    }\n    if (!result[label]) result[label] = 0;\n    result[label] += Number(log.total) || 0;\n  });\n  // Sort labels\n  return Object.entries(result).sort(([a], [b]) => a.localeCompare(b)).map(([label, total]) => ({\n    label,\n    total: Number(total.toFixed(2))\n  }));\n}\nfunction getMonthYearLabel(label) {\n  // Only parse if label matches YYYY-MM\n  if (!label || !/^[0-9]{4}-[0-9]{2}$/.test(label)) return label;\n  const [year, month] = label.split(\"-\");\n  const date = new Date(Number(year), Number(month) - 1, 1);\n  return format(date, \"MMM yyyy\");\n}\nfunction getWeekLabel(label) {\n  // label: '2025-W23' => 'W23 2025'\n  if (!label) return \"\";\n  const [year, week] = label.split(\"-W\");\n  if (!year || !week) return label;\n  return `W${week} ${year}`;\n}\nfunction getXAxisLabel(label, period) {\n  if (period === \"month\") return getMonthYearLabel(label);\n  if (period === \"week\") return getWeekLabel(label);\n  return label;\n}\nfunction getAvailableMonths(data) {\n  // Returns array of { value: '2025-06', label: 'Jun 2025' }\n  return data.map(d => ({\n    value: d.label,\n    label: getMonthYearLabel(d.label)\n  }));\n}\nconst ROWS_PER_PAGE = 12;\nconst AccountingAdmin = () => {\n  _s();\n  var _selectedLog$orderId, _selectedLog$brandId;\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [brandFilter, setBrandFilter] = useState(\"\");\n  const [dateFrom, setDateFrom] = useState(\"\");\n  const [dateTo, setDateTo] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"date\");\n  const [sortOrder, setSortOrder] = useState(\"desc\");\n\n  // Calculator states\n  const [calcBrand, setCalcBrand] = useState(\"\");\n  const [calcType, setCalcType] = useState(\"brandPayout\");\n  const [calcResult, setCalcResult] = useState(null);\n\n  // Chart states\n  const [chartPeriod, setChartPeriod] = useState(\"month\");\n  const [chartBrand, setChartBrand] = useState(\"\");\n  const [chartDateFrom, setChartDateFrom] = useState(\"\");\n  const [chartDateTo, setChartDateTo] = useState(\"\");\n  const [selectedMonth, setSelectedMonth] = useState(\"\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const [selectedLog, setSelectedLog] = useState(null);\n  useEffect(() => {\n    setLoading(true);\n    axios.get(\"https://api.thedesigngrit.com/api/admin-financials/getall-logs\").then(res => {\n      setLogs(res.data || []);\n      setLoading(false);\n    }).catch(err => {\n      setError(\"Failed to fetch data\");\n      setLoading(false);\n    });\n  }, []);\n\n  // Extract unique brands for filter dropdown\n  const brands = useMemo(() => {\n    const brandSet = new Set();\n    logs.forEach(log => {\n      if (log.brandId && log.brandId.brandName) {\n        brandSet.add(log.brandId.brandName);\n      }\n    });\n    return Array.from(brandSet);\n  }, [logs]);\n\n  // Filtering\n  const filteredLogs = useMemo(() => {\n    return logs.filter(log => {\n      var _log$brandId2;\n      // Brand filter\n      if (brandFilter && ((_log$brandId2 = log.brandId) === null || _log$brandId2 === void 0 ? void 0 : _log$brandId2.brandName) !== brandFilter) return false;\n      // Date filter\n      if (dateFrom && new Date(log.date) < new Date(dateFrom)) return false;\n      if (dateTo && new Date(log.date) > new Date(dateTo)) return false;\n      return true;\n    });\n  }, [logs, brandFilter, dateFrom, dateTo]);\n\n  // Sorting\n  const sortedLogs = useMemo(() => {\n    const sorted = [...filteredLogs];\n    sorted.sort((a, b) => {\n      let aValue, bValue;\n      if (sortBy === \"date\") {\n        aValue = new Date(a.date);\n        bValue = new Date(b.date);\n      } else {\n        var _a$sortBy, _b$sortBy;\n        aValue = (_a$sortBy = a[sortBy]) !== null && _a$sortBy !== void 0 ? _a$sortBy : 0;\n        bValue = (_b$sortBy = b[sortBy]) !== null && _b$sortBy !== void 0 ? _b$sortBy : 0;\n      }\n      if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\n      if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\n      return 0;\n    });\n    return sorted;\n  }, [filteredLogs, sortBy, sortOrder]);\n\n  // Calculator logic\n  const handleCalculate = () => {\n    // If calcBrand is empty string, calculate for all brands\n    const brandLogs = !calcBrand ? logs : logs.filter(log => {\n      var _log$brandId3;\n      return ((_log$brandId3 = log.brandId) === null || _log$brandId3 === void 0 ? void 0 : _log$brandId3.brandName) === calcBrand;\n    });\n    if (!brandLogs.length) {\n      setCalcResult(\"No data for this brand.\");\n      return;\n    }\n    const sum = brandLogs.reduce((acc, log) => acc + (Number(log[calcType]) || 0), 0);\n    setCalcResult(`${calculatorOptions.find(opt => opt.value === calcType).label} for ${calcBrand || \"All Brands\"}: ${formatMoney(sum)}`);\n  };\n\n  // Calculator export logic\n  const handleExport = () => {\n    const brandLogs = !calcBrand ? logs : logs.filter(log => {\n      var _log$brandId4;\n      return ((_log$brandId4 = log.brandId) === null || _log$brandId4 === void 0 ? void 0 : _log$brandId4.brandName) === calcBrand;\n    });\n    exportToCSV(brandLogs, calcBrand || \"all_brands\", calcBrand && calcType ? calcType : null);\n  };\n\n  // Chart filtered logs\n  const chartFilteredLogs = useMemo(() => {\n    return logs.filter(log => {\n      var _log$brandId5;\n      if (chartBrand && ((_log$brandId5 = log.brandId) === null || _log$brandId5 === void 0 ? void 0 : _log$brandId5.brandName) !== chartBrand) return false;\n      if (chartDateFrom && new Date(log.date) < new Date(chartDateFrom)) return false;\n      if (chartDateTo && new Date(log.date) > new Date(chartDateTo)) return false;\n      return true;\n    });\n  }, [logs, chartBrand, chartDateFrom, chartDateTo]);\n  const chartData = useMemo(() => groupLogsByPeriod(chartFilteredLogs, chartPeriod), [chartFilteredLogs, chartPeriod]);\n\n  // Filter chartData by selectedMonth if in months mode\n  const displayedChartData = useMemo(() => {\n    if (chartPeriod === \"month\" && selectedMonth) {\n      return chartData.filter(d => d.label === selectedMonth);\n    }\n    return chartData;\n  }, [chartData, chartPeriod, selectedMonth]);\n\n  // Available months for select menu\n  const availableMonths = useMemo(() => {\n    if (chartPeriod !== \"month\") return [];\n    return getAvailableMonths(chartData).filter(m => /^[0-9]{4}-[0-9]{2}$/.test(m.value));\n  }, [chartData, chartPeriod]);\n\n  // Pagination logic\n  const totalPages = Math.ceil(sortedLogs.length / ROWS_PER_PAGE);\n  const indexOfLastRow = currentPage * ROWS_PER_PAGE;\n  const indexOfFirstRow = indexOfLastRow - ROWS_PER_PAGE;\n  const currentRows = sortedLogs.slice(indexOfFirstRow, indexOfLastRow);\n\n  // Reset to first page if filters change and current page is out of range\n  useEffect(() => {\n    if (currentPage > totalPages) setCurrentPage(1);\n  }, [totalPages, currentPage]);\n\n  // Totals from filtered logs (sortedLogs)\n  const totalVat = useMemo(() => sortedLogs.reduce((acc, log) => acc + (Number(log.vat) || 0), 0), [sortedLogs]);\n  const totalProfit = useMemo(() => sortedLogs.reduce((acc, log) => acc + (Number(log.netAdminProfit) || 0), 0), [sortedLogs]);\n  const totalPaymob = useMemo(() => sortedLogs.reduce((acc, log) => acc + (Number(log.paymobFee) || 0), 0), [sortedLogs]);\n  const totalBrandPayout = useMemo(() => sortedLogs.reduce((acc, log) => acc + (Number(log.brandPayout) || 0), 0), [sortedLogs]);\n  const totalSales = useMemo(() => sortedLogs.reduce((acc, log) => acc + (Number(log.total) || 0), 0), [sortedLogs]);\n  if (loading) return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    minHeight: \"60vh\",\n    children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 440,\n    columnNumber: 7\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    minHeight: \"60vh\",\n    children: /*#__PURE__*/_jsxDEV(Typography, {\n      color: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 451,\n    columnNumber: 7\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        flexDirection: \"row\",\n        gap: 3,\n        mb: 4,\n        flexWrap: \"wrap\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          minWidth: 180,\n          background: \"#fff\",\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n          size: 28,\n          style: {\n            color: \"#6b7b58\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: 0,\n              fontSize: \"1.1em\",\n              fontWeight: 700\n            },\n            children: \"Total Taxes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontWeight: 600,\n              fontSize: \"1.1em\"\n            },\n            children: [totalVat.toLocaleString(\"en-US\", {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            }), \" \", \"E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          minWidth: 180,\n          background: \"#fff\",\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaPiggyBank, {\n          size: 28,\n          style: {\n            color: \"#6b7b58\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: 0,\n              fontSize: \"1.1em\",\n              fontWeight: 700\n            },\n            children: \"Total Profit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontWeight: 600,\n              fontSize: \"1.1em\"\n            },\n            children: [totalProfit.toLocaleString(\"en-US\", {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            }), \" \", \"E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          minWidth: 180,\n          background: \"#fff\",\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n          size: 28,\n          style: {\n            color: \"#6b7b58\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: 0,\n              fontSize: \"1.1em\",\n              fontWeight: 700\n            },\n            children: \"Total Paymob Fees\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontWeight: 600,\n              fontSize: \"1.1em\"\n            },\n            children: [totalPaymob.toLocaleString(\"en-US\", {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            }), \" \", \"E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          minWidth: 180,\n          background: \"#fff\",\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaHandHoldingUsd, {\n          size: 28,\n          style: {\n            color: \"#6b7b58\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: 0,\n              fontSize: \"1.1em\",\n              fontWeight: 700\n            },\n            children: \"Total Brand Payout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontWeight: 600,\n              fontSize: \"1.1em\"\n            },\n            children: [totalBrandPayout.toLocaleString(\"en-US\", {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            }), \" \", \"E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          minWidth: 180,\n          background: \"#fff\",\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n          size: 28,\n          style: {\n            color: \"#6b7b58\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: 0,\n              fontSize: \"1.1em\",\n              fontWeight: 700\n            },\n            children: \"Total Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              fontWeight: 600,\n              fontSize: \"1.1em\"\n            },\n            children: [totalSales.toLocaleString(\"en-US\", {\n              minimumFractionDigits: 2,\n              maximumFractionDigits: 2\n            }), \" \", \"E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      p: 2,\n      sx: {\n        background: \"#fff\",\n        borderRadius: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        mb: 2,\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: 700,\n          fontFamily: \"Montserrat\",\n          sx: {\n            flex: 1\n          },\n          children: \"Sales Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this), chartPeriods.map(period => /*#__PURE__*/_jsxDEV(Button, {\n          variant: chartPeriod === period.value ? \"outlined\" : \"text\",\n          size: \"small\",\n          sx: {\n            borderColor: \"#2d2d2d\",\n            color: chartPeriod === period.value ? \"#2d2d2d\" : \"#888\",\n            fontWeight: 600,\n            borderRadius: 2,\n            minWidth: 70,\n            px: 1.5,\n            background: chartPeriod === period.value ? \"#f5f5f5\" : \"transparent\",\n            boxShadow: \"none\",\n            textTransform: \"none\",\n            \"&:hover\": {\n              background: \"none\",\n              color: chartPeriod === period.value ? \"#2d2d2d\" : \"#888\"\n            }\n          },\n          onClick: () => {\n            setChartPeriod(period.value);\n            setSelectedMonth(\"\");\n          },\n          children: period.label\n        }, period.value, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          sx: {\n            minWidth: 140\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Brand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: chartBrand,\n            label: \"Brand\",\n            onChange: e => setChartBrand(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"All Brands\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this), brands.map(brand => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: brand,\n              children: brand\n            }, brand, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this), chartPeriod === \"month\" && availableMonths.length > 0 && /*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          sx: {\n            minWidth: 140\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Month\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedMonth,\n            label: \"Month\",\n            onChange: e => setSelectedMonth(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"All Months\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this), availableMonths.map(m => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: m.value,\n              children: m.label\n            }, m.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"From\",\n          type: \"date\",\n          size: \"small\",\n          InputLabelProps: {\n            shrink: true\n          },\n          value: chartDateFrom,\n          onChange: e => setChartDateFrom(e.target.value),\n          sx: {\n            minWidth: 120\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"To\",\n          type: \"date\",\n          size: \"small\",\n          InputLabelProps: {\n            shrink: true\n          },\n          value: chartDateTo,\n          onChange: e => setChartDateTo(e.target.value),\n          sx: {\n            minWidth: 120\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n        width: \"100%\",\n        height: 300,\n        children: /*#__PURE__*/_jsxDEV(LineChart, {\n          data: displayedChartData,\n          margin: {\n            top: 10,\n            right: 30,\n            left: 0,\n            bottom: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n            strokeDasharray: \"3 3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n            dataKey: \"label\",\n            style: {\n              fontFamily: \"Montserrat\"\n            },\n            tickFormatter: label => getXAxisLabel(label, chartPeriod)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n            style: {\n              fontFamily: \"Montserrat\"\n            },\n            tickFormatter: value => value.toLocaleString(\"en-US\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            formatter: value => formatMoney(value),\n            labelFormatter: label => getXAxisLabel(label, chartPeriod)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Line, {\n            type: \"monotone\",\n            dataKey: \"total\",\n            name: \"Total Sales\",\n            stroke: \"#2d2d2d\",\n            strokeWidth: 2,\n            dot: {\n              r: 3\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      p: 2,\n      sx: {\n        background: \"#fff\",\n        borderRadius: 2,\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        flexWrap: \"wrap\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormControl, {\n        sx: {\n          minWidth: 180\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Brand\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: calcBrand,\n          label: \"Brand\",\n          onChange: e => setCalcBrand(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"\",\n            children: \"All Brands\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), brands.map(brand => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: brand,\n            children: brand\n          }, brand, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        sx: {\n          minWidth: 180\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Calculation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: calcType,\n          label: \"Calculation\",\n          onChange: e => setCalcType(e.target.value),\n          children: calculatorOptions.map(opt => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: opt.value,\n            children: opt.label\n          }, opt.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        style: {\n          backgroundColor: \"#2d2d2d\",\n          color: \"white\"\n        },\n        onClick: handleCalculate,\n        children: \"Calculate\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        style: {\n          marginLeft: 8\n        },\n        onClick: handleExport,\n        disabled: calcBrand === null,\n        children: \"Export CSV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 9\n      }, this), calcResult && /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          ml: 2,\n          fontWeight: 600,\n          color: \"#2d2d2d\"\n        },\n        children: calcResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      p: 2,\n      sx: {\n        background: \"#fff\",\n        borderRadius: 2,\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        flexWrap: \"wrap\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormControl, {\n        sx: {\n          minWidth: 180\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Brand\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: brandFilter,\n          label: \"Brand\",\n          onChange: e => setBrandFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"\",\n            children: \"All Brands\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this), brands.map(brand => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: brand,\n            children: brand\n          }, brand, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"From\",\n        type: \"date\",\n        InputLabelProps: {\n          shrink: true\n        },\n        value: dateFrom,\n        onChange: e => setDateFrom(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"To\",\n        type: \"date\",\n        InputLabelProps: {\n          shrink: true\n        },\n        value: dateTo,\n        onChange: e => setDateTo(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        sx: {\n          minWidth: 180\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Sort By\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: sortBy,\n          label: \"Sort By\",\n          onChange: e => setSortBy(e.target.value),\n          children: sortOptions.map(opt => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: opt.value,\n            children: opt.label\n          }, opt.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        sx: {\n          minWidth: 140\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: sortOrder,\n          label: \"Order\",\n          onChange: e => setSortOrder(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"desc\",\n            children: \"High to Low\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"asc\",\n            children: \"Low to High\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"newest\",\n            children: \"Newest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"oldest\",\n            children: \"Oldest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        style: {\n          backgroundColor: \"#2d2d2d\",\n          color: \"white\",\n          marginLeft: \"10px\"\n        },\n        onClick: () => {\n          setBrandFilter(\"\");\n          setDateFrom(\"\");\n          setDateTo(\"\");\n          setSortBy(\"date\");\n          setSortOrder(\"desc\");\n        },\n        children: \"Reset Filters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: columns.map(col => /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 700,\n                fontFamily: \"Montserrat\"\n              },\n              children: [col.label, sortBy === col.id && /*#__PURE__*/_jsxDEV(TableSortLabel, {\n                active: true,\n                direction: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 21\n              }, this)]\n            }, col.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: [currentRows.map(log => {\n            var _log$orderId2, _log$brandId6;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              style: {\n                cursor: \"pointer\"\n              },\n              onClick: () => setSelectedLog(log),\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_log$orderId2 = log.orderId) === null || _log$orderId2 === void 0 ? void 0 : _log$orderId2._id) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_log$brandId6 = log.brandId) === null || _log$brandId6 === void 0 ? void 0 : _log$brandId6.brandName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.total\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.vat\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.shippingFee\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.paymobFee\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.commission\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.brandPayout\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(MoneyCell, {\n                  value: log.netAdminProfit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: log.date ? new Date(log.date).toLocaleDateString() : \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 17\n              }, this)]\n            }, log._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 15\n            }, this);\n          }), currentRows.length === 0 && /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(TableCell, {\n              colSpan: columns.length,\n              align: \"center\",\n              children: \"No data found.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 881,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"pagination\",\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        mt: 2,\n        gap: 1\n      },\n      children: Array.from({\n        length: totalPages\n      }, (_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: currentPage === index + 1 ? \"active\" : \"\",\n        style: {\n          border: \"1px solid #2d2d2d\",\n          background: currentPage === index + 1 ? \"#2d2d2d\" : \"#fff\",\n          color: currentPage === index + 1 ? \"#fff\" : \"#2d2d2d\",\n          borderRadius: 4,\n          padding: \"4px 12px\",\n          fontWeight: 600,\n          cursor: \"pointer\",\n          margin: \"0 2px\",\n          outline: \"none\",\n          minWidth: 32\n        },\n        onClick: () => setCurrentPage(index + 1),\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 952,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: !!selectedLog,\n      onClose: () => setSelectedLog(null),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"space-between\"\n        },\n        children: [\"Row Details\", /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setSelectedLog(null),\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: selectedLog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            fontFamily: \"Montserrat\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Order ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 17\n            }, this), \" \", ((_selectedLog$orderId = selectedLog.orderId) === null || _selectedLog$orderId === void 0 ? void 0 : _selectedLog$orderId._id) || \"N/A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Brand:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 17\n            }, this), \" \", ((_selectedLog$brandId = selectedLog.brandId) === null || _selectedLog$brandId === void 0 ? void 0 : _selectedLog$brandId.brandName) || \"N/A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total (EGP):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.total)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"VAT:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.vat)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1012,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Shipping:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.shippingFee)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Paymob Fee:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.paymobFee)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Commission:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1024,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.commission)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1023,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Brand Payout:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.brandPayout)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Admin Profit:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 17\n            }, this), \" \", formatMoney(selectedLog.netAdminProfit)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1031,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 17\n            }, this), \" \", selectedLog.date ? new Date(selectedLog.date).toLocaleDateString() : \"N/A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setSelectedLog(null),\n          color: \"primary\",\n          variant: \"contained\",\n          sx: {\n            background: \"#2d2d2d\"\n          },\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 974,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 462,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountingAdmin, \"CCZVlB8gIp81E/I1cSDjkRj3KnE=\");\n_c2 = AccountingAdmin;\nexport default AccountingAdmin;\nvar _c, _c2;\n$RefreshReg$(_c, \"MoneyCell\");\n$RefreshReg$(_c2, \"AccountingAdmin\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useMemo", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Select", "MenuItem", "FormControl", "InputLabel", "TextField", "<PERSON><PERSON>", "CircularProgress", "Typography", "TableSortLabel", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "Cartesian<PERSON><PERSON>", "Legend", "axios", "format", "FaMoneyBillWave", "FaPiggyBank", "FaCreditCard", "FaHandHoldingUsd", "FaChartBar", "CloseIcon", "jsxDEV", "_jsxDEV", "columns", "id", "label", "money", "sortOptions", "value", "formatMoney", "undefined", "Number", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "MoneyCell", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "alignItems", "gap", "_c", "calculatorOptions", "exportToCSV", "data", "brandName", "calcType", "length", "headers", "wrap", "val", "rows", "map", "log", "_log$orderId", "_log$brandId", "_log$total", "_log$vat", "_log$shippingFee", "_log$paymobFee", "_log$commission", "_log$brandPayout", "_log$netAdminProfit", "orderId", "_id", "brandId", "total", "vat", "shippingFee", "paymobFee", "commission", "brandPayout", "netAdminProfit", "date", "Date", "toLocaleDateString", "sum", "key", "reduce", "acc", "totalsRow", "idx", "includes", "colIdx", "push", "slice", "csv<PERSON><PERSON>nt", "join", "for<PERSON>ach", "row", "blob", "Blob", "type", "link", "document", "createElement", "href", "URL", "createObjectURL", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "chartPeriods", "groupLogsByPeriod", "logs", "period", "result", "year", "getFullYear", "first<PERSON>an", "week", "Math", "ceil", "getDay", "String", "getMonth", "padStart", "Object", "entries", "sort", "a", "b", "localeCompare", "toFixed", "getMonthYearLabel", "test", "month", "split", "getWeekLabel", "getXAxisLabel", "getAvailableMonths", "d", "ROWS_PER_PAGE", "AccountingAdmin", "_s", "_selectedLog$orderId", "_selectedLog$brandId", "setLogs", "loading", "setLoading", "error", "setError", "brandFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dateFrom", "setDateFrom", "dateTo", "setDateTo", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "calcBrand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCalcType", "calcResult", "setCalcResult", "chartPeriod", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chartBrand", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chartDateFrom", "setChartDateFrom", "chartDateTo", "setChartDateTo", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "currentPage", "setCurrentPage", "<PERSON><PERSON><PERSON>", "setSelectedLog", "get", "then", "res", "catch", "err", "brands", "brandSet", "Set", "add", "Array", "from", "filteredLogs", "filter", "_log$brandId2", "sortedLogs", "sorted", "aValue", "bValue", "_a$sortBy", "_b$sortBy", "handleCalculate", "brandLogs", "_log$brandId3", "find", "opt", "handleExport", "_log$brandId4", "chartFilteredLogs", "_log$brandId5", "chartData", "displayedChartData", "availableMonths", "m", "totalPages", "indexOfLastRow", "indexOfFirstRow", "currentRows", "totalVat", "totalProfit", "totalPaymob", "totalBrandPayout", "totalSales", "justifyContent", "minHeight", "color", "p", "sx", "flexDirection", "mb", "flexWrap", "flex", "min<PERSON><PERSON><PERSON>", "background", "borderRadius", "boxShadow", "size", "margin", "fontSize", "fontWeight", "variant", "fontFamily", "borderColor", "px", "textTransform", "onClick", "onChange", "e", "target", "brand", "InputLabelProps", "shrink", "width", "height", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "tick<PERSON><PERSON><PERSON><PERSON>", "formatter", "labelFormatter", "name", "stroke", "strokeWidth", "dot", "r", "backgroundColor", "marginLeft", "disabled", "ml", "component", "col", "active", "direction", "_log$orderId2", "_log$brandId6", "cursor", "colSpan", "align", "className", "mt", "_", "index", "border", "padding", "outline", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "_c2", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/accountingAdmin.jsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo } from \"react\";\r\nimport {\r\n  Box,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Paper,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  TextField,\r\n  Button,\r\n  CircularProgress,\r\n  Typography,\r\n  TableSortLabel,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport {\r\n  LineChart,\r\n  Line,\r\n  XAxis,\r\n  YAxis,\r\n  Tooltip,\r\n  ResponsiveContainer,\r\n  CartesianGrid,\r\n  Legend,\r\n} from \"recharts\";\r\nimport axios from \"axios\";\r\nimport { format } from \"date-fns\";\r\nimport {\r\n  FaMoneyBillWave,\r\n  FaPiggyBank,\r\n  FaCreditCard,\r\n  FaHandHoldingUsd,\r\n  FaChartBar,\r\n} from \"react-icons/fa\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n\r\nconst columns = [\r\n  { id: \"orderId\", label: \"Order ID\" },\r\n  { id: \"brand\", label: \"Brand\" },\r\n  { id: \"total\", label: \"Total (EGP)\", money: true },\r\n  { id: \"vat\", label: \"VAT\", money: true },\r\n  { id: \"shippingFee\", label: \"Shipping\", money: true },\r\n  { id: \"paymobFee\", label: \"Paymob Fee\", money: true },\r\n  { id: \"commission\", label: \"Commission\", money: true },\r\n  { id: \"brandPayout\", label: \"Brand Payout\", money: true },\r\n  { id: \"netAdminProfit\", label: \"Admin Profit\", money: true },\r\n  { id: \"date\", label: \"Date\" },\r\n];\r\n\r\nconst sortOptions = [\r\n  { value: \"total\", label: \"Total (EGP)\" },\r\n  { value: \"netAdminProfit\", label: \"Admin Profit\" },\r\n  { value: \"brandPayout\", label: \"Brand Payout\" },\r\n  { value: \"date\", label: \"Date\" },\r\n];\r\n\r\nfunction formatMoney(value) {\r\n  if (value === null || value === undefined || value === \"N/A\") return \"N/A\";\r\n  return `${Number(value).toLocaleString(\"en-US\", {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2,\r\n  })} E£`;\r\n}\r\n\r\nconst MoneyCell = ({ value }) =>\r\n  value === \"N/A\" ? (\r\n    <span>N/A</span>\r\n  ) : (\r\n    <span style={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n      {formatMoney(value)}\r\n    </span>\r\n  );\r\n\r\nconst calculatorOptions = [\r\n  { value: \"brandPayout\", label: \"Brand Payout\" },\r\n  { value: \"total\", label: \"Total Sales\" },\r\n  { value: \"commission\", label: \"Commission\" },\r\n  { value: \"netAdminProfit\", label: \"Profit Admin\" },\r\n];\r\n\r\nfunction exportToCSV(data, brandName, calcType = null) {\r\n  if (!data.length) return;\r\n  const headers = [\r\n    \"Order ID\",\r\n    \"Brand\",\r\n    \"Total (EGP)\",\r\n    \"VAT\",\r\n    \"Shipping\",\r\n    \"Paymob Fee\",\r\n    \"Commission\",\r\n    \"Brand Payout\",\r\n    \"Admin Profit\",\r\n    \"Date\",\r\n  ];\r\n  const wrap = (val) => `\"${val}\"`;\r\n  const rows = data.map((log) => [\r\n    wrap(log.orderId?._id || \"N/A\"),\r\n    wrap(log.brandId?.brandName || \"N/A\"),\r\n    wrap(log.total ?? \"N/A\"),\r\n    wrap(log.vat ?? \"N/A\"),\r\n    wrap(log.shippingFee ?? \"N/A\"),\r\n    wrap(log.paymobFee ?? \"N/A\"),\r\n    wrap(log.commission ?? \"N/A\"),\r\n    wrap(log.brandPayout ?? \"N/A\"),\r\n    wrap(log.netAdminProfit ?? \"N/A\"),\r\n    wrap(log.date ? new Date(log.date).toLocaleDateString() : \"N/A\"),\r\n  ]);\r\n\r\n  // Calculate totals for each money column\r\n  const sum = (key) =>\r\n    data.reduce((acc, log) => acc + (Number(log[key]) || 0), 0);\r\n  let totalsRow = [\r\n    \"\", // Order ID\r\n    \"TOTAL\", // Brand\r\n    sum(\"total\"),\r\n    sum(\"vat\"),\r\n    sum(\"shippingFee\"),\r\n    sum(\"paymobFee\"),\r\n    sum(\"commission\"),\r\n    sum(\"brandPayout\"),\r\n    sum(\"netAdminProfit\"),\r\n    \"\", // Date\r\n  ].map((val, idx) => {\r\n    // Format money columns\r\n    if ([2, 3, 4, 5, 6, 7, 8].includes(idx) && typeof val === \"number\") {\r\n      return val.toLocaleString(\"en-US\", {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2,\r\n      });\r\n    }\r\n    return val;\r\n  });\r\n\r\n  // If a calculation type is provided, add a label to the totals row for that column\r\n  if (calcType) {\r\n    const colIdx = {\r\n      total: 2,\r\n      vat: 3,\r\n      shippingFee: 4,\r\n      paymobFee: 5,\r\n      commission: 6,\r\n      brandPayout: 7,\r\n      netAdminProfit: 8,\r\n    }[calcType];\r\n    if (colIdx !== undefined) {\r\n      totalsRow[colIdx] = `SUM: ${totalsRow[colIdx]}`;\r\n    }\r\n  }\r\n\r\n  // Ensure totalsRow matches header length and wrap all values\r\n  totalsRow = totalsRow.map(wrap);\r\n  while (totalsRow.length < headers.length) totalsRow.push(wrap(\"\"));\r\n  while (totalsRow.length > headers.length)\r\n    totalsRow = totalsRow.slice(0, headers.length);\r\n\r\n  let csvContent = headers.map(wrap).join(\",\") + \"\\n\";\r\n  rows.forEach((row) => {\r\n    csvContent += row.join(\",\") + \"\\n\";\r\n  });\r\n  csvContent += totalsRow.join(\",\") + \"\\n\";\r\n\r\n  const blob = new Blob([csvContent], { type: \"text/csv;charset=utf-8;\" });\r\n  const link = document.createElement(\"a\");\r\n  link.href = URL.createObjectURL(blob);\r\n  link.setAttribute(\"download\", `${brandName || \"all_brands\"}_financials.csv`);\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n}\r\n\r\nconst chartPeriods = [\r\n  { value: \"week\", label: \"Weeks\" },\r\n  { value: \"month\", label: \"Months\" },\r\n  { value: \"year\", label: \"Years\" },\r\n];\r\n\r\nfunction groupLogsByPeriod(logs, period) {\r\n  // Returns [{ label, total }]\r\n  if (!logs.length) return [];\r\n  const result = {};\r\n  logs.forEach((log) => {\r\n    if (!log.date) return;\r\n    const date = new Date(log.date);\r\n    let label = \"\";\r\n    if (period === \"week\") {\r\n      // Week of year: YYYY-WW\r\n      const year = date.getFullYear();\r\n      const firstJan = new Date(date.getFullYear(), 0, 1);\r\n      const week = Math.ceil(\r\n        ((date - firstJan) / 86400000 + firstJan.getDay() + 1) / 7\r\n      );\r\n      label = `${year}-W${week}`;\r\n    } else if (period === \"month\") {\r\n      label = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\r\n        2,\r\n        \"0\"\r\n      )}`;\r\n    } else if (period === \"year\") {\r\n      label = `${date.getFullYear()}`;\r\n    }\r\n    if (!result[label]) result[label] = 0;\r\n    result[label] += Number(log.total) || 0;\r\n  });\r\n  // Sort labels\r\n  return Object.entries(result)\r\n    .sort(([a], [b]) => a.localeCompare(b))\r\n    .map(([label, total]) => ({ label, total: Number(total.toFixed(2)) }));\r\n}\r\n\r\nfunction getMonthYearLabel(label) {\r\n  // Only parse if label matches YYYY-MM\r\n  if (!label || !/^[0-9]{4}-[0-9]{2}$/.test(label)) return label;\r\n  const [year, month] = label.split(\"-\");\r\n  const date = new Date(Number(year), Number(month) - 1, 1);\r\n  return format(date, \"MMM yyyy\");\r\n}\r\n\r\nfunction getWeekLabel(label) {\r\n  // label: '2025-W23' => 'W23 2025'\r\n  if (!label) return \"\";\r\n  const [year, week] = label.split(\"-W\");\r\n  if (!year || !week) return label;\r\n  return `W${week} ${year}`;\r\n}\r\n\r\nfunction getXAxisLabel(label, period) {\r\n  if (period === \"month\") return getMonthYearLabel(label);\r\n  if (period === \"week\") return getWeekLabel(label);\r\n  return label;\r\n}\r\n\r\nfunction getAvailableMonths(data) {\r\n  // Returns array of { value: '2025-06', label: 'Jun 2025' }\r\n  return data.map((d) => ({\r\n    value: d.label,\r\n    label: getMonthYearLabel(d.label),\r\n  }));\r\n}\r\n\r\nconst ROWS_PER_PAGE = 12;\r\n\r\nconst AccountingAdmin = () => {\r\n  const [logs, setLogs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [brandFilter, setBrandFilter] = useState(\"\");\r\n  const [dateFrom, setDateFrom] = useState(\"\");\r\n  const [dateTo, setDateTo] = useState(\"\");\r\n  const [sortBy, setSortBy] = useState(\"date\");\r\n  const [sortOrder, setSortOrder] = useState(\"desc\");\r\n\r\n  // Calculator states\r\n  const [calcBrand, setCalcBrand] = useState(\"\");\r\n  const [calcType, setCalcType] = useState(\"brandPayout\");\r\n  const [calcResult, setCalcResult] = useState(null);\r\n\r\n  // Chart states\r\n  const [chartPeriod, setChartPeriod] = useState(\"month\");\r\n  const [chartBrand, setChartBrand] = useState(\"\");\r\n  const [chartDateFrom, setChartDateFrom] = useState(\"\");\r\n  const [chartDateTo, setChartDateTo] = useState(\"\");\r\n  const [selectedMonth, setSelectedMonth] = useState(\"\");\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedLog, setSelectedLog] = useState(null);\r\n\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios\r\n      .get(\"https://api.thedesigngrit.com/api/admin-financials/getall-logs\")\r\n      .then((res) => {\r\n        setLogs(res.data || []);\r\n        setLoading(false);\r\n      })\r\n      .catch((err) => {\r\n        setError(\"Failed to fetch data\");\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  // Extract unique brands for filter dropdown\r\n  const brands = useMemo(() => {\r\n    const brandSet = new Set();\r\n    logs.forEach((log) => {\r\n      if (log.brandId && log.brandId.brandName) {\r\n        brandSet.add(log.brandId.brandName);\r\n      }\r\n    });\r\n    return Array.from(brandSet);\r\n  }, [logs]);\r\n\r\n  // Filtering\r\n  const filteredLogs = useMemo(() => {\r\n    return logs.filter((log) => {\r\n      // Brand filter\r\n      if (brandFilter && log.brandId?.brandName !== brandFilter) return false;\r\n      // Date filter\r\n      if (dateFrom && new Date(log.date) < new Date(dateFrom)) return false;\r\n      if (dateTo && new Date(log.date) > new Date(dateTo)) return false;\r\n      return true;\r\n    });\r\n  }, [logs, brandFilter, dateFrom, dateTo]);\r\n\r\n  // Sorting\r\n  const sortedLogs = useMemo(() => {\r\n    const sorted = [...filteredLogs];\r\n    sorted.sort((a, b) => {\r\n      let aValue, bValue;\r\n      if (sortBy === \"date\") {\r\n        aValue = new Date(a.date);\r\n        bValue = new Date(b.date);\r\n      } else {\r\n        aValue = a[sortBy] ?? 0;\r\n        bValue = b[sortBy] ?? 0;\r\n      }\r\n      if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\r\n      if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\r\n      return 0;\r\n    });\r\n    return sorted;\r\n  }, [filteredLogs, sortBy, sortOrder]);\r\n\r\n  // Calculator logic\r\n  const handleCalculate = () => {\r\n    // If calcBrand is empty string, calculate for all brands\r\n    const brandLogs = !calcBrand\r\n      ? logs\r\n      : logs.filter((log) => log.brandId?.brandName === calcBrand);\r\n    if (!brandLogs.length) {\r\n      setCalcResult(\"No data for this brand.\");\r\n      return;\r\n    }\r\n    const sum = brandLogs.reduce(\r\n      (acc, log) => acc + (Number(log[calcType]) || 0),\r\n      0\r\n    );\r\n    setCalcResult(\r\n      `${calculatorOptions.find((opt) => opt.value === calcType).label} for ${\r\n        calcBrand || \"All Brands\"\r\n      }: ${formatMoney(sum)}`\r\n    );\r\n  };\r\n\r\n  // Calculator export logic\r\n  const handleExport = () => {\r\n    const brandLogs = !calcBrand\r\n      ? logs\r\n      : logs.filter((log) => log.brandId?.brandName === calcBrand);\r\n    exportToCSV(\r\n      brandLogs,\r\n      calcBrand || \"all_brands\",\r\n      calcBrand && calcType ? calcType : null\r\n    );\r\n  };\r\n\r\n  // Chart filtered logs\r\n  const chartFilteredLogs = useMemo(() => {\r\n    return logs.filter((log) => {\r\n      if (chartBrand && log.brandId?.brandName !== chartBrand) return false;\r\n      if (chartDateFrom && new Date(log.date) < new Date(chartDateFrom))\r\n        return false;\r\n      if (chartDateTo && new Date(log.date) > new Date(chartDateTo))\r\n        return false;\r\n      return true;\r\n    });\r\n  }, [logs, chartBrand, chartDateFrom, chartDateTo]);\r\n\r\n  const chartData = useMemo(\r\n    () => groupLogsByPeriod(chartFilteredLogs, chartPeriod),\r\n    [chartFilteredLogs, chartPeriod]\r\n  );\r\n\r\n  // Filter chartData by selectedMonth if in months mode\r\n  const displayedChartData = useMemo(() => {\r\n    if (chartPeriod === \"month\" && selectedMonth) {\r\n      return chartData.filter((d) => d.label === selectedMonth);\r\n    }\r\n    return chartData;\r\n  }, [chartData, chartPeriod, selectedMonth]);\r\n\r\n  // Available months for select menu\r\n  const availableMonths = useMemo(() => {\r\n    if (chartPeriod !== \"month\") return [];\r\n    return getAvailableMonths(chartData).filter((m) =>\r\n      /^[0-9]{4}-[0-9]{2}$/.test(m.value)\r\n    );\r\n  }, [chartData, chartPeriod]);\r\n\r\n  // Pagination logic\r\n  const totalPages = Math.ceil(sortedLogs.length / ROWS_PER_PAGE);\r\n  const indexOfLastRow = currentPage * ROWS_PER_PAGE;\r\n  const indexOfFirstRow = indexOfLastRow - ROWS_PER_PAGE;\r\n  const currentRows = sortedLogs.slice(indexOfFirstRow, indexOfLastRow);\r\n\r\n  // Reset to first page if filters change and current page is out of range\r\n  useEffect(() => {\r\n    if (currentPage > totalPages) setCurrentPage(1);\r\n  }, [totalPages, currentPage]);\r\n\r\n  // Totals from filtered logs (sortedLogs)\r\n  const totalVat = useMemo(\r\n    () => sortedLogs.reduce((acc, log) => acc + (Number(log.vat) || 0), 0),\r\n    [sortedLogs]\r\n  );\r\n  const totalProfit = useMemo(\r\n    () =>\r\n      sortedLogs.reduce(\r\n        (acc, log) => acc + (Number(log.netAdminProfit) || 0),\r\n        0\r\n      ),\r\n    [sortedLogs]\r\n  );\r\n  const totalPaymob = useMemo(\r\n    () =>\r\n      sortedLogs.reduce((acc, log) => acc + (Number(log.paymobFee) || 0), 0),\r\n    [sortedLogs]\r\n  );\r\n  const totalBrandPayout = useMemo(\r\n    () =>\r\n      sortedLogs.reduce((acc, log) => acc + (Number(log.brandPayout) || 0), 0),\r\n    [sortedLogs]\r\n  );\r\n  const totalSales = useMemo(\r\n    () => sortedLogs.reduce((acc, log) => acc + (Number(log.total) || 0), 0),\r\n    [sortedLogs]\r\n  );\r\n\r\n  if (loading)\r\n    return (\r\n      <Box\r\n        display=\"flex\"\r\n        justifyContent=\"center\"\r\n        alignItems=\"center\"\r\n        minHeight=\"60vh\"\r\n      >\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  if (error)\r\n    return (\r\n      <Box\r\n        display=\"flex\"\r\n        justifyContent=\"center\"\r\n        alignItems=\"center\"\r\n        minHeight=\"60vh\"\r\n      >\r\n        <Typography color=\"error\">{error}</Typography>\r\n      </Box>\r\n    );\r\n\r\n  return (\r\n    <Box p={3}>\r\n      {/* Summary Cards Section */}\r\n      <Box\r\n        sx={{\r\n          display: \"flex\",\r\n          flexDirection: \"row\",\r\n          gap: 3,\r\n          mb: 4,\r\n          flexWrap: \"wrap\",\r\n        }}\r\n      >\r\n        <Box\r\n          sx={{\r\n            flex: 1,\r\n            minWidth: 180,\r\n            background: \"#fff\",\r\n            borderRadius: 2,\r\n            boxShadow: 1,\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <FaMoneyBillWave size={28} style={{ color: \"#6b7b58\" }} />\r\n          <Box>\r\n            <h4 style={{ margin: 0, fontSize: \"1.1em\", fontWeight: 700 }}>\r\n              Total Taxes\r\n            </h4>\r\n            <p style={{ margin: 0, fontWeight: 600, fontSize: \"1.1em\" }}>\r\n              {totalVat.toLocaleString(\"en-US\", {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 2,\r\n              })}{\" \"}\r\n              E£\r\n            </p>\r\n          </Box>\r\n        </Box>\r\n        <Box\r\n          sx={{\r\n            flex: 1,\r\n            minWidth: 180,\r\n            background: \"#fff\",\r\n            borderRadius: 2,\r\n            boxShadow: 1,\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <FaPiggyBank size={28} style={{ color: \"#6b7b58\" }} />\r\n          <Box>\r\n            <h4 style={{ margin: 0, fontSize: \"1.1em\", fontWeight: 700 }}>\r\n              Total Profit\r\n            </h4>\r\n            <p style={{ margin: 0, fontWeight: 600, fontSize: \"1.1em\" }}>\r\n              {totalProfit.toLocaleString(\"en-US\", {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 2,\r\n              })}{\" \"}\r\n              E£\r\n            </p>\r\n          </Box>\r\n        </Box>\r\n        <Box\r\n          sx={{\r\n            flex: 1,\r\n            minWidth: 180,\r\n            background: \"#fff\",\r\n            borderRadius: 2,\r\n            boxShadow: 1,\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <FaCreditCard size={28} style={{ color: \"#6b7b58\" }} />\r\n          <Box>\r\n            <h4 style={{ margin: 0, fontSize: \"1.1em\", fontWeight: 700 }}>\r\n              Total Paymob Fees\r\n            </h4>\r\n            <p style={{ margin: 0, fontWeight: 600, fontSize: \"1.1em\" }}>\r\n              {totalPaymob.toLocaleString(\"en-US\", {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 2,\r\n              })}{\" \"}\r\n              E£\r\n            </p>\r\n          </Box>\r\n        </Box>\r\n        <Box\r\n          sx={{\r\n            flex: 1,\r\n            minWidth: 180,\r\n            background: \"#fff\",\r\n            borderRadius: 2,\r\n            boxShadow: 1,\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <FaHandHoldingUsd size={28} style={{ color: \"#6b7b58\" }} />\r\n          <Box>\r\n            <h4 style={{ margin: 0, fontSize: \"1.1em\", fontWeight: 700 }}>\r\n              Total Brand Payout\r\n            </h4>\r\n            <p style={{ margin: 0, fontWeight: 600, fontSize: \"1.1em\" }}>\r\n              {totalBrandPayout.toLocaleString(\"en-US\", {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 2,\r\n              })}{\" \"}\r\n              E£\r\n            </p>\r\n          </Box>\r\n        </Box>\r\n        <Box\r\n          sx={{\r\n            flex: 1,\r\n            minWidth: 180,\r\n            background: \"#fff\",\r\n            borderRadius: 2,\r\n            boxShadow: 1,\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <FaChartBar size={28} style={{ color: \"#6b7b58\" }} />\r\n          <Box>\r\n            <h4 style={{ margin: 0, fontSize: \"1.1em\", fontWeight: 700 }}>\r\n              Total Sales\r\n            </h4>\r\n            <p style={{ margin: 0, fontWeight: 600, fontSize: \"1.1em\" }}>\r\n              {totalSales.toLocaleString(\"en-US\", {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 2,\r\n              })}{\" \"}\r\n              E£\r\n            </p>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n      {/* Chart Section */}\r\n      <Box mb={4} p={2} sx={{ background: \"#fff\", borderRadius: 3 }}>\r\n        <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2} flexWrap=\"wrap\">\r\n          <Typography\r\n            variant=\"h6\"\r\n            fontWeight={700}\r\n            fontFamily=\"Montserrat\"\r\n            sx={{ flex: 1 }}\r\n          >\r\n            Sales Overview\r\n          </Typography>\r\n          {chartPeriods.map((period) => (\r\n            <Button\r\n              key={period.value}\r\n              variant={chartPeriod === period.value ? \"outlined\" : \"text\"}\r\n              size=\"small\"\r\n              sx={{\r\n                borderColor: \"#2d2d2d\",\r\n                color: chartPeriod === period.value ? \"#2d2d2d\" : \"#888\",\r\n                fontWeight: 600,\r\n                borderRadius: 2,\r\n                minWidth: 70,\r\n                px: 1.5,\r\n                background:\r\n                  chartPeriod === period.value ? \"#f5f5f5\" : \"transparent\",\r\n                boxShadow: \"none\",\r\n                textTransform: \"none\",\r\n                \"&:hover\": {\r\n                  background: \"none\",\r\n                  color: chartPeriod === period.value ? \"#2d2d2d\" : \"#888\",\r\n                },\r\n              }}\r\n              onClick={() => {\r\n                setChartPeriod(period.value);\r\n                setSelectedMonth(\"\");\r\n              }}\r\n            >\r\n              {period.label}\r\n            </Button>\r\n          ))}\r\n          <FormControl size=\"small\" sx={{ minWidth: 140 }}>\r\n            <InputLabel>Brand</InputLabel>\r\n            <Select\r\n              value={chartBrand}\r\n              label=\"Brand\"\r\n              onChange={(e) => setChartBrand(e.target.value)}\r\n            >\r\n              <MenuItem value=\"\">All Brands</MenuItem>\r\n              {brands.map((brand) => (\r\n                <MenuItem key={brand} value={brand}>\r\n                  {brand}\r\n                </MenuItem>\r\n              ))}\r\n            </Select>\r\n          </FormControl>\r\n          {chartPeriod === \"month\" && availableMonths.length > 0 && (\r\n            <FormControl size=\"small\" sx={{ minWidth: 140 }}>\r\n              <InputLabel>Month</InputLabel>\r\n              <Select\r\n                value={selectedMonth}\r\n                label=\"Month\"\r\n                onChange={(e) => setSelectedMonth(e.target.value)}\r\n              >\r\n                <MenuItem value=\"\">All Months</MenuItem>\r\n                {availableMonths.map((m) => (\r\n                  <MenuItem key={m.value} value={m.value}>\r\n                    {m.label}\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n          )}\r\n          <TextField\r\n            label=\"From\"\r\n            type=\"date\"\r\n            size=\"small\"\r\n            InputLabelProps={{ shrink: true }}\r\n            value={chartDateFrom}\r\n            onChange={(e) => setChartDateFrom(e.target.value)}\r\n            sx={{ minWidth: 120 }}\r\n          />\r\n          <TextField\r\n            label=\"To\"\r\n            type=\"date\"\r\n            size=\"small\"\r\n            InputLabelProps={{ shrink: true }}\r\n            value={chartDateTo}\r\n            onChange={(e) => setChartDateTo(e.target.value)}\r\n            sx={{ minWidth: 120 }}\r\n          />\r\n        </Box>\r\n        <ResponsiveContainer width=\"100%\" height={300}>\r\n          <LineChart\r\n            data={displayedChartData}\r\n            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}\r\n          >\r\n            <CartesianGrid strokeDasharray=\"3 3\" />\r\n            <XAxis\r\n              dataKey=\"label\"\r\n              style={{ fontFamily: \"Montserrat\" }}\r\n              tickFormatter={(label) => getXAxisLabel(label, chartPeriod)}\r\n            />\r\n            <YAxis\r\n              style={{ fontFamily: \"Montserrat\" }}\r\n              tickFormatter={(value) => value.toLocaleString(\"en-US\")}\r\n            />\r\n            <Tooltip\r\n              formatter={(value) => formatMoney(value)}\r\n              labelFormatter={(label) => getXAxisLabel(label, chartPeriod)}\r\n            />\r\n            <Legend />\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"total\"\r\n              name=\"Total Sales\"\r\n              stroke=\"#2d2d2d\"\r\n              strokeWidth={2}\r\n              dot={{ r: 3 }}\r\n            />\r\n          </LineChart>\r\n        </ResponsiveContainer>\r\n      </Box>\r\n      {/* Calculator Section */}\r\n      <Box\r\n        mb={4}\r\n        p={2}\r\n        sx={{\r\n          background: \"#fff\",\r\n          borderRadius: 2,\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          gap: 2,\r\n          flexWrap: \"wrap\",\r\n        }}\r\n      >\r\n        <FormControl sx={{ minWidth: 180 }}>\r\n          <InputLabel>Brand</InputLabel>\r\n          <Select\r\n            value={calcBrand}\r\n            label=\"Brand\"\r\n            onChange={(e) => setCalcBrand(e.target.value)}\r\n          >\r\n            <MenuItem value=\"\">All Brands</MenuItem>\r\n            {brands.map((brand) => (\r\n              <MenuItem key={brand} value={brand}>\r\n                {brand}\r\n              </MenuItem>\r\n            ))}\r\n          </Select>\r\n        </FormControl>\r\n        <FormControl sx={{ minWidth: 180 }}>\r\n          <InputLabel>Calculation</InputLabel>\r\n          <Select\r\n            value={calcType}\r\n            label=\"Calculation\"\r\n            onChange={(e) => setCalcType(e.target.value)}\r\n          >\r\n            {calculatorOptions.map((opt) => (\r\n              <MenuItem key={opt.value} value={opt.value}>\r\n                {opt.label}\r\n              </MenuItem>\r\n            ))}\r\n          </Select>\r\n        </FormControl>\r\n        <Button\r\n          variant=\"contained\"\r\n          style={{ backgroundColor: \"#2d2d2d\", color: \"white\" }}\r\n          onClick={handleCalculate}\r\n        >\r\n          Calculate\r\n        </Button>\r\n        <Button\r\n          variant=\"outlined\"\r\n          style={{ marginLeft: 8 }}\r\n          onClick={handleExport}\r\n          disabled={calcBrand === null}\r\n        >\r\n          Export CSV\r\n        </Button>\r\n        {calcResult && (\r\n          <Typography sx={{ ml: 2, fontWeight: 600, color: \"#2d2d2d\" }}>\r\n            {calcResult}\r\n          </Typography>\r\n        )}\r\n      </Box>\r\n      {/* Filters and Table Section */}\r\n      <Box\r\n        mb={4}\r\n        p={2}\r\n        sx={{\r\n          background: \"#fff\",\r\n          borderRadius: 2,\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          gap: 2,\r\n          flexWrap: \"wrap\",\r\n        }}\r\n      >\r\n        <FormControl sx={{ minWidth: 180 }}>\r\n          <InputLabel>Brand</InputLabel>\r\n          <Select\r\n            value={brandFilter}\r\n            label=\"Brand\"\r\n            onChange={(e) => setBrandFilter(e.target.value)}\r\n          >\r\n            <MenuItem value=\"\">All Brands</MenuItem>\r\n            {brands.map((brand) => (\r\n              <MenuItem key={brand} value={brand}>\r\n                {brand}\r\n              </MenuItem>\r\n            ))}\r\n          </Select>\r\n        </FormControl>\r\n        <TextField\r\n          label=\"From\"\r\n          type=\"date\"\r\n          InputLabelProps={{ shrink: true }}\r\n          value={dateFrom}\r\n          onChange={(e) => setDateFrom(e.target.value)}\r\n        />\r\n        <TextField\r\n          label=\"To\"\r\n          type=\"date\"\r\n          InputLabelProps={{ shrink: true }}\r\n          value={dateTo}\r\n          onChange={(e) => setDateTo(e.target.value)}\r\n        />\r\n        <FormControl sx={{ minWidth: 180 }}>\r\n          <InputLabel>Sort By</InputLabel>\r\n          <Select\r\n            value={sortBy}\r\n            label=\"Sort By\"\r\n            onChange={(e) => setSortBy(e.target.value)}\r\n          >\r\n            {sortOptions.map((opt) => (\r\n              <MenuItem key={opt.value} value={opt.value}>\r\n                {opt.label}\r\n              </MenuItem>\r\n            ))}\r\n          </Select>\r\n        </FormControl>\r\n        <FormControl sx={{ minWidth: 140 }}>\r\n          <InputLabel>Order</InputLabel>\r\n          <Select\r\n            value={sortOrder}\r\n            label=\"Order\"\r\n            onChange={(e) => setSortOrder(e.target.value)}\r\n          >\r\n            <MenuItem value=\"desc\">High to Low</MenuItem>\r\n            <MenuItem value=\"asc\">Low to High</MenuItem>\r\n            <MenuItem value=\"newest\">Newest</MenuItem>\r\n            <MenuItem value=\"oldest\">Oldest</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n        <Button\r\n          variant=\"contained\"\r\n          style={{\r\n            backgroundColor: \"#2d2d2d\",\r\n            color: \"white\",\r\n            marginLeft: \"10px\",\r\n          }}\r\n          onClick={() => {\r\n            setBrandFilter(\"\");\r\n            setDateFrom(\"\");\r\n            setDateTo(\"\");\r\n            setSortBy(\"date\");\r\n            setSortOrder(\"desc\");\r\n          }}\r\n        >\r\n          Reset Filters\r\n        </Button>\r\n      </Box>\r\n      <TableContainer component={Paper}>\r\n        <Table>\r\n          <TableHead>\r\n            <TableRow>\r\n              {columns.map((col) => (\r\n                <TableCell\r\n                  key={col.id}\r\n                  sx={{ fontWeight: 700, fontFamily: \"Montserrat\" }}\r\n                >\r\n                  {col.label}\r\n                  {sortBy === col.id && (\r\n                    <TableSortLabel\r\n                      active\r\n                      direction={sortOrder === \"asc\" ? \"asc\" : \"desc\"}\r\n                    />\r\n                  )}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {currentRows.map((log) => (\r\n              <TableRow\r\n                key={log._id}\r\n                style={{ cursor: \"pointer\" }}\r\n                onClick={() => setSelectedLog(log)}\r\n              >\r\n                <TableCell>{log.orderId?._id || \"N/A\"}</TableCell>\r\n                <TableCell>{log.brandId?.brandName || \"N/A\"}</TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.total} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.vat} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.shippingFee} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.paymobFee} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.commission} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.brandPayout} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  <MoneyCell value={log.netAdminProfit} />\r\n                </TableCell>\r\n                <TableCell>\r\n                  {log.date ? new Date(log.date).toLocaleDateString() : \"N/A\"}\r\n                </TableCell>\r\n              </TableRow>\r\n            ))}\r\n            {currentRows.length === 0 && (\r\n              <TableRow>\r\n                <TableCell colSpan={columns.length} align=\"center\">\r\n                  No data found.\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </TableContainer>\r\n      {/* Pagination */}\r\n      <Box\r\n        className=\"pagination\"\r\n        sx={{ display: \"flex\", justifyContent: \"center\", mt: 2, gap: 1 }}\r\n      >\r\n        {Array.from({ length: totalPages }, (_, index) => (\r\n          <button\r\n            key={index}\r\n            className={currentPage === index + 1 ? \"active\" : \"\"}\r\n            style={{\r\n              border: \"1px solid #2d2d2d\",\r\n              background: currentPage === index + 1 ? \"#2d2d2d\" : \"#fff\",\r\n              color: currentPage === index + 1 ? \"#fff\" : \"#2d2d2d\",\r\n              borderRadius: 4,\r\n              padding: \"4px 12px\",\r\n              fontWeight: 600,\r\n              cursor: \"pointer\",\r\n              margin: \"0 2px\",\r\n              outline: \"none\",\r\n              minWidth: 32,\r\n            }}\r\n            onClick={() => setCurrentPage(index + 1)}\r\n          >\r\n            {index + 1}\r\n          </button>\r\n        ))}\r\n      </Box>\r\n      {/* Details Dialog */}\r\n      <Dialog\r\n        open={!!selectedLog}\r\n        onClose={() => setSelectedLog(null)}\r\n        maxWidth=\"sm\"\r\n        fullWidth\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"space-between\",\r\n          }}\r\n        >\r\n          Row Details\r\n          <IconButton onClick={() => setSelectedLog(null)}>\r\n            <CloseIcon />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent dividers>\r\n          {selectedLog && (\r\n            <Box\r\n              sx={{\r\n                fontFamily: \"Montserrat\",\r\n                display: \"flex\",\r\n                flexDirection: \"column\",\r\n                gap: 2,\r\n              }}\r\n            >\r\n              <div>\r\n                <strong>Order ID:</strong> {selectedLog.orderId?._id || \"N/A\"}\r\n              </div>\r\n              <div>\r\n                <strong>Brand:</strong>{\" \"}\r\n                {selectedLog.brandId?.brandName || \"N/A\"}\r\n              </div>\r\n              <div>\r\n                <strong>Total (EGP):</strong> {formatMoney(selectedLog.total)}\r\n              </div>\r\n              <div>\r\n                <strong>VAT:</strong> {formatMoney(selectedLog.vat)}\r\n              </div>\r\n              <div>\r\n                <strong>Shipping:</strong>{\" \"}\r\n                {formatMoney(selectedLog.shippingFee)}\r\n              </div>\r\n              <div>\r\n                <strong>Paymob Fee:</strong>{\" \"}\r\n                {formatMoney(selectedLog.paymobFee)}\r\n              </div>\r\n              <div>\r\n                <strong>Commission:</strong>{\" \"}\r\n                {formatMoney(selectedLog.commission)}\r\n              </div>\r\n              <div>\r\n                <strong>Brand Payout:</strong>{\" \"}\r\n                {formatMoney(selectedLog.brandPayout)}\r\n              </div>\r\n              <div>\r\n                <strong>Admin Profit:</strong>{\" \"}\r\n                {formatMoney(selectedLog.netAdminProfit)}\r\n              </div>\r\n              <div>\r\n                <strong>Date:</strong>{\" \"}\r\n                {selectedLog.date\r\n                  ? new Date(selectedLog.date).toLocaleDateString()\r\n                  : \"N/A\"}\r\n              </div>\r\n            </Box>\r\n          )}\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button\r\n            onClick={() => setSelectedLog(null)}\r\n            color=\"primary\"\r\n            variant=\"contained\"\r\n            sx={{ background: \"#2d2d2d\" }}\r\n          >\r\n            Close\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AccountingAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,gBAAgB,EAChBC,UAAU,EACVC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,UAAU,QACL,eAAe;AACtB,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,mBAAmB,EACnBC,aAAa,EACbC,MAAM,QACD,UAAU;AACjB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,UAAU;AACjC,SACEC,eAAe,EACfC,WAAW,EACXC,YAAY,EACZC,gBAAgB,EAChBC,UAAU,QACL,gBAAgB;AACvB,OAAOC,SAAS,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,OAAO,GAAG,CACd;EAAEC,EAAE,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAW,CAAC,EACpC;EAAED,EAAE,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC/B;EAAED,EAAE,EAAE,OAAO;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAK,CAAC,EAClD;EAAEF,EAAE,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAK,CAAC,EACxC;EAAEF,EAAE,EAAE,aAAa;EAAEC,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAK,CAAC,EACrD;EAAEF,EAAE,EAAE,WAAW;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAK,CAAC,EACrD;EAAEF,EAAE,EAAE,YAAY;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAK,CAAC,EACtD;EAAEF,EAAE,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAK,CAAC,EACzD;EAAEF,EAAE,EAAE,gBAAgB;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAK,CAAC,EAC5D;EAAEF,EAAE,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,CAC9B;AAED,MAAME,WAAW,GAAG,CAClB;EAAEC,KAAK,EAAE,OAAO;EAAEH,KAAK,EAAE;AAAc,CAAC,EACxC;EAAEG,KAAK,EAAE,gBAAgB;EAAEH,KAAK,EAAE;AAAe,CAAC,EAClD;EAAEG,KAAK,EAAE,aAAa;EAAEH,KAAK,EAAE;AAAe,CAAC,EAC/C;EAAEG,KAAK,EAAE,MAAM;EAAEH,KAAK,EAAE;AAAO,CAAC,CACjC;AAED,SAASI,WAAWA,CAACD,KAAK,EAAE;EAC1B,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,KAAK,EAAE,OAAO,KAAK;EAC1E,OAAO,GAAGG,MAAM,CAACH,KAAK,CAAC,CAACI,cAAc,CAAC,OAAO,EAAE;IAC9CC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,KAAK;AACT;AAEA,MAAMC,SAAS,GAAGA,CAAC;EAAEP;AAAM,CAAC,KAC1BA,KAAK,KAAK,KAAK,gBACbN,OAAA;EAAAc,QAAA,EAAM;AAAG;EAAAC,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CAAC,gBAEhBlB,OAAA;EAAMmB,KAAK,EAAE;IAAEC,OAAO,EAAE,MAAM;IAAEC,UAAU,EAAE,QAAQ;IAAEC,GAAG,EAAE;EAAE,CAAE;EAAAR,QAAA,EAC5DP,WAAW,CAACD,KAAK;AAAC;EAAAS,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACf,CACP;AAACK,EAAA,GAPEV,SAAS;AASf,MAAMW,iBAAiB,GAAG,CACxB;EAAElB,KAAK,EAAE,aAAa;EAAEH,KAAK,EAAE;AAAe,CAAC,EAC/C;EAAEG,KAAK,EAAE,OAAO;EAAEH,KAAK,EAAE;AAAc,CAAC,EACxC;EAAEG,KAAK,EAAE,YAAY;EAAEH,KAAK,EAAE;AAAa,CAAC,EAC5C;EAAEG,KAAK,EAAE,gBAAgB;EAAEH,KAAK,EAAE;AAAe,CAAC,CACnD;AAED,SAASsB,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACrD,IAAI,CAACF,IAAI,CAACG,MAAM,EAAE;EAClB,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,OAAO,EACP,aAAa,EACb,KAAK,EACL,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,MAAM,CACP;EACD,MAAMC,IAAI,GAAIC,GAAG,IAAK,IAAIA,GAAG,GAAG;EAChC,MAAMC,IAAI,GAAGP,IAAI,CAACQ,GAAG,CAAEC,GAAG;IAAA,IAAAC,YAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,mBAAA;IAAA,OAAK,CAC7Bb,IAAI,CAAC,EAAAK,YAAA,GAAAD,GAAG,CAACU,OAAO,cAAAT,YAAA,uBAAXA,YAAA,CAAaU,GAAG,KAAI,KAAK,CAAC,EAC/Bf,IAAI,CAAC,EAAAM,YAAA,GAAAF,GAAG,CAACY,OAAO,cAAAV,YAAA,uBAAXA,YAAA,CAAaV,SAAS,KAAI,KAAK,CAAC,EACrCI,IAAI,EAAAO,UAAA,GAACH,GAAG,CAACa,KAAK,cAAAV,UAAA,cAAAA,UAAA,GAAI,KAAK,CAAC,EACxBP,IAAI,EAAAQ,QAAA,GAACJ,GAAG,CAACc,GAAG,cAAAV,QAAA,cAAAA,QAAA,GAAI,KAAK,CAAC,EACtBR,IAAI,EAAAS,gBAAA,GAACL,GAAG,CAACe,WAAW,cAAAV,gBAAA,cAAAA,gBAAA,GAAI,KAAK,CAAC,EAC9BT,IAAI,EAAAU,cAAA,GAACN,GAAG,CAACgB,SAAS,cAAAV,cAAA,cAAAA,cAAA,GAAI,KAAK,CAAC,EAC5BV,IAAI,EAAAW,eAAA,GAACP,GAAG,CAACiB,UAAU,cAAAV,eAAA,cAAAA,eAAA,GAAI,KAAK,CAAC,EAC7BX,IAAI,EAAAY,gBAAA,GAACR,GAAG,CAACkB,WAAW,cAAAV,gBAAA,cAAAA,gBAAA,GAAI,KAAK,CAAC,EAC9BZ,IAAI,EAAAa,mBAAA,GAACT,GAAG,CAACmB,cAAc,cAAAV,mBAAA,cAAAA,mBAAA,GAAI,KAAK,CAAC,EACjCb,IAAI,CAACI,GAAG,CAACoB,IAAI,GAAG,IAAIC,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG,KAAK,CAAC,CACjE;EAAA,EAAC;;EAEF;EACA,MAAMC,GAAG,GAAIC,GAAG,IACdjC,IAAI,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACwB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7D,IAAIG,SAAS,GAAG,CACd,EAAE;EAAE;EACJ,OAAO;EAAE;EACTJ,GAAG,CAAC,OAAO,CAAC,EACZA,GAAG,CAAC,KAAK,CAAC,EACVA,GAAG,CAAC,aAAa,CAAC,EAClBA,GAAG,CAAC,WAAW,CAAC,EAChBA,GAAG,CAAC,YAAY,CAAC,EACjBA,GAAG,CAAC,aAAa,CAAC,EAClBA,GAAG,CAAC,gBAAgB,CAAC,EACrB,EAAE,CAAE;EAAA,CACL,CAACxB,GAAG,CAAC,CAACF,GAAG,EAAE+B,GAAG,KAAK;IAClB;IACA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,IAAI,OAAO/B,GAAG,KAAK,QAAQ,EAAE;MAClE,OAAOA,GAAG,CAACtB,cAAc,CAAC,OAAO,EAAE;QACjCC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MACzB,CAAC,CAAC;IACJ;IACA,OAAOoB,GAAG;EACZ,CAAC,CAAC;;EAEF;EACA,IAAIJ,QAAQ,EAAE;IACZ,MAAMqC,MAAM,GAAG;MACbjB,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE;IAClB,CAAC,CAAC1B,QAAQ,CAAC;IACX,IAAIqC,MAAM,KAAKzD,SAAS,EAAE;MACxBsD,SAAS,CAACG,MAAM,CAAC,GAAG,QAAQH,SAAS,CAACG,MAAM,CAAC,EAAE;IACjD;EACF;;EAEA;EACAH,SAAS,GAAGA,SAAS,CAAC5B,GAAG,CAACH,IAAI,CAAC;EAC/B,OAAO+B,SAAS,CAACjC,MAAM,GAAGC,OAAO,CAACD,MAAM,EAAEiC,SAAS,CAACI,IAAI,CAACnC,IAAI,CAAC,EAAE,CAAC,CAAC;EAClE,OAAO+B,SAAS,CAACjC,MAAM,GAAGC,OAAO,CAACD,MAAM,EACtCiC,SAAS,GAAGA,SAAS,CAACK,KAAK,CAAC,CAAC,EAAErC,OAAO,CAACD,MAAM,CAAC;EAEhD,IAAIuC,UAAU,GAAGtC,OAAO,CAACI,GAAG,CAACH,IAAI,CAAC,CAACsC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;EACnDpC,IAAI,CAACqC,OAAO,CAAEC,GAAG,IAAK;IACpBH,UAAU,IAAIG,GAAG,CAACF,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;EACpC,CAAC,CAAC;EACFD,UAAU,IAAIN,SAAS,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;EAExC,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE;IAAEM,IAAI,EAAE;EAA0B,CAAC,CAAC;EACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACxCF,IAAI,CAACG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;EACrCG,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,GAAGtD,SAAS,IAAI,YAAY,iBAAiB,CAAC;EAC5EiD,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;EAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;EACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;AACjC;AAEA,MAAMW,YAAY,GAAG,CACnB;EAAEhF,KAAK,EAAE,MAAM;EAAEH,KAAK,EAAE;AAAQ,CAAC,EACjC;EAAEG,KAAK,EAAE,OAAO;EAAEH,KAAK,EAAE;AAAS,CAAC,EACnC;EAAEG,KAAK,EAAE,MAAM;EAAEH,KAAK,EAAE;AAAQ,CAAC,CAClC;AAED,SAASoF,iBAAiBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACvC;EACA,IAAI,CAACD,IAAI,CAAC3D,MAAM,EAAE,OAAO,EAAE;EAC3B,MAAM6D,MAAM,GAAG,CAAC,CAAC;EACjBF,IAAI,CAAClB,OAAO,CAAEnC,GAAG,IAAK;IACpB,IAAI,CAACA,GAAG,CAACoB,IAAI,EAAE;IACf,MAAMA,IAAI,GAAG,IAAIC,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC;IAC/B,IAAIpD,KAAK,GAAG,EAAE;IACd,IAAIsF,MAAM,KAAK,MAAM,EAAE;MACrB;MACA,MAAME,IAAI,GAAGpC,IAAI,CAACqC,WAAW,CAAC,CAAC;MAC/B,MAAMC,QAAQ,GAAG,IAAIrC,IAAI,CAACD,IAAI,CAACqC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACnD,MAAME,IAAI,GAAGC,IAAI,CAACC,IAAI,CACpB,CAAC,CAACzC,IAAI,GAAGsC,QAAQ,IAAI,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAC3D,CAAC;MACD9F,KAAK,GAAG,GAAGwF,IAAI,KAAKG,IAAI,EAAE;IAC5B,CAAC,MAAM,IAAIL,MAAM,KAAK,OAAO,EAAE;MAC7BtF,KAAK,GAAG,GAAGoD,IAAI,CAACqC,WAAW,CAAC,CAAC,IAAIM,MAAM,CAAC3C,IAAI,CAAC4C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CACnE,CAAC,EACD,GACF,CAAC,EAAE;IACL,CAAC,MAAM,IAAIX,MAAM,KAAK,MAAM,EAAE;MAC5BtF,KAAK,GAAG,GAAGoD,IAAI,CAACqC,WAAW,CAAC,CAAC,EAAE;IACjC;IACA,IAAI,CAACF,MAAM,CAACvF,KAAK,CAAC,EAAEuF,MAAM,CAACvF,KAAK,CAAC,GAAG,CAAC;IACrCuF,MAAM,CAACvF,KAAK,CAAC,IAAIM,MAAM,CAAC0B,GAAG,CAACa,KAAK,CAAC,IAAI,CAAC;EACzC,CAAC,CAAC;EACF;EACA,OAAOqD,MAAM,CAACC,OAAO,CAACZ,MAAM,CAAC,CAC1Ba,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKD,CAAC,CAACE,aAAa,CAACD,CAAC,CAAC,CAAC,CACtCvE,GAAG,CAAC,CAAC,CAAC/B,KAAK,EAAE6C,KAAK,CAAC,MAAM;IAAE7C,KAAK;IAAE6C,KAAK,EAAEvC,MAAM,CAACuC,KAAK,CAAC2D,OAAO,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,CAAC;AAC1E;AAEA,SAASC,iBAAiBA,CAACzG,KAAK,EAAE;EAChC;EACA,IAAI,CAACA,KAAK,IAAI,CAAC,qBAAqB,CAAC0G,IAAI,CAAC1G,KAAK,CAAC,EAAE,OAAOA,KAAK;EAC9D,MAAM,CAACwF,IAAI,EAAEmB,KAAK,CAAC,GAAG3G,KAAK,CAAC4G,KAAK,CAAC,GAAG,CAAC;EACtC,MAAMxD,IAAI,GAAG,IAAIC,IAAI,CAAC/C,MAAM,CAACkF,IAAI,CAAC,EAAElF,MAAM,CAACqG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACzD,OAAOtH,MAAM,CAAC+D,IAAI,EAAE,UAAU,CAAC;AACjC;AAEA,SAASyD,YAAYA,CAAC7G,KAAK,EAAE;EAC3B;EACA,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;EACrB,MAAM,CAACwF,IAAI,EAAEG,IAAI,CAAC,GAAG3F,KAAK,CAAC4G,KAAK,CAAC,IAAI,CAAC;EACtC,IAAI,CAACpB,IAAI,IAAI,CAACG,IAAI,EAAE,OAAO3F,KAAK;EAChC,OAAO,IAAI2F,IAAI,IAAIH,IAAI,EAAE;AAC3B;AAEA,SAASsB,aAAaA,CAAC9G,KAAK,EAAEsF,MAAM,EAAE;EACpC,IAAIA,MAAM,KAAK,OAAO,EAAE,OAAOmB,iBAAiB,CAACzG,KAAK,CAAC;EACvD,IAAIsF,MAAM,KAAK,MAAM,EAAE,OAAOuB,YAAY,CAAC7G,KAAK,CAAC;EACjD,OAAOA,KAAK;AACd;AAEA,SAAS+G,kBAAkBA,CAACxF,IAAI,EAAE;EAChC;EACA,OAAOA,IAAI,CAACQ,GAAG,CAAEiF,CAAC,KAAM;IACtB7G,KAAK,EAAE6G,CAAC,CAAChH,KAAK;IACdA,KAAK,EAAEyG,iBAAiB,CAACO,CAAC,CAAChH,KAAK;EAClC,CAAC,CAAC,CAAC;AACL;AAEA,MAAMiH,aAAa,GAAG,EAAE;AAExB,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,oBAAA;EAC5B,MAAM,CAAChC,IAAI,EAAEiC,OAAO,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACmK,OAAO,EAAEC,UAAU,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqK,KAAK,EAAEC,QAAQ,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuK,WAAW,EAAEC,cAAc,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyK,QAAQ,EAAEC,WAAW,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2K,MAAM,EAAEC,SAAS,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6K,MAAM,EAAEC,SAAS,CAAC,GAAG9K,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAAC+K,SAAS,EAAEC,YAAY,CAAC,GAAGhL,QAAQ,CAAC,MAAM,CAAC;;EAElD;EACA,MAAM,CAACiL,SAAS,EAAEC,YAAY,CAAC,GAAGlL,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqE,QAAQ,EAAE8G,WAAW,CAAC,GAAGnL,QAAQ,CAAC,aAAa,CAAC;EACvD,MAAM,CAACoL,UAAU,EAAEC,aAAa,CAAC,GAAGrL,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACsL,WAAW,EAAEC,cAAc,CAAC,GAAGvL,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAACwL,UAAU,EAAEC,aAAa,CAAC,GAAGzL,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0L,aAAa,EAAEC,gBAAgB,CAAC,GAAG3L,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4L,WAAW,EAAEC,cAAc,CAAC,GAAG7L,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8L,aAAa,EAAEC,gBAAgB,CAAC,GAAG/L,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACgM,WAAW,EAAEC,cAAc,CAAC,GAAGjM,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkM,WAAW,EAAEC,cAAc,CAAC,GAAGnM,QAAQ,CAAC,IAAI,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACdqK,UAAU,CAAC,IAAI,CAAC;IAChBpI,KAAK,CACFoK,GAAG,CAAC,gEAAgE,CAAC,CACrEC,IAAI,CAAEC,GAAG,IAAK;MACbpC,OAAO,CAACoC,GAAG,CAACnI,IAAI,IAAI,EAAE,CAAC;MACvBiG,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDmC,KAAK,CAAEC,GAAG,IAAK;MACdlC,QAAQ,CAAC,sBAAsB,CAAC;MAChCF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,MAAM,GAAGxM,OAAO,CAAC,MAAM;IAC3B,MAAMyM,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B1E,IAAI,CAAClB,OAAO,CAAEnC,GAAG,IAAK;MACpB,IAAIA,GAAG,CAACY,OAAO,IAAIZ,GAAG,CAACY,OAAO,CAACpB,SAAS,EAAE;QACxCsI,QAAQ,CAACE,GAAG,CAAChI,GAAG,CAACY,OAAO,CAACpB,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;IACF,OAAOyI,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC;EAC7B,CAAC,EAAE,CAACzE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM8E,YAAY,GAAG9M,OAAO,CAAC,MAAM;IACjC,OAAOgI,IAAI,CAAC+E,MAAM,CAAEpI,GAAG,IAAK;MAAA,IAAAqI,aAAA;MAC1B;MACA,IAAI1C,WAAW,IAAI,EAAA0C,aAAA,GAAArI,GAAG,CAACY,OAAO,cAAAyH,aAAA,uBAAXA,aAAA,CAAa7I,SAAS,MAAKmG,WAAW,EAAE,OAAO,KAAK;MACvE;MACA,IAAIE,QAAQ,IAAI,IAAIxE,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC,GAAG,IAAIC,IAAI,CAACwE,QAAQ,CAAC,EAAE,OAAO,KAAK;MACrE,IAAIE,MAAM,IAAI,IAAI1E,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC,GAAG,IAAIC,IAAI,CAAC0E,MAAM,CAAC,EAAE,OAAO,KAAK;MACjE,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1C,IAAI,EAAEsC,WAAW,EAAEE,QAAQ,EAAEE,MAAM,CAAC,CAAC;;EAEzC;EACA,MAAMuC,UAAU,GAAGjN,OAAO,CAAC,MAAM;IAC/B,MAAMkN,MAAM,GAAG,CAAC,GAAGJ,YAAY,CAAC;IAChCI,MAAM,CAACnE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACpB,IAAIkE,MAAM,EAAEC,MAAM;MAClB,IAAIxC,MAAM,KAAK,MAAM,EAAE;QACrBuC,MAAM,GAAG,IAAInH,IAAI,CAACgD,CAAC,CAACjD,IAAI,CAAC;QACzBqH,MAAM,GAAG,IAAIpH,IAAI,CAACiD,CAAC,CAAClD,IAAI,CAAC;MAC3B,CAAC,MAAM;QAAA,IAAAsH,SAAA,EAAAC,SAAA;QACLH,MAAM,IAAAE,SAAA,GAAGrE,CAAC,CAAC4B,MAAM,CAAC,cAAAyC,SAAA,cAAAA,SAAA,GAAI,CAAC;QACvBD,MAAM,IAAAE,SAAA,GAAGrE,CAAC,CAAC2B,MAAM,CAAC,cAAA0C,SAAA,cAAAA,SAAA,GAAI,CAAC;MACzB;MACA,IAAIH,MAAM,GAAGC,MAAM,EAAE,OAAOtC,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACxD,IAAIqC,MAAM,GAAGC,MAAM,EAAE,OAAOtC,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MACxD,OAAO,CAAC;IACV,CAAC,CAAC;IACF,OAAOoC,MAAM;EACf,CAAC,EAAE,CAACJ,YAAY,EAAElC,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAErC;EACA,MAAMyC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMC,SAAS,GAAG,CAACxC,SAAS,GACxBhD,IAAI,GACJA,IAAI,CAAC+E,MAAM,CAAEpI,GAAG;MAAA,IAAA8I,aAAA;MAAA,OAAK,EAAAA,aAAA,GAAA9I,GAAG,CAACY,OAAO,cAAAkI,aAAA,uBAAXA,aAAA,CAAatJ,SAAS,MAAK6G,SAAS;IAAA,EAAC;IAC9D,IAAI,CAACwC,SAAS,CAACnJ,MAAM,EAAE;MACrB+G,aAAa,CAAC,yBAAyB,CAAC;MACxC;IACF;IACA,MAAMlF,GAAG,GAAGsH,SAAS,CAACpH,MAAM,CAC1B,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACP,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,EAChD,CACF,CAAC;IACDgH,aAAa,CACX,GAAGpH,iBAAiB,CAAC0J,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAAC7K,KAAK,KAAKsB,QAAQ,CAAC,CAACzB,KAAK,QAC9DqI,SAAS,IAAI,YAAY,KACtBjI,WAAW,CAACmD,GAAG,CAAC,EACvB,CAAC;EACH,CAAC;;EAED;EACA,MAAM0H,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMJ,SAAS,GAAG,CAACxC,SAAS,GACxBhD,IAAI,GACJA,IAAI,CAAC+E,MAAM,CAAEpI,GAAG;MAAA,IAAAkJ,aAAA;MAAA,OAAK,EAAAA,aAAA,GAAAlJ,GAAG,CAACY,OAAO,cAAAsI,aAAA,uBAAXA,aAAA,CAAa1J,SAAS,MAAK6G,SAAS;IAAA,EAAC;IAC9D/G,WAAW,CACTuJ,SAAS,EACTxC,SAAS,IAAI,YAAY,EACzBA,SAAS,IAAI5G,QAAQ,GAAGA,QAAQ,GAAG,IACrC,CAAC;EACH,CAAC;;EAED;EACA,MAAM0J,iBAAiB,GAAG9N,OAAO,CAAC,MAAM;IACtC,OAAOgI,IAAI,CAAC+E,MAAM,CAAEpI,GAAG,IAAK;MAAA,IAAAoJ,aAAA;MAC1B,IAAIxC,UAAU,IAAI,EAAAwC,aAAA,GAAApJ,GAAG,CAACY,OAAO,cAAAwI,aAAA,uBAAXA,aAAA,CAAa5J,SAAS,MAAKoH,UAAU,EAAE,OAAO,KAAK;MACrE,IAAIE,aAAa,IAAI,IAAIzF,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC,GAAG,IAAIC,IAAI,CAACyF,aAAa,CAAC,EAC/D,OAAO,KAAK;MACd,IAAIE,WAAW,IAAI,IAAI3F,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC,GAAG,IAAIC,IAAI,CAAC2F,WAAW,CAAC,EAC3D,OAAO,KAAK;MACd,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC3D,IAAI,EAAEuD,UAAU,EAAEE,aAAa,EAAEE,WAAW,CAAC,CAAC;EAElD,MAAMqC,SAAS,GAAGhO,OAAO,CACvB,MAAM+H,iBAAiB,CAAC+F,iBAAiB,EAAEzC,WAAW,CAAC,EACvD,CAACyC,iBAAiB,EAAEzC,WAAW,CACjC,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAGjO,OAAO,CAAC,MAAM;IACvC,IAAIqL,WAAW,KAAK,OAAO,IAAIQ,aAAa,EAAE;MAC5C,OAAOmC,SAAS,CAACjB,MAAM,CAAEpD,CAAC,IAAKA,CAAC,CAAChH,KAAK,KAAKkJ,aAAa,CAAC;IAC3D;IACA,OAAOmC,SAAS;EAClB,CAAC,EAAE,CAACA,SAAS,EAAE3C,WAAW,EAAEQ,aAAa,CAAC,CAAC;;EAE3C;EACA,MAAMqC,eAAe,GAAGlO,OAAO,CAAC,MAAM;IACpC,IAAIqL,WAAW,KAAK,OAAO,EAAE,OAAO,EAAE;IACtC,OAAO3B,kBAAkB,CAACsE,SAAS,CAAC,CAACjB,MAAM,CAAEoB,CAAC,IAC5C,qBAAqB,CAAC9E,IAAI,CAAC8E,CAAC,CAACrL,KAAK,CACpC,CAAC;EACH,CAAC,EAAE,CAACkL,SAAS,EAAE3C,WAAW,CAAC,CAAC;;EAE5B;EACA,MAAM+C,UAAU,GAAG7F,IAAI,CAACC,IAAI,CAACyE,UAAU,CAAC5I,MAAM,GAAGuF,aAAa,CAAC;EAC/D,MAAMyE,cAAc,GAAGtC,WAAW,GAAGnC,aAAa;EAClD,MAAM0E,eAAe,GAAGD,cAAc,GAAGzE,aAAa;EACtD,MAAM2E,WAAW,GAAGtB,UAAU,CAACtG,KAAK,CAAC2H,eAAe,EAAED,cAAc,CAAC;;EAErE;EACAvO,SAAS,CAAC,MAAM;IACd,IAAIiM,WAAW,GAAGqC,UAAU,EAAEpC,cAAc,CAAC,CAAC,CAAC;EACjD,CAAC,EAAE,CAACoC,UAAU,EAAErC,WAAW,CAAC,CAAC;;EAE7B;EACA,MAAMyC,QAAQ,GAAGxO,OAAO,CACtB,MAAMiN,UAAU,CAAC7G,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACc,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EACtE,CAACwH,UAAU,CACb,CAAC;EACD,MAAMwB,WAAW,GAAGzO,OAAO,CACzB,MACEiN,UAAU,CAAC7G,MAAM,CACf,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACmB,cAAc,CAAC,IAAI,CAAC,CAAC,EACrD,CACF,CAAC,EACH,CAACmH,UAAU,CACb,CAAC;EACD,MAAMyB,WAAW,GAAG1O,OAAO,CACzB,MACEiN,UAAU,CAAC7G,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACgB,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EACxE,CAACsH,UAAU,CACb,CAAC;EACD,MAAM0B,gBAAgB,GAAG3O,OAAO,CAC9B,MACEiN,UAAU,CAAC7G,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACkB,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1E,CAACoH,UAAU,CACb,CAAC;EACD,MAAM2B,UAAU,GAAG5O,OAAO,CACxB,MAAMiN,UAAU,CAAC7G,MAAM,CAAC,CAACC,GAAG,EAAE1B,GAAG,KAAK0B,GAAG,IAAIpD,MAAM,CAAC0B,GAAG,CAACa,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EACxE,CAACyH,UAAU,CACb,CAAC;EAED,IAAI/C,OAAO,EACT,oBACE1H,OAAA,CAACvC,GAAG;IACF2D,OAAO,EAAC,MAAM;IACdiL,cAAc,EAAC,QAAQ;IACvBhL,UAAU,EAAC,QAAQ;IACnBiL,SAAS,EAAC,MAAM;IAAAxL,QAAA,eAEhBd,OAAA,CAACzB,gBAAgB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;EAEV,IAAI0G,KAAK,EACP,oBACE5H,OAAA,CAACvC,GAAG;IACF2D,OAAO,EAAC,MAAM;IACdiL,cAAc,EAAC,QAAQ;IACvBhL,UAAU,EAAC,QAAQ;IACnBiL,SAAS,EAAC,MAAM;IAAAxL,QAAA,eAEhBd,OAAA,CAACxB,UAAU;MAAC+N,KAAK,EAAC,OAAO;MAAAzL,QAAA,EAAE8G;IAAK;MAAA7G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC;EAGV,oBACElB,OAAA,CAACvC,GAAG;IAAC+O,CAAC,EAAE,CAAE;IAAA1L,QAAA,gBAERd,OAAA,CAACvC,GAAG;MACFgP,EAAE,EAAE;QACFrL,OAAO,EAAE,MAAM;QACfsL,aAAa,EAAE,KAAK;QACpBpL,GAAG,EAAE,CAAC;QACNqL,EAAE,EAAE,CAAC;QACLC,QAAQ,EAAE;MACZ,CAAE;MAAA9L,QAAA,gBAEFd,OAAA,CAACvC,GAAG;QACFgP,EAAE,EAAE;UACFI,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZT,CAAC,EAAE,CAAC;UACJpL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBAEFd,OAAA,CAACP,eAAe;UAACyN,IAAI,EAAE,EAAG;UAAC/L,KAAK,EAAE;YAAEoL,KAAK,EAAE;UAAU;QAAE;UAAAxL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DlB,OAAA,CAACvC,GAAG;UAAAqD,QAAA,gBACFd,OAAA;YAAImB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAvM,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlB,OAAA;YAAGmB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEE,UAAU,EAAE,GAAG;cAAED,QAAQ,EAAE;YAAQ,CAAE;YAAAtM,QAAA,GACzDkL,QAAQ,CAACtL,cAAc,CAAC,OAAO,EAAE;cAChCC,qBAAqB,EAAE,CAAC;cACxBC,qBAAqB,EAAE;YACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlB,OAAA,CAACvC,GAAG;QACFgP,EAAE,EAAE;UACFI,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZT,CAAC,EAAE,CAAC;UACJpL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBAEFd,OAAA,CAACN,WAAW;UAACwN,IAAI,EAAE,EAAG;UAAC/L,KAAK,EAAE;YAAEoL,KAAK,EAAE;UAAU;QAAE;UAAAxL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDlB,OAAA,CAACvC,GAAG;UAAAqD,QAAA,gBACFd,OAAA;YAAImB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAvM,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlB,OAAA;YAAGmB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEE,UAAU,EAAE,GAAG;cAAED,QAAQ,EAAE;YAAQ,CAAE;YAAAtM,QAAA,GACzDmL,WAAW,CAACvL,cAAc,CAAC,OAAO,EAAE;cACnCC,qBAAqB,EAAE,CAAC;cACxBC,qBAAqB,EAAE;YACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlB,OAAA,CAACvC,GAAG;QACFgP,EAAE,EAAE;UACFI,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZT,CAAC,EAAE,CAAC;UACJpL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBAEFd,OAAA,CAACL,YAAY;UAACuN,IAAI,EAAE,EAAG;UAAC/L,KAAK,EAAE;YAAEoL,KAAK,EAAE;UAAU;QAAE;UAAAxL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDlB,OAAA,CAACvC,GAAG;UAAAqD,QAAA,gBACFd,OAAA;YAAImB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAvM,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlB,OAAA;YAAGmB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEE,UAAU,EAAE,GAAG;cAAED,QAAQ,EAAE;YAAQ,CAAE;YAAAtM,QAAA,GACzDoL,WAAW,CAACxL,cAAc,CAAC,OAAO,EAAE;cACnCC,qBAAqB,EAAE,CAAC;cACxBC,qBAAqB,EAAE;YACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlB,OAAA,CAACvC,GAAG;QACFgP,EAAE,EAAE;UACFI,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZT,CAAC,EAAE,CAAC;UACJpL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBAEFd,OAAA,CAACJ,gBAAgB;UAACsN,IAAI,EAAE,EAAG;UAAC/L,KAAK,EAAE;YAAEoL,KAAK,EAAE;UAAU;QAAE;UAAAxL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DlB,OAAA,CAACvC,GAAG;UAAAqD,QAAA,gBACFd,OAAA;YAAImB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAvM,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlB,OAAA;YAAGmB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEE,UAAU,EAAE,GAAG;cAAED,QAAQ,EAAE;YAAQ,CAAE;YAAAtM,QAAA,GACzDqL,gBAAgB,CAACzL,cAAc,CAAC,OAAO,EAAE;cACxCC,qBAAqB,EAAE,CAAC;cACxBC,qBAAqB,EAAE;YACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlB,OAAA,CAACvC,GAAG;QACFgP,EAAE,EAAE;UACFI,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,GAAG;UACbC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZT,CAAC,EAAE,CAAC;UACJpL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAR,QAAA,gBAEFd,OAAA,CAACH,UAAU;UAACqN,IAAI,EAAE,EAAG;UAAC/L,KAAK,EAAE;YAAEoL,KAAK,EAAE;UAAU;QAAE;UAAAxL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDlB,OAAA,CAACvC,GAAG;UAAAqD,QAAA,gBACFd,OAAA;YAAImB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEC,QAAQ,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAvM,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlB,OAAA;YAAGmB,KAAK,EAAE;cAAEgM,MAAM,EAAE,CAAC;cAAEE,UAAU,EAAE,GAAG;cAAED,QAAQ,EAAE;YAAQ,CAAE;YAAAtM,QAAA,GACzDsL,UAAU,CAAC1L,cAAc,CAAC,OAAO,EAAE;cAClCC,qBAAqB,EAAE,CAAC;cACxBC,qBAAqB,EAAE;YACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA,CAACvC,GAAG;MAACkP,EAAE,EAAE,CAAE;MAACH,CAAC,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEM,UAAU,EAAE,MAAM;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAlM,QAAA,gBAC5Dd,OAAA,CAACvC,GAAG;QAAC2D,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAACqL,EAAE,EAAE,CAAE;QAACC,QAAQ,EAAC,MAAM;QAAA9L,QAAA,gBACpEd,OAAA,CAACxB,UAAU;UACT8O,OAAO,EAAC,IAAI;UACZD,UAAU,EAAE,GAAI;UAChBE,UAAU,EAAC,YAAY;UACvBd,EAAE,EAAE;YAAEI,IAAI,EAAE;UAAE,CAAE;UAAA/L,QAAA,EACjB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZoE,YAAY,CAACpD,GAAG,CAAEuD,MAAM,iBACvBzF,OAAA,CAAC1B,MAAM;UAELgP,OAAO,EAAEzE,WAAW,KAAKpD,MAAM,CAACnF,KAAK,GAAG,UAAU,GAAG,MAAO;UAC5D4M,IAAI,EAAC,OAAO;UACZT,EAAE,EAAE;YACFe,WAAW,EAAE,SAAS;YACtBjB,KAAK,EAAE1D,WAAW,KAAKpD,MAAM,CAACnF,KAAK,GAAG,SAAS,GAAG,MAAM;YACxD+M,UAAU,EAAE,GAAG;YACfL,YAAY,EAAE,CAAC;YACfF,QAAQ,EAAE,EAAE;YACZW,EAAE,EAAE,GAAG;YACPV,UAAU,EACRlE,WAAW,KAAKpD,MAAM,CAACnF,KAAK,GAAG,SAAS,GAAG,aAAa;YAC1D2M,SAAS,EAAE,MAAM;YACjBS,aAAa,EAAE,MAAM;YACrB,SAAS,EAAE;cACTX,UAAU,EAAE,MAAM;cAClBR,KAAK,EAAE1D,WAAW,KAAKpD,MAAM,CAACnF,KAAK,GAAG,SAAS,GAAG;YACpD;UACF,CAAE;UACFqN,OAAO,EAAEA,CAAA,KAAM;YACb7E,cAAc,CAACrD,MAAM,CAACnF,KAAK,CAAC;YAC5BgJ,gBAAgB,CAAC,EAAE,CAAC;UACtB,CAAE;UAAAxI,QAAA,EAED2E,MAAM,CAACtF;QAAK,GAxBRsF,MAAM,CAACnF,KAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBX,CACT,CAAC,eACFlB,OAAA,CAAC7B,WAAW;UAAC+O,IAAI,EAAC,OAAO;UAACT,EAAE,EAAE;YAAEK,QAAQ,EAAE;UAAI,CAAE;UAAAhM,QAAA,gBAC9Cd,OAAA,CAAC5B,UAAU;YAAA0C,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9BlB,OAAA,CAAC/B,MAAM;YACLqC,KAAK,EAAEyI,UAAW;YAClB5I,KAAK,EAAC,OAAO;YACbyN,QAAQ,EAAGC,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;YAAAQ,QAAA,gBAE/Cd,OAAA,CAAC9B,QAAQ;cAACoC,KAAK,EAAC,EAAE;cAAAQ,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EACvC8I,MAAM,CAAC9H,GAAG,CAAE6L,KAAK,iBAChB/N,OAAA,CAAC9B,QAAQ;cAAaoC,KAAK,EAAEyN,KAAM;cAAAjN,QAAA,EAChCiN;YAAK,GADOA,KAAK;cAAAhN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACb2H,WAAW,KAAK,OAAO,IAAI6C,eAAe,CAAC7J,MAAM,GAAG,CAAC,iBACpD7B,OAAA,CAAC7B,WAAW;UAAC+O,IAAI,EAAC,OAAO;UAACT,EAAE,EAAE;YAAEK,QAAQ,EAAE;UAAI,CAAE;UAAAhM,QAAA,gBAC9Cd,OAAA,CAAC5B,UAAU;YAAA0C,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9BlB,OAAA,CAAC/B,MAAM;YACLqC,KAAK,EAAE+I,aAAc;YACrBlJ,KAAK,EAAC,OAAO;YACbyN,QAAQ,EAAGC,CAAC,IAAKvE,gBAAgB,CAACuE,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;YAAAQ,QAAA,gBAElDd,OAAA,CAAC9B,QAAQ;cAACoC,KAAK,EAAC,EAAE;cAAAQ,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EACvCwK,eAAe,CAACxJ,GAAG,CAAEyJ,CAAC,iBACrB3L,OAAA,CAAC9B,QAAQ;cAAeoC,KAAK,EAAEqL,CAAC,CAACrL,KAAM;cAAAQ,QAAA,EACpC6K,CAAC,CAACxL;YAAK,GADKwL,CAAC,CAACrL,KAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACd,eACDlB,OAAA,CAAC3B,SAAS;UACR8B,KAAK,EAAC,MAAM;UACZuE,IAAI,EAAC,MAAM;UACXwI,IAAI,EAAC,OAAO;UACZc,eAAe,EAAE;YAAEC,MAAM,EAAE;UAAK,CAAE;UAClC3N,KAAK,EAAE2I,aAAc;UACrB2E,QAAQ,EAAGC,CAAC,IAAK3E,gBAAgB,CAAC2E,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAClDmM,EAAE,EAAE;YAAEK,QAAQ,EAAE;UAAI;QAAE;UAAA/L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFlB,OAAA,CAAC3B,SAAS;UACR8B,KAAK,EAAC,IAAI;UACVuE,IAAI,EAAC,MAAM;UACXwI,IAAI,EAAC,OAAO;UACZc,eAAe,EAAE;YAAEC,MAAM,EAAE;UAAK,CAAE;UAClC3N,KAAK,EAAE6I,WAAY;UACnByE,QAAQ,EAAGC,CAAC,IAAKzE,cAAc,CAACyE,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAChDmM,EAAE,EAAE;YAAEK,QAAQ,EAAE;UAAI;QAAE;UAAA/L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlB,OAAA,CAACZ,mBAAmB;QAAC8O,KAAK,EAAC,MAAM;QAACC,MAAM,EAAE,GAAI;QAAArN,QAAA,eAC5Cd,OAAA,CAACjB,SAAS;UACR2C,IAAI,EAAE+J,kBAAmB;UACzB0B,MAAM,EAAE;YAAEiB,GAAG,EAAE,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,IAAI,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAAzN,QAAA,gBAEnDd,OAAA,CAACX,aAAa;YAACmP,eAAe,EAAC;UAAK;YAAAzN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvClB,OAAA,CAACf,KAAK;YACJwP,OAAO,EAAC,OAAO;YACftN,KAAK,EAAE;cAAEoM,UAAU,EAAE;YAAa,CAAE;YACpCmB,aAAa,EAAGvO,KAAK,IAAK8G,aAAa,CAAC9G,KAAK,EAAE0I,WAAW;UAAE;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACFlB,OAAA,CAACd,KAAK;YACJiC,KAAK,EAAE;cAAEoM,UAAU,EAAE;YAAa,CAAE;YACpCmB,aAAa,EAAGpO,KAAK,IAAKA,KAAK,CAACI,cAAc,CAAC,OAAO;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACFlB,OAAA,CAACb,OAAO;YACNwP,SAAS,EAAGrO,KAAK,IAAKC,WAAW,CAACD,KAAK,CAAE;YACzCsO,cAAc,EAAGzO,KAAK,IAAK8G,aAAa,CAAC9G,KAAK,EAAE0I,WAAW;UAAE;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACFlB,OAAA,CAACV,MAAM;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVlB,OAAA,CAAChB,IAAI;YACH0F,IAAI,EAAC,UAAU;YACf+J,OAAO,EAAC,OAAO;YACfI,IAAI,EAAC,aAAa;YAClBC,MAAM,EAAC,SAAS;YAChBC,WAAW,EAAE,CAAE;YACfC,GAAG,EAAE;cAAEC,CAAC,EAAE;YAAE;UAAE;YAAAlO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAENlB,OAAA,CAACvC,GAAG;MACFkP,EAAE,EAAE,CAAE;MACNH,CAAC,EAAE,CAAE;MACLC,EAAE,EAAE;QACFM,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,CAAC;QACf5L,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,CAAC;QACNsL,QAAQ,EAAE;MACZ,CAAE;MAAA9L,QAAA,gBAEFd,OAAA,CAAC7B,WAAW;QAACsO,EAAE,EAAE;UAAEK,QAAQ,EAAE;QAAI,CAAE;QAAAhM,QAAA,gBACjCd,OAAA,CAAC5B,UAAU;UAAA0C,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9BlB,OAAA,CAAC/B,MAAM;UACLqC,KAAK,EAAEkI,SAAU;UACjBrI,KAAK,EAAC,OAAO;UACbyN,QAAQ,EAAGC,CAAC,IAAKpF,YAAY,CAACoF,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAAAQ,QAAA,gBAE9Cd,OAAA,CAAC9B,QAAQ;YAACoC,KAAK,EAAC,EAAE;YAAAQ,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACvC8I,MAAM,CAAC9H,GAAG,CAAE6L,KAAK,iBAChB/N,OAAA,CAAC9B,QAAQ;YAAaoC,KAAK,EAAEyN,KAAM;YAAAjN,QAAA,EAChCiN;UAAK,GADOA,KAAK;YAAAhN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlB,OAAA,CAAC7B,WAAW;QAACsO,EAAE,EAAE;UAAEK,QAAQ,EAAE;QAAI,CAAE;QAAAhM,QAAA,gBACjCd,OAAA,CAAC5B,UAAU;UAAA0C,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACpClB,OAAA,CAAC/B,MAAM;UACLqC,KAAK,EAAEsB,QAAS;UAChBzB,KAAK,EAAC,aAAa;UACnByN,QAAQ,EAAGC,CAAC,IAAKnF,WAAW,CAACmF,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAAAQ,QAAA,EAE5CU,iBAAiB,CAACU,GAAG,CAAEiJ,GAAG,iBACzBnL,OAAA,CAAC9B,QAAQ;YAAiBoC,KAAK,EAAE6K,GAAG,CAAC7K,KAAM;YAAAQ,QAAA,EACxCqK,GAAG,CAAChL;UAAK,GADGgL,GAAG,CAAC7K,KAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlB,OAAA,CAAC1B,MAAM;QACLgP,OAAO,EAAC,WAAW;QACnBnM,KAAK,EAAE;UAAE+N,eAAe,EAAE,SAAS;UAAE3C,KAAK,EAAE;QAAQ,CAAE;QACtDoB,OAAO,EAAE5C,eAAgB;QAAAjK,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlB,OAAA,CAAC1B,MAAM;QACLgP,OAAO,EAAC,UAAU;QAClBnM,KAAK,EAAE;UAAEgO,UAAU,EAAE;QAAE,CAAE;QACzBxB,OAAO,EAAEvC,YAAa;QACtBgE,QAAQ,EAAE5G,SAAS,KAAK,IAAK;QAAA1H,QAAA,EAC9B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRyH,UAAU,iBACT3I,OAAA,CAACxB,UAAU;QAACiO,EAAE,EAAE;UAAE4C,EAAE,EAAE,CAAC;UAAEhC,UAAU,EAAE,GAAG;UAAEd,KAAK,EAAE;QAAU,CAAE;QAAAzL,QAAA,EAC1D6H;MAAU;QAAA5H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENlB,OAAA,CAACvC,GAAG;MACFkP,EAAE,EAAE,CAAE;MACNH,CAAC,EAAE,CAAE;MACLC,EAAE,EAAE;QACFM,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,CAAC;QACf5L,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,CAAC;QACNsL,QAAQ,EAAE;MACZ,CAAE;MAAA9L,QAAA,gBAEFd,OAAA,CAAC7B,WAAW;QAACsO,EAAE,EAAE;UAAEK,QAAQ,EAAE;QAAI,CAAE;QAAAhM,QAAA,gBACjCd,OAAA,CAAC5B,UAAU;UAAA0C,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9BlB,OAAA,CAAC/B,MAAM;UACLqC,KAAK,EAAEwH,WAAY;UACnB3H,KAAK,EAAC,OAAO;UACbyN,QAAQ,EAAGC,CAAC,IAAK9F,cAAc,CAAC8F,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAAAQ,QAAA,gBAEhDd,OAAA,CAAC9B,QAAQ;YAACoC,KAAK,EAAC,EAAE;YAAAQ,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACvC8I,MAAM,CAAC9H,GAAG,CAAE6L,KAAK,iBAChB/N,OAAA,CAAC9B,QAAQ;YAAaoC,KAAK,EAAEyN,KAAM;YAAAjN,QAAA,EAChCiN;UAAK,GADOA,KAAK;YAAAhN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlB,OAAA,CAAC3B,SAAS;QACR8B,KAAK,EAAC,MAAM;QACZuE,IAAI,EAAC,MAAM;QACXsJ,eAAe,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;QAClC3N,KAAK,EAAE0H,QAAS;QAChB4F,QAAQ,EAAGC,CAAC,IAAK5F,WAAW,CAAC4F,CAAC,CAACC,MAAM,CAACxN,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACFlB,OAAA,CAAC3B,SAAS;QACR8B,KAAK,EAAC,IAAI;QACVuE,IAAI,EAAC,MAAM;QACXsJ,eAAe,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;QAClC3N,KAAK,EAAE4H,MAAO;QACd0F,QAAQ,EAAGC,CAAC,IAAK1F,SAAS,CAAC0F,CAAC,CAACC,MAAM,CAACxN,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACFlB,OAAA,CAAC7B,WAAW;QAACsO,EAAE,EAAE;UAAEK,QAAQ,EAAE;QAAI,CAAE;QAAAhM,QAAA,gBACjCd,OAAA,CAAC5B,UAAU;UAAA0C,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChClB,OAAA,CAAC/B,MAAM;UACLqC,KAAK,EAAE8H,MAAO;UACdjI,KAAK,EAAC,SAAS;UACfyN,QAAQ,EAAGC,CAAC,IAAKxF,SAAS,CAACwF,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAAAQ,QAAA,EAE1CT,WAAW,CAAC6B,GAAG,CAAEiJ,GAAG,iBACnBnL,OAAA,CAAC9B,QAAQ;YAAiBoC,KAAK,EAAE6K,GAAG,CAAC7K,KAAM;YAAAQ,QAAA,EACxCqK,GAAG,CAAChL;UAAK,GADGgL,GAAG,CAAC7K,KAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlB,OAAA,CAAC7B,WAAW;QAACsO,EAAE,EAAE;UAAEK,QAAQ,EAAE;QAAI,CAAE;QAAAhM,QAAA,gBACjCd,OAAA,CAAC5B,UAAU;UAAA0C,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9BlB,OAAA,CAAC/B,MAAM;UACLqC,KAAK,EAAEgI,SAAU;UACjBnI,KAAK,EAAC,OAAO;UACbyN,QAAQ,EAAGC,CAAC,IAAKtF,YAAY,CAACsF,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;UAAAQ,QAAA,gBAE9Cd,OAAA,CAAC9B,QAAQ;YAACoC,KAAK,EAAC,MAAM;YAAAQ,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC7ClB,OAAA,CAAC9B,QAAQ;YAACoC,KAAK,EAAC,KAAK;YAAAQ,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC5ClB,OAAA,CAAC9B,QAAQ;YAACoC,KAAK,EAAC,QAAQ;YAAAQ,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC1ClB,OAAA,CAAC9B,QAAQ;YAACoC,KAAK,EAAC,QAAQ;YAAAQ,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlB,OAAA,CAAC1B,MAAM;QACLgP,OAAO,EAAC,WAAW;QACnBnM,KAAK,EAAE;UACL+N,eAAe,EAAE,SAAS;UAC1B3C,KAAK,EAAE,OAAO;UACd4C,UAAU,EAAE;QACd,CAAE;QACFxB,OAAO,EAAEA,CAAA,KAAM;UACb5F,cAAc,CAAC,EAAE,CAAC;UAClBE,WAAW,CAAC,EAAE,CAAC;UACfE,SAAS,CAAC,EAAE,CAAC;UACbE,SAAS,CAAC,MAAM,CAAC;UACjBE,YAAY,CAAC,MAAM,CAAC;QACtB,CAAE;QAAAzH,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNlB,OAAA,CAACnC,cAAc;MAACyR,SAAS,EAAEtR,KAAM;MAAA8C,QAAA,eAC/Bd,OAAA,CAACtC,KAAK;QAAAoD,QAAA,gBACJd,OAAA,CAAClC,SAAS;UAAAgD,QAAA,eACRd,OAAA,CAACjC,QAAQ;YAAA+C,QAAA,EACNb,OAAO,CAACiC,GAAG,CAAEqN,GAAG,iBACfvP,OAAA,CAACpC,SAAS;cAER6O,EAAE,EAAE;gBAAEY,UAAU,EAAE,GAAG;gBAAEE,UAAU,EAAE;cAAa,CAAE;cAAAzM,QAAA,GAEjDyO,GAAG,CAACpP,KAAK,EACTiI,MAAM,KAAKmH,GAAG,CAACrP,EAAE,iBAChBF,OAAA,CAACvB,cAAc;gBACb+Q,MAAM;gBACNC,SAAS,EAAEnH,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG;cAAO;gBAAAvH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CACF;YAAA,GATIqO,GAAG,CAACrP,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUF,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZlB,OAAA,CAACrC,SAAS;UAAAmD,QAAA,GACPiL,WAAW,CAAC7J,GAAG,CAAEC,GAAG;YAAA,IAAAuN,aAAA,EAAAC,aAAA;YAAA,oBACnB3P,OAAA,CAACjC,QAAQ;cAEPoD,KAAK,EAAE;gBAAEyO,MAAM,EAAE;cAAU,CAAE;cAC7BjC,OAAO,EAAEA,CAAA,KAAMjE,cAAc,CAACvH,GAAG,CAAE;cAAArB,QAAA,gBAEnCd,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,EAAE,EAAA4O,aAAA,GAAAvN,GAAG,CAACU,OAAO,cAAA6M,aAAA,uBAAXA,aAAA,CAAa5M,GAAG,KAAI;cAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClDlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,EAAE,EAAA6O,aAAA,GAAAxN,GAAG,CAACY,OAAO,cAAA4M,aAAA,uBAAXA,aAAA,CAAahO,SAAS,KAAI;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxDlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACa;gBAAM;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACc;gBAAI;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACe;gBAAY;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACgB;gBAAU;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACiB;gBAAW;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACkB;gBAAY;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,eACRd,OAAA,CAACa,SAAS;kBAACP,KAAK,EAAE6B,GAAG,CAACmB;gBAAe;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACZlB,OAAA,CAACpC,SAAS;gBAAAkD,QAAA,EACPqB,GAAG,CAACoB,IAAI,GAAG,IAAIC,IAAI,CAACrB,GAAG,CAACoB,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;cAAK;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA,GA7BPiB,GAAG,CAACW,GAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8BJ,CAAC;UAAA,CACZ,CAAC,EACD6K,WAAW,CAAClK,MAAM,KAAK,CAAC,iBACvB7B,OAAA,CAACjC,QAAQ;YAAA+C,QAAA,eACPd,OAAA,CAACpC,SAAS;cAACiS,OAAO,EAAE5P,OAAO,CAAC4B,MAAO;cAACiO,KAAK,EAAC,QAAQ;cAAAhP,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEjBlB,OAAA,CAACvC,GAAG;MACFsS,SAAS,EAAC,YAAY;MACtBtD,EAAE,EAAE;QAAErL,OAAO,EAAE,MAAM;QAAEiL,cAAc,EAAE,QAAQ;QAAE2D,EAAE,EAAE,CAAC;QAAE1O,GAAG,EAAE;MAAE,CAAE;MAAAR,QAAA,EAEhEsJ,KAAK,CAACC,IAAI,CAAC;QAAExI,MAAM,EAAE+J;MAAW,CAAC,EAAE,CAACqE,CAAC,EAAEC,KAAK,kBAC3ClQ,OAAA;QAEE+P,SAAS,EAAExG,WAAW,KAAK2G,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAG;QACrD/O,KAAK,EAAE;UACLgP,MAAM,EAAE,mBAAmB;UAC3BpD,UAAU,EAAExD,WAAW,KAAK2G,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM;UAC1D3D,KAAK,EAAEhD,WAAW,KAAK2G,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;UACrDlD,YAAY,EAAE,CAAC;UACfoD,OAAO,EAAE,UAAU;UACnB/C,UAAU,EAAE,GAAG;UACfuC,MAAM,EAAE,SAAS;UACjBzC,MAAM,EAAE,OAAO;UACfkD,OAAO,EAAE,MAAM;UACfvD,QAAQ,EAAE;QACZ,CAAE;QACFa,OAAO,EAAEA,CAAA,KAAMnE,cAAc,CAAC0G,KAAK,GAAG,CAAC,CAAE;QAAApP,QAAA,EAExCoP,KAAK,GAAG;MAAC,GAhBLA,KAAK;QAAAnP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENlB,OAAA,CAACtB,MAAM;MACL4R,IAAI,EAAE,CAAC,CAAC7G,WAAY;MACpB8G,OAAO,EAAEA,CAAA,KAAM7G,cAAc,CAAC,IAAI,CAAE;MACpC8G,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA3P,QAAA,gBAETd,OAAA,CAACrB,WAAW;QACV8N,EAAE,EAAE;UACFrL,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBgL,cAAc,EAAE;QAClB,CAAE;QAAAvL,QAAA,GACH,aAEC,eAAAd,OAAA,CAAClB,UAAU;UAAC6O,OAAO,EAAEA,CAAA,KAAMjE,cAAc,CAAC,IAAI,CAAE;UAAA5I,QAAA,eAC9Cd,OAAA,CAACF,SAAS;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdlB,OAAA,CAACpB,aAAa;QAAC8R,QAAQ;QAAA5P,QAAA,EACpB2I,WAAW,iBACVzJ,OAAA,CAACvC,GAAG;UACFgP,EAAE,EAAE;YACFc,UAAU,EAAE,YAAY;YACxBnM,OAAO,EAAE,MAAM;YACfsL,aAAa,EAAE,QAAQ;YACvBpL,GAAG,EAAE;UACP,CAAE;UAAAR,QAAA,gBAEFd,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,EAAAqG,oBAAA,GAAAkC,WAAW,CAAC5G,OAAO,cAAA0E,oBAAA,uBAAnBA,oBAAA,CAAqBzE,GAAG,KAAI,KAAK;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAC1B,EAAAsG,oBAAA,GAAAiC,WAAW,CAAC1G,OAAO,cAAAyE,oBAAA,uBAAnBA,oBAAA,CAAqB7F,SAAS,KAAI,KAAK;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACX,WAAW,CAACkJ,WAAW,CAACzG,KAAK,CAAC;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACX,WAAW,CAACkJ,WAAW,CAACxG,GAAG,CAAC;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAC7BX,WAAW,CAACkJ,WAAW,CAACvG,WAAW,CAAC;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAC/BX,WAAW,CAACkJ,WAAW,CAACtG,SAAS,CAAC;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAC/BX,WAAW,CAACkJ,WAAW,CAACrG,UAAU,CAAC;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACjCX,WAAW,CAACkJ,WAAW,CAACpG,WAAW,CAAC;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACjCX,WAAW,CAACkJ,WAAW,CAACnG,cAAc,CAAC;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAAc,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACzBuI,WAAW,CAAClG,IAAI,GACb,IAAIC,IAAI,CAACiG,WAAW,CAAClG,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAC/C,KAAK;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBlB,OAAA,CAACnB,aAAa;QAAAiC,QAAA,eACZd,OAAA,CAAC1B,MAAM;UACLqP,OAAO,EAAEA,CAAA,KAAMjE,cAAc,CAAC,IAAI,CAAE;UACpC6C,KAAK,EAAC,SAAS;UACfe,OAAO,EAAC,WAAW;UACnBb,EAAE,EAAE;YAAEM,UAAU,EAAE;UAAU,CAAE;UAAAjM,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACoG,EAAA,CAryBID,eAAe;AAAAsJ,GAAA,GAAftJ,eAAe;AAuyBrB,eAAeA,eAAe;AAAC,IAAA9F,EAAA,EAAAoP,GAAA;AAAAC,YAAA,CAAArP,EAAA;AAAAqP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}