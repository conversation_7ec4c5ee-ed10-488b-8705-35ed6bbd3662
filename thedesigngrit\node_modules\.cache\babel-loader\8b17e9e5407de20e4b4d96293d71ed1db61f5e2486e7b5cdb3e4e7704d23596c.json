{"ast": null, "code": "import { __assign, __extends } from 'tslib';\nimport * as React from 'react';\nimport normalizeWheel from 'normalize-wheel';\n\n/**\r\n * Compute the dimension of the crop area based on media size,\r\n * aspect ratio and optionally rotation\r\n */\nfunction getCropSize(mediaWidth, mediaHeight, containerWidth, containerHeight, aspect, rotation) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  var _a = rotateSize(mediaWidth, mediaHeight, rotation),\n    width = _a.width,\n    height = _a.height;\n  var fittingWidth = Math.min(width, containerWidth);\n  var fittingHeight = Math.min(height, containerHeight);\n  if (fittingWidth > fittingHeight * aspect) {\n    return {\n      width: fittingHeight * aspect,\n      height: fittingHeight\n    };\n  }\n  return {\n    width: fittingWidth,\n    height: fittingWidth / aspect\n  };\n}\n/**\r\n * Compute media zoom.\r\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\r\n */\nfunction getMediaZoom(mediaSize) {\n  // Take the axis with more pixels to improve accuracy\n  return mediaSize.width > mediaSize.height ? mediaSize.width / mediaSize.naturalWidth : mediaSize.height / mediaSize.naturalHeight;\n}\n/**\r\n * Ensure a new media position stays in the crop area.\r\n */\nfunction restrictPosition(position, mediaSize, cropSize, zoom, rotation) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  var _a = rotateSize(mediaSize.width, mediaSize.height, rotation),\n    width = _a.width,\n    height = _a.height;\n  return {\n    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n    y: restrictPositionCoord(position.y, height, cropSize.height, zoom)\n  };\n}\nfunction restrictPositionCoord(position, mediaSize, cropSize, zoom) {\n  var maxPosition = mediaSize * zoom / 2 - cropSize / 2;\n  return clamp(position, -maxPosition, maxPosition);\n}\nfunction getDistanceBetweenPoints(pointA, pointB) {\n  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2));\n}\nfunction getRotationBetweenPoints(pointA, pointB) {\n  return Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180 / Math.PI;\n}\n/**\r\n * Compute the output cropped area of the media in percentages and pixels.\r\n * x/y are the top-left coordinates on the src media\r\n */\nfunction computeCroppedArea(crop, mediaSize, cropSize, aspect, zoom, rotation, restrictPosition) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  if (restrictPosition === void 0) {\n    restrictPosition = true;\n  }\n  // if the media is rotated by the user, we cannot limit the position anymore\n  // as it might need to be negative.\n  var limitAreaFn = restrictPosition ? limitArea : noOp;\n  var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);\n  var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);\n  // calculate the crop area in percentages\n  // in the rotated space\n  var croppedAreaPercentages = {\n    x: limitAreaFn(100, ((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width * 100),\n    y: limitAreaFn(100, ((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) / mediaBBoxSize.height * 100),\n    width: limitAreaFn(100, cropSize.width / mediaBBoxSize.width * 100 / zoom),\n    height: limitAreaFn(100, cropSize.height / mediaBBoxSize.height * 100 / zoom)\n  };\n  // we compute the pixels size naively\n  var widthInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.width, croppedAreaPercentages.width * mediaNaturalBBoxSize.width / 100));\n  var heightInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.height, croppedAreaPercentages.height * mediaNaturalBBoxSize.height / 100));\n  var isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect;\n  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n  // thus we want to compute the width from the height and aspect for accuracy.\n  // Otherwise, we compute the height from width and aspect.\n  var sizePixels = isImgWiderThanHigh ? {\n    width: Math.round(heightInPixels * aspect),\n    height: heightInPixels\n  } : {\n    width: widthInPixels,\n    height: Math.round(widthInPixels / aspect)\n  };\n  var croppedAreaPixels = __assign(__assign({}, sizePixels), {\n    x: Math.round(limitAreaFn(mediaNaturalBBoxSize.width - sizePixels.width, croppedAreaPercentages.x * mediaNaturalBBoxSize.width / 100)),\n    y: Math.round(limitAreaFn(mediaNaturalBBoxSize.height - sizePixels.height, croppedAreaPercentages.y * mediaNaturalBBoxSize.height / 100))\n  });\n  return {\n    croppedAreaPercentages: croppedAreaPercentages,\n    croppedAreaPixels: croppedAreaPixels\n  };\n}\n/**\r\n * Ensure the returned value is between 0 and max\r\n */\nfunction limitArea(max, value) {\n  return Math.min(max, Math.max(0, value));\n}\nfunction noOp(_max, value) {\n  return value;\n}\n/**\r\n * Compute crop and zoom from the croppedAreaPercentages.\r\n */\nfunction getInitialCropFromCroppedAreaPercentages(croppedAreaPercentages, mediaSize, rotation, cropSize, minZoom, maxZoom) {\n  var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);\n  // This is the inverse process of computeCroppedArea\n  var zoom = clamp(cropSize.width / mediaBBoxSize.width * (100 / croppedAreaPercentages.width), minZoom, maxZoom);\n  var crop = {\n    x: zoom * mediaBBoxSize.width / 2 - cropSize.width / 2 - mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n    y: zoom * mediaBBoxSize.height / 2 - cropSize.height / 2 - mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100)\n  };\n  return {\n    crop: crop,\n    zoom: zoom\n  };\n}\n/**\r\n * Compute zoom from the croppedAreaPixels\r\n */\nfunction getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize) {\n  var mediaZoom = getMediaZoom(mediaSize);\n  return cropSize.height > cropSize.width ? cropSize.height / (croppedAreaPixels.height * mediaZoom) : cropSize.width / (croppedAreaPixels.width * mediaZoom);\n}\n/**\r\n * Compute crop and zoom from the croppedAreaPixels\r\n */\nfunction getInitialCropFromCroppedAreaPixels(croppedAreaPixels, mediaSize, rotation, cropSize, minZoom, maxZoom) {\n  if (rotation === void 0) {\n    rotation = 0;\n  }\n  var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);\n  var zoom = clamp(getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize), minZoom, maxZoom);\n  var cropZoom = cropSize.height > cropSize.width ? cropSize.height / croppedAreaPixels.height : cropSize.width / croppedAreaPixels.width;\n  var crop = {\n    x: ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n    y: ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) * cropZoom\n  };\n  return {\n    crop: crop,\n    zoom: zoom\n  };\n}\n/**\r\n * Return the point that is the center of point a and b\r\n */\nfunction getCenter(a, b) {\n  return {\n    x: (b.x + a.x) / 2,\n    y: (b.y + a.y) / 2\n  };\n}\nfunction getRadianAngle(degreeValue) {\n  return degreeValue * Math.PI / 180;\n}\n/**\r\n * Returns the new bounding area of a rotated rectangle.\r\n */\nfunction rotateSize(width, height, rotation) {\n  var rotRad = getRadianAngle(rotation);\n  return {\n    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height)\n  };\n}\n/**\r\n * Clamp value between min and max\r\n */\nfunction clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}\n/**\r\n * Combine multiple class names into a single string.\r\n */\nfunction classNames() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return args.filter(function (value) {\n    if (typeof value === 'string' && value.length > 0) {\n      return true;\n    }\n    return false;\n  }).join(' ').trim();\n}\nvar css_248z = \".reactEasyCrop_Container {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  overflow: hidden;\\n  user-select: none;\\n  touch-action: none;\\n  cursor: move;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.reactEasyCrop_Image,\\n.reactEasyCrop_Video {\\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\\n}\\n\\n.reactEasyCrop_Contain {\\n  max-width: 100%;\\n  max-height: 100%;\\n  margin: auto;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n.reactEasyCrop_Cover_Horizontal {\\n  width: 100%;\\n  height: auto;\\n}\\n.reactEasyCrop_Cover_Vertical {\\n  width: auto;\\n  height: 100%;\\n}\\n\\n.reactEasyCrop_CropArea {\\n  position: absolute;\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  box-sizing: border-box;\\n  box-shadow: 0 0 0 9999em;\\n  color: rgba(0, 0, 0, 0.5);\\n  overflow: hidden;\\n}\\n\\n.reactEasyCrop_CropAreaRound {\\n  border-radius: 50%;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::before {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 0;\\n  bottom: 0;\\n  left: 33.33%;\\n  right: 33.33%;\\n  border-top: 0;\\n  border-bottom: 0;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::after {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 33.33%;\\n  bottom: 33.33%;\\n  left: 0;\\n  right: 0;\\n  border-left: 0;\\n  border-right: 0;\\n}\\n\";\nvar MIN_ZOOM = 1;\nvar MAX_ZOOM = 3;\nvar KEYBOARD_STEP = 1;\nvar Cropper = /** @class */function (_super) {\n  __extends(Cropper, _super);\n  function Cropper() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.cropperRef = React.createRef();\n    _this.imageRef = React.createRef();\n    _this.videoRef = React.createRef();\n    _this.containerPosition = {\n      x: 0,\n      y: 0\n    };\n    _this.containerRef = null;\n    _this.styleRef = null;\n    _this.containerRect = null;\n    _this.mediaSize = {\n      width: 0,\n      height: 0,\n      naturalWidth: 0,\n      naturalHeight: 0\n    };\n    _this.dragStartPosition = {\n      x: 0,\n      y: 0\n    };\n    _this.dragStartCrop = {\n      x: 0,\n      y: 0\n    };\n    _this.gestureZoomStart = 0;\n    _this.gestureRotationStart = 0;\n    _this.isTouching = false;\n    _this.lastPinchDistance = 0;\n    _this.lastPinchRotation = 0;\n    _this.rafDragTimeout = null;\n    _this.rafPinchTimeout = null;\n    _this.wheelTimer = null;\n    _this.currentDoc = typeof document !== 'undefined' ? document : null;\n    _this.currentWindow = typeof window !== 'undefined' ? window : null;\n    _this.resizeObserver = null;\n    _this.state = {\n      cropSize: null,\n      hasWheelJustStarted: false,\n      mediaObjectFit: undefined\n    };\n    _this.initResizeObserver = function () {\n      if (typeof window.ResizeObserver === 'undefined' || !_this.containerRef) {\n        return;\n      }\n      var isFirstResize = true;\n      _this.resizeObserver = new window.ResizeObserver(function (entries) {\n        if (isFirstResize) {\n          isFirstResize = false; // observe() is called on mount, we don't want to trigger a recompute on mount\n          return;\n        }\n        _this.computeSizes();\n      });\n      _this.resizeObserver.observe(_this.containerRef);\n    };\n    // this is to prevent Safari on iOS >= 10 to zoom the page\n    _this.preventZoomSafari = function (e) {\n      return e.preventDefault();\n    };\n    _this.cleanEvents = function () {\n      if (!_this.currentDoc) return;\n      _this.currentDoc.removeEventListener('mousemove', _this.onMouseMove);\n      _this.currentDoc.removeEventListener('mouseup', _this.onDragStopped);\n      _this.currentDoc.removeEventListener('touchmove', _this.onTouchMove);\n      _this.currentDoc.removeEventListener('touchend', _this.onDragStopped);\n      _this.currentDoc.removeEventListener('gesturechange', _this.onGestureChange);\n      _this.currentDoc.removeEventListener('gestureend', _this.onGestureEnd);\n      _this.currentDoc.removeEventListener('scroll', _this.onScroll);\n    };\n    _this.clearScrollEvent = function () {\n      if (_this.containerRef) _this.containerRef.removeEventListener('wheel', _this.onWheel);\n      if (_this.wheelTimer) {\n        clearTimeout(_this.wheelTimer);\n      }\n    };\n    _this.onMediaLoad = function () {\n      var cropSize = _this.computeSizes();\n      if (cropSize) {\n        _this.emitCropData();\n        _this.setInitialCrop(cropSize);\n      }\n      if (_this.props.onMediaLoaded) {\n        _this.props.onMediaLoaded(_this.mediaSize);\n      }\n    };\n    _this.setInitialCrop = function (cropSize) {\n      if (_this.props.initialCroppedAreaPercentages) {\n        var _a = getInitialCropFromCroppedAreaPercentages(_this.props.initialCroppedAreaPercentages, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom),\n          crop = _a.crop,\n          zoom = _a.zoom;\n        _this.props.onCropChange(crop);\n        _this.props.onZoomChange && _this.props.onZoomChange(zoom);\n      } else if (_this.props.initialCroppedAreaPixels) {\n        var _b = getInitialCropFromCroppedAreaPixels(_this.props.initialCroppedAreaPixels, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom),\n          crop = _b.crop,\n          zoom = _b.zoom;\n        _this.props.onCropChange(crop);\n        _this.props.onZoomChange && _this.props.onZoomChange(zoom);\n      }\n    };\n    _this.computeSizes = function () {\n      var _a, _b, _c, _d, _e, _f;\n      var mediaRef = _this.imageRef.current || _this.videoRef.current;\n      if (mediaRef && _this.containerRef) {\n        _this.containerRect = _this.containerRef.getBoundingClientRect();\n        _this.saveContainerPosition();\n        var containerAspect = _this.containerRect.width / _this.containerRect.height;\n        var naturalWidth = ((_a = _this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = _this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;\n        var naturalHeight = ((_c = _this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = _this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;\n        var isMediaScaledDown = mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight;\n        var mediaAspect = naturalWidth / naturalHeight;\n        // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n        // as the values they report are rounded. That will result in precision losses\n        // when calculating zoom. We use the fact that the media is positionned relative\n        // to the container. That allows us to use the container's dimensions\n        // and natural aspect ratio of the media to calculate accurate media size.\n        // However, for this to work, the container should not be rotated\n        var renderedMediaSize = void 0;\n        if (isMediaScaledDown) {\n          switch (_this.state.mediaObjectFit) {\n            default:\n            case 'contain':\n              renderedMediaSize = containerAspect > mediaAspect ? {\n                width: _this.containerRect.height * mediaAspect,\n                height: _this.containerRect.height\n              } : {\n                width: _this.containerRect.width,\n                height: _this.containerRect.width / mediaAspect\n              };\n              break;\n            case 'horizontal-cover':\n              renderedMediaSize = {\n                width: _this.containerRect.width,\n                height: _this.containerRect.width / mediaAspect\n              };\n              break;\n            case 'vertical-cover':\n              renderedMediaSize = {\n                width: _this.containerRect.height * mediaAspect,\n                height: _this.containerRect.height\n              };\n              break;\n          }\n        } else {\n          renderedMediaSize = {\n            width: mediaRef.offsetWidth,\n            height: mediaRef.offsetHeight\n          };\n        }\n        _this.mediaSize = __assign(__assign({}, renderedMediaSize), {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight\n        });\n        // set media size in the parent\n        if (_this.props.setMediaSize) {\n          _this.props.setMediaSize(_this.mediaSize);\n        }\n        var cropSize = _this.props.cropSize ? _this.props.cropSize : getCropSize(_this.mediaSize.width, _this.mediaSize.height, _this.containerRect.width, _this.containerRect.height, _this.props.aspect, _this.props.rotation);\n        if (((_e = _this.state.cropSize) === null || _e === void 0 ? void 0 : _e.height) !== cropSize.height || ((_f = _this.state.cropSize) === null || _f === void 0 ? void 0 : _f.width) !== cropSize.width) {\n          _this.props.onCropSizeChange && _this.props.onCropSizeChange(cropSize);\n        }\n        _this.setState({\n          cropSize: cropSize\n        }, _this.recomputeCropPosition);\n        // pass crop size to parent\n        if (_this.props.setCropSize) {\n          _this.props.setCropSize(cropSize);\n        }\n        return cropSize;\n      }\n    };\n    _this.saveContainerPosition = function () {\n      if (_this.containerRef) {\n        var bounds = _this.containerRef.getBoundingClientRect();\n        _this.containerPosition = {\n          x: bounds.left,\n          y: bounds.top\n        };\n      }\n    };\n    _this.onMouseDown = function (e) {\n      if (!_this.currentDoc) return;\n      e.preventDefault();\n      _this.currentDoc.addEventListener('mousemove', _this.onMouseMove);\n      _this.currentDoc.addEventListener('mouseup', _this.onDragStopped);\n      _this.saveContainerPosition();\n      _this.onDragStart(Cropper.getMousePoint(e));\n    };\n    _this.onMouseMove = function (e) {\n      return _this.onDrag(Cropper.getMousePoint(e));\n    };\n    _this.onScroll = function (e) {\n      if (!_this.currentDoc) return;\n      e.preventDefault();\n      _this.saveContainerPosition();\n    };\n    _this.onTouchStart = function (e) {\n      if (!_this.currentDoc) return;\n      _this.isTouching = true;\n      if (_this.props.onTouchRequest && !_this.props.onTouchRequest(e)) {\n        return;\n      }\n      _this.currentDoc.addEventListener('touchmove', _this.onTouchMove, {\n        passive: false\n      }); // iOS 11 now defaults to passive: true\n      _this.currentDoc.addEventListener('touchend', _this.onDragStopped);\n      _this.saveContainerPosition();\n      if (e.touches.length === 2) {\n        _this.onPinchStart(e);\n      } else if (e.touches.length === 1) {\n        _this.onDragStart(Cropper.getTouchPoint(e.touches[0]));\n      }\n    };\n    _this.onTouchMove = function (e) {\n      // Prevent whole page from scrolling on iOS.\n      e.preventDefault();\n      if (e.touches.length === 2) {\n        _this.onPinchMove(e);\n      } else if (e.touches.length === 1) {\n        _this.onDrag(Cropper.getTouchPoint(e.touches[0]));\n      }\n    };\n    _this.onGestureStart = function (e) {\n      if (!_this.currentDoc) return;\n      e.preventDefault();\n      _this.currentDoc.addEventListener('gesturechange', _this.onGestureChange);\n      _this.currentDoc.addEventListener('gestureend', _this.onGestureEnd);\n      _this.gestureZoomStart = _this.props.zoom;\n      _this.gestureRotationStart = _this.props.rotation;\n    };\n    _this.onGestureChange = function (e) {\n      e.preventDefault();\n      if (_this.isTouching) {\n        // this is to avoid conflict between gesture and touch events\n        return;\n      }\n      var point = Cropper.getMousePoint(e);\n      var newZoom = _this.gestureZoomStart - 1 + e.scale;\n      _this.setNewZoom(newZoom, point, {\n        shouldUpdatePosition: true\n      });\n      if (_this.props.onRotationChange) {\n        var newRotation = _this.gestureRotationStart + e.rotation;\n        _this.props.onRotationChange(newRotation);\n      }\n    };\n    _this.onGestureEnd = function (e) {\n      _this.cleanEvents();\n    };\n    _this.onDragStart = function (_a) {\n      var _b, _c;\n      var x = _a.x,\n        y = _a.y;\n      _this.dragStartPosition = {\n        x: x,\n        y: y\n      };\n      _this.dragStartCrop = __assign({}, _this.props.crop);\n      (_c = (_b = _this.props).onInteractionStart) === null || _c === void 0 ? void 0 : _c.call(_b);\n    };\n    _this.onDrag = function (_a) {\n      var x = _a.x,\n        y = _a.y;\n      if (!_this.currentWindow) return;\n      if (_this.rafDragTimeout) _this.currentWindow.cancelAnimationFrame(_this.rafDragTimeout);\n      _this.rafDragTimeout = _this.currentWindow.requestAnimationFrame(function () {\n        if (!_this.state.cropSize) return;\n        if (x === undefined || y === undefined) return;\n        var offsetX = x - _this.dragStartPosition.x;\n        var offsetY = y - _this.dragStartPosition.y;\n        var requestedPosition = {\n          x: _this.dragStartCrop.x + offsetX,\n          y: _this.dragStartCrop.y + offsetY\n        };\n        var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : requestedPosition;\n        _this.props.onCropChange(newPosition);\n      });\n    };\n    _this.onDragStopped = function () {\n      var _a, _b;\n      _this.isTouching = false;\n      _this.cleanEvents();\n      _this.emitCropData();\n      (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    _this.onWheel = function (e) {\n      if (!_this.currentWindow) return;\n      if (_this.props.onWheelRequest && !_this.props.onWheelRequest(e)) {\n        return;\n      }\n      e.preventDefault();\n      var point = Cropper.getMousePoint(e);\n      var pixelY = normalizeWheel(e).pixelY;\n      var newZoom = _this.props.zoom - pixelY * _this.props.zoomSpeed / 200;\n      _this.setNewZoom(newZoom, point, {\n        shouldUpdatePosition: true\n      });\n      if (!_this.state.hasWheelJustStarted) {\n        _this.setState({\n          hasWheelJustStarted: true\n        }, function () {\n          var _a, _b;\n          return (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n      }\n      if (_this.wheelTimer) {\n        clearTimeout(_this.wheelTimer);\n      }\n      _this.wheelTimer = _this.currentWindow.setTimeout(function () {\n        return _this.setState({\n          hasWheelJustStarted: false\n        }, function () {\n          var _a, _b;\n          return (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n      }, 250);\n    };\n    _this.getPointOnContainer = function (_a, containerTopLeft) {\n      var x = _a.x,\n        y = _a.y;\n      if (!_this.containerRect) {\n        throw new Error('The Cropper is not mounted');\n      }\n      return {\n        x: _this.containerRect.width / 2 - (x - containerTopLeft.x),\n        y: _this.containerRect.height / 2 - (y - containerTopLeft.y)\n      };\n    };\n    _this.getPointOnMedia = function (_a) {\n      var x = _a.x,\n        y = _a.y;\n      var _b = _this.props,\n        crop = _b.crop,\n        zoom = _b.zoom;\n      return {\n        x: (x + crop.x) / zoom,\n        y: (y + crop.y) / zoom\n      };\n    };\n    _this.setNewZoom = function (zoom, point, _a) {\n      var _b = _a === void 0 ? {} : _a,\n        _c = _b.shouldUpdatePosition,\n        shouldUpdatePosition = _c === void 0 ? true : _c;\n      if (!_this.state.cropSize || !_this.props.onZoomChange) return;\n      var newZoom = clamp(zoom, _this.props.minZoom, _this.props.maxZoom);\n      if (shouldUpdatePosition) {\n        var zoomPoint = _this.getPointOnContainer(point, _this.containerPosition);\n        var zoomTarget = _this.getPointOnMedia(zoomPoint);\n        var requestedPosition = {\n          x: zoomTarget.x * newZoom - zoomPoint.x,\n          y: zoomTarget.y * newZoom - zoomPoint.y\n        };\n        var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, newZoom, _this.props.rotation) : requestedPosition;\n        _this.props.onCropChange(newPosition);\n      }\n      _this.props.onZoomChange(newZoom);\n    };\n    _this.getCropData = function () {\n      if (!_this.state.cropSize) {\n        return null;\n      }\n      // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n      var restrictedPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;\n      return computeCroppedArea(restrictedPosition, _this.mediaSize, _this.state.cropSize, _this.getAspect(), _this.props.zoom, _this.props.rotation, _this.props.restrictPosition);\n    };\n    _this.emitCropData = function () {\n      var cropData = _this.getCropData();\n      if (!cropData) return;\n      var croppedAreaPercentages = cropData.croppedAreaPercentages,\n        croppedAreaPixels = cropData.croppedAreaPixels;\n      if (_this.props.onCropComplete) {\n        _this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels);\n      }\n      if (_this.props.onCropAreaChange) {\n        _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);\n      }\n    };\n    _this.emitCropAreaChange = function () {\n      var cropData = _this.getCropData();\n      if (!cropData) return;\n      var croppedAreaPercentages = cropData.croppedAreaPercentages,\n        croppedAreaPixels = cropData.croppedAreaPixels;\n      if (_this.props.onCropAreaChange) {\n        _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);\n      }\n    };\n    _this.recomputeCropPosition = function () {\n      if (!_this.state.cropSize) return;\n      var newPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;\n      _this.props.onCropChange(newPosition);\n      _this.emitCropData();\n    };\n    _this.onKeyDown = function (event) {\n      var _a, _b;\n      var _c = _this.props,\n        crop = _c.crop,\n        onCropChange = _c.onCropChange,\n        keyboardStep = _c.keyboardStep,\n        zoom = _c.zoom,\n        rotation = _c.rotation;\n      var step = keyboardStep;\n      if (!_this.state.cropSize) return;\n      // if the shift key is pressed, reduce the step to allow finer control\n      if (event.shiftKey) {\n        step *= 0.2;\n      }\n      var newCrop = __assign({}, crop);\n      switch (event.key) {\n        case 'ArrowUp':\n          newCrop.y -= step;\n          event.preventDefault();\n          break;\n        case 'ArrowDown':\n          newCrop.y += step;\n          event.preventDefault();\n          break;\n        case 'ArrowLeft':\n          newCrop.x -= step;\n          event.preventDefault();\n          break;\n        case 'ArrowRight':\n          newCrop.x += step;\n          event.preventDefault();\n          break;\n        default:\n          return;\n      }\n      if (_this.props.restrictPosition) {\n        newCrop = restrictPosition(newCrop, _this.mediaSize, _this.state.cropSize, zoom, rotation);\n      }\n      if (!event.repeat) {\n        (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);\n      }\n      onCropChange(newCrop);\n    };\n    _this.onKeyUp = function (event) {\n      var _a, _b;\n      switch (event.key) {\n        case 'ArrowUp':\n        case 'ArrowDown':\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          event.preventDefault();\n          break;\n        default:\n          return;\n      }\n      _this.emitCropData();\n      (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    return _this;\n  }\n  Cropper.prototype.componentDidMount = function () {\n    if (!this.currentDoc || !this.currentWindow) return;\n    if (this.containerRef) {\n      if (this.containerRef.ownerDocument) {\n        this.currentDoc = this.containerRef.ownerDocument;\n      }\n      if (this.currentDoc.defaultView) {\n        this.currentWindow = this.currentDoc.defaultView;\n      }\n      this.initResizeObserver();\n      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n      if (typeof window.ResizeObserver === 'undefined') {\n        this.currentWindow.addEventListener('resize', this.computeSizes);\n      }\n      this.props.zoomWithScroll && this.containerRef.addEventListener('wheel', this.onWheel, {\n        passive: false\n      });\n      this.containerRef.addEventListener('gesturestart', this.onGestureStart);\n    }\n    this.currentDoc.addEventListener('scroll', this.onScroll);\n    if (!this.props.disableAutomaticStylesInjection) {\n      this.styleRef = this.currentDoc.createElement('style');\n      this.styleRef.setAttribute('type', 'text/css');\n      if (this.props.nonce) {\n        this.styleRef.setAttribute('nonce', this.props.nonce);\n      }\n      this.styleRef.innerHTML = css_248z;\n      this.currentDoc.head.appendChild(this.styleRef);\n    }\n    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n    if (this.imageRef.current && this.imageRef.current.complete) {\n      this.onMediaLoad();\n    }\n    // set image and video refs in the parent if the callbacks exist\n    if (this.props.setImageRef) {\n      this.props.setImageRef(this.imageRef);\n    }\n    if (this.props.setVideoRef) {\n      this.props.setVideoRef(this.videoRef);\n    }\n    if (this.props.setCropperRef) {\n      this.props.setCropperRef(this.cropperRef);\n    }\n  };\n  Cropper.prototype.componentWillUnmount = function () {\n    var _a, _b;\n    if (!this.currentDoc || !this.currentWindow) return;\n    if (typeof window.ResizeObserver === 'undefined') {\n      this.currentWindow.removeEventListener('resize', this.computeSizes);\n    }\n    (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    if (this.containerRef) {\n      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari);\n    }\n    if (this.styleRef) {\n      (_b = this.styleRef.parentNode) === null || _b === void 0 ? void 0 : _b.removeChild(this.styleRef);\n    }\n    this.cleanEvents();\n    this.props.zoomWithScroll && this.clearScrollEvent();\n  };\n  Cropper.prototype.componentDidUpdate = function (prevProps) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n    if (prevProps.rotation !== this.props.rotation) {\n      this.computeSizes();\n      this.recomputeCropPosition();\n    } else if (prevProps.aspect !== this.props.aspect) {\n      this.computeSizes();\n    } else if (prevProps.objectFit !== this.props.objectFit) {\n      this.computeSizes();\n    } else if (prevProps.zoom !== this.props.zoom) {\n      this.recomputeCropPosition();\n    } else if (((_a = prevProps.cropSize) === null || _a === void 0 ? void 0 : _a.height) !== ((_b = this.props.cropSize) === null || _b === void 0 ? void 0 : _b.height) || ((_c = prevProps.cropSize) === null || _c === void 0 ? void 0 : _c.width) !== ((_d = this.props.cropSize) === null || _d === void 0 ? void 0 : _d.width)) {\n      this.computeSizes();\n    } else if (((_e = prevProps.crop) === null || _e === void 0 ? void 0 : _e.x) !== ((_f = this.props.crop) === null || _f === void 0 ? void 0 : _f.x) || ((_g = prevProps.crop) === null || _g === void 0 ? void 0 : _g.y) !== ((_h = this.props.crop) === null || _h === void 0 ? void 0 : _h.y)) {\n      this.emitCropAreaChange();\n    }\n    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n      this.props.zoomWithScroll ? this.containerRef.addEventListener('wheel', this.onWheel, {\n        passive: false\n      }) : this.clearScrollEvent();\n    }\n    if (prevProps.video !== this.props.video) {\n      (_j = this.videoRef.current) === null || _j === void 0 ? void 0 : _j.load();\n    }\n    var objectFit = this.getObjectFit();\n    if (objectFit !== this.state.mediaObjectFit) {\n      this.setState({\n        mediaObjectFit: objectFit\n      }, this.computeSizes);\n    }\n  };\n  Cropper.prototype.getAspect = function () {\n    var _a = this.props,\n      cropSize = _a.cropSize,\n      aspect = _a.aspect;\n    if (cropSize) {\n      return cropSize.width / cropSize.height;\n    }\n    return aspect;\n  };\n  Cropper.prototype.getObjectFit = function () {\n    var _a, _b, _c, _d;\n    if (this.props.objectFit === 'cover') {\n      var mediaRef = this.imageRef.current || this.videoRef.current;\n      if (mediaRef && this.containerRef) {\n        this.containerRect = this.containerRef.getBoundingClientRect();\n        var containerAspect = this.containerRect.width / this.containerRect.height;\n        var naturalWidth = ((_a = this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;\n        var naturalHeight = ((_c = this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;\n        var mediaAspect = naturalWidth / naturalHeight;\n        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover';\n      }\n      return 'horizontal-cover';\n    }\n    return this.props.objectFit;\n  };\n  Cropper.prototype.onPinchStart = function (e) {\n    var pointA = Cropper.getTouchPoint(e.touches[0]);\n    var pointB = Cropper.getTouchPoint(e.touches[1]);\n    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB);\n    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB);\n    this.onDragStart(getCenter(pointA, pointB));\n  };\n  Cropper.prototype.onPinchMove = function (e) {\n    var _this = this;\n    if (!this.currentDoc || !this.currentWindow) return;\n    var pointA = Cropper.getTouchPoint(e.touches[0]);\n    var pointB = Cropper.getTouchPoint(e.touches[1]);\n    var center = getCenter(pointA, pointB);\n    this.onDrag(center);\n    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout);\n    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(function () {\n      var distance = getDistanceBetweenPoints(pointA, pointB);\n      var newZoom = _this.props.zoom * (distance / _this.lastPinchDistance);\n      _this.setNewZoom(newZoom, center, {\n        shouldUpdatePosition: false\n      });\n      _this.lastPinchDistance = distance;\n      var rotation = getRotationBetweenPoints(pointA, pointB);\n      var newRotation = _this.props.rotation + (rotation - _this.lastPinchRotation);\n      _this.props.onRotationChange && _this.props.onRotationChange(newRotation);\n      _this.lastPinchRotation = rotation;\n    });\n  };\n  Cropper.prototype.render = function () {\n    var _this = this;\n    var _a;\n    var _b = this.props,\n      image = _b.image,\n      video = _b.video,\n      mediaProps = _b.mediaProps,\n      cropperProps = _b.cropperProps,\n      transform = _b.transform,\n      _c = _b.crop,\n      x = _c.x,\n      y = _c.y,\n      rotation = _b.rotation,\n      zoom = _b.zoom,\n      cropShape = _b.cropShape,\n      showGrid = _b.showGrid,\n      _d = _b.style,\n      containerStyle = _d.containerStyle,\n      cropAreaStyle = _d.cropAreaStyle,\n      mediaStyle = _d.mediaStyle,\n      _e = _b.classes,\n      containerClassName = _e.containerClassName,\n      cropAreaClassName = _e.cropAreaClassName,\n      mediaClassName = _e.mediaClassName;\n    var objectFit = (_a = this.state.mediaObjectFit) !== null && _a !== void 0 ? _a : this.getObjectFit();\n    return React.createElement(\"div\", {\n      onMouseDown: this.onMouseDown,\n      onTouchStart: this.onTouchStart,\n      ref: function ref(el) {\n        return _this.containerRef = el;\n      },\n      \"data-testid\": \"container\",\n      style: containerStyle,\n      className: classNames('reactEasyCrop_Container', containerClassName)\n    }, image ? React.createElement(\"img\", __assign({\n      alt: \"\",\n      className: classNames('reactEasyCrop_Image', objectFit === 'contain' && 'reactEasyCrop_Contain', objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal', objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical', mediaClassName)\n    }, mediaProps, {\n      src: image,\n      ref: this.imageRef,\n      style: __assign(__assign({}, mediaStyle), {\n        transform: transform || \"translate(\".concat(x, \"px, \").concat(y, \"px) rotate(\").concat(rotation, \"deg) scale(\").concat(zoom, \")\")\n      }),\n      onLoad: this.onMediaLoad\n    })) : video && React.createElement(\"video\", __assign({\n      autoPlay: true,\n      playsInline: true,\n      loop: true,\n      muted: true,\n      className: classNames('reactEasyCrop_Video', objectFit === 'contain' && 'reactEasyCrop_Contain', objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal', objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical', mediaClassName)\n    }, mediaProps, {\n      ref: this.videoRef,\n      onLoadedMetadata: this.onMediaLoad,\n      style: __assign(__assign({}, mediaStyle), {\n        transform: transform || \"translate(\".concat(x, \"px, \").concat(y, \"px) rotate(\").concat(rotation, \"deg) scale(\").concat(zoom, \")\")\n      }),\n      controls: false\n    }), (Array.isArray(video) ? video : [{\n      src: video\n    }]).map(function (item) {\n      return React.createElement(\"source\", __assign({\n        key: item.src\n      }, item));\n    })), this.state.cropSize && React.createElement(\"div\", __assign({\n      ref: this.cropperRef,\n      style: __assign(__assign({}, cropAreaStyle), {\n        width: this.state.cropSize.width,\n        height: this.state.cropSize.height\n      }),\n      tabIndex: 0,\n      onKeyDown: this.onKeyDown,\n      onKeyUp: this.onKeyUp,\n      \"data-testid\": \"cropper\",\n      className: classNames('reactEasyCrop_CropArea', cropShape === 'round' && 'reactEasyCrop_CropAreaRound', showGrid && 'reactEasyCrop_CropAreaGrid', cropAreaClassName)\n    }, cropperProps)));\n  };\n  Cropper.defaultProps = {\n    zoom: 1,\n    rotation: 0,\n    aspect: 4 / 3,\n    maxZoom: MAX_ZOOM,\n    minZoom: MIN_ZOOM,\n    cropShape: 'rect',\n    objectFit: 'contain',\n    showGrid: true,\n    style: {},\n    classes: {},\n    mediaProps: {},\n    cropperProps: {},\n    zoomSpeed: 1,\n    restrictPosition: true,\n    zoomWithScroll: true,\n    keyboardStep: KEYBOARD_STEP\n  };\n  Cropper.getMousePoint = function (e) {\n    return {\n      x: Number(e.clientX),\n      y: Number(e.clientY)\n    };\n  };\n  Cropper.getTouchPoint = function (touch) {\n    return {\n      x: Number(touch.clientX),\n      y: Number(touch.clientY)\n    };\n  };\n  return Cropper;\n}(React.Component);\nexport { Cropper as default, getInitialCropFromCroppedAreaPercentages, getInitialCropFromCroppedAreaPixels };", "map": {"version": 3, "names": ["getCropSize", "mediaWidth", "mediaHeight", "containerWidth", "containerHeight", "aspect", "rotation", "_a", "rotateSize", "width", "height", "fittingWidth", "Math", "min", "fittingHeight", "getMediaZoom", "mediaSize", "naturalWidth", "naturalHeight", "restrictPosition", "position", "cropSize", "zoom", "x", "restrictPositionCoord", "y", "maxPosition", "clamp", "getDistanceBetweenPoints", "pointA", "pointB", "sqrt", "pow", "getRotationBetweenPoints", "atan2", "PI", "computeCroppedArea", "crop", "limitAreaFn", "limitArea", "noOp", "mediaBBoxSize", "mediaNaturalBBoxSize", "croppedAreaPercentages", "widthInPixels", "round", "heightInPixels", "isImgWiderThanHigh", "sizePixels", "croppedAreaPixels", "__assign", "max", "value", "_max", "getInitialCropFromCroppedAreaPercentages", "minZoom", "max<PERSON><PERSON>", "getZoomFromCroppedAreaPixels", "mediaZoom", "getInitialCropFromCroppedAreaPixels", "cropZoom", "getCenter", "a", "b", "getRadianAngle", "degreeValue", "rotRad", "abs", "cos", "sin", "classNames", "args", "_i", "arguments", "length", "filter", "join", "trim", "MIN_ZOOM", "MAX_ZOOM", "KEYBOARD_STEP", "C<PERSON>per", "_super", "__extends", "_this", "apply", "cropperRef", "React", "createRef", "imageRef", "videoRef", "containerPosition", "containerRef", "styleRef", "containerRect", "dragStartPosition", "dragStartCrop", "gestureZoomStart", "gestureRotationStart", "isTouching", "lastPinchDistance", "lastPinchRotation", "rafDragTimeout", "rafPinchTimeout", "wheelTimer", "currentDoc", "document", "currentWindow", "window", "resizeObserver", "state", "hasWheelJustStarted", "mediaObjectFit", "undefined", "initResizeObserver", "ResizeObserver", "isFirstResize", "entries", "computeSizes", "observe", "preventZoomSafari", "e", "preventDefault", "cleanEvents", "removeEventListener", "onMouseMove", "onDragStopped", "onTouchMove", "onGestureChange", "onGestureEnd", "onScroll", "clearScrollEvent", "onWheel", "clearTimeout", "onMediaLoad", "emitCropData", "setInitialCrop", "props", "onMediaLoaded", "initialCroppedAreaPercentages", "onCropChange", "onZoomChange", "initialCroppedAreaPixels", "_b", "mediaRef", "current", "getBoundingClientRect", "saveContainerPosition", "containerAspect", "videoWidth", "_c", "_d", "videoHeight", "isMediaScaledDown", "offsetWidth", "offsetHeight", "mediaAspect", "renderedMediaSize", "setMediaSize", "_e", "_f", "onCropSizeChange", "setState", "recomputeCropPosition", "setCropSize", "bounds", "left", "top", "onMouseDown", "addEventListener", "onDragStart", "getMousePoint", "onDrag", "onTouchStart", "onTouchRequest", "passive", "touches", "onPinchStart", "getTouchPoint", "onPinchMove", "onGestureStart", "point", "newZoom", "scale", "setNewZoom", "shouldUpdatePosition", "onRotationChange", "newRotation", "onInteractionStart", "call", "cancelAnimationFrame", "requestAnimationFrame", "offsetX", "offsetY", "requestedPosition", "newPosition", "onInteractionEnd", "onWheelRequest", "pixelY", "normalizeWheel", "zoomSpeed", "setTimeout", "getPointOnContainer", "containerTopLeft", "Error", "getPointOnMedia", "zoomPoint", "zoomTarget", "getCropData", "restrictedPosition", "getAspect", "cropData", "onCropComplete", "onCropAreaChange", "emitCropAreaChange", "onKeyDown", "event", "keyboardStep", "step", "shift<PERSON>ey", "newCrop", "key", "repeat", "onKeyUp", "prototype", "componentDidMount", "ownerDocument", "defaultView", "zoomWithScroll", "disableAutomaticStylesInjection", "createElement", "setAttribute", "nonce", "innerHTML", "css_248z", "head", "append<PERSON><PERSON><PERSON>", "complete", "setImageRef", "setVideoRef", "setCropperRef", "componentWillUnmount", "disconnect", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "componentDidUpdate", "prevProps", "objectFit", "_g", "_h", "video", "_j", "load", "getObjectFit", "center", "distance", "render", "image", "mediaProps", "cropperProps", "transform", "cropShape", "showGrid", "style", "containerStyle", "cropAreaStyle", "mediaStyle", "classes", "containerClassName", "cropAreaClassName", "mediaClassName", "ref", "el", "className", "alt", "src", "concat", "onLoad", "autoPlay", "playsInline", "loop", "muted", "onLoadedMetadata", "controls", "Array", "isArray", "map", "item", "tabIndex", "defaultProps", "Number", "clientX", "clientY", "touch", "Component"], "sources": ["D:\\TDGweb\\TDG\\thedesigngrit\\node_modules\\src\\helpers.ts", "D:\\TDGweb\\TDG\\thedesigngrit\\node_modules\\src\\Cropper.tsx"], "sourcesContent": ["import { Area, MediaSize, Point, Size } from './types'\n\n/**\n * Compute the dimension of the crop area based on media size,\n * aspect ratio and optionally rotation\n */\nexport function getCropSize(\n  mediaWidth: number,\n  mediaHeight: number,\n  containerWidth: number,\n  containerHeight: number,\n  aspect: number,\n  rotation = 0\n): Size {\n  const { width, height } = rotateSize(mediaWidth, mediaHeight, rotation)\n  const fittingWidth = Math.min(width, containerWidth)\n  const fittingHeight = Math.min(height, containerHeight)\n\n  if (fittingWidth > fittingHeight * aspect) {\n    return {\n      width: fittingHeight * aspect,\n      height: fittingHeight,\n    }\n  }\n\n  return {\n    width: fittingWidth,\n    height: fittingWidth / aspect,\n  }\n}\n\n/**\n * Compute media zoom.\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\n */\nexport function getMediaZoom(mediaSize: MediaSize) {\n  // Take the axis with more pixels to improve accuracy\n  return mediaSize.width > mediaSize.height\n    ? mediaSize.width / mediaSize.naturalWidth\n    : mediaSize.height / mediaSize.naturalHeight\n}\n\n/**\n * Ensure a new media position stays in the crop area.\n */\nexport function restrictPosition(\n  position: Point,\n  mediaSize: Size,\n  cropSize: Size,\n  zoom: number,\n  rotation = 0\n): Point {\n  const { width, height } = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  return {\n    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n    y: restrictPositionCoord(position.y, height, cropSize.height, zoom),\n  }\n}\n\nfunction restrictPositionCoord(\n  position: number,\n  mediaSize: number,\n  cropSize: number,\n  zoom: number\n): number {\n  const maxPosition = (mediaSize * zoom) / 2 - cropSize / 2\n\n  return clamp(position, -maxPosition, maxPosition)\n}\n\nexport function getDistanceBetweenPoints(pointA: Point, pointB: Point) {\n  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2))\n}\n\nexport function getRotationBetweenPoints(pointA: Point, pointB: Point) {\n  return (Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180) / Math.PI\n}\n\n/**\n * Compute the output cropped area of the media in percentages and pixels.\n * x/y are the top-left coordinates on the src media\n */\nexport function computeCroppedArea(\n  crop: Point,\n  mediaSize: MediaSize,\n  cropSize: Size,\n  aspect: number,\n  zoom: number,\n  rotation = 0,\n  restrictPosition = true\n): { croppedAreaPercentages: Area; croppedAreaPixels: Area } {\n  // if the media is rotated by the user, we cannot limit the position anymore\n  // as it might need to be negative.\n  const limitAreaFn = restrictPosition ? limitArea : noOp\n\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  // calculate the crop area in percentages\n  // in the rotated space\n  const croppedAreaPercentages = {\n    x: limitAreaFn(\n      100,\n      (((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width) *\n        100\n    ),\n    y: limitAreaFn(\n      100,\n      (((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) /\n        mediaBBoxSize.height) *\n        100\n    ),\n    width: limitAreaFn(100, ((cropSize.width / mediaBBoxSize.width) * 100) / zoom),\n    height: limitAreaFn(100, ((cropSize.height / mediaBBoxSize.height) * 100) / zoom),\n  }\n\n  // we compute the pixels size naively\n  const widthInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.width,\n      (croppedAreaPercentages.width * mediaNaturalBBoxSize.width) / 100\n    )\n  )\n  const heightInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.height,\n      (croppedAreaPercentages.height * mediaNaturalBBoxSize.height) / 100\n    )\n  )\n  const isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect\n\n  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n  // thus we want to compute the width from the height and aspect for accuracy.\n  // Otherwise, we compute the height from width and aspect.\n  const sizePixels = isImgWiderThanHigh\n    ? {\n        width: Math.round(heightInPixels * aspect),\n        height: heightInPixels,\n      }\n    : {\n        width: widthInPixels,\n        height: Math.round(widthInPixels / aspect),\n      }\n\n  const croppedAreaPixels = {\n    ...sizePixels,\n    x: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.width - sizePixels.width,\n        (croppedAreaPercentages.x * mediaNaturalBBoxSize.width) / 100\n      )\n    ),\n    y: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.height - sizePixels.height,\n        (croppedAreaPercentages.y * mediaNaturalBBoxSize.height) / 100\n      )\n    ),\n  }\n\n  return { croppedAreaPercentages, croppedAreaPixels }\n}\n\n/**\n * Ensure the returned value is between 0 and max\n */\nfunction limitArea(max: number, value: number): number {\n  return Math.min(max, Math.max(0, value))\n}\n\nfunction noOp(_max: number, value: number) {\n  return value\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPercentages.\n */\nexport function getInitialCropFromCroppedAreaPercentages(\n  croppedAreaPercentages: Area,\n  mediaSize: MediaSize,\n  rotation: number,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n) {\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  // This is the inverse process of computeCroppedArea\n  const zoom = clamp(\n    (cropSize.width / mediaBBoxSize.width) * (100 / croppedAreaPercentages.width),\n    minZoom,\n    maxZoom\n  )\n\n  const crop = {\n    x:\n      (zoom * mediaBBoxSize.width) / 2 -\n      cropSize.width / 2 -\n      mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n    y:\n      (zoom * mediaBBoxSize.height) / 2 -\n      cropSize.height / 2 -\n      mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100),\n  }\n\n  return { crop, zoom }\n}\n\n/**\n * Compute zoom from the croppedAreaPixels\n */\nfunction getZoomFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  cropSize: Size\n): number {\n  const mediaZoom = getMediaZoom(mediaSize)\n\n  return cropSize.height > cropSize.width\n    ? cropSize.height / (croppedAreaPixels.height * mediaZoom)\n    : cropSize.width / (croppedAreaPixels.width * mediaZoom)\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPixels\n */\nexport function getInitialCropFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  rotation = 0,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n): { crop: Point; zoom: number } {\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  const zoom = clamp(\n    getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize),\n    minZoom,\n    maxZoom\n  )\n\n  const cropZoom =\n    cropSize.height > cropSize.width\n      ? cropSize.height / croppedAreaPixels.height\n      : cropSize.width / croppedAreaPixels.width\n\n  const crop = {\n    x:\n      ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n    y:\n      ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) *\n      cropZoom,\n  }\n  return { crop, zoom }\n}\n\n/**\n * Return the point that is the center of point a and b\n */\nexport function getCenter(a: Point, b: Point): Point {\n  return {\n    x: (b.x + a.x) / 2,\n    y: (b.y + a.y) / 2,\n  }\n}\n\nexport function getRadianAngle(degreeValue: number) {\n  return (degreeValue * Math.PI) / 180\n}\n\n/**\n * Returns the new bounding area of a rotated rectangle.\n */\nexport function rotateSize(width: number, height: number, rotation: number): Size {\n  const rotRad = getRadianAngle(rotation)\n\n  return {\n    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height),\n  }\n}\n\n/**\n * Clamp value between min and max\n */\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, min), max)\n}\n\n/**\n * Combine multiple class names into a single string.\n */\nexport function classNames(...args: (boolean | string | number | undefined | void | null)[]) {\n  return args\n    .filter((value) => {\n      if (typeof value === 'string' && value.length > 0) {\n        return true\n      }\n\n      return false\n    })\n    .join(' ')\n    .trim()\n}\n", "import * as React from 'react'\nimport normalizeWheel from 'normalize-wheel'\nimport { Area, MediaSize, Point, Size, VideoSrc } from './types'\nimport {\n  getCropSize,\n  restrictPosition,\n  getDistanceBetweenPoints,\n  getRotationBetweenPoints,\n  computeCroppedArea,\n  getCenter,\n  getInitialCropFromCroppedAreaPixels,\n  getInitialCropFromCroppedAreaPercentages,\n  classNames,\n  clamp,\n} from './helpers'\nimport cssStyles from './styles.css'\n\nexport type CropperProps = {\n  image?: string\n  video?: string | VideoSrc[]\n  transform?: string\n  crop: Point\n  zoom: number\n  rotation: number\n  aspect: number\n  minZoom: number\n  maxZoom: number\n  cropShape: 'rect' | 'round'\n  cropSize?: Size\n  objectFit?: 'contain' | 'cover' | 'horizontal-cover' | 'vertical-cover'\n  showGrid?: boolean\n  zoomSpeed: number\n  zoomWithScroll?: boolean\n  onCropChange: (location: Point) => void\n  onZoomChange?: (zoom: number) => void\n  onRotationChange?: (rotation: number) => void\n  onCropComplete?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropAreaChange?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropSizeChange?: (cropSize: Size) => void\n  onInteractionStart?: () => void\n  onInteractionEnd?: () => void\n  onMediaLoaded?: (mediaSize: MediaSize) => void\n  style: {\n    containerStyle?: React.CSSProperties\n    mediaStyle?: React.CSSProperties\n    cropAreaStyle?: React.CSSProperties\n  }\n  classes: {\n    containerClassName?: string\n    mediaClassName?: string\n    cropAreaClassName?: string\n  }\n  restrictPosition: boolean\n  mediaProps: React.ImgHTMLAttributes<HTMLElement> | React.VideoHTMLAttributes<HTMLElement>\n  cropperProps: React.HTMLAttributes<HTMLDivElement>\n  disableAutomaticStylesInjection?: boolean\n  initialCroppedAreaPixels?: Area\n  initialCroppedAreaPercentages?: Area\n  onTouchRequest?: (e: React.TouchEvent<HTMLDivElement>) => boolean\n  onWheelRequest?: (e: WheelEvent) => boolean\n  setCropperRef?: (ref: React.RefObject<HTMLDivElement>) => void\n  setImageRef?: (ref: React.RefObject<HTMLImageElement>) => void\n  setVideoRef?: (ref: React.RefObject<HTMLVideoElement>) => void\n  setMediaSize?: (size: MediaSize) => void\n  setCropSize?: (size: Size) => void\n  nonce?: string\n  keyboardStep: number\n}\n\ntype State = {\n  cropSize: Size | null\n  hasWheelJustStarted: boolean\n  mediaObjectFit: String | undefined\n}\n\nconst MIN_ZOOM = 1\nconst MAX_ZOOM = 3\nconst KEYBOARD_STEP = 1\n\ntype GestureEvent = UIEvent & {\n  rotation: number\n  scale: number\n  clientX: number\n  clientY: number\n}\n\nclass Cropper extends React.Component<CropperProps, State> {\n  static defaultProps = {\n    zoom: 1,\n    rotation: 0,\n    aspect: 4 / 3,\n    maxZoom: MAX_ZOOM,\n    minZoom: MIN_ZOOM,\n    cropShape: 'rect' as const,\n    objectFit: 'contain' as const,\n    showGrid: true,\n    style: {},\n    classes: {},\n    mediaProps: {},\n    cropperProps: {},\n    zoomSpeed: 1,\n    restrictPosition: true,\n    zoomWithScroll: true,\n    keyboardStep: KEYBOARD_STEP,\n  }\n\n  cropperRef: React.RefObject<HTMLDivElement> = React.createRef()\n  imageRef: React.RefObject<HTMLImageElement> = React.createRef()\n  videoRef: React.RefObject<HTMLVideoElement> = React.createRef()\n  containerPosition: Point = { x: 0, y: 0 }\n  containerRef: HTMLDivElement | null = null\n  styleRef: HTMLStyleElement | null = null\n  containerRect: DOMRect | null = null\n  mediaSize: MediaSize = { width: 0, height: 0, naturalWidth: 0, naturalHeight: 0 }\n  dragStartPosition: Point = { x: 0, y: 0 }\n  dragStartCrop: Point = { x: 0, y: 0 }\n  gestureZoomStart = 0\n  gestureRotationStart = 0\n  isTouching = false\n  lastPinchDistance = 0\n  lastPinchRotation = 0\n  rafDragTimeout: number | null = null\n  rafPinchTimeout: number | null = null\n  wheelTimer: number | null = null\n  currentDoc: Document | null = typeof document !== 'undefined' ? document : null\n  currentWindow: Window | null = typeof window !== 'undefined' ? window : null\n  resizeObserver: ResizeObserver | null = null\n\n  state: State = {\n    cropSize: null,\n    hasWheelJustStarted: false,\n    mediaObjectFit: undefined,\n  }\n\n  componentDidMount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (this.containerRef) {\n      if (this.containerRef.ownerDocument) {\n        this.currentDoc = this.containerRef.ownerDocument\n      }\n      if (this.currentDoc.defaultView) {\n        this.currentWindow = this.currentDoc.defaultView\n      }\n\n      this.initResizeObserver()\n      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n      if (typeof window.ResizeObserver === 'undefined') {\n        this.currentWindow.addEventListener('resize', this.computeSizes)\n      }\n      this.props.zoomWithScroll &&\n        this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n      this.containerRef.addEventListener('gesturestart', this.onGestureStart as EventListener)\n    }\n\n    this.currentDoc.addEventListener('scroll', this.onScroll)\n\n    if (!this.props.disableAutomaticStylesInjection) {\n      this.styleRef = this.currentDoc.createElement('style')\n      this.styleRef.setAttribute('type', 'text/css')\n      if (this.props.nonce) {\n        this.styleRef.setAttribute('nonce', this.props.nonce)\n      }\n      this.styleRef.innerHTML = cssStyles\n      this.currentDoc.head.appendChild(this.styleRef)\n    }\n\n    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n    if (this.imageRef.current && this.imageRef.current.complete) {\n      this.onMediaLoad()\n    }\n\n    // set image and video refs in the parent if the callbacks exist\n    if (this.props.setImageRef) {\n      this.props.setImageRef(this.imageRef)\n    }\n\n    if (this.props.setVideoRef) {\n      this.props.setVideoRef(this.videoRef)\n    }\n\n    if (this.props.setCropperRef) {\n      this.props.setCropperRef(this.cropperRef)\n    }\n  }\n\n  componentWillUnmount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (typeof window.ResizeObserver === 'undefined') {\n      this.currentWindow.removeEventListener('resize', this.computeSizes)\n    }\n    this.resizeObserver?.disconnect()\n    if (this.containerRef) {\n      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari)\n    }\n\n    if (this.styleRef) {\n      this.styleRef.parentNode?.removeChild(this.styleRef)\n    }\n\n    this.cleanEvents()\n    this.props.zoomWithScroll && this.clearScrollEvent()\n  }\n\n  componentDidUpdate(prevProps: CropperProps) {\n    if (prevProps.rotation !== this.props.rotation) {\n      this.computeSizes()\n      this.recomputeCropPosition()\n    } else if (prevProps.aspect !== this.props.aspect) {\n      this.computeSizes()\n    } else if (prevProps.objectFit !== this.props.objectFit) {\n      this.computeSizes()\n    } else if (prevProps.zoom !== this.props.zoom) {\n      this.recomputeCropPosition()\n    } else if (\n      prevProps.cropSize?.height !== this.props.cropSize?.height ||\n      prevProps.cropSize?.width !== this.props.cropSize?.width\n    ) {\n      this.computeSizes()\n    } else if (\n      prevProps.crop?.x !== this.props.crop?.x ||\n      prevProps.crop?.y !== this.props.crop?.y\n    ) {\n      this.emitCropAreaChange()\n    }\n    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n      this.props.zoomWithScroll\n        ? this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n        : this.clearScrollEvent()\n    }\n    if (prevProps.video !== this.props.video) {\n      this.videoRef.current?.load()\n    }\n\n    const objectFit = this.getObjectFit()\n    if (objectFit !== this.state.mediaObjectFit) {\n      this.setState({ mediaObjectFit: objectFit }, this.computeSizes)\n    }\n  }\n\n  initResizeObserver = () => {\n    if (typeof window.ResizeObserver === 'undefined' || !this.containerRef) {\n      return\n    }\n    let isFirstResize = true\n    this.resizeObserver = new window.ResizeObserver((entries) => {\n      if (isFirstResize) {\n        isFirstResize = false // observe() is called on mount, we don't want to trigger a recompute on mount\n        return\n      }\n      this.computeSizes()\n    })\n    this.resizeObserver.observe(this.containerRef)\n  }\n\n  // this is to prevent Safari on iOS >= 10 to zoom the page\n  preventZoomSafari = (e: Event) => e.preventDefault()\n\n  cleanEvents = () => {\n    if (!this.currentDoc) return\n    this.currentDoc.removeEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.removeEventListener('mouseup', this.onDragStopped)\n    this.currentDoc.removeEventListener('touchmove', this.onTouchMove)\n    this.currentDoc.removeEventListener('touchend', this.onDragStopped)\n    this.currentDoc.removeEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.removeEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.currentDoc.removeEventListener('scroll', this.onScroll)\n  }\n\n  clearScrollEvent = () => {\n    if (this.containerRef) this.containerRef.removeEventListener('wheel', this.onWheel)\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n  }\n\n  onMediaLoad = () => {\n    const cropSize = this.computeSizes()\n\n    if (cropSize) {\n      this.emitCropData()\n      this.setInitialCrop(cropSize)\n    }\n\n    if (this.props.onMediaLoaded) {\n      this.props.onMediaLoaded(this.mediaSize)\n    }\n  }\n\n  setInitialCrop = (cropSize: Size) => {\n    if (this.props.initialCroppedAreaPercentages) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPercentages(\n        this.props.initialCroppedAreaPercentages,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    } else if (this.props.initialCroppedAreaPixels) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPixels(\n        this.props.initialCroppedAreaPixels,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    }\n  }\n\n  getAspect() {\n    const { cropSize, aspect } = this.props\n    if (cropSize) {\n      return cropSize.width / cropSize.height\n    }\n    return aspect\n  }\n\n  getObjectFit() {\n    if (this.props.objectFit === 'cover') {\n      const mediaRef = this.imageRef.current || this.videoRef.current\n\n      if (mediaRef && this.containerRef) {\n        this.containerRect = this.containerRef.getBoundingClientRect()\n        const containerAspect = this.containerRect.width / this.containerRect.height\n        const naturalWidth =\n          this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n        const naturalHeight =\n          this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n        const mediaAspect = naturalWidth / naturalHeight\n\n        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover'\n      }\n      return 'horizontal-cover'\n    }\n\n    return this.props.objectFit\n  }\n\n  computeSizes = () => {\n    const mediaRef = this.imageRef.current || this.videoRef.current\n\n    if (mediaRef && this.containerRef) {\n      this.containerRect = this.containerRef.getBoundingClientRect()\n      this.saveContainerPosition()\n      const containerAspect = this.containerRect.width / this.containerRect.height\n      const naturalWidth =\n        this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n      const naturalHeight =\n        this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n      const isMediaScaledDown =\n        mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight\n      const mediaAspect = naturalWidth / naturalHeight\n\n      // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n      // as the values they report are rounded. That will result in precision losses\n      // when calculating zoom. We use the fact that the media is positionned relative\n      // to the container. That allows us to use the container's dimensions\n      // and natural aspect ratio of the media to calculate accurate media size.\n      // However, for this to work, the container should not be rotated\n      let renderedMediaSize: Size\n\n      if (isMediaScaledDown) {\n        switch (this.state.mediaObjectFit) {\n          default:\n          case 'contain':\n            renderedMediaSize =\n              containerAspect > mediaAspect\n                ? {\n                    width: this.containerRect.height * mediaAspect,\n                    height: this.containerRect.height,\n                  }\n                : {\n                    width: this.containerRect.width,\n                    height: this.containerRect.width / mediaAspect,\n                  }\n            break\n          case 'horizontal-cover':\n            renderedMediaSize = {\n              width: this.containerRect.width,\n              height: this.containerRect.width / mediaAspect,\n            }\n            break\n          case 'vertical-cover':\n            renderedMediaSize = {\n              width: this.containerRect.height * mediaAspect,\n              height: this.containerRect.height,\n            }\n            break\n        }\n      } else {\n        renderedMediaSize = {\n          width: mediaRef.offsetWidth,\n          height: mediaRef.offsetHeight,\n        }\n      }\n\n      this.mediaSize = {\n        ...renderedMediaSize,\n        naturalWidth,\n        naturalHeight,\n      }\n\n      // set media size in the parent\n      if (this.props.setMediaSize) {\n        this.props.setMediaSize(this.mediaSize)\n      }\n\n      const cropSize = this.props.cropSize\n        ? this.props.cropSize\n        : getCropSize(\n            this.mediaSize.width,\n            this.mediaSize.height,\n            this.containerRect.width,\n            this.containerRect.height,\n            this.props.aspect,\n            this.props.rotation\n          )\n\n      if (\n        this.state.cropSize?.height !== cropSize.height ||\n        this.state.cropSize?.width !== cropSize.width\n      ) {\n        this.props.onCropSizeChange && this.props.onCropSizeChange(cropSize)\n      }\n      this.setState({ cropSize }, this.recomputeCropPosition)\n      // pass crop size to parent\n      if (this.props.setCropSize) {\n        this.props.setCropSize(cropSize)\n      }\n\n      return cropSize\n    }\n  }\n\n  saveContainerPosition = () => {\n    if (this.containerRef) {\n      const bounds = this.containerRef.getBoundingClientRect()\n      this.containerPosition = { x: bounds.left, y: bounds.top }\n    }\n  }\n\n  static getMousePoint = (e: MouseEvent | React.MouseEvent | GestureEvent) => ({\n    x: Number(e.clientX),\n    y: Number(e.clientY),\n  })\n\n  static getTouchPoint = (touch: Touch | React.Touch) => ({\n    x: Number(touch.clientX),\n    y: Number(touch.clientY),\n  })\n\n  onMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.addEventListener('mouseup', this.onDragStopped)\n    this.saveContainerPosition()\n    this.onDragStart(Cropper.getMousePoint(e))\n  }\n\n  onMouseMove = (e: MouseEvent) => this.onDrag(Cropper.getMousePoint(e))\n\n  onScroll = (e: Event) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.saveContainerPosition()\n  }\n\n  onTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {\n    if (!this.currentDoc) return\n    this.isTouching = true\n    if (this.props.onTouchRequest && !this.props.onTouchRequest(e)) {\n      return\n    }\n\n    this.currentDoc.addEventListener('touchmove', this.onTouchMove, { passive: false }) // iOS 11 now defaults to passive: true\n    this.currentDoc.addEventListener('touchend', this.onDragStopped)\n\n    this.saveContainerPosition()\n\n    if (e.touches.length === 2) {\n      this.onPinchStart(e)\n    } else if (e.touches.length === 1) {\n      this.onDragStart(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onTouchMove = (e: TouchEvent) => {\n    // Prevent whole page from scrolling on iOS.\n    e.preventDefault()\n    if (e.touches.length === 2) {\n      this.onPinchMove(e)\n    } else if (e.touches.length === 1) {\n      this.onDrag(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onGestureStart = (e: GestureEvent) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.addEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.gestureZoomStart = this.props.zoom\n    this.gestureRotationStart = this.props.rotation\n  }\n\n  onGestureChange = (e: GestureEvent) => {\n    e.preventDefault()\n    if (this.isTouching) {\n      // this is to avoid conflict between gesture and touch events\n      return\n    }\n\n    const point = Cropper.getMousePoint(e)\n    const newZoom = this.gestureZoomStart - 1 + e.scale\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n    if (this.props.onRotationChange) {\n      const newRotation = this.gestureRotationStart + e.rotation\n      this.props.onRotationChange(newRotation)\n    }\n  }\n\n  onGestureEnd = (e: GestureEvent) => {\n    this.cleanEvents()\n  }\n\n  onDragStart = ({ x, y }: Point) => {\n    this.dragStartPosition = { x, y }\n    this.dragStartCrop = { ...this.props.crop }\n    this.props.onInteractionStart?.()\n  }\n\n  onDrag = ({ x, y }: Point) => {\n    if (!this.currentWindow) return\n    if (this.rafDragTimeout) this.currentWindow.cancelAnimationFrame(this.rafDragTimeout)\n\n    this.rafDragTimeout = this.currentWindow.requestAnimationFrame(() => {\n      if (!this.state.cropSize) return\n      if (x === undefined || y === undefined) return\n      const offsetX = x - this.dragStartPosition.x\n      const offsetY = y - this.dragStartPosition.y\n      const requestedPosition = {\n        x: this.dragStartCrop.x + offsetX,\n        y: this.dragStartCrop.y + offsetY,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            this.props.zoom,\n            this.props.rotation\n          )\n        : requestedPosition\n      this.props.onCropChange(newPosition)\n    })\n  }\n\n  onDragStopped = () => {\n    this.isTouching = false\n    this.cleanEvents()\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  onPinchStart(e: React.TouchEvent<HTMLDivElement>) {\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB)\n    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB)\n    this.onDragStart(getCenter(pointA, pointB))\n  }\n\n  onPinchMove(e: TouchEvent) {\n    if (!this.currentDoc || !this.currentWindow) return\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    const center = getCenter(pointA, pointB)\n    this.onDrag(center)\n\n    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout)\n    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(() => {\n      const distance = getDistanceBetweenPoints(pointA, pointB)\n      const newZoom = this.props.zoom * (distance / this.lastPinchDistance)\n      this.setNewZoom(newZoom, center, { shouldUpdatePosition: false })\n      this.lastPinchDistance = distance\n\n      const rotation = getRotationBetweenPoints(pointA, pointB)\n      const newRotation = this.props.rotation + (rotation - this.lastPinchRotation)\n      this.props.onRotationChange && this.props.onRotationChange(newRotation)\n      this.lastPinchRotation = rotation\n    })\n  }\n\n  onWheel = (e: WheelEvent) => {\n    if (!this.currentWindow) return\n    if (this.props.onWheelRequest && !this.props.onWheelRequest(e)) {\n      return\n    }\n\n    e.preventDefault()\n    const point = Cropper.getMousePoint(e)\n    const { pixelY } = normalizeWheel(e)\n    const newZoom = this.props.zoom - (pixelY * this.props.zoomSpeed) / 200\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n\n    if (!this.state.hasWheelJustStarted) {\n      this.setState({ hasWheelJustStarted: true }, () => this.props.onInteractionStart?.())\n    }\n\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n    this.wheelTimer = this.currentWindow.setTimeout(\n      () => this.setState({ hasWheelJustStarted: false }, () => this.props.onInteractionEnd?.()),\n      250\n    )\n  }\n\n  getPointOnContainer = ({ x, y }: Point, containerTopLeft: Point): Point => {\n    if (!this.containerRect) {\n      throw new Error('The Cropper is not mounted')\n    }\n    return {\n      x: this.containerRect.width / 2 - (x - containerTopLeft.x),\n      y: this.containerRect.height / 2 - (y - containerTopLeft.y),\n    }\n  }\n\n  getPointOnMedia = ({ x, y }: Point) => {\n    const { crop, zoom } = this.props\n    return {\n      x: (x + crop.x) / zoom,\n      y: (y + crop.y) / zoom,\n    }\n  }\n\n  setNewZoom = (zoom: number, point: Point, { shouldUpdatePosition = true } = {}) => {\n    if (!this.state.cropSize || !this.props.onZoomChange) return\n\n    const newZoom = clamp(zoom, this.props.minZoom, this.props.maxZoom)\n\n    if (shouldUpdatePosition) {\n      const zoomPoint = this.getPointOnContainer(point, this.containerPosition)\n      const zoomTarget = this.getPointOnMedia(zoomPoint)\n      const requestedPosition = {\n        x: zoomTarget.x * newZoom - zoomPoint.x,\n        y: zoomTarget.y * newZoom - zoomPoint.y,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            newZoom,\n            this.props.rotation\n          )\n        : requestedPosition\n\n      this.props.onCropChange(newPosition)\n    }\n    this.props.onZoomChange(newZoom)\n  }\n\n  getCropData = () => {\n    if (!this.state.cropSize) {\n      return null\n    }\n\n    // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n    const restrictedPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n    return computeCroppedArea(\n      restrictedPosition,\n      this.mediaSize,\n      this.state.cropSize,\n      this.getAspect(),\n      this.props.zoom,\n      this.props.rotation,\n      this.props.restrictPosition\n    )\n  }\n\n  emitCropData = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropComplete) {\n      this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels)\n    }\n\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  emitCropAreaChange = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  recomputeCropPosition = () => {\n    if (!this.state.cropSize) return\n\n    const newPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n\n    this.props.onCropChange(newPosition)\n    this.emitCropData()\n  }\n\n  onKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    const { crop, onCropChange, keyboardStep, zoom, rotation } = this.props\n    let step = keyboardStep\n\n    if (!this.state.cropSize) return\n\n    // if the shift key is pressed, reduce the step to allow finer control\n    if (event.shiftKey) {\n      step *= 0.2\n    }\n\n    let newCrop = { ...crop }\n\n    switch (event.key) {\n      case 'ArrowUp':\n        newCrop.y -= step\n        event.preventDefault()\n        break\n      case 'ArrowDown':\n        newCrop.y += step\n        event.preventDefault()\n        break\n      case 'ArrowLeft':\n        newCrop.x -= step\n        event.preventDefault()\n        break\n      case 'ArrowRight':\n        newCrop.x += step\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n\n    if (this.props.restrictPosition) {\n      newCrop = restrictPosition(newCrop, this.mediaSize, this.state.cropSize, zoom, rotation)\n    }\n\n    if (!event.repeat) {\n      this.props.onInteractionStart?.()\n    }\n\n    onCropChange(newCrop)\n  }\n\n  onKeyUp = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    switch (event.key) {\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  render() {\n    const {\n      image,\n      video,\n      mediaProps,\n      cropperProps,\n      transform,\n      crop: { x, y },\n      rotation,\n      zoom,\n      cropShape,\n      showGrid,\n      style: { containerStyle, cropAreaStyle, mediaStyle },\n      classes: { containerClassName, cropAreaClassName, mediaClassName },\n    } = this.props\n\n    const objectFit = this.state.mediaObjectFit ?? this.getObjectFit()\n\n    return (\n      <div\n        onMouseDown={this.onMouseDown}\n        onTouchStart={this.onTouchStart}\n        ref={(el) => (this.containerRef = el)}\n        data-testid=\"container\"\n        style={containerStyle}\n        className={classNames('reactEasyCrop_Container', containerClassName)}\n      >\n        {image ? (\n          <img\n            alt=\"\"\n            className={classNames(\n              'reactEasyCrop_Image',\n              objectFit === 'contain' && 'reactEasyCrop_Contain',\n              objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n              objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n              mediaClassName\n            )}\n            {...(mediaProps as React.ImgHTMLAttributes<HTMLElement>)}\n            src={image}\n            ref={this.imageRef}\n            style={{\n              ...mediaStyle,\n              transform:\n                transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n            }}\n            onLoad={this.onMediaLoad}\n          />\n        ) : (\n          video && (\n            <video\n              autoPlay\n              playsInline\n              loop\n              muted={true}\n              className={classNames(\n                'reactEasyCrop_Video',\n                objectFit === 'contain' && 'reactEasyCrop_Contain',\n                objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n                objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n                mediaClassName\n              )}\n              {...mediaProps}\n              ref={this.videoRef}\n              onLoadedMetadata={this.onMediaLoad}\n              style={{\n                ...mediaStyle,\n                transform:\n                  transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n              }}\n              controls={false}\n            >\n              {(Array.isArray(video) ? video : [{ src: video }]).map((item) => (\n                <source key={item.src} {...item} />\n              ))}\n            </video>\n          )\n        )}\n        {this.state.cropSize && (\n          <div\n            ref={this.cropperRef}\n            style={{\n              ...cropAreaStyle,\n              width: this.state.cropSize.width,\n              height: this.state.cropSize.height,\n            }}\n            tabIndex={0}\n            onKeyDown={this.onKeyDown}\n            onKeyUp={this.onKeyUp}\n            data-testid=\"cropper\"\n            className={classNames(\n              'reactEasyCrop_CropArea',\n              cropShape === 'round' && 'reactEasyCrop_CropAreaRound',\n              showGrid && 'reactEasyCrop_CropAreaGrid',\n              cropAreaClassName\n            )}\n            {...cropperProps}\n          />\n        )}\n      </div>\n    )\n  }\n}\n\nexport default Cropper\n"], "mappings": ";;;;AAEA;;;AAGG;AACa,SAAAA,WAAWA,CACzBC,UAAkB,EAClBC,WAAmB,EACnBC,cAAsB,EACtBC,eAAuB,EACvBC,MAAc,EACdC,QAAY;EAAZ,IAAAA,QAAA;IAAAA,QAAY;EAAA;EAEN,IAAAC,EAAoB,GAAAC,UAAU,CAACP,UAAU,EAAEC,WAAW,EAAEI,QAAQ,CAAC;IAA/DG,KAAK,GAAAF,EAAA,CAAAE,KAAA;IAAEC,MAAM,GAAAH,EAAA,CAAAG,MAAkD;EACvE,IAAMC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,EAAEN,cAAc,CAAC;EACpD,IAAMW,aAAa,GAAGF,IAAI,CAACC,GAAG,CAACH,MAAM,EAAEN,eAAe,CAAC;EAEvD,IAAIO,YAAY,GAAGG,aAAa,GAAGT,MAAM,EAAE;IACzC,OAAO;MACLI,KAAK,EAAEK,aAAa,GAAGT,MAAM;MAC7BK,MAAM,EAAEI;KACT;EACF;EAED,OAAO;IACLL,KAAK,EAAEE,YAAY;IACnBD,MAAM,EAAEC,YAAY,GAAGN;GACxB;AACH;AAEA;;;AAGG;AACG,SAAUU,YAAYA,CAACC,SAAoB;EAC/C;EACA,OAAOA,SAAS,CAACP,KAAK,GAAGO,SAAS,CAACN,MAAM,GACrCM,SAAS,CAACP,KAAK,GAAGO,SAAS,CAACC,YAAY,GACxCD,SAAS,CAACN,MAAM,GAAGM,SAAS,CAACE,aAAa;AAChD;AAEA;;AAEG;AACG,SAAUC,gBAAgBA,CAC9BC,QAAe,EACfJ,SAAe,EACfK,QAAc,EACdC,IAAY,EACZhB,QAAY;EAAZ,IAAAA,QAAA;IAAAA,QAAY;EAAA;EAEN,IAAAC,EAAA,GAAoBC,UAAU,CAACQ,SAAS,CAACP,KAAK,EAAEO,SAAS,CAACN,MAAM,EAAEJ,QAAQ,CAAC;IAAzEG,KAAK,GAAAF,EAAA,CAAAE,KAAA;IAAEC,MAAM,GAAAH,EAAA,CAAAG,MAA4D;EAEjF,OAAO;IACLa,CAAC,EAAEC,qBAAqB,CAACJ,QAAQ,CAACG,CAAC,EAAEd,KAAK,EAAEY,QAAQ,CAACZ,KAAK,EAAEa,IAAI,CAAC;IACjEG,CAAC,EAAED,qBAAqB,CAACJ,QAAQ,CAACK,CAAC,EAAEf,MAAM,EAAEW,QAAQ,CAACX,MAAM,EAAEY,IAAI;GACnE;AACH;AAEA,SAASE,qBAAqBA,CAC5BJ,QAAgB,EAChBJ,SAAiB,EACjBK,QAAgB,EAChBC,IAAY;EAEZ,IAAMI,WAAW,GAAIV,SAAS,GAAGM,IAAI,GAAI,CAAC,GAAGD,QAAQ,GAAG,CAAC;EAEzD,OAAOM,KAAK,CAACP,QAAQ,EAAE,CAACM,WAAW,EAAEA,WAAW,CAAC;AACnD;AAEgB,SAAAE,wBAAwBA,CAACC,MAAa,EAAEC,MAAa;EACnE,OAAOlB,IAAI,CAACmB,IAAI,CAACnB,IAAI,CAACoB,GAAG,CAACH,MAAM,CAACJ,CAAC,GAAGK,MAAM,CAACL,CAAC,EAAE,CAAC,CAAC,GAAGb,IAAI,CAACoB,GAAG,CAACH,MAAM,CAACN,CAAC,GAAGO,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC,CAAC;AACvF;AAEgB,SAAAU,wBAAwBA,CAACJ,MAAa,EAAEC,MAAa;EACnE,OAAQlB,IAAI,CAACsB,KAAK,CAACJ,MAAM,CAACL,CAAC,GAAGI,MAAM,CAACJ,CAAC,EAAEK,MAAM,CAACP,CAAC,GAAGM,MAAM,CAACN,CAAC,CAAC,GAAG,GAAG,GAAIX,IAAI,CAACuB,EAAE;AAC/E;AAEA;;;AAGG;AACa,SAAAC,kBAAkBA,CAChCC,IAAW,EACXrB,SAAoB,EACpBK,QAAc,EACdhB,MAAc,EACdiB,IAAY,EACZhB,QAAY,EACZa,gBAAuB;EADvB,IAAAb,QAAA;IAAAA,QAAY;EAAA;EACZ,IAAAa,gBAAA;IAAAA,gBAAuB;EAAA;EAEvB;EACA;EACA,IAAMmB,WAAW,GAAGnB,gBAAgB,GAAGoB,SAAS,GAAGC,IAAI;EAEvD,IAAMC,aAAa,GAAGjC,UAAU,CAACQ,SAAS,CAACP,KAAK,EAAEO,SAAS,CAACN,MAAM,EAAEJ,QAAQ,CAAC;EAC7E,IAAMoC,oBAAoB,GAAGlC,UAAU,CAACQ,SAAS,CAACC,YAAY,EAAED,SAAS,CAACE,aAAa,EAAEZ,QAAQ,CAAC;EAElG;EACA;EACA,IAAMqC,sBAAsB,GAAG;IAC7BpB,CAAC,EAAEe,WAAW,CACZ,GAAG,EACF,CAAC,CAACG,aAAa,CAAChC,KAAK,GAAGY,QAAQ,CAACZ,KAAK,GAAGa,IAAI,IAAI,CAAC,GAAGe,IAAI,CAACd,CAAC,GAAGD,IAAI,IAAImB,aAAa,CAAChC,KAAK,GACxF,GAAG,CACN;IACDgB,CAAC,EAAEa,WAAW,CACZ,GAAG,EACF,CAAC,CAACG,aAAa,CAAC/B,MAAM,GAAGW,QAAQ,CAACX,MAAM,GAAGY,IAAI,IAAI,CAAC,GAAGe,IAAI,CAACZ,CAAC,GAAGH,IAAI,IACnEmB,aAAa,CAAC/B,MAAM,GACpB,GAAG,CACN;IACDD,KAAK,EAAE6B,WAAW,CAAC,GAAG,EAAIjB,QAAQ,CAACZ,KAAK,GAAGgC,aAAa,CAAChC,KAAK,GAAI,GAAG,GAAIa,IAAI,CAAC;IAC9EZ,MAAM,EAAE4B,WAAW,CAAC,GAAG,EAAIjB,QAAQ,CAACX,MAAM,GAAG+B,aAAa,CAAC/B,MAAM,GAAI,GAAG,GAAIY,IAAI;GACjF;EAED;EACA,IAAMsB,aAAa,GAAGhC,IAAI,CAACiC,KAAK,CAC9BP,WAAW,CACTI,oBAAoB,CAACjC,KAAK,EACzBkC,sBAAsB,CAAClC,KAAK,GAAGiC,oBAAoB,CAACjC,KAAK,GAAI,GAAG,CAClE,CACF;EACD,IAAMqC,cAAc,GAAGlC,IAAI,CAACiC,KAAK,CAC/BP,WAAW,CACTI,oBAAoB,CAAChC,MAAM,EAC1BiC,sBAAsB,CAACjC,MAAM,GAAGgC,oBAAoB,CAAChC,MAAM,GAAI,GAAG,CACpE,CACF;EACD,IAAMqC,kBAAkB,GAAGL,oBAAoB,CAACjC,KAAK,IAAIiC,oBAAoB,CAAChC,MAAM,GAAGL,MAAM;EAE7F;EACA;EACA;EACA;EACA,IAAM2C,UAAU,GAAGD,kBAAkB,GACjC;IACEtC,KAAK,EAAEG,IAAI,CAACiC,KAAK,CAACC,cAAc,GAAGzC,MAAM,CAAC;IAC1CK,MAAM,EAAEoC;EACT,IACD;IACErC,KAAK,EAAEmC,aAAa;IACpBlC,MAAM,EAAEE,IAAI,CAACiC,KAAK,CAACD,aAAa,GAAGvC,MAAM;GAC1C;EAEL,IAAM4C,iBAAiB,GAAAC,QAAA,CAAAA,QAAA,KAClBF,UAAU;IACbzB,CAAC,EAAEX,IAAI,CAACiC,KAAK,CACXP,WAAW,CACTI,oBAAoB,CAACjC,KAAK,GAAGuC,UAAU,CAACvC,KAAK,EAC5CkC,sBAAsB,CAACpB,CAAC,GAAGmB,oBAAoB,CAACjC,KAAK,GAAI,GAAG,CAC9D,CACF;IACDgB,CAAC,EAAEb,IAAI,CAACiC,KAAK,CACXP,WAAW,CACTI,oBAAoB,CAAChC,MAAM,GAAGsC,UAAU,CAACtC,MAAM,EAC9CiC,sBAAsB,CAAClB,CAAC,GAAGiB,oBAAoB,CAAChC,MAAM,GAAI,GAAG,CAC/D;IAEJ;EAED,OAAO;IAAEiC,sBAAsB,EAAAA,sBAAA;IAAEM,iBAAiB,EAAAA;GAAE;AACtD;AAEA;;AAEG;AACH,SAASV,SAASA,CAACY,GAAW,EAAEC,KAAa;EAC3C,OAAOxC,IAAI,CAACC,GAAG,CAACsC,GAAG,EAAEvC,IAAI,CAACuC,GAAG,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC;AAC1C;AAEA,SAASZ,IAAIA,CAACa,IAAY,EAAED,KAAa;EACvC,OAAOA,KAAK;AACd;AAEA;;AAEG;AACa,SAAAE,wCAAwCA,CACtDX,sBAA4B,EAC5B3B,SAAoB,EACpBV,QAAgB,EAChBe,QAAc,EACdkC,OAAe,EACfC,OAAe;EAEf,IAAMf,aAAa,GAAGjC,UAAU,CAACQ,SAAS,CAACP,KAAK,EAAEO,SAAS,CAACN,MAAM,EAAEJ,QAAQ,CAAC;EAE7E;EACA,IAAMgB,IAAI,GAAGK,KAAK,CACfN,QAAQ,CAACZ,KAAK,GAAGgC,aAAa,CAAChC,KAAK,IAAK,GAAG,GAAGkC,sBAAsB,CAAClC,KAAK,CAAC,EAC7E8C,OAAO,EACPC,OAAO,CACR;EAED,IAAMnB,IAAI,GAAG;IACXd,CAAC,EACED,IAAI,GAAGmB,aAAa,CAAChC,KAAK,GAAI,CAAC,GAChCY,QAAQ,CAACZ,KAAK,GAAG,CAAC,GAClBgC,aAAa,CAAChC,KAAK,GAAGa,IAAI,IAAIqB,sBAAsB,CAACpB,CAAC,GAAG,GAAG,CAAC;IAC/DE,CAAC,EACEH,IAAI,GAAGmB,aAAa,CAAC/B,MAAM,GAAI,CAAC,GACjCW,QAAQ,CAACX,MAAM,GAAG,CAAC,GACnB+B,aAAa,CAAC/B,MAAM,GAAGY,IAAI,IAAIqB,sBAAsB,CAAClB,CAAC,GAAG,GAAG;GAChE;EAED,OAAO;IAAEY,IAAI,EAAAA,IAAA;IAAEf,IAAI,EAAAA;GAAE;AACvB;AAEA;;AAEG;AACH,SAASmC,4BAA4BA,CACnCR,iBAAuB,EACvBjC,SAAoB,EACpBK,QAAc;EAEd,IAAMqC,SAAS,GAAG3C,YAAY,CAACC,SAAS,CAAC;EAEzC,OAAOK,QAAQ,CAACX,MAAM,GAAGW,QAAQ,CAACZ,KAAK,GACnCY,QAAQ,CAACX,MAAM,IAAIuC,iBAAiB,CAACvC,MAAM,GAAGgD,SAAS,CAAC,GACxDrC,QAAQ,CAACZ,KAAK,IAAIwC,iBAAiB,CAACxC,KAAK,GAAGiD,SAAS,CAAC;AAC5D;AAEA;;AAEG;AACa,SAAAC,mCAAmCA,CACjDV,iBAAuB,EACvBjC,SAAoB,EACpBV,QAAY,EACZe,QAAc,EACdkC,OAAe,EACfC,OAAe;EAHf,IAAAlD,QAAA;IAAAA,QAAY;EAAA;EAKZ,IAAMoC,oBAAoB,GAAGlC,UAAU,CAACQ,SAAS,CAACC,YAAY,EAAED,SAAS,CAACE,aAAa,EAAEZ,QAAQ,CAAC;EAElG,IAAMgB,IAAI,GAAGK,KAAK,CAChB8B,4BAA4B,CAACR,iBAAiB,EAAEjC,SAAS,EAAEK,QAAQ,CAAC,EACpEkC,OAAO,EACPC,OAAO,CACR;EAED,IAAMI,QAAQ,GACZvC,QAAQ,CAACX,MAAM,GAAGW,QAAQ,CAACZ,KAAK,GAC5BY,QAAQ,CAACX,MAAM,GAAGuC,iBAAiB,CAACvC,MAAM,GAC1CW,QAAQ,CAACZ,KAAK,GAAGwC,iBAAiB,CAACxC,KAAK;EAE9C,IAAM4B,IAAI,GAAG;IACXd,CAAC,EACC,CAAC,CAACmB,oBAAoB,CAACjC,KAAK,GAAGwC,iBAAiB,CAACxC,KAAK,IAAI,CAAC,GAAGwC,iBAAiB,CAAC1B,CAAC,IAAIqC,QAAQ;IAC/FnC,CAAC,EACC,CAAC,CAACiB,oBAAoB,CAAChC,MAAM,GAAGuC,iBAAiB,CAACvC,MAAM,IAAI,CAAC,GAAGuC,iBAAiB,CAACxB,CAAC,IACnFmC;GACH;EACD,OAAO;IAAEvB,IAAI,EAAAA,IAAA;IAAEf,IAAI,EAAAA;GAAE;AACvB;AAEA;;AAEG;AACa,SAAAuC,SAASA,CAACC,CAAQ,EAAEC,CAAQ;EAC1C,OAAO;IACLxC,CAAC,EAAE,CAACwC,CAAC,CAACxC,CAAC,GAAGuC,CAAC,CAACvC,CAAC,IAAI,CAAC;IAClBE,CAAC,EAAE,CAACsC,CAAC,CAACtC,CAAC,GAAGqC,CAAC,CAACrC,CAAC,IAAI;GAClB;AACH;AAEM,SAAUuC,cAAcA,CAACC,WAAmB;EAChD,OAAQA,WAAW,GAAGrD,IAAI,CAACuB,EAAE,GAAI,GAAG;AACtC;AAEA;;AAEG;SACa3B,UAAUA,CAACC,KAAa,EAAEC,MAAc,EAAEJ,QAAgB;EACxE,IAAM4D,MAAM,GAAGF,cAAc,CAAC1D,QAAQ,CAAC;EAEvC,OAAO;IACLG,KAAK,EAAEG,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACwD,GAAG,CAACF,MAAM,CAAC,GAAGzD,KAAK,CAAC,GAAGG,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACyD,GAAG,CAACH,MAAM,CAAC,GAAGxD,MAAM,CAAC;IAC/EA,MAAM,EAAEE,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACyD,GAAG,CAACH,MAAM,CAAC,GAAGzD,KAAK,CAAC,GAAGG,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACwD,GAAG,CAACF,MAAM,CAAC,GAAGxD,MAAM;GAChF;AACH;AAEA;;AAEG;SACaiB,KAAKA,CAACyB,KAAa,EAAEvC,GAAW,EAAEsC,GAAW;EAC3D,OAAOvC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACuC,GAAG,CAACC,KAAK,EAAEvC,GAAG,CAAC,EAAEsC,GAAG,CAAC;AAC5C;AAEA;;AAEG;SACamB,UAAUA,CAAA;EAAC,IAAgEC,IAAA;OAAhE,IAAgEC,EAAA,MAAhEA,EAAgE,GAAAC,SAAA,CAAAC,MAAA,EAAhEF,EAAgE;IAAhED,IAAgE,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACzF,OAAOD,IAAI,CACRI,MAAM,CAAC,UAACvB,KAAK;IACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;MACjD,OAAO,IAAI;IACZ;IAED,OAAO,KAAK;GACb,CAAC,CACDE,IAAI,CAAC,GAAG,CAAC,CACTC,IAAI,EAAE;AACX;;ACvOA,IAAMC,QAAQ,GAAG,CAAC;AAClB,IAAMC,QAAQ,GAAG,CAAC;AAClB,IAAMC,aAAa,GAAG,CAAC;AASvB,IAAAC,OAAA,0BAAAC,MAAA;EAAsBC,SAAoC,CAAAF,OAAA,EAAAC,MAAA;EAA1D,SAAAD,QAAA;IAAA,IA+yBCG,KAAA,GAAAF,MAAA,aAAAA,MAAA,CAAAG,KAAA,OAAAZ,SAAA;IA3xBCW,KAAA,CAAAE,UAAU,GAAoCC,KAAK,CAACC,SAAS,EAAE;IAC/DJ,KAAA,CAAAK,QAAQ,GAAsCF,KAAK,CAACC,SAAS,EAAE;IAC/DJ,KAAA,CAAAM,QAAQ,GAAsCH,KAAK,CAACC,SAAS,EAAE;IAC/DJ,KAAiB,CAAAO,iBAAA,GAAU;MAAEpE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;KAAG;IACzC2D,KAAY,CAAAQ,YAAA,GAA0B,IAAI;IAC1CR,KAAQ,CAAAS,QAAA,GAA4B,IAAI;IACxCT,KAAa,CAAAU,aAAA,GAAmB,IAAI;IACpCV,KAAA,CAAApE,SAAS,GAAc;MAAEP,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEO,YAAY,EAAE,CAAC;MAAEC,aAAa,EAAE;KAAG;IACjFkE,KAAiB,CAAAW,iBAAA,GAAU;MAAExE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;KAAG;IACzC2D,KAAa,CAAAY,aAAA,GAAU;MAAEzE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;KAAG;IACrC2D,KAAgB,CAAAa,gBAAA,GAAG,CAAC;IACpBb,KAAoB,CAAAc,oBAAA,GAAG,CAAC;IACxBd,KAAU,CAAAe,UAAA,GAAG,KAAK;IAClBf,KAAiB,CAAAgB,iBAAA,GAAG,CAAC;IACrBhB,KAAiB,CAAAiB,iBAAA,GAAG,CAAC;IACrBjB,KAAc,CAAAkB,cAAA,GAAkB,IAAI;IACpClB,KAAe,CAAAmB,eAAA,GAAkB,IAAI;IACrCnB,KAAU,CAAAoB,UAAA,GAAkB,IAAI;IAChCpB,KAAA,CAAAqB,UAAU,GAAoB,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI;IAC/EtB,KAAA,CAAAuB,aAAa,GAAkB,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;IAC5ExB,KAAc,CAAAyB,cAAA,GAA0B,IAAI;IAE5CzB,KAAA,CAAA0B,KAAK,GAAU;MACbzF,QAAQ,EAAE,IAAI;MACd0F,mBAAmB,EAAE,KAAK;MAC1BC,cAAc,EAAEC;KACjB;IA2GD7B,KAAA,CAAA8B,kBAAkB,GAAG;MACnB,IAAI,OAAON,MAAM,CAACO,cAAc,KAAK,WAAW,IAAI,CAAC/B,KAAI,CAACQ,YAAY,EAAE;QACtE;MACD;MACD,IAAIwB,aAAa,GAAG,IAAI;MACxBhC,KAAI,CAACyB,cAAc,GAAG,IAAID,MAAM,CAACO,cAAc,CAAC,UAACE,OAAO;QACtD,IAAID,aAAa,EAAE;UACjBA,aAAa,GAAG,KAAK;UACrB;QACD;QACDhC,KAAI,CAACkC,YAAY,EAAE;MACrB,CAAC,CAAC;MACFlC,KAAI,CAACyB,cAAc,CAACU,OAAO,CAACnC,KAAI,CAACQ,YAAY,CAAC;KAC/C;IAED;IACAR,KAAiB,CAAAoC,iBAAA,GAAG,UAACC,CAAQ,EAAK;MAAA,OAAAA,CAAC,CAACC,cAAc,EAAE;KAAA;IAEpDtC,KAAA,CAAAuC,WAAW,GAAG;MACZ,IAAI,CAACvC,KAAI,CAACqB,UAAU,EAAE;MACtBrB,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,WAAW,EAAExC,KAAI,CAACyC,WAAW,CAAC;MAClEzC,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,SAAS,EAAExC,KAAI,CAAC0C,aAAa,CAAC;MAClE1C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,WAAW,EAAExC,KAAI,CAAC2C,WAAW,CAAC;MAClE3C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,UAAU,EAAExC,KAAI,CAAC0C,aAAa,CAAC;MACnE1C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,eAAe,EAAExC,KAAI,CAAC4C,eAAgC,CAAC;MAC3F5C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,YAAY,EAAExC,KAAI,CAAC6C,YAA6B,CAAC;MACrF7C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,QAAQ,EAAExC,KAAI,CAAC8C,QAAQ,CAAC;KAC7D;IAED9C,KAAA,CAAA+C,gBAAgB,GAAG;MACjB,IAAI/C,KAAI,CAACQ,YAAY,EAAER,KAAI,CAACQ,YAAY,CAACgC,mBAAmB,CAAC,OAAO,EAAExC,KAAI,CAACgD,OAAO,CAAC;MACnF,IAAIhD,KAAI,CAACoB,UAAU,EAAE;QACnB6B,YAAY,CAACjD,KAAI,CAACoB,UAAU,CAAC;MAC9B;KACF;IAEDpB,KAAA,CAAAkD,WAAW,GAAG;MACZ,IAAMjH,QAAQ,GAAG+D,KAAI,CAACkC,YAAY,EAAE;MAEpC,IAAIjG,QAAQ,EAAE;QACZ+D,KAAI,CAACmD,YAAY,EAAE;QACnBnD,KAAI,CAACoD,cAAc,CAACnH,QAAQ,CAAC;MAC9B;MAED,IAAI+D,KAAI,CAACqD,KAAK,CAACC,aAAa,EAAE;QAC5BtD,KAAI,CAACqD,KAAK,CAACC,aAAa,CAACtD,KAAI,CAACpE,SAAS,CAAC;MACzC;KACF;IAEDoE,KAAc,CAAAoD,cAAA,GAAG,UAACnH,QAAc;MAC9B,IAAI+D,KAAI,CAACqD,KAAK,CAACE,6BAA6B,EAAE;QACtC,IAAApI,EAAA,GAAiB+C,wCAAwC,CAC7D8B,KAAI,CAACqD,KAAK,CAACE,6BAA6B,EACxCvD,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAACqD,KAAK,CAACnI,QAAQ,EACnBe,QAAQ,EACR+D,KAAI,CAACqD,KAAK,CAAClF,OAAO,EAClB6B,KAAI,CAACqD,KAAK,CAACjF,OAAO,CACnB;UAPOnB,IAAI,GAAA9B,EAAA,CAAA8B,IAAA;UAAEf,IAAI,GAAAf,EAAA,CAAAe,IAOjB;QAED8D,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACvG,IAAI,CAAC;QAC7B+C,KAAI,CAACqD,KAAK,CAACI,YAAY,IAAIzD,KAAI,CAACqD,KAAK,CAACI,YAAY,CAACvH,IAAI,CAAC;MACzD,OAAM,IAAI8D,KAAI,CAACqD,KAAK,CAACK,wBAAwB,EAAE;QACxC,IAAAC,EAAA,GAAiBpF,mCAAmC,CACxDyB,KAAI,CAACqD,KAAK,CAACK,wBAAwB,EACnC1D,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAACqD,KAAK,CAACnI,QAAQ,EACnBe,QAAQ,EACR+D,KAAI,CAACqD,KAAK,CAAClF,OAAO,EAClB6B,KAAI,CAACqD,KAAK,CAACjF,OAAO,CACnB;UAPOnB,IAAI,GAAA0G,EAAA,CAAA1G,IAAA;UAAEf,IAAI,GAAAyH,EAAA,CAAAzH,IAOjB;QAED8D,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACvG,IAAI,CAAC;QAC7B+C,KAAI,CAACqD,KAAK,CAACI,YAAY,IAAIzD,KAAI,CAACqD,KAAK,CAACI,YAAY,CAACvH,IAAI,CAAC;MACzD;KACF;IA+BD8D,KAAA,CAAAkC,YAAY,GAAG;;MACb,IAAM0B,QAAQ,GAAG5D,KAAI,CAACK,QAAQ,CAACwD,OAAO,IAAI7D,KAAI,CAACM,QAAQ,CAACuD,OAAO;MAE/D,IAAID,QAAQ,IAAI5D,KAAI,CAACQ,YAAY,EAAE;QACjCR,KAAI,CAACU,aAAa,GAAGV,KAAI,CAACQ,YAAY,CAACsD,qBAAqB,EAAE;QAC9D9D,KAAI,CAAC+D,qBAAqB,EAAE;QAC5B,IAAMC,eAAe,GAAGhE,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAG2E,KAAI,CAACU,aAAa,CAACpF,MAAM;QAC5E,IAAMO,YAAY,GAChB,EAAAV,EAAA,GAAA6E,KAAI,CAACK,QAAQ,CAACwD,OAAO,MAAE,QAAA1I,EAAA,uBAAAA,EAAA,CAAAU,YAAY,MAAI,CAAA8H,EAAA,GAAA3D,KAAI,CAACM,QAAQ,CAACuD,OAAO,cAAAF,EAAA,uBAAAA,EAAA,CAAEM,UAAU,KAAI,CAAC;QAC/E,IAAMnI,aAAa,GACjB,EAAAoI,EAAA,GAAAlE,KAAI,CAACK,QAAQ,CAACwD,OAAO,MAAE,QAAAK,EAAA,uBAAAA,EAAA,CAAApI,aAAa,MAAI,CAAAqI,EAAA,GAAAnE,KAAI,CAACM,QAAQ,CAACuD,OAAO,cAAAM,EAAA,uBAAAA,EAAA,CAAEC,WAAW,KAAI,CAAC;QACjF,IAAMC,iBAAiB,GACrBT,QAAQ,CAACU,WAAW,GAAGzI,YAAY,IAAI+H,QAAQ,CAACW,YAAY,GAAGzI,aAAa;QAC9E,IAAM0I,WAAW,GAAG3I,YAAY,GAAGC,aAAa;QAEhD;QACA;QACA;QACA;QACA;QACA;QACA,IAAI2I,iBAAiB,SAAM;QAE3B,IAAIJ,iBAAiB,EAAE;UACrB,QAAQrE,KAAI,CAAC0B,KAAK,CAACE,cAAc;YAC/B;YACA,KAAK,SAAS;cACZ6C,iBAAiB,GACfT,eAAe,GAAGQ,WAAW,GACzB;gBACEnJ,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACpF,MAAM,GAAGkJ,WAAW;gBAC9ClJ,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACpF;cAC5B,IACD;gBACED,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACrF,KAAK;gBAC/BC,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAGmJ;eACpC;cACP;YACF,KAAK,kBAAkB;cACrBC,iBAAiB,GAAG;gBAClBpJ,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACrF,KAAK;gBAC/BC,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAGmJ;eACpC;cACD;YACF,KAAK,gBAAgB;cACnBC,iBAAiB,GAAG;gBAClBpJ,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACpF,MAAM,GAAGkJ,WAAW;gBAC9ClJ,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACpF;eAC5B;cACD;UAAK;QAEV,OAAM;UACLmJ,iBAAiB,GAAG;YAClBpJ,KAAK,EAAEuI,QAAQ,CAACU,WAAW;YAC3BhJ,MAAM,EAAEsI,QAAQ,CAACW;WAClB;QACF;QAEDvE,KAAI,CAACpE,SAAS,GAAAkC,QAAA,CAAAA,QAAA,KACT2G,iBAAiB;UACpB5I,YAAY,EAAAA,YAAA;UACZC,aAAa,EAAAA;QAAA,EACd;QAED;QACA,IAAIkE,KAAI,CAACqD,KAAK,CAACqB,YAAY,EAAE;UAC3B1E,KAAI,CAACqD,KAAK,CAACqB,YAAY,CAAC1E,KAAI,CAACpE,SAAS,CAAC;QACxC;QAED,IAAMK,QAAQ,GAAG+D,KAAI,CAACqD,KAAK,CAACpH,QAAQ,GAChC+D,KAAI,CAACqD,KAAK,CAACpH,QAAQ,GACnBrB,WAAW,CACToF,KAAI,CAACpE,SAAS,CAACP,KAAK,EACpB2E,KAAI,CAACpE,SAAS,CAACN,MAAM,EACrB0E,KAAI,CAACU,aAAa,CAACrF,KAAK,EACxB2E,KAAI,CAACU,aAAa,CAACpF,MAAM,EACzB0E,KAAI,CAACqD,KAAK,CAACpI,MAAM,EACjB+E,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB;QAEL,IACE,EAAAyJ,EAAA,GAAA3E,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,cAAA0I,EAAA,uBAAAA,EAAA,CAAErJ,MAAM,MAAKW,QAAQ,CAACX,MAAM,IAC/C,EAAAsJ,EAAA,GAAA5E,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,MAAE,QAAA2I,EAAA,uBAAAA,EAAA,CAAAvJ,KAAK,MAAKY,QAAQ,CAACZ,KAAK,EAC7C;UACA2E,KAAI,CAACqD,KAAK,CAACwB,gBAAgB,IAAI7E,KAAI,CAACqD,KAAK,CAACwB,gBAAgB,CAAC5I,QAAQ,CAAC;QACrE;QACD+D,KAAI,CAAC8E,QAAQ,CAAC;UAAE7I,QAAQ,EAAAA;QAAE,GAAE+D,KAAI,CAAC+E,qBAAqB,CAAC;QACvD;QACA,IAAI/E,KAAI,CAACqD,KAAK,CAAC2B,WAAW,EAAE;UAC1BhF,KAAI,CAACqD,KAAK,CAAC2B,WAAW,CAAC/I,QAAQ,CAAC;QACjC;QAED,OAAOA,QAAQ;MAChB;KACF;IAED+D,KAAA,CAAA+D,qBAAqB,GAAG;MACtB,IAAI/D,KAAI,CAACQ,YAAY,EAAE;QACrB,IAAMyE,MAAM,GAAGjF,KAAI,CAACQ,YAAY,CAACsD,qBAAqB,EAAE;QACxD9D,KAAI,CAACO,iBAAiB,GAAG;UAAEpE,CAAC,EAAE8I,MAAM,CAACC,IAAI;UAAE7I,CAAC,EAAE4I,MAAM,CAACE;SAAK;MAC3D;KACF;IAYDnF,KAAW,CAAAoF,WAAA,GAAG,UAAC/C,CAA+C;MAC5D,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE;MACtBgB,CAAC,CAACC,cAAc,EAAE;MAClBtC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,WAAW,EAAErF,KAAI,CAACyC,WAAW,CAAC;MAC/DzC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,SAAS,EAAErF,KAAI,CAAC0C,aAAa,CAAC;MAC/D1C,KAAI,CAAC+D,qBAAqB,EAAE;MAC5B/D,KAAI,CAACsF,WAAW,CAACzF,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC,CAAC;KAC3C;IAEDrC,KAAA,CAAAyC,WAAW,GAAG,UAACJ,CAAa;MAAK,OAAArC,KAAI,CAACwF,MAAM,CAAC3F,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC,CAAC;KAAA;IAEtErC,KAAQ,CAAA8C,QAAA,GAAG,UAACT,CAAQ;MAClB,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE;MACtBgB,CAAC,CAACC,cAAc,EAAE;MAClBtC,KAAI,CAAC+D,qBAAqB,EAAE;KAC7B;IAED/D,KAAY,CAAAyF,YAAA,GAAG,UAACpD,CAAmC;MACjD,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE;MACtBrB,KAAI,CAACe,UAAU,GAAG,IAAI;MACtB,IAAIf,KAAI,CAACqD,KAAK,CAACqC,cAAc,IAAI,CAAC1F,KAAI,CAACqD,KAAK,CAACqC,cAAc,CAACrD,CAAC,CAAC,EAAE;QAC9D;MACD;MAEDrC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,WAAW,EAAErF,KAAI,CAAC2C,WAAW,EAAE;QAAEgD,OAAO,EAAE;OAAO,CAAC;MACnF3F,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,UAAU,EAAErF,KAAI,CAAC0C,aAAa,CAAC;MAEhE1C,KAAI,CAAC+D,qBAAqB,EAAE;MAE5B,IAAI1B,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;QAC1BU,KAAI,CAAC6F,YAAY,CAACxD,CAAC,CAAC;OACrB,MAAM,IAAIA,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;QACjCU,KAAI,CAACsF,WAAW,CAACzF,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD;KACF;IAED5F,KAAW,CAAA2C,WAAA,GAAG,UAACN,CAAa;MAC1B;MACAA,CAAC,CAACC,cAAc,EAAE;MAClB,IAAID,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;QAC1BU,KAAI,CAAC+F,WAAW,CAAC1D,CAAC,CAAC;OACpB,MAAM,IAAIA,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;QACjCU,KAAI,CAACwF,MAAM,CAAC3F,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD;KACF;IAED5F,KAAc,CAAAgG,cAAA,GAAG,UAAC3D,CAAe;MAC/B,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE;MACtBgB,CAAC,CAACC,cAAc,EAAE;MAClBtC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,eAAe,EAAErF,KAAI,CAAC4C,eAAgC,CAAC;MACxF5C,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,YAAY,EAAErF,KAAI,CAAC6C,YAA6B,CAAC;MAClF7C,KAAI,CAACa,gBAAgB,GAAGb,KAAI,CAACqD,KAAK,CAACnH,IAAI;MACvC8D,KAAI,CAACc,oBAAoB,GAAGd,KAAI,CAACqD,KAAK,CAACnI,QAAQ;KAChD;IAED8E,KAAe,CAAA4C,eAAA,GAAG,UAACP,CAAe;MAChCA,CAAC,CAACC,cAAc,EAAE;MAClB,IAAItC,KAAI,CAACe,UAAU,EAAE;QACnB;QACA;MACD;MAED,IAAMkF,KAAK,GAAGpG,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC;MACtC,IAAM6D,OAAO,GAAGlG,KAAI,CAACa,gBAAgB,GAAG,CAAC,GAAGwB,CAAC,CAAC8D,KAAK;MACnDnG,KAAI,CAACoG,UAAU,CAACF,OAAO,EAAED,KAAK,EAAE;QAAEI,oBAAoB,EAAE;MAAI,CAAE,CAAC;MAC/D,IAAIrG,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,EAAE;QAC/B,IAAMC,WAAW,GAAGvG,KAAI,CAACc,oBAAoB,GAAGuB,CAAC,CAACnH,QAAQ;QAC1D8E,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,CAACC,WAAW,CAAC;MACzC;KACF;IAEDvG,KAAY,CAAA6C,YAAA,GAAG,UAACR,CAAe;MAC7BrC,KAAI,CAACuC,WAAW,EAAE;KACnB;IAEDvC,KAAW,CAAAsF,WAAA,GAAG,UAACnK,EAAe;;UAAbgB,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA;MACnB2D,KAAI,CAACW,iBAAiB,GAAG;QAAExE,CAAC,EAAAA,CAAA;QAAEE,CAAC,EAAAA;OAAE;MACjC2D,KAAI,CAACY,aAAa,GAAQ9C,QAAA,KAAAkC,KAAI,CAACqD,KAAK,CAACpG,IAAI,CAAE;MAC3C,CAAAiH,EAAA,IAAAP,EAAA,GAAA3D,KAAI,CAACqD,KAAK,EAACmD,kBAAkB,cAAAtC,EAAA,uBAAAA,EAAA,CAAAuC,IAAA,CAAA9C,EAAA,CAAI;KAClC;IAED3D,KAAM,CAAAwF,MAAA,GAAG,UAACrK,EAAe;UAAbgB,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA;MACd,IAAI,CAAC2D,KAAI,CAACuB,aAAa,EAAE;MACzB,IAAIvB,KAAI,CAACkB,cAAc,EAAElB,KAAI,CAACuB,aAAa,CAACmF,oBAAoB,CAAC1G,KAAI,CAACkB,cAAc,CAAC;MAErFlB,KAAI,CAACkB,cAAc,GAAGlB,KAAI,CAACuB,aAAa,CAACoF,qBAAqB,CAAC;QAC7D,IAAI,CAAC3G,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE;QAC1B,IAAIE,CAAC,KAAK0F,SAAS,IAAIxF,CAAC,KAAKwF,SAAS,EAAE;QACxC,IAAM+E,OAAO,GAAGzK,CAAC,GAAG6D,KAAI,CAACW,iBAAiB,CAACxE,CAAC;QAC5C,IAAM0K,OAAO,GAAGxK,CAAC,GAAG2D,KAAI,CAACW,iBAAiB,CAACtE,CAAC;QAC5C,IAAMyK,iBAAiB,GAAG;UACxB3K,CAAC,EAAE6D,KAAI,CAACY,aAAa,CAACzE,CAAC,GAAGyK,OAAO;UACjCvK,CAAC,EAAE2D,KAAI,CAACY,aAAa,CAACvE,CAAC,GAAGwK;SAC3B;QAED,IAAME,WAAW,GAAG/G,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAC3CA,gBAAgB,CACd+K,iBAAiB,EACjB9G,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD4L,iBAAiB;QACrB9G,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACuD,WAAW,CAAC;MACtC,CAAC,CAAC;KACH;IAED/G,KAAA,CAAA0C,aAAa,GAAG;;MACd1C,KAAI,CAACe,UAAU,GAAG,KAAK;MACvBf,KAAI,CAACuC,WAAW,EAAE;MAClBvC,KAAI,CAACmD,YAAY,EAAE;MACnB,CAAAQ,EAAA,IAAAxI,EAAA,GAAA6E,KAAI,CAACqD,KAAK,EAAC2D,gBAAgB,cAAArD,EAAA,uBAAAA,EAAA,CAAA8C,IAAA,CAAAtL,EAAA,CAAI;KAChC;IA+BD6E,KAAO,CAAAgD,OAAA,GAAG,UAACX,CAAa;MACtB,IAAI,CAACrC,KAAI,CAACuB,aAAa,EAAE;MACzB,IAAIvB,KAAI,CAACqD,KAAK,CAAC4D,cAAc,IAAI,CAACjH,KAAI,CAACqD,KAAK,CAAC4D,cAAc,CAAC5E,CAAC,CAAC,EAAE;QAC9D;MACD;MAEDA,CAAC,CAACC,cAAc,EAAE;MAClB,IAAM2D,KAAK,GAAGpG,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC;MAC9B,IAAA6E,MAAM,GAAKC,cAAc,CAAC9E,CAAC,CAAC,CAAA6E,MAAtB;MACd,IAAMhB,OAAO,GAAGlG,KAAI,CAACqD,KAAK,CAACnH,IAAI,GAAIgL,MAAM,GAAGlH,KAAI,CAACqD,KAAK,CAAC+D,SAAS,GAAI,GAAG;MACvEpH,KAAI,CAACoG,UAAU,CAACF,OAAO,EAAED,KAAK,EAAE;QAAEI,oBAAoB,EAAE;MAAI,CAAE,CAAC;MAE/D,IAAI,CAACrG,KAAI,CAAC0B,KAAK,CAACC,mBAAmB,EAAE;QACnC3B,KAAI,CAAC8E,QAAQ,CAAC;UAAEnD,mBAAmB,EAAE;QAAM,GAAE,YAAM;UAAA,IAAAxG,EAAA,EAAAwI,EAAA;UAAA,QAAAA,EAAA,IAAAxI,EAAA,GAAA6E,KAAI,CAACqD,KAAK,EAACmD,kBAAkB,MAAI,QAAA7C,EAAA,uBAAAA,EAAA,CAAA8C,IAAA,CAAAtL,EAAA;QAAA,EAAC;MACtF;MAED,IAAI6E,KAAI,CAACoB,UAAU,EAAE;QACnB6B,YAAY,CAACjD,KAAI,CAACoB,UAAU,CAAC;MAC9B;MACDpB,KAAI,CAACoB,UAAU,GAAGpB,KAAI,CAACuB,aAAa,CAAC8F,UAAU,CAC7C;QAAM,OAAArH,KAAI,CAAC8E,QAAQ,CAAC;UAAEnD,mBAAmB,EAAE;QAAO,GAAE;;UAAM,QAAAgC,EAAA,IAAAxI,EAAA,GAAA6E,KAAI,CAACqD,KAAK,EAAC2D,gBAAgB,MAAI,QAAArD,EAAA,uBAAAA,EAAA,CAAA8C,IAAA,CAAAtL,EAAA;SAAA,CAAC;OAAA,EAC1F,GAAG,CACJ;KACF;IAED6E,KAAA,CAAAsH,mBAAmB,GAAG,UAACnM,EAAe,EAAEoM,gBAAuB;UAAtCpL,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA;MAC3B,IAAI,CAAC2D,KAAI,CAACU,aAAa,EAAE;QACvB,MAAM,IAAI8G,KAAK,CAAC,4BAA4B,CAAC;MAC9C;MACD,OAAO;QACLrL,CAAC,EAAE6D,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAG,CAAC,IAAIc,CAAC,GAAGoL,gBAAgB,CAACpL,CAAC,CAAC;QAC1DE,CAAC,EAAE2D,KAAI,CAACU,aAAa,CAACpF,MAAM,GAAG,CAAC,IAAIe,CAAC,GAAGkL,gBAAgB,CAAClL,CAAC;OAC3D;KACF;IAED2D,KAAe,CAAAyH,eAAA,GAAG,UAACtM,EAAe;UAAbgB,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA;MACjB,IAAAsH,EAAA,GAAiB3D,KAAI,CAACqD,KAAK;QAAzBpG,IAAI,GAAA0G,EAAA,CAAA1G,IAAA;QAAEf,IAAI,GAAAyH,EAAA,CAAAzH,IAAe;MACjC,OAAO;QACLC,CAAC,EAAE,CAACA,CAAC,GAAGc,IAAI,CAACd,CAAC,IAAID,IAAI;QACtBG,CAAC,EAAE,CAACA,CAAC,GAAGY,IAAI,CAACZ,CAAC,IAAIH;OACnB;KACF;IAED8D,KAAA,CAAAoG,UAAU,GAAG,UAAClK,IAAY,EAAE+J,KAAY,EAAE9K,EAAoC;UAApCwI,EAAkC,GAAAxI,EAAA,gBAAE,GAAAA,EAAA;QAAlC+I,EAAA,GAAAP,EAAA,CAAA0C,oBAA2B;QAA3BA,oBAAoB,GAAAnC,EAAA,cAAG,IAAI,GAAAA,EAAA;MACrE,IAAI,CAAClE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,IAAI,CAAC+D,KAAI,CAACqD,KAAK,CAACI,YAAY,EAAE;MAEtD,IAAMyC,OAAO,GAAG3J,KAAK,CAACL,IAAI,EAAE8D,KAAI,CAACqD,KAAK,CAAClF,OAAO,EAAE6B,KAAI,CAACqD,KAAK,CAACjF,OAAO,CAAC;MAEnE,IAAIiI,oBAAoB,EAAE;QACxB,IAAMqB,SAAS,GAAG1H,KAAI,CAACsH,mBAAmB,CAACrB,KAAK,EAAEjG,KAAI,CAACO,iBAAiB,CAAC;QACzE,IAAMoH,UAAU,GAAG3H,KAAI,CAACyH,eAAe,CAACC,SAAS,CAAC;QAClD,IAAMZ,iBAAiB,GAAG;UACxB3K,CAAC,EAAEwL,UAAU,CAACxL,CAAC,GAAG+J,OAAO,GAAGwB,SAAS,CAACvL,CAAC;UACvCE,CAAC,EAAEsL,UAAU,CAACtL,CAAC,GAAG6J,OAAO,GAAGwB,SAAS,CAACrL;SACvC;QAED,IAAM0K,WAAW,GAAG/G,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAC3CA,gBAAgB,CACd+K,iBAAiB,EACjB9G,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnBiK,OAAO,EACPlG,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD4L,iBAAiB;QAErB9G,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACuD,WAAW,CAAC;MACrC;MACD/G,KAAI,CAACqD,KAAK,CAACI,YAAY,CAACyC,OAAO,CAAC;KACjC;IAEDlG,KAAA,CAAA4H,WAAW,GAAG;MACZ,IAAI,CAAC5H,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE;QACxB,OAAO,IAAI;MACZ;MAED;MACA,IAAM4L,kBAAkB,GAAG7H,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAClDA,gBAAgB,CACdiE,KAAI,CAACqD,KAAK,CAACpG,IAAI,EACf+C,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD8E,KAAI,CAACqD,KAAK,CAACpG,IAAI;MACnB,OAAOD,kBAAkB,CACvB6K,kBAAkB,EAClB7H,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAAC8H,SAAS,EAAE,EAChB9H,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,EACnB8E,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,CAC5B;KACF;IAEDiE,KAAA,CAAAmD,YAAY,GAAG;MACb,IAAM4E,QAAQ,GAAG/H,KAAI,CAAC4H,WAAW,EAAE;MACnC,IAAI,CAACG,QAAQ,EAAE;MAEP,IAAAxK,sBAAsB,GAAwBwK,QAAQ,CAAAxK,sBAAhC;QAAEM,iBAAiB,GAAKkK,QAAQ,CAAAlK,iBAAb;MACjD,IAAImC,KAAI,CAACqD,KAAK,CAAC2E,cAAc,EAAE;QAC7BhI,KAAI,CAACqD,KAAK,CAAC2E,cAAc,CAACzK,sBAAsB,EAAEM,iBAAiB,CAAC;MACrE;MAED,IAAImC,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,EAAE;QAC/BjI,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,CAAC1K,sBAAsB,EAAEM,iBAAiB,CAAC;MACvE;KACF;IAEDmC,KAAA,CAAAkI,kBAAkB,GAAG;MACnB,IAAMH,QAAQ,GAAG/H,KAAI,CAAC4H,WAAW,EAAE;MACnC,IAAI,CAACG,QAAQ,EAAE;MAEP,IAAAxK,sBAAsB,GAAwBwK,QAAQ,CAAAxK,sBAAhC;QAAEM,iBAAiB,GAAKkK,QAAQ,CAAAlK,iBAAb;MACjD,IAAImC,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,EAAE;QAC/BjI,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,CAAC1K,sBAAsB,EAAEM,iBAAiB,CAAC;MACvE;KACF;IAEDmC,KAAA,CAAA+E,qBAAqB,GAAG;MACtB,IAAI,CAAC/E,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE;MAE1B,IAAM8K,WAAW,GAAG/G,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAC3CA,gBAAgB,CACdiE,KAAI,CAACqD,KAAK,CAACpG,IAAI,EACf+C,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD8E,KAAI,CAACqD,KAAK,CAACpG,IAAI;MAEnB+C,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACuD,WAAW,CAAC;MACpC/G,KAAI,CAACmD,YAAY,EAAE;KACpB;IAEDnD,KAAS,CAAAmI,SAAA,GAAG,UAACC,KAA0C;;MAC/C,IAAAlE,EAAA,GAAuDlE,KAAI,CAACqD,KAAK;QAA/DpG,IAAI,GAAAiH,EAAA,CAAAjH,IAAA;QAAEuG,YAAY,GAAAU,EAAA,CAAAV,YAAA;QAAE6E,YAAY,GAAAnE,EAAA,CAAAmE,YAAA;QAAEnM,IAAI,GAAAgI,EAAA,CAAAhI,IAAA;QAAEhB,QAAQ,GAAAgJ,EAAA,CAAAhJ,QAAe;MACvE,IAAIoN,IAAI,GAAGD,YAAY;MAEvB,IAAI,CAACrI,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE;MAE1B;MACA,IAAImM,KAAK,CAACG,QAAQ,EAAE;QAClBD,IAAI,IAAI,GAAG;MACZ;MAED,IAAIE,OAAO,GAAA1K,QAAA,KAAQb,IAAI,CAAE;MAEzB,QAAQmL,KAAK,CAACK,GAAG;QACf,KAAK,SAAS;UACZD,OAAO,CAACnM,CAAC,IAAIiM,IAAI;UACjBF,KAAK,CAAC9F,cAAc,EAAE;UACtB;QACF,KAAK,WAAW;UACdkG,OAAO,CAACnM,CAAC,IAAIiM,IAAI;UACjBF,KAAK,CAAC9F,cAAc,EAAE;UACtB;QACF,KAAK,WAAW;UACdkG,OAAO,CAACrM,CAAC,IAAImM,IAAI;UACjBF,KAAK,CAAC9F,cAAc,EAAE;UACtB;QACF,KAAK,YAAY;UACfkG,OAAO,CAACrM,CAAC,IAAImM,IAAI;UACjBF,KAAK,CAAC9F,cAAc,EAAE;UACtB;QACF;UACE;MAAM;MAGV,IAAItC,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,EAAE;QAC/ByM,OAAO,GAAGzM,gBAAgB,CAACyM,OAAO,EAAExI,KAAI,CAACpE,SAAS,EAAEoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAEC,IAAI,EAAEhB,QAAQ,CAAC;MACzF;MAED,IAAI,CAACkN,KAAK,CAACM,MAAM,EAAE;QACjB,CAAA/E,EAAA,IAAAxI,EAAA,GAAA6E,KAAI,CAACqD,KAAK,EAACmD,kBAAkB,cAAA7C,EAAA,uBAAAA,EAAA,CAAA8C,IAAA,CAAAtL,EAAA,CAAI;MAClC;MAEDqI,YAAY,CAACgF,OAAO,CAAC;KACtB;IAEDxI,KAAO,CAAA2I,OAAA,GAAG,UAACP,KAA0C;;MACnD,QAAQA,KAAK,CAACK,GAAG;QACf,KAAK,SAAS;QACd,KAAK,WAAW;QAChB,KAAK,WAAW;QAChB,KAAK,YAAY;UACfL,KAAK,CAAC9F,cAAc,EAAE;UACtB;QACF;UACE;MAAM;MAEVtC,KAAI,CAACmD,YAAY,EAAE;MACnB,CAAAQ,EAAA,IAAAxI,EAAA,GAAA6E,KAAI,CAACqD,KAAK,EAAC2D,gBAAgB,cAAArD,EAAA,uBAAAA,EAAA,CAAA8C,IAAA,CAAAtL,EAAA,CAAI;KAChC;;EAuGH;EA/vBE0E,OAAA,CAAA+I,SAAA,CAAAC,iBAAiB,GAAjB;IACE,IAAI,CAAC,IAAI,CAACxH,UAAU,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;IAC7C,IAAI,IAAI,CAACf,YAAY,EAAE;MACrB,IAAI,IAAI,CAACA,YAAY,CAACsI,aAAa,EAAE;QACnC,IAAI,CAACzH,UAAU,GAAG,IAAI,CAACb,YAAY,CAACsI,aAAa;MAClD;MACD,IAAI,IAAI,CAACzH,UAAU,CAAC0H,WAAW,EAAE;QAC/B,IAAI,CAACxH,aAAa,GAAG,IAAI,CAACF,UAAU,CAAC0H,WAAW;MACjD;MAED,IAAI,CAACjH,kBAAkB,EAAE;MACzB;MACA,IAAI,OAAON,MAAM,CAACO,cAAc,KAAK,WAAW,EAAE;QAChD,IAAI,CAACR,aAAa,CAAC8D,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACnD,YAAY,CAAC;MACjE;MACD,IAAI,CAACmB,KAAK,CAAC2F,cAAc,IACvB,IAAI,CAACxI,YAAY,CAAC6E,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrC,OAAO,EAAE;QAAE2C,OAAO,EAAE;MAAK,CAAE,CAAC;MAC/E,IAAI,CAACnF,YAAY,CAAC6E,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACW,cAA+B,CAAC;IACzF;IAED,IAAI,CAAC3E,UAAU,CAACgE,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACvC,QAAQ,CAAC;IAEzD,IAAI,CAAC,IAAI,CAACO,KAAK,CAAC4F,+BAA+B,EAAE;MAC/C,IAAI,CAACxI,QAAQ,GAAG,IAAI,CAACY,UAAU,CAAC6H,aAAa,CAAC,OAAO,CAAC;MACtD,IAAI,CAACzI,QAAQ,CAAC0I,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MAC9C,IAAI,IAAI,CAAC9F,KAAK,CAAC+F,KAAK,EAAE;QACpB,IAAI,CAAC3I,QAAQ,CAAC0I,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC9F,KAAK,CAAC+F,KAAK,CAAC;MACtD;MACD,IAAI,CAAC3I,QAAQ,CAAC4I,SAAS,GAAGC,QAAS;MACnC,IAAI,CAACjI,UAAU,CAACkI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC/I,QAAQ,CAAC;IAChD;IAED;IACA,IAAI,IAAI,CAACJ,QAAQ,CAACwD,OAAO,IAAI,IAAI,CAACxD,QAAQ,CAACwD,OAAO,CAAC4F,QAAQ,EAAE;MAC3D,IAAI,CAACvG,WAAW,EAAE;IACnB;IAED;IACA,IAAI,IAAI,CAACG,KAAK,CAACqG,WAAW,EAAE;MAC1B,IAAI,CAACrG,KAAK,CAACqG,WAAW,CAAC,IAAI,CAACrJ,QAAQ,CAAC;IACtC;IAED,IAAI,IAAI,CAACgD,KAAK,CAACsG,WAAW,EAAE;MAC1B,IAAI,CAACtG,KAAK,CAACsG,WAAW,CAAC,IAAI,CAACrJ,QAAQ,CAAC;IACtC;IAED,IAAI,IAAI,CAAC+C,KAAK,CAACuG,aAAa,EAAE;MAC5B,IAAI,CAACvG,KAAK,CAACuG,aAAa,CAAC,IAAI,CAAC1J,UAAU,CAAC;IAC1C;GACF;EAEDL,OAAA,CAAA+I,SAAA,CAAAiB,oBAAoB,GAApB;;IACE,IAAI,CAAC,IAAI,CAACxI,UAAU,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;IAC7C,IAAI,OAAOC,MAAM,CAACO,cAAc,KAAK,WAAW,EAAE;MAChD,IAAI,CAACR,aAAa,CAACiB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACN,YAAY,CAAC;IACpE;IACD,CAAA/G,EAAA,OAAI,CAACsG,cAAc,MAAE,QAAAtG,EAAA,uBAAAA,EAAA,CAAA2O,UAAU,EAAE;IACjC,IAAI,IAAI,CAACtJ,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACgC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACJ,iBAAiB,CAAC;IAC9E;IAED,IAAI,IAAI,CAAC3B,QAAQ,EAAE;MACjB,CAAAkD,EAAA,OAAI,CAAClD,QAAQ,CAACsJ,UAAU,cAAApG,EAAA,uBAAAA,EAAA,CAAEqG,WAAW,CAAC,IAAI,CAACvJ,QAAQ,CAAC;IACrD;IAED,IAAI,CAAC8B,WAAW,EAAE;IAClB,IAAI,CAACc,KAAK,CAAC2F,cAAc,IAAI,IAAI,CAACjG,gBAAgB,EAAE;GACrD;EAEDlD,OAAkB,CAAA+I,SAAA,CAAAqB,kBAAA,GAAlB,UAAmBC,SAAuB;;IACxC,IAAIA,SAAS,CAAChP,QAAQ,KAAK,IAAI,CAACmI,KAAK,CAACnI,QAAQ,EAAE;MAC9C,IAAI,CAACgH,YAAY,EAAE;MACnB,IAAI,CAAC6C,qBAAqB,EAAE;KAC7B,MAAM,IAAImF,SAAS,CAACjP,MAAM,KAAK,IAAI,CAACoI,KAAK,CAACpI,MAAM,EAAE;MACjD,IAAI,CAACiH,YAAY,EAAE;KACpB,MAAM,IAAIgI,SAAS,CAACC,SAAS,KAAK,IAAI,CAAC9G,KAAK,CAAC8G,SAAS,EAAE;MACvD,IAAI,CAACjI,YAAY,EAAE;KACpB,MAAM,IAAIgI,SAAS,CAAChO,IAAI,KAAK,IAAI,CAACmH,KAAK,CAACnH,IAAI,EAAE;MAC7C,IAAI,CAAC6I,qBAAqB,EAAE;KAC7B,MAAM,IACL,EAAA5J,EAAA,GAAA+O,SAAS,CAACjO,QAAQ,cAAAd,EAAA,uBAAAA,EAAA,CAAEG,MAAM,OAAK,CAAAqI,EAAA,OAAI,CAACN,KAAK,CAACpH,QAAQ,cAAA0H,EAAA,uBAAAA,EAAA,CAAErI,MAAM,KAC1D,EAAA4I,EAAA,GAAAgG,SAAS,CAACjO,QAAQ,cAAAiI,EAAA,uBAAAA,EAAA,CAAE7I,KAAK,OAAK,CAAA8I,EAAA,OAAI,CAACd,KAAK,CAACpH,QAAQ,MAAE,QAAAkI,EAAA,uBAAAA,EAAA,CAAA9I,KAAK,GACxD;MACA,IAAI,CAAC6G,YAAY,EAAE;KACpB,MAAM,IACL,EAAAyC,EAAA,GAAAuF,SAAS,CAACjN,IAAI,cAAA0H,EAAA,uBAAAA,EAAA,CAAExI,CAAC,OAAK,CAAAyI,EAAA,OAAI,CAACvB,KAAK,CAACpG,IAAI,cAAA2H,EAAA,uBAAAA,EAAA,CAAEzI,CAAC,KACxC,EAAAiO,EAAA,GAAAF,SAAS,CAACjN,IAAI,cAAAmN,EAAA,uBAAAA,EAAA,CAAE/N,CAAC,OAAK,CAAAgO,EAAA,OAAI,CAAChH,KAAK,CAACpG,IAAI,MAAE,QAAAoN,EAAA,uBAAAA,EAAA,CAAAhO,CAAC,GACxC;MACA,IAAI,CAAC6L,kBAAkB,EAAE;IAC1B;IACD,IAAIgC,SAAS,CAAClB,cAAc,KAAK,IAAI,CAAC3F,KAAK,CAAC2F,cAAc,IAAI,IAAI,CAACxI,YAAY,EAAE;MAC/E,IAAI,CAAC6C,KAAK,CAAC2F,cAAc,GACrB,IAAI,CAACxI,YAAY,CAAC6E,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrC,OAAO,EAAE;QAAE2C,OAAO,EAAE;OAAO,CAAC,GAC7E,IAAI,CAAC5C,gBAAgB,EAAE;IAC5B;IACD,IAAImH,SAAS,CAACI,KAAK,KAAK,IAAI,CAACjH,KAAK,CAACiH,KAAK,EAAE;MACxC,CAAAC,EAAA,OAAI,CAACjK,QAAQ,CAACuD,OAAO,MAAE,QAAA0G,EAAA,uBAAAA,EAAA,CAAAC,IAAI,EAAE;IAC9B;IAED,IAAML,SAAS,GAAG,IAAI,CAACM,YAAY,EAAE;IACrC,IAAIN,SAAS,KAAK,IAAI,CAACzI,KAAK,CAACE,cAAc,EAAE;MAC3C,IAAI,CAACkD,QAAQ,CAAC;QAAElD,cAAc,EAAEuI;MAAS,CAAE,EAAE,IAAI,CAACjI,YAAY,CAAC;IAChE;GACF;EA+EDrC,OAAA,CAAA+I,SAAA,CAAAd,SAAS,GAAT;IACQ,IAAA3M,EAAA,GAAuB,IAAI,CAACkI,KAAK;MAA/BpH,QAAQ,GAAAd,EAAA,CAAAc,QAAA;MAAEhB,MAAM,GAAAE,EAAA,CAAAF,MAAe;IACvC,IAAIgB,QAAQ,EAAE;MACZ,OAAOA,QAAQ,CAACZ,KAAK,GAAGY,QAAQ,CAACX,MAAM;IACxC;IACD,OAAOL,MAAM;GACd;EAED4E,OAAA,CAAA+I,SAAA,CAAA6B,YAAY,GAAZ;;IACE,IAAI,IAAI,CAACpH,KAAK,CAAC8G,SAAS,KAAK,OAAO,EAAE;MACpC,IAAMvG,QAAQ,GAAG,IAAI,CAACvD,QAAQ,CAACwD,OAAO,IAAI,IAAI,CAACvD,QAAQ,CAACuD,OAAO;MAE/D,IAAID,QAAQ,IAAI,IAAI,CAACpD,YAAY,EAAE;QACjC,IAAI,CAACE,aAAa,GAAG,IAAI,CAACF,YAAY,CAACsD,qBAAqB,EAAE;QAC9D,IAAME,eAAe,GAAG,IAAI,CAACtD,aAAa,CAACrF,KAAK,GAAG,IAAI,CAACqF,aAAa,CAACpF,MAAM;QAC5E,IAAMO,YAAY,GAChB,EAAAV,EAAA,OAAI,CAACkF,QAAQ,CAACwD,OAAO,MAAE,QAAA1I,EAAA,uBAAAA,EAAA,CAAAU,YAAY,MAAI,CAAA8H,EAAA,OAAI,CAACrD,QAAQ,CAACuD,OAAO,cAAAF,EAAA,uBAAAA,EAAA,CAAEM,UAAU,KAAI,CAAC;QAC/E,IAAMnI,aAAa,GACjB,EAAAoI,EAAA,OAAI,CAAC7D,QAAQ,CAACwD,OAAO,MAAE,QAAAK,EAAA,uBAAAA,EAAA,CAAApI,aAAa,MAAI,CAAAqI,EAAA,OAAI,CAAC7D,QAAQ,CAACuD,OAAO,cAAAM,EAAA,uBAAAA,EAAA,CAAEC,WAAW,KAAI,CAAC;QACjF,IAAMI,WAAW,GAAG3I,YAAY,GAAGC,aAAa;QAEhD,OAAO0I,WAAW,GAAGR,eAAe,GAAG,kBAAkB,GAAG,gBAAgB;MAC7E;MACD,OAAO,kBAAkB;IAC1B;IAED,OAAO,IAAI,CAACX,KAAK,CAAC8G,SAAS;GAC5B;EAsODtK,OAAY,CAAA+I,SAAA,CAAA/C,YAAA,GAAZ,UAAaxD,CAAmC;IAC9C,IAAM5F,MAAM,GAAGoD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC;IAClD,IAAMlJ,MAAM,GAAGmD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC5E,iBAAiB,GAAGxE,wBAAwB,CAACC,MAAM,EAAEC,MAAM,CAAC;IACjE,IAAI,CAACuE,iBAAiB,GAAGpE,wBAAwB,CAACJ,MAAM,EAAEC,MAAM,CAAC;IACjE,IAAI,CAAC4I,WAAW,CAAC7G,SAAS,CAAChC,MAAM,EAAEC,MAAM,CAAC,CAAC;GAC5C;EAEDmD,OAAW,CAAA+I,SAAA,CAAA7C,WAAA,GAAX,UAAY1D,CAAa;IAAzB,IAmBCrC,KAAA;IAlBC,IAAI,CAAC,IAAI,CAACqB,UAAU,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;IAC7C,IAAM9E,MAAM,GAAGoD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC;IAClD,IAAMlJ,MAAM,GAAGmD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC;IAClD,IAAM8E,MAAM,GAAGjM,SAAS,CAAChC,MAAM,EAAEC,MAAM,CAAC;IACxC,IAAI,CAAC8I,MAAM,CAACkF,MAAM,CAAC;IAEnB,IAAI,IAAI,CAACvJ,eAAe,EAAE,IAAI,CAACI,aAAa,CAACmF,oBAAoB,CAAC,IAAI,CAACvF,eAAe,CAAC;IACvF,IAAI,CAACA,eAAe,GAAG,IAAI,CAACI,aAAa,CAACoF,qBAAqB,CAAC;MAC9D,IAAMgE,QAAQ,GAAGnO,wBAAwB,CAACC,MAAM,EAAEC,MAAM,CAAC;MACzD,IAAMwJ,OAAO,GAAGlG,KAAI,CAACqD,KAAK,CAACnH,IAAI,IAAIyO,QAAQ,GAAG3K,KAAI,CAACgB,iBAAiB,CAAC;MACrEhB,KAAI,CAACoG,UAAU,CAACF,OAAO,EAAEwE,MAAM,EAAE;QAAErE,oBAAoB,EAAE;MAAK,CAAE,CAAC;MACjErG,KAAI,CAACgB,iBAAiB,GAAG2J,QAAQ;MAEjC,IAAMzP,QAAQ,GAAG2B,wBAAwB,CAACJ,MAAM,EAAEC,MAAM,CAAC;MACzD,IAAM6J,WAAW,GAAGvG,KAAI,CAACqD,KAAK,CAACnI,QAAQ,IAAIA,QAAQ,GAAG8E,KAAI,CAACiB,iBAAiB,CAAC;MAC7EjB,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,IAAItG,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,CAACC,WAAW,CAAC;MACvEvG,KAAI,CAACiB,iBAAiB,GAAG/F,QAAQ;IACnC,CAAC,CAAC;GACH;EAwMD2E,OAAA,CAAA+I,SAAA,CAAAgC,MAAM,GAAN;IAAA,IAoGC5K,KAAA;;IAnGO,IAAA2D,EAaF,OAAI,CAACN,KAAK;MAZZwH,KAAK,GAAAlH,EAAA,CAAAkH,KAAA;MACLP,KAAK,GAAA3G,EAAA,CAAA2G,KAAA;MACLQ,UAAU,GAAAnH,EAAA,CAAAmH,UAAA;MACVC,YAAY,GAAApH,EAAA,CAAAoH,YAAA;MACZC,SAAS,GAAArH,EAAA,CAAAqH,SAAA;MACT9G,EAAc,GAAAP,EAAA,CAAA1G,IAAA;MAANd,CAAC,GAAA+H,EAAA,CAAA/H,CAAA;MAAEE,CAAC,GAAA6H,EAAA,CAAA7H,CAAA;MACZnB,QAAQ,GAAAyI,EAAA,CAAAzI,QAAA;MACRgB,IAAI,GAAAyH,EAAA,CAAAzH,IAAA;MACJ+O,SAAS,GAAAtH,EAAA,CAAAsH,SAAA;MACTC,QAAQ,GAAAvH,EAAA,CAAAuH,QAAA;MACR/G,EAAoD,GAAAR,EAAA,CAAAwH,KAAA;MAA3CC,cAAc,GAAAjH,EAAA,CAAAiH,cAAA;MAAEC,aAAa,GAAAlH,EAAA,CAAAkH,aAAA;MAAEC,UAAU,GAAAnH,EAAA,CAAAmH,UAAA;MAClD3G,EAAA,GAAAhB,EAAA,CAAA4H,OAAkE;MAAvDC,kBAAkB,GAAA7G,EAAA,CAAA6G,kBAAA;MAAEC,iBAAiB,GAAA9G,EAAA,CAAA8G,iBAAA;MAAEC,cAAc,GAAA/G,EAAA,CAAA+G,cACpD;IAEd,IAAMvB,SAAS,GAAG,CAAAhP,EAAA,OAAI,CAACuG,KAAK,CAACE,cAAc,MAAI,QAAAzG,EAAA,cAAAA,EAAA,OAAI,CAACsP,YAAY,EAAE;IAElE,OACEtK,KACE,CAAA+I,aAAA;MAAA9D,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BK,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BkG,GAAG,EAAE,SAACA,IAAAC,EAAE;QAAK,OAAC5L,KAAI,CAACQ,YAAY,GAAGoL,EAAE;OAAC;MAAA,eACzB,WAAW;MACvBT,KAAK,EAAEC,cAAc;MACrBS,SAAS,EAAE3M,UAAU,CAAC,yBAAyB,EAAEsM,kBAAkB;KAAC,EAEnEX,KAAK,GACJ1K,KAAA,CAAA+I,aAAA,QAAApL,QAAA;MACEgO,GAAG,EAAC,EAAE;MACND,SAAS,EAAE3M,UAAU,CACnB,qBAAqB,EACrBiL,SAAS,KAAK,SAAS,IAAI,uBAAuB,EAClDA,SAAS,KAAK,kBAAkB,IAAI,gCAAgC,EACpEA,SAAS,KAAK,gBAAgB,IAAI,8BAA8B,EAChEuB,cAAc;OAEXZ,UAAmD;MACxDiB,GAAG,EAAElB,KAAK;MACVc,GAAG,EAAE,IAAI,CAACtL,QAAQ;MAClB8K,KAAK,EACArN,QAAA,CAAAA,QAAA,KAAAwN,UAAU;QACbN,SAAS,EACPA,SAAS,IAAI,aAAAgB,MAAA,CAAa7P,CAAC,EAAO,QAAA6P,MAAA,CAAA3P,CAAC,iBAAA2P,MAAA,CAAc9Q,QAAQ,iBAAA8Q,MAAA,CAAc9P,IAAI,EAAG;MAAA;MAElF+P,MAAM,EAAE,IAAI,CAAC/I;IACb,MAEFoH,KAAK,IACHnK,KACE,CAAA+I,aAAA,UAAApL,QAAA;MAAAoO,QAAQ,EACR;MAAAC,WAAW,EACX;MAAAC,IAAI;MACJC,KAAK,EAAE,IAAI;MACXR,SAAS,EAAE3M,UAAU,CACnB,qBAAqB,EACrBiL,SAAS,KAAK,SAAS,IAAI,uBAAuB,EAClDA,SAAS,KAAK,kBAAkB,IAAI,gCAAgC,EACpEA,SAAS,KAAK,gBAAgB,IAAI,8BAA8B,EAChEuB,cAAc;OAEZZ,UAAU;MACda,GAAG,EAAE,IAAI,CAACrL,QAAQ;MAClBgM,gBAAgB,EAAE,IAAI,CAACpJ,WAAW;MAClCiI,KAAK,EACArN,QAAA,CAAAA,QAAA,KAAAwN,UAAU,CACb;QAAAN,SAAS,EACPA,SAAS,IAAI,aAAAgB,MAAA,CAAa7P,CAAC,UAAA6P,MAAA,CAAO3P,CAAC,EAAc,eAAA2P,MAAA,CAAA9Q,QAAQ,EAAc,eAAA8Q,MAAA,CAAA9P,IAAI,EAAG;MAAA;MAElFqQ,QAAQ,EAAE;QAET,CAACC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;MAAEyB,GAAG,EAAEzB;IAAO,EAAC,EAAEoC,GAAG,CAAC,UAACC,IAAI;MAAK,OAC/DxM,KAAQ,CAAA+I,aAAA,WAAApL,QAAA;QAAA2K,GAAG,EAAEkE,IAAI,CAACZ;OAAG,EAAMY,IAAI,CAAI;IAD4B,CAEhE,CAAC,CAGP,EACA,IAAI,CAACjL,KAAK,CAACzF,QAAQ,IAClBkE,KACE,CAAA+I,aAAA,QAAApL,QAAA;MAAA6N,GAAG,EAAE,IAAI,CAACzL,UAAU;MACpBiL,KAAK,EAAArN,QAAA,CAAAA,QAAA,KACAuN,aAAa,CAChB;QAAAhQ,KAAK,EAAE,IAAI,CAACqG,KAAK,CAACzF,QAAQ,CAACZ,KAAK;QAChCC,MAAM,EAAE,IAAI,CAACoG,KAAK,CAACzF,QAAQ,CAACX;;MAE9BsR,QAAQ,EAAE,CAAC;MACXzE,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBQ,OAAO,EAAE,IAAI,CAACA,OAAO;MACT,wBAAS;MACrBkD,SAAS,EAAE3M,UAAU,CACnB,wBAAwB,EACxB+L,SAAS,KAAK,OAAO,IAAI,6BAA6B,EACtDC,QAAQ,IAAI,4BAA4B,EACxCO,iBAAiB;IAEf,GAAAV,YAAY,EAEnB,CACG;GAET;EA7yBMlL,OAAA,CAAAgN,YAAY,GAAG;IACpB3Q,IAAI,EAAE,CAAC;IACPhB,QAAQ,EAAE,CAAC;IACXD,MAAM,EAAE,CAAC,GAAG,CAAC;IACbmD,OAAO,EAAEuB,QAAQ;IACjBxB,OAAO,EAAEuB,QAAQ;IACjBuL,SAAS,EAAE,MAAe;IAC1Bd,SAAS,EAAE,SAAkB;IAC7Be,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,EAAE;IACTI,OAAO,EAAE,EAAE;IACXT,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChB3D,SAAS,EAAE,CAAC;IACZrL,gBAAgB,EAAE,IAAI;IACtBiN,cAAc,EAAE,IAAI;IACpBX,YAAY,EAAEzI;GACf;EAwVMC,OAAA,CAAA0F,aAAa,GAAG,UAAClD,CAA+C;IAAK,OAAC;MAC3ElG,CAAC,EAAE2Q,MAAM,CAACzK,CAAC,CAAC0K,OAAO,CAAC;MACpB1Q,CAAC,EAAEyQ,MAAM,CAACzK,CAAC,CAAC2K,OAAO;KACpB;GAAC;EAEKnN,OAAA,CAAAiG,aAAa,GAAG,UAACmH,KAA0B;IAAK,OAAC;MACtD9Q,CAAC,EAAE2Q,MAAM,CAACG,KAAK,CAACF,OAAO,CAAC;MACxB1Q,CAAC,EAAEyQ,MAAM,CAACG,KAAK,CAACD,OAAO;KACxB;GAAC;EA6bJ,OAACnN,OAAA;AAAA,CA/yBD,CAAsBM,KAAK,CAAC+M,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}