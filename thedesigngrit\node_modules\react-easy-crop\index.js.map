{"version": 3, "file": "index.js", "sources": ["../src/helpers.ts", "../src/Cropper.tsx"], "sourcesContent": ["import { Area, MediaSize, Point, Size } from './types'\n\n/**\n * Compute the dimension of the crop area based on media size,\n * aspect ratio and optionally rotation\n */\nexport function getCropSize(\n  mediaWidth: number,\n  mediaHeight: number,\n  containerWidth: number,\n  containerHeight: number,\n  aspect: number,\n  rotation = 0\n): Size {\n  const { width, height } = rotateSize(mediaWidth, mediaHeight, rotation)\n  const fittingWidth = Math.min(width, containerWidth)\n  const fittingHeight = Math.min(height, containerHeight)\n\n  if (fittingWidth > fittingHeight * aspect) {\n    return {\n      width: fittingHeight * aspect,\n      height: fittingHeight,\n    }\n  }\n\n  return {\n    width: fittingWidth,\n    height: fittingWidth / aspect,\n  }\n}\n\n/**\n * Compute media zoom.\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\n */\nexport function getMediaZoom(mediaSize: MediaSize) {\n  // Take the axis with more pixels to improve accuracy\n  return mediaSize.width > mediaSize.height\n    ? mediaSize.width / mediaSize.naturalWidth\n    : mediaSize.height / mediaSize.naturalHeight\n}\n\n/**\n * Ensure a new media position stays in the crop area.\n */\nexport function restrictPosition(\n  position: Point,\n  mediaSize: Size,\n  cropSize: Size,\n  zoom: number,\n  rotation = 0\n): Point {\n  const { width, height } = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  return {\n    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n    y: restrictPositionCoord(position.y, height, cropSize.height, zoom),\n  }\n}\n\nfunction restrictPositionCoord(\n  position: number,\n  mediaSize: number,\n  cropSize: number,\n  zoom: number\n): number {\n  const maxPosition = (mediaSize * zoom) / 2 - cropSize / 2\n\n  return clamp(position, -maxPosition, maxPosition)\n}\n\nexport function getDistanceBetweenPoints(pointA: Point, pointB: Point) {\n  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2))\n}\n\nexport function getRotationBetweenPoints(pointA: Point, pointB: Point) {\n  return (Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180) / Math.PI\n}\n\n/**\n * Compute the output cropped area of the media in percentages and pixels.\n * x/y are the top-left coordinates on the src media\n */\nexport function computeCroppedArea(\n  crop: Point,\n  mediaSize: MediaSize,\n  cropSize: Size,\n  aspect: number,\n  zoom: number,\n  rotation = 0,\n  restrictPosition = true\n): { croppedAreaPercentages: Area; croppedAreaPixels: Area } {\n  // if the media is rotated by the user, we cannot limit the position anymore\n  // as it might need to be negative.\n  const limitAreaFn = restrictPosition ? limitArea : noOp\n\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  // calculate the crop area in percentages\n  // in the rotated space\n  const croppedAreaPercentages = {\n    x: limitAreaFn(\n      100,\n      (((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width) *\n        100\n    ),\n    y: limitAreaFn(\n      100,\n      (((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) /\n        mediaBBoxSize.height) *\n        100\n    ),\n    width: limitAreaFn(100, ((cropSize.width / mediaBBoxSize.width) * 100) / zoom),\n    height: limitAreaFn(100, ((cropSize.height / mediaBBoxSize.height) * 100) / zoom),\n  }\n\n  // we compute the pixels size naively\n  const widthInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.width,\n      (croppedAreaPercentages.width * mediaNaturalBBoxSize.width) / 100\n    )\n  )\n  const heightInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.height,\n      (croppedAreaPercentages.height * mediaNaturalBBoxSize.height) / 100\n    )\n  )\n  const isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect\n\n  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n  // thus we want to compute the width from the height and aspect for accuracy.\n  // Otherwise, we compute the height from width and aspect.\n  const sizePixels = isImgWiderThanHigh\n    ? {\n        width: Math.round(heightInPixels * aspect),\n        height: heightInPixels,\n      }\n    : {\n        width: widthInPixels,\n        height: Math.round(widthInPixels / aspect),\n      }\n\n  const croppedAreaPixels = {\n    ...sizePixels,\n    x: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.width - sizePixels.width,\n        (croppedAreaPercentages.x * mediaNaturalBBoxSize.width) / 100\n      )\n    ),\n    y: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.height - sizePixels.height,\n        (croppedAreaPercentages.y * mediaNaturalBBoxSize.height) / 100\n      )\n    ),\n  }\n\n  return { croppedAreaPercentages, croppedAreaPixels }\n}\n\n/**\n * Ensure the returned value is between 0 and max\n */\nfunction limitArea(max: number, value: number): number {\n  return Math.min(max, Math.max(0, value))\n}\n\nfunction noOp(_max: number, value: number) {\n  return value\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPercentages.\n */\nexport function getInitialCropFromCroppedAreaPercentages(\n  croppedAreaPercentages: Area,\n  mediaSize: MediaSize,\n  rotation: number,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n) {\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  // This is the inverse process of computeCroppedArea\n  const zoom = clamp(\n    (cropSize.width / mediaBBoxSize.width) * (100 / croppedAreaPercentages.width),\n    minZoom,\n    maxZoom\n  )\n\n  const crop = {\n    x:\n      (zoom * mediaBBoxSize.width) / 2 -\n      cropSize.width / 2 -\n      mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n    y:\n      (zoom * mediaBBoxSize.height) / 2 -\n      cropSize.height / 2 -\n      mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100),\n  }\n\n  return { crop, zoom }\n}\n\n/**\n * Compute zoom from the croppedAreaPixels\n */\nfunction getZoomFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  cropSize: Size\n): number {\n  const mediaZoom = getMediaZoom(mediaSize)\n\n  return cropSize.height > cropSize.width\n    ? cropSize.height / (croppedAreaPixels.height * mediaZoom)\n    : cropSize.width / (croppedAreaPixels.width * mediaZoom)\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPixels\n */\nexport function getInitialCropFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  rotation = 0,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n): { crop: Point; zoom: number } {\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  const zoom = clamp(\n    getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize),\n    minZoom,\n    maxZoom\n  )\n\n  const cropZoom =\n    cropSize.height > cropSize.width\n      ? cropSize.height / croppedAreaPixels.height\n      : cropSize.width / croppedAreaPixels.width\n\n  const crop = {\n    x:\n      ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n    y:\n      ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) *\n      cropZoom,\n  }\n  return { crop, zoom }\n}\n\n/**\n * Return the point that is the center of point a and b\n */\nexport function getCenter(a: Point, b: Point): Point {\n  return {\n    x: (b.x + a.x) / 2,\n    y: (b.y + a.y) / 2,\n  }\n}\n\nexport function getRadianAngle(degreeValue: number) {\n  return (degreeValue * Math.PI) / 180\n}\n\n/**\n * Returns the new bounding area of a rotated rectangle.\n */\nexport function rotateSize(width: number, height: number, rotation: number): Size {\n  const rotRad = getRadianAngle(rotation)\n\n  return {\n    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height),\n  }\n}\n\n/**\n * Clamp value between min and max\n */\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, min), max)\n}\n\n/**\n * Combine multiple class names into a single string.\n */\nexport function classNames(...args: (boolean | string | number | undefined | void | null)[]) {\n  return args\n    .filter((value) => {\n      if (typeof value === 'string' && value.length > 0) {\n        return true\n      }\n\n      return false\n    })\n    .join(' ')\n    .trim()\n}\n", "import * as React from 'react'\nimport normalizeWheel from 'normalize-wheel'\nimport { Area, MediaSize, Point, Size, VideoSrc } from './types'\nimport {\n  getCropSize,\n  restrictPosition,\n  getDistanceBetweenPoints,\n  getRotationBetweenPoints,\n  computeCroppedArea,\n  getCenter,\n  getInitialCropFromCroppedAreaPixels,\n  getInitialCropFromCroppedAreaPercentages,\n  classNames,\n  clamp,\n} from './helpers'\nimport cssStyles from './styles.css'\n\nexport type CropperProps = {\n  image?: string\n  video?: string | VideoSrc[]\n  transform?: string\n  crop: Point\n  zoom: number\n  rotation: number\n  aspect: number\n  minZoom: number\n  maxZoom: number\n  cropShape: 'rect' | 'round'\n  cropSize?: Size\n  objectFit?: 'contain' | 'cover' | 'horizontal-cover' | 'vertical-cover'\n  showGrid?: boolean\n  zoomSpeed: number\n  zoomWithScroll?: boolean\n  onCropChange: (location: Point) => void\n  onZoomChange?: (zoom: number) => void\n  onRotationChange?: (rotation: number) => void\n  onCropComplete?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropAreaChange?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropSizeChange?: (cropSize: Size) => void\n  onInteractionStart?: () => void\n  onInteractionEnd?: () => void\n  onMediaLoaded?: (mediaSize: MediaSize) => void\n  style: {\n    containerStyle?: React.CSSProperties\n    mediaStyle?: React.CSSProperties\n    cropAreaStyle?: React.CSSProperties\n  }\n  classes: {\n    containerClassName?: string\n    mediaClassName?: string\n    cropAreaClassName?: string\n  }\n  restrictPosition: boolean\n  mediaProps: React.ImgHTMLAttributes<HTMLElement> | React.VideoHTMLAttributes<HTMLElement>\n  cropperProps: React.HTMLAttributes<HTMLDivElement>\n  disableAutomaticStylesInjection?: boolean\n  initialCroppedAreaPixels?: Area\n  initialCroppedAreaPercentages?: Area\n  onTouchRequest?: (e: React.TouchEvent<HTMLDivElement>) => boolean\n  onWheelRequest?: (e: WheelEvent) => boolean\n  setCropperRef?: (ref: React.RefObject<HTMLDivElement>) => void\n  setImageRef?: (ref: React.RefObject<HTMLImageElement>) => void\n  setVideoRef?: (ref: React.RefObject<HTMLVideoElement>) => void\n  setMediaSize?: (size: MediaSize) => void\n  setCropSize?: (size: Size) => void\n  nonce?: string\n  keyboardStep: number\n}\n\ntype State = {\n  cropSize: Size | null\n  hasWheelJustStarted: boolean\n  mediaObjectFit: String | undefined\n}\n\nconst MIN_ZOOM = 1\nconst MAX_ZOOM = 3\nconst KEYBOARD_STEP = 1\n\ntype GestureEvent = UIEvent & {\n  rotation: number\n  scale: number\n  clientX: number\n  clientY: number\n}\n\nclass Cropper extends React.Component<CropperProps, State> {\n  static defaultProps = {\n    zoom: 1,\n    rotation: 0,\n    aspect: 4 / 3,\n    maxZoom: MAX_ZOOM,\n    minZoom: MIN_ZOOM,\n    cropShape: 'rect' as const,\n    objectFit: 'contain' as const,\n    showGrid: true,\n    style: {},\n    classes: {},\n    mediaProps: {},\n    cropperProps: {},\n    zoomSpeed: 1,\n    restrictPosition: true,\n    zoomWithScroll: true,\n    keyboardStep: KEYBOARD_STEP,\n  }\n\n  cropperRef: React.RefObject<HTMLDivElement> = React.createRef()\n  imageRef: React.RefObject<HTMLImageElement> = React.createRef()\n  videoRef: React.RefObject<HTMLVideoElement> = React.createRef()\n  containerPosition: Point = { x: 0, y: 0 }\n  containerRef: HTMLDivElement | null = null\n  styleRef: HTMLStyleElement | null = null\n  containerRect: DOMRect | null = null\n  mediaSize: MediaSize = { width: 0, height: 0, naturalWidth: 0, naturalHeight: 0 }\n  dragStartPosition: Point = { x: 0, y: 0 }\n  dragStartCrop: Point = { x: 0, y: 0 }\n  gestureZoomStart = 0\n  gestureRotationStart = 0\n  isTouching = false\n  lastPinchDistance = 0\n  lastPinchRotation = 0\n  rafDragTimeout: number | null = null\n  rafPinchTimeout: number | null = null\n  wheelTimer: number | null = null\n  currentDoc: Document | null = typeof document !== 'undefined' ? document : null\n  currentWindow: Window | null = typeof window !== 'undefined' ? window : null\n  resizeObserver: ResizeObserver | null = null\n\n  state: State = {\n    cropSize: null,\n    hasWheelJustStarted: false,\n    mediaObjectFit: undefined,\n  }\n\n  componentDidMount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (this.containerRef) {\n      if (this.containerRef.ownerDocument) {\n        this.currentDoc = this.containerRef.ownerDocument\n      }\n      if (this.currentDoc.defaultView) {\n        this.currentWindow = this.currentDoc.defaultView\n      }\n\n      this.initResizeObserver()\n      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n      if (typeof window.ResizeObserver === 'undefined') {\n        this.currentWindow.addEventListener('resize', this.computeSizes)\n      }\n      this.props.zoomWithScroll &&\n        this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n      this.containerRef.addEventListener('gesturestart', this.onGestureStart as EventListener)\n    }\n\n    this.currentDoc.addEventListener('scroll', this.onScroll)\n\n    if (!this.props.disableAutomaticStylesInjection) {\n      this.styleRef = this.currentDoc.createElement('style')\n      this.styleRef.setAttribute('type', 'text/css')\n      if (this.props.nonce) {\n        this.styleRef.setAttribute('nonce', this.props.nonce)\n      }\n      this.styleRef.innerHTML = cssStyles\n      this.currentDoc.head.appendChild(this.styleRef)\n    }\n\n    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n    if (this.imageRef.current && this.imageRef.current.complete) {\n      this.onMediaLoad()\n    }\n\n    // set image and video refs in the parent if the callbacks exist\n    if (this.props.setImageRef) {\n      this.props.setImageRef(this.imageRef)\n    }\n\n    if (this.props.setVideoRef) {\n      this.props.setVideoRef(this.videoRef)\n    }\n\n    if (this.props.setCropperRef) {\n      this.props.setCropperRef(this.cropperRef)\n    }\n  }\n\n  componentWillUnmount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (typeof window.ResizeObserver === 'undefined') {\n      this.currentWindow.removeEventListener('resize', this.computeSizes)\n    }\n    this.resizeObserver?.disconnect()\n    if (this.containerRef) {\n      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari)\n    }\n\n    if (this.styleRef) {\n      this.styleRef.parentNode?.removeChild(this.styleRef)\n    }\n\n    this.cleanEvents()\n    this.props.zoomWithScroll && this.clearScrollEvent()\n  }\n\n  componentDidUpdate(prevProps: CropperProps) {\n    if (prevProps.rotation !== this.props.rotation) {\n      this.computeSizes()\n      this.recomputeCropPosition()\n    } else if (prevProps.aspect !== this.props.aspect) {\n      this.computeSizes()\n    } else if (prevProps.objectFit !== this.props.objectFit) {\n      this.computeSizes()\n    } else if (prevProps.zoom !== this.props.zoom) {\n      this.recomputeCropPosition()\n    } else if (\n      prevProps.cropSize?.height !== this.props.cropSize?.height ||\n      prevProps.cropSize?.width !== this.props.cropSize?.width\n    ) {\n      this.computeSizes()\n    } else if (\n      prevProps.crop?.x !== this.props.crop?.x ||\n      prevProps.crop?.y !== this.props.crop?.y\n    ) {\n      this.emitCropAreaChange()\n    }\n    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n      this.props.zoomWithScroll\n        ? this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n        : this.clearScrollEvent()\n    }\n    if (prevProps.video !== this.props.video) {\n      this.videoRef.current?.load()\n    }\n\n    const objectFit = this.getObjectFit()\n    if (objectFit !== this.state.mediaObjectFit) {\n      this.setState({ mediaObjectFit: objectFit }, this.computeSizes)\n    }\n  }\n\n  initResizeObserver = () => {\n    if (typeof window.ResizeObserver === 'undefined' || !this.containerRef) {\n      return\n    }\n    let isFirstResize = true\n    this.resizeObserver = new window.ResizeObserver((entries) => {\n      if (isFirstResize) {\n        isFirstResize = false // observe() is called on mount, we don't want to trigger a recompute on mount\n        return\n      }\n      this.computeSizes()\n    })\n    this.resizeObserver.observe(this.containerRef)\n  }\n\n  // this is to prevent Safari on iOS >= 10 to zoom the page\n  preventZoomSafari = (e: Event) => e.preventDefault()\n\n  cleanEvents = () => {\n    if (!this.currentDoc) return\n    this.currentDoc.removeEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.removeEventListener('mouseup', this.onDragStopped)\n    this.currentDoc.removeEventListener('touchmove', this.onTouchMove)\n    this.currentDoc.removeEventListener('touchend', this.onDragStopped)\n    this.currentDoc.removeEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.removeEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.currentDoc.removeEventListener('scroll', this.onScroll)\n  }\n\n  clearScrollEvent = () => {\n    if (this.containerRef) this.containerRef.removeEventListener('wheel', this.onWheel)\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n  }\n\n  onMediaLoad = () => {\n    const cropSize = this.computeSizes()\n\n    if (cropSize) {\n      this.emitCropData()\n      this.setInitialCrop(cropSize)\n    }\n\n    if (this.props.onMediaLoaded) {\n      this.props.onMediaLoaded(this.mediaSize)\n    }\n  }\n\n  setInitialCrop = (cropSize: Size) => {\n    if (this.props.initialCroppedAreaPercentages) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPercentages(\n        this.props.initialCroppedAreaPercentages,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    } else if (this.props.initialCroppedAreaPixels) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPixels(\n        this.props.initialCroppedAreaPixels,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    }\n  }\n\n  getAspect() {\n    const { cropSize, aspect } = this.props\n    if (cropSize) {\n      return cropSize.width / cropSize.height\n    }\n    return aspect\n  }\n\n  getObjectFit() {\n    if (this.props.objectFit === 'cover') {\n      const mediaRef = this.imageRef.current || this.videoRef.current\n\n      if (mediaRef && this.containerRef) {\n        this.containerRect = this.containerRef.getBoundingClientRect()\n        const containerAspect = this.containerRect.width / this.containerRect.height\n        const naturalWidth =\n          this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n        const naturalHeight =\n          this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n        const mediaAspect = naturalWidth / naturalHeight\n\n        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover'\n      }\n      return 'horizontal-cover'\n    }\n\n    return this.props.objectFit\n  }\n\n  computeSizes = () => {\n    const mediaRef = this.imageRef.current || this.videoRef.current\n\n    if (mediaRef && this.containerRef) {\n      this.containerRect = this.containerRef.getBoundingClientRect()\n      this.saveContainerPosition()\n      const containerAspect = this.containerRect.width / this.containerRect.height\n      const naturalWidth =\n        this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n      const naturalHeight =\n        this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n      const isMediaScaledDown =\n        mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight\n      const mediaAspect = naturalWidth / naturalHeight\n\n      // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n      // as the values they report are rounded. That will result in precision losses\n      // when calculating zoom. We use the fact that the media is positionned relative\n      // to the container. That allows us to use the container's dimensions\n      // and natural aspect ratio of the media to calculate accurate media size.\n      // However, for this to work, the container should not be rotated\n      let renderedMediaSize: Size\n\n      if (isMediaScaledDown) {\n        switch (this.state.mediaObjectFit) {\n          default:\n          case 'contain':\n            renderedMediaSize =\n              containerAspect > mediaAspect\n                ? {\n                    width: this.containerRect.height * mediaAspect,\n                    height: this.containerRect.height,\n                  }\n                : {\n                    width: this.containerRect.width,\n                    height: this.containerRect.width / mediaAspect,\n                  }\n            break\n          case 'horizontal-cover':\n            renderedMediaSize = {\n              width: this.containerRect.width,\n              height: this.containerRect.width / mediaAspect,\n            }\n            break\n          case 'vertical-cover':\n            renderedMediaSize = {\n              width: this.containerRect.height * mediaAspect,\n              height: this.containerRect.height,\n            }\n            break\n        }\n      } else {\n        renderedMediaSize = {\n          width: mediaRef.offsetWidth,\n          height: mediaRef.offsetHeight,\n        }\n      }\n\n      this.mediaSize = {\n        ...renderedMediaSize,\n        naturalWidth,\n        naturalHeight,\n      }\n\n      // set media size in the parent\n      if (this.props.setMediaSize) {\n        this.props.setMediaSize(this.mediaSize)\n      }\n\n      const cropSize = this.props.cropSize\n        ? this.props.cropSize\n        : getCropSize(\n            this.mediaSize.width,\n            this.mediaSize.height,\n            this.containerRect.width,\n            this.containerRect.height,\n            this.props.aspect,\n            this.props.rotation\n          )\n\n      if (\n        this.state.cropSize?.height !== cropSize.height ||\n        this.state.cropSize?.width !== cropSize.width\n      ) {\n        this.props.onCropSizeChange && this.props.onCropSizeChange(cropSize)\n      }\n      this.setState({ cropSize }, this.recomputeCropPosition)\n      // pass crop size to parent\n      if (this.props.setCropSize) {\n        this.props.setCropSize(cropSize)\n      }\n\n      return cropSize\n    }\n  }\n\n  saveContainerPosition = () => {\n    if (this.containerRef) {\n      const bounds = this.containerRef.getBoundingClientRect()\n      this.containerPosition = { x: bounds.left, y: bounds.top }\n    }\n  }\n\n  static getMousePoint = (e: MouseEvent | React.MouseEvent | GestureEvent) => ({\n    x: Number(e.clientX),\n    y: Number(e.clientY),\n  })\n\n  static getTouchPoint = (touch: Touch | React.Touch) => ({\n    x: Number(touch.clientX),\n    y: Number(touch.clientY),\n  })\n\n  onMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.addEventListener('mouseup', this.onDragStopped)\n    this.saveContainerPosition()\n    this.onDragStart(Cropper.getMousePoint(e))\n  }\n\n  onMouseMove = (e: MouseEvent) => this.onDrag(Cropper.getMousePoint(e))\n\n  onScroll = (e: Event) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.saveContainerPosition()\n  }\n\n  onTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {\n    if (!this.currentDoc) return\n    this.isTouching = true\n    if (this.props.onTouchRequest && !this.props.onTouchRequest(e)) {\n      return\n    }\n\n    this.currentDoc.addEventListener('touchmove', this.onTouchMove, { passive: false }) // iOS 11 now defaults to passive: true\n    this.currentDoc.addEventListener('touchend', this.onDragStopped)\n\n    this.saveContainerPosition()\n\n    if (e.touches.length === 2) {\n      this.onPinchStart(e)\n    } else if (e.touches.length === 1) {\n      this.onDragStart(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onTouchMove = (e: TouchEvent) => {\n    // Prevent whole page from scrolling on iOS.\n    e.preventDefault()\n    if (e.touches.length === 2) {\n      this.onPinchMove(e)\n    } else if (e.touches.length === 1) {\n      this.onDrag(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onGestureStart = (e: GestureEvent) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.addEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.gestureZoomStart = this.props.zoom\n    this.gestureRotationStart = this.props.rotation\n  }\n\n  onGestureChange = (e: GestureEvent) => {\n    e.preventDefault()\n    if (this.isTouching) {\n      // this is to avoid conflict between gesture and touch events\n      return\n    }\n\n    const point = Cropper.getMousePoint(e)\n    const newZoom = this.gestureZoomStart - 1 + e.scale\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n    if (this.props.onRotationChange) {\n      const newRotation = this.gestureRotationStart + e.rotation\n      this.props.onRotationChange(newRotation)\n    }\n  }\n\n  onGestureEnd = (e: GestureEvent) => {\n    this.cleanEvents()\n  }\n\n  onDragStart = ({ x, y }: Point) => {\n    this.dragStartPosition = { x, y }\n    this.dragStartCrop = { ...this.props.crop }\n    this.props.onInteractionStart?.()\n  }\n\n  onDrag = ({ x, y }: Point) => {\n    if (!this.currentWindow) return\n    if (this.rafDragTimeout) this.currentWindow.cancelAnimationFrame(this.rafDragTimeout)\n\n    this.rafDragTimeout = this.currentWindow.requestAnimationFrame(() => {\n      if (!this.state.cropSize) return\n      if (x === undefined || y === undefined) return\n      const offsetX = x - this.dragStartPosition.x\n      const offsetY = y - this.dragStartPosition.y\n      const requestedPosition = {\n        x: this.dragStartCrop.x + offsetX,\n        y: this.dragStartCrop.y + offsetY,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            this.props.zoom,\n            this.props.rotation\n          )\n        : requestedPosition\n      this.props.onCropChange(newPosition)\n    })\n  }\n\n  onDragStopped = () => {\n    this.isTouching = false\n    this.cleanEvents()\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  onPinchStart(e: React.TouchEvent<HTMLDivElement>) {\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB)\n    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB)\n    this.onDragStart(getCenter(pointA, pointB))\n  }\n\n  onPinchMove(e: TouchEvent) {\n    if (!this.currentDoc || !this.currentWindow) return\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    const center = getCenter(pointA, pointB)\n    this.onDrag(center)\n\n    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout)\n    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(() => {\n      const distance = getDistanceBetweenPoints(pointA, pointB)\n      const newZoom = this.props.zoom * (distance / this.lastPinchDistance)\n      this.setNewZoom(newZoom, center, { shouldUpdatePosition: false })\n      this.lastPinchDistance = distance\n\n      const rotation = getRotationBetweenPoints(pointA, pointB)\n      const newRotation = this.props.rotation + (rotation - this.lastPinchRotation)\n      this.props.onRotationChange && this.props.onRotationChange(newRotation)\n      this.lastPinchRotation = rotation\n    })\n  }\n\n  onWheel = (e: WheelEvent) => {\n    if (!this.currentWindow) return\n    if (this.props.onWheelRequest && !this.props.onWheelRequest(e)) {\n      return\n    }\n\n    e.preventDefault()\n    const point = Cropper.getMousePoint(e)\n    const { pixelY } = normalizeWheel(e)\n    const newZoom = this.props.zoom - (pixelY * this.props.zoomSpeed) / 200\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n\n    if (!this.state.hasWheelJustStarted) {\n      this.setState({ hasWheelJustStarted: true }, () => this.props.onInteractionStart?.())\n    }\n\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n    this.wheelTimer = this.currentWindow.setTimeout(\n      () => this.setState({ hasWheelJustStarted: false }, () => this.props.onInteractionEnd?.()),\n      250\n    )\n  }\n\n  getPointOnContainer = ({ x, y }: Point, containerTopLeft: Point): Point => {\n    if (!this.containerRect) {\n      throw new Error('The Cropper is not mounted')\n    }\n    return {\n      x: this.containerRect.width / 2 - (x - containerTopLeft.x),\n      y: this.containerRect.height / 2 - (y - containerTopLeft.y),\n    }\n  }\n\n  getPointOnMedia = ({ x, y }: Point) => {\n    const { crop, zoom } = this.props\n    return {\n      x: (x + crop.x) / zoom,\n      y: (y + crop.y) / zoom,\n    }\n  }\n\n  setNewZoom = (zoom: number, point: Point, { shouldUpdatePosition = true } = {}) => {\n    if (!this.state.cropSize || !this.props.onZoomChange) return\n\n    const newZoom = clamp(zoom, this.props.minZoom, this.props.maxZoom)\n\n    if (shouldUpdatePosition) {\n      const zoomPoint = this.getPointOnContainer(point, this.containerPosition)\n      const zoomTarget = this.getPointOnMedia(zoomPoint)\n      const requestedPosition = {\n        x: zoomTarget.x * newZoom - zoomPoint.x,\n        y: zoomTarget.y * newZoom - zoomPoint.y,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            newZoom,\n            this.props.rotation\n          )\n        : requestedPosition\n\n      this.props.onCropChange(newPosition)\n    }\n    this.props.onZoomChange(newZoom)\n  }\n\n  getCropData = () => {\n    if (!this.state.cropSize) {\n      return null\n    }\n\n    // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n    const restrictedPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n    return computeCroppedArea(\n      restrictedPosition,\n      this.mediaSize,\n      this.state.cropSize,\n      this.getAspect(),\n      this.props.zoom,\n      this.props.rotation,\n      this.props.restrictPosition\n    )\n  }\n\n  emitCropData = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropComplete) {\n      this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels)\n    }\n\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  emitCropAreaChange = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  recomputeCropPosition = () => {\n    if (!this.state.cropSize) return\n\n    const newPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n\n    this.props.onCropChange(newPosition)\n    this.emitCropData()\n  }\n\n  onKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    const { crop, onCropChange, keyboardStep, zoom, rotation } = this.props\n    let step = keyboardStep\n\n    if (!this.state.cropSize) return\n\n    // if the shift key is pressed, reduce the step to allow finer control\n    if (event.shiftKey) {\n      step *= 0.2\n    }\n\n    let newCrop = { ...crop }\n\n    switch (event.key) {\n      case 'ArrowUp':\n        newCrop.y -= step\n        event.preventDefault()\n        break\n      case 'ArrowDown':\n        newCrop.y += step\n        event.preventDefault()\n        break\n      case 'ArrowLeft':\n        newCrop.x -= step\n        event.preventDefault()\n        break\n      case 'ArrowRight':\n        newCrop.x += step\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n\n    if (this.props.restrictPosition) {\n      newCrop = restrictPosition(newCrop, this.mediaSize, this.state.cropSize, zoom, rotation)\n    }\n\n    if (!event.repeat) {\n      this.props.onInteractionStart?.()\n    }\n\n    onCropChange(newCrop)\n  }\n\n  onKeyUp = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    switch (event.key) {\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  render() {\n    const {\n      image,\n      video,\n      mediaProps,\n      cropperProps,\n      transform,\n      crop: { x, y },\n      rotation,\n      zoom,\n      cropShape,\n      showGrid,\n      style: { containerStyle, cropAreaStyle, mediaStyle },\n      classes: { containerClassName, cropAreaClassName, mediaClassName },\n    } = this.props\n\n    const objectFit = this.state.mediaObjectFit ?? this.getObjectFit()\n\n    return (\n      <div\n        onMouseDown={this.onMouseDown}\n        onTouchStart={this.onTouchStart}\n        ref={(el) => (this.containerRef = el)}\n        data-testid=\"container\"\n        style={containerStyle}\n        className={classNames('reactEasyCrop_Container', containerClassName)}\n      >\n        {image ? (\n          <img\n            alt=\"\"\n            className={classNames(\n              'reactEasyCrop_Image',\n              objectFit === 'contain' && 'reactEasyCrop_Contain',\n              objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n              objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n              mediaClassName\n            )}\n            {...(mediaProps as React.ImgHTMLAttributes<HTMLElement>)}\n            src={image}\n            ref={this.imageRef}\n            style={{\n              ...mediaStyle,\n              transform:\n                transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n            }}\n            onLoad={this.onMediaLoad}\n          />\n        ) : (\n          video && (\n            <video\n              autoPlay\n              playsInline\n              loop\n              muted={true}\n              className={classNames(\n                'reactEasyCrop_Video',\n                objectFit === 'contain' && 'reactEasyCrop_Contain',\n                objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n                objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n                mediaClassName\n              )}\n              {...mediaProps}\n              ref={this.videoRef}\n              onLoadedMetadata={this.onMediaLoad}\n              style={{\n                ...mediaStyle,\n                transform:\n                  transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n              }}\n              controls={false}\n            >\n              {(Array.isArray(video) ? video : [{ src: video }]).map((item) => (\n                <source key={item.src} {...item} />\n              ))}\n            </video>\n          )\n        )}\n        {this.state.cropSize && (\n          <div\n            ref={this.cropperRef}\n            style={{\n              ...cropAreaStyle,\n              width: this.state.cropSize.width,\n              height: this.state.cropSize.height,\n            }}\n            tabIndex={0}\n            onKeyDown={this.onKeyDown}\n            onKeyUp={this.onKeyUp}\n            data-testid=\"cropper\"\n            className={classNames(\n              'reactEasyCrop_CropArea',\n              cropShape === 'round' && 'reactEasyCrop_CropAreaRound',\n              showGrid && 'reactEasyCrop_CropAreaGrid',\n              cropAreaClassName\n            )}\n            {...cropperProps}\n          />\n        )}\n      </div>\n    )\n  }\n}\n\nexport default Cropper\n"], "names": ["getCropSize", "mediaWidth", "mediaHeight", "containerWidth", "containerHeight", "aspect", "rotation", "_a", "rotateSize", "width", "height", "fittingWidth", "Math", "min", "fittingHeight", "getMediaZoom", "mediaSize", "naturalWidth", "naturalHeight", "restrictPosition", "position", "cropSize", "zoom", "x", "restrictPositionCoord", "y", "maxPosition", "clamp", "getDistanceBetweenPoints", "pointA", "pointB", "sqrt", "pow", "getRotationBetweenPoints", "atan2", "PI", "computeCroppedArea", "crop", "limitAreaFn", "limitArea", "noOp", "mediaBBoxSize", "mediaNaturalBBoxSize", "croppedAreaPercentages", "widthInPixels", "round", "heightInPixels", "isImgWiderThanHigh", "sizePixels", "croppedAreaPixels", "__assign", "max", "value", "_max", "getInitialCropFromCroppedAreaPercentages", "minZoom", "max<PERSON><PERSON>", "getZoomFromCroppedAreaPixels", "mediaZoom", "getInitialCropFromCroppedAreaPixels", "cropZoom", "getCenter", "a", "b", "getRadianAngle", "degreeValue", "rotRad", "abs", "cos", "sin", "classNames", "args", "_i", "arguments", "length", "filter", "join", "trim", "MIN_ZOOM", "MAX_ZOOM", "KEYBOARD_STEP", "C<PERSON>per", "_super", "__extends", "_this", "apply", "cropperRef", "React", "createRef", "imageRef", "videoRef", "containerPosition", "containerRef", "styleRef", "containerRect", "dragStartPosition", "dragStartCrop", "gestureZoomStart", "gestureRotationStart", "isTouching", "lastPinchDistance", "lastPinchRotation", "rafDragTimeout", "rafPinchTimeout", "wheelTimer", "currentDoc", "document", "currentWindow", "window", "resizeObserver", "state", "hasWheelJustStarted", "mediaObjectFit", "undefined", "initResizeObserver", "ResizeObserver", "isFirstResize", "entries", "computeSizes", "observe", "preventZoomSafari", "e", "preventDefault", "cleanEvents", "removeEventListener", "onMouseMove", "onDragStopped", "onTouchMove", "onGestureChange", "onGestureEnd", "onScroll", "clearScrollEvent", "onWheel", "clearTimeout", "onMediaLoad", "emitCropData", "setInitialCrop", "props", "onMediaLoaded", "initialCroppedAreaPercentages", "onCropChange", "onZoomChange", "initialCroppedAreaPixels", "_b", "mediaRef", "current", "getBoundingClientRect", "saveContainerPosition", "containerAspect", "videoWidth", "_c", "_d", "videoHeight", "isMediaScaledDown", "offsetWidth", "offsetHeight", "mediaAspect", "renderedMediaSize", "setMediaSize", "_e", "_f", "onCropSizeChange", "setState", "recomputeCropPosition", "setCropSize", "bounds", "left", "top", "onMouseDown", "addEventListener", "onDragStart", "getMousePoint", "onDrag", "onTouchStart", "onTouchRequest", "passive", "touches", "onPinchStart", "getTouchPoint", "onPinchMove", "onGestureStart", "point", "newZoom", "scale", "setNewZoom", "shouldUpdatePosition", "onRotationChange", "newRotation", "onInteractionStart", "cancelAnimationFrame", "requestAnimationFrame", "offsetX", "offsetY", "requestedPosition", "newPosition", "onInteractionEnd", "onWheelRequest", "pixelY", "normalizeWheel", "zoomSpeed", "call", "setTimeout", "getPointOnContainer", "containerTopLeft", "Error", "getPointOnMedia", "zoomPoint", "zoomTarget", "getCropData", "restrictedPosition", "getAspect", "cropData", "onCropComplete", "onCropAreaChange", "emitCropAreaChange", "onKeyDown", "event", "keyboardStep", "step", "shift<PERSON>ey", "newCrop", "key", "repeat", "onKeyUp", "prototype", "componentDidMount", "ownerDocument", "defaultView", "zoomWithScroll", "disableAutomaticStylesInjection", "createElement", "setAttribute", "nonce", "innerHTML", "cssStyles", "head", "append<PERSON><PERSON><PERSON>", "complete", "setImageRef", "setVideoRef", "setCropperRef", "componentWillUnmount", "disconnect", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "componentDidUpdate", "prevProps", "objectFit", "_g", "_h", "video", "_j", "load", "getObjectFit", "center", "distance", "render", "image", "mediaProps", "cropperProps", "transform", "cropShape", "showGrid", "style", "containerStyle", "cropAreaStyle", "mediaStyle", "classes", "containerClassName", "cropAreaClassName", "mediaClassName", "ref", "el", "className", "alt", "src", "concat", "onLoad", "autoPlay", "playsInline", "loop", "muted", "onLoadedMetadata", "controls", "Array", "isArray", "map", "item", "tabIndex", "defaultProps", "Number", "clientX", "clientY", "touch", "Component"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;AAGG;AACa,SAAAA,WAAW,CACzBC,UAAkB,EAClBC,WAAmB,EACnBC,cAAsB,EACtBC,eAAuB,EACvBC,MAAc,EACdC,QAAY,EAAA;AAAZ,EAAA,IAAAA,QAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,QAAY,GAAA,CAAA,CAAA;AAAA,GAAA;EAEN,IAAAC,EAAoB,GAAAC,UAAU,CAACP,UAAU,EAAEC,WAAW,EAAEI,QAAQ,CAAC;IAA/DG,KAAK,GAAAF,EAAA,CAAAE,KAAA;IAAEC,MAAM,YAAkD,CAAA;EACvE,IAAMC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,EAAEN,cAAc,CAAC,CAAA;EACpD,IAAMW,aAAa,GAAGF,IAAI,CAACC,GAAG,CAACH,MAAM,EAAEN,eAAe,CAAC,CAAA;AAEvD,EAAA,IAAIO,YAAY,GAAGG,aAAa,GAAGT,MAAM,EAAE;IACzC,OAAO;MACLI,KAAK,EAAEK,aAAa,GAAGT,MAAM;AAC7BK,MAAAA,MAAM,EAAEI,aAAAA;KACT,CAAA;AACF,GAAA;EAED,OAAO;AACLL,IAAAA,KAAK,EAAEE,YAAY;IACnBD,MAAM,EAAEC,YAAY,GAAGN,MAAAA;GACxB,CAAA;AACH,CAAA;AAEA;;;AAGG;AACG,SAAUU,YAAY,CAACC,SAAoB,EAAA;AAC/C;EACA,OAAOA,SAAS,CAACP,KAAK,GAAGO,SAAS,CAACN,MAAM,GACrCM,SAAS,CAACP,KAAK,GAAGO,SAAS,CAACC,YAAY,GACxCD,SAAS,CAACN,MAAM,GAAGM,SAAS,CAACE,aAAa,CAAA;AAChD,CAAA;AAEA;;AAEG;AACG,SAAUC,gBAAgB,CAC9BC,QAAe,EACfJ,SAAe,EACfK,QAAc,EACdC,IAAY,EACZhB,QAAY,EAAA;AAAZ,EAAA,IAAAA,QAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,QAAY,GAAA,CAAA,CAAA;AAAA,GAAA;AAEN,EAAA,IAAAC,KAAoBC,UAAU,CAACQ,SAAS,CAACP,KAAK,EAAEO,SAAS,CAACN,MAAM,EAAEJ,QAAQ,CAAC;IAAzEG,KAAK,WAAA;IAAEC,MAAM,YAA4D,CAAA;EAEjF,OAAO;AACLa,IAAAA,CAAC,EAAEC,qBAAqB,CAACJ,QAAQ,CAACG,CAAC,EAAEd,KAAK,EAAEY,QAAQ,CAACZ,KAAK,EAAEa,IAAI,CAAC;AACjEG,IAAAA,CAAC,EAAED,qBAAqB,CAACJ,QAAQ,CAACK,CAAC,EAAEf,MAAM,EAAEW,QAAQ,CAACX,MAAM,EAAEY,IAAI,CAAA;GACnE,CAAA;AACH,CAAA;AAEA,SAASE,qBAAqB,CAC5BJ,QAAgB,EAChBJ,SAAiB,EACjBK,QAAgB,EAChBC,IAAY,EAAA;EAEZ,IAAMI,WAAW,GAAIV,SAAS,GAAGM,IAAI,GAAI,CAAC,GAAGD,QAAQ,GAAG,CAAC,CAAA;EAEzD,OAAOM,KAAK,CAACP,QAAQ,EAAE,CAACM,WAAW,EAAEA,WAAW,CAAC,CAAA;AACnD,CAAA;AAEgB,SAAAE,wBAAwB,CAACC,MAAa,EAAEC,MAAa,EAAA;AACnE,EAAA,OAAOlB,IAAI,CAACmB,IAAI,CAACnB,IAAI,CAACoB,GAAG,CAACH,MAAM,CAACJ,CAAC,GAAGK,MAAM,CAACL,CAAC,EAAE,CAAC,CAAC,GAAGb,IAAI,CAACoB,GAAG,CAACH,MAAM,CAACN,CAAC,GAAGO,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AACvF,CAAA;AAEgB,SAAAU,wBAAwB,CAACJ,MAAa,EAAEC,MAAa,EAAA;EACnE,OAAQlB,IAAI,CAACsB,KAAK,CAACJ,MAAM,CAACL,CAAC,GAAGI,MAAM,CAACJ,CAAC,EAAEK,MAAM,CAACP,CAAC,GAAGM,MAAM,CAACN,CAAC,CAAC,GAAG,GAAG,GAAIX,IAAI,CAACuB,EAAE,CAAA;AAC/E,CAAA;AAEA;;;AAGG;AACa,SAAAC,kBAAkB,CAChCC,IAAW,EACXrB,SAAoB,EACpBK,QAAc,EACdhB,MAAc,EACdiB,IAAY,EACZhB,QAAY,EACZa,gBAAuB,EAAA;AADvB,EAAA,IAAAb,QAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,QAAY,GAAA,CAAA,CAAA;AAAA,GAAA;AACZ,EAAA,IAAAa,gBAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,gBAAuB,GAAA,IAAA,CAAA;AAAA,GAAA;AAEvB;AACA;AACA,EAAA,IAAMmB,WAAW,GAAGnB,gBAAgB,GAAGoB,SAAS,GAAGC,IAAI,CAAA;AAEvD,EAAA,IAAMC,aAAa,GAAGjC,UAAU,CAACQ,SAAS,CAACP,KAAK,EAAEO,SAAS,CAACN,MAAM,EAAEJ,QAAQ,CAAC,CAAA;AAC7E,EAAA,IAAMoC,oBAAoB,GAAGlC,UAAU,CAACQ,SAAS,CAACC,YAAY,EAAED,SAAS,CAACE,aAAa,EAAEZ,QAAQ,CAAC,CAAA;AAElG;AACA;AACA,EAAA,IAAMqC,sBAAsB,GAAG;AAC7BpB,IAAAA,CAAC,EAAEe,WAAW,CACZ,GAAG,EACF,CAAC,CAACG,aAAa,CAAChC,KAAK,GAAGY,QAAQ,CAACZ,KAAK,GAAGa,IAAI,IAAI,CAAC,GAAGe,IAAI,CAACd,CAAC,GAAGD,IAAI,IAAImB,aAAa,CAAChC,KAAK,GACxF,GAAG,CACN;AACDgB,IAAAA,CAAC,EAAEa,WAAW,CACZ,GAAG,EACF,CAAC,CAACG,aAAa,CAAC/B,MAAM,GAAGW,QAAQ,CAACX,MAAM,GAAGY,IAAI,IAAI,CAAC,GAAGe,IAAI,CAACZ,CAAC,GAAGH,IAAI,IACnEmB,aAAa,CAAC/B,MAAM,GACpB,GAAG,CACN;AACDD,IAAAA,KAAK,EAAE6B,WAAW,CAAC,GAAG,EAAIjB,QAAQ,CAACZ,KAAK,GAAGgC,aAAa,CAAChC,KAAK,GAAI,GAAG,GAAIa,IAAI,CAAC;AAC9EZ,IAAAA,MAAM,EAAE4B,WAAW,CAAC,GAAG,EAAIjB,QAAQ,CAACX,MAAM,GAAG+B,aAAa,CAAC/B,MAAM,GAAI,GAAG,GAAIY,IAAI,CAAA;GACjF,CAAA;AAED;EACA,IAAMsB,aAAa,GAAGhC,IAAI,CAACiC,KAAK,CAC9BP,WAAW,CACTI,oBAAoB,CAACjC,KAAK,EACzBkC,sBAAsB,CAAClC,KAAK,GAAGiC,oBAAoB,CAACjC,KAAK,GAAI,GAAG,CAClE,CACF,CAAA;EACD,IAAMqC,cAAc,GAAGlC,IAAI,CAACiC,KAAK,CAC/BP,WAAW,CACTI,oBAAoB,CAAChC,MAAM,EAC1BiC,sBAAsB,CAACjC,MAAM,GAAGgC,oBAAoB,CAAChC,MAAM,GAAI,GAAG,CACpE,CACF,CAAA;EACD,IAAMqC,kBAAkB,GAAGL,oBAAoB,CAACjC,KAAK,IAAIiC,oBAAoB,CAAChC,MAAM,GAAGL,MAAM,CAAA;AAE7F;AACA;AACA;AACA;EACA,IAAM2C,UAAU,GAAGD,kBAAkB,GACjC;IACEtC,KAAK,EAAEG,IAAI,CAACiC,KAAK,CAACC,cAAc,GAAGzC,MAAM,CAAC;AAC1CK,IAAAA,MAAM,EAAEoC,cAAAA;AACT,GAAA,GACD;AACErC,IAAAA,KAAK,EAAEmC,aAAa;AACpBlC,IAAAA,MAAM,EAAEE,IAAI,CAACiC,KAAK,CAACD,aAAa,GAAGvC,MAAM,CAAA;GAC1C,CAAA;EAEL,IAAM4C,iBAAiB,GAAAC,cAAA,CAAAA,cAAA,CAAA,EAAA,EAClBF,UAAU,CAAA,EAAA;IACbzB,CAAC,EAAEX,IAAI,CAACiC,KAAK,CACXP,WAAW,CACTI,oBAAoB,CAACjC,KAAK,GAAGuC,UAAU,CAACvC,KAAK,EAC5CkC,sBAAsB,CAACpB,CAAC,GAAGmB,oBAAoB,CAACjC,KAAK,GAAI,GAAG,CAC9D,CACF;IACDgB,CAAC,EAAEb,IAAI,CAACiC,KAAK,CACXP,WAAW,CACTI,oBAAoB,CAAChC,MAAM,GAAGsC,UAAU,CAACtC,MAAM,EAC9CiC,sBAAsB,CAAClB,CAAC,GAAGiB,oBAAoB,CAAChC,MAAM,GAAI,GAAG,CAC/D,CAAA;IAEJ,CAAA;EAED,OAAO;AAAEiC,IAAAA,sBAAsB,EAAAA,sBAAA;AAAEM,IAAAA,iBAAiB,EAAAA,iBAAAA;GAAE,CAAA;AACtD,CAAA;AAEA;;AAEG;AACH,SAASV,SAAS,CAACY,GAAW,EAAEC,KAAa,EAAA;AAC3C,EAAA,OAAOxC,IAAI,CAACC,GAAG,CAACsC,GAAG,EAAEvC,IAAI,CAACuC,GAAG,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAA;AAC1C,CAAA;AAEA,SAASZ,IAAI,CAACa,IAAY,EAAED,KAAa,EAAA;AACvC,EAAA,OAAOA,KAAK,CAAA;AACd,CAAA;AAEA;;AAEG;AACa,SAAAE,wCAAwC,CACtDX,sBAA4B,EAC5B3B,SAAoB,EACpBV,QAAgB,EAChBe,QAAc,EACdkC,OAAe,EACfC,OAAe,EAAA;AAEf,EAAA,IAAMf,aAAa,GAAGjC,UAAU,CAACQ,SAAS,CAACP,KAAK,EAAEO,SAAS,CAACN,MAAM,EAAEJ,QAAQ,CAAC,CAAA;AAE7E;EACA,IAAMgB,IAAI,GAAGK,KAAK,CACfN,QAAQ,CAACZ,KAAK,GAAGgC,aAAa,CAAChC,KAAK,IAAK,GAAG,GAAGkC,sBAAsB,CAAClC,KAAK,CAAC,EAC7E8C,OAAO,EACPC,OAAO,CACR,CAAA;AAED,EAAA,IAAMnB,IAAI,GAAG;IACXd,CAAC,EACED,IAAI,GAAGmB,aAAa,CAAChC,KAAK,GAAI,CAAC,GAChCY,QAAQ,CAACZ,KAAK,GAAG,CAAC,GAClBgC,aAAa,CAAChC,KAAK,GAAGa,IAAI,IAAIqB,sBAAsB,CAACpB,CAAC,GAAG,GAAG,CAAC;IAC/DE,CAAC,EACEH,IAAI,GAAGmB,aAAa,CAAC/B,MAAM,GAAI,CAAC,GACjCW,QAAQ,CAACX,MAAM,GAAG,CAAC,GACnB+B,aAAa,CAAC/B,MAAM,GAAGY,IAAI,IAAIqB,sBAAsB,CAAClB,CAAC,GAAG,GAAG,CAAA;GAChE,CAAA;EAED,OAAO;AAAEY,IAAAA,IAAI,EAAAA,IAAA;AAAEf,IAAAA,IAAI,EAAAA,IAAAA;GAAE,CAAA;AACvB,CAAA;AAEA;;AAEG;AACH,SAASmC,4BAA4B,CACnCR,iBAAuB,EACvBjC,SAAoB,EACpBK,QAAc,EAAA;AAEd,EAAA,IAAMqC,SAAS,GAAG3C,YAAY,CAACC,SAAS,CAAC,CAAA;AAEzC,EAAA,OAAOK,QAAQ,CAACX,MAAM,GAAGW,QAAQ,CAACZ,KAAK,GACnCY,QAAQ,CAACX,MAAM,IAAIuC,iBAAiB,CAACvC,MAAM,GAAGgD,SAAS,CAAC,GACxDrC,QAAQ,CAACZ,KAAK,IAAIwC,iBAAiB,CAACxC,KAAK,GAAGiD,SAAS,CAAC,CAAA;AAC5D,CAAA;AAEA;;AAEG;AACa,SAAAC,mCAAmC,CACjDV,iBAAuB,EACvBjC,SAAoB,EACpBV,QAAY,EACZe,QAAc,EACdkC,OAAe,EACfC,OAAe,EAAA;AAHf,EAAA,IAAAlD,QAAA,KAAA,KAAA,CAAA,EAAA;AAAAA,IAAAA,QAAY,GAAA,CAAA,CAAA;AAAA,GAAA;AAKZ,EAAA,IAAMoC,oBAAoB,GAAGlC,UAAU,CAACQ,SAAS,CAACC,YAAY,EAAED,SAAS,CAACE,aAAa,EAAEZ,QAAQ,CAAC,CAAA;AAElG,EAAA,IAAMgB,IAAI,GAAGK,KAAK,CAChB8B,4BAA4B,CAACR,iBAAiB,EAAEjC,SAAS,EAAEK,QAAQ,CAAC,EACpEkC,OAAO,EACPC,OAAO,CACR,CAAA;EAED,IAAMI,QAAQ,GACZvC,QAAQ,CAACX,MAAM,GAAGW,QAAQ,CAACZ,KAAK,GAC5BY,QAAQ,CAACX,MAAM,GAAGuC,iBAAiB,CAACvC,MAAM,GAC1CW,QAAQ,CAACZ,KAAK,GAAGwC,iBAAiB,CAACxC,KAAK,CAAA;AAE9C,EAAA,IAAM4B,IAAI,GAAG;AACXd,IAAAA,CAAC,EACC,CAAC,CAACmB,oBAAoB,CAACjC,KAAK,GAAGwC,iBAAiB,CAACxC,KAAK,IAAI,CAAC,GAAGwC,iBAAiB,CAAC1B,CAAC,IAAIqC,QAAQ;AAC/FnC,IAAAA,CAAC,EACC,CAAC,CAACiB,oBAAoB,CAAChC,MAAM,GAAGuC,iBAAiB,CAACvC,MAAM,IAAI,CAAC,GAAGuC,iBAAiB,CAACxB,CAAC,IACnFmC,QAAAA;GACH,CAAA;EACD,OAAO;AAAEvB,IAAAA,IAAI,EAAAA,IAAA;AAAEf,IAAAA,IAAI,EAAAA,IAAAA;GAAE,CAAA;AACvB,CAAA;AAEA;;AAEG;AACa,SAAAuC,SAAS,CAACC,CAAQ,EAAEC,CAAQ,EAAA;EAC1C,OAAO;IACLxC,CAAC,EAAE,CAACwC,CAAC,CAACxC,CAAC,GAAGuC,CAAC,CAACvC,CAAC,IAAI,CAAC;IAClBE,CAAC,EAAE,CAACsC,CAAC,CAACtC,CAAC,GAAGqC,CAAC,CAACrC,CAAC,IAAI,CAAA;GAClB,CAAA;AACH,CAAA;AAEM,SAAUuC,cAAc,CAACC,WAAmB,EAAA;AAChD,EAAA,OAAQA,WAAW,GAAGrD,IAAI,CAACuB,EAAE,GAAI,GAAG,CAAA;AACtC,CAAA;AAEA;;AAEG;SACa3B,UAAU,CAACC,KAAa,EAAEC,MAAc,EAAEJ,QAAgB,EAAA;AACxE,EAAA,IAAM4D,MAAM,GAAGF,cAAc,CAAC1D,QAAQ,CAAC,CAAA;EAEvC,OAAO;IACLG,KAAK,EAAEG,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACwD,GAAG,CAACF,MAAM,CAAC,GAAGzD,KAAK,CAAC,GAAGG,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACyD,GAAG,CAACH,MAAM,CAAC,GAAGxD,MAAM,CAAC;IAC/EA,MAAM,EAAEE,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACyD,GAAG,CAACH,MAAM,CAAC,GAAGzD,KAAK,CAAC,GAAGG,IAAI,CAACuD,GAAG,CAACvD,IAAI,CAACwD,GAAG,CAACF,MAAM,CAAC,GAAGxD,MAAM,CAAA;GAChF,CAAA;AACH,CAAA;AAEA;;AAEG;SACaiB,KAAK,CAACyB,KAAa,EAAEvC,GAAW,EAAEsC,GAAW,EAAA;AAC3D,EAAA,OAAOvC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACuC,GAAG,CAACC,KAAK,EAAEvC,GAAG,CAAC,EAAEsC,GAAG,CAAC,CAAA;AAC5C,CAAA;AAEA;;AAEG;SACamB,UAAU,GAAA;EAAC,IAAgEC,IAAA,GAAA,EAAA,CAAA;OAAhE,IAAgEC,EAAA,GAAA,CAAA,EAAhEA,EAAgE,GAAAC,SAAA,CAAAC,MAAA,EAAhEF,EAAgE,EAAA,EAAA;AAAhED,IAAAA,IAAgE,CAAAC,EAAA,CAAA,GAAAC,SAAA,CAAAD,EAAA,CAAA,CAAA;;AACzF,EAAA,OAAOD,IAAI,CACRI,MAAM,CAAC,UAACvB,KAAK,EAAA;IACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;AACjD,MAAA,OAAO,IAAI,CAAA;AACZ,KAAA;AAED,IAAA,OAAO,KAAK,CAAA;GACb,CAAC,CACDE,IAAI,CAAC,GAAG,CAAC,CACTC,IAAI,EAAE,CAAA;AACX;;;;ACvOA,IAAMC,QAAQ,GAAG,CAAC,CAAA;AAClB,IAAMC,QAAQ,GAAG,CAAC,CAAA;AAClB,IAAMC,aAAa,GAAG,CAAC,CAAA;AASvB,IAAAC,OAAA,gBAAA,UAAAC,MAAA,EAAA;AAAsBC,EAAAA,eAAoC,CAAAF,OAAA,EAAAC,MAAA,CAAA,CAAA;AAA1D,EAAA,SAAAD,OAAA,GAAA;AAAA,IAAA,IA+yBCG,KAAA,GAAAF,MAAA,KAAA,IAAA,IAAAA,MAAA,CAAAG,KAAA,CAAA,IAAA,EAAAZ,SAAA,CAAA,IAAA,IAAA,CAAA;AA3xBCW,IAAAA,KAAA,CAAAE,UAAU,GAAoCC,gBAAK,CAACC,SAAS,EAAE,CAAA;AAC/DJ,IAAAA,KAAA,CAAAK,QAAQ,GAAsCF,gBAAK,CAACC,SAAS,EAAE,CAAA;AAC/DJ,IAAAA,KAAA,CAAAM,QAAQ,GAAsCH,gBAAK,CAACC,SAAS,EAAE,CAAA;IAC/DJ,KAAiB,CAAAO,iBAAA,GAAU;AAAEpE,MAAAA,CAAC,EAAE,CAAC;AAAEE,MAAAA,CAAC,EAAE,CAAA;KAAG,CAAA;IACzC2D,KAAY,CAAAQ,YAAA,GAA0B,IAAI,CAAA;IAC1CR,KAAQ,CAAAS,QAAA,GAA4B,IAAI,CAAA;IACxCT,KAAa,CAAAU,aAAA,GAAmB,IAAI,CAAA;IACpCV,KAAA,CAAApE,SAAS,GAAc;AAAEP,MAAAA,KAAK,EAAE,CAAC;AAAEC,MAAAA,MAAM,EAAE,CAAC;AAAEO,MAAAA,YAAY,EAAE,CAAC;AAAEC,MAAAA,aAAa,EAAE,CAAA;KAAG,CAAA;IACjFkE,KAAiB,CAAAW,iBAAA,GAAU;AAAExE,MAAAA,CAAC,EAAE,CAAC;AAAEE,MAAAA,CAAC,EAAE,CAAA;KAAG,CAAA;IACzC2D,KAAa,CAAAY,aAAA,GAAU;AAAEzE,MAAAA,CAAC,EAAE,CAAC;AAAEE,MAAAA,CAAC,EAAE,CAAA;KAAG,CAAA;IACrC2D,KAAgB,CAAAa,gBAAA,GAAG,CAAC,CAAA;IACpBb,KAAoB,CAAAc,oBAAA,GAAG,CAAC,CAAA;IACxBd,KAAU,CAAAe,UAAA,GAAG,KAAK,CAAA;IAClBf,KAAiB,CAAAgB,iBAAA,GAAG,CAAC,CAAA;IACrBhB,KAAiB,CAAAiB,iBAAA,GAAG,CAAC,CAAA;IACrBjB,KAAc,CAAAkB,cAAA,GAAkB,IAAI,CAAA;IACpClB,KAAe,CAAAmB,eAAA,GAAkB,IAAI,CAAA;IACrCnB,KAAU,CAAAoB,UAAA,GAAkB,IAAI,CAAA;IAChCpB,KAAA,CAAAqB,UAAU,GAAoB,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAI,CAAA;IAC/EtB,KAAA,CAAAuB,aAAa,GAAkB,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI,CAAA;IAC5ExB,KAAc,CAAAyB,cAAA,GAA0B,IAAI,CAAA;IAE5CzB,KAAA,CAAA0B,KAAK,GAAU;AACbzF,MAAAA,QAAQ,EAAE,IAAI;AACd0F,MAAAA,mBAAmB,EAAE,KAAK;AAC1BC,MAAAA,cAAc,EAAEC,SAAAA;KACjB,CAAA;IA2GD7B,KAAA,CAAA8B,kBAAkB,GAAG,YAAA;MACnB,IAAI,OAAON,MAAM,CAACO,cAAc,KAAK,WAAW,IAAI,CAAC/B,KAAI,CAACQ,YAAY,EAAE;AACtE,QAAA,OAAA;AACD,OAAA;MACD,IAAIwB,aAAa,GAAG,IAAI,CAAA;MACxBhC,KAAI,CAACyB,cAAc,GAAG,IAAID,MAAM,CAACO,cAAc,CAAC,UAACE,OAAO,EAAA;AACtD,QAAA,IAAID,aAAa,EAAE;UACjBA,aAAa,GAAG,KAAK,CAAA;AACrB,UAAA,OAAA;AACD,SAAA;QACDhC,KAAI,CAACkC,YAAY,EAAE,CAAA;AACrB,OAAC,CAAC,CAAA;MACFlC,KAAI,CAACyB,cAAc,CAACU,OAAO,CAACnC,KAAI,CAACQ,YAAY,CAAC,CAAA;KAC/C,CAAA;AAED;AACAR,IAAAA,KAAiB,CAAAoC,iBAAA,GAAG,UAACC,CAAQ,EAAK;MAAA,OAAAA,CAAC,CAACC,cAAc,EAAE,CAAA;KAAA,CAAA;IAEpDtC,KAAA,CAAAuC,WAAW,GAAG,YAAA;AACZ,MAAA,IAAI,CAACvC,KAAI,CAACqB,UAAU,EAAE,OAAA;MACtBrB,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,WAAW,EAAExC,KAAI,CAACyC,WAAW,CAAC,CAAA;MAClEzC,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,SAAS,EAAExC,KAAI,CAAC0C,aAAa,CAAC,CAAA;MAClE1C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,WAAW,EAAExC,KAAI,CAAC2C,WAAW,CAAC,CAAA;MAClE3C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,UAAU,EAAExC,KAAI,CAAC0C,aAAa,CAAC,CAAA;MACnE1C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,eAAe,EAAExC,KAAI,CAAC4C,eAAgC,CAAC,CAAA;MAC3F5C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,YAAY,EAAExC,KAAI,CAAC6C,YAA6B,CAAC,CAAA;MACrF7C,KAAI,CAACqB,UAAU,CAACmB,mBAAmB,CAAC,QAAQ,EAAExC,KAAI,CAAC8C,QAAQ,CAAC,CAAA;KAC7D,CAAA;IAED9C,KAAA,CAAA+C,gBAAgB,GAAG,YAAA;AACjB,MAAA,IAAI/C,KAAI,CAACQ,YAAY,EAAER,KAAI,CAACQ,YAAY,CAACgC,mBAAmB,CAAC,OAAO,EAAExC,KAAI,CAACgD,OAAO,CAAC,CAAA;MACnF,IAAIhD,KAAI,CAACoB,UAAU,EAAE;AACnB6B,QAAAA,YAAY,CAACjD,KAAI,CAACoB,UAAU,CAAC,CAAA;AAC9B,OAAA;KACF,CAAA;IAEDpB,KAAA,CAAAkD,WAAW,GAAG,YAAA;AACZ,MAAA,IAAMjH,QAAQ,GAAG+D,KAAI,CAACkC,YAAY,EAAE,CAAA;AAEpC,MAAA,IAAIjG,QAAQ,EAAE;QACZ+D,KAAI,CAACmD,YAAY,EAAE,CAAA;AACnBnD,QAAAA,KAAI,CAACoD,cAAc,CAACnH,QAAQ,CAAC,CAAA;AAC9B,OAAA;AAED,MAAA,IAAI+D,KAAI,CAACqD,KAAK,CAACC,aAAa,EAAE;QAC5BtD,KAAI,CAACqD,KAAK,CAACC,aAAa,CAACtD,KAAI,CAACpE,SAAS,CAAC,CAAA;AACzC,OAAA;KACF,CAAA;AAEDoE,IAAAA,KAAc,CAAAoD,cAAA,GAAG,UAACnH,QAAc,EAAA;AAC9B,MAAA,IAAI+D,KAAI,CAACqD,KAAK,CAACE,6BAA6B,EAAE;AACtC,QAAA,IAAApI,KAAiB+C,wCAAwC,CAC7D8B,KAAI,CAACqD,KAAK,CAACE,6BAA6B,EACxCvD,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAACqD,KAAK,CAACnI,QAAQ,EACnBe,QAAQ,EACR+D,KAAI,CAACqD,KAAK,CAAClF,OAAO,EAClB6B,KAAI,CAACqD,KAAK,CAACjF,OAAO,CACnB;UAPOnB,IAAI,UAAA;UAAEf,IAAI,UAOjB,CAAA;AAED8D,QAAAA,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACvG,IAAI,CAAC,CAAA;AAC7B+C,QAAAA,KAAI,CAACqD,KAAK,CAACI,YAAY,IAAIzD,KAAI,CAACqD,KAAK,CAACI,YAAY,CAACvH,IAAI,CAAC,CAAA;AACzD,OAAA,MAAM,IAAI8D,KAAI,CAACqD,KAAK,CAACK,wBAAwB,EAAE;AACxC,QAAA,IAAAC,KAAiBpF,mCAAmC,CACxDyB,KAAI,CAACqD,KAAK,CAACK,wBAAwB,EACnC1D,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAACqD,KAAK,CAACnI,QAAQ,EACnBe,QAAQ,EACR+D,KAAI,CAACqD,KAAK,CAAClF,OAAO,EAClB6B,KAAI,CAACqD,KAAK,CAACjF,OAAO,CACnB;UAPOnB,IAAI,UAAA;UAAEf,IAAI,UAOjB,CAAA;AAED8D,QAAAA,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACvG,IAAI,CAAC,CAAA;AAC7B+C,QAAAA,KAAI,CAACqD,KAAK,CAACI,YAAY,IAAIzD,KAAI,CAACqD,KAAK,CAACI,YAAY,CAACvH,IAAI,CAAC,CAAA;AACzD,OAAA;KACF,CAAA;IA+BD8D,KAAA,CAAAkC,YAAY,GAAG,YAAA;;AACb,MAAA,IAAM0B,QAAQ,GAAG5D,KAAI,CAACK,QAAQ,CAACwD,OAAO,IAAI7D,KAAI,CAACM,QAAQ,CAACuD,OAAO,CAAA;AAE/D,MAAA,IAAID,QAAQ,IAAI5D,KAAI,CAACQ,YAAY,EAAE;QACjCR,KAAI,CAACU,aAAa,GAAGV,KAAI,CAACQ,YAAY,CAACsD,qBAAqB,EAAE,CAAA;QAC9D9D,KAAI,CAAC+D,qBAAqB,EAAE,CAAA;AAC5B,QAAA,IAAMC,eAAe,GAAGhE,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAG2E,KAAI,CAACU,aAAa,CAACpF,MAAM,CAAA;QAC5E,IAAMO,YAAY,GAChB,CAAA,CAAAV,EAAA,GAAA6E,KAAI,CAACK,QAAQ,CAACwD,OAAO,MAAE,IAAA,IAAA1I,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAU,YAAY,MAAI,CAAA8H,EAAA,GAAA3D,KAAI,CAACM,QAAQ,CAACuD,OAAO,0CAAEI,UAAU,CAAA,IAAI,CAAC,CAAA;QAC/E,IAAMnI,aAAa,GACjB,CAAA,CAAAoI,EAAA,GAAAlE,KAAI,CAACK,QAAQ,CAACwD,OAAO,MAAE,IAAA,IAAAK,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAApI,aAAa,MAAI,CAAAqI,EAAA,GAAAnE,KAAI,CAACM,QAAQ,CAACuD,OAAO,0CAAEO,WAAW,CAAA,IAAI,CAAC,CAAA;AACjF,QAAA,IAAMC,iBAAiB,GACrBT,QAAQ,CAACU,WAAW,GAAGzI,YAAY,IAAI+H,QAAQ,CAACW,YAAY,GAAGzI,aAAa,CAAA;AAC9E,QAAA,IAAM0I,WAAW,GAAG3I,YAAY,GAAGC,aAAa,CAAA;AAEhD;AACA;AACA;AACA;AACA;AACA;QACA,IAAI2I,iBAAiB,SAAM,CAAA;AAE3B,QAAA,IAAIJ,iBAAiB,EAAE;AACrB,UAAA,QAAQrE,KAAI,CAAC0B,KAAK,CAACE,cAAc;AAC/B,YAAA,QAAA;AACA,YAAA,KAAK,SAAS;AACZ6C,cAAAA,iBAAiB,GACfT,eAAe,GAAGQ,WAAW,GACzB;AACEnJ,gBAAAA,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACpF,MAAM,GAAGkJ,WAAW;AAC9ClJ,gBAAAA,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACpF,MAAAA;AAC5B,eAAA,GACD;AACED,gBAAAA,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACrF,KAAK;AAC/BC,gBAAAA,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAGmJ,WAAAA;eACpC,CAAA;AACP,cAAA,MAAA;AACF,YAAA,KAAK,kBAAkB;AACrBC,cAAAA,iBAAiB,GAAG;AAClBpJ,gBAAAA,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACrF,KAAK;AAC/BC,gBAAAA,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAGmJ,WAAAA;eACpC,CAAA;AACD,cAAA,MAAA;AACF,YAAA,KAAK,gBAAgB;AACnBC,cAAAA,iBAAiB,GAAG;AAClBpJ,gBAAAA,KAAK,EAAE2E,KAAI,CAACU,aAAa,CAACpF,MAAM,GAAGkJ,WAAW;AAC9ClJ,gBAAAA,MAAM,EAAE0E,KAAI,CAACU,aAAa,CAACpF,MAAAA;eAC5B,CAAA;AACD,cAAA,MAAA;AAAK,WAAA;AAEV,SAAA,MAAM;AACLmJ,UAAAA,iBAAiB,GAAG;YAClBpJ,KAAK,EAAEuI,QAAQ,CAACU,WAAW;YAC3BhJ,MAAM,EAAEsI,QAAQ,CAACW,YAAAA;WAClB,CAAA;AACF,SAAA;AAEDvE,QAAAA,KAAI,CAACpE,SAAS,GAAAkC,cAAA,CAAAA,cAAA,CAAA,EAAA,EACT2G,iBAAiB,CAAA,EAAA;AACpB5I,UAAAA,YAAY,EAAAA,YAAA;AACZC,UAAAA,aAAa,EAAAA,aAAAA;AAAA,SAAA,CACd,CAAA;AAED;AACA,QAAA,IAAIkE,KAAI,CAACqD,KAAK,CAACqB,YAAY,EAAE;UAC3B1E,KAAI,CAACqD,KAAK,CAACqB,YAAY,CAAC1E,KAAI,CAACpE,SAAS,CAAC,CAAA;AACxC,SAAA;QAED,IAAMK,QAAQ,GAAG+D,KAAI,CAACqD,KAAK,CAACpH,QAAQ,GAChC+D,KAAI,CAACqD,KAAK,CAACpH,QAAQ,GACnBrB,WAAW,CACToF,KAAI,CAACpE,SAAS,CAACP,KAAK,EACpB2E,KAAI,CAACpE,SAAS,CAACN,MAAM,EACrB0E,KAAI,CAACU,aAAa,CAACrF,KAAK,EACxB2E,KAAI,CAACU,aAAa,CAACpF,MAAM,EACzB0E,KAAI,CAACqD,KAAK,CAACpI,MAAM,EACjB+E,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,CAAA;QAEL,IACE,CAAA,CAAAyJ,EAAA,GAAA3E,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,MAAA,IAAA,IAAA0I,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAErJ,MAAM,MAAKW,QAAQ,CAACX,MAAM,IAC/C,CAAA,CAAAsJ,EAAA,GAAA5E,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,MAAE,IAAA,IAAA2I,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAvJ,KAAK,MAAKY,QAAQ,CAACZ,KAAK,EAC7C;AACA2E,UAAAA,KAAI,CAACqD,KAAK,CAACwB,gBAAgB,IAAI7E,KAAI,CAACqD,KAAK,CAACwB,gBAAgB,CAAC5I,QAAQ,CAAC,CAAA;AACrE,SAAA;QACD+D,KAAI,CAAC8E,QAAQ,CAAC;AAAE7I,UAAAA,QAAQ,EAAAA,QAAAA;AAAE,SAAA,EAAE+D,KAAI,CAAC+E,qBAAqB,CAAC,CAAA;AACvD;AACA,QAAA,IAAI/E,KAAI,CAACqD,KAAK,CAAC2B,WAAW,EAAE;AAC1BhF,UAAAA,KAAI,CAACqD,KAAK,CAAC2B,WAAW,CAAC/I,QAAQ,CAAC,CAAA;AACjC,SAAA;AAED,QAAA,OAAOA,QAAQ,CAAA;AAChB,OAAA;KACF,CAAA;IAED+D,KAAA,CAAA+D,qBAAqB,GAAG,YAAA;MACtB,IAAI/D,KAAI,CAACQ,YAAY,EAAE;AACrB,QAAA,IAAMyE,MAAM,GAAGjF,KAAI,CAACQ,YAAY,CAACsD,qBAAqB,EAAE,CAAA;QACxD9D,KAAI,CAACO,iBAAiB,GAAG;UAAEpE,CAAC,EAAE8I,MAAM,CAACC,IAAI;UAAE7I,CAAC,EAAE4I,MAAM,CAACE,GAAAA;SAAK,CAAA;AAC3D,OAAA;KACF,CAAA;AAYDnF,IAAAA,KAAW,CAAAoF,WAAA,GAAG,UAAC/C,CAA+C,EAAA;AAC5D,MAAA,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE,OAAA;MACtBgB,CAAC,CAACC,cAAc,EAAE,CAAA;MAClBtC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,WAAW,EAAErF,KAAI,CAACyC,WAAW,CAAC,CAAA;MAC/DzC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,SAAS,EAAErF,KAAI,CAAC0C,aAAa,CAAC,CAAA;MAC/D1C,KAAI,CAAC+D,qBAAqB,EAAE,CAAA;MAC5B/D,KAAI,CAACsF,WAAW,CAACzF,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC,CAAC,CAAA;KAC3C,CAAA;AAEDrC,IAAAA,KAAA,CAAAyC,WAAW,GAAG,UAACJ,CAAa;MAAK,OAAArC,KAAI,CAACwF,MAAM,CAAC3F,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC,CAAC,CAAA;KAAA,CAAA;AAEtErC,IAAAA,KAAQ,CAAA8C,QAAA,GAAG,UAACT,CAAQ,EAAA;AAClB,MAAA,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE,OAAA;MACtBgB,CAAC,CAACC,cAAc,EAAE,CAAA;MAClBtC,KAAI,CAAC+D,qBAAqB,EAAE,CAAA;KAC7B,CAAA;AAED/D,IAAAA,KAAY,CAAAyF,YAAA,GAAG,UAACpD,CAAmC,EAAA;AACjD,MAAA,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE,OAAA;MACtBrB,KAAI,CAACe,UAAU,GAAG,IAAI,CAAA;AACtB,MAAA,IAAIf,KAAI,CAACqD,KAAK,CAACqC,cAAc,IAAI,CAAC1F,KAAI,CAACqD,KAAK,CAACqC,cAAc,CAACrD,CAAC,CAAC,EAAE;AAC9D,QAAA,OAAA;AACD,OAAA;MAEDrC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,WAAW,EAAErF,KAAI,CAAC2C,WAAW,EAAE;AAAEgD,QAAAA,OAAO,EAAE,KAAA;OAAO,CAAC,CAAA;MACnF3F,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,UAAU,EAAErF,KAAI,CAAC0C,aAAa,CAAC,CAAA;MAEhE1C,KAAI,CAAC+D,qBAAqB,EAAE,CAAA;AAE5B,MAAA,IAAI1B,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;AAC1BU,QAAAA,KAAI,CAAC6F,YAAY,CAACxD,CAAC,CAAC,CAAA;OACrB,MAAM,IAAIA,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;AACjCU,QAAAA,KAAI,CAACsF,WAAW,CAACzF,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACtD,OAAA;KACF,CAAA;AAED5F,IAAAA,KAAW,CAAA2C,WAAA,GAAG,UAACN,CAAa,EAAA;AAC1B;MACAA,CAAC,CAACC,cAAc,EAAE,CAAA;AAClB,MAAA,IAAID,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;AAC1BU,QAAAA,KAAI,CAAC+F,WAAW,CAAC1D,CAAC,CAAC,CAAA;OACpB,MAAM,IAAIA,CAAC,CAACuD,OAAO,CAACtG,MAAM,KAAK,CAAC,EAAE;AACjCU,QAAAA,KAAI,CAACwF,MAAM,CAAC3F,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACjD,OAAA;KACF,CAAA;AAED5F,IAAAA,KAAc,CAAAgG,cAAA,GAAG,UAAC3D,CAAe,EAAA;AAC/B,MAAA,IAAI,CAACrC,KAAI,CAACqB,UAAU,EAAE,OAAA;MACtBgB,CAAC,CAACC,cAAc,EAAE,CAAA;MAClBtC,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,eAAe,EAAErF,KAAI,CAAC4C,eAAgC,CAAC,CAAA;MACxF5C,KAAI,CAACqB,UAAU,CAACgE,gBAAgB,CAAC,YAAY,EAAErF,KAAI,CAAC6C,YAA6B,CAAC,CAAA;AAClF7C,MAAAA,KAAI,CAACa,gBAAgB,GAAGb,KAAI,CAACqD,KAAK,CAACnH,IAAI,CAAA;AACvC8D,MAAAA,KAAI,CAACc,oBAAoB,GAAGd,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CAAA;KAChD,CAAA;AAED8E,IAAAA,KAAe,CAAA4C,eAAA,GAAG,UAACP,CAAe,EAAA;MAChCA,CAAC,CAACC,cAAc,EAAE,CAAA;MAClB,IAAItC,KAAI,CAACe,UAAU,EAAE;AACnB;AACA,QAAA,OAAA;AACD,OAAA;AAED,MAAA,IAAMkF,KAAK,GAAGpG,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC,CAAA;MACtC,IAAM6D,OAAO,GAAGlG,KAAI,CAACa,gBAAgB,GAAG,CAAC,GAAGwB,CAAC,CAAC8D,KAAK,CAAA;AACnDnG,MAAAA,KAAI,CAACoG,UAAU,CAACF,OAAO,EAAED,KAAK,EAAE;AAAEI,QAAAA,oBAAoB,EAAE,IAAA;AAAI,OAAE,CAAC,CAAA;AAC/D,MAAA,IAAIrG,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,EAAE;QAC/B,IAAMC,WAAW,GAAGvG,KAAI,CAACc,oBAAoB,GAAGuB,CAAC,CAACnH,QAAQ,CAAA;AAC1D8E,QAAAA,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,CAACC,WAAW,CAAC,CAAA;AACzC,OAAA;KACF,CAAA;AAEDvG,IAAAA,KAAY,CAAA6C,YAAA,GAAG,UAACR,CAAe,EAAA;MAC7BrC,KAAI,CAACuC,WAAW,EAAE,CAAA;KACnB,CAAA;AAEDvC,IAAAA,KAAW,CAAAsF,WAAA,GAAG,UAACnK,EAAe,EAAA;;UAAbgB,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA,CAAA;MACnB2D,KAAI,CAACW,iBAAiB,GAAG;AAAExE,QAAAA,CAAC,GAAA;AAAEE,QAAAA,CAAC,EAAAA,CAAAA;OAAE,CAAA;AACjC2D,MAAAA,KAAI,CAACY,aAAa,GAAQ9C,cAAA,CAAA,EAAA,EAAAkC,KAAI,CAACqD,KAAK,CAACpG,IAAI,CAAE,CAAA;MAC3C,CAAAiH,EAAA,GAAA,MAAAlE,KAAI,CAACqD,KAAK,EAACmD,kBAAkB,kDAAI,CAAA;KAClC,CAAA;AAEDxG,IAAAA,KAAM,CAAAwF,MAAA,GAAG,UAACrK,EAAe,EAAA;UAAbgB,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA,CAAA;AACd,MAAA,IAAI,CAAC2D,KAAI,CAACuB,aAAa,EAAE,OAAA;AACzB,MAAA,IAAIvB,KAAI,CAACkB,cAAc,EAAElB,KAAI,CAACuB,aAAa,CAACkF,oBAAoB,CAACzG,KAAI,CAACkB,cAAc,CAAC,CAAA;MAErFlB,KAAI,CAACkB,cAAc,GAAGlB,KAAI,CAACuB,aAAa,CAACmF,qBAAqB,CAAC,YAAA;AAC7D,QAAA,IAAI,CAAC1G,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE,OAAA;AAC1B,QAAA,IAAIE,CAAC,KAAK0F,SAAS,IAAIxF,CAAC,KAAKwF,SAAS,EAAE,OAAA;QACxC,IAAM8E,OAAO,GAAGxK,CAAC,GAAG6D,KAAI,CAACW,iBAAiB,CAACxE,CAAC,CAAA;QAC5C,IAAMyK,OAAO,GAAGvK,CAAC,GAAG2D,KAAI,CAACW,iBAAiB,CAACtE,CAAC,CAAA;AAC5C,QAAA,IAAMwK,iBAAiB,GAAG;AACxB1K,UAAAA,CAAC,EAAE6D,KAAI,CAACY,aAAa,CAACzE,CAAC,GAAGwK,OAAO;AACjCtK,UAAAA,CAAC,EAAE2D,KAAI,CAACY,aAAa,CAACvE,CAAC,GAAGuK,OAAAA;SAC3B,CAAA;AAED,QAAA,IAAME,WAAW,GAAG9G,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAC3CA,gBAAgB,CACd8K,iBAAiB,EACjB7G,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD2L,iBAAiB,CAAA;AACrB7G,QAAAA,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACsD,WAAW,CAAC,CAAA;AACtC,OAAC,CAAC,CAAA;KACH,CAAA;IAED9G,KAAA,CAAA0C,aAAa,GAAG,YAAA;;MACd1C,KAAI,CAACe,UAAU,GAAG,KAAK,CAAA;MACvBf,KAAI,CAACuC,WAAW,EAAE,CAAA;MAClBvC,KAAI,CAACmD,YAAY,EAAE,CAAA;MACnB,CAAAQ,EAAA,GAAA,MAAA3D,KAAI,CAACqD,KAAK,EAAC0D,gBAAgB,kDAAI,CAAA;KAChC,CAAA;AA+BD/G,IAAAA,KAAO,CAAAgD,OAAA,GAAG,UAACX,CAAa,EAAA;AACtB,MAAA,IAAI,CAACrC,KAAI,CAACuB,aAAa,EAAE,OAAA;AACzB,MAAA,IAAIvB,KAAI,CAACqD,KAAK,CAAC2D,cAAc,IAAI,CAAChH,KAAI,CAACqD,KAAK,CAAC2D,cAAc,CAAC3E,CAAC,CAAC,EAAE;AAC9D,QAAA,OAAA;AACD,OAAA;MAEDA,CAAC,CAACC,cAAc,EAAE,CAAA;AAClB,MAAA,IAAM2D,KAAK,GAAGpG,OAAO,CAAC0F,aAAa,CAAClD,CAAC,CAAC,CAAA;AAC9B,MAAA,IAAA4E,MAAM,GAAKC,kCAAc,CAAC7E,CAAC,CAAC,OAAtB,CAAA;AACd,MAAA,IAAM6D,OAAO,GAAGlG,KAAI,CAACqD,KAAK,CAACnH,IAAI,GAAI+K,MAAM,GAAGjH,KAAI,CAACqD,KAAK,CAAC8D,SAAS,GAAI,GAAG,CAAA;AACvEnH,MAAAA,KAAI,CAACoG,UAAU,CAACF,OAAO,EAAED,KAAK,EAAE;AAAEI,QAAAA,oBAAoB,EAAE,IAAA;AAAI,OAAE,CAAC,CAAA;AAE/D,MAAA,IAAI,CAACrG,KAAI,CAAC0B,KAAK,CAACC,mBAAmB,EAAE;QACnC3B,KAAI,CAAC8E,QAAQ,CAAC;AAAEnD,UAAAA,mBAAmB,EAAE,IAAA;AAAM,SAAA,EAAE,YAAM;UAAA,IAAAxG,EAAA,EAAAwI,EAAA,CAAA;UAAA,OAAA,MAAA,CAAAxI,EAAA,GAAA6E,KAAI,CAACqD,KAAK,EAACmD,kBAAkB,MAAI,IAAA,IAAA7C,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAyD,IAAA,CAAAjM,EAAA,CAAA,CAAA;AAAA,SAAA,CAAC,CAAA;AACtF,OAAA;MAED,IAAI6E,KAAI,CAACoB,UAAU,EAAE;AACnB6B,QAAAA,YAAY,CAACjD,KAAI,CAACoB,UAAU,CAAC,CAAA;AAC9B,OAAA;MACDpB,KAAI,CAACoB,UAAU,GAAGpB,KAAI,CAACuB,aAAa,CAAC8F,UAAU,CAC7C,YAAA;QAAM,OAAArH,KAAI,CAAC8E,QAAQ,CAAC;AAAEnD,UAAAA,mBAAmB,EAAE,KAAA;AAAO,SAAA,EAAE;;UAAM,OAAA,CAAAgC,EAAA,GAAA,MAAA3D,KAAI,CAACqD,KAAK,EAAC0D,gBAAgB,MAAI,IAAA,IAAApD,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAyD,IAAA,CAAAjM,EAAA,CAAA,CAAA;SAAA,CAAC,CAAA;OAAA,EAC1F,GAAG,CACJ,CAAA;KACF,CAAA;AAED6E,IAAAA,KAAA,CAAAsH,mBAAmB,GAAG,UAACnM,EAAe,EAAEoM,gBAAuB,EAAA;UAAtCpL,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA,CAAA;AAC3B,MAAA,IAAI,CAAC2D,KAAI,CAACU,aAAa,EAAE;AACvB,QAAA,MAAM,IAAI8G,KAAK,CAAC,4BAA4B,CAAC,CAAA;AAC9C,OAAA;MACD,OAAO;AACLrL,QAAAA,CAAC,EAAE6D,KAAI,CAACU,aAAa,CAACrF,KAAK,GAAG,CAAC,IAAIc,CAAC,GAAGoL,gBAAgB,CAACpL,CAAC,CAAC;AAC1DE,QAAAA,CAAC,EAAE2D,KAAI,CAACU,aAAa,CAACpF,MAAM,GAAG,CAAC,IAAIe,CAAC,GAAGkL,gBAAgB,CAAClL,CAAC,CAAA;OAC3D,CAAA;KACF,CAAA;AAED2D,IAAAA,KAAe,CAAAyH,eAAA,GAAG,UAACtM,EAAe,EAAA;UAAbgB,CAAC,GAAAhB,EAAA,CAAAgB,CAAA;QAAEE,CAAC,GAAAlB,EAAA,CAAAkB,CAAA,CAAA;AACjB,MAAA,IAAAsH,EAAA,GAAiB3D,KAAI,CAACqD,KAAK;QAAzBpG,IAAI,GAAA0G,EAAA,CAAA1G,IAAA;QAAEf,IAAI,GAAAyH,EAAA,CAAAzH,IAAe,CAAA;MACjC,OAAO;QACLC,CAAC,EAAE,CAACA,CAAC,GAAGc,IAAI,CAACd,CAAC,IAAID,IAAI;AACtBG,QAAAA,CAAC,EAAE,CAACA,CAAC,GAAGY,IAAI,CAACZ,CAAC,IAAIH,IAAAA;OACnB,CAAA;KACF,CAAA;IAED8D,KAAA,CAAAoG,UAAU,GAAG,UAAClK,IAAY,EAAE+J,KAAY,EAAE9K,EAAoC,EAAA;UAApCwI,EAAkC,GAAAxI,EAAA,KAAA,KAAA,CAAA,GAAA,EAAE,KAAA;QAAlC+I,EAAA,GAAAP,EAAA,CAAA0C,oBAA2B;QAA3BA,oBAAoB,GAAAnC,EAAA,KAAA,KAAA,CAAA,GAAG,IAAI,GAAAA,EAAA,CAAA;AACrE,MAAA,IAAI,CAAClE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,IAAI,CAAC+D,KAAI,CAACqD,KAAK,CAACI,YAAY,EAAE,OAAA;AAEtD,MAAA,IAAMyC,OAAO,GAAG3J,KAAK,CAACL,IAAI,EAAE8D,KAAI,CAACqD,KAAK,CAAClF,OAAO,EAAE6B,KAAI,CAACqD,KAAK,CAACjF,OAAO,CAAC,CAAA;AAEnE,MAAA,IAAIiI,oBAAoB,EAAE;QACxB,IAAMqB,SAAS,GAAG1H,KAAI,CAACsH,mBAAmB,CAACrB,KAAK,EAAEjG,KAAI,CAACO,iBAAiB,CAAC,CAAA;AACzE,QAAA,IAAMoH,UAAU,GAAG3H,KAAI,CAACyH,eAAe,CAACC,SAAS,CAAC,CAAA;AAClD,QAAA,IAAMb,iBAAiB,GAAG;UACxB1K,CAAC,EAAEwL,UAAU,CAACxL,CAAC,GAAG+J,OAAO,GAAGwB,SAAS,CAACvL,CAAC;UACvCE,CAAC,EAAEsL,UAAU,CAACtL,CAAC,GAAG6J,OAAO,GAAGwB,SAAS,CAACrL,CAAAA;SACvC,CAAA;AAED,QAAA,IAAMyK,WAAW,GAAG9G,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAC3CA,gBAAgB,CACd8K,iBAAiB,EACjB7G,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnBiK,OAAO,EACPlG,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD2L,iBAAiB,CAAA;AAErB7G,QAAAA,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACsD,WAAW,CAAC,CAAA;AACrC,OAAA;AACD9G,MAAAA,KAAI,CAACqD,KAAK,CAACI,YAAY,CAACyC,OAAO,CAAC,CAAA;KACjC,CAAA;IAEDlG,KAAA,CAAA4H,WAAW,GAAG,YAAA;AACZ,MAAA,IAAI,CAAC5H,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE;AACxB,QAAA,OAAO,IAAI,CAAA;AACZ,OAAA;AAED;AACA,MAAA,IAAM4L,kBAAkB,GAAG7H,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAClDA,gBAAgB,CACdiE,KAAI,CAACqD,KAAK,CAACpG,IAAI,EACf+C,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD8E,KAAI,CAACqD,KAAK,CAACpG,IAAI,CAAA;AACnB,MAAA,OAAOD,kBAAkB,CACvB6K,kBAAkB,EAClB7H,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAAC8H,SAAS,EAAE,EAChB9H,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,EACnB8E,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,CAC5B,CAAA;KACF,CAAA;IAEDiE,KAAA,CAAAmD,YAAY,GAAG,YAAA;AACb,MAAA,IAAM4E,QAAQ,GAAG/H,KAAI,CAAC4H,WAAW,EAAE,CAAA;MACnC,IAAI,CAACG,QAAQ,EAAE,OAAA;AAEP,MAAA,IAAAxK,sBAAsB,GAAwBwK,QAAQ,CAAAxK,sBAAhC;QAAEM,iBAAiB,GAAKkK,QAAQ,CAAAlK,iBAAb,CAAA;AACjD,MAAA,IAAImC,KAAI,CAACqD,KAAK,CAAC2E,cAAc,EAAE;QAC7BhI,KAAI,CAACqD,KAAK,CAAC2E,cAAc,CAACzK,sBAAsB,EAAEM,iBAAiB,CAAC,CAAA;AACrE,OAAA;AAED,MAAA,IAAImC,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,EAAE;QAC/BjI,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,CAAC1K,sBAAsB,EAAEM,iBAAiB,CAAC,CAAA;AACvE,OAAA;KACF,CAAA;IAEDmC,KAAA,CAAAkI,kBAAkB,GAAG,YAAA;AACnB,MAAA,IAAMH,QAAQ,GAAG/H,KAAI,CAAC4H,WAAW,EAAE,CAAA;MACnC,IAAI,CAACG,QAAQ,EAAE,OAAA;AAEP,MAAA,IAAAxK,sBAAsB,GAAwBwK,QAAQ,CAAAxK,sBAAhC;QAAEM,iBAAiB,GAAKkK,QAAQ,CAAAlK,iBAAb,CAAA;AACjD,MAAA,IAAImC,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,EAAE;QAC/BjI,KAAI,CAACqD,KAAK,CAAC4E,gBAAgB,CAAC1K,sBAAsB,EAAEM,iBAAiB,CAAC,CAAA;AACvE,OAAA;KACF,CAAA;IAEDmC,KAAA,CAAA+E,qBAAqB,GAAG,YAAA;AACtB,MAAA,IAAI,CAAC/E,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE,OAAA;AAE1B,MAAA,IAAM6K,WAAW,GAAG9G,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,GAC3CA,gBAAgB,CACdiE,KAAI,CAACqD,KAAK,CAACpG,IAAI,EACf+C,KAAI,CAACpE,SAAS,EACdoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EACnB+D,KAAI,CAACqD,KAAK,CAACnH,IAAI,EACf8D,KAAI,CAACqD,KAAK,CAACnI,QAAQ,CACpB,GACD8E,KAAI,CAACqD,KAAK,CAACpG,IAAI,CAAA;AAEnB+C,MAAAA,KAAI,CAACqD,KAAK,CAACG,YAAY,CAACsD,WAAW,CAAC,CAAA;MACpC9G,KAAI,CAACmD,YAAY,EAAE,CAAA;KACpB,CAAA;AAEDnD,IAAAA,KAAS,CAAAmI,SAAA,GAAG,UAACC,KAA0C,EAAA;;AAC/C,MAAA,IAAAlE,KAAuDlE,KAAI,CAACqD,KAAK;QAA/DpG,IAAI,UAAA;QAAEuG,YAAY,kBAAA;QAAE6E,YAAY,kBAAA;QAAEnM,IAAI,UAAA;QAAEhB,QAAQ,cAAe,CAAA;MACvE,IAAIoN,IAAI,GAAGD,YAAY,CAAA;AAEvB,MAAA,IAAI,CAACrI,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAE,OAAA;AAE1B;MACA,IAAImM,KAAK,CAACG,QAAQ,EAAE;AAClBD,QAAAA,IAAI,IAAI,GAAG,CAAA;AACZ,OAAA;MAED,IAAIE,OAAO,GAAA1K,cAAA,CAAA,EAAA,EAAQb,IAAI,CAAE,CAAA;MAEzB,QAAQmL,KAAK,CAACK,GAAG;AACf,QAAA,KAAK,SAAS;UACZD,OAAO,CAACnM,CAAC,IAAIiM,IAAI,CAAA;UACjBF,KAAK,CAAC9F,cAAc,EAAE,CAAA;AACtB,UAAA,MAAA;AACF,QAAA,KAAK,WAAW;UACdkG,OAAO,CAACnM,CAAC,IAAIiM,IAAI,CAAA;UACjBF,KAAK,CAAC9F,cAAc,EAAE,CAAA;AACtB,UAAA,MAAA;AACF,QAAA,KAAK,WAAW;UACdkG,OAAO,CAACrM,CAAC,IAAImM,IAAI,CAAA;UACjBF,KAAK,CAAC9F,cAAc,EAAE,CAAA;AACtB,UAAA,MAAA;AACF,QAAA,KAAK,YAAY;UACfkG,OAAO,CAACrM,CAAC,IAAImM,IAAI,CAAA;UACjBF,KAAK,CAAC9F,cAAc,EAAE,CAAA;AACtB,UAAA,MAAA;AACF,QAAA;AACE,UAAA,OAAA;AAAM,OAAA;AAGV,MAAA,IAAItC,KAAI,CAACqD,KAAK,CAACtH,gBAAgB,EAAE;AAC/ByM,QAAAA,OAAO,GAAGzM,gBAAgB,CAACyM,OAAO,EAAExI,KAAI,CAACpE,SAAS,EAAEoE,KAAI,CAAC0B,KAAK,CAACzF,QAAQ,EAAEC,IAAI,EAAEhB,QAAQ,CAAC,CAAA;AACzF,OAAA;AAED,MAAA,IAAI,CAACkN,KAAK,CAACM,MAAM,EAAE;QACjB,CAAA/E,EAAA,GAAA,MAAA3D,KAAI,CAACqD,KAAK,EAACmD,kBAAkB,kDAAI,CAAA;AAClC,OAAA;MAEDhD,YAAY,CAACgF,OAAO,CAAC,CAAA;KACtB,CAAA;AAEDxI,IAAAA,KAAO,CAAA2I,OAAA,GAAG,UAACP,KAA0C,EAAA;;MACnD,QAAQA,KAAK,CAACK,GAAG;AACf,QAAA,KAAK,SAAS,CAAA;AACd,QAAA,KAAK,WAAW,CAAA;AAChB,QAAA,KAAK,WAAW,CAAA;AAChB,QAAA,KAAK,YAAY;UACfL,KAAK,CAAC9F,cAAc,EAAE,CAAA;AACtB,UAAA,MAAA;AACF,QAAA;AACE,UAAA,OAAA;AAAM,OAAA;MAEVtC,KAAI,CAACmD,YAAY,EAAE,CAAA;MACnB,CAAAQ,EAAA,GAAA,MAAA3D,KAAI,CAACqD,KAAK,EAAC0D,gBAAgB,kDAAI,CAAA;KAChC,CAAA;;AAuGH,GAAA;AA/vBElH,EAAAA,OAAA,CAAA+I,SAAA,CAAAC,iBAAiB,GAAjB,YAAA;IACE,IAAI,CAAC,IAAI,CAACxH,UAAU,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE,OAAA;IAC7C,IAAI,IAAI,CAACf,YAAY,EAAE;AACrB,MAAA,IAAI,IAAI,CAACA,YAAY,CAACsI,aAAa,EAAE;AACnC,QAAA,IAAI,CAACzH,UAAU,GAAG,IAAI,CAACb,YAAY,CAACsI,aAAa,CAAA;AAClD,OAAA;AACD,MAAA,IAAI,IAAI,CAACzH,UAAU,CAAC0H,WAAW,EAAE;AAC/B,QAAA,IAAI,CAACxH,aAAa,GAAG,IAAI,CAACF,UAAU,CAAC0H,WAAW,CAAA;AACjD,OAAA;MAED,IAAI,CAACjH,kBAAkB,EAAE,CAAA;AACzB;AACA,MAAA,IAAI,OAAON,MAAM,CAACO,cAAc,KAAK,WAAW,EAAE;QAChD,IAAI,CAACR,aAAa,CAAC8D,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACnD,YAAY,CAAC,CAAA;AACjE,OAAA;AACD,MAAA,IAAI,CAACmB,KAAK,CAAC2F,cAAc,IACvB,IAAI,CAACxI,YAAY,CAAC6E,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrC,OAAO,EAAE;AAAE2C,QAAAA,OAAO,EAAE,KAAA;AAAK,OAAE,CAAC,CAAA;MAC/E,IAAI,CAACnF,YAAY,CAAC6E,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACW,cAA+B,CAAC,CAAA;AACzF,KAAA;IAED,IAAI,CAAC3E,UAAU,CAACgE,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACvC,QAAQ,CAAC,CAAA;AAEzD,IAAA,IAAI,CAAC,IAAI,CAACO,KAAK,CAAC4F,+BAA+B,EAAE;MAC/C,IAAI,CAACxI,QAAQ,GAAG,IAAI,CAACY,UAAU,CAAC6H,aAAa,CAAC,OAAO,CAAC,CAAA;MACtD,IAAI,CAACzI,QAAQ,CAAC0I,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;AAC9C,MAAA,IAAI,IAAI,CAAC9F,KAAK,CAAC+F,KAAK,EAAE;AACpB,QAAA,IAAI,CAAC3I,QAAQ,CAAC0I,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC9F,KAAK,CAAC+F,KAAK,CAAC,CAAA;AACtD,OAAA;AACD,MAAA,IAAI,CAAC3I,QAAQ,CAAC4I,SAAS,GAAGC,QAAS,CAAA;MACnC,IAAI,CAACjI,UAAU,CAACkI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC/I,QAAQ,CAAC,CAAA;AAChD,KAAA;AAED;AACA,IAAA,IAAI,IAAI,CAACJ,QAAQ,CAACwD,OAAO,IAAI,IAAI,CAACxD,QAAQ,CAACwD,OAAO,CAAC4F,QAAQ,EAAE;MAC3D,IAAI,CAACvG,WAAW,EAAE,CAAA;AACnB,KAAA;AAED;AACA,IAAA,IAAI,IAAI,CAACG,KAAK,CAACqG,WAAW,EAAE;MAC1B,IAAI,CAACrG,KAAK,CAACqG,WAAW,CAAC,IAAI,CAACrJ,QAAQ,CAAC,CAAA;AACtC,KAAA;AAED,IAAA,IAAI,IAAI,CAACgD,KAAK,CAACsG,WAAW,EAAE;MAC1B,IAAI,CAACtG,KAAK,CAACsG,WAAW,CAAC,IAAI,CAACrJ,QAAQ,CAAC,CAAA;AACtC,KAAA;AAED,IAAA,IAAI,IAAI,CAAC+C,KAAK,CAACuG,aAAa,EAAE;MAC5B,IAAI,CAACvG,KAAK,CAACuG,aAAa,CAAC,IAAI,CAAC1J,UAAU,CAAC,CAAA;AAC1C,KAAA;GACF,CAAA;AAEDL,EAAAA,OAAA,CAAA+I,SAAA,CAAAiB,oBAAoB,GAApB,YAAA;;IACE,IAAI,CAAC,IAAI,CAACxI,UAAU,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE,OAAA;AAC7C,IAAA,IAAI,OAAOC,MAAM,CAACO,cAAc,KAAK,WAAW,EAAE;MAChD,IAAI,CAACR,aAAa,CAACiB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACN,YAAY,CAAC,CAAA;AACpE,KAAA;AACD,IAAA,CAAA/G,EAAA,GAAA,IAAI,CAACsG,cAAc,MAAE,IAAA,IAAAtG,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAA2O,UAAU,EAAE,CAAA;IACjC,IAAI,IAAI,CAACtJ,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACgC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACJ,iBAAiB,CAAC,CAAA;AAC9E,KAAA;IAED,IAAI,IAAI,CAAC3B,QAAQ,EAAE;MACjB,CAAAkD,EAAA,GAAA,IAAI,CAAClD,QAAQ,CAACsJ,UAAU,MAAA,IAAA,IAAApG,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAEqG,WAAW,CAAC,IAAI,CAACvJ,QAAQ,CAAC,CAAA;AACrD,KAAA;IAED,IAAI,CAAC8B,WAAW,EAAE,CAAA;IAClB,IAAI,CAACc,KAAK,CAAC2F,cAAc,IAAI,IAAI,CAACjG,gBAAgB,EAAE,CAAA;GACrD,CAAA;AAEDlD,EAAAA,OAAkB,CAAA+I,SAAA,CAAAqB,kBAAA,GAAlB,UAAmBC,SAAuB,EAAA;;IACxC,IAAIA,SAAS,CAAChP,QAAQ,KAAK,IAAI,CAACmI,KAAK,CAACnI,QAAQ,EAAE;MAC9C,IAAI,CAACgH,YAAY,EAAE,CAAA;MACnB,IAAI,CAAC6C,qBAAqB,EAAE,CAAA;KAC7B,MAAM,IAAImF,SAAS,CAACjP,MAAM,KAAK,IAAI,CAACoI,KAAK,CAACpI,MAAM,EAAE;MACjD,IAAI,CAACiH,YAAY,EAAE,CAAA;KACpB,MAAM,IAAIgI,SAAS,CAACC,SAAS,KAAK,IAAI,CAAC9G,KAAK,CAAC8G,SAAS,EAAE;MACvD,IAAI,CAACjI,YAAY,EAAE,CAAA;KACpB,MAAM,IAAIgI,SAAS,CAAChO,IAAI,KAAK,IAAI,CAACmH,KAAK,CAACnH,IAAI,EAAE;MAC7C,IAAI,CAAC6I,qBAAqB,EAAE,CAAA;KAC7B,MAAM,IACL,CAAA,CAAA5J,EAAA,GAAA+O,SAAS,CAACjO,QAAQ,0CAAEX,MAAM,OAAK,CAAAqI,EAAA,GAAA,IAAI,CAACN,KAAK,CAACpH,QAAQ,MAAA,IAAA,IAAA0H,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAErI,MAAM,CAAA,IAC1D,CAAA,MAAA4O,SAAS,CAACjO,QAAQ,MAAA,IAAA,IAAAiI,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAE7I,KAAK,OAAK,CAAA8I,EAAA,GAAA,IAAI,CAACd,KAAK,CAACpH,QAAQ,MAAE,IAAA,IAAAkI,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAA9I,KAAK,CAAA,EACxD;MACA,IAAI,CAAC6G,YAAY,EAAE,CAAA;KACpB,MAAM,IACL,CAAA,CAAAyC,EAAA,GAAAuF,SAAS,CAACjN,IAAI,0CAAEd,CAAC,OAAK,CAAAyI,EAAA,GAAA,IAAI,CAACvB,KAAK,CAACpG,IAAI,MAAA,IAAA,IAAA2H,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAEzI,CAAC,CAAA,IACxC,CAAA,MAAA+N,SAAS,CAACjN,IAAI,MAAA,IAAA,IAAAmN,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAE/N,CAAC,OAAK,CAAAgO,EAAA,GAAA,IAAI,CAAChH,KAAK,CAACpG,IAAI,MAAE,IAAA,IAAAoN,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAhO,CAAC,CAAA,EACxC;MACA,IAAI,CAAC6L,kBAAkB,EAAE,CAAA;AAC1B,KAAA;AACD,IAAA,IAAIgC,SAAS,CAAClB,cAAc,KAAK,IAAI,CAAC3F,KAAK,CAAC2F,cAAc,IAAI,IAAI,CAACxI,YAAY,EAAE;AAC/E,MAAA,IAAI,CAAC6C,KAAK,CAAC2F,cAAc,GACrB,IAAI,CAACxI,YAAY,CAAC6E,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrC,OAAO,EAAE;AAAE2C,QAAAA,OAAO,EAAE,KAAA;OAAO,CAAC,GAC7E,IAAI,CAAC5C,gBAAgB,EAAE,CAAA;AAC5B,KAAA;IACD,IAAImH,SAAS,CAACI,KAAK,KAAK,IAAI,CAACjH,KAAK,CAACiH,KAAK,EAAE;MACxC,CAAAC,EAAA,GAAA,IAAI,CAACjK,QAAQ,CAACuD,OAAO,MAAE,IAAA,IAAA0G,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAC,IAAI,EAAE,CAAA;AAC9B,KAAA;AAED,IAAA,IAAML,SAAS,GAAG,IAAI,CAACM,YAAY,EAAE,CAAA;AACrC,IAAA,IAAIN,SAAS,KAAK,IAAI,CAACzI,KAAK,CAACE,cAAc,EAAE;MAC3C,IAAI,CAACkD,QAAQ,CAAC;AAAElD,QAAAA,cAAc,EAAEuI,SAAAA;AAAS,OAAE,EAAE,IAAI,CAACjI,YAAY,CAAC,CAAA;AAChE,KAAA;GACF,CAAA;AA+EDrC,EAAAA,OAAA,CAAA+I,SAAA,CAAAd,SAAS,GAAT,YAAA;AACQ,IAAA,IAAA3M,EAAA,GAAuB,IAAI,CAACkI,KAAK;MAA/BpH,QAAQ,GAAAd,EAAA,CAAAc,QAAA;MAAEhB,MAAM,GAAAE,EAAA,CAAAF,MAAe,CAAA;AACvC,IAAA,IAAIgB,QAAQ,EAAE;AACZ,MAAA,OAAOA,QAAQ,CAACZ,KAAK,GAAGY,QAAQ,CAACX,MAAM,CAAA;AACxC,KAAA;AACD,IAAA,OAAOL,MAAM,CAAA;GACd,CAAA;AAED4E,EAAAA,OAAA,CAAA+I,SAAA,CAAA6B,YAAY,GAAZ,YAAA;;AACE,IAAA,IAAI,IAAI,CAACpH,KAAK,CAAC8G,SAAS,KAAK,OAAO,EAAE;AACpC,MAAA,IAAMvG,QAAQ,GAAG,IAAI,CAACvD,QAAQ,CAACwD,OAAO,IAAI,IAAI,CAACvD,QAAQ,CAACuD,OAAO,CAAA;AAE/D,MAAA,IAAID,QAAQ,IAAI,IAAI,CAACpD,YAAY,EAAE;QACjC,IAAI,CAACE,aAAa,GAAG,IAAI,CAACF,YAAY,CAACsD,qBAAqB,EAAE,CAAA;AAC9D,QAAA,IAAME,eAAe,GAAG,IAAI,CAACtD,aAAa,CAACrF,KAAK,GAAG,IAAI,CAACqF,aAAa,CAACpF,MAAM,CAAA;QAC5E,IAAMO,YAAY,GAChB,CAAA,CAAAV,EAAA,GAAA,IAAI,CAACkF,QAAQ,CAACwD,OAAO,MAAE,IAAA,IAAA1I,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAAU,YAAY,MAAI,CAAA8H,EAAA,GAAA,IAAI,CAACrD,QAAQ,CAACuD,OAAO,0CAAEI,UAAU,CAAA,IAAI,CAAC,CAAA;QAC/E,IAAMnI,aAAa,GACjB,CAAA,CAAAoI,EAAA,GAAA,IAAI,CAAC7D,QAAQ,CAACwD,OAAO,MAAE,IAAA,IAAAK,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,EAAA,CAAApI,aAAa,MAAI,CAAAqI,EAAA,GAAA,IAAI,CAAC7D,QAAQ,CAACuD,OAAO,0CAAEO,WAAW,CAAA,IAAI,CAAC,CAAA;AACjF,QAAA,IAAMI,WAAW,GAAG3I,YAAY,GAAGC,aAAa,CAAA;AAEhD,QAAA,OAAO0I,WAAW,GAAGR,eAAe,GAAG,kBAAkB,GAAG,gBAAgB,CAAA;AAC7E,OAAA;AACD,MAAA,OAAO,kBAAkB,CAAA;AAC1B,KAAA;AAED,IAAA,OAAO,IAAI,CAACX,KAAK,CAAC8G,SAAS,CAAA;GAC5B,CAAA;AAsODtK,EAAAA,OAAY,CAAA+I,SAAA,CAAA/C,YAAA,GAAZ,UAAaxD,CAAmC,EAAA;AAC9C,IAAA,IAAM5F,MAAM,GAAGoD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AAClD,IAAA,IAAMlJ,MAAM,GAAGmD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IAClD,IAAI,CAAC5E,iBAAiB,GAAGxE,wBAAwB,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAA;IACjE,IAAI,CAACuE,iBAAiB,GAAGpE,wBAAwB,CAACJ,MAAM,EAAEC,MAAM,CAAC,CAAA;IACjE,IAAI,CAAC4I,WAAW,CAAC7G,SAAS,CAAChC,MAAM,EAAEC,MAAM,CAAC,CAAC,CAAA;GAC5C,CAAA;AAEDmD,EAAAA,OAAW,CAAA+I,SAAA,CAAA7C,WAAA,GAAX,UAAY1D,CAAa,EAAA;IAAzB,IAmBCrC,KAAA,GAAA,IAAA,CAAA;IAlBC,IAAI,CAAC,IAAI,CAACqB,UAAU,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE,OAAA;AAC7C,IAAA,IAAM9E,MAAM,GAAGoD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AAClD,IAAA,IAAMlJ,MAAM,GAAGmD,OAAO,CAACiG,aAAa,CAACzD,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AAClD,IAAA,IAAM8E,MAAM,GAAGjM,SAAS,CAAChC,MAAM,EAAEC,MAAM,CAAC,CAAA;AACxC,IAAA,IAAI,CAAC8I,MAAM,CAACkF,MAAM,CAAC,CAAA;AAEnB,IAAA,IAAI,IAAI,CAACvJ,eAAe,EAAE,IAAI,CAACI,aAAa,CAACkF,oBAAoB,CAAC,IAAI,CAACtF,eAAe,CAAC,CAAA;IACvF,IAAI,CAACA,eAAe,GAAG,IAAI,CAACI,aAAa,CAACmF,qBAAqB,CAAC,YAAA;AAC9D,MAAA,IAAMiE,QAAQ,GAAGnO,wBAAwB,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAA;AACzD,MAAA,IAAMwJ,OAAO,GAAGlG,KAAI,CAACqD,KAAK,CAACnH,IAAI,IAAIyO,QAAQ,GAAG3K,KAAI,CAACgB,iBAAiB,CAAC,CAAA;AACrEhB,MAAAA,KAAI,CAACoG,UAAU,CAACF,OAAO,EAAEwE,MAAM,EAAE;AAAErE,QAAAA,oBAAoB,EAAE,KAAA;AAAK,OAAE,CAAC,CAAA;MACjErG,KAAI,CAACgB,iBAAiB,GAAG2J,QAAQ,CAAA;AAEjC,MAAA,IAAMzP,QAAQ,GAAG2B,wBAAwB,CAACJ,MAAM,EAAEC,MAAM,CAAC,CAAA;AACzD,MAAA,IAAM6J,WAAW,GAAGvG,KAAI,CAACqD,KAAK,CAACnI,QAAQ,IAAIA,QAAQ,GAAG8E,KAAI,CAACiB,iBAAiB,CAAC,CAAA;AAC7EjB,MAAAA,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,IAAItG,KAAI,CAACqD,KAAK,CAACiD,gBAAgB,CAACC,WAAW,CAAC,CAAA;MACvEvG,KAAI,CAACiB,iBAAiB,GAAG/F,QAAQ,CAAA;AACnC,KAAC,CAAC,CAAA;GACH,CAAA;AAwMD2E,EAAAA,OAAA,CAAA+I,SAAA,CAAAgC,MAAM,GAAN,YAAA;IAAA,IAoGC5K,KAAA,GAAA,IAAA,CAAA;;AAnGO,IAAA,IAAA2D,EAaF,GAAA,IAAI,CAACN,KAAK;MAZZwH,KAAK,GAAAlH,EAAA,CAAAkH,KAAA;MACLP,KAAK,GAAA3G,EAAA,CAAA2G,KAAA;MACLQ,UAAU,GAAAnH,EAAA,CAAAmH,UAAA;MACVC,YAAY,GAAApH,EAAA,CAAAoH,YAAA;MACZC,SAAS,GAAArH,EAAA,CAAAqH,SAAA;MACT9G,EAAc,GAAAP,EAAA,CAAA1G,IAAA;MAANd,CAAC,GAAA+H,EAAA,CAAA/H,CAAA;MAAEE,CAAC,GAAA6H,EAAA,CAAA7H,CAAA;MACZnB,QAAQ,GAAAyI,EAAA,CAAAzI,QAAA;MACRgB,IAAI,GAAAyH,EAAA,CAAAzH,IAAA;MACJ+O,SAAS,GAAAtH,EAAA,CAAAsH,SAAA;MACTC,QAAQ,GAAAvH,EAAA,CAAAuH,QAAA;MACR/G,EAAoD,GAAAR,EAAA,CAAAwH,KAAA;MAA3CC,cAAc,GAAAjH,EAAA,CAAAiH,cAAA;MAAEC,aAAa,GAAAlH,EAAA,CAAAkH,aAAA;MAAEC,UAAU,GAAAnH,EAAA,CAAAmH,UAAA;MAClD3G,EAAA,GAAAhB,EAAA,CAAA4H,OAAkE;MAAvDC,kBAAkB,GAAA7G,EAAA,CAAA6G,kBAAA;MAAEC,iBAAiB,GAAA9G,EAAA,CAAA8G,iBAAA;MAAEC,cAAc,oBACpD,CAAA;IAEd,IAAMvB,SAAS,GAAG,CAAAhP,EAAA,GAAA,IAAI,CAACuG,KAAK,CAACE,cAAc,MAAI,IAAA,IAAAzG,EAAA,KAAA,KAAA,CAAA,GAAAA,EAAA,GAAA,IAAI,CAACsP,YAAY,EAAE,CAAA;AAElE,IAAA,OACEtK,gBACE,CAAA+I,aAAA,CAAA,KAAA,EAAA;MAAA9D,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BK,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BkG,GAAG,EAAE,SAACC,GAAAA,CAAAA,EAAE;AAAK,QAAA,OAAC5L,KAAI,CAACQ,YAAY,GAAGoL,EAAE,CAAA;OAAC;AAAA,MAAA,aAAA,EACzB,WAAW;AACvBT,MAAAA,KAAK,EAAEC,cAAc;AACrBS,MAAAA,SAAS,EAAE3M,UAAU,CAAC,yBAAyB,EAAEsM,kBAAkB,CAAA;KAAC,EAEnEX,KAAK,GACJ1K;AACE2L,MAAAA,GAAG,EAAC,EAAE;MACND,SAAS,EAAE3M,UAAU,CACnB,qBAAqB,EACrBiL,SAAS,KAAK,SAAS,IAAI,uBAAuB,EAClDA,SAAS,KAAK,kBAAkB,IAAI,gCAAgC,EACpEA,SAAS,KAAK,gBAAgB,IAAI,8BAA8B,EAChEuB,cAAc,CAAA;OAEXZ,UAAmD,EAAA;AACxDiB,MAAAA,GAAG,EAAElB,KAAK;MACVc,GAAG,EAAE,IAAI,CAACtL,QAAQ;MAClB8K,KAAK,EACArN,cAAA,CAAAA,cAAA,CAAA,EAAA,EAAAwN,UAAU;AACbN,QAAAA,SAAS,EACPA,SAAS,IAAI,YAAA,CAAAgB,MAAA,CAAa7P,CAAC,EAAO,MAAA,CAAA,CAAA6P,MAAA,CAAA3P,CAAC,wBAAcnB,QAAQ,EAAA,aAAA,CAAA,CAAA8Q,MAAA,CAAc9P,IAAI,EAAG,GAAA,CAAA;AAAA,OAAA,CAAA;MAElF+P,MAAM,EAAE,IAAI,CAAC/I,WAAAA;AACb,KAAA,CAAA,CAAA,GAEFoH,KAAK,IACHnK,gBACE,CAAA+I,aAAA,CAAA,OAAA,EAAApL,cAAA,CAAA;AAAAoO,MAAAA,QAAQ,EACR,IAAA;AAAAC,MAAAA,WAAW,EACX,IAAA;AAAAC,MAAAA,IAAI;AACJC,MAAAA,KAAK,EAAE,IAAI;MACXR,SAAS,EAAE3M,UAAU,CACnB,qBAAqB,EACrBiL,SAAS,KAAK,SAAS,IAAI,uBAAuB,EAClDA,SAAS,KAAK,kBAAkB,IAAI,gCAAgC,EACpEA,SAAS,KAAK,gBAAgB,IAAI,8BAA8B,EAChEuB,cAAc,CAAA;OAEZZ,UAAU,EAAA;MACda,GAAG,EAAE,IAAI,CAACrL,QAAQ;MAClBgM,gBAAgB,EAAE,IAAI,CAACpJ,WAAW;MAClCiI,KAAK,EACArN,cAAA,CAAAA,cAAA,CAAA,EAAA,EAAAwN,UAAU,CACb,EAAA;AAAAN,QAAAA,SAAS,EACPA,SAAS,IAAI,oBAAa7O,CAAC,EAAA,MAAA,CAAA,CAAA6P,MAAA,CAAO3P,CAAC,EAAc,aAAA,CAAA,CAAA2P,MAAA,CAAA9Q,QAAQ,EAAc,aAAA,CAAA,CAAA8Q,MAAA,CAAA9P,IAAI,EAAG,GAAA,CAAA;AAAA,OAAA,CAAA;AAElFqQ,MAAAA,QAAQ,EAAE,KAAA;QAET,CAACC,KAAK,CAACC,OAAO,CAACnC,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;AAAEyB,MAAAA,GAAG,EAAEzB,KAAAA;AAAO,KAAA,CAAC,EAAEoC,GAAG,CAAC,UAACC,IAAI;AAAK,MAAA,OAC/DxM,gBAAQ,CAAA+I,aAAA,CAAA,QAAA,EAAApL,cAAA,CAAA;QAAA2K,GAAG,EAAEkE,IAAI,CAACZ,GAAAA;OAAG,EAAMY,IAAI,CAAI,CAAA,CAAA;AAD4B,KAEhE,CAAC,CAGP,EACA,IAAI,CAACjL,KAAK,CAACzF,QAAQ,IAClBkE,gBACE,CAAA+I,aAAA,CAAA,KAAA,EAAApL,cAAA,CAAA;MAAA6N,GAAG,EAAE,IAAI,CAACzL,UAAU;MACpBiL,KAAK,EAAArN,cAAA,CAAAA,cAAA,CAAA,EAAA,EACAuN,aAAa,CAChB,EAAA;AAAAhQ,QAAAA,KAAK,EAAE,IAAI,CAACqG,KAAK,CAACzF,QAAQ,CAACZ,KAAK;AAChCC,QAAAA,MAAM,EAAE,IAAI,CAACoG,KAAK,CAACzF,QAAQ,CAACX,MAAAA;;AAE9BsR,MAAAA,QAAQ,EAAE,CAAC;MACXzE,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBQ,OAAO,EAAE,IAAI,CAACA,OAAO;AACT,MAAA,aAAA,EAAA,SAAS;AACrBkD,MAAAA,SAAS,EAAE3M,UAAU,CACnB,wBAAwB,EACxB+L,SAAS,KAAK,OAAO,IAAI,6BAA6B,EACtDC,QAAQ,IAAI,4BAA4B,EACxCO,iBAAiB,CAAA;AAEf,KAAA,EAAAV,YAAY,EAEnB,CACG,CAAA;GAET,CAAA;EA7yBMlL,OAAA,CAAAgN,YAAY,GAAG;AACpB3Q,IAAAA,IAAI,EAAE,CAAC;AACPhB,IAAAA,QAAQ,EAAE,CAAC;IACXD,MAAM,EAAE,CAAC,GAAG,CAAC;AACbmD,IAAAA,OAAO,EAAEuB,QAAQ;AACjBxB,IAAAA,OAAO,EAAEuB,QAAQ;AACjBuL,IAAAA,SAAS,EAAE,MAAe;AAC1Bd,IAAAA,SAAS,EAAE,SAAkB;AAC7Be,IAAAA,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,EAAE;IACTI,OAAO,EAAE,EAAE;IACXT,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;AAChB5D,IAAAA,SAAS,EAAE,CAAC;AACZpL,IAAAA,gBAAgB,EAAE,IAAI;AACtBiN,IAAAA,cAAc,EAAE,IAAI;AACpBX,IAAAA,YAAY,EAAEzI,aAAAA;GACf,CAAA;AAwVMC,EAAAA,OAAA,CAAA0F,aAAa,GAAG,UAAClD,CAA+C,EAAA;IAAK,OAAC;AAC3ElG,MAAAA,CAAC,EAAE2Q,MAAM,CAACzK,CAAC,CAAC0K,OAAO,CAAC;AACpB1Q,MAAAA,CAAC,EAAEyQ,MAAM,CAACzK,CAAC,CAAC2K,OAAO,CAAA;KACpB,CAAA;GAAC,CAAA;AAEKnN,EAAAA,OAAA,CAAAiG,aAAa,GAAG,UAACmH,KAA0B,EAAA;IAAK,OAAC;AACtD9Q,MAAAA,CAAC,EAAE2Q,MAAM,CAACG,KAAK,CAACF,OAAO,CAAC;AACxB1Q,MAAAA,CAAC,EAAEyQ,MAAM,CAACG,KAAK,CAACD,OAAO,CAAA;KACxB,CAAA;GAAC,CAAA;AA6bJ,EAAA,OAACnN,OAAA,CAAA;AAAA,CA/yBD,CAAsBM,gBAAK,CAAC+M,SAAS;;;;;;"}