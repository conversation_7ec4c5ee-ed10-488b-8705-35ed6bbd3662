{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Vendor-Profile\\\\Products.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Typography, Grid, useMediaQuery } from \"@mui/material\";\nimport VendorProductsCard from \"./Productscard\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VendorsProductsGrid({\n  vendor\n}) {\n  _s();\n  const [products, setProducts] = useState([]);\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n  useEffect(() => {\n    if (!(vendor !== null && vendor !== void 0 && vendor._id)) {\n      setProducts([]);\n      return;\n    }\n    fetch(`https://api.thedesigngrit.com/api/products/getproducts/brand/${vendor._id}`).then(response => {\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch products\");\n      }\n      return response.json();\n    }).then(data => {\n      console.log(\"Fetched Products:\", data);\n      setProducts(Array.isArray(data) ? data : []);\n    }).catch(error => {\n      console.error(\"Error fetching products:\", error);\n      setProducts([]);\n    });\n  }, [vendor._id]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: \"#fff\",\n      borderRadius: \"24px\",\n      maxWidth: \"1700px\",\n      margin: \"0 auto\",\n      mt: {\n        xs: 2,\n        md: 4\n      },\n      mb: {\n        xs: 2,\n        md: 4\n      },\n      px: {\n        xs: 2,\n        md: 6\n      },\n      py: {\n        xs: 2,\n        md: 4\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: \"Productsgrid-header\",\n      sx: {\n        display: \"flex\",\n        flexDirection: isMobile ? \"column\" : \"row\",\n        alignItems: isMobile ? \"flex-start\" : \"center\",\n        justifyContent: \"space-between\",\n        gap: isMobile ? \"10px\" : \"0\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontFamily: \"Horizon\",\n          fontWeight: \"bold\",\n          fontSize: isMobile ? \"18px\" : \"32px\",\n          padding: isMobile ? \"15px 0\" : \"25px\"\n        },\n        children: [vendor.brandName, \"'s Products\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: isMobile ? 2 : 3,\n      className: \"vendorProducts-grid\",\n      sx: {\n        justifyContent: \"center\",\n        margin: \"0 auto\"\n      },\n      children: Array.isArray(products) && products.length > 0 ? /*#__PURE__*/_jsxDEV(VendorProductsCard, {\n        vendor: vendor,\n        products: products\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          textAlign: \"center\",\n          mt: 4,\n          color: \"gray\",\n          width: \"100%\"\n        },\n        children: \"No products available for this vendor yet.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_s(VendorsProductsGrid, \"sZH5NkOfhCIzW4FMpiOPiOQtpy8=\", false, function () {\n  return [useMediaQuery];\n});\n_c = VendorsProductsGrid;\nexport default VendorsProductsGrid;\nvar _c;\n$RefreshReg$(_c, \"VendorsProductsGrid\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Grid", "useMediaQuery", "VendorProductsCard", "jsxDEV", "_jsxDEV", "VendorsProductsGrid", "vendor", "_s", "products", "setProducts", "isMobile", "_id", "fetch", "then", "response", "ok", "Error", "json", "data", "console", "log", "Array", "isArray", "catch", "error", "sx", "background", "borderRadius", "max<PERSON><PERSON><PERSON>", "margin", "mt", "xs", "md", "mb", "px", "py", "children", "className", "display", "flexDirection", "alignItems", "justifyContent", "gap", "fontFamily", "fontWeight", "fontSize", "padding", "brandName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "length", "variant", "textAlign", "color", "width", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Vendor-Profile/Products.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Typography, Grid, useMediaQuery } from \"@mui/material\";\r\nimport VendorProductsCard from \"./Productscard\";\r\nfunction VendorsProductsGrid({ vendor }) {\r\n  const [products, setProducts] = useState([]);\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n\r\n  useEffect(() => {\r\n    if (!vendor?._id) {\r\n      setProducts([]);\r\n      return;\r\n    }\r\n\r\n    fetch(\r\n      `https://api.thedesigngrit.com/api/products/getproducts/brand/${vendor._id}`\r\n    )\r\n      .then((response) => {\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch products\");\r\n        }\r\n        return response.json();\r\n      })\r\n      .then((data) => {\r\n        console.log(\"Fetched Products:\", data);\r\n        setProducts(Array.isArray(data) ? data : []);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching products:\", error);\r\n        setProducts([]);\r\n      });\r\n  }, [vendor._id]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        background: \"#fff\",\r\n        borderRadius: \"24px\",\r\n        maxWidth: \"1700px\",\r\n        margin: \"0 auto\",\r\n        mt: { xs: 2, md: 4 },\r\n        mb: { xs: 2, md: 4 },\r\n        px: { xs: 2, md: 6 },\r\n        py: { xs: 2, md: 4 },\r\n      }}\r\n    >\r\n      <Box\r\n        className=\"Productsgrid-header\"\r\n        sx={{\r\n          display: \"flex\",\r\n          flexDirection: isMobile ? \"column\" : \"row\",\r\n          alignItems: isMobile ? \"flex-start\" : \"center\",\r\n          justifyContent: \"space-between\",\r\n          gap: isMobile ? \"10px\" : \"0\",\r\n        }}\r\n      >\r\n        <Typography\r\n          sx={{\r\n            fontFamily: \"Horizon\",\r\n            fontWeight: \"bold\",\r\n            fontSize: isMobile ? \"18px\" : \"32px\",\r\n            padding: isMobile ? \"15px 0\" : \"25px\",\r\n          }}\r\n        >\r\n          {vendor.brandName}'s Products\r\n        </Typography>\r\n        {/* <Button\r\n          variant=\"contained\"\r\n          sx={{\r\n            padding: \"8px 16px\",\r\n            whiteSpace: \"nowrap\",\r\n            fontSize: isMobile ? \"12px\" : \"14px\",\r\n            \":hover\": {\r\n              backgroundColor: \"#2d2d2d\",\r\n              color: \"#fff\",\r\n            },\r\n          }}\r\n        >\r\n          View all\r\n        </Button> */}\r\n      </Box>\r\n      <Grid\r\n        container\r\n        spacing={isMobile ? 2 : 3}\r\n        className=\"vendorProducts-grid\"\r\n        sx={{\r\n          justifyContent: \"center\",\r\n          margin: \"0 auto\",\r\n        }}\r\n      >\r\n        {Array.isArray(products) && products.length > 0 ? (\r\n          <VendorProductsCard vendor={vendor} products={products} />\r\n        ) : (\r\n          <Typography\r\n            variant=\"body1\"\r\n            sx={{ textAlign: \"center\", mt: 4, color: \"gray\", width: \"100%\" }}\r\n          >\r\n            No products available for this vendor yet.\r\n          </Typography>\r\n        )}\r\n      </Grid>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default VendorsProductsGrid;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,aAAa,QAAQ,eAAe;AACpE,OAAOC,kBAAkB,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAChD,SAASC,mBAAmBA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EACvC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMc,QAAQ,GAAGT,aAAa,CAAC,mBAAmB,CAAC;EAEnDJ,SAAS,CAAC,MAAM;IACd,IAAI,EAACS,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEK,GAAG,GAAE;MAChBF,WAAW,CAAC,EAAE,CAAC;MACf;IACF;IAEAG,KAAK,CACH,gEAAgEN,MAAM,CAACK,GAAG,EAC5E,CAAC,CACEE,IAAI,CAAEC,QAAQ,IAAK;MAClB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MACA,OAAOF,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CACDJ,IAAI,CAAEK,IAAI,IAAK;MACdC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,IAAI,CAAC;MACtCT,WAAW,CAACY,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC;IAC9C,CAAC,CAAC,CACDK,KAAK,CAAEC,KAAK,IAAK;MAChBL,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDf,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,CAAC;EACN,CAAC,EAAE,CAACH,MAAM,CAACK,GAAG,CAAC,CAAC;EAEhB,oBACEP,OAAA,CAACN,GAAG;IACF2B,EAAE,EAAE;MACFC,UAAU,EAAE,MAAM;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,QAAQ;MAChBC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MACpBC,EAAE,EAAE;QAAEF,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MACpBE,EAAE,EAAE;QAAEH,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MACpBG,EAAE,EAAE;QAAEJ,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IACrB,CAAE;IAAAI,QAAA,gBAEFhC,OAAA,CAACN,GAAG;MACFuC,SAAS,EAAC,qBAAqB;MAC/BZ,EAAE,EAAE;QACFa,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE7B,QAAQ,GAAG,QAAQ,GAAG,KAAK;QAC1C8B,UAAU,EAAE9B,QAAQ,GAAG,YAAY,GAAG,QAAQ;QAC9C+B,cAAc,EAAE,eAAe;QAC/BC,GAAG,EAAEhC,QAAQ,GAAG,MAAM,GAAG;MAC3B,CAAE;MAAA0B,QAAA,eAEFhC,OAAA,CAACL,UAAU;QACT0B,EAAE,EAAE;UACFkB,UAAU,EAAE,SAAS;UACrBC,UAAU,EAAE,MAAM;UAClBC,QAAQ,EAAEnC,QAAQ,GAAG,MAAM,GAAG,MAAM;UACpCoC,OAAO,EAAEpC,QAAQ,GAAG,QAAQ,GAAG;QACjC,CAAE;QAAA0B,QAAA,GAED9B,MAAM,CAACyC,SAAS,EAAC,aACpB;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeV,CAAC,eACN/C,OAAA,CAACJ,IAAI;MACHoD,SAAS;MACTC,OAAO,EAAE3C,QAAQ,GAAG,CAAC,GAAG,CAAE;MAC1B2B,SAAS,EAAC,qBAAqB;MAC/BZ,EAAE,EAAE;QACFgB,cAAc,EAAE,QAAQ;QACxBZ,MAAM,EAAE;MACV,CAAE;MAAAO,QAAA,EAEDf,KAAK,CAACC,OAAO,CAACd,QAAQ,CAAC,IAAIA,QAAQ,CAAC8C,MAAM,GAAG,CAAC,gBAC7ClD,OAAA,CAACF,kBAAkB;QAACI,MAAM,EAAEA,MAAO;QAACE,QAAQ,EAAEA;MAAS;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE1D/C,OAAA,CAACL,UAAU;QACTwD,OAAO,EAAC,OAAO;QACf9B,EAAE,EAAE;UAAE+B,SAAS,EAAE,QAAQ;UAAE1B,EAAE,EAAE,CAAC;UAAE2B,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,EAClE;MAED;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC5C,EAAA,CAnGQF,mBAAmB;EAAA,QAETJ,aAAa;AAAA;AAAA0D,EAAA,GAFvBtD,mBAAmB;AAqG5B,eAAeA,mBAAmB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}