{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\adminSideBar.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SidebarAdmin = ({\n  setActivePage\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: \"sidebar-vendor\",\n    children: /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"sidebar-menu-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"dashboard\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"adminNotificationPage\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Notifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"allProducts\"),\n        className: \"sidebar-item-vendor\",\n        children: \"All Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"orderList\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Order List\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"Requests\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Requests\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"categoriesList\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"tags\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Tags\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"concepts\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Concepts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"promotions\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Promotions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"brandsManagement\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Brands\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"PendingBrandUpdates\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Brands Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"PendingProductsUpdates\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Products Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"contactusRequests\"),\n        className: \"sidebar-item-vendor\",\n        children: \"ContactUs Requests\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"AllEmployees\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Brands Employees\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"ourEmployees\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Our Employees\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setActivePage(\"AccountingAdmin\"),\n        className: \"sidebar-item-vendor\",\n        children: \"Accounting\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = SidebarAdmin;\nexport default SidebarAdmin;\nvar _c;\n$RefreshReg$(_c, \"SidebarAdmin\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SidebarAdmin", "setActivePage", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/adminSideBar.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst SidebarAdmin = ({ setActivePage }) => {\r\n  return (\r\n    <aside className=\"sidebar-vendor\">\r\n      <ul className=\"sidebar-menu-vendor\">\r\n        <li\r\n          onClick={() => setActivePage(\"dashboard\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Dashboard\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"adminNotificationPage\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Notifications\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"allProducts\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          All Products\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"orderList\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Order List\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"Requests\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Requests\r\n        </li>\r\n        {/* <li\r\n          onClick={() => setActivePage(\"createCategory\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Categories\r\n        </li> */}\r\n        <li\r\n          onClick={() => setActivePage(\"categoriesList\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Categories\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"tags\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Tags\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"concepts\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Concepts\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"promotions\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Promotions\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"brandsManagement\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Brands\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"PendingBrandUpdates\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Brands Changes\r\n        </li>{\" \"}\r\n        <li\r\n          onClick={() => setActivePage(\"PendingProductsUpdates\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Products Changes\r\n        </li>{\" \"}\r\n        <li\r\n          onClick={() => setActivePage(\"contactusRequests\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          ContactUs Requests\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"AllEmployees\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Brands Employees\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"ourEmployees\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Our Employees\r\n        </li>\r\n        <li\r\n          onClick={() => setActivePage(\"AccountingAdmin\")}\r\n          className=\"sidebar-item-vendor\"\r\n        >\r\n          Accounting\r\n        </li>\r\n      </ul>\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default SidebarAdmin;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAC1C,oBACEF,OAAA;IAAOG,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC/BJ,OAAA;MAAIG,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBACjCJ,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,WAAW,CAAE;QAC1CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,uBAAuB,CAAE;QACtDC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,aAAa,CAAE;QAC5CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,WAAW,CAAE;QAC1CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,UAAU,CAAE;QACzCC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAOLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,gBAAgB,CAAE;QAC/CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,MAAM,CAAE;QACrCC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,UAAU,CAAE;QACzCC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,YAAY,CAAE;QAC3CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,kBAAkB,CAAE;QACjDC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,qBAAqB,CAAE;QACpDC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAAC,GAAG,eACTT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,wBAAwB,CAAE;QACvDC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAAC,GAAG,eACTT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,mBAAmB,CAAE;QAClDC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,cAAc,CAAE;QAC7CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,cAAc,CAAE;QAC7CC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLT,OAAA;QACEK,OAAO,EAAEA,CAAA,KAAMH,aAAa,CAAC,iBAAiB,CAAE;QAChDC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAChC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEZ,CAAC;AAACC,EAAA,GA7GIT,YAAY;AA+GlB,eAAeA,YAAY;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}