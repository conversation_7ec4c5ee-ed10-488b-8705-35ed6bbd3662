{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\adminNotifications.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { Box, Typography, Chip, Paper, FormControl, InputLabel, Select, MenuItem, Tabs, Tab, CircularProgress } from \"@mui/material\";\nimport { IoEllipse } from \"react-icons/io5\";\n// import { useAdmin } from \"../../utils/adminContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminNotificationPage = () => {\n  _s();\n  var _selectedNotification, _selectedNotification2;\n  // const { admin } = useAdmin();\n  const [notifications, setNotifications] = useState([]);\n  const [allNotifications, setAllNotifications] = useState([]);\n  const [adminNotifications, setAdminNotifications] = useState([]);\n  const [selectedNotification, setSelectedNotification] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [selectedBrand, setSelectedBrand] = useState(\"\");\n  const [brands, setBrands] = useState([]);\n  const [activeTab, setActiveTab] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isMarkingAsRead, setIsMarkingAsRead] = useState(false);\n  const notificationsPerPage = 8;\n\n  // Fetch brand notifications\n  const fetchBrandNotifications = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/notifications/admin/all-notifications\");\n      const data = await response.json();\n      setAllNotifications(data);\n      setNotifications(data);\n\n      // Extract unique brands from notifications\n      const uniqueBrands = Array.from(new Set(data.filter(notification => notification.brandId).map(notification => JSON.stringify({\n        _id: notification.brandId._id,\n        brandName: notification.brandId.brandName\n      })))).map(str => JSON.parse(str));\n      setBrands(uniqueBrands);\n    } catch (error) {\n      console.error(\"Error fetching brand notifications:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Fetch admin notifications\n  const fetchAdminNotifications = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/admin-notifications/\");\n      const data = await response.json();\n      setAdminNotifications(data);\n    } catch (error) {\n      console.error(\"Error fetching admin notifications:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchBrandNotifications();\n    fetchAdminNotifications();\n  }, [fetchBrandNotifications, fetchAdminNotifications]);\n  const handleBrandChange = e => {\n    const brandId = e.target.value;\n    setSelectedBrand(brandId);\n    setCurrentPage(1); // Reset to first page when filter changes\n\n    if (brandId) {\n      // Filter notifications by selected brand\n      const filtered = allNotifications.filter(notification => notification.brandId && notification.brandId._id === brandId);\n      setNotifications(filtered);\n    } else {\n      // If no brand selected, show all notifications\n      setNotifications(allNotifications);\n    }\n  };\n\n  // Function to format the date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      weekday: \"short\",\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\"\n    });\n  };\n\n  // Mark a brand notification as read\n  const markBrandNotificationAsRead = async id => {\n    setIsMarkingAsRead(true);\n    try {\n      // Update the read status locally\n      setNotifications(prevNotifications => prevNotifications.map(notification => notification._id === id ? {\n        ...notification,\n        read: true\n      } : notification));\n\n      // Also update in the allNotifications array\n      setAllNotifications(prevNotifications => prevNotifications.map(notification => notification._id === id ? {\n        ...notification,\n        read: true\n      } : notification));\n\n      // Send request to the backend to persist the change\n      await fetch(`https://api.thedesigngrit.com/api/notifications/${id}/mark-as-read`, {\n        method: \"PATCH\"\n      });\n    } catch (error) {\n      console.error(\"Error marking brand notification as read:\", error);\n    } finally {\n      setIsMarkingAsRead(false);\n    }\n  };\n\n  // Mark an admin notification as read\n  const markAdminNotificationAsRead = async id => {\n    setIsMarkingAsRead(true);\n    try {\n      // Update the read status locally\n      setAdminNotifications(prevNotifications => prevNotifications.map(notification => notification._id === id ? {\n        ...notification,\n        read: true\n      } : notification));\n\n      // Send request to the backend to persist the change\n      await fetch(`https://api.thedesigngrit.com/api/admin-notifications/${id}/mark-as-read`, {\n        method: \"PATCH\"\n      });\n    } catch (error) {\n      console.error(\"Error marking admin notification as read:\", error);\n    } finally {\n      setIsMarkingAsRead(false);\n    }\n  };\n\n  // Pagination Logic for brand notifications\n  const indexOfLastNotification = currentPage * notificationsPerPage;\n  const indexOfFirstNotification = indexOfLastNotification - notificationsPerPage;\n  const currentBrandNotifications = notifications.slice(indexOfFirstNotification, indexOfLastNotification);\n  const totalBrandPages = Math.ceil(notifications.length / notificationsPerPage);\n\n  // Pagination Logic for admin notifications\n  const currentAdminNotifications = adminNotifications.slice(indexOfFirstNotification, indexOfLastNotification);\n  const totalAdminPages = Math.ceil(adminNotifications.length / notificationsPerPage);\n\n  // Open the overlay popup with notification details\n  const openNotificationDetails = (notification, isAdminNotification = false) => {\n    setSelectedNotification(notification);\n    if (!notification.read) {\n      if (isAdminNotification) {\n        markAdminNotificationAsRead(notification._id);\n      } else {\n        markBrandNotificationAsRead(notification._id);\n      }\n    }\n  };\n\n  // Close the overlay\n  const closeOverlay = () => {\n    setSelectedNotification(null);\n  };\n\n  // Handle tab change\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1); // Reset to first page when changing tabs\n  };\n\n  // Render notification table\n  const renderNotificationTable = (notifications, isAdminNotification = false) => {\n    if (isLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          minHeight: \"400px\",\n          width: \"100%\"\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60,\n          thickness: 4,\n          sx: {\n            color: \"#6b7b58\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: notifications.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: \"center\",\n          py: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: [\"No notifications found\", !isAdminNotification && selectedBrand && \" for the selected brand\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          overflowX: \"auto\",\n          width: \"100%\",\n          maxWidth: \"100%\" // Match the page/component width\n        },\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          style: {\n            width: \"100%\",\n            // Always fill the container\n            borderCollapse: \"separate\",\n            borderSpacing: \"0 8px\",\n            minWidth: 0 // Remove forced min-width\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              style: {\n                backgroundColor: \"#f5f5f5\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\",\n                  borderRadius: \"8px 0 0 8px\"\n                },\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\"\n                },\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\"\n                },\n                children: \"Id\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), !isAdminNotification && /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\"\n                },\n                children: \"Brand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\"\n                },\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\"\n                },\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: \"12px 16px\",\n                  textAlign: \"left\",\n                  borderRadius: \"0 8px 8px 0\"\n                },\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: notifications.map(notification => {\n              var _notification$brandId;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                onClick: () => openNotificationDetails(notification, isAdminNotification),\n                style: {\n                  cursor: \"pointer\",\n                  backgroundColor: notification.read ? \"#ffffff\" : \"#f9f9f9\",\n                  boxShadow: \"0 2px 4px rgba(0,0,0,0.05)\",\n                  transition: \"all 0.2s ease\"\n                },\n                onMouseOver: e => e.currentTarget.style.boxShadow = \"0 4px 8px rgba(0,0,0,0.1)\",\n                onMouseOut: e => e.currentTarget.style.boxShadow = \"0 2px 4px rgba(0,0,0,0.05)\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\",\n                    borderRadius: \"8px 0 0 8px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(IoEllipse, {\n                    color: notification.read ? \"grey\" : \"#e74c3c\",\n                    style: {\n                      fontSize: \"1rem\",\n                      border: notification.read ? \"1px solid grey\" : \"none\",\n                      borderRadius: \"50%\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: notification.type,\n                    size: \"small\",\n                    color: notification.type === \"order\" ? \"primary\" : notification.type === \"quote\" ? \"secondary\" : \"default\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\"\n                  },\n                  children: notification.orderId || notification._id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), !isAdminNotification && /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\"\n                  },\n                  children: ((_notification$brandId = notification.brandId) === null || _notification$brandId === void 0 ? void 0 : _notification$brandId.brandName) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\",\n                    maxWidth: \"300px\",\n                    overflow: \"hidden\",\n                    textOverflow: \"ellipsis\",\n                    whiteSpace: \"nowrap\"\n                  },\n                  children: notification.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\"\n                  },\n                  children: formatDate(notification.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: \"16px\",\n                    borderRadius: \"0 8px 8px 0\"\n                  },\n                  children: !notification.read ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      backgroundColor: \"#2d2d2d\",\n                      color: \"white\",\n                      border: \"none\",\n                      padding: \"10px 15px\",\n                      borderRadius: \"5px\",\n                      cursor: \"pointer\",\n                      transition: \"background-color 0.3s\",\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"center\",\n                      minWidth: \"120px\"\n                    },\n                    onClick: e => {\n                      e.stopPropagation();\n                      isAdminNotification ? markAdminNotificationAsRead(notification._id) : markBrandNotificationAsRead(notification._id);\n                    },\n                    onMouseOver: e => e.currentTarget.style.backgroundColor = \"#444\",\n                    onMouseOut: e => e.currentTarget.style.backgroundColor = \"#2d2d2d\",\n                    disabled: isMarkingAsRead,\n                    children: isMarkingAsRead && notification._id === (selectedNotification === null || selectedNotification === void 0 ? void 0 : selectedNotification._id) ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                      size: 20,\n                      thickness: 4,\n                      sx: {\n                        color: \"#fff\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 29\n                    }, this) : \"Mark as Read\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      backgroundColor: \"#e0e0e0\",\n                      color: \"#757575\",\n                      border: \"none\",\n                      padding: \"10px 15px\",\n                      borderRadius: \"5px\",\n                      cursor: \"not-allowed\",\n                      minWidth: \"120px\"\n                    },\n                    disabled: true,\n                    children: \"Read\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)]\n              }, notification._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)\n    }, void 0, false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"dashboard-header-vendor\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-header-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Admin Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Admin Dashboard > Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          p: 3,\n          borderRadius: 2,\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: 1,\n            borderColor: \"divider\",\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            value: activeTab,\n            onChange: handleTabChange,\n            \"aria-label\": \"notification tabs\",\n            textColor: \"inherit\",\n            TabIndicatorProps: {\n              style: {\n                backgroundColor: \"#2d2d2d\",\n                height: 3\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Admin Notifications\",\n              sx: {\n                color: \"#2d2d2d\",\n                textTransform: \"none\",\n                fontWeight: 500,\n                borderRadius: 1,\n                \"&:hover\": {\n                  backgroundColor: \"transparent\"\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Brand Notifications\",\n              sx: {\n                color: \"#2d2d2d\",\n                textTransform: \"none\",\n                fontWeight: 500,\n                borderRadius: 1,\n                \"&:hover\": {\n                  backgroundColor: \"transparent\"\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), activeTab === 0 ?\n        /*#__PURE__*/\n        // Admin Notifications Tab\n        _jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"600\",\n              children: \"Admin Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), renderNotificationTable(currentAdminNotifications, true), !isLoading && adminNotifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination\",\n            style: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              gap: \"8px\",\n              margin: \"20px 0\"\n            },\n            children: Array.from({\n              length: totalAdminPages\n            }, (_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: currentPage === index + 1 ? \"active\" : \"\",\n              onClick: () => setCurrentPage(index + 1),\n              style: {\n                padding: \"8px 16px\",\n                border: currentPage === index + 1 ? \"none\" : \"1px solid #ddd\",\n                backgroundColor: currentPage === index + 1 ? \"#2d2d2d\" : \"white\",\n                color: currentPage === index + 1 ? \"white\" : \"#333\",\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n                transition: \"all 0.3s ease\"\n              },\n              onMouseOver: e => {\n                if (currentPage !== index + 1) {\n                  e.currentTarget.style.backgroundColor = \"#f5f5f5\";\n                }\n              },\n              onMouseOut: e => {\n                if (currentPage !== index + 1) {\n                  e.currentTarget.style.backgroundColor = \"white\";\n                }\n              },\n              children: index + 1\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) :\n        /*#__PURE__*/\n        // Brand Notifications Tab\n        _jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"20px\",\n              marginBottom: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"600\",\n              children: \"Brand Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this), !isLoading && /*#__PURE__*/_jsxDEV(FormControl, {\n              sx: {\n                m: 1,\n                border: \"1px solid #ddd\",\n                borderRadius: \"4px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"brand-select-label\",\n                sx: {\n                  position: \"relative\",\n                  top: \"-8px\",\n                  backgroundColor: \"#fff\"\n                },\n                children: \"Brand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"brand-select-label\",\n                value: selectedBrand,\n                onChange: handleBrandChange,\n                sx: {\n                  width: \"200px\",\n                  color: \"#2d2d2d\",\n                  backgroundColor: \"#fff\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"All Brands\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this), brands.map(brand => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: brand._id,\n                  children: brand.brandName\n                }, brand._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), renderNotificationTable(currentBrandNotifications), !isLoading && notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination\",\n            style: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              gap: \"8px\",\n              margin: \"20px 0\"\n            },\n            children: Array.from({\n              length: totalBrandPages\n            }, (_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: currentPage === index + 1 ? \"active\" : \"\",\n              onClick: () => setCurrentPage(index + 1),\n              style: {\n                padding: \"8px 16px\",\n                border: currentPage === index + 1 ? \"none\" : \"1px solid #ddd\",\n                backgroundColor: currentPage === index + 1 ? \"#2d2d2d\" : \"white\",\n                color: currentPage === index + 1 ? \"white\" : \"#333\",\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n                transition: \"all 0.3s ease\"\n              },\n              onMouseOver: e => {\n                if (currentPage !== index + 1) {\n                  e.currentTarget.style.backgroundColor = \"#f5f5f5\";\n                }\n              },\n              onMouseOut: e => {\n                if (currentPage !== index + 1) {\n                  e.currentTarget.style.backgroundColor = \"white\";\n                }\n              },\n              children: index + 1\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), selectedNotification && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notifiy-overlay-popup\",\n        style: {\n          position: \"fixed\",\n          top: 0,\n          left: 0,\n          width: \"100%\",\n          height: \"100%\",\n          backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          zIndex: 1000\n        },\n        onClick: closeOverlay,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notifiy-overlay-content\",\n          style: {\n            backgroundColor: \"white\",\n            padding: \"30px\",\n            borderRadius: \"10px\",\n            width: \"500px\",\n            maxWidth: \"90%\",\n            boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\n            position: \"relative\"\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: \"600\",\n                children: [selectedNotification.type.charAt(0).toUpperCase() + selectedNotification.type.slice(1), \" \", \"Notification\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this), ((_selectedNotification = selectedNotification.brandId) === null || _selectedNotification === void 0 ? void 0 : _selectedNotification.brandName) && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                color: \"text.secondary\",\n                children: selectedNotification.brandId.brandName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 21\n              }, this), ((_selectedNotification2 = selectedNotification.brandId) === null || _selectedNotification2 === void 0 ? void 0 : _selectedNotification2.email) && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: \"#2d2d2d !important\"\n                },\n                children: selectedNotification.brandId.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              variant: \"outlined\",\n              sx: {\n                p: 2,\n                borderRadius: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                paragraph: true,\n                sx: {\n                  color: \"#2d2d2d !important\"\n                },\n                children: selectedNotification.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: \"#2d2d2d !important\"\n                },\n                children: [\"Date: \", formatDate(selectedNotification.date)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 19\n              }, this), selectedNotification.orderId && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: \"#2d2d2d !important\"\n                },\n                children: [\"Order ID: \", selectedNotification.orderId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"flex-end\",\n              gap: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: closeOverlay,\n              style: {\n                backgroundColor: \"#2d2d2d\",\n                color: \"white\",\n                border: \"none\",\n                padding: \"12px 24px\",\n                borderRadius: \"5px\",\n                cursor: \"pointer\",\n                fontWeight: \"500\",\n                transition: \"background-color 0.3s\"\n              },\n              onMouseOver: e => e.currentTarget.style.backgroundColor = \"#444\",\n              onMouseOut: e => e.currentTarget.style.backgroundColor = \"#2d2d2d\",\n              children: \"Close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(AdminNotificationPage, \"eOmqch0B9k9WqR033khfxMas5bw=\");\n_c = AdminNotificationPage;\nexport default AdminNotificationPage;\nvar _c;\n$RefreshReg$(_c, \"AdminNotificationPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Chip", "Paper", "FormControl", "InputLabel", "Select", "MenuItem", "Tabs", "Tab", "CircularProgress", "<PERSON>o<PERSON><PERSON>pse", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminNotificationPage", "_s", "_selectedNotification", "_selectedNotification2", "notifications", "setNotifications", "allNotifications", "setAllNotifications", "adminNotifications", "setAdminNotifications", "selectedNotification", "setSelectedNotification", "currentPage", "setCurrentPage", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rand", "brands", "setBrands", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "isMarkingAsRead", "setIsMarkingAsRead", "notificationsPerPage", "fetchBrandNotifications", "response", "fetch", "data", "json", "uniqueBrands", "Array", "from", "Set", "filter", "notification", "brandId", "map", "JSON", "stringify", "_id", "brandName", "str", "parse", "error", "console", "fetchAdminNotifications", "handleBrandChange", "e", "target", "value", "filtered", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "markBrandNotificationAsRead", "id", "prevNotifications", "read", "method", "markAdminNotificationAsRead", "indexOfLastNotification", "indexOfFirstNotification", "currentBrandNotifications", "slice", "totalBrandPages", "Math", "ceil", "length", "currentAdminNotifications", "totalAdminPages", "openNotificationDetails", "isAdminNotification", "closeOverlay", "handleTabChange", "event", "newValue", "renderNotificationTable", "sx", "display", "justifyContent", "alignItems", "minHeight", "width", "children", "size", "thickness", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "py", "variant", "overflowX", "max<PERSON><PERSON><PERSON>", "style", "borderCollapse", "borderSpacing", "min<PERSON><PERSON><PERSON>", "backgroundColor", "padding", "borderRadius", "_notification$brandId", "onClick", "cursor", "boxShadow", "transition", "onMouseOver", "currentTarget", "onMouseOut", "fontSize", "border", "label", "type", "orderId", "overflow", "textOverflow", "whiteSpace", "description", "stopPropagation", "disabled", "className", "elevation", "p", "mb", "borderBottom", "borderColor", "onChange", "textColor", "TabIndicatorProps", "height", "textTransform", "fontWeight", "gap", "margin", "_", "index", "marginBottom", "m", "position", "top", "labelId", "brand", "left", "zIndex", "char<PERSON>t", "toUpperCase", "email", "gutterBottom", "paragraph", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/adminNotifications.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Chip,\r\n  Paper,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Tabs,\r\n  Tab,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport { IoEllipse } from \"react-icons/io5\";\r\n// import { useAdmin } from \"../../utils/adminContext\";\r\n\r\nconst AdminNotificationPage = () => {\r\n  // const { admin } = useAdmin();\r\n  const [notifications, setNotifications] = useState([]);\r\n  const [allNotifications, setAllNotifications] = useState([]);\r\n  const [adminNotifications, setAdminNotifications] = useState([]);\r\n  const [selectedNotification, setSelectedNotification] = useState(null);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedBrand, setSelectedBrand] = useState(\"\");\r\n  const [brands, setBrands] = useState([]);\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isMarkingAsRead, setIsMarkingAsRead] = useState(false);\r\n  const notificationsPerPage = 8;\r\n\r\n  // Fetch brand notifications\r\n  const fetchBrandNotifications = useCallback(async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/notifications/admin/all-notifications\"\r\n      );\r\n      const data = await response.json();\r\n      setAllNotifications(data);\r\n      setNotifications(data);\r\n\r\n      // Extract unique brands from notifications\r\n      const uniqueBrands = Array.from(\r\n        new Set(\r\n          data\r\n            .filter((notification) => notification.brandId)\r\n            .map((notification) =>\r\n              JSON.stringify({\r\n                _id: notification.brandId._id,\r\n                brandName: notification.brandId.brandName,\r\n              })\r\n            )\r\n        )\r\n      ).map((str) => JSON.parse(str));\r\n\r\n      setBrands(uniqueBrands);\r\n    } catch (error) {\r\n      console.error(\"Error fetching brand notifications:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Fetch admin notifications\r\n  const fetchAdminNotifications = useCallback(async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/admin-notifications/\"\r\n      );\r\n      const data = await response.json();\r\n      setAdminNotifications(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching admin notifications:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchBrandNotifications();\r\n    fetchAdminNotifications();\r\n  }, [fetchBrandNotifications, fetchAdminNotifications]);\r\n\r\n  const handleBrandChange = (e) => {\r\n    const brandId = e.target.value;\r\n    setSelectedBrand(brandId);\r\n    setCurrentPage(1); // Reset to first page when filter changes\r\n\r\n    if (brandId) {\r\n      // Filter notifications by selected brand\r\n      const filtered = allNotifications.filter(\r\n        (notification) =>\r\n          notification.brandId && notification.brandId._id === brandId\r\n      );\r\n      setNotifications(filtered);\r\n    } else {\r\n      // If no brand selected, show all notifications\r\n      setNotifications(allNotifications);\r\n    }\r\n  };\r\n\r\n  // Function to format the date\r\n  const formatDate = (dateString) => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString(\"en-US\", {\r\n      weekday: \"short\",\r\n      year: \"numeric\",\r\n      month: \"short\",\r\n      day: \"numeric\",\r\n    });\r\n  };\r\n\r\n  // Mark a brand notification as read\r\n  const markBrandNotificationAsRead = async (id) => {\r\n    setIsMarkingAsRead(true);\r\n    try {\r\n      // Update the read status locally\r\n      setNotifications((prevNotifications) =>\r\n        prevNotifications.map((notification) =>\r\n          notification._id === id\r\n            ? { ...notification, read: true }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Also update in the allNotifications array\r\n      setAllNotifications((prevNotifications) =>\r\n        prevNotifications.map((notification) =>\r\n          notification._id === id\r\n            ? { ...notification, read: true }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Send request to the backend to persist the change\r\n      await fetch(\r\n        `https://api.thedesigngrit.com/api/notifications/${id}/mark-as-read`,\r\n        {\r\n          method: \"PATCH\",\r\n        }\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error marking brand notification as read:\", error);\r\n    } finally {\r\n      setIsMarkingAsRead(false);\r\n    }\r\n  };\r\n\r\n  // Mark an admin notification as read\r\n  const markAdminNotificationAsRead = async (id) => {\r\n    setIsMarkingAsRead(true);\r\n    try {\r\n      // Update the read status locally\r\n      setAdminNotifications((prevNotifications) =>\r\n        prevNotifications.map((notification) =>\r\n          notification._id === id\r\n            ? { ...notification, read: true }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Send request to the backend to persist the change\r\n      await fetch(\r\n        `https://api.thedesigngrit.com/api/admin-notifications/${id}/mark-as-read`,\r\n        {\r\n          method: \"PATCH\",\r\n        }\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error marking admin notification as read:\", error);\r\n    } finally {\r\n      setIsMarkingAsRead(false);\r\n    }\r\n  };\r\n\r\n  // Pagination Logic for brand notifications\r\n  const indexOfLastNotification = currentPage * notificationsPerPage;\r\n  const indexOfFirstNotification =\r\n    indexOfLastNotification - notificationsPerPage;\r\n  const currentBrandNotifications = notifications.slice(\r\n    indexOfFirstNotification,\r\n    indexOfLastNotification\r\n  );\r\n  const totalBrandPages = Math.ceil(\r\n    notifications.length / notificationsPerPage\r\n  );\r\n\r\n  // Pagination Logic for admin notifications\r\n  const currentAdminNotifications = adminNotifications.slice(\r\n    indexOfFirstNotification,\r\n    indexOfLastNotification\r\n  );\r\n  const totalAdminPages = Math.ceil(\r\n    adminNotifications.length / notificationsPerPage\r\n  );\r\n\r\n  // Open the overlay popup with notification details\r\n  const openNotificationDetails = (\r\n    notification,\r\n    isAdminNotification = false\r\n  ) => {\r\n    setSelectedNotification(notification);\r\n    if (!notification.read) {\r\n      if (isAdminNotification) {\r\n        markAdminNotificationAsRead(notification._id);\r\n      } else {\r\n        markBrandNotificationAsRead(notification._id);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Close the overlay\r\n  const closeOverlay = () => {\r\n    setSelectedNotification(null);\r\n  };\r\n\r\n  // Handle tab change\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    setCurrentPage(1); // Reset to first page when changing tabs\r\n  };\r\n\r\n  // Render notification table\r\n  const renderNotificationTable = (\r\n    notifications,\r\n    isAdminNotification = false\r\n  ) => {\r\n    if (isLoading) {\r\n      return (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            minHeight: \"400px\",\r\n            width: \"100%\",\r\n          }}\r\n        >\r\n          <CircularProgress size={60} thickness={4} sx={{ color: \"#6b7b58\" }} />\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <>\r\n        {notifications.length === 0 ? (\r\n          <Box sx={{ textAlign: \"center\", py: 4 }}>\r\n            <Typography variant=\"h6\" color=\"text.secondary\">\r\n              No notifications found\r\n              {!isAdminNotification &&\r\n                selectedBrand &&\r\n                \" for the selected brand\"}\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box\r\n            sx={{\r\n              overflowX: \"auto\",\r\n              width: \"100%\",\r\n              maxWidth: \"100%\", // Match the page/component width\r\n            }}\r\n          >\r\n            <table\r\n              style={{\r\n                width: \"100%\", // Always fill the container\r\n                borderCollapse: \"separate\",\r\n                borderSpacing: \"0 8px\",\r\n                minWidth: 0, // Remove forced min-width\r\n              }}\r\n            >\r\n              <thead>\r\n                <tr style={{ backgroundColor: \"#f5f5f5\" }}>\r\n                  <th\r\n                    style={{\r\n                      padding: \"12px 16px\",\r\n                      textAlign: \"left\",\r\n                      borderRadius: \"8px 0 0 8px\",\r\n                    }}\r\n                  >\r\n                    Status\r\n                  </th>\r\n                  <th style={{ padding: \"12px 16px\", textAlign: \"left\" }}>\r\n                    Type\r\n                  </th>\r\n                  <th style={{ padding: \"12px 16px\", textAlign: \"left\" }}>\r\n                    Id\r\n                  </th>\r\n                  {!isAdminNotification && (\r\n                    <th style={{ padding: \"12px 16px\", textAlign: \"left\" }}>\r\n                      Brand\r\n                    </th>\r\n                  )}\r\n                  <th style={{ padding: \"12px 16px\", textAlign: \"left\" }}>\r\n                    Description\r\n                  </th>\r\n                  <th style={{ padding: \"12px 16px\", textAlign: \"left\" }}>\r\n                    Date\r\n                  </th>\r\n                  <th\r\n                    style={{\r\n                      padding: \"12px 16px\",\r\n                      textAlign: \"left\",\r\n                      borderRadius: \"0 8px 8px 0\",\r\n                    }}\r\n                  >\r\n                    Action\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {notifications.map((notification) => (\r\n                  <tr\r\n                    key={notification._id}\r\n                    onClick={() =>\r\n                      openNotificationDetails(notification, isAdminNotification)\r\n                    }\r\n                    style={{\r\n                      cursor: \"pointer\",\r\n                      backgroundColor: notification.read\r\n                        ? \"#ffffff\"\r\n                        : \"#f9f9f9\",\r\n                      boxShadow: \"0 2px 4px rgba(0,0,0,0.05)\",\r\n                      transition: \"all 0.2s ease\",\r\n                    }}\r\n                    onMouseOver={(e) =>\r\n                      (e.currentTarget.style.boxShadow =\r\n                        \"0 4px 8px rgba(0,0,0,0.1)\")\r\n                    }\r\n                    onMouseOut={(e) =>\r\n                      (e.currentTarget.style.boxShadow =\r\n                        \"0 2px 4px rgba(0,0,0,0.05)\")\r\n                    }\r\n                  >\r\n                    <td\r\n                      style={{ padding: \"16px\", borderRadius: \"8px 0 0 8px\" }}\r\n                    >\r\n                      <IoEllipse\r\n                        color={notification.read ? \"grey\" : \"#e74c3c\"}\r\n                        style={{\r\n                          fontSize: \"1rem\",\r\n                          border: notification.read ? \"1px solid grey\" : \"none\",\r\n                          borderRadius: \"50%\",\r\n                        }}\r\n                      />\r\n                    </td>\r\n                    <td style={{ padding: \"16px\" }}>\r\n                      <Chip\r\n                        label={notification.type}\r\n                        size=\"small\"\r\n                        color={\r\n                          notification.type === \"order\"\r\n                            ? \"primary\"\r\n                            : notification.type === \"quote\"\r\n                            ? \"secondary\"\r\n                            : \"default\"\r\n                        }\r\n                        variant=\"outlined\"\r\n                      />\r\n                    </td>\r\n                    <td style={{ padding: \"16px\" }}>\r\n                      {notification.orderId || notification._id}\r\n                    </td>\r\n                    {!isAdminNotification && (\r\n                      <td style={{ padding: \"16px\" }}>\r\n                        {notification.brandId?.brandName || \"N/A\"}\r\n                      </td>\r\n                    )}\r\n                    <td\r\n                      style={{\r\n                        padding: \"16px\",\r\n                        maxWidth: \"300px\",\r\n                        overflow: \"hidden\",\r\n                        textOverflow: \"ellipsis\",\r\n                        whiteSpace: \"nowrap\",\r\n                      }}\r\n                    >\r\n                      {notification.description}\r\n                    </td>\r\n                    <td style={{ padding: \"16px\" }}>\r\n                      {formatDate(notification.date)}\r\n                    </td>\r\n                    <td\r\n                      style={{ padding: \"16px\", borderRadius: \"0 8px 8px 0\" }}\r\n                    >\r\n                      {!notification.read ? (\r\n                        <button\r\n                          style={{\r\n                            backgroundColor: \"#2d2d2d\",\r\n                            color: \"white\",\r\n                            border: \"none\",\r\n                            padding: \"10px 15px\",\r\n                            borderRadius: \"5px\",\r\n                            cursor: \"pointer\",\r\n                            transition: \"background-color 0.3s\",\r\n                            display: \"flex\",\r\n                            alignItems: \"center\",\r\n                            justifyContent: \"center\",\r\n                            minWidth: \"120px\",\r\n                          }}\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            isAdminNotification\r\n                              ? markAdminNotificationAsRead(notification._id)\r\n                              : markBrandNotificationAsRead(notification._id);\r\n                          }}\r\n                          onMouseOver={(e) =>\r\n                            (e.currentTarget.style.backgroundColor = \"#444\")\r\n                          }\r\n                          onMouseOut={(e) =>\r\n                            (e.currentTarget.style.backgroundColor = \"#2d2d2d\")\r\n                          }\r\n                          disabled={isMarkingAsRead}\r\n                        >\r\n                          {isMarkingAsRead &&\r\n                          notification._id === selectedNotification?._id ? (\r\n                            <CircularProgress\r\n                              size={20}\r\n                              thickness={4}\r\n                              sx={{ color: \"#fff\" }}\r\n                            />\r\n                          ) : (\r\n                            \"Mark as Read\"\r\n                          )}\r\n                        </button>\r\n                      ) : (\r\n                        <button\r\n                          style={{\r\n                            backgroundColor: \"#e0e0e0\",\r\n                            color: \"#757575\",\r\n                            border: \"none\",\r\n                            padding: \"10px 15px\",\r\n                            borderRadius: \"5px\",\r\n                            cursor: \"not-allowed\",\r\n                            minWidth: \"120px\",\r\n                          }}\r\n                          disabled\r\n                        >\r\n                          Read\r\n                        </button>\r\n                      )}\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </Box>\r\n        )}\r\n      </>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"dashboard-vendor\">\r\n        <header className=\"dashboard-header-vendor\">\r\n          <div className=\"dashboard-header-title\">\r\n            <h2>Admin Notifications</h2>\r\n            <p>Admin Dashboard &gt; Notifications</p>\r\n          </div>\r\n        </header>\r\n\r\n        <Paper elevation={3} sx={{ p: 3, borderRadius: 2, mb: 4 }}>\r\n          <Box sx={{ borderBottom: 1, borderColor: \"divider\", mb: 3 }}>\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              aria-label=\"notification tabs\"\r\n              textColor=\"inherit\"\r\n              TabIndicatorProps={{\r\n                style: { backgroundColor: \"#2d2d2d\", height: 3 },\r\n              }}\r\n            >\r\n              <Tab\r\n                label=\"Admin Notifications\"\r\n                sx={{\r\n                  color: \"#2d2d2d\",\r\n                  textTransform: \"none\",\r\n                  fontWeight: 500,\r\n                  borderRadius: 1,\r\n                  \"&:hover\": {\r\n                    backgroundColor: \"transparent\",\r\n                  },\r\n                }}\r\n              />\r\n              <Tab\r\n                label=\"Brand Notifications\"\r\n                sx={{\r\n                  color: \"#2d2d2d\",\r\n                  textTransform: \"none\",\r\n                  fontWeight: 500,\r\n                  borderRadius: 1,\r\n                  \"&:hover\": {\r\n                    backgroundColor: \"transparent\",\r\n                  },\r\n                }}\r\n              />\r\n            </Tabs>\r\n          </Box>\r\n\r\n          {activeTab === 0 ? (\r\n            // Admin Notifications Tab\r\n            <>\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"space-between\",\r\n                  alignItems: \"center\",\r\n                  mb: 3,\r\n                }}\r\n              >\r\n                <Typography variant=\"h5\" fontWeight=\"600\">\r\n                  Admin Notifications\r\n                </Typography>\r\n              </Box>\r\n              {renderNotificationTable(currentAdminNotifications, true)}\r\n\r\n              {!isLoading && adminNotifications.length > 0 && (\r\n                <div\r\n                  className=\"pagination\"\r\n                  style={{\r\n                    display: \"flex\",\r\n                    justifyContent: \"center\",\r\n                    gap: \"8px\",\r\n                    margin: \"20px 0\",\r\n                  }}\r\n                >\r\n                  {Array.from({ length: totalAdminPages }, (_, index) => (\r\n                    <button\r\n                      key={index}\r\n                      className={currentPage === index + 1 ? \"active\" : \"\"}\r\n                      onClick={() => setCurrentPage(index + 1)}\r\n                      style={{\r\n                        padding: \"8px 16px\",\r\n                        border:\r\n                          currentPage === index + 1 ? \"none\" : \"1px solid #ddd\",\r\n                        backgroundColor:\r\n                          currentPage === index + 1 ? \"#2d2d2d\" : \"white\",\r\n                        color: currentPage === index + 1 ? \"white\" : \"#333\",\r\n                        borderRadius: \"4px\",\r\n                        cursor: \"pointer\",\r\n                        transition: \"all 0.3s ease\",\r\n                      }}\r\n                      onMouseOver={(e) => {\r\n                        if (currentPage !== index + 1) {\r\n                          e.currentTarget.style.backgroundColor = \"#f5f5f5\";\r\n                        }\r\n                      }}\r\n                      onMouseOut={(e) => {\r\n                        if (currentPage !== index + 1) {\r\n                          e.currentTarget.style.backgroundColor = \"white\";\r\n                        }\r\n                      }}\r\n                    >\r\n                      {index + 1}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </>\r\n          ) : (\r\n            // Brand Notifications Tab\r\n            <>\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <Typography variant=\"h5\" fontWeight=\"600\">\r\n                  Brand Notifications\r\n                </Typography>\r\n\r\n                {/* Brand Filter */}\r\n                {!isLoading && (\r\n                  <FormControl\r\n                    sx={{ m: 1, border: \"1px solid #ddd\", borderRadius: \"4px\" }}\r\n                  >\r\n                    <InputLabel\r\n                      id=\"brand-select-label\"\r\n                      sx={{\r\n                        position: \"relative\",\r\n                        top: \"-8px\",\r\n                        backgroundColor: \"#fff\",\r\n                      }}\r\n                    >\r\n                      Brand\r\n                    </InputLabel>\r\n                    <Select\r\n                      labelId=\"brand-select-label\"\r\n                      value={selectedBrand}\r\n                      onChange={handleBrandChange}\r\n                      sx={{\r\n                        width: \"200px\",\r\n                        color: \"#2d2d2d\",\r\n                        backgroundColor: \"#fff\",\r\n                      }}\r\n                    >\r\n                      <MenuItem value=\"\">All Brands</MenuItem>\r\n                      {brands.map((brand) => (\r\n                        <MenuItem key={brand._id} value={brand._id}>\r\n                          {brand.brandName}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  </FormControl>\r\n                )}\r\n              </div>\r\n\r\n              {renderNotificationTable(currentBrandNotifications)}\r\n\r\n              {!isLoading && notifications.length > 0 && (\r\n                <div\r\n                  className=\"pagination\"\r\n                  style={{\r\n                    display: \"flex\",\r\n                    justifyContent: \"center\",\r\n                    gap: \"8px\",\r\n                    margin: \"20px 0\",\r\n                  }}\r\n                >\r\n                  {Array.from({ length: totalBrandPages }, (_, index) => (\r\n                    <button\r\n                      key={index}\r\n                      className={currentPage === index + 1 ? \"active\" : \"\"}\r\n                      onClick={() => setCurrentPage(index + 1)}\r\n                      style={{\r\n                        padding: \"8px 16px\",\r\n                        border:\r\n                          currentPage === index + 1 ? \"none\" : \"1px solid #ddd\",\r\n                        backgroundColor:\r\n                          currentPage === index + 1 ? \"#2d2d2d\" : \"white\",\r\n                        color: currentPage === index + 1 ? \"white\" : \"#333\",\r\n                        borderRadius: \"4px\",\r\n                        cursor: \"pointer\",\r\n                        transition: \"all 0.3s ease\",\r\n                      }}\r\n                      onMouseOver={(e) => {\r\n                        if (currentPage !== index + 1) {\r\n                          e.currentTarget.style.backgroundColor = \"#f5f5f5\";\r\n                        }\r\n                      }}\r\n                      onMouseOut={(e) => {\r\n                        if (currentPage !== index + 1) {\r\n                          e.currentTarget.style.backgroundColor = \"white\";\r\n                        }\r\n                      }}\r\n                    >\r\n                      {index + 1}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n        </Paper>\r\n\r\n        {selectedNotification && (\r\n          <div\r\n            className=\"notifiy-overlay-popup\"\r\n            style={{\r\n              position: \"fixed\",\r\n              top: 0,\r\n              left: 0,\r\n              width: \"100%\",\r\n              height: \"100%\",\r\n              backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              alignItems: \"center\",\r\n              zIndex: 1000,\r\n            }}\r\n            onClick={closeOverlay}\r\n          >\r\n            <div\r\n              className=\"notifiy-overlay-content\"\r\n              style={{\r\n                backgroundColor: \"white\",\r\n                padding: \"30px\",\r\n                borderRadius: \"10px\",\r\n                width: \"500px\",\r\n                maxWidth: \"90%\",\r\n                boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\",\r\n                position: \"relative\",\r\n              }}\r\n              onClick={(e) => e.stopPropagation()}\r\n            >\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: 2,\r\n                  mb: 3,\r\n                }}\r\n              >\r\n                <Box>\r\n                  <Typography variant=\"h5\" fontWeight=\"600\">\r\n                    {selectedNotification.type.charAt(0).toUpperCase() +\r\n                      selectedNotification.type.slice(1)}{\" \"}\r\n                    Notification\r\n                  </Typography>\r\n                  {selectedNotification.brandId?.brandName && (\r\n                    <Typography variant=\"subtitle1\" color=\"text.secondary\">\r\n                      {selectedNotification.brandId.brandName}\r\n                    </Typography>\r\n                  )}\r\n                  {selectedNotification.brandId?.email && (\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{ color: \"#2d2d2d !important\" }}\r\n                    >\r\n                      {selectedNotification.brandId.email}\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n              </Box>\r\n\r\n              <Box sx={{ mb: 3 }}>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Details\r\n                </Typography>\r\n                <Paper variant=\"outlined\" sx={{ p: 2, borderRadius: 2 }}>\r\n                  <Typography\r\n                    variant=\"body1\"\r\n                    paragraph\r\n                    sx={{ color: \"#2d2d2d !important\" }}\r\n                  >\r\n                    {selectedNotification.description}\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{ color: \"#2d2d2d !important\" }}\r\n                  >\r\n                    Date: {formatDate(selectedNotification.date)}\r\n                  </Typography>\r\n                  {selectedNotification.orderId && (\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{ color: \"#2d2d2d !important\" }}\r\n                    >\r\n                      Order ID: {selectedNotification.orderId}\r\n                    </Typography>\r\n                  )}\r\n                </Paper>\r\n              </Box>\r\n\r\n              <Box sx={{ display: \"flex\", justifyContent: \"flex-end\", gap: 2 }}>\r\n                <button\r\n                  onClick={closeOverlay}\r\n                  style={{\r\n                    backgroundColor: \"#2d2d2d\",\r\n                    color: \"white\",\r\n                    border: \"none\",\r\n                    padding: \"12px 24px\",\r\n                    borderRadius: \"5px\",\r\n                    cursor: \"pointer\",\r\n                    fontWeight: \"500\",\r\n                    transition: \"background-color 0.3s\",\r\n                  }}\r\n                  onMouseOver={(e) =>\r\n                    (e.currentTarget.style.backgroundColor = \"#444\")\r\n                  }\r\n                  onMouseOut={(e) =>\r\n                    (e.currentTarget.style.backgroundColor = \"#2d2d2d\")\r\n                  }\r\n                >\r\n                  Close\r\n                </button>\r\n              </Box>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AdminNotificationPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,gBAAgB,QACX,eAAe;AACtB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAClC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC6B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM2C,oBAAoB,GAAG,CAAC;;EAE9B;EACA,MAAMC,uBAAuB,GAAG1C,WAAW,CAAC,YAAY;IACtDsC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAC1B,yEACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCtB,mBAAmB,CAACqB,IAAI,CAAC;MACzBvB,gBAAgB,CAACuB,IAAI,CAAC;;MAEtB;MACA,MAAME,YAAY,GAAGC,KAAK,CAACC,IAAI,CAC7B,IAAIC,GAAG,CACLL,IAAI,CACDM,MAAM,CAAEC,YAAY,IAAKA,YAAY,CAACC,OAAO,CAAC,CAC9CC,GAAG,CAAEF,YAAY,IAChBG,IAAI,CAACC,SAAS,CAAC;QACbC,GAAG,EAAEL,YAAY,CAACC,OAAO,CAACI,GAAG;QAC7BC,SAAS,EAAEN,YAAY,CAACC,OAAO,CAACK;MAClC,CAAC,CACH,CACJ,CACF,CAAC,CAACJ,GAAG,CAAEK,GAAG,IAAKJ,IAAI,CAACK,KAAK,CAACD,GAAG,CAAC,CAAC;MAE/BzB,SAAS,CAACa,YAAY,CAAC;IACzB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRvB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,uBAAuB,GAAG/D,WAAW,CAAC,YAAY;IACtDsC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAC1B,wDACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCpB,qBAAqB,CAACmB,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRvB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,MAAM;IACd2C,uBAAuB,CAAC,CAAC;IACzBqB,uBAAuB,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACrB,uBAAuB,EAAEqB,uBAAuB,CAAC,CAAC;EAEtD,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMZ,OAAO,GAAGY,CAAC,CAACC,MAAM,CAACC,KAAK;IAC9BnC,gBAAgB,CAACqB,OAAO,CAAC;IACzBvB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,IAAIuB,OAAO,EAAE;MACX;MACA,MAAMe,QAAQ,GAAG7C,gBAAgB,CAAC4B,MAAM,CACrCC,YAAY,IACXA,YAAY,CAACC,OAAO,IAAID,YAAY,CAACC,OAAO,CAACI,GAAG,KAAKJ,OACzD,CAAC;MACD/B,gBAAgB,CAAC8C,QAAQ,CAAC;IAC5B,CAAC,MAAM;MACL;MACA9C,gBAAgB,CAACC,gBAAgB,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAM8C,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAG,MAAOC,EAAE,IAAK;IAChDvC,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF;MACAlB,gBAAgB,CAAE0D,iBAAiB,IACjCA,iBAAiB,CAAC1B,GAAG,CAAEF,YAAY,IACjCA,YAAY,CAACK,GAAG,KAAKsB,EAAE,GACnB;QAAE,GAAG3B,YAAY;QAAE6B,IAAI,EAAE;MAAK,CAAC,GAC/B7B,YACN,CACF,CAAC;;MAED;MACA5B,mBAAmB,CAAEwD,iBAAiB,IACpCA,iBAAiB,CAAC1B,GAAG,CAAEF,YAAY,IACjCA,YAAY,CAACK,GAAG,KAAKsB,EAAE,GACnB;QAAE,GAAG3B,YAAY;QAAE6B,IAAI,EAAE;MAAK,CAAC,GAC/B7B,YACN,CACF,CAAC;;MAED;MACA,MAAMR,KAAK,CACT,mDAAmDmC,EAAE,eAAe,EACpE;QACEG,MAAM,EAAE;MACV,CACF,CAAC;IACH,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE,CAAC,SAAS;MACRrB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM2C,2BAA2B,GAAG,MAAOJ,EAAE,IAAK;IAChDvC,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF;MACAd,qBAAqB,CAAEsD,iBAAiB,IACtCA,iBAAiB,CAAC1B,GAAG,CAAEF,YAAY,IACjCA,YAAY,CAACK,GAAG,KAAKsB,EAAE,GACnB;QAAE,GAAG3B,YAAY;QAAE6B,IAAI,EAAE;MAAK,CAAC,GAC/B7B,YACN,CACF,CAAC;;MAED;MACA,MAAMR,KAAK,CACT,yDAAyDmC,EAAE,eAAe,EAC1E;QACEG,MAAM,EAAE;MACV,CACF,CAAC;IACH,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE,CAAC,SAAS;MACRrB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM4C,uBAAuB,GAAGvD,WAAW,GAAGY,oBAAoB;EAClE,MAAM4C,wBAAwB,GAC5BD,uBAAuB,GAAG3C,oBAAoB;EAChD,MAAM6C,yBAAyB,GAAGjE,aAAa,CAACkE,KAAK,CACnDF,wBAAwB,EACxBD,uBACF,CAAC;EACD,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAC/BrE,aAAa,CAACsE,MAAM,GAAGlD,oBACzB,CAAC;;EAED;EACA,MAAMmD,yBAAyB,GAAGnE,kBAAkB,CAAC8D,KAAK,CACxDF,wBAAwB,EACxBD,uBACF,CAAC;EACD,MAAMS,eAAe,GAAGJ,IAAI,CAACC,IAAI,CAC/BjE,kBAAkB,CAACkE,MAAM,GAAGlD,oBAC9B,CAAC;;EAED;EACA,MAAMqD,uBAAuB,GAAGA,CAC9B1C,YAAY,EACZ2C,mBAAmB,GAAG,KAAK,KACxB;IACHnE,uBAAuB,CAACwB,YAAY,CAAC;IACrC,IAAI,CAACA,YAAY,CAAC6B,IAAI,EAAE;MACtB,IAAIc,mBAAmB,EAAE;QACvBZ,2BAA2B,CAAC/B,YAAY,CAACK,GAAG,CAAC;MAC/C,CAAC,MAAM;QACLqB,2BAA2B,CAAC1B,YAAY,CAACK,GAAG,CAAC;MAC/C;IACF;EACF,CAAC;;EAED;EACA,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzBpE,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMqE,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C/D,YAAY,CAAC+D,QAAQ,CAAC;IACtBrE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMsE,uBAAuB,GAAGA,CAC9B/E,aAAa,EACb0E,mBAAmB,GAAG,KAAK,KACxB;IACH,IAAI1D,SAAS,EAAE;MACb,oBACEvB,OAAA,CAACb,GAAG;QACFoG,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,OAAO;UAClBC,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,eAEF7F,OAAA,CAACH,gBAAgB;UAACiG,IAAI,EAAE,EAAG;UAACC,SAAS,EAAE,CAAE;UAACR,EAAE,EAAE;YAAES,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAEV;IAEA,oBACEpG,OAAA,CAAAE,SAAA;MAAA2F,QAAA,EACGtF,aAAa,CAACsE,MAAM,KAAK,CAAC,gBACzB7E,OAAA,CAACb,GAAG;QAACoG,EAAE,EAAE;UAAEc,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,eACtC7F,OAAA,CAACZ,UAAU;UAACmH,OAAO,EAAC,IAAI;UAACP,KAAK,EAAC,gBAAgB;UAAAH,QAAA,GAAC,wBAE9C,EAAC,CAACZ,mBAAmB,IACnBhE,aAAa,IACb,yBAAyB;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENpG,OAAA,CAACb,GAAG;QACFoG,EAAE,EAAE;UACFiB,SAAS,EAAE,MAAM;UACjBZ,KAAK,EAAE,MAAM;UACba,QAAQ,EAAE,MAAM,CAAE;QACpB,CAAE;QAAAZ,QAAA,eAEF7F,OAAA;UACE0G,KAAK,EAAE;YACLd,KAAK,EAAE,MAAM;YAAE;YACfe,cAAc,EAAE,UAAU;YAC1BC,aAAa,EAAE,OAAO;YACtBC,QAAQ,EAAE,CAAC,CAAE;UACf,CAAE;UAAAhB,QAAA,gBAEF7F,OAAA;YAAA6F,QAAA,eACE7F,OAAA;cAAI0G,KAAK,EAAE;gBAAEI,eAAe,EAAE;cAAU,CAAE;cAAAjB,QAAA,gBACxC7F,OAAA;gBACE0G,KAAK,EAAE;kBACLK,OAAO,EAAE,WAAW;kBACpBV,SAAS,EAAE,MAAM;kBACjBW,YAAY,EAAE;gBAChB,CAAE;gBAAAnB,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpG,OAAA;gBAAI0G,KAAK,EAAE;kBAAEK,OAAO,EAAE,WAAW;kBAAEV,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EAAC;cAExD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpG,OAAA;gBAAI0G,KAAK,EAAE;kBAAEK,OAAO,EAAE,WAAW;kBAAEV,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EAAC;cAExD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJ,CAACnB,mBAAmB,iBACnBjF,OAAA;gBAAI0G,KAAK,EAAE;kBAAEK,OAAO,EAAE,WAAW;kBAAEV,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EAAC;cAExD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACL,eACDpG,OAAA;gBAAI0G,KAAK,EAAE;kBAAEK,OAAO,EAAE,WAAW;kBAAEV,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EAAC;cAExD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpG,OAAA;gBAAI0G,KAAK,EAAE;kBAAEK,OAAO,EAAE,WAAW;kBAAEV,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EAAC;cAExD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpG,OAAA;gBACE0G,KAAK,EAAE;kBACLK,OAAO,EAAE,WAAW;kBACpBV,SAAS,EAAE,MAAM;kBACjBW,YAAY,EAAE;gBAChB,CAAE;gBAAAnB,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpG,OAAA;YAAA6F,QAAA,EACGtF,aAAa,CAACiC,GAAG,CAAEF,YAAY;cAAA,IAAA2E,qBAAA;cAAA,oBAC9BjH,OAAA;gBAEEkH,OAAO,EAAEA,CAAA,KACPlC,uBAAuB,CAAC1C,YAAY,EAAE2C,mBAAmB,CAC1D;gBACDyB,KAAK,EAAE;kBACLS,MAAM,EAAE,SAAS;kBACjBL,eAAe,EAAExE,YAAY,CAAC6B,IAAI,GAC9B,SAAS,GACT,SAAS;kBACbiD,SAAS,EAAE,4BAA4B;kBACvCC,UAAU,EAAE;gBACd,CAAE;gBACFC,WAAW,EAAGnE,CAAC,IACZA,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACU,SAAS,GAC9B,2BACH;gBACDI,UAAU,EAAGrE,CAAC,IACXA,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACU,SAAS,GAC9B,4BACH;gBAAAvB,QAAA,gBAED7F,OAAA;kBACE0G,KAAK,EAAE;oBAAEK,OAAO,EAAE,MAAM;oBAAEC,YAAY,EAAE;kBAAc,CAAE;kBAAAnB,QAAA,eAExD7F,OAAA,CAACF,SAAS;oBACRkG,KAAK,EAAE1D,YAAY,CAAC6B,IAAI,GAAG,MAAM,GAAG,SAAU;oBAC9CuC,KAAK,EAAE;sBACLe,QAAQ,EAAE,MAAM;sBAChBC,MAAM,EAAEpF,YAAY,CAAC6B,IAAI,GAAG,gBAAgB,GAAG,MAAM;sBACrD6C,YAAY,EAAE;oBAChB;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLpG,OAAA;kBAAI0G,KAAK,EAAE;oBAAEK,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,eAC7B7F,OAAA,CAACX,IAAI;oBACHsI,KAAK,EAAErF,YAAY,CAACsF,IAAK;oBACzB9B,IAAI,EAAC,OAAO;oBACZE,KAAK,EACH1D,YAAY,CAACsF,IAAI,KAAK,OAAO,GACzB,SAAS,GACTtF,YAAY,CAACsF,IAAI,KAAK,OAAO,GAC7B,WAAW,GACX,SACL;oBACDrB,OAAO,EAAC;kBAAU;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLpG,OAAA;kBAAI0G,KAAK,EAAE;oBAAEK,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EAC5BvD,YAAY,CAACuF,OAAO,IAAIvF,YAAY,CAACK;gBAAG;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,EACJ,CAACnB,mBAAmB,iBACnBjF,OAAA;kBAAI0G,KAAK,EAAE;oBAAEK,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EAC5B,EAAAoB,qBAAA,GAAA3E,YAAY,CAACC,OAAO,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBrE,SAAS,KAAI;gBAAK;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CACL,eACDpG,OAAA;kBACE0G,KAAK,EAAE;oBACLK,OAAO,EAAE,MAAM;oBACfN,QAAQ,EAAE,OAAO;oBACjBqB,QAAQ,EAAE,QAAQ;oBAClBC,YAAY,EAAE,UAAU;oBACxBC,UAAU,EAAE;kBACd,CAAE;kBAAAnC,QAAA,EAEDvD,YAAY,CAAC2F;gBAAW;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACLpG,OAAA;kBAAI0G,KAAK,EAAE;oBAAEK,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EAC5BtC,UAAU,CAACjB,YAAY,CAACmB,IAAI;gBAAC;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACLpG,OAAA;kBACE0G,KAAK,EAAE;oBAAEK,OAAO,EAAE,MAAM;oBAAEC,YAAY,EAAE;kBAAc,CAAE;kBAAAnB,QAAA,EAEvD,CAACvD,YAAY,CAAC6B,IAAI,gBACjBnE,OAAA;oBACE0G,KAAK,EAAE;sBACLI,eAAe,EAAE,SAAS;sBAC1Bd,KAAK,EAAE,OAAO;sBACd0B,MAAM,EAAE,MAAM;sBACdX,OAAO,EAAE,WAAW;sBACpBC,YAAY,EAAE,KAAK;sBACnBG,MAAM,EAAE,SAAS;sBACjBE,UAAU,EAAE,uBAAuB;sBACnC7B,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBD,cAAc,EAAE,QAAQ;sBACxBoB,QAAQ,EAAE;oBACZ,CAAE;oBACFK,OAAO,EAAG/D,CAAC,IAAK;sBACdA,CAAC,CAAC+E,eAAe,CAAC,CAAC;sBACnBjD,mBAAmB,GACfZ,2BAA2B,CAAC/B,YAAY,CAACK,GAAG,CAAC,GAC7CqB,2BAA2B,CAAC1B,YAAY,CAACK,GAAG,CAAC;oBACnD,CAAE;oBACF2E,WAAW,EAAGnE,CAAC,IACZA,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,MAC1C;oBACDU,UAAU,EAAGrE,CAAC,IACXA,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,SAC1C;oBACDqB,QAAQ,EAAE1G,eAAgB;oBAAAoE,QAAA,EAEzBpE,eAAe,IAChBa,YAAY,CAACK,GAAG,MAAK9B,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE8B,GAAG,iBAC5C3C,OAAA,CAACH,gBAAgB;sBACfiG,IAAI,EAAE,EAAG;sBACTC,SAAS,EAAE,CAAE;sBACbR,EAAE,EAAE;wBAAES,KAAK,EAAE;sBAAO;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,GAEF;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,gBAETpG,OAAA;oBACE0G,KAAK,EAAE;sBACLI,eAAe,EAAE,SAAS;sBAC1Bd,KAAK,EAAE,SAAS;sBAChB0B,MAAM,EAAE,MAAM;sBACdX,OAAO,EAAE,WAAW;sBACpBC,YAAY,EAAE,KAAK;sBACnBG,MAAM,EAAE,aAAa;sBACrBN,QAAQ,EAAE;oBACZ,CAAE;oBACFsB,QAAQ;oBAAAtC,QAAA,EACT;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAhIA9D,YAAY,CAACK,GAAG;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiInB,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACN,gBACD,CAAC;EAEP,CAAC;EAED,oBACEpG,OAAA,CAAAE,SAAA;IAAA2F,QAAA,eACE7F,OAAA;MAAKoI,SAAS,EAAC,kBAAkB;MAAAvC,QAAA,gBAC/B7F,OAAA;QAAQoI,SAAS,EAAC,yBAAyB;QAAAvC,QAAA,eACzC7F,OAAA;UAAKoI,SAAS,EAAC,wBAAwB;UAAAvC,QAAA,gBACrC7F,OAAA;YAAA6F,QAAA,EAAI;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BpG,OAAA;YAAA6F,QAAA,EAAG;UAAkC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAETpG,OAAA,CAACV,KAAK;QAAC+I,SAAS,EAAE,CAAE;QAAC9C,EAAE,EAAE;UAAE+C,CAAC,EAAE,CAAC;UAAEtB,YAAY,EAAE,CAAC;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACxD7F,OAAA,CAACb,GAAG;UAACoG,EAAE,EAAE;YAAEiD,YAAY,EAAE,CAAC;YAAEC,WAAW,EAAE,SAAS;YAAEF,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,eAC1D7F,OAAA,CAACL,IAAI;YACH0D,KAAK,EAAEhC,SAAU;YACjBqH,QAAQ,EAAEvD,eAAgB;YAC1B,cAAW,mBAAmB;YAC9BwD,SAAS,EAAC,SAAS;YACnBC,iBAAiB,EAAE;cACjBlC,KAAK,EAAE;gBAAEI,eAAe,EAAE,SAAS;gBAAE+B,MAAM,EAAE;cAAE;YACjD,CAAE;YAAAhD,QAAA,gBAEF7F,OAAA,CAACJ,GAAG;cACF+H,KAAK,EAAC,qBAAqB;cAC3BpC,EAAE,EAAE;gBACFS,KAAK,EAAE,SAAS;gBAChB8C,aAAa,EAAE,MAAM;gBACrBC,UAAU,EAAE,GAAG;gBACf/B,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE;kBACTF,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFpG,OAAA,CAACJ,GAAG;cACF+H,KAAK,EAAC,qBAAqB;cAC3BpC,EAAE,EAAE;gBACFS,KAAK,EAAE,SAAS;gBAChB8C,aAAa,EAAE,MAAM;gBACrBC,UAAU,EAAE,GAAG;gBACf/B,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE;kBACTF,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL/E,SAAS,KAAK,CAAC;QAAA;QACd;QACArB,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA,CAACb,GAAG;YACFoG,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpB6C,EAAE,EAAE;YACN,CAAE;YAAA1C,QAAA,eAEF7F,OAAA,CAACZ,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACwC,UAAU,EAAC,KAAK;cAAAlD,QAAA,EAAC;YAE1C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACLd,uBAAuB,CAACR,yBAAyB,EAAE,IAAI,CAAC,EAExD,CAACvD,SAAS,IAAIZ,kBAAkB,CAACkE,MAAM,GAAG,CAAC,iBAC1C7E,OAAA;YACEoI,SAAS,EAAC,YAAY;YACtB1B,KAAK,EAAE;cACLlB,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,QAAQ;cACxBuD,GAAG,EAAE,KAAK;cACVC,MAAM,EAAE;YACV,CAAE;YAAApD,QAAA,EAED3D,KAAK,CAACC,IAAI,CAAC;cAAE0C,MAAM,EAAEE;YAAgB,CAAC,EAAE,CAACmE,CAAC,EAAEC,KAAK,kBAChDnJ,OAAA;cAEEoI,SAAS,EAAErH,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAG;cACrDjC,OAAO,EAAEA,CAAA,KAAMlG,cAAc,CAACmI,KAAK,GAAG,CAAC,CAAE;cACzCzC,KAAK,EAAE;gBACLK,OAAO,EAAE,UAAU;gBACnBW,MAAM,EACJ3G,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,gBAAgB;gBACvDrC,eAAe,EACb/F,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO;gBACjDnD,KAAK,EAAEjF,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;gBACnDnC,YAAY,EAAE,KAAK;gBACnBG,MAAM,EAAE,SAAS;gBACjBE,UAAU,EAAE;cACd,CAAE;cACFC,WAAW,EAAGnE,CAAC,IAAK;gBAClB,IAAIpC,WAAW,KAAKoI,KAAK,GAAG,CAAC,EAAE;kBAC7BhG,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,SAAS;gBACnD;cACF,CAAE;cACFU,UAAU,EAAGrE,CAAC,IAAK;gBACjB,IAAIpC,WAAW,KAAKoI,KAAK,GAAG,CAAC,EAAE;kBAC7BhG,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,OAAO;gBACjD;cACF,CAAE;cAAAjB,QAAA,EAEDsD,KAAK,GAAG;YAAC,GAzBLA,KAAK;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CAAC;QAAA;QAEH;QACApG,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA;YACE0G,KAAK,EAAE;cACLlB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBsD,GAAG,EAAE,MAAM;cACXI,YAAY,EAAE;YAChB,CAAE;YAAAvD,QAAA,gBAEF7F,OAAA,CAACZ,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACwC,UAAU,EAAC,KAAK;cAAAlD,QAAA,EAAC;YAE1C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAGZ,CAAC7E,SAAS,iBACTvB,OAAA,CAACT,WAAW;cACVgG,EAAE,EAAE;gBAAE8D,CAAC,EAAE,CAAC;gBAAE3B,MAAM,EAAE,gBAAgB;gBAAEV,YAAY,EAAE;cAAM,CAAE;cAAAnB,QAAA,gBAE5D7F,OAAA,CAACR,UAAU;gBACTyE,EAAE,EAAC,oBAAoB;gBACvBsB,EAAE,EAAE;kBACF+D,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,MAAM;kBACXzC,eAAe,EAAE;gBACnB,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpG,OAAA,CAACP,MAAM;gBACL+J,OAAO,EAAC,oBAAoB;gBAC5BnG,KAAK,EAAEpC,aAAc;gBACrByH,QAAQ,EAAExF,iBAAkB;gBAC5BqC,EAAE,EAAE;kBACFK,KAAK,EAAE,OAAO;kBACdI,KAAK,EAAE,SAAS;kBAChBc,eAAe,EAAE;gBACnB,CAAE;gBAAAjB,QAAA,gBAEF7F,OAAA,CAACN,QAAQ;kBAAC2D,KAAK,EAAC,EAAE;kBAAAwC,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EACvCjF,MAAM,CAACqB,GAAG,CAAEiH,KAAK,iBAChBzJ,OAAA,CAACN,QAAQ;kBAAiB2D,KAAK,EAAEoG,KAAK,CAAC9G,GAAI;kBAAAkD,QAAA,EACxC4D,KAAK,CAAC7G;gBAAS,GADH6G,KAAK,CAAC9G,GAAG;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELd,uBAAuB,CAACd,yBAAyB,CAAC,EAElD,CAACjD,SAAS,IAAIhB,aAAa,CAACsE,MAAM,GAAG,CAAC,iBACrC7E,OAAA;YACEoI,SAAS,EAAC,YAAY;YACtB1B,KAAK,EAAE;cACLlB,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,QAAQ;cACxBuD,GAAG,EAAE,KAAK;cACVC,MAAM,EAAE;YACV,CAAE;YAAApD,QAAA,EAED3D,KAAK,CAACC,IAAI,CAAC;cAAE0C,MAAM,EAAEH;YAAgB,CAAC,EAAE,CAACwE,CAAC,EAAEC,KAAK,kBAChDnJ,OAAA;cAEEoI,SAAS,EAAErH,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAG;cACrDjC,OAAO,EAAEA,CAAA,KAAMlG,cAAc,CAACmI,KAAK,GAAG,CAAC,CAAE;cACzCzC,KAAK,EAAE;gBACLK,OAAO,EAAE,UAAU;gBACnBW,MAAM,EACJ3G,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,gBAAgB;gBACvDrC,eAAe,EACb/F,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO;gBACjDnD,KAAK,EAAEjF,WAAW,KAAKoI,KAAK,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;gBACnDnC,YAAY,EAAE,KAAK;gBACnBG,MAAM,EAAE,SAAS;gBACjBE,UAAU,EAAE;cACd,CAAE;cACFC,WAAW,EAAGnE,CAAC,IAAK;gBAClB,IAAIpC,WAAW,KAAKoI,KAAK,GAAG,CAAC,EAAE;kBAC7BhG,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,SAAS;gBACnD;cACF,CAAE;cACFU,UAAU,EAAGrE,CAAC,IAAK;gBACjB,IAAIpC,WAAW,KAAKoI,KAAK,GAAG,CAAC,EAAE;kBAC7BhG,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,OAAO;gBACjD;cACF,CAAE;cAAAjB,QAAA,EAEDsD,KAAK,GAAG;YAAC,GAzBLA,KAAK;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAEPvF,oBAAoB,iBACnBb,OAAA;QACEoI,SAAS,EAAC,uBAAuB;QACjC1B,KAAK,EAAE;UACL4C,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNG,IAAI,EAAE,CAAC;UACP9D,KAAK,EAAE,MAAM;UACbiD,MAAM,EAAE,MAAM;UACd/B,eAAe,EAAE,oBAAoB;UACrCtB,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBiE,MAAM,EAAE;QACV,CAAE;QACFzC,OAAO,EAAEhC,YAAa;QAAAW,QAAA,eAEtB7F,OAAA;UACEoI,SAAS,EAAC,yBAAyB;UACnC1B,KAAK,EAAE;YACLI,eAAe,EAAE,OAAO;YACxBC,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE,MAAM;YACpBpB,KAAK,EAAE,OAAO;YACda,QAAQ,EAAE,KAAK;YACfW,SAAS,EAAE,gCAAgC;YAC3CkC,QAAQ,EAAE;UACZ,CAAE;UACFpC,OAAO,EAAG/D,CAAC,IAAKA,CAAC,CAAC+E,eAAe,CAAC,CAAE;UAAArC,QAAA,gBAEpC7F,OAAA,CAACb,GAAG;YACFoG,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBsD,GAAG,EAAE,CAAC;cACNT,EAAE,EAAE;YACN,CAAE;YAAA1C,QAAA,eAEF7F,OAAA,CAACb,GAAG;cAAA0G,QAAA,gBACF7F,OAAA,CAACZ,UAAU;gBAACmH,OAAO,EAAC,IAAI;gBAACwC,UAAU,EAAC,KAAK;gBAAAlD,QAAA,GACtChF,oBAAoB,CAAC+G,IAAI,CAACgC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAChDhJ,oBAAoB,CAAC+G,IAAI,CAACnD,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAC,cAE5C;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ,EAAA/F,qBAAA,GAAAQ,oBAAoB,CAAC0B,OAAO,cAAAlC,qBAAA,uBAA5BA,qBAAA,CAA8BuC,SAAS,kBACtC5C,OAAA,CAACZ,UAAU;gBAACmH,OAAO,EAAC,WAAW;gBAACP,KAAK,EAAC,gBAAgB;gBAAAH,QAAA,EACnDhF,oBAAoB,CAAC0B,OAAO,CAACK;cAAS;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACb,EACA,EAAA9F,sBAAA,GAAAO,oBAAoB,CAAC0B,OAAO,cAAAjC,sBAAA,uBAA5BA,sBAAA,CAA8BwJ,KAAK,kBAClC9J,OAAA,CAACZ,UAAU;gBACTmH,OAAO,EAAC,OAAO;gBACfhB,EAAE,EAAE;kBAAES,KAAK,EAAE;gBAAqB,CAAE;gBAAAH,QAAA,EAEnChF,oBAAoB,CAAC0B,OAAO,CAACuH;cAAK;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpG,OAAA,CAACb,GAAG;YAACoG,EAAE,EAAE;cAAEgD,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBACjB7F,OAAA,CAACZ,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACwD,YAAY;cAAAlE,QAAA,EAAC;YAEtC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpG,OAAA,CAACV,KAAK;cAACiH,OAAO,EAAC,UAAU;cAAChB,EAAE,EAAE;gBAAE+C,CAAC,EAAE,CAAC;gBAAEtB,YAAY,EAAE;cAAE,CAAE;cAAAnB,QAAA,gBACtD7F,OAAA,CAACZ,UAAU;gBACTmH,OAAO,EAAC,OAAO;gBACfyD,SAAS;gBACTzE,EAAE,EAAE;kBAAES,KAAK,EAAE;gBAAqB,CAAE;gBAAAH,QAAA,EAEnChF,oBAAoB,CAACoH;cAAW;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACbpG,OAAA,CAACZ,UAAU;gBACTmH,OAAO,EAAC,OAAO;gBACfhB,EAAE,EAAE;kBAAES,KAAK,EAAE;gBAAqB,CAAE;gBAAAH,QAAA,GACrC,QACO,EAACtC,UAAU,CAAC1C,oBAAoB,CAAC4C,IAAI,CAAC;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACZvF,oBAAoB,CAACgH,OAAO,iBAC3B7H,OAAA,CAACZ,UAAU;gBACTmH,OAAO,EAAC,OAAO;gBACfhB,EAAE,EAAE;kBAAES,KAAK,EAAE;gBAAqB,CAAE;gBAAAH,QAAA,GACrC,YACW,EAAChF,oBAAoB,CAACgH,OAAO;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENpG,OAAA,CAACb,GAAG;YAACoG,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEuD,GAAG,EAAE;YAAE,CAAE;YAAAnD,QAAA,eAC/D7F,OAAA;cACEkH,OAAO,EAAEhC,YAAa;cACtBwB,KAAK,EAAE;gBACLI,eAAe,EAAE,SAAS;gBAC1Bd,KAAK,EAAE,OAAO;gBACd0B,MAAM,EAAE,MAAM;gBACdX,OAAO,EAAE,WAAW;gBACpBC,YAAY,EAAE,KAAK;gBACnBG,MAAM,EAAE,SAAS;gBACjB4B,UAAU,EAAE,KAAK;gBACjB1B,UAAU,EAAE;cACd,CAAE;cACFC,WAAW,EAAGnE,CAAC,IACZA,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,MAC1C;cACDU,UAAU,EAAGrE,CAAC,IACXA,CAAC,CAACoE,aAAa,CAACb,KAAK,CAACI,eAAe,GAAG,SAC1C;cAAAjB,QAAA,EACF;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC,gBACN,CAAC;AAEP,CAAC;AAAChG,EAAA,CAzvBID,qBAAqB;AAAA8J,EAAA,GAArB9J,qBAAqB;AA2vB3B,eAAeA,qBAAqB;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}