{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\productsPageVendor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { CiCirclePlus } from \"react-icons/ci\";\nimport { MenuItem, Select, FormControl, InputLabel } from \"@mui/material\";\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\nimport { AiOutlineDown, AiOutlineUp } from \"react-icons/ai\"; // Import arrow icons\nimport axios from \"axios\";\nimport PromotionModal from \"./promotionProduct\"; // Import the PromotionModal component\nimport { useVendor } from \"../../utils/vendorContext\"; // Import vendor context\nimport UpdateProduct from \"./UpdateProduct\"; // Import UpdateProduct\nimport { Button } from \"@mui/material\";\nimport VariantDialog from \"./VariantDialog\";\nimport ConfirmationDialog from \"../confirmationMsg\";\n// import ProductAnalyticsGraph from \"./ProductAnalyticsGraph\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductsPageVendor = ({\n  setActivePage\n}) => {\n  _s();\n  const {\n    vendor\n  } = useVendor(); // Access vendor data from context (vendorId, brandId)\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [subCategories, setSubCategories] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState(\"\");\n  const [selectedSubCategory, setSelectedSubCategory] = useState(\"\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const productsPerPage = 12;\n  const [menuOpen, setMenuOpen] = useState(null); // Track the open menu by productId or null\n  const [showUpdate, setShowUpdate] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null); // Selected product for update\n  const [promotionModalOpen, setPromotionModalOpen] = useState(false); // Modal open state\n  const [product, setProduct] = useState(null);\n  const [error, setError] = useState(null);\n  // State for toggling sections\n  const [showFalseStatus, setShowFalseStatus] = useState(false);\n  const [showTrueStatus, setShowTrueStatus] = useState(true);\n  const [openVariantDialog, setOpenVariantDialog] = useState(false);\n  const [variants, setVariants] = useState([]);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [productToDelete, setProductToDelete] = useState(null);\n  const handleVariantSubmit = async variantData => {\n    try {\n      const res = await axios.post(\"http://api.thedesigngrit.com/api/products/variants\", variantData);\n      setVariants(prev => [...prev, res.data]);\n    } catch (err) {\n      console.error(\"Error creating variant:\", err);\n    }\n  };\n  useEffect(() => {\n    if (vendor) {\n      const {\n        brandId\n      } = vendor; // Destructure brandId from the vendor object\n\n      const fetchProducts = async () => {\n        try {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId}`, {\n            params: {\n              category: selectedCategory\n            }\n          });\n          setProduct(response.data);\n          const fetchedProducts = response.data;\n\n          // Map products and fetch type name by type ID\n          const productsWithTypeNames = await Promise.all(fetchedProducts.map(async product => {\n            if (product.type) {\n              try {\n                const typeResponse = await axios.get(`https://api.thedesigngrit.com/api/types/types/${product.type}`);\n                product.typeName = typeResponse.data.name || \"Unknown\"; // Set type name\n              } catch (error) {\n                console.error(\"Error fetching type:\", error);\n                product.typeName = \"Unknown\"; // Fallback in case of error\n              }\n            } else {\n              product.typeName = \"Unknown\"; // Handle products with no type\n            }\n            return product;\n          }));\n          setProducts(productsWithTypeNames); // Set the products with type names\n        } catch (error) {\n          console.error(\"Error fetching products:\", error);\n        }\n      };\n      const fetchCategories = async () => {\n        try {\n          const response = await axios.get(\"https://api.thedesigngrit.com/api/categories/categories\");\n          setCategories(response.data); // Set the fetched categories\n        } catch (error) {\n          console.error(\"Error fetching categories:\", error);\n        }\n      };\n      fetchProducts(); // Fetch products\n      fetchCategories(); // Fetch categories\n    }\n  }, [vendor, selectedCategory, subCategories]); // Fetch when vendor context changes\n\n  const handleCategoryChange = async e => {\n    const selectedCategoryId = e.target.value;\n    setSelectedCategory(selectedCategoryId); // Save the selected category ID\n    setSubCategories([]); // Reset subcategories\n    setSelectedSubCategory(\"\"); // Reset selected subcategory\n\n    // Fetch subcategories if needed\n    if (selectedCategoryId) {\n      try {\n        const response = await axios.get(`https://api.thedesigngrit.com/api/subcategories/byCategory/${selectedCategoryId}`);\n        setSubCategories(response.data);\n      } catch (error) {\n        console.error(\"Error fetching subcategories:\", error);\n      }\n    }\n  };\n  const filteredProducts = products.filter(product => {\n    if (selectedSubCategory) {\n      return product.subCategoryId === selectedSubCategory;\n    }\n    if (selectedCategory) {\n      return product.category === selectedCategory; // Match product.category with selectedCategory ID\n    }\n    return true; // No filter applied\n  });\n  const falseStatusProducts = filteredProducts.filter(product => product.status === false);\n  const trueStatusProducts = filteredProducts.filter(product => product.status === true);\n  const toggleMenu = productId => {\n    setMenuOpen(prevId => prevId === productId ? null : productId);\n  };\n  const closeAllMenus = () => {\n    setMenuOpen(null); // Close menu\n  };\n  const handleEdit = product => {\n    setSelectedProduct(product);\n    setShowUpdate(true);\n  };\n  const handleDelete = async () => {\n    if (!productToDelete) return;\n    let success = false;\n    try {\n      await axios.delete(`https://api.thedesigngrit.com/api/products/${productToDelete._id}`);\n      setProducts(prev => prev.filter(p => p._id !== productToDelete._id));\n      success = true;\n    } catch (error) {\n      console.error(\"Error deleting product:\", error);\n      setError(\"Failed to delete product. Please try again.\");\n      alert(\"Failed to delete product. Please try again.\");\n    }\n    setConfirmDialogOpen(false);\n    setProductToDelete(null);\n  };\n  const handleInsights = product => {\n    setSelectedProduct(product); // Set the selected product\n    setPromotionModalOpen(true); // Open the modal\n  };\n  const handleSavePromotion = promotionDetails => {\n    console.log(\"Promotion Details:\", promotionDetails);\n    // Save promotion details (e.g., send to API or update state)\n    setPromotionModalOpen(false); // Close the modal after saving\n  };\n\n  // const indexOfLastProduct = currentPage * productsPerPage;\n  // const indexOfFirstProduct = indexOfLastProduct - productsPerPage;\n  // const currentProducts = filteredProducts.slice(\n  //   indexOfFirstProduct,\n  //   indexOfLastProduct\n  // );\n\n  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Close all menus when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => closeAllMenus(); // Close all menus on outside clicks\n\n    document.addEventListener(\"click\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"click\", handleClickOutside); // Cleanup\n    };\n  }, []);\n  if (showUpdate) {\n    return /*#__PURE__*/_jsxDEV(UpdateProduct, {\n      existingProduct: selectedProduct // Pass the selected product data\n      ,\n      onBack: () => setShowUpdate(false) // Function to go back to the product list\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-list-page-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"dashboard-header-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-header-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \" Home > All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-date-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setActivePage(\"AddProduct\"),\n            variant: \"contained\",\n            style: {\n              backgroundColor: \"#2d2d2d\",\n              color: \"white\",\n              marginLeft: \"10px\",\n              marginTop: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(CiCirclePlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), \" Add Product\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setOpenVariantDialog(true),\n            variant: \"contained\",\n            style: {\n              backgroundColor: \"#2d2d2d\",\n              color: \"white\",\n              marginLeft: \"10px\",\n              marginTop: \"20px\"\n            },\n            children: \"Add Variant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"end\",\n          marginBottom: \"20px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          sx: {\n            m: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: \"demo-multiple-chip-label\",\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            sx: {\n              width: \"200px\",\n              color: \"#2d2d2d\",\n              backgroundColor: \"#fff\"\n            },\n            value: selectedCategory,\n            onChange: handleCategoryChange,\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"Select Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: category._id,\n              children: category.name\n            }, category._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          onClick: () => setShowFalseStatus(prev => !prev),\n          style: {\n            margin: \"30px 0\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Products without approval\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), showFalseStatus ? /*#__PURE__*/_jsxDEV(AiOutlineUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 32\n          }, this) : /*#__PURE__*/_jsxDEV(AiOutlineDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 50\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), showFalseStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"false-status-section\",\n          children: falseStatusProducts.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No products Not approval.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-grid\",\n            children: falseStatusProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-image-container\",\n                children: [product && product.mainImage && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product === null || product === void 0 ? void 0 : product.mainImage}`,\n                  alt: (product === null || product === void 0 ? void 0 : product.name) || \"Product\",\n                  className: \"promotion-image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 27\n                }, this), product.discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"discount-badge\",\n                  children: [product.discountPercentage, \"% OFF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    flexDirection: \"row\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"promotion-details\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"menu-container\",\n                    children: [/*#__PURE__*/_jsxDEV(BsThreeDotsVertical, {\n                      onClick: e => {\n                        e.stopPropagation(); // Prevent the click from triggering the document listener\n                        toggleMenu(product._id);\n                      },\n                      className: \"three-dots-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 29\n                    }, this), menuOpen === product._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"menu-dropdown\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleEdit(product),\n                        children: \"Edit\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setProductToDelete(product);\n                          setConfirmDialogOpen(true);\n                        },\n                        children: \"Delete\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 339,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleInsights(product),\n                        children: \"Promotion\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    style: {\n                      textDecoration: product.salePrice !== null ? \"line-through\" : \"none\",\n                      color: product.salePrice !== null ? \"#999\" : \"#2d2d2d\"\n                    },\n                    children: [\"E\\xA3\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 27\n                  }, this), product.salePrice !== null && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sale-price\",\n                    children: [\"E\\xA3\", product.salePrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 29\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"product-summary\",\n                  children: [product.description.substring(0, 100), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: \"Summary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metrics-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"metric\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Sales\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"5px\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value\",\n                        children: product.sales ? product.sales : \"No yet sales\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                    style: {\n                      margin: \"10px 0\",\n                      color: \"#ddd\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"metric\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Semaining Products\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-value\",\n                      children: product.stock\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 23\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          onClick: () => setShowTrueStatus(prev => !prev),\n          style: {\n            margin: \"30px 0\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Products with approval\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), showTrueStatus ? /*#__PURE__*/_jsxDEV(AiOutlineUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 31\n          }, this) : /*#__PURE__*/_jsxDEV(AiOutlineDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 49\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), showTrueStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"true-status-section\",\n          children: trueStatusProducts.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No products with approval.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-grid\",\n            children: trueStatusProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"promotion-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-image-container\",\n                children: [product && product.mainImage && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product === null || product === void 0 ? void 0 : product.mainImage}`,\n                  alt: (product === null || product === void 0 ? void 0 : product.name) || \"Product\",\n                  className: \"promotion-image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 27\n                }, this), product.discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"discount-badge\",\n                  children: [product.discountPercentage, \"% OFF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    flexDirection: \"row\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"promotion-details\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"menu-container\",\n                    children: [/*#__PURE__*/_jsxDEV(BsThreeDotsVertical, {\n                      onClick: e => {\n                        e.stopPropagation(); // Prevent the click from triggering the document listener\n                        toggleMenu(product._id);\n                      },\n                      className: \"three-dots-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 29\n                    }, this), menuOpen === product._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"menu-dropdown\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleEdit(product),\n                        children: \"Edit\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setProductToDelete(product);\n                          setConfirmDialogOpen(true);\n                        },\n                        children: \"Delete\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleInsights(product),\n                        children: \"Promotion\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    style: {\n                      textDecoration: product.salePrice !== null ? \"line-through\" : \"none\",\n                      color: product.salePrice !== null ? \"#999\" : \"#2d2d2d\"\n                    },\n                    children: [\"E\\xA3\", product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 27\n                  }, this), product.salePrice !== null && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sale-price\",\n                    children: [\"E\\xA3\", product.salePrice]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 29\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"product-summary\",\n                  children: [product.description.substring(0, 100), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: \"Summary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metrics-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"metric\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Sales\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"5px\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"metric-value\",\n                        children: product.sales ? product.sales : \"No yet sales\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                    style: {\n                      margin: \"10px 0\",\n                      color: \"#ddd\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"metric\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-label\",\n                      children: \"Semaining Products\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"metric-value\",\n                      children: product.stock\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination\",\n        children: Array.from({\n          length: totalPages\n        }, (_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => paginate(index + 1),\n          style: {\n            margin: \"5px\",\n            padding: \"8px 12px\",\n            backgroundColor: currentPage === index + 1 ? \"#2d2d2d\" : \"#efebe8\",\n            color: currentPage === index + 1 ? \"#fff\" : \"#2d2d2d\",\n            borderRadius: \"5px\",\n            cursor: \"pointer\"\n          },\n          children: index + 1\n        }, index + 1, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(VariantDialog, {\n        open: openVariantDialog,\n        onClose: () => setOpenVariantDialog(false),\n        onSubmit: handleVariantSubmit,\n        sku: product === null || product === void 0 ? void 0 : product.sku,\n        brandId: vendor === null || vendor === void 0 ? void 0 : vendor.brandId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this), promotionModalOpen && /*#__PURE__*/_jsxDEV(PromotionModal, {\n        open: promotionModalOpen,\n        onClose: () => setPromotionModalOpen(false),\n        onSave: handleSavePromotion,\n        product: selectedProduct\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationDialog, {\n        open: confirmDialogOpen,\n        title: \"Delete Product\",\n        content: \"Are you sure you want to delete this product?\",\n        onConfirm: handleDelete,\n        onCancel: () => {\n          setConfirmDialogOpen(false);\n          setProductToDelete(null);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ProductsPageVendor, \"ZJ3FIvTpYrsW1ZtrhYpgl/WIk8o=\", false, function () {\n  return [useVendor];\n});\n_c = ProductsPageVendor;\nexport default ProductsPageVendor;\nvar _c;\n$RefreshReg$(_c, \"ProductsPageVendor\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "CiCirclePlus", "MenuItem", "Select", "FormControl", "InputLabel", "BsThreeDotsVertical", "AiOutlineDown", "AiOutlineUp", "axios", "PromotionModal", "useVendor", "UpdateProduct", "<PERSON><PERSON>", "VariantDialog", "ConfirmationDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsPageVendor", "setActivePage", "_s", "vendor", "products", "setProducts", "categories", "setCategories", "subCategories", "setSubCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "currentPage", "setCurrentPage", "productsPerPage", "menuOpen", "setMenuOpen", "showUpdate", "setShowUpdate", "selectedProduct", "setSelectedProduct", "promotionModalOpen", "setPromotionModalOpen", "product", "setProduct", "error", "setError", "showFalseStatus", "setShowFalseStatus", "showTrueStatus", "setShowTrueStatus", "openVariantDialog", "setOpenVariantDialog", "variants", "setVariants", "confirmDialogOpen", "setConfirmDialogOpen", "productToDelete", "setProductToDelete", "handleVariantSubmit", "variantData", "res", "post", "prev", "data", "err", "console", "brandId", "fetchProducts", "response", "get", "params", "category", "fetchedProducts", "productsWithTypeNames", "Promise", "all", "map", "type", "typeResponse", "typeName", "name", "fetchCategories", "handleCategoryChange", "e", "selectedCategoryId", "target", "value", "filteredProducts", "filter", "subCategoryId", "falseStatusProducts", "status", "trueStatusProducts", "toggleMenu", "productId", "prevId", "closeAllMenus", "handleEdit", "handleDelete", "success", "delete", "_id", "p", "alert", "handleInsights", "handleSavePromotion", "promotionDetails", "log", "totalPages", "Math", "ceil", "length", "paginate", "pageNumber", "handleClickOutside", "document", "addEventListener", "removeEventListener", "existingProduct", "onBack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "onClick", "variant", "style", "backgroundColor", "color", "marginLeft", "marginTop", "display", "alignItems", "justifyContent", "marginBottom", "sx", "m", "id", "width", "onChange", "margin", "mainImage", "src", "alt", "discountPercentage", "flexDirection", "stopPropagation", "textDecoration", "salePrice", "price", "description", "substring", "gap", "sales", "stock", "Array", "from", "_", "index", "padding", "borderRadius", "cursor", "open", "onClose", "onSubmit", "sku", "onSave", "title", "content", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/productsPageVendor.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { CiCirclePlus } from \"react-icons/ci\";\r\nimport { MenuItem, Select, FormControl, InputLabel } from \"@mui/material\";\r\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\r\nimport { AiOutlineDown, AiOutlineUp } from \"react-icons/ai\"; // Import arrow icons\r\nimport axios from \"axios\";\r\nimport PromotionModal from \"./promotionProduct\"; // Import the PromotionModal component\r\nimport { useVendor } from \"../../utils/vendorContext\"; // Import vendor context\r\nimport UpdateProduct from \"./UpdateProduct\"; // Import UpdateProduct\r\nimport { Button } from \"@mui/material\";\r\nimport VariantDialog from \"./VariantDialog\";\r\nimport ConfirmationDialog from \"../confirmationMsg\";\r\n// import ProductAnalyticsGraph from \"./ProductAnalyticsGraph\";\r\n\r\nconst ProductsPageVendor = ({ setActivePage }) => {\r\n  const { vendor } = useVendor(); // Access vendor data from context (vendorId, brandId)\r\n  const [products, setProducts] = useState([]);\r\n  const [categories, setCategories] = useState([]);\r\n  const [subCategories, setSubCategories] = useState([]);\r\n  const [selectedCategory, setSelectedCategory] = useState(\"\");\r\n  const [selectedSubCategory, setSelectedSubCategory] = useState(\"\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const productsPerPage = 12;\r\n  const [menuOpen, setMenuOpen] = useState(null); // Track the open menu by productId or null\r\n  const [showUpdate, setShowUpdate] = useState(false);\r\n  const [selectedProduct, setSelectedProduct] = useState(null); // Selected product for update\r\n  const [promotionModalOpen, setPromotionModalOpen] = useState(false); // Modal open state\r\n  const [product, setProduct] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  // State for toggling sections\r\n  const [showFalseStatus, setShowFalseStatus] = useState(false);\r\n  const [showTrueStatus, setShowTrueStatus] = useState(true);\r\n  const [openVariantDialog, setOpenVariantDialog] = useState(false);\r\n  const [variants, setVariants] = useState([]);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [productToDelete, setProductToDelete] = useState(null);\r\n\r\n  const handleVariantSubmit = async (variantData) => {\r\n    try {\r\n      const res = await axios.post(\r\n        \"http://api.thedesigngrit.com/api/products/variants\",\r\n        variantData\r\n      );\r\n      setVariants((prev) => [...prev, res.data]);\r\n    } catch (err) {\r\n      console.error(\"Error creating variant:\", err);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (vendor) {\r\n      const { brandId } = vendor; // Destructure brandId from the vendor object\r\n\r\n      const fetchProducts = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId}`,\r\n            {\r\n              params: {\r\n                category: selectedCategory,\r\n              },\r\n            }\r\n          );\r\n          setProduct(response.data);\r\n          const fetchedProducts = response.data;\r\n\r\n          // Map products and fetch type name by type ID\r\n          const productsWithTypeNames = await Promise.all(\r\n            fetchedProducts.map(async (product) => {\r\n              if (product.type) {\r\n                try {\r\n                  const typeResponse = await axios.get(\r\n                    `https://api.thedesigngrit.com/api/types/types/${product.type}`\r\n                  );\r\n                  product.typeName = typeResponse.data.name || \"Unknown\"; // Set type name\r\n                } catch (error) {\r\n                  console.error(\"Error fetching type:\", error);\r\n                  product.typeName = \"Unknown\"; // Fallback in case of error\r\n                }\r\n              } else {\r\n                product.typeName = \"Unknown\"; // Handle products with no type\r\n              }\r\n              return product;\r\n            })\r\n          );\r\n          setProducts(productsWithTypeNames); // Set the products with type names\r\n        } catch (error) {\r\n          console.error(\"Error fetching products:\", error);\r\n        }\r\n      };\r\n\r\n      const fetchCategories = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            \"https://api.thedesigngrit.com/api/categories/categories\"\r\n          );\r\n          setCategories(response.data); // Set the fetched categories\r\n        } catch (error) {\r\n          console.error(\"Error fetching categories:\", error);\r\n        }\r\n      };\r\n\r\n      fetchProducts(); // Fetch products\r\n      fetchCategories(); // Fetch categories\r\n    }\r\n  }, [vendor, selectedCategory, subCategories]); // Fetch when vendor context changes\r\n\r\n  const handleCategoryChange = async (e) => {\r\n    const selectedCategoryId = e.target.value;\r\n    setSelectedCategory(selectedCategoryId); // Save the selected category ID\r\n    setSubCategories([]); // Reset subcategories\r\n    setSelectedSubCategory(\"\"); // Reset selected subcategory\r\n\r\n    // Fetch subcategories if needed\r\n    if (selectedCategoryId) {\r\n      try {\r\n        const response = await axios.get(\r\n          `https://api.thedesigngrit.com/api/subcategories/byCategory/${selectedCategoryId}`\r\n        );\r\n        setSubCategories(response.data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching subcategories:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const filteredProducts = products.filter((product) => {\r\n    if (selectedSubCategory) {\r\n      return product.subCategoryId === selectedSubCategory;\r\n    }\r\n    if (selectedCategory) {\r\n      return product.category === selectedCategory; // Match product.category with selectedCategory ID\r\n    }\r\n    return true; // No filter applied\r\n  });\r\n\r\n  const falseStatusProducts = filteredProducts.filter(\r\n    (product) => product.status === false\r\n  );\r\n  const trueStatusProducts = filteredProducts.filter(\r\n    (product) => product.status === true\r\n  );\r\n\r\n  const toggleMenu = (productId) => {\r\n    setMenuOpen((prevId) => (prevId === productId ? null : productId));\r\n  };\r\n\r\n  const closeAllMenus = () => {\r\n    setMenuOpen(null); // Close menu\r\n  };\r\n\r\n  const handleEdit = (product) => {\r\n    setSelectedProduct(product);\r\n    setShowUpdate(true);\r\n  };\r\n\r\n  const handleDelete = async () => {\r\n    if (!productToDelete) return;\r\n    let success = false;\r\n    try {\r\n      await axios.delete(\r\n        `https://api.thedesigngrit.com/api/products/${productToDelete._id}`\r\n      );\r\n      setProducts((prev) => prev.filter((p) => p._id !== productToDelete._id));\r\n      success = true;\r\n    } catch (error) {\r\n      console.error(\"Error deleting product:\", error);\r\n      setError(\"Failed to delete product. Please try again.\");\r\n      alert(\"Failed to delete product. Please try again.\");\r\n    }\r\n    setConfirmDialogOpen(false);\r\n    setProductToDelete(null);\r\n  };\r\n\r\n  const handleInsights = (product) => {\r\n    setSelectedProduct(product); // Set the selected product\r\n    setPromotionModalOpen(true); // Open the modal\r\n  };\r\n\r\n  const handleSavePromotion = (promotionDetails) => {\r\n    console.log(\"Promotion Details:\", promotionDetails);\r\n    // Save promotion details (e.g., send to API or update state)\r\n    setPromotionModalOpen(false); // Close the modal after saving\r\n  };\r\n\r\n  // const indexOfLastProduct = currentPage * productsPerPage;\r\n  // const indexOfFirstProduct = indexOfLastProduct - productsPerPage;\r\n  // const currentProducts = filteredProducts.slice(\r\n  //   indexOfFirstProduct,\r\n  //   indexOfLastProduct\r\n  // );\r\n\r\n  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);\r\n  const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n  // Close all menus when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = () => closeAllMenus(); // Close all menus on outside clicks\r\n\r\n    document.addEventListener(\"click\", handleClickOutside);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"click\", handleClickOutside); // Cleanup\r\n    };\r\n  }, []);\r\n\r\n  if (showUpdate) {\r\n    return (\r\n      <UpdateProduct\r\n        existingProduct={selectedProduct} // Pass the selected product data\r\n        onBack={() => setShowUpdate(false)} // Function to go back to the product list\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"product-list-page-vendor\">\r\n        <header className=\"dashboard-header-vendor\">\r\n          <div className=\"dashboard-header-title\">\r\n            <h2>All Products</h2>\r\n            <p> Home &gt; All Products</p>\r\n          </div>\r\n          <div className=\"dashboard-date-vendor\">\r\n            <Button\r\n              onClick={() => setActivePage(\"AddProduct\")}\r\n              variant=\"contained\"\r\n              style={{\r\n                backgroundColor: \"#2d2d2d\",\r\n                color: \"white\",\r\n                marginLeft: \"10px\",\r\n                marginTop: \"20px\",\r\n              }}\r\n            >\r\n              <CiCirclePlus /> Add Product\r\n            </Button>\r\n            <Button\r\n              onClick={() => setOpenVariantDialog(true)}\r\n              variant=\"contained\"\r\n              style={{\r\n                backgroundColor: \"#2d2d2d\",\r\n                color: \"white\",\r\n                marginLeft: \"10px\",\r\n                marginTop: \"20px\",\r\n              }}\r\n            >\r\n              Add Variant\r\n            </Button>\r\n          </div>\r\n        </header>\r\n        {/* <ProductAnalyticsGraph products={products} /> */}\r\n\r\n        {/* Filters */}\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"end\",\r\n            marginBottom: \"20px\",\r\n          }}\r\n        >\r\n          <FormControl sx={{ m: 1 }}>\r\n            <InputLabel id=\"demo-multiple-chip-label\">Category</InputLabel>\r\n            <Select\r\n              sx={{\r\n                width: \"200px\",\r\n                color: \"#2d2d2d\",\r\n                backgroundColor: \"#fff\",\r\n              }}\r\n              value={selectedCategory}\r\n              onChange={handleCategoryChange}\r\n            >\r\n              <MenuItem value=\"\">Select Category</MenuItem>\r\n              {categories.map((category) => (\r\n                <MenuItem key={category._id} value={category._id}>\r\n                  {category.name}\r\n                </MenuItem>\r\n              ))}\r\n            </Select>\r\n          </FormControl>\r\n        </div>\r\n\r\n        {/* Section for products with status false */}\r\n        <div>\r\n          <div\r\n            className=\"section-header\"\r\n            onClick={() => setShowFalseStatus((prev) => !prev)}\r\n            style={{\r\n              margin: \"30px 0\",\r\n            }}\r\n          >\r\n            <span>Products without approval</span>\r\n            {showFalseStatus ? <AiOutlineUp /> : <AiOutlineDown />}\r\n          </div>\r\n          {showFalseStatus && (\r\n            <div className=\"false-status-section\">\r\n              {falseStatusProducts.length === 0 ? (\r\n                <p>No products Not approval.</p>\r\n              ) : (\r\n                <div className=\"product-grid\">\r\n                  {falseStatusProducts.map((product) => (\r\n                    <div className=\"promotion-card\" key={product.id}>\r\n                      <div className=\"promotion-image-container\">\r\n                        {product && product.mainImage && (\r\n                          <img\r\n                            src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product?.mainImage}`}\r\n                            alt={product?.name || \"Product\"}\r\n                            className=\"promotion-image\"\r\n                          />\r\n                        )}\r\n                        {product.discountPercentage && (\r\n                          <div className=\"discount-badge\">\r\n                            {product.discountPercentage}% OFF\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"promotion-details\">\r\n                        <div\r\n                          style={{\r\n                            display: \"flex\",\r\n                            justifyContent: \"space-between\",\r\n                            flexDirection: \"row\",\r\n                          }}\r\n                        >\r\n                          <h3 className=\"promotion-details\">{product.name}</h3>\r\n                          <div className=\"menu-container\">\r\n                            <BsThreeDotsVertical\r\n                              onClick={(e) => {\r\n                                e.stopPropagation(); // Prevent the click from triggering the document listener\r\n                                toggleMenu(product._id);\r\n                              }}\r\n                              className=\"three-dots-icon\"\r\n                            />\r\n                            {menuOpen === product._id && (\r\n                              <div className=\"menu-dropdown\">\r\n                                <button onClick={() => handleEdit(product)}>\r\n                                  Edit\r\n                                </button>\r\n                                <button\r\n                                  onClick={() => {\r\n                                    setProductToDelete(product);\r\n                                    setConfirmDialogOpen(true);\r\n                                  }}\r\n                                >\r\n                                  Delete\r\n                                </button>\r\n                                <button onClick={() => handleInsights(product)}>\r\n                                  Promotion\r\n                                </button>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"price-container\">\r\n                          <span\r\n                            className=\"original-price\"\r\n                            style={{\r\n                              textDecoration:\r\n                                product.salePrice !== null\r\n                                  ? \"line-through\"\r\n                                  : \"none\",\r\n                              color:\r\n                                product.salePrice !== null ? \"#999\" : \"#2d2d2d\",\r\n                            }}\r\n                          >\r\n                            E£{product.price}\r\n                          </span>\r\n                          {product.salePrice !== null && (\r\n                            <span className=\"sale-price\">\r\n                              E£{product.salePrice}\r\n                            </span>\r\n                          )}{\" \"}\r\n                        </div>\r\n                        <p className=\"product-summary\">\r\n                          {product.description.substring(0, 100)}...\r\n                        </p>\r\n                      </div>\r\n\r\n                      <div className=\"product-card-body\">\r\n                        <h5>Summary</h5>\r\n\r\n                        <div className=\"metrics-container\">\r\n                          <div className=\"metric\">\r\n                            <span className=\"metric-label\">Sales</span>\r\n                            <div\r\n                              style={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                gap: \"5px\",\r\n                              }}\r\n                            >\r\n                              <span className=\"metric-value\">\r\n                                {product.sales ? product.sales : \"No yet sales\"}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                          <hr style={{ margin: \"10px 0\", color: \"#ddd\" }} />\r\n                          <div className=\"metric\">\r\n                            <span className=\"metric-label\">\r\n                              Semaining Products\r\n                            </span>\r\n                            <span className=\"metric-value\">\r\n                              {product.stock}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Section for products with status true */}\r\n        <div>\r\n          <div\r\n            className=\"section-header\"\r\n            onClick={() => setShowTrueStatus((prev) => !prev)}\r\n            style={{\r\n              margin: \"30px 0\",\r\n            }}\r\n          >\r\n            <span>Products with approval</span>\r\n            {showTrueStatus ? <AiOutlineUp /> : <AiOutlineDown />}\r\n          </div>\r\n          {showTrueStatus && (\r\n            <div className=\"true-status-section\">\r\n              {trueStatusProducts.length === 0 ? (\r\n                <p>No products with approval.</p>\r\n              ) : (\r\n                <div className=\"product-grid\">\r\n                  {trueStatusProducts.map((product) => (\r\n                    <div className=\"promotion-card\" key={product.id}>\r\n                      <div className=\"promotion-image-container\">\r\n                        {product && product.mainImage && (\r\n                          <img\r\n                            src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product?.mainImage}`}\r\n                            alt={product?.name || \"Product\"}\r\n                            className=\"promotion-image\"\r\n                          />\r\n                        )}\r\n                        {product.discountPercentage && (\r\n                          <div className=\"discount-badge\">\r\n                            {product.discountPercentage}% OFF\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"promotion-details\">\r\n                        <div\r\n                          style={{\r\n                            display: \"flex\",\r\n                            justifyContent: \"space-between\",\r\n                            flexDirection: \"row\",\r\n                          }}\r\n                        >\r\n                          <h3 className=\"promotion-details\">{product.name}</h3>\r\n                          <div className=\"menu-container\">\r\n                            <BsThreeDotsVertical\r\n                              onClick={(e) => {\r\n                                e.stopPropagation(); // Prevent the click from triggering the document listener\r\n                                toggleMenu(product._id);\r\n                              }}\r\n                              className=\"three-dots-icon\"\r\n                            />\r\n                            {menuOpen === product._id && (\r\n                              <div className=\"menu-dropdown\">\r\n                                <button onClick={() => handleEdit(product)}>\r\n                                  Edit\r\n                                </button>\r\n                                <button\r\n                                  onClick={() => {\r\n                                    setProductToDelete(product);\r\n                                    setConfirmDialogOpen(true);\r\n                                  }}\r\n                                >\r\n                                  Delete\r\n                                </button>\r\n                                <button onClick={() => handleInsights(product)}>\r\n                                  Promotion\r\n                                </button>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"price-container\">\r\n                          <span\r\n                            className=\"original-price\"\r\n                            style={{\r\n                              textDecoration:\r\n                                product.salePrice !== null\r\n                                  ? \"line-through\"\r\n                                  : \"none\",\r\n                              color:\r\n                                product.salePrice !== null ? \"#999\" : \"#2d2d2d\",\r\n                            }}\r\n                          >\r\n                            E£{product.price}\r\n                          </span>\r\n                          {product.salePrice !== null && (\r\n                            <span className=\"sale-price\">\r\n                              E£{product.salePrice}\r\n                            </span>\r\n                          )}{\" \"}\r\n                        </div>\r\n                        <p className=\"product-summary\">\r\n                          {product.description.substring(0, 100)}...\r\n                        </p>\r\n                      </div>\r\n\r\n                      <div className=\"product-card-body\">\r\n                        <h5>Summary</h5>\r\n\r\n                        <div className=\"metrics-container\">\r\n                          <div className=\"metric\">\r\n                            <span className=\"metric-label\">Sales</span>\r\n                            <div\r\n                              style={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                gap: \"5px\",\r\n                              }}\r\n                            >\r\n                              <span className=\"metric-value\">\r\n                                {product.sales ? product.sales : \"No yet sales\"}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                          <hr style={{ margin: \"10px 0\", color: \"#ddd\" }} />\r\n                          <div className=\"metric\">\r\n                            <span className=\"metric-label\">\r\n                              Semaining Products\r\n                            </span>\r\n                            <span className=\"metric-value\">\r\n                              {product.stock}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Pagination */}\r\n        <div className=\"pagination\">\r\n          {Array.from({ length: totalPages }, (_, index) => (\r\n            <button\r\n              key={index + 1}\r\n              onClick={() => paginate(index + 1)}\r\n              style={{\r\n                margin: \"5px\",\r\n                padding: \"8px 12px\",\r\n                backgroundColor:\r\n                  currentPage === index + 1 ? \"#2d2d2d\" : \"#efebe8\",\r\n                color: currentPage === index + 1 ? \"#fff\" : \"#2d2d2d\",\r\n                borderRadius: \"5px\",\r\n                cursor: \"pointer\",\r\n              }}\r\n            >\r\n              {index + 1}\r\n            </button>\r\n          ))}\r\n        </div>\r\n        <VariantDialog\r\n          open={openVariantDialog}\r\n          onClose={() => setOpenVariantDialog(false)}\r\n          onSubmit={handleVariantSubmit}\r\n          sku={product?.sku}\r\n          brandId={vendor?.brandId}\r\n        />\r\n        {/* Promotion Modal */}\r\n        {promotionModalOpen && (\r\n          <PromotionModal\r\n            open={promotionModalOpen}\r\n            onClose={() => setPromotionModalOpen(false)}\r\n            onSave={handleSavePromotion}\r\n            product={selectedProduct}\r\n          />\r\n        )}\r\n        <ConfirmationDialog\r\n          open={confirmDialogOpen}\r\n          title=\"Delete Product\"\r\n          content=\"Are you sure you want to delete this product?\"\r\n          onConfirm={handleDelete}\r\n          onCancel={() => {\r\n            setConfirmDialogOpen(false);\r\n            setProductToDelete(null);\r\n          }}\r\n        />\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ProductsPageVendor;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,QAAQ,eAAe;AACzE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB,CAAC,CAAC;AAC7D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,oBAAoB,CAAC,CAAC;AACjD,SAASC,SAAS,QAAQ,2BAA2B,CAAC,CAAC;AACvD,OAAOC,aAAa,MAAM,iBAAiB,CAAC,CAAC;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM;IAAEC;EAAO,CAAC,GAAGZ,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAMoC,eAAe,GAAG,EAAE;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAAC2C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM6D,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACF,MAAMC,GAAG,GAAG,MAAMtD,KAAK,CAACuD,IAAI,CAC1B,oDAAoD,EACpDF,WACF,CAAC;MACDN,WAAW,CAAES,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEF,GAAG,CAACG,IAAI,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACrB,KAAK,CAAC,yBAAyB,EAAEoB,GAAG,CAAC;IAC/C;EACF,CAAC;EAEDpE,SAAS,CAAC,MAAM;IACd,IAAIwB,MAAM,EAAE;MACV,MAAM;QAAE8C;MAAQ,CAAC,GAAG9C,MAAM,CAAC,CAAC;;MAE5B,MAAM+C,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAC9B,gEAAgEH,OAAO,EAAE,EACzE;YACEI,MAAM,EAAE;cACNC,QAAQ,EAAE5C;YACZ;UACF,CACF,CAAC;UACDgB,UAAU,CAACyB,QAAQ,CAACL,IAAI,CAAC;UACzB,MAAMS,eAAe,GAAGJ,QAAQ,CAACL,IAAI;;UAErC;UACA,MAAMU,qBAAqB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC7CH,eAAe,CAACI,GAAG,CAAC,MAAOlC,OAAO,IAAK;YACrC,IAAIA,OAAO,CAACmC,IAAI,EAAE;cAChB,IAAI;gBACF,MAAMC,YAAY,GAAG,MAAMxE,KAAK,CAAC+D,GAAG,CAClC,iDAAiD3B,OAAO,CAACmC,IAAI,EAC/D,CAAC;gBACDnC,OAAO,CAACqC,QAAQ,GAAGD,YAAY,CAACf,IAAI,CAACiB,IAAI,IAAI,SAAS,CAAC,CAAC;cAC1D,CAAC,CAAC,OAAOpC,KAAK,EAAE;gBACdqB,OAAO,CAACrB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;gBAC5CF,OAAO,CAACqC,QAAQ,GAAG,SAAS,CAAC,CAAC;cAChC;YACF,CAAC,MAAM;cACLrC,OAAO,CAACqC,QAAQ,GAAG,SAAS,CAAC,CAAC;YAChC;YACA,OAAOrC,OAAO;UAChB,CAAC,CACH,CAAC;UACDpB,WAAW,CAACmD,qBAAqB,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC;MAED,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;QAClC,IAAI;UACF,MAAMb,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAC9B,yDACF,CAAC;UACD7C,aAAa,CAAC4C,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACdqB,OAAO,CAACrB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC;MAEDuB,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBc,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAC7D,MAAM,EAAEO,gBAAgB,EAAEF,aAAa,CAAC,CAAC,CAAC,CAAC;;EAE/C,MAAMyD,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACxC,MAAMC,kBAAkB,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACzC1D,mBAAmB,CAACwD,kBAAkB,CAAC,CAAC,CAAC;IACzC1D,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBI,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE5B;IACA,IAAIsD,kBAAkB,EAAE;MACtB,IAAI;QACF,MAAMhB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAC9B,8DAA8De,kBAAkB,EAClF,CAAC;QACD1D,gBAAgB,CAAC0C,QAAQ,CAACL,IAAI,CAAC;MACjC,CAAC,CAAC,OAAOnB,KAAK,EAAE;QACdqB,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF;EACF,CAAC;EAED,MAAM2C,gBAAgB,GAAGlE,QAAQ,CAACmE,MAAM,CAAE9C,OAAO,IAAK;IACpD,IAAIb,mBAAmB,EAAE;MACvB,OAAOa,OAAO,CAAC+C,aAAa,KAAK5D,mBAAmB;IACtD;IACA,IAAIF,gBAAgB,EAAE;MACpB,OAAOe,OAAO,CAAC6B,QAAQ,KAAK5C,gBAAgB,CAAC,CAAC;IAChD;IACA,OAAO,IAAI,CAAC,CAAC;EACf,CAAC,CAAC;EAEF,MAAM+D,mBAAmB,GAAGH,gBAAgB,CAACC,MAAM,CAChD9C,OAAO,IAAKA,OAAO,CAACiD,MAAM,KAAK,KAClC,CAAC;EACD,MAAMC,kBAAkB,GAAGL,gBAAgB,CAACC,MAAM,CAC/C9C,OAAO,IAAKA,OAAO,CAACiD,MAAM,KAAK,IAClC,CAAC;EAED,MAAME,UAAU,GAAIC,SAAS,IAAK;IAChC3D,WAAW,CAAE4D,MAAM,IAAMA,MAAM,KAAKD,SAAS,GAAG,IAAI,GAAGA,SAAU,CAAC;EACpE,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B7D,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM8D,UAAU,GAAIvD,OAAO,IAAK;IAC9BH,kBAAkB,CAACG,OAAO,CAAC;IAC3BL,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM6D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC1C,eAAe,EAAE;IACtB,IAAI2C,OAAO,GAAG,KAAK;IACnB,IAAI;MACF,MAAM7F,KAAK,CAAC8F,MAAM,CAChB,8CAA8C5C,eAAe,CAAC6C,GAAG,EACnE,CAAC;MACD/E,WAAW,CAAEwC,IAAI,IAAKA,IAAI,CAAC0B,MAAM,CAAEc,CAAC,IAAKA,CAAC,CAACD,GAAG,KAAK7C,eAAe,CAAC6C,GAAG,CAAC,CAAC;MACxEF,OAAO,GAAG,IAAI;IAChB,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAAC,6CAA6C,CAAC;MACvD0D,KAAK,CAAC,6CAA6C,CAAC;IACtD;IACAhD,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM+C,cAAc,GAAI9D,OAAO,IAAK;IAClCH,kBAAkB,CAACG,OAAO,CAAC,CAAC,CAAC;IAC7BD,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMgE,mBAAmB,GAAIC,gBAAgB,IAAK;IAChDzC,OAAO,CAAC0C,GAAG,CAAC,oBAAoB,EAAED,gBAAgB,CAAC;IACnD;IACAjE,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;EAChC,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMmE,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACvB,gBAAgB,CAACwB,MAAM,GAAG9E,eAAe,CAAC;EACvE,MAAM+E,QAAQ,GAAIC,UAAU,IAAKjF,cAAc,CAACiF,UAAU,CAAC;;EAE3D;EACArH,SAAS,CAAC,MAAM;IACd,MAAMsH,kBAAkB,GAAGA,CAAA,KAAMlB,aAAa,CAAC,CAAC,CAAC,CAAC;;IAElDmB,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEF,kBAAkB,CAAC;IAEtD,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEH,kBAAkB,CAAC,CAAC,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI9E,UAAU,EAAE;IACd,oBACEtB,OAAA,CAACL,aAAa;MACZ6G,eAAe,EAAEhF,eAAgB,CAAC;MAAA;MAClCiF,MAAM,EAAEA,CAAA,KAAMlF,aAAa,CAAC,KAAK,CAAE,CAAC;IAAA;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAEN;EAEA,oBACE7G,OAAA,CAAAE,SAAA;IAAA4G,QAAA,eACE9G,OAAA;MAAK+G,SAAS,EAAC,0BAA0B;MAAAD,QAAA,gBACvC9G,OAAA;QAAQ+G,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACzC9G,OAAA;UAAK+G,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrC9G,OAAA;YAAA8G,QAAA,EAAI;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB7G,OAAA;YAAA8G,QAAA,EAAG;UAAuB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACN7G,OAAA;UAAK+G,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBACpC9G,OAAA,CAACJ,MAAM;YACLoH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,YAAY,CAAE;YAC3C6G,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAE;cACLC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdC,UAAU,EAAE,MAAM;cAClBC,SAAS,EAAE;YACb,CAAE;YAAAR,QAAA,gBAEF9G,OAAA,CAAChB,YAAY;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAClB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7G,OAAA,CAACJ,MAAM;YACLoH,OAAO,EAAEA,CAAA,KAAM3E,oBAAoB,CAAC,IAAI,CAAE;YAC1C4E,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAE;cACLC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdC,UAAU,EAAE,MAAM;cAClBC,SAAS,EAAE;YACb,CAAE;YAAAR,QAAA,EACH;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAIT7G,OAAA;QACEkH,KAAK,EAAE;UACLK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,KAAK;UACrBC,YAAY,EAAE;QAChB,CAAE;QAAAZ,QAAA,eAEF9G,OAAA,CAACb,WAAW;UAACwI,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACxB9G,OAAA,CAACZ,UAAU;YAACyI,EAAE,EAAC,0BAA0B;YAAAf,QAAA,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/D7G,OAAA,CAACd,MAAM;YACLyI,EAAE,EAAE;cACFG,KAAK,EAAE,OAAO;cACdV,KAAK,EAAE,SAAS;cAChBD,eAAe,EAAE;YACnB,CAAE;YACF3C,KAAK,EAAE3D,gBAAiB;YACxBkH,QAAQ,EAAE3D,oBAAqB;YAAA0C,QAAA,gBAE/B9G,OAAA,CAACf,QAAQ;cAACuF,KAAK,EAAC,EAAE;cAAAsC,QAAA,EAAC;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EAC5CpG,UAAU,CAACqD,GAAG,CAAEL,QAAQ,iBACvBzD,OAAA,CAACf,QAAQ;cAAoBuF,KAAK,EAAEf,QAAQ,CAAC8B,GAAI;cAAAuB,QAAA,EAC9CrD,QAAQ,CAACS;YAAI,GADDT,QAAQ,CAAC8B,GAAG;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAGN7G,OAAA;QAAA8G,QAAA,gBACE9G,OAAA;UACE+G,SAAS,EAAC,gBAAgB;UAC1BC,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAAEe,IAAI,IAAK,CAACA,IAAI,CAAE;UACnDkE,KAAK,EAAE;YACLc,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,gBAEF9G,OAAA;YAAA8G,QAAA,EAAM;UAAyB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACrC7E,eAAe,gBAAGhC,OAAA,CAACT,WAAW;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7G,OAAA,CAACV,aAAa;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,EACL7E,eAAe,iBACdhC,OAAA;UAAK+G,SAAS,EAAC,sBAAsB;UAAAD,QAAA,EAClClC,mBAAmB,CAACqB,MAAM,KAAK,CAAC,gBAC/BjG,OAAA;YAAA8G,QAAA,EAAG;UAAyB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAEhC7G,OAAA;YAAK+G,SAAS,EAAC,cAAc;YAAAD,QAAA,EAC1BlC,mBAAmB,CAACd,GAAG,CAAElC,OAAO,iBAC/B5B,OAAA;cAAK+G,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B9G,OAAA;gBAAK+G,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,GACvClF,OAAO,IAAIA,OAAO,CAACqG,SAAS,iBAC3BjI,OAAA;kBACEkI,GAAG,EAAE,uDAAuDtG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqG,SAAS,EAAG;kBACjFE,GAAG,EAAE,CAAAvG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,IAAI,KAAI,SAAU;kBAChC6C,SAAS,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CACF,EACAjF,OAAO,CAACwG,kBAAkB,iBACzBpI,OAAA;kBAAK+G,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,GAC5BlF,OAAO,CAACwG,kBAAkB,EAAC,OAC9B;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN7G,OAAA;gBAAK+G,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC9G,OAAA;kBACEkH,KAAK,EAAE;oBACLK,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BY,aAAa,EAAE;kBACjB,CAAE;kBAAAvB,QAAA,gBAEF9G,OAAA;oBAAI+G,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,EAAElF,OAAO,CAACsC;kBAAI;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD7G,OAAA;oBAAK+G,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,gBAC7B9G,OAAA,CAACX,mBAAmB;sBAClB2H,OAAO,EAAG3C,CAAC,IAAK;wBACdA,CAAC,CAACiE,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrBvD,UAAU,CAACnD,OAAO,CAAC2D,GAAG,CAAC;sBACzB,CAAE;sBACFwB,SAAS,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,EACDzF,QAAQ,KAAKQ,OAAO,CAAC2D,GAAG,iBACvBvF,OAAA;sBAAK+G,SAAS,EAAC,eAAe;sBAAAD,QAAA,gBAC5B9G,OAAA;wBAAQgH,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAACvD,OAAO,CAAE;wBAAAkF,QAAA,EAAC;sBAE5C;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT7G,OAAA;wBACEgH,OAAO,EAAEA,CAAA,KAAM;0BACbrE,kBAAkB,CAACf,OAAO,CAAC;0BAC3Ba,oBAAoB,CAAC,IAAI,CAAC;wBAC5B,CAAE;wBAAAqE,QAAA,EACH;sBAED;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT7G,OAAA;wBAAQgH,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAAC9D,OAAO,CAAE;wBAAAkF,QAAA,EAAC;sBAEhD;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7G,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9G,OAAA;oBACE+G,SAAS,EAAC,gBAAgB;oBAC1BG,KAAK,EAAE;sBACLqB,cAAc,EACZ3G,OAAO,CAAC4G,SAAS,KAAK,IAAI,GACtB,cAAc,GACd,MAAM;sBACZpB,KAAK,EACHxF,OAAO,CAAC4G,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG;oBAC1C,CAAE;oBAAA1B,QAAA,GACH,OACG,EAAClF,OAAO,CAAC6G,KAAK;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACNjF,OAAO,CAAC4G,SAAS,KAAK,IAAI,iBACzBxI,OAAA;oBAAM+G,SAAS,EAAC,YAAY;oBAAAD,QAAA,GAAC,OACzB,EAAClF,OAAO,CAAC4G,SAAS;kBAAA;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CACP,EAAE,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7G,OAAA;kBAAG+G,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,GAC3BlF,OAAO,CAAC8G,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzC;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN7G,OAAA;gBAAK+G,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC9G,OAAA;kBAAA8G,QAAA,EAAI;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEhB7G,OAAA;kBAAK+G,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChC9G,OAAA;oBAAK+G,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrB9G,OAAA;sBAAM+G,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAAK;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3C7G,OAAA;sBACEkH,KAAK,EAAE;wBACLK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBoB,GAAG,EAAE;sBACP,CAAE;sBAAA9B,QAAA,eAEF9G,OAAA;wBAAM+G,SAAS,EAAC,cAAc;wBAAAD,QAAA,EAC3BlF,OAAO,CAACiH,KAAK,GAAGjH,OAAO,CAACiH,KAAK,GAAG;sBAAc;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7G,OAAA;oBAAIkH,KAAK,EAAE;sBAAEc,MAAM,EAAE,QAAQ;sBAAEZ,KAAK,EAAE;oBAAO;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClD7G,OAAA;oBAAK+G,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrB9G,OAAA;sBAAM+G,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAE/B;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP7G,OAAA;sBAAM+G,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAC3BlF,OAAO,CAACkH;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA1G6BjF,OAAO,CAACiG,EAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2G1C,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7G,OAAA;QAAA8G,QAAA,gBACE9G,OAAA;UACE+G,SAAS,EAAC,gBAAgB;UAC1BC,OAAO,EAAEA,CAAA,KAAM7E,iBAAiB,CAAEa,IAAI,IAAK,CAACA,IAAI,CAAE;UAClDkE,KAAK,EAAE;YACLc,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,gBAEF9G,OAAA;YAAA8G,QAAA,EAAM;UAAsB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAClC3E,cAAc,gBAAGlC,OAAA,CAACT,WAAW;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7G,OAAA,CAACV,aAAa;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,EACL3E,cAAc,iBACblC,OAAA;UAAK+G,SAAS,EAAC,qBAAqB;UAAAD,QAAA,EACjChC,kBAAkB,CAACmB,MAAM,KAAK,CAAC,gBAC9BjG,OAAA;YAAA8G,QAAA,EAAG;UAA0B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAEjC7G,OAAA;YAAK+G,SAAS,EAAC,cAAc;YAAAD,QAAA,EAC1BhC,kBAAkB,CAAChB,GAAG,CAAElC,OAAO,iBAC9B5B,OAAA;cAAK+G,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B9G,OAAA;gBAAK+G,SAAS,EAAC,2BAA2B;gBAAAD,QAAA,GACvClF,OAAO,IAAIA,OAAO,CAACqG,SAAS,iBAC3BjI,OAAA;kBACEkI,GAAG,EAAE,uDAAuDtG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqG,SAAS,EAAG;kBACjFE,GAAG,EAAE,CAAAvG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsC,IAAI,KAAI,SAAU;kBAChC6C,SAAS,EAAC;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CACF,EACAjF,OAAO,CAACwG,kBAAkB,iBACzBpI,OAAA;kBAAK+G,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,GAC5BlF,OAAO,CAACwG,kBAAkB,EAAC,OAC9B;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN7G,OAAA;gBAAK+G,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC9G,OAAA;kBACEkH,KAAK,EAAE;oBACLK,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BY,aAAa,EAAE;kBACjB,CAAE;kBAAAvB,QAAA,gBAEF9G,OAAA;oBAAI+G,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,EAAElF,OAAO,CAACsC;kBAAI;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD7G,OAAA;oBAAK+G,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,gBAC7B9G,OAAA,CAACX,mBAAmB;sBAClB2H,OAAO,EAAG3C,CAAC,IAAK;wBACdA,CAAC,CAACiE,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrBvD,UAAU,CAACnD,OAAO,CAAC2D,GAAG,CAAC;sBACzB,CAAE;sBACFwB,SAAS,EAAC;oBAAiB;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,EACDzF,QAAQ,KAAKQ,OAAO,CAAC2D,GAAG,iBACvBvF,OAAA;sBAAK+G,SAAS,EAAC,eAAe;sBAAAD,QAAA,gBAC5B9G,OAAA;wBAAQgH,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAACvD,OAAO,CAAE;wBAAAkF,QAAA,EAAC;sBAE5C;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT7G,OAAA;wBACEgH,OAAO,EAAEA,CAAA,KAAM;0BACbrE,kBAAkB,CAACf,OAAO,CAAC;0BAC3Ba,oBAAoB,CAAC,IAAI,CAAC;wBAC5B,CAAE;wBAAAqE,QAAA,EACH;sBAED;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT7G,OAAA;wBAAQgH,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAAC9D,OAAO,CAAE;wBAAAkF,QAAA,EAAC;sBAEhD;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7G,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9G,OAAA;oBACE+G,SAAS,EAAC,gBAAgB;oBAC1BG,KAAK,EAAE;sBACLqB,cAAc,EACZ3G,OAAO,CAAC4G,SAAS,KAAK,IAAI,GACtB,cAAc,GACd,MAAM;sBACZpB,KAAK,EACHxF,OAAO,CAAC4G,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG;oBAC1C,CAAE;oBAAA1B,QAAA,GACH,OACG,EAAClF,OAAO,CAAC6G,KAAK;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACNjF,OAAO,CAAC4G,SAAS,KAAK,IAAI,iBACzBxI,OAAA;oBAAM+G,SAAS,EAAC,YAAY;oBAAAD,QAAA,GAAC,OACzB,EAAClF,OAAO,CAAC4G,SAAS;kBAAA;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CACP,EAAE,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7G,OAAA;kBAAG+G,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,GAC3BlF,OAAO,CAAC8G,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzC;gBAAA;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN7G,OAAA;gBAAK+G,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC9G,OAAA;kBAAA8G,QAAA,EAAI;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEhB7G,OAAA;kBAAK+G,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChC9G,OAAA;oBAAK+G,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrB9G,OAAA;sBAAM+G,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAAK;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3C7G,OAAA;sBACEkH,KAAK,EAAE;wBACLK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBoB,GAAG,EAAE;sBACP,CAAE;sBAAA9B,QAAA,eAEF9G,OAAA;wBAAM+G,SAAS,EAAC,cAAc;wBAAAD,QAAA,EAC3BlF,OAAO,CAACiH,KAAK,GAAGjH,OAAO,CAACiH,KAAK,GAAG;sBAAc;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7G,OAAA;oBAAIkH,KAAK,EAAE;sBAAEc,MAAM,EAAE,QAAQ;sBAAEZ,KAAK,EAAE;oBAAO;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClD7G,OAAA;oBAAK+G,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrB9G,OAAA;sBAAM+G,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAAC;oBAE/B;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACP7G,OAAA;sBAAM+G,SAAS,EAAC,cAAc;sBAAAD,QAAA,EAC3BlF,OAAO,CAACkH;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA1G6BjF,OAAO,CAACiG,EAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2G1C,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7G,OAAA;QAAK+G,SAAS,EAAC,YAAY;QAAAD,QAAA,EACxBiC,KAAK,CAACC,IAAI,CAAC;UAAE/C,MAAM,EAAEH;QAAW,CAAC,EAAE,CAACmD,CAAC,EAAEC,KAAK,kBAC3ClJ,OAAA;UAEEgH,OAAO,EAAEA,CAAA,KAAMd,QAAQ,CAACgD,KAAK,GAAG,CAAC,CAAE;UACnChC,KAAK,EAAE;YACLc,MAAM,EAAE,KAAK;YACbmB,OAAO,EAAE,UAAU;YACnBhC,eAAe,EACblG,WAAW,KAAKiI,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACnD9B,KAAK,EAAEnG,WAAW,KAAKiI,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;YACrDE,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UAAAvC,QAAA,EAEDoC,KAAK,GAAG;QAAC,GAZLA,KAAK,GAAG,CAAC;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaR,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN7G,OAAA,CAACH,aAAa;QACZyJ,IAAI,EAAElH,iBAAkB;QACxBmH,OAAO,EAAEA,CAAA,KAAMlH,oBAAoB,CAAC,KAAK,CAAE;QAC3CmH,QAAQ,EAAE5G,mBAAoB;QAC9B6G,GAAG,EAAE7H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6H,GAAI;QAClBrG,OAAO,EAAE9C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8C;MAAQ;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,EAEDnF,kBAAkB,iBACjB1B,OAAA,CAACP,cAAc;QACb6J,IAAI,EAAE5H,kBAAmB;QACzB6H,OAAO,EAAEA,CAAA,KAAM5H,qBAAqB,CAAC,KAAK,CAAE;QAC5C+H,MAAM,EAAE/D,mBAAoB;QAC5B/D,OAAO,EAAEJ;MAAgB;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACF,eACD7G,OAAA,CAACF,kBAAkB;QACjBwJ,IAAI,EAAE9G,iBAAkB;QACxBmH,KAAK,EAAC,gBAAgB;QACtBC,OAAO,EAAC,+CAA+C;QACvDC,SAAS,EAAEzE,YAAa;QACxB0E,QAAQ,EAAEA,CAAA,KAAM;UACdrH,oBAAoB,CAAC,KAAK,CAAC;UAC3BE,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MAAE;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC,gBACN,CAAC;AAEP,CAAC;AAACxG,EAAA,CAzkBIF,kBAAkB;EAAA,QACHT,SAAS;AAAA;AAAAqK,EAAA,GADxB5J,kBAAkB;AA2kBxB,eAAeA,kBAAkB;AAAC,IAAA4J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}