{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\DashboardVendor.jsx\",\n  _s = $RefreshSig$();\nimport React, { lazy, Suspense, useState, useEffect } from \"react\";\nimport { SlCalender } from \"react-icons/sl\";\nimport { Box } from \"@mui/material\";\nimport { FaBox, FaTruck, FaCheckCircle, FaRedo, FaChartLine, FaShoppingBag, FaClipboardList } from \"react-icons/fa\"; // React Icons\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\nimport { useVendor } from \"../../utils/vendorContext\";\nimport { LineChart, Line, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from \"recharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderDetails = /*#__PURE__*/lazy(_c = () => import(\"./orderDetails\"));\n_c2 = OrderDetails;\nconst LoadingScreen = /*#__PURE__*/lazy(_c3 = () => import(\"../../Pages/loadingScreen\"));\n_c4 = LoadingScreen;\nconst DashboardVendor = () => {\n  _s();\n  const {\n    vendor\n  } = useVendor();\n  const [orders, setOrders] = useState([]);\n  const [selectedOrder, setSelectedOrder] = useState(null); // State for selected order\n  const [products, setProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [totalOrders, setTotalOrders] = useState(0);\n  const [activeOrders, setActiveOrders] = useState(0);\n  const [completedOrders, setCompletedOrders] = useState(0);\n  const [returnedOrders, setReturnedOrders] = useState(0);\n  const [totalSales, setTotalSales] = useState(0);\n  const [confirmedSales, setConfirmedSales] = useState(0);\n  const [deliveredSales, setDeliveredSales] = useState(0);\n  const [returnedSales, setReturnedSales] = useState(0);\n  const [salesPercentageChange, setSalesPercentageChange] = useState(0);\n  const [confirmedSalesPercentageChange, setConfirmedSalesPercentageChange] = useState(0);\n  const [deliveredSalesPercentageChange, setDeliveredSalesPercentageChange] = useState(0);\n  const [returnedSalesPercentageChange, setReturnedSalesPercentageChange] = useState(0);\n  const [activeTab, setActiveTab] = useState(\"weekly\");\n  const [weeklySales, setWeeklySales] = useState([]);\n  const [monthlySales, setMonthlySales] = useState([]);\n  const [yearlySales, setYearlySales] = useState([]);\n  const [chartData, setChartData] = useState([]);\n\n  // Add date calculation\n  const getCurrentDateRange = () => {\n    const today = new Date();\n    const formatDate = date => {\n      const months = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n      return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;\n    };\n    return formatDate(today);\n  };\n\n  // Fetch best sellers\n  useEffect(() => {\n    const fetchProducts = async () => {\n      if (!(vendor !== null && vendor !== void 0 && vendor.brandId)) {\n        setIsLoading(false);\n        return;\n      }\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/orders/vendor/best-sellers/${vendor.brandId}`);\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch best sellers\");\n        }\n        const data = await response.json();\n        setProducts(data || []);\n      } catch (error) {\n        console.error(\"Error fetching best sellers:\", error);\n        setProducts([]);\n      }\n    };\n    fetchProducts();\n  }, [vendor]);\n\n  // Fetch orders\n  useEffect(() => {\n    const fetchOrders = async () => {\n      if (!(vendor !== null && vendor !== void 0 && vendor.brandId)) {\n        setIsLoading(false);\n        return;\n      }\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/orders/orders/brand/${vendor.brandId}`);\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch orders\");\n        }\n        const data = await response.json();\n        const sortedOrders = [...data].sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate));\n        setOrders(sortedOrders || []);\n        setTotalOrders((sortedOrders === null || sortedOrders === void 0 ? void 0 : sortedOrders.length) || 0);\n      } catch (error) {\n        console.error(\"Error fetching orders:\", error);\n        setOrders([]);\n        setTotalOrders(0);\n      }\n    };\n    fetchOrders();\n  }, [vendor]);\n\n  // Fetch statistics\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!(vendor !== null && vendor !== void 0 && vendor.brandId)) {\n        setIsLoading(false);\n        return;\n      }\n      setIsLoading(true);\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/orders/statistics/${vendor.brandId}`);\n        const data = await response.json();\n        if (data.error) {\n          console.warn(\"No orders found for this brand.\");\n          // Set default values when no orders exist\n          setTotalOrders(0);\n          setActiveOrders(0);\n          setCompletedOrders(0);\n          setReturnedOrders(0);\n          setTotalSales(0);\n          setConfirmedSales(0);\n          setDeliveredSales(0);\n          setReturnedSales(0);\n          setSalesPercentageChange(0);\n          setConfirmedSalesPercentageChange(0);\n          setDeliveredSalesPercentageChange(0);\n          setReturnedSalesPercentageChange(0);\n        } else {\n          // Set statistics from data\n          setTotalOrders(data.totalOrders || 0);\n          setActiveOrders(data.totalConfirmed || 0);\n          setCompletedOrders(data.totalDelivered || 0);\n          setReturnedOrders(data.totalReturned || 0);\n          setTotalSales(data.totalSales || 0);\n          setConfirmedSales(data.confirmedSales || 0);\n          setDeliveredSales(data.deliveredSales || 0);\n          setReturnedSales(data.returnedSales || 0);\n\n          // Calculate percentages safely\n          setSalesPercentageChange(data.totalSales > 0 ? (data.deliveredSales / data.totalSales * 100).toFixed(2) : 0);\n          setConfirmedSalesPercentageChange(data.totalSales > 0 ? (data.confirmedSales / data.totalSales * 100).toFixed(2) : 0);\n          setDeliveredSalesPercentageChange(data.totalSales > 0 ? (data.deliveredSales / data.totalSales * 100).toFixed(2) : 0);\n          setReturnedSalesPercentageChange(data.totalSales > 0 ? (data.returnedSales / data.totalSales * 100).toFixed(2) : 0);\n        }\n      } catch (error) {\n        console.error(\"Error fetching statistics:\", error);\n        setError(\"Failed to load statistics\");\n        // Set default values on error\n        setTotalOrders(0);\n        setActiveOrders(0);\n        setCompletedOrders(0);\n        setReturnedOrders(0);\n        setTotalSales(0);\n        setConfirmedSales(0);\n        setDeliveredSales(0);\n        setReturnedSales(0);\n        setSalesPercentageChange(0);\n        setConfirmedSalesPercentageChange(0);\n        setDeliveredSalesPercentageChange(0);\n        setReturnedSalesPercentageChange(0);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchData();\n  }, [vendor]);\n  useEffect(() => {\n    const fetchSalesData = async () => {\n      if (!(vendor !== null && vendor !== void 0 && vendor.brandId)) return;\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/orders/sales-graph/${vendor.brandId}`);\n        const data = await response.json();\n\n        // Set default empty arrays if no data\n        setWeeklySales(data.weeklySales || []);\n        setMonthlySales(data.monthlySales || []);\n        setYearlySales(data.yearlySales || []);\n      } catch (error) {\n        console.error(\"Error fetching sales graph data:\", error);\n        setWeeklySales([]);\n        setMonthlySales([]);\n        setYearlySales([]);\n      }\n    };\n    fetchSalesData();\n  }, [vendor]);\n  useEffect(() => {\n    function aggregateAndSort(data, key) {\n      // Aggregate sales for duplicate keys\n      const map = {};\n      data.forEach(item => {\n        if (!map[item[key]]) map[item[key]] = 0;\n        map[item[key]] += Number(item.sales) || 0;\n      });\n      // Convert to array and sort\n      return Object.entries(map).map(([k, v]) => ({\n        [key]: Number(k),\n        sales: v\n      })).sort((a, b) => a[key] - b[key]);\n    }\n    let formattedData = [];\n    switch (activeTab) {\n      case \"weekly\":\n        formattedData = aggregateAndSort(weeklySales, \"week\").map(item => ({\n          ...item,\n          week: `Week ${item.week}`\n        }));\n        break;\n      case \"monthly\":\n        const monthNames = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n        formattedData = aggregateAndSort(monthlySales, \"month\").map(item => ({\n          ...item,\n          month: monthNames[item.month - 1] || `Month ${item.month}`\n        }));\n        break;\n      case \"yearly\":\n        formattedData = aggregateAndSort(yearlySales, \"year\").map(item => ({\n          ...item,\n          year: `${item.year}`\n        }));\n        break;\n      default:\n        formattedData = [];\n    }\n    setChartData(formattedData);\n  }, [activeTab, weeklySales, monthlySales, yearlySales]);\n  if (selectedOrder) {\n    return /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(OrderDetails, {\n        order: selectedOrder,\n        onBack: () => setSelectedOrder(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this);\n  }\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-vendor\",\n      style: {\n        textAlign: \"center\",\n        padding: \"2rem\",\n        color: \"red\"\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-vendor\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Home > Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-date-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(SlCalender, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: getCurrentDateRange()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-overview-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaBox, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Total Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"E\\xA3 \", totalSales]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Total orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: totalOrders\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u25B2 \", salesPercentageChange, \"% Compared to Last Month\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaTruck, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Active Orders sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"E\\xA3 \", confirmedSales]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Active Orders \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\" \", activeOrders]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u25B2 \", confirmedSalesPercentageChange, \"% Compared to Last Month\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaCheckCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Completed Orders sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"E\\xA3 \", deliveredSales]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Completed Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\" \", completedOrders]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u25B2 \", deliveredSalesPercentageChange, \"% Compared to Last Month\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaRedo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Return Orders sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"E\\xA3 \", returnedSales]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Returned Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: returnedOrders\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\u25B2 \", returnedSalesPercentageChange, \"% Compared to Last Month\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-chart-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: \"10px auto\"\n          },\n          children: \"Sales Graph\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-tabs-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `chart-tab-vendor ${activeTab === \"weekly\" ? \"active\" : \"\"}`,\n            onClick: () => setActiveTab(\"weekly\"),\n            children: \"Weekly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `chart-tab-vendor ${activeTab === \"monthly\" ? \"active\" : \"\"}`,\n            onClick: () => setActiveTab(\"monthly\"),\n            children: \"Monthly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `chart-tab-vendor ${activeTab === \"yearly\" ? \"active\" : \"\"}`,\n            onClick: () => setActiveTab(\"yearly\"),\n            children: \"Yearly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-content-vendor\",\n          children: chartData.length > 0 ? /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: chartData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 40,\n                bottom: 40\n              } // Add this line\n              ,\n              children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: activeTab === \"weekly\" ? \"week\" : activeTab === \"monthly\" ? \"month\" : \"year\",\n                angle: -30 // Optional: tilt labels for better fit\n                ,\n                textAnchor: \"end\",\n                interval: 0 // Show all labels\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"sales\",\n                stroke: \"#8884d8\",\n                style: {\n                  marginTop: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: \"17.75rem\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              backgroundColor: \"transparent\",\n              borderRadius: \"12px\",\n              border: \"2px dashed #dee2e6\",\n              color: \"#6c757d\",\n              padding: \"2rem\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n              style: {\n                fontSize: \"3rem\",\n                marginBottom: \"1rem\",\n                color: \"#adb5bd\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: \"0 0 0.5rem 0\",\n                color: \"#495057\"\n              },\n              children: \"No Sales Data Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"0\",\n                textAlign: \"center\",\n                maxWidth: \"300px\"\n              },\n              children: \"Sales data will appear here once you start receiving orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"best-sellers-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Best Sellers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowY: \"scroll\",\n            height: \"300px\",\n            margin: \"8px 0px\"\n          },\n          children: products.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: products.map((product, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.image}`,\n                alt: product.name,\n                onError: e => {\n                  e.target.src = \"placeholder-image-url\"; // Add a placeholder image URL\n                  e.target.onerror = null;\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 21\n              }, this), product.name, \" - E\\xA3 \", product.price, \" (\", product.totalSold, \" \", \"sales)\"]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: \"center\",\n              padding: \"2rem\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              height: \"100%\",\n              color: \"#6c757d\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaShoppingBag, {\n              style: {\n                fontSize: \"2.5rem\",\n                marginBottom: \"1rem\",\n                color: \"#adb5bd\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: \"0 0 0.5rem 0\",\n                color: \"#495057\"\n              },\n              children: \"No Best Sellers Yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: \"0\",\n                maxWidth: \"250px\"\n              },\n              children: \"Your top-performing products will appear here once you start selling\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-lists-vendor\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recent-orders-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            flexDirection: \"row\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Recent Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(BsThreeDotsVertical, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), orders.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Order ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Customer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: orders.slice(0, 5).map(order => {\n              var _order$cartItems$find;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                onClick: () => setSelectedOrder(order),\n                style: {\n                  cursor: \"pointer\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [\" \", ((_order$cartItems$find = order.cartItems.find(item => {\n                    const itemBrandId = item.brandId && typeof item.brandId === \"object\" ? item.brandId._id : item.brandId;\n                    return itemBrandId === vendor.brandId;\n                  })) === null || _order$cartItems$find === void 0 ? void 0 : _order$cartItems$find.name) || \"N/A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: order._id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(order.orderDate).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [order.customerId.firstName, \" \", \"\", order.customerId.lastName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      display: \"inline-block\",\n                      marginTop: \"4px\",\n                      padding: \"4px 12px\",\n                      borderRadius: \"5px\",\n                      backgroundColor: order.orderStatus === \"Pending\" ? \"#f8d7da\" : order.orderStatus === \"Delivered\" ? \"#d4edda\" : \"#FFE5B4\",\n                      color: order.orderStatus === \"Pending\" ? \"#721c24\" : order.orderStatus === \"Delivered\" ? \"#155724\" : \"#FF7518\",\n                      fontWeight: \"500\",\n                      textAlign: \"center\",\n                      minWidth: \"80px\"\n                    },\n                    children: order.orderStatus\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [\"E\\xA3 \", order.total]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)]\n              }, order._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            padding: \"3rem 2rem\",\n            backgroundColor: \"transparent\",\n            borderRadius: \"12px\",\n            margin: \"1rem 0\",\n            border: \"2px dashed #dee2e6\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            minHeight: \"200px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n            style: {\n              fontSize: \"3rem\",\n              marginBottom: \"1rem\",\n              color: \"#adb5bd\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: \"0 0 0.5rem 0\",\n              color: \"#495057\"\n            },\n            children: \"No Orders Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: \"0\",\n              maxWidth: \"300px\",\n              color: \"#6c757d\"\n            },\n            children: \"Orders will appear here once customers start placing orders for your products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardVendor, \"7BZ0M3pdLlfKS3kc9Pk4vgtbbAw=\", false, function () {\n  return [useVendor];\n});\n_c5 = DashboardVendor;\nexport default DashboardVendor;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"OrderDetails$lazy\");\n$RefreshReg$(_c2, \"OrderDetails\");\n$RefreshReg$(_c3, \"LoadingScreen$lazy\");\n$RefreshReg$(_c4, \"LoadingScreen\");\n$RefreshReg$(_c5, \"DashboardVendor\");", "map": {"version": 3, "names": ["React", "lazy", "Suspense", "useState", "useEffect", "SlCalender", "Box", "FaBox", "FaTruck", "FaCheckCircle", "FaRedo", "FaChartLine", "FaShoppingBag", "FaClipboardList", "BsThreeDotsVertical", "useVendor", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "OrderDetails", "_c", "_c2", "LoadingScreen", "_c3", "_c4", "DashboardVendor", "_s", "vendor", "orders", "setOrders", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "products", "setProducts", "isLoading", "setIsLoading", "error", "setError", "totalOrders", "setTotalOrders", "activeOrders", "setActiveOrders", "completedOrders", "setCompletedOrders", "returnedOrders", "setReturnedOrders", "totalSales", "setTotalSales", "confirmedSales", "setConfirmedSales", "deliveredSales", "setDeliveredSales", "returnedSales", "setReturnedSales", "salesPercentageChange", "setSalesPercentageChange", "confirmedSalesPercentageChange", "setConfirmedSalesPercentageChange", "deliveredSalesPercentageChange", "setDeliveredSalesPercentageChange", "returnedSalesPercentageChange", "setReturnedSalesPercentageChange", "activeTab", "setActiveTab", "weeklySales", "setWeeklySales", "monthlySales", "setMonthlySales", "yearlySales", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chartData", "setChartData", "getCurrentDateRange", "today", "Date", "formatDate", "date", "months", "getMonth", "getDate", "getFullYear", "fetchProducts", "brandId", "response", "fetch", "ok", "Error", "data", "json", "console", "fetchOrders", "sortedOrders", "sort", "a", "b", "orderDate", "length", "fetchData", "warn", "totalConfirmed", "totalDelivered", "totalReturned", "toFixed", "fetchSalesData", "aggregateAndSort", "key", "map", "for<PERSON>ach", "item", "Number", "sales", "Object", "entries", "k", "v", "formattedData", "week", "monthNames", "month", "year", "fallback", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "order", "onBack", "className", "style", "textAlign", "padding", "color", "margin", "onClick", "width", "height", "top", "right", "left", "bottom", "dataKey", "angle", "textAnchor", "interval", "type", "stroke", "marginTop", "display", "flexDirection", "alignItems", "justifyContent", "backgroundColor", "borderRadius", "border", "fontSize", "marginBottom", "max<PERSON><PERSON><PERSON>", "overflowY", "product", "index", "src", "image", "alt", "name", "onError", "e", "target", "onerror", "price", "totalSold", "sx", "slice", "_order$cartItems$find", "cursor", "cartItems", "find", "itemBrandId", "_id", "toLocaleDateString", "customerId", "firstName", "lastName", "orderStatus", "fontWeight", "min<PERSON><PERSON><PERSON>", "total", "minHeight", "_c5", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/DashboardVendor.jsx"], "sourcesContent": ["import React, { lazy, Suspense, useState, useEffect } from \"react\";\r\nimport { SlCalender } from \"react-icons/sl\";\r\nimport { Box } from \"@mui/material\";\r\nimport {\r\n  FaBox,\r\n  FaTruck,\r\n  FaCheckCircle,\r\n  FaRedo,\r\n  FaChartLine,\r\n  FaShoppingBag,\r\n  FaClipboardList,\r\n} from \"react-icons/fa\"; // React Icons\r\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\nimport {\r\n  LineChart,\r\n  Line,\r\n  XAxis,\r\n  YAxis,\r\n  Tooltip,\r\n  Legend,\r\n  ResponsiveContainer,\r\n} from \"recharts\";\r\nconst OrderDetails = lazy(() => import(\"./orderDetails\"));\r\nconst LoadingScreen = lazy(() => import(\"../../Pages/loadingScreen\"));\r\n\r\nconst DashboardVendor = () => {\r\n  const { vendor } = useVendor();\r\n  const [orders, setOrders] = useState([]);\r\n  const [selectedOrder, setSelectedOrder] = useState(null); // State for selected order\r\n  const [products, setProducts] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const [totalOrders, setTotalOrders] = useState(0);\r\n  const [activeOrders, setActiveOrders] = useState(0);\r\n  const [completedOrders, setCompletedOrders] = useState(0);\r\n  const [returnedOrders, setReturnedOrders] = useState(0);\r\n\r\n  const [totalSales, setTotalSales] = useState(0);\r\n  const [confirmedSales, setConfirmedSales] = useState(0);\r\n  const [deliveredSales, setDeliveredSales] = useState(0);\r\n  const [returnedSales, setReturnedSales] = useState(0);\r\n\r\n  const [salesPercentageChange, setSalesPercentageChange] = useState(0);\r\n  const [confirmedSalesPercentageChange, setConfirmedSalesPercentageChange] =\r\n    useState(0);\r\n  const [deliveredSalesPercentageChange, setDeliveredSalesPercentageChange] =\r\n    useState(0);\r\n  const [returnedSalesPercentageChange, setReturnedSalesPercentageChange] =\r\n    useState(0);\r\n  const [activeTab, setActiveTab] = useState(\"weekly\");\r\n  const [weeklySales, setWeeklySales] = useState([]);\r\n  const [monthlySales, setMonthlySales] = useState([]);\r\n  const [yearlySales, setYearlySales] = useState([]);\r\n  const [chartData, setChartData] = useState([]);\r\n\r\n  // Add date calculation\r\n  const getCurrentDateRange = () => {\r\n    const today = new Date();\r\n\r\n    const formatDate = (date) => {\r\n      const months = [\r\n        \"Jan\",\r\n        \"Feb\",\r\n        \"Mar\",\r\n        \"Apr\",\r\n        \"May\",\r\n        \"Jun\",\r\n        \"Jul\",\r\n        \"Aug\",\r\n        \"Sep\",\r\n        \"Oct\",\r\n        \"Nov\",\r\n        \"Dec\",\r\n      ];\r\n      return `${\r\n        months[date.getMonth()]\r\n      } ${date.getDate()}, ${date.getFullYear()}`;\r\n    };\r\n\r\n    return formatDate(today);\r\n  };\r\n\r\n  // Fetch best sellers\r\n  useEffect(() => {\r\n    const fetchProducts = async () => {\r\n      if (!vendor?.brandId) {\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/orders/vendor/best-sellers/${vendor.brandId}`\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch best sellers\");\r\n        }\r\n        const data = await response.json();\r\n        setProducts(data || []);\r\n      } catch (error) {\r\n        console.error(\"Error fetching best sellers:\", error);\r\n        setProducts([]);\r\n      }\r\n    };\r\n    fetchProducts();\r\n  }, [vendor]);\r\n\r\n  // Fetch orders\r\n  useEffect(() => {\r\n    const fetchOrders = async () => {\r\n      if (!vendor?.brandId) {\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/orders/orders/brand/${vendor.brandId}`\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch orders\");\r\n        }\r\n        const data = await response.json();\r\n        const sortedOrders = [...data].sort(\r\n          (a, b) => new Date(b.orderDate) - new Date(a.orderDate)\r\n        );\r\n        setOrders(sortedOrders || []);\r\n        setTotalOrders(sortedOrders?.length || 0);\r\n      } catch (error) {\r\n        console.error(\"Error fetching orders:\", error);\r\n        setOrders([]);\r\n        setTotalOrders(0);\r\n      }\r\n    };\r\n\r\n    fetchOrders();\r\n  }, [vendor]);\r\n\r\n  // Fetch statistics\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!vendor?.brandId) {\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n      setIsLoading(true);\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/orders/statistics/${vendor.brandId}`\r\n        );\r\n        const data = await response.json();\r\n\r\n        if (data.error) {\r\n          console.warn(\"No orders found for this brand.\");\r\n          // Set default values when no orders exist\r\n          setTotalOrders(0);\r\n          setActiveOrders(0);\r\n          setCompletedOrders(0);\r\n          setReturnedOrders(0);\r\n          setTotalSales(0);\r\n          setConfirmedSales(0);\r\n          setDeliveredSales(0);\r\n          setReturnedSales(0);\r\n          setSalesPercentageChange(0);\r\n          setConfirmedSalesPercentageChange(0);\r\n          setDeliveredSalesPercentageChange(0);\r\n          setReturnedSalesPercentageChange(0);\r\n        } else {\r\n          // Set statistics from data\r\n          setTotalOrders(data.totalOrders || 0);\r\n          setActiveOrders(data.totalConfirmed || 0);\r\n          setCompletedOrders(data.totalDelivered || 0);\r\n          setReturnedOrders(data.totalReturned || 0);\r\n          setTotalSales(data.totalSales || 0);\r\n          setConfirmedSales(data.confirmedSales || 0);\r\n          setDeliveredSales(data.deliveredSales || 0);\r\n          setReturnedSales(data.returnedSales || 0);\r\n\r\n          // Calculate percentages safely\r\n          setSalesPercentageChange(\r\n            data.totalSales > 0\r\n              ? ((data.deliveredSales / data.totalSales) * 100).toFixed(2)\r\n              : 0\r\n          );\r\n          setConfirmedSalesPercentageChange(\r\n            data.totalSales > 0\r\n              ? ((data.confirmedSales / data.totalSales) * 100).toFixed(2)\r\n              : 0\r\n          );\r\n          setDeliveredSalesPercentageChange(\r\n            data.totalSales > 0\r\n              ? ((data.deliveredSales / data.totalSales) * 100).toFixed(2)\r\n              : 0\r\n          );\r\n          setReturnedSalesPercentageChange(\r\n            data.totalSales > 0\r\n              ? ((data.returnedSales / data.totalSales) * 100).toFixed(2)\r\n              : 0\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching statistics:\", error);\r\n        setError(\"Failed to load statistics\");\r\n        // Set default values on error\r\n        setTotalOrders(0);\r\n        setActiveOrders(0);\r\n        setCompletedOrders(0);\r\n        setReturnedOrders(0);\r\n        setTotalSales(0);\r\n        setConfirmedSales(0);\r\n        setDeliveredSales(0);\r\n        setReturnedSales(0);\r\n        setSalesPercentageChange(0);\r\n        setConfirmedSalesPercentageChange(0);\r\n        setDeliveredSalesPercentageChange(0);\r\n        setReturnedSalesPercentageChange(0);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [vendor]);\r\n\r\n  useEffect(() => {\r\n    const fetchSalesData = async () => {\r\n      if (!vendor?.brandId) return;\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/orders/sales-graph/${vendor.brandId}`\r\n        );\r\n        const data = await response.json();\r\n\r\n        // Set default empty arrays if no data\r\n        setWeeklySales(data.weeklySales || []);\r\n        setMonthlySales(data.monthlySales || []);\r\n        setYearlySales(data.yearlySales || []);\r\n      } catch (error) {\r\n        console.error(\"Error fetching sales graph data:\", error);\r\n        setWeeklySales([]);\r\n        setMonthlySales([]);\r\n        setYearlySales([]);\r\n      }\r\n    };\r\n\r\n    fetchSalesData();\r\n  }, [vendor]);\r\n\r\n  useEffect(() => {\r\n    function aggregateAndSort(data, key) {\r\n      // Aggregate sales for duplicate keys\r\n      const map = {};\r\n      data.forEach((item) => {\r\n        if (!map[item[key]]) map[item[key]] = 0;\r\n        map[item[key]] += Number(item.sales) || 0;\r\n      });\r\n      // Convert to array and sort\r\n      return Object.entries(map)\r\n        .map(([k, v]) => ({ [key]: Number(k), sales: v }))\r\n        .sort((a, b) => a[key] - b[key]);\r\n    }\r\n\r\n    let formattedData = [];\r\n    switch (activeTab) {\r\n      case \"weekly\":\r\n        formattedData = aggregateAndSort(weeklySales, \"week\").map((item) => ({\r\n          ...item,\r\n          week: `Week ${item.week}`,\r\n        }));\r\n        break;\r\n      case \"monthly\":\r\n        const monthNames = [\r\n          \"Jan\",\r\n          \"Feb\",\r\n          \"Mar\",\r\n          \"Apr\",\r\n          \"May\",\r\n          \"Jun\",\r\n          \"Jul\",\r\n          \"Aug\",\r\n          \"Sep\",\r\n          \"Oct\",\r\n          \"Nov\",\r\n          \"Dec\",\r\n        ];\r\n        formattedData = aggregateAndSort(monthlySales, \"month\").map((item) => ({\r\n          ...item,\r\n          month: monthNames[item.month - 1] || `Month ${item.month}`,\r\n        }));\r\n        break;\r\n      case \"yearly\":\r\n        formattedData = aggregateAndSort(yearlySales, \"year\").map((item) => ({\r\n          ...item,\r\n          year: `${item.year}`,\r\n        }));\r\n        break;\r\n      default:\r\n        formattedData = [];\r\n    }\r\n    setChartData(formattedData);\r\n  }, [activeTab, weeklySales, monthlySales, yearlySales]);\r\n\r\n  if (selectedOrder) {\r\n    return (\r\n      <Suspense fallback={<div>Loading...</div>}>\r\n        <OrderDetails\r\n          order={selectedOrder}\r\n          onBack={() => setSelectedOrder(null)}\r\n        />\r\n      </Suspense>\r\n    );\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Suspense fallback={<div>Loading...</div>}>\r\n        <LoadingScreen />\r\n      </Suspense>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div\r\n        className=\"dashboard-vendor\"\r\n        style={{ textAlign: \"center\", padding: \"2rem\", color: \"red\" }}\r\n      >\r\n        {error}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"dashboard-vendor\">\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <h2>Dashboard</h2>\r\n          <p>Home &gt; Dashboard</p>\r\n        </div>\r\n        <div className=\"dashboard-date-vendor\">\r\n          <SlCalender />\r\n          <span>{getCurrentDateRange()}</span>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Overview Section */}\r\n      <section className=\"dashboard-overview-vendor\">\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaBox />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Total Sales</h3>\r\n            <p>E£ {totalSales}</p>\r\n            <h3>Total orders</h3>\r\n            <p>{totalOrders}</p>\r\n            <span>▲ {salesPercentageChange}% Compared to Last Month</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaTruck />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Active Orders sales</h3>\r\n            <p>E£ {confirmedSales}</p>\r\n            <h3>Active Orders </h3>\r\n            <p> {activeOrders}</p>\r\n            <span>\r\n              ▲ {confirmedSalesPercentageChange}% Compared to Last Month\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaCheckCircle />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Completed Orders sales</h3>\r\n            <p>E£ {deliveredSales}</p>\r\n            <h3>Completed Orders</h3>\r\n            <p> {completedOrders}</p>\r\n            <span>\r\n              ▲ {deliveredSalesPercentageChange}% Compared to Last Month\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaRedo />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Return Orders sales</h3>\r\n            <p>E£ {returnedSales}</p>\r\n            <h3>Returned Orders</h3>\r\n            <p>{returnedOrders}</p>\r\n            <span>\r\n              ▲ {returnedSalesPercentageChange}% Compared to Last Month\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* Sales Chart Section */}\r\n      <section className=\"dashboard-chart-vendor\">\r\n        <div className=\"chart-header-vendor\">\r\n          <h3 style={{ margin: \"10px auto\" }}>Sales Graph</h3>\r\n          <div className=\"chart-tabs-vendor\">\r\n            <button\r\n              className={`chart-tab-vendor ${\r\n                activeTab === \"weekly\" ? \"active\" : \"\"\r\n              }`}\r\n              onClick={() => setActiveTab(\"weekly\")}\r\n            >\r\n              Weekly\r\n            </button>\r\n            <button\r\n              className={`chart-tab-vendor ${\r\n                activeTab === \"monthly\" ? \"active\" : \"\"\r\n              }`}\r\n              onClick={() => setActiveTab(\"monthly\")}\r\n            >\r\n              Monthly\r\n            </button>\r\n            <button\r\n              className={`chart-tab-vendor ${\r\n                activeTab === \"yearly\" ? \"active\" : \"\"\r\n              }`}\r\n              onClick={() => setActiveTab(\"yearly\")}\r\n            >\r\n              Yearly\r\n            </button>\r\n          </div>\r\n          <div className=\"chart-content-vendor\">\r\n            {chartData.length > 0 ? (\r\n              <ResponsiveContainer width=\"100%\" height={300}>\r\n                <LineChart\r\n                  data={chartData}\r\n                  margin={{ top: 20, right: 30, left: 40, bottom: 40 }} // Add this line\r\n                >\r\n                  <XAxis\r\n                    dataKey={\r\n                      activeTab === \"weekly\"\r\n                        ? \"week\"\r\n                        : activeTab === \"monthly\"\r\n                        ? \"month\"\r\n                        : \"year\"\r\n                    }\r\n                    angle={-30} // Optional: tilt labels for better fit\r\n                    textAnchor=\"end\"\r\n                    interval={0} // Show all labels\r\n                  />\r\n                  <YAxis />\r\n                  <Tooltip />\r\n                  <Legend />\r\n                  <Line\r\n                    type=\"monotone\"\r\n                    dataKey=\"sales\"\r\n                    stroke=\"#8884d8\"\r\n                    style={{ marginTop: \"20px\" }}\r\n                  />\r\n                </LineChart>\r\n              </ResponsiveContainer>\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  height: \"17.75rem\",\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  backgroundColor: \"transparent\",\r\n                  borderRadius: \"12px\",\r\n                  border: \"2px dashed #dee2e6\",\r\n                  color: \"#6c757d\",\r\n                  padding: \"2rem\",\r\n                }}\r\n              >\r\n                <FaChartLine\r\n                  style={{\r\n                    fontSize: \"3rem\",\r\n                    marginBottom: \"1rem\",\r\n                    color: \"#adb5bd\",\r\n                  }}\r\n                />\r\n                <h4 style={{ margin: \"0 0 0.5rem 0\", color: \"#495057\" }}>\r\n                  No Sales Data Available\r\n                </h4>\r\n                <p\r\n                  style={{\r\n                    margin: \"0\",\r\n                    textAlign: \"center\",\r\n                    maxWidth: \"300px\",\r\n                  }}\r\n                >\r\n                  Sales data will appear here once you start receiving orders\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"best-sellers-vendor\">\r\n          <h3>Best Sellers</h3>\r\n          <hr />\r\n          <div\r\n            style={{\r\n              overflowY: \"scroll\",\r\n              height: \"300px\",\r\n              margin: \"8px 0px\",\r\n            }}\r\n          >\r\n            {products.length > 0 ? (\r\n              <ul>\r\n                {products.map((product, index) => (\r\n                  <li key={index}>\r\n                    <img\r\n                      src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.image}`}\r\n                      alt={product.name}\r\n                      onError={(e) => {\r\n                        e.target.src = \"placeholder-image-url\"; // Add a placeholder image URL\r\n                        e.target.onerror = null;\r\n                      }}\r\n                    />\r\n                    {product.name} - E£ {product.price} ({product.totalSold}{\" \"}\r\n                    sales)\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  textAlign: \"center\",\r\n                  padding: \"2rem\",\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  height: \"100%\",\r\n                  color: \"#6c757d\",\r\n                }}\r\n              >\r\n                <FaShoppingBag\r\n                  style={{\r\n                    fontSize: \"2.5rem\",\r\n                    marginBottom: \"1rem\",\r\n                    color: \"#adb5bd\",\r\n                  }}\r\n                />\r\n                <h4 style={{ margin: \"0 0 0.5rem 0\", color: \"#495057\" }}>\r\n                  No Best Sellers Yet\r\n                </h4>\r\n                <p style={{ margin: \"0\", maxWidth: \"250px\" }}>\r\n                  Your top-performing products will appear here once you start\r\n                  selling\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Recent Orders & Best Sellers */}\r\n      <section className=\"dashboard-lists-vendor\">\r\n        <div className=\"recent-orders-vendor\">\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              flexDirection: \"row\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <h3>Recent Orders</h3>\r\n            <BsThreeDotsVertical />\r\n          </Box>\r\n          {orders.length > 0 ? (\r\n            <table>\r\n              <thead>\r\n                <tr>\r\n                  <th>Product</th>\r\n                  <th>Order ID</th>\r\n                  <th>Date</th>\r\n                  <th>Customer Name</th>\r\n                  <th>Status</th>\r\n                  <th>Amount</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {orders.slice(0, 5).map((order) => (\r\n                  <tr\r\n                    key={order._id}\r\n                    onClick={() => setSelectedOrder(order)}\r\n                    style={{ cursor: \"pointer\" }}\r\n                  >\r\n                    <td>\r\n                      {\" \"}\r\n                      {order.cartItems.find((item) => {\r\n                        const itemBrandId =\r\n                          item.brandId && typeof item.brandId === \"object\"\r\n                            ? item.brandId._id\r\n                            : item.brandId;\r\n                        return itemBrandId === vendor.brandId;\r\n                      })?.name || \"N/A\"}\r\n                    </td>\r\n                    <td>{order._id}</td>\r\n                    <td>{new Date(order.orderDate).toLocaleDateString()}</td>\r\n                    <td>\r\n                      {order.customerId.firstName} {\"\"}\r\n                      {order.customerId.lastName}\r\n                    </td>\r\n                    <td>\r\n                      <span\r\n                        style={{\r\n                          display: \"inline-block\",\r\n                          marginTop: \"4px\",\r\n                          padding: \"4px 12px\",\r\n                          borderRadius: \"5px\",\r\n                          backgroundColor:\r\n                            order.orderStatus === \"Pending\"\r\n                              ? \"#f8d7da\"\r\n                              : order.orderStatus === \"Delivered\"\r\n                              ? \"#d4edda\"\r\n                              : \"#FFE5B4\",\r\n                          color:\r\n                            order.orderStatus === \"Pending\"\r\n                              ? \"#721c24\"\r\n                              : order.orderStatus === \"Delivered\"\r\n                              ? \"#155724\"\r\n                              : \"#FF7518\",\r\n                          fontWeight: \"500\",\r\n                          textAlign: \"center\",\r\n                          minWidth: \"80px\",\r\n                        }}\r\n                      >\r\n                        {order.orderStatus}\r\n                      </span>\r\n                    </td>\r\n                    <td>E£ {order.total}</td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          ) : (\r\n            <div\r\n              style={{\r\n                textAlign: \"center\",\r\n                padding: \"3rem 2rem\",\r\n                backgroundColor: \"transparent\",\r\n                borderRadius: \"12px\",\r\n                margin: \"1rem 0\",\r\n                border: \"2px dashed #dee2e6\",\r\n                display: \"flex\",\r\n                flexDirection: \"column\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\",\r\n                minHeight: \"200px\",\r\n              }}\r\n            >\r\n              <FaClipboardList\r\n                style={{\r\n                  fontSize: \"3rem\",\r\n                  marginBottom: \"1rem\",\r\n                  color: \"#adb5bd\",\r\n                }}\r\n              />\r\n              <h4 style={{ margin: \"0 0 0.5rem 0\", color: \"#495057\" }}>\r\n                No Orders Available\r\n              </h4>\r\n              <p style={{ margin: \"0\", maxWidth: \"300px\", color: \"#6c757d\" }}>\r\n                Orders will appear here once customers start placing orders for\r\n                your products\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardVendor;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClE,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,GAAG,QAAQ,eAAe;AACnC,SACEC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,eAAe,QACV,gBAAgB,CAAC,CAAC;AACzB,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,mBAAmB,QACd,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAClB,MAAMC,YAAY,gBAAGxB,IAAI,CAAAyB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAACC,GAAA,GAApDF,YAAY;AAClB,MAAMG,aAAa,gBAAG3B,IAAI,CAAA4B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,GAAA,GAAhEF,aAAa;AAEnB,MAAMG,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAO,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC9B,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC;EAErD,MAAM,CAACyD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC2D,8BAA8B,EAAEC,iCAAiC,CAAC,GACvE5D,QAAQ,CAAC,CAAC,CAAC;EACb,MAAM,CAAC6D,8BAA8B,EAAEC,iCAAiC,CAAC,GACvE9D,QAAQ,CAAC,CAAC,CAAC;EACb,MAAM,CAAC+D,6BAA6B,EAAEC,gCAAgC,CAAC,GACrEhE,QAAQ,CAAC,CAAC,CAAC;EACb,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM2E,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IAExB,MAAMC,UAAU,GAAIC,IAAI,IAAK;MAC3B,MAAMC,MAAM,GAAG,CACb,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;MACD,OAAO,GACLA,MAAM,CAACD,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,IACrBF,IAAI,CAACG,OAAO,CAAC,CAAC,KAAKH,IAAI,CAACI,WAAW,CAAC,CAAC,EAAE;IAC7C,CAAC;IAED,OAAOL,UAAU,CAACF,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA3E,SAAS,CAAC,MAAM;IACd,MAAMmF,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI,EAACtD,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEuD,OAAO,GAAE;QACpB/C,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;MACA,IAAI;QACF,MAAMgD,QAAQ,GAAG,MAAMC,KAAK,CAC1B,gEAAgEzD,MAAM,CAACuD,OAAO,EAChF,CAAC;QACD,IAAI,CAACC,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;QACjD;QACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCvD,WAAW,CAACsD,IAAI,IAAI,EAAE,CAAC;MACzB,CAAC,CAAC,OAAOnD,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDH,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC;IACDgD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACtD,MAAM,CAAC,CAAC;;EAEZ;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM4F,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,EAAC/D,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEuD,OAAO,GAAE;QACpB/C,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;MACA,IAAI;QACF,MAAMgD,QAAQ,GAAG,MAAMC,KAAK,CAC1B,yDAAyDzD,MAAM,CAACuD,OAAO,EACzE,CAAC;QACD,IAAI,CAACC,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClC,MAAMG,YAAY,GAAG,CAAC,GAAGJ,IAAI,CAAC,CAACK,IAAI,CACjC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIpB,IAAI,CAACoB,CAAC,CAACC,SAAS,CAAC,GAAG,IAAIrB,IAAI,CAACmB,CAAC,CAACE,SAAS,CACxD,CAAC;QACDlE,SAAS,CAAC8D,YAAY,IAAI,EAAE,CAAC;QAC7BpD,cAAc,CAAC,CAAAoD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEK,MAAM,KAAI,CAAC,CAAC;MAC3C,CAAC,CAAC,OAAO5D,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CP,SAAS,CAAC,EAAE,CAAC;QACbU,cAAc,CAAC,CAAC,CAAC;MACnB;IACF,CAAC;IAEDmD,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC/D,MAAM,CAAC,CAAC;;EAEZ;EACA7B,SAAS,CAAC,MAAM;IACd,MAAMmG,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,EAACtE,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEuD,OAAO,GAAE;QACpB/C,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;MACAA,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QACF,MAAMgD,QAAQ,GAAG,MAAMC,KAAK,CAC1B,uDAAuDzD,MAAM,CAACuD,OAAO,EACvE,CAAC;QACD,MAAMK,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACnD,KAAK,EAAE;UACdqD,OAAO,CAACS,IAAI,CAAC,iCAAiC,CAAC;UAC/C;UACA3D,cAAc,CAAC,CAAC,CAAC;UACjBE,eAAe,CAAC,CAAC,CAAC;UAClBE,kBAAkB,CAAC,CAAC,CAAC;UACrBE,iBAAiB,CAAC,CAAC,CAAC;UACpBE,aAAa,CAAC,CAAC,CAAC;UAChBE,iBAAiB,CAAC,CAAC,CAAC;UACpBE,iBAAiB,CAAC,CAAC,CAAC;UACpBE,gBAAgB,CAAC,CAAC,CAAC;UACnBE,wBAAwB,CAAC,CAAC,CAAC;UAC3BE,iCAAiC,CAAC,CAAC,CAAC;UACpCE,iCAAiC,CAAC,CAAC,CAAC;UACpCE,gCAAgC,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM;UACL;UACAtB,cAAc,CAACgD,IAAI,CAACjD,WAAW,IAAI,CAAC,CAAC;UACrCG,eAAe,CAAC8C,IAAI,CAACY,cAAc,IAAI,CAAC,CAAC;UACzCxD,kBAAkB,CAAC4C,IAAI,CAACa,cAAc,IAAI,CAAC,CAAC;UAC5CvD,iBAAiB,CAAC0C,IAAI,CAACc,aAAa,IAAI,CAAC,CAAC;UAC1CtD,aAAa,CAACwC,IAAI,CAACzC,UAAU,IAAI,CAAC,CAAC;UACnCG,iBAAiB,CAACsC,IAAI,CAACvC,cAAc,IAAI,CAAC,CAAC;UAC3CG,iBAAiB,CAACoC,IAAI,CAACrC,cAAc,IAAI,CAAC,CAAC;UAC3CG,gBAAgB,CAACkC,IAAI,CAACnC,aAAa,IAAI,CAAC,CAAC;;UAEzC;UACAG,wBAAwB,CACtBgC,IAAI,CAACzC,UAAU,GAAG,CAAC,GACf,CAAEyC,IAAI,CAACrC,cAAc,GAAGqC,IAAI,CAACzC,UAAU,GAAI,GAAG,EAAEwD,OAAO,CAAC,CAAC,CAAC,GAC1D,CACN,CAAC;UACD7C,iCAAiC,CAC/B8B,IAAI,CAACzC,UAAU,GAAG,CAAC,GACf,CAAEyC,IAAI,CAACvC,cAAc,GAAGuC,IAAI,CAACzC,UAAU,GAAI,GAAG,EAAEwD,OAAO,CAAC,CAAC,CAAC,GAC1D,CACN,CAAC;UACD3C,iCAAiC,CAC/B4B,IAAI,CAACzC,UAAU,GAAG,CAAC,GACf,CAAEyC,IAAI,CAACrC,cAAc,GAAGqC,IAAI,CAACzC,UAAU,GAAI,GAAG,EAAEwD,OAAO,CAAC,CAAC,CAAC,GAC1D,CACN,CAAC;UACDzC,gCAAgC,CAC9B0B,IAAI,CAACzC,UAAU,GAAG,CAAC,GACf,CAAEyC,IAAI,CAACnC,aAAa,GAAGmC,IAAI,CAACzC,UAAU,GAAI,GAAG,EAAEwD,OAAO,CAAC,CAAC,CAAC,GACzD,CACN,CAAC;QACH;MACF,CAAC,CAAC,OAAOlE,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDC,QAAQ,CAAC,2BAA2B,CAAC;QACrC;QACAE,cAAc,CAAC,CAAC,CAAC;QACjBE,eAAe,CAAC,CAAC,CAAC;QAClBE,kBAAkB,CAAC,CAAC,CAAC;QACrBE,iBAAiB,CAAC,CAAC,CAAC;QACpBE,aAAa,CAAC,CAAC,CAAC;QAChBE,iBAAiB,CAAC,CAAC,CAAC;QACpBE,iBAAiB,CAAC,CAAC,CAAC;QACpBE,gBAAgB,CAAC,CAAC,CAAC;QACnBE,wBAAwB,CAAC,CAAC,CAAC;QAC3BE,iCAAiC,CAAC,CAAC,CAAC;QACpCE,iCAAiC,CAAC,CAAC,CAAC;QACpCE,gCAAgC,CAAC,CAAC,CAAC;MACrC,CAAC,SAAS;QACR1B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAED8D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACtE,MAAM,CAAC,CAAC;EAEZ7B,SAAS,CAAC,MAAM;IACd,MAAMyG,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,EAAC5E,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEuD,OAAO,GAAE;MAEtB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,wDAAwDzD,MAAM,CAACuD,OAAO,EACxE,CAAC;QACD,MAAMK,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;;QAElC;QACAvB,cAAc,CAACsB,IAAI,CAACvB,WAAW,IAAI,EAAE,CAAC;QACtCG,eAAe,CAACoB,IAAI,CAACrB,YAAY,IAAI,EAAE,CAAC;QACxCG,cAAc,CAACkB,IAAI,CAACnB,WAAW,IAAI,EAAE,CAAC;MACxC,CAAC,CAAC,OAAOhC,KAAK,EAAE;QACdqD,OAAO,CAACrD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD6B,cAAc,CAAC,EAAE,CAAC;QAClBE,eAAe,CAAC,EAAE,CAAC;QACnBE,cAAc,CAAC,EAAE,CAAC;MACpB;IACF,CAAC;IAEDkC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5E,MAAM,CAAC,CAAC;EAEZ7B,SAAS,CAAC,MAAM;IACd,SAAS0G,gBAAgBA,CAACjB,IAAI,EAAEkB,GAAG,EAAE;MACnC;MACA,MAAMC,GAAG,GAAG,CAAC,CAAC;MACdnB,IAAI,CAACoB,OAAO,CAAEC,IAAI,IAAK;QACrB,IAAI,CAACF,GAAG,CAACE,IAAI,CAACH,GAAG,CAAC,CAAC,EAAEC,GAAG,CAACE,IAAI,CAACH,GAAG,CAAC,CAAC,GAAG,CAAC;QACvCC,GAAG,CAACE,IAAI,CAACH,GAAG,CAAC,CAAC,IAAII,MAAM,CAACD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC;MAC3C,CAAC,CAAC;MACF;MACA,OAAOC,MAAM,CAACC,OAAO,CAACN,GAAG,CAAC,CACvBA,GAAG,CAAC,CAAC,CAACO,CAAC,EAAEC,CAAC,CAAC,MAAM;QAAE,CAACT,GAAG,GAAGI,MAAM,CAACI,CAAC,CAAC;QAAEH,KAAK,EAAEI;MAAE,CAAC,CAAC,CAAC,CACjDtB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACY,GAAG,CAAC,GAAGX,CAAC,CAACW,GAAG,CAAC,CAAC;IACpC;IAEA,IAAIU,aAAa,GAAG,EAAE;IACtB,QAAQrD,SAAS;MACf,KAAK,QAAQ;QACXqD,aAAa,GAAGX,gBAAgB,CAACxC,WAAW,EAAE,MAAM,CAAC,CAAC0C,GAAG,CAAEE,IAAI,KAAM;UACnE,GAAGA,IAAI;UACPQ,IAAI,EAAE,QAAQR,IAAI,CAACQ,IAAI;QACzB,CAAC,CAAC,CAAC;QACH;MACF,KAAK,SAAS;QACZ,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;QACDF,aAAa,GAAGX,gBAAgB,CAACtC,YAAY,EAAE,OAAO,CAAC,CAACwC,GAAG,CAAEE,IAAI,KAAM;UACrE,GAAGA,IAAI;UACPU,KAAK,EAAED,UAAU,CAACT,IAAI,CAACU,KAAK,GAAG,CAAC,CAAC,IAAI,SAASV,IAAI,CAACU,KAAK;QAC1D,CAAC,CAAC,CAAC;QACH;MACF,KAAK,QAAQ;QACXH,aAAa,GAAGX,gBAAgB,CAACpC,WAAW,EAAE,MAAM,CAAC,CAACsC,GAAG,CAAEE,IAAI,KAAM;UACnE,GAAGA,IAAI;UACPW,IAAI,EAAE,GAAGX,IAAI,CAACW,IAAI;QACpB,CAAC,CAAC,CAAC;QACH;MACF;QACEJ,aAAa,GAAG,EAAE;IACtB;IACA5C,YAAY,CAAC4C,aAAa,CAAC;EAC7B,CAAC,EAAE,CAACrD,SAAS,EAAEE,WAAW,EAAEE,YAAY,EAAEE,WAAW,CAAC,CAAC;EAEvD,IAAItC,aAAa,EAAE;IACjB,oBACEZ,OAAA,CAACtB,QAAQ;MAAC4H,QAAQ,eAAEtG,OAAA;QAAAuG,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eACxCvG,OAAA,CAACC,YAAY;QACX2G,KAAK,EAAEhG,aAAc;QACrBiG,MAAM,EAAEA,CAAA,KAAMhG,gBAAgB,CAAC,IAAI;MAAE;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEf;EAEA,IAAI3F,SAAS,EAAE;IACb,oBACEhB,OAAA,CAACtB,QAAQ;MAAC4H,QAAQ,eAAEtG,OAAA;QAAAuG,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eACxCvG,OAAA,CAACI,aAAa;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEf;EAEA,IAAIzF,KAAK,EAAE;IACT,oBACElB,OAAA;MACE8G,SAAS,EAAC,kBAAkB;MAC5BC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAM,CAAE;MAAAX,QAAA,EAE7DrF;IAAK;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3G,OAAA;IAAK8G,SAAS,EAAC,kBAAkB;IAAAP,QAAA,gBAC/BvG,OAAA;MAAQ8G,SAAS,EAAC,yBAAyB;MAAAP,QAAA,gBACzCvG,OAAA;QAAK8G,SAAS,EAAC,wBAAwB;QAAAP,QAAA,gBACrCvG,OAAA;UAAAuG,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB3G,OAAA;UAAAuG,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACN3G,OAAA;QAAK8G,SAAS,EAAC,uBAAuB;QAAAP,QAAA,gBACpCvG,OAAA,CAACnB,UAAU;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACd3G,OAAA;UAAAuG,QAAA,EAAOjD,mBAAmB,CAAC;QAAC;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT3G,OAAA;MAAS8G,SAAS,EAAC,2BAA2B;MAAAP,QAAA,gBAC5CvG,OAAA;QAAK8G,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnCvG,OAAA;UAAK8G,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/BvG,OAAA,CAACjB,KAAK;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN3G,OAAA;UAAK8G,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClCvG,OAAA;YAAAuG,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB3G,OAAA;YAAAuG,QAAA,GAAG,QAAG,EAAC3E,UAAU;UAAA;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB3G,OAAA;YAAAuG,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB3G,OAAA;YAAAuG,QAAA,EAAInF;UAAW;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB3G,OAAA;YAAAuG,QAAA,GAAM,SAAE,EAACnE,qBAAqB,EAAC,0BAAwB;UAAA;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3G,OAAA;QAAK8G,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnCvG,OAAA;UAAK8G,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/BvG,OAAA,CAAChB,OAAO;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACN3G,OAAA;UAAK8G,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClCvG,OAAA;YAAAuG,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B3G,OAAA;YAAAuG,QAAA,GAAG,QAAG,EAACzE,cAAc;UAAA;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B3G,OAAA;YAAAuG,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB3G,OAAA;YAAAuG,QAAA,GAAG,GAAC,EAACjF,YAAY;UAAA;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB3G,OAAA;YAAAuG,QAAA,GAAM,SACF,EAACjE,8BAA8B,EAAC,0BACpC;UAAA;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3G,OAAA;QAAK8G,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnCvG,OAAA;UAAK8G,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/BvG,OAAA,CAACf,aAAa;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACN3G,OAAA;UAAK8G,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClCvG,OAAA;YAAAuG,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/B3G,OAAA;YAAAuG,QAAA,GAAG,QAAG,EAACvE,cAAc;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B3G,OAAA;YAAAuG,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB3G,OAAA;YAAAuG,QAAA,GAAG,GAAC,EAAC/E,eAAe;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB3G,OAAA;YAAAuG,QAAA,GAAM,SACF,EAAC/D,8BAA8B,EAAC,0BACpC;UAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3G,OAAA;QAAK8G,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnCvG,OAAA;UAAK8G,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/BvG,OAAA,CAACd,MAAM;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACN3G,OAAA;UAAK8G,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClCvG,OAAA;YAAAuG,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B3G,OAAA;YAAAuG,QAAA,GAAG,QAAG,EAACrE,aAAa;UAAA;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB3G,OAAA;YAAAuG,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB3G,OAAA;YAAAuG,QAAA,EAAI7E;UAAc;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB3G,OAAA;YAAAuG,QAAA,GAAM,SACF,EAAC7D,6BAA6B,EAAC,0BACnC;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV3G,OAAA;MAAS8G,SAAS,EAAC,wBAAwB;MAAAP,QAAA,gBACzCvG,OAAA;QAAK8G,SAAS,EAAC,qBAAqB;QAAAP,QAAA,gBAClCvG,OAAA;UAAI+G,KAAK,EAAE;YAAEI,MAAM,EAAE;UAAY,CAAE;UAAAZ,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD3G,OAAA;UAAK8G,SAAS,EAAC,mBAAmB;UAAAP,QAAA,gBAChCvG,OAAA;YACE8G,SAAS,EAAE,oBACTlE,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;YACHwE,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,QAAQ,CAAE;YAAA0D,QAAA,EACvC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YACE8G,SAAS,EAAE,oBACTlE,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EACtC;YACHwE,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,SAAS,CAAE;YAAA0D,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YACE8G,SAAS,EAAE,oBACTlE,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EACrC;YACHwE,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,QAAQ,CAAE;YAAA0D,QAAA,EACvC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN3G,OAAA;UAAK8G,SAAS,EAAC,sBAAsB;UAAAP,QAAA,EAClCnD,SAAS,CAAC0B,MAAM,GAAG,CAAC,gBACnB9E,OAAA,CAACF,mBAAmB;YAACuH,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAf,QAAA,eAC5CvG,OAAA,CAACR,SAAS;cACR6E,IAAI,EAAEjB,SAAU;cAChB+D,MAAM,EAAE;gBAAEI,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAG,CAAE,CAAC;cAAA;cAAAnB,QAAA,gBAEtDvG,OAAA,CAACN,KAAK;gBACJiI,OAAO,EACL/E,SAAS,KAAK,QAAQ,GAClB,MAAM,GACNA,SAAS,KAAK,SAAS,GACvB,OAAO,GACP,MACL;gBACDgF,KAAK,EAAE,CAAC,EAAG,CAAC;gBAAA;gBACZC,UAAU,EAAC,KAAK;gBAChBC,QAAQ,EAAE,CAAE,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACF3G,OAAA,CAACL,KAAK;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT3G,OAAA,CAACJ,OAAO;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX3G,OAAA,CAACH,MAAM;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV3G,OAAA,CAACP,IAAI;gBACHsI,IAAI,EAAC,UAAU;gBACfJ,OAAO,EAAC,OAAO;gBACfK,MAAM,EAAC,SAAS;gBAChBjB,KAAK,EAAE;kBAAEkB,SAAS,EAAE;gBAAO;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,gBAEtB3G,OAAA;YACE+G,KAAK,EAAE;cACLO,MAAM,EAAE,UAAU;cAClBY,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,eAAe,EAAE,aAAa;cAC9BC,YAAY,EAAE,MAAM;cACpBC,MAAM,EAAE,oBAAoB;cAC5BtB,KAAK,EAAE,SAAS;cAChBD,OAAO,EAAE;YACX,CAAE;YAAAV,QAAA,gBAEFvG,OAAA,CAACb,WAAW;cACV4H,KAAK,EAAE;gBACL0B,QAAQ,EAAE,MAAM;gBAChBC,YAAY,EAAE,MAAM;gBACpBxB,KAAK,EAAE;cACT;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF3G,OAAA;cAAI+G,KAAK,EAAE;gBAAEI,MAAM,EAAE,cAAc;gBAAED,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3G,OAAA;cACE+G,KAAK,EAAE;gBACLI,MAAM,EAAE,GAAG;gBACXH,SAAS,EAAE,QAAQ;gBACnB2B,QAAQ,EAAE;cACZ,CAAE;cAAApC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3G,OAAA;QAAK8G,SAAS,EAAC,qBAAqB;QAAAP,QAAA,gBAClCvG,OAAA;UAAAuG,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB3G,OAAA;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3G,OAAA;UACE+G,KAAK,EAAE;YACL6B,SAAS,EAAE,QAAQ;YACnBtB,MAAM,EAAE,OAAO;YACfH,MAAM,EAAE;UACV,CAAE;UAAAZ,QAAA,EAEDzF,QAAQ,CAACgE,MAAM,GAAG,CAAC,gBAClB9E,OAAA;YAAAuG,QAAA,EACGzF,QAAQ,CAAC0E,GAAG,CAAC,CAACqD,OAAO,EAAEC,KAAK,kBAC3B9I,OAAA;cAAAuG,QAAA,gBACEvG,OAAA;gBACE+I,GAAG,EAAE,uDAAuDF,OAAO,CAACG,KAAK,EAAG;gBAC5EC,GAAG,EAAEJ,OAAO,CAACK,IAAK;gBAClBC,OAAO,EAAGC,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,uBAAuB,CAAC,CAAC;kBACxCK,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;gBACzB;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDkC,OAAO,CAACK,IAAI,EAAC,WAAM,EAACL,OAAO,CAACU,KAAK,EAAC,IAAE,EAACV,OAAO,CAACW,SAAS,EAAE,GAAG,EAAC,QAE/D;YAAA,GAXSV,KAAK;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,gBAEL3G,OAAA;YACE+G,KAAK,EAAE;cACLC,SAAS,EAAE,QAAQ;cACnBC,OAAO,EAAE,MAAM;cACfiB,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBf,MAAM,EAAE,MAAM;cACdJ,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,gBAEFvG,OAAA,CAACZ,aAAa;cACZ2H,KAAK,EAAE;gBACL0B,QAAQ,EAAE,QAAQ;gBAClBC,YAAY,EAAE,MAAM;gBACpBxB,KAAK,EAAE;cACT;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF3G,OAAA;cAAI+G,KAAK,EAAE;gBAAEI,MAAM,EAAE,cAAc;gBAAED,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3G,OAAA;cAAG+G,KAAK,EAAE;gBAAEI,MAAM,EAAE,GAAG;gBAAEwB,QAAQ,EAAE;cAAQ,CAAE;cAAApC,QAAA,EAAC;YAG9C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV3G,OAAA;MAAS8G,SAAS,EAAC,wBAAwB;MAAAP,QAAA,eACzCvG,OAAA;QAAK8G,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnCvG,OAAA,CAAClB,GAAG;UACF2K,EAAE,EAAE;YACFvB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,KAAK;YACpBE,cAAc,EAAE,eAAe;YAC/BD,UAAU,EAAE;UACd,CAAE;UAAA7B,QAAA,gBAEFvG,OAAA;YAAAuG,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB3G,OAAA,CAACV,mBAAmB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EACLjG,MAAM,CAACoE,MAAM,GAAG,CAAC,gBAChB9E,OAAA;UAAAuG,QAAA,gBACEvG,OAAA;YAAAuG,QAAA,eACEvG,OAAA;cAAAuG,QAAA,gBACEvG,OAAA;gBAAAuG,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB3G,OAAA;gBAAAuG,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB3G,OAAA;gBAAAuG,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb3G,OAAA;gBAAAuG,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtB3G,OAAA;gBAAAuG,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf3G,OAAA;gBAAAuG,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR3G,OAAA;YAAAuG,QAAA,EACG7F,MAAM,CAACgJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClE,GAAG,CAAEoB,KAAK;cAAA,IAAA+C,qBAAA;cAAA,oBAC5B3J,OAAA;gBAEEoH,OAAO,EAAEA,CAAA,KAAMvG,gBAAgB,CAAC+F,KAAK,CAAE;gBACvCG,KAAK,EAAE;kBAAE6C,MAAM,EAAE;gBAAU,CAAE;gBAAArD,QAAA,gBAE7BvG,OAAA;kBAAAuG,QAAA,GACG,GAAG,EACH,EAAAoD,qBAAA,GAAA/C,KAAK,CAACiD,SAAS,CAACC,IAAI,CAAEpE,IAAI,IAAK;oBAC9B,MAAMqE,WAAW,GACfrE,IAAI,CAAC1B,OAAO,IAAI,OAAO0B,IAAI,CAAC1B,OAAO,KAAK,QAAQ,GAC5C0B,IAAI,CAAC1B,OAAO,CAACgG,GAAG,GAChBtE,IAAI,CAAC1B,OAAO;oBAClB,OAAO+F,WAAW,KAAKtJ,MAAM,CAACuD,OAAO;kBACvC,CAAC,CAAC,cAAA2F,qBAAA,uBANDA,qBAAA,CAMGT,IAAI,KAAI,KAAK;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACL3G,OAAA;kBAAAuG,QAAA,EAAKK,KAAK,CAACoD;gBAAG;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpB3G,OAAA;kBAAAuG,QAAA,EAAK,IAAI/C,IAAI,CAACoD,KAAK,CAAC/B,SAAS,CAAC,CAACoF,kBAAkB,CAAC;gBAAC;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzD3G,OAAA;kBAAAuG,QAAA,GACGK,KAAK,CAACsD,UAAU,CAACC,SAAS,EAAC,GAAC,EAAC,EAAE,EAC/BvD,KAAK,CAACsD,UAAU,CAACE,QAAQ;gBAAA;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACL3G,OAAA;kBAAAuG,QAAA,eACEvG,OAAA;oBACE+G,KAAK,EAAE;sBACLmB,OAAO,EAAE,cAAc;sBACvBD,SAAS,EAAE,KAAK;sBAChBhB,OAAO,EAAE,UAAU;sBACnBsB,YAAY,EAAE,KAAK;sBACnBD,eAAe,EACb1B,KAAK,CAACyD,WAAW,KAAK,SAAS,GAC3B,SAAS,GACTzD,KAAK,CAACyD,WAAW,KAAK,WAAW,GACjC,SAAS,GACT,SAAS;sBACfnD,KAAK,EACHN,KAAK,CAACyD,WAAW,KAAK,SAAS,GAC3B,SAAS,GACTzD,KAAK,CAACyD,WAAW,KAAK,WAAW,GACjC,SAAS,GACT,SAAS;sBACfC,UAAU,EAAE,KAAK;sBACjBtD,SAAS,EAAE,QAAQ;sBACnBuD,QAAQ,EAAE;oBACZ,CAAE;oBAAAhE,QAAA,EAEDK,KAAK,CAACyD;kBAAW;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACL3G,OAAA;kBAAAuG,QAAA,GAAI,QAAG,EAACK,KAAK,CAAC4D,KAAK;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GA/CpBC,KAAK,CAACoD,GAAG;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgDZ,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAER3G,OAAA;UACE+G,KAAK,EAAE;YACLC,SAAS,EAAE,QAAQ;YACnBC,OAAO,EAAE,WAAW;YACpBqB,eAAe,EAAE,aAAa;YAC9BC,YAAY,EAAE,MAAM;YACpBpB,MAAM,EAAE,QAAQ;YAChBqB,MAAM,EAAE,oBAAoB;YAC5BN,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBoC,SAAS,EAAE;UACb,CAAE;UAAAlE,QAAA,gBAEFvG,OAAA,CAACX,eAAe;YACd0H,KAAK,EAAE;cACL0B,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE,MAAM;cACpBxB,KAAK,EAAE;YACT;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF3G,OAAA;YAAI+G,KAAK,EAAE;cAAEI,MAAM,EAAE,cAAc;cAAED,KAAK,EAAE;YAAU,CAAE;YAAAX,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3G,OAAA;YAAG+G,KAAK,EAAE;cAAEI,MAAM,EAAE,GAAG;cAAEwB,QAAQ,EAAE,OAAO;cAAEzB,KAAK,EAAE;YAAU,CAAE;YAAAX,QAAA,EAAC;UAGhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnG,EAAA,CAhpBID,eAAe;EAAA,QACAhB,SAAS;AAAA;AAAAmL,GAAA,GADxBnK,eAAe;AAkpBrB,eAAeA,eAAe;AAAC,IAAAL,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAoK,GAAA;AAAAC,YAAA,CAAAzK,EAAA;AAAAyK,YAAA,CAAAxK,GAAA;AAAAwK,YAAA,CAAAtK,GAAA;AAAAsK,YAAA,CAAArK,GAAA;AAAAqK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}