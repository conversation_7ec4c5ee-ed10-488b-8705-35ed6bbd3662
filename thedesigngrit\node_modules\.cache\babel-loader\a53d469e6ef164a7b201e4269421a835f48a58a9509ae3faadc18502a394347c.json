{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\adminNav.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useAdmin } from \"../../utils/adminContext\";\nimport { useNavigate } from \"react-router-dom\"; // Import useHistory for navigation\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavbarAdmin = () => {\n  _s();\n  const {\n    logout\n  } = useAdmin(); // Access admin data and logout function from context\n  const navigate = useNavigate(); // Get history for navigation\n\n  // Handle logout\n  const handleLogout = () => {\n    logout(); // Call logout function from context\n    navigate(\"/admin-login\"); // Redirect to login page\n  };\n\n  // Handle select change (Logout)\n  const handleSelectChange = event => {\n    if (event.target.value === \"Logout\") {\n      handleLogout(); // Call handleLogout when \"Logout\" is selected\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar-vendor\",\n    style: {\n      position: \"relative\",\n      boxShadow: \"0px 0px 0px 0px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-logo-vendor\",\n      style: {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: \"10px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/Assets/TDG_Logo_Black.webp\",\n        alt: \"ProjectLogo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontWeight: \"bold\",\n          fontSize: \"18px\"\n        },\n        children: \"The Design Grit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-actions-vendor\",\n      children: /*#__PURE__*/_jsxDEV(\"select\", {\n        onChange: handleSelectChange,\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(NavbarAdmin, \"45NLnT89/Y8rnjpylGAPHeooWjg=\", false, function () {\n  return [useAdmin, useNavigate];\n});\n_c = NavbarAdmin;\nexport default NavbarAdmin;\nvar _c;\n$RefreshReg$(_c, \"NavbarAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAdmin", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "logout", "navigate", "handleLogout", "handleSelectChange", "event", "target", "value", "className", "style", "position", "boxShadow", "children", "display", "alignItems", "gap", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "fontSize", "onChange", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/adminNav.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useAdmin } from \"../../utils/adminContext\";\r\nimport { useNavigate } from \"react-router-dom\"; // Import useHistory for navigation\r\n\r\nconst NavbarAdmin = () => {\r\n  const { logout } = useAdmin(); // Access admin data and logout function from context\r\n  const navigate = useNavigate(); // Get history for navigation\r\n\r\n  // Handle logout\r\n  const handleLogout = () => {\r\n    logout(); // Call logout function from context\r\n    navigate(\"/admin-login\"); // Redirect to login page\r\n  };\r\n\r\n  // Handle select change (Logout)\r\n  const handleSelectChange = (event) => {\r\n    if (event.target.value === \"Logout\") {\r\n      handleLogout(); // Call handleLogout when \"Logout\" is selected\r\n    }\r\n  };\r\n\r\n  return (\r\n    <nav\r\n      className=\"navbar-vendor\"\r\n      style={{ position: \"relative\", boxShadow: \"0px 0px 0px 0px\" }}\r\n    >\r\n      {/* Logo */}\r\n      <div\r\n        className=\"navbar-logo-vendor\"\r\n        style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}\r\n      >\r\n        <img src=\"/Assets/TDG_Logo_Black.webp\" alt=\"ProjectLogo\" />\r\n        <span style={{ fontWeight: \"bold\", fontSize: \"18px\" }}>\r\n          The Design Grit\r\n        </span>\r\n      </div>\r\n\r\n      {/* Actions */}\r\n      <div className=\"navbar-actions-vendor\">\r\n        {/* Bell Icon */}\r\n        <select onChange={handleSelectChange}>\r\n          {\" \"}\r\n          {/* Added onChange handler */}\r\n          <option>Admin</option>\r\n          <option>Logout</option>\r\n        </select>\r\n      </div>\r\n\r\n      {/* Notification Overlay\r\n      {showNotifications && (\r\n        <div\r\n          className=\"notifications-overlay\"\r\n          style={{\r\n            position: \"absolute\",\r\n            top: \"60px\",\r\n            right: \"10px\",\r\n            width: \"320px\",\r\n            backgroundColor: \"#fff\",\r\n            boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.2)\",\r\n            borderRadius: \"8px\",\r\n            padding: \"15px\",\r\n            zIndex: 1000,\r\n          }}\r\n        >\r\n          <div style={{ display: \"flex\", justifyContent: \"space-between\" }}>\r\n            <h3>Notifications</h3>\r\n            <button\r\n              onClick={toggleNotifications}\r\n              style={{\r\n                border: \"none\",\r\n                backgroundColor: \"transparent\",\r\n                cursor: \"pointer\",\r\n                fontSize: \"16px\",\r\n              }}\r\n            >\r\n              ✕\r\n            </button>\r\n          </div>\r\n          {loading && <p>Loading...</p>}\r\n          {error && <p>Error: {error}</p>}\r\n          {requests.length > 0 ? (\r\n            requests.map((request) => (\r\n              <div\r\n                key={request.id}\r\n                style={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"space-between\",\r\n                  borderBottom: \"1px solid #ddd\",\r\n                  padding: \"10px 0\",\r\n                }}\r\n              >\r\n                <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                  <div\r\n                    style={{\r\n                      width: \"50px\",\r\n                      height: \"30px\",\r\n                      backgroundColor: \"#fff\",\r\n                      marginRight: \"10px\",\r\n                      borderRadius: \"5px\",\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src={request.brandLogo}\r\n                      alt={`${request.brand} Logo`}\r\n                      style={{\r\n                        width: \"100%\",\r\n                        height: \"100%\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <p\r\n                      style={{\r\n                        margin: \"0\",\r\n                        fontWeight: \"bold\",\r\n                        fontFamily: \"montserrat\",\r\n                      }}\r\n                    >\r\n                      {request.brand}\r\n                    </p>\r\n                    <p\r\n                      style={{\r\n                        margin: \"0\",\r\n                        color: \"#666\",\r\n                        fontSize: \"14px\",\r\n                        fontFamily: \"montserrat\",\r\n                        width: \"200px\",\r\n                      }}\r\n                    >\r\n                      {request.requestorName} · {request.email}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  style={{\r\n                    backgroundColor: \"#6B7755\",\r\n                    color: \"#fff\",\r\n                    padding: \"4px 10px\",\r\n                    borderRadius: \"5px\",\r\n                    fontSize: \"12px\",\r\n                    cursor: \"pointer\",\r\n                  }}\r\n                >\r\n                  View Details\r\n                </button>\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <p>No new requests</p>\r\n          )}\r\n\r\n          <div style={{ marginTop: \"10px\", textAlign: \"center\" }}>\r\n            <button\r\n              style={{\r\n                backgroundColor: \"#6B7755\",\r\n                color: \"#fff\",\r\n                border: \"none\",\r\n                padding: \"10px 15px\",\r\n                borderRadius: \"5px\",\r\n                cursor: \"pointer\",\r\n              }}\r\n            >\r\n              View All Notifications\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )} */}\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default NavbarAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,kBAAkB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAO,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC/B,MAAMO,QAAQ,GAAGN,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEhC;EACA,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzBF,MAAM,CAAC,CAAC,CAAC,CAAC;IACVC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAIC,KAAK,IAAK;IACpC,IAAIA,KAAK,CAACC,MAAM,CAACC,KAAK,KAAK,QAAQ,EAAE;MACnCJ,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EAED,oBACEL,OAAA;IACEU,SAAS,EAAC,eAAe;IACzBC,KAAK,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,SAAS,EAAE;IAAkB,CAAE;IAAAC,QAAA,gBAG9Dd,OAAA;MACEU,SAAS,EAAC,oBAAoB;MAC9BC,KAAK,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAH,QAAA,gBAE9Dd,OAAA;QAAKkB,GAAG,EAAC,6BAA6B;QAACC,GAAG,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DvB,OAAA;QAAMW,KAAK,EAAE;UAAEa,UAAU,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAX,QAAA,EAAC;MAEvD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNvB,OAAA;MAAKU,SAAS,EAAC,uBAAuB;MAAAI,QAAA,eAEpCd,OAAA;QAAQ0B,QAAQ,EAAEpB,kBAAmB;QAAAQ,QAAA,GAClC,GAAG,eAEJd,OAAA;UAAAc,QAAA,EAAQ;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtBvB,OAAA;UAAAc,QAAA,EAAQ;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA2HH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAvKID,WAAW;EAAA,QACIJ,QAAQ,EACVC,WAAW;AAAA;AAAA6B,EAAA,GAFxB1B,WAAW;AAyKjB,eAAeA,WAAW;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}