{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\ourEmployees.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Box, IconButton, CircularProgress } from \"@mui/material\";\nimport { IoIosClose } from \"react-icons/io\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = \"https://api.thedesigngrit.com/api/admin/all-admins\";\nconst OurEmployees = () => {\n  _s();\n  const [admins, setAdmins] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editPopupVisible, setEditPopupVisible] = useState(false);\n  const [currentAdmin, setCurrentAdmin] = useState(null);\n  const [deletingId, setDeletingId] = useState(null);\n  useEffect(() => {\n    fetchAdmins();\n  }, []);\n  const fetchAdmins = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(API_URL);\n      setAdmins(response.data);\n    } catch (error) {\n      console.error(\"Error fetching admins\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClick = admin => {\n    setCurrentAdmin(admin);\n    setEditPopupVisible(true);\n  };\n  const handleEditClose = () => {\n    setEditPopupVisible(false);\n    setCurrentAdmin(null);\n  };\n  const handleFormSubmit = async e => {\n    e.preventDefault();\n    try {\n      await axios.put(`https://api.thedesigngrit.com/api/admin/profile/${currentAdmin._id}`, currentAdmin);\n      setEditPopupVisible(false);\n      fetchAdmins();\n    } catch (error) {\n      console.error(\"Error updating admin\", error);\n    }\n  };\n  const handleDelete = async () => {\n    if (!window.confirm(\"Are you sure you want to delete this admin?\")) return;\n    setDeletingId(currentAdmin._id);\n    try {\n      await axios.delete(`https://api.thedesigngrit.com/api/admin/profile/${currentAdmin._id}`);\n      setEditPopupVisible(false);\n      fetchAdmins();\n    } catch (error) {\n      console.error(\"Error deleting admin\", error);\n    } finally {\n      setDeletingId(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"70px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header-title\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          flexDirection: \"row\",\n          gap: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Admins\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"12px\",\n          fontFamily: \"Montserrat\"\n        },\n        children: \"Home > Admins\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-lists-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recent-orders-vendor\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              color: \"#2d2d2d\",\n              textAlign: \"left\",\n              marginBottom: \"20px\"\n            },\n            children: \"Admins List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: {\n                backgroundColor: \"#f2f2f2\",\n                color: \"#2d2d2d\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), loading ? /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: 4,\n                  style: {\n                    textAlign: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    style: {\n                      color: \"#6b7b58\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: admins.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: 4,\n                  style: {\n                    textAlign: \"center\"\n                  },\n                  children: \"No admins found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 21\n              }, this) : admins.map(admin => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: admin.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: admin.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: admin.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEditClick(admin),\n                    style: {\n                      color: \"#e3e3e3\",\n                      backgroundColor: \"#6a8452\",\n                      marginRight: 8\n                    },\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 25\n                }, this)]\n              }, admin._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), editPopupVisible && currentAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"requestInfo-popup-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"requestInfo-popup\",\n          style: {\n            width: \"700px\",\n            height: \"400px\",\n            backdropFilter: \"blur(5px)\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"requestInfo-popup-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Edit Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleEditClose,\n              sx: {\n                position: \"absolute\",\n                top: \"16px\",\n                right: \"16px\",\n                color: \"#2d2d2d\"\n              },\n              children: /*#__PURE__*/_jsxDEV(IoIosClose, {\n                size: 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleFormSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"requestInfo-form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: currentAdmin.username,\n                onChange: e => setCurrentAdmin({\n                  ...currentAdmin,\n                  username: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"requestInfo-form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                value: currentAdmin.email,\n                onChange: e => setCurrentAdmin({\n                  ...currentAdmin,\n                  email: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"requestInfo-form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: currentAdmin.role,\n                onChange: e => setCurrentAdmin({\n                  ...currentAdmin,\n                  role: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"row\",\n                justifyContent: \"space-between\",\n                margin: \"auto\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"requestInfo-submit-button\",\n                style: {\n                  width: \"15%\"\n                },\n                children: \"Edit Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: handleDelete,\n                className: \"requestInfo-submit-button\",\n                style: {\n                  backgroundColor: \"#DC143C\",\n                  width: \"15%\"\n                },\n                disabled: deletingId === currentAdmin._id,\n                children: deletingId === currentAdmin._id ? \"Deleting...\" : \"Delete Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(OurEmployees, \"dD3rAvnyQDEFtQFKO3VISvLS5JU=\");\n_c = OurEmployees;\nexport default OurEmployees;\nvar _c;\n$RefreshReg$(_c, \"OurEmployees\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "IconButton", "CircularProgress", "IoIosClose", "jsxDEV", "_jsxDEV", "API_URL", "OurEmployees", "_s", "admins", "setAdmins", "loading", "setLoading", "editPopupVisible", "setEditPopupVisible", "currentAdmin", "setCurrentAdmin", "deletingId", "setDeletingId", "fetchAdmins", "response", "get", "data", "error", "console", "handleEditClick", "admin", "handleEditClose", "handleFormSubmit", "e", "preventDefault", "put", "_id", "handleDelete", "window", "confirm", "delete", "style", "padding", "children", "className", "display", "alignItems", "flexDirection", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontFamily", "sx", "justifyContent", "color", "textAlign", "marginBottom", "backgroundColor", "colSpan", "length", "map", "username", "email", "role", "onClick", "marginRight", "width", "height", "<PERSON><PERSON>ilter", "position", "top", "right", "size", "onSubmit", "type", "value", "onChange", "target", "margin", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/ourEmployees.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { Box, IconButton, CircularProgress } from \"@mui/material\";\r\nimport { IoIosClose } from \"react-icons/io\";\r\n\r\nconst API_URL = \"https://api.thedesigngrit.com/api/admin/all-admins\";\r\n\r\nconst OurEmployees = () => {\r\n  const [admins, setAdmins] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [editPopupVisible, setEditPopupVisible] = useState(false);\r\n  const [currentAdmin, setCurrentAdmin] = useState(null);\r\n  const [deletingId, setDeletingId] = useState(null);\r\n\r\n  useEffect(() => {\r\n    fetchAdmins();\r\n  }, []);\r\n\r\n  const fetchAdmins = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await axios.get(API_URL);\r\n      setAdmins(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching admins\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleEditClick = (admin) => {\r\n    setCurrentAdmin(admin);\r\n    setEditPopupVisible(true);\r\n  };\r\n\r\n  const handleEditClose = () => {\r\n    setEditPopupVisible(false);\r\n    setCurrentAdmin(null);\r\n  };\r\n\r\n  const handleFormSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      await axios.put(\r\n        `https://api.thedesigngrit.com/api/admin/profile/${currentAdmin._id}`,\r\n        currentAdmin\r\n      );\r\n      setEditPopupVisible(false);\r\n      fetchAdmins();\r\n    } catch (error) {\r\n      console.error(\"Error updating admin\", error);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async () => {\r\n    if (!window.confirm(\"Are you sure you want to delete this admin?\")) return;\r\n    setDeletingId(currentAdmin._id);\r\n    try {\r\n      await axios.delete(\r\n        `https://api.thedesigngrit.com/api/admin/profile/${currentAdmin._id}`\r\n      );\r\n      setEditPopupVisible(false);\r\n      fetchAdmins();\r\n    } catch (error) {\r\n      console.error(\"Error deleting admin\", error);\r\n    } finally {\r\n      setDeletingId(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ padding: \"70px\" }}>\r\n      <div className=\"dashboard-header-title\">\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            flexDirection: \"row\",\r\n            gap: \"10px\",\r\n          }}\r\n        >\r\n          <h2>Admins</h2>\r\n        </div>\r\n        <p style={{ fontSize: \"12px\", fontFamily: \"Montserrat\" }}>\r\n          Home &gt; Admins\r\n        </p>\r\n      </div>\r\n      <section className=\"dashboard-lists-vendor\">\r\n        <div className=\"recent-orders-vendor\">\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <h2\r\n              style={{\r\n                color: \"#2d2d2d\",\r\n                textAlign: \"left\",\r\n                marginBottom: \"20px\",\r\n              }}\r\n            >\r\n              Admins List\r\n            </h2>\r\n            <table>\r\n              <thead style={{ backgroundColor: \"#f2f2f2\", color: \"#2d2d2d\" }}>\r\n                <tr>\r\n                  <th>Username</th>\r\n                  <th>Email</th>\r\n                  <th>Role</th>\r\n                  <th>Actions</th>\r\n                </tr>\r\n              </thead>\r\n              {loading ? (\r\n                <tbody>\r\n                  <tr>\r\n                    <td colSpan={4} style={{ textAlign: \"center\" }}>\r\n                      <CircularProgress style={{ color: \"#6b7b58\" }} />\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              ) : (\r\n                <tbody>\r\n                  {admins.length === 0 ? (\r\n                    <tr>\r\n                      <td colSpan={4} style={{ textAlign: \"center\" }}>\r\n                        No admins found\r\n                      </td>\r\n                    </tr>\r\n                  ) : (\r\n                    admins.map((admin) => (\r\n                      <tr key={admin._id}>\r\n                        <td>{admin.username}</td>\r\n                        <td>{admin.email}</td>\r\n                        <td>{admin.role}</td>\r\n                        <td>\r\n                          <button\r\n                            onClick={() => handleEditClick(admin)}\r\n                            style={{\r\n                              color: \"#e3e3e3\",\r\n                              backgroundColor: \"#6a8452\",\r\n                              marginRight: 8,\r\n                            }}\r\n                          >\r\n                            Edit\r\n                          </button>\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  )}\r\n                </tbody>\r\n              )}\r\n            </table>\r\n          </Box>\r\n        </div>\r\n        {editPopupVisible && currentAdmin && (\r\n          <div className=\"requestInfo-popup-overlay\">\r\n            <div\r\n              className=\"requestInfo-popup\"\r\n              style={{\r\n                width: \"700px\",\r\n                height: \"400px\",\r\n                backdropFilter: \"blur(5px)\",\r\n              }}\r\n            >\r\n              <div className=\"requestInfo-popup-header\">\r\n                <h2>Edit Admin</h2>\r\n                <IconButton\r\n                  onClick={handleEditClose}\r\n                  sx={{\r\n                    position: \"absolute\",\r\n                    top: \"16px\",\r\n                    right: \"16px\",\r\n                    color: \"#2d2d2d\",\r\n                  }}\r\n                >\r\n                  <IoIosClose size={30} />\r\n                </IconButton>\r\n              </div>\r\n              <form onSubmit={handleFormSubmit}>\r\n                <div className=\"requestInfo-form-group\">\r\n                  <label>Username</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={currentAdmin.username}\r\n                    onChange={(e) =>\r\n                      setCurrentAdmin({\r\n                        ...currentAdmin,\r\n                        username: e.target.value,\r\n                      })\r\n                    }\r\n                  />\r\n                </div>\r\n                <div className=\"requestInfo-form-group\">\r\n                  <label>Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    value={currentAdmin.email}\r\n                    onChange={(e) =>\r\n                      setCurrentAdmin({\r\n                        ...currentAdmin,\r\n                        email: e.target.value,\r\n                      })\r\n                    }\r\n                  />\r\n                </div>\r\n                <div className=\"requestInfo-form-group\">\r\n                  <label>Role</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={currentAdmin.role}\r\n                    onChange={(e) =>\r\n                      setCurrentAdmin({\r\n                        ...currentAdmin,\r\n                        role: e.target.value,\r\n                      })\r\n                    }\r\n                  />\r\n                </div>\r\n                <div\r\n                  style={{\r\n                    display: \"flex\",\r\n                    flexDirection: \"row\",\r\n                    justifyContent: \"space-between\",\r\n                    margin: \"auto\",\r\n                  }}\r\n                >\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"requestInfo-submit-button\"\r\n                    style={{ width: \"15%\" }}\r\n                  >\r\n                    Edit Admin\r\n                  </button>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleDelete}\r\n                    className=\"requestInfo-submit-button\"\r\n                    style={{ backgroundColor: \"#DC143C\", width: \"15%\" }}\r\n                    disabled={deletingId === currentAdmin._id}\r\n                  >\r\n                    {deletingId === currentAdmin._id\r\n                      ? \"Deleting...\"\r\n                      : \"Delete Admin\"}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OurEmployees;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,eAAe;AACjE,SAASC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,OAAO,GAAG,oDAAoD;AAEpE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdqB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAACf,OAAO,CAAC;MACzCI,SAAS,CAACU,QAAQ,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,eAAe,GAAIC,KAAK,IAAK;IACjCV,eAAe,CAACU,KAAK,CAAC;IACtBZ,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMa,eAAe,GAAGA,CAAA,KAAM;IAC5Bb,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMY,gBAAgB,GAAG,MAAOC,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAM/B,KAAK,CAACgC,GAAG,CACb,mDAAmDhB,YAAY,CAACiB,GAAG,EAAE,EACrEjB,YACF,CAAC;MACDD,mBAAmB,CAAC,KAAK,CAAC;MAC1BK,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,6CAA6C,CAAC,EAAE;IACpEjB,aAAa,CAACH,YAAY,CAACiB,GAAG,CAAC;IAC/B,IAAI;MACF,MAAMjC,KAAK,CAACqC,MAAM,CAChB,mDAAmDrB,YAAY,CAACiB,GAAG,EACrE,CAAC;MACDlB,mBAAmB,CAAC,KAAK,CAAC;MAC1BK,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRL,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,oBACEb,OAAA;IAAKgC,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BlC,OAAA;MAAKmC,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACrClC,OAAA;QACEgC,KAAK,EAAE;UACLI,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,aAAa,EAAE,KAAK;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAL,QAAA,eAEFlC,OAAA;UAAAkC,QAAA,EAAI;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACN3C,OAAA;QAAGgC,KAAK,EAAE;UAAEY,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAa,CAAE;QAAAX,QAAA,EAAC;MAE1D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACN3C,OAAA;MAASmC,SAAS,EAAC,wBAAwB;MAAAD,QAAA,gBACzClC,OAAA;QAAKmC,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnClC,OAAA,CAACL,GAAG;UACFmD,EAAE,EAAE;YACFV,OAAO,EAAE,MAAM;YACfE,aAAa,EAAE,QAAQ;YACvBS,cAAc,EAAE,eAAe;YAC/BV,UAAU,EAAE;UACd,CAAE;UAAAH,QAAA,gBAEFlC,OAAA;YACEgC,KAAK,EAAE;cACLgB,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,YAAY,EAAE;YAChB,CAAE;YAAAhB,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3C,OAAA;YAAAkC,QAAA,gBACElC,OAAA;cAAOgC,KAAK,EAAE;gBAAEmB,eAAe,EAAE,SAAS;gBAAEH,KAAK,EAAE;cAAU,CAAE;cAAAd,QAAA,eAC7DlC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAAkC,QAAA,EAAI;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjB3C,OAAA;kBAAAkC,QAAA,EAAI;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd3C,OAAA;kBAAAkC,QAAA,EAAI;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb3C,OAAA;kBAAAkC,QAAA,EAAI;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EACPrC,OAAO,gBACNN,OAAA;cAAAkC,QAAA,eACElC,OAAA;gBAAAkC,QAAA,eACElC,OAAA;kBAAIoD,OAAO,EAAE,CAAE;kBAACpB,KAAK,EAAE;oBAAEiB,SAAS,EAAE;kBAAS,CAAE;kBAAAf,QAAA,eAC7ClC,OAAA,CAACH,gBAAgB;oBAACmC,KAAK,EAAE;sBAAEgB,KAAK,EAAE;oBAAU;kBAAE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAER3C,OAAA;cAAAkC,QAAA,EACG9B,MAAM,CAACiD,MAAM,KAAK,CAAC,gBAClBrD,OAAA;gBAAAkC,QAAA,eACElC,OAAA;kBAAIoD,OAAO,EAAE,CAAE;kBAACpB,KAAK,EAAE;oBAAEiB,SAAS,EAAE;kBAAS,CAAE;kBAAAf,QAAA,EAAC;gBAEhD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,GAELvC,MAAM,CAACkD,GAAG,CAAEjC,KAAK,iBACfrB,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAAkC,QAAA,EAAKb,KAAK,CAACkC;gBAAQ;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzB3C,OAAA;kBAAAkC,QAAA,EAAKb,KAAK,CAACmC;gBAAK;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtB3C,OAAA;kBAAAkC,QAAA,EAAKb,KAAK,CAACoC;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrB3C,OAAA;kBAAAkC,QAAA,eACElC,OAAA;oBACE0D,OAAO,EAAEA,CAAA,KAAMtC,eAAe,CAACC,KAAK,CAAE;oBACtCW,KAAK,EAAE;sBACLgB,KAAK,EAAE,SAAS;sBAChBG,eAAe,EAAE,SAAS;sBAC1BQ,WAAW,EAAE;oBACf,CAAE;oBAAAzB,QAAA,EACH;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAfEtB,KAAK,CAACM,GAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBd,CACL;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLnC,gBAAgB,IAAIE,YAAY,iBAC/BV,OAAA;QAAKmC,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACxClC,OAAA;UACEmC,SAAS,EAAC,mBAAmB;UAC7BH,KAAK,EAAE;YACL4B,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,OAAO;YACfC,cAAc,EAAE;UAClB,CAAE;UAAA5B,QAAA,gBAEFlC,OAAA;YAAKmC,SAAS,EAAC,0BAA0B;YAAAD,QAAA,gBACvClC,OAAA;cAAAkC,QAAA,EAAI;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3C,OAAA,CAACJ,UAAU;cACT8D,OAAO,EAAEpC,eAAgB;cACzBwB,EAAE,EAAE;gBACFiB,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,MAAM;gBACXC,KAAK,EAAE,MAAM;gBACbjB,KAAK,EAAE;cACT,CAAE;cAAAd,QAAA,eAEFlC,OAAA,CAACF,UAAU;gBAACoE,IAAI,EAAE;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3C,OAAA;YAAMmE,QAAQ,EAAE5C,gBAAiB;YAAAW,QAAA,gBAC/BlC,OAAA;cAAKmC,SAAS,EAAC,wBAAwB;cAAAD,QAAA,gBACrClC,OAAA;gBAAAkC,QAAA,EAAO;cAAQ;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB3C,OAAA;gBACEoE,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE3D,YAAY,CAAC6C,QAAS;gBAC7Be,QAAQ,EAAG9C,CAAC,IACVb,eAAe,CAAC;kBACd,GAAGD,YAAY;kBACf6C,QAAQ,EAAE/B,CAAC,CAAC+C,MAAM,CAACF;gBACrB,CAAC;cACF;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3C,OAAA;cAAKmC,SAAS,EAAC,wBAAwB;cAAAD,QAAA,gBACrClC,OAAA;gBAAAkC,QAAA,EAAO;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpB3C,OAAA;gBACEoE,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE3D,YAAY,CAAC8C,KAAM;gBAC1Bc,QAAQ,EAAG9C,CAAC,IACVb,eAAe,CAAC;kBACd,GAAGD,YAAY;kBACf8C,KAAK,EAAEhC,CAAC,CAAC+C,MAAM,CAACF;gBAClB,CAAC;cACF;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3C,OAAA;cAAKmC,SAAS,EAAC,wBAAwB;cAAAD,QAAA,gBACrClC,OAAA;gBAAAkC,QAAA,EAAO;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnB3C,OAAA;gBACEoE,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE3D,YAAY,CAAC+C,IAAK;gBACzBa,QAAQ,EAAG9C,CAAC,IACVb,eAAe,CAAC;kBACd,GAAGD,YAAY;kBACf+C,IAAI,EAAEjC,CAAC,CAAC+C,MAAM,CAACF;gBACjB,CAAC;cACF;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3C,OAAA;cACEgC,KAAK,EAAE;gBACLI,OAAO,EAAE,MAAM;gBACfE,aAAa,EAAE,KAAK;gBACpBS,cAAc,EAAE,eAAe;gBAC/ByB,MAAM,EAAE;cACV,CAAE;cAAAtC,QAAA,gBAEFlC,OAAA;gBACEoE,IAAI,EAAC,QAAQ;gBACbjC,SAAS,EAAC,2BAA2B;gBACrCH,KAAK,EAAE;kBAAE4B,KAAK,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EACzB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3C,OAAA;gBACEoE,IAAI,EAAC,QAAQ;gBACbV,OAAO,EAAE9B,YAAa;gBACtBO,SAAS,EAAC,2BAA2B;gBACrCH,KAAK,EAAE;kBAAEmB,eAAe,EAAE,SAAS;kBAAES,KAAK,EAAE;gBAAM,CAAE;gBACpDa,QAAQ,EAAE7D,UAAU,KAAKF,YAAY,CAACiB,GAAI;gBAAAO,QAAA,EAEzCtB,UAAU,KAAKF,YAAY,CAACiB,GAAG,GAC5B,aAAa,GACb;cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACxC,EAAA,CAxPID,YAAY;AAAAwE,EAAA,GAAZxE,YAAY;AA0PlB,eAAeA,YAAY;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}