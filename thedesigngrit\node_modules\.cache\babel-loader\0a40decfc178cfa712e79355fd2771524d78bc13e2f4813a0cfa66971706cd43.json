{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\signInForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { FaFacebook } from \"react-icons/fa\";\nimport { FcGoogle } from \"react-icons/fc\";\nimport { AiOutlineEye, AiOutlineEyeInvisible } from \"react-icons/ai\";\nimport { GoogleLogin } from \"@react-oauth/google\";\nimport axios from \"axios\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useUser } from \"../utils/userContext\";\nimport { useForm } from \"react-hook-form\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport * as yup from \"yup\";\nimport ForgotPasswordDialog from \"./forgetPassword\";\nimport ConfirmationDialog from \"./confirmationMsg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst schema = yup.object().shape({\n  email: yup.string().email(\"Invalid email format\").required(\"Email is required\"),\n  password: yup.string().min(6, \"Password must be at least 6 characters\").required(\"Password is required\")\n});\nfunction SignInForm() {\n  _s();\n  var _errors$email2, _errors$password2;\n  const {\n    setUserSession\n  } = useUser();\n  const navigate = useNavigate();\n  const [showPassword, setShowPassword] = useState(false);\n  const [forgotPasswordDialogOpen, setForgotPasswordDialogOpen] = useState(false);\n  const [forgotPasswordSuccessDialogOpen, setForgotPasswordSuccessDialogOpen] = useState(false);\n  const [loginError, setLoginError] = useState(\"\");\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    trigger // ✅ Add this\n  } = useForm({\n    resolver: yupResolver(schema)\n  });\n\n  // Google OAuth handlers\n  const handleGoogleSuccess = async credentialResponse => {\n    try {\n      setLoginError(\"\"); // Clear previous error messages\n      const googleCredential = credentialResponse.credential;\n      const response = await axios.post(\"https://api.thedesigngrit.com/api/signin\", {\n        googleCredential\n      }, {\n        withCredentials: true\n      });\n      setUserSession(response.data.user);\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Error during Google sign-in:\", error.response || error);\n      if (error.response) {\n        if (error.response.status === 401) {\n          setLoginError(\"Google authentication failed. Please try again.\");\n        } else if (error.response.data && error.response.data.message) {\n          setLoginError(error.response.data.message);\n        } else {\n          setLoginError(\"Google login failed. Please try again.\");\n        }\n      } else {\n        setLoginError(\"Network error. Please check your connection and try again.\");\n      }\n    }\n  };\n  const handleGoogleError = () => {\n    setLoginError(\"Google sign-in was cancelled or failed. Please try again.\");\n  };\n  const onSubmit = async data => {\n    try {\n      setLoginError(\"\"); // Clear previous error messages\n\n      const response = await axios.post(\"https://api.thedesigngrit.com/api/signin\", data, {\n        withCredentials: true\n      });\n      setUserSession(response.data.user);\n      navigate(\"/\");\n    } catch (error) {\n      console.error(\"Error during sign-in:\", error.response || error);\n\n      // Set appropriate error message based on the error response\n      if (error.response) {\n        if (error.response.status === 401) {\n          setLoginError(\"Invalid email or password. Please try again.\");\n        } else if (error.response.data && error.response.data.message) {\n          setLoginError(error.response.data.message);\n        } else {\n          setLoginError(\"Login failed. Please check your credentials and try again.\");\n        }\n      } else {\n        setLoginError(\"Network error. Please check your connection and try again.\");\n      }\n    }\n  };\n  const handleValidateAndSubmit = async data => {\n    const isValid = await trigger(); // Validate fields manually\n    if (!isValid) {\n      var _errors$email, _errors$password;\n      const firstError = ((_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message) || ((_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message);\n      setLoginError(firstError);\n      return;\n    }\n    onSubmit(data);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"form-title-signin\",\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"signin-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"social-btns-section\",\n        children: /*#__PURE__*/_jsxDEV(GoogleLogin, {\n          onSuccess: handleGoogleSuccess,\n          onError: handleGoogleError,\n          useOneTap: false,\n          render: renderProps => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn social-btn google-btn\",\n            onClick: renderProps.onClick,\n            disabled: renderProps.disabled,\n            style: {\n              width: \"100%\",\n              height: \"40px\",\n              fontFamily: \"Montserrat\",\n              fontSize: \"14px\",\n              fontWeight: \"500\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              gap: \"8px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(FcGoogle, {\n              style: {\n                fontSize: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), \"Continue with Google\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divider-signIn\",\n        children: \" OR\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(handleValidateAndSubmit),\n        children: [(errors.email || errors.password || loginError) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-error-message\",\n          children: [((_errors$email2 = errors.email) === null || _errors$email2 === void 0 ? void 0 : _errors$email2.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: errors.email.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 41\n          }, this), ((_errors$password2 = errors.password) === null || _errors$password2 === void 0 ? void 0 : _errors$password2.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: errors.password.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 44\n          }, this), loginError && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: loginError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          ...register(\"email\"),\n          placeholder: \"E-mail\",\n          className: \"input-field\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: showPassword ? \"text\" : \"password\",\n            name: \"password\",\n            ...register(\"password\"),\n            placeholder: \"Password\",\n            className: \"input-field\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            onClick: () => setShowPassword(prevState => !prevState),\n            style: {\n              position: \"absolute\",\n              right: \"18px\",\n              top: \"53%\",\n              transform: \"translateY(-50%)\",\n              cursor: \"pointer\",\n              color: \"#6b7b58\",\n              fontFamily: \"Montserrat\"\n            },\n            children: showPassword ? /*#__PURE__*/_jsxDEV(AiOutlineEyeInvisible, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(AiOutlineEye, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => setForgotPasswordDialogOpen(true),\n          style: {\n            position: \"absolute\",\n            right: \"59px\",\n            fontSize: \"12px\",\n            color: \"#e0e0e0\",\n            cursor: \"pointer\",\n            fontFamily: \"Montserrat\",\n            \"@media (max-width: 768px)\": {\n              right: \"40px\"\n            }\n          },\n          children: \"Forgot Password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn signin-btn\",\n          style: {\n            marginTop: \"24px\",\n            marginBottom: \"-20px\",\n            \"@media (max-width: 768px)\": {\n              marginBottom: \"-15px\"\n            }\n          },\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"register-link\",\n        children: [\"If you don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/signup\",\n          children: \"Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 41\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ForgotPasswordDialog, {\n      open: forgotPasswordDialogOpen,\n      onClose: () => setForgotPasswordDialogOpen(false),\n      onSend: () => setForgotPasswordSuccessDialogOpen(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationDialog, {\n      open: forgotPasswordSuccessDialogOpen,\n      title: \"Reset Link Sent\",\n      content: \"A password reset link has been sent to your email.\",\n      onConfirm: () => setForgotPasswordSuccessDialogOpen(false),\n      onCancel: () => setForgotPasswordSuccessDialogOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n}\n_s(SignInForm, \"8NTEv4pIUDgwNYkhoJJiF7gueDY=\", false, function () {\n  return [useUser, useNavigate, useForm];\n});\n_c = SignInForm;\nexport default SignInForm;\nvar _c;\n$RefreshReg$(_c, \"SignInForm\");", "map": {"version": 3, "names": ["React", "useState", "FaFacebook", "FcGoogle", "AiOutlineEye", "AiOutlineEyeInvisible", "GoogleLogin", "axios", "useNavigate", "useUser", "useForm", "yupResolver", "yup", "ForgotPasswordDialog", "ConfirmationDialog", "jsxDEV", "_jsxDEV", "schema", "object", "shape", "email", "string", "required", "password", "min", "SignInForm", "_s", "_errors$email2", "_errors$password2", "setUserSession", "navigate", "showPassword", "setShowPassword", "forgotPasswordDialogOpen", "setForgotPasswordDialogOpen", "forgotPasswordSuccessDialogOpen", "setForgotPasswordSuccessDialogOpen", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register", "handleSubmit", "formState", "errors", "trigger", "resolver", "handleGoogleSuccess", "credentialResponse", "googleCredential", "credential", "response", "post", "withCredentials", "data", "user", "error", "console", "status", "message", "handleGoogleError", "onSubmit", "handleValidateAndSubmit", "<PERSON><PERSON><PERSON><PERSON>", "_errors$email", "_errors$password", "firstError", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSuccess", "onError", "useOneTap", "render", "renderProps", "type", "onClick", "disabled", "style", "width", "height", "fontFamily", "fontSize", "fontWeight", "display", "alignItems", "justifyContent", "gap", "name", "placeholder", "position", "prevState", "right", "top", "transform", "cursor", "color", "marginTop", "marginBottom", "href", "open", "onClose", "onSend", "title", "content", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/signInForm.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { FaFacebook } from \"react-icons/fa\";\r\nimport { FcGoogle } from \"react-icons/fc\";\r\nimport { AiOutlineEye, AiOutlineEyeInvisible } from \"react-icons/ai\";\r\nimport { GoogleLogin } from \"@react-oauth/google\";\r\nimport axios from \"axios\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useUser } from \"../utils/userContext\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { yupResolver } from \"@hookform/resolvers/yup\";\r\nimport * as yup from \"yup\";\r\nimport ForgotPasswordDialog from \"./forgetPassword\";\r\nimport ConfirmationDialog from \"./confirmationMsg\";\r\nconst schema = yup.object().shape({\r\n  email: yup\r\n    .string()\r\n    .email(\"Invalid email format\")\r\n    .required(\"Email is required\"),\r\n  password: yup\r\n    .string()\r\n    .min(6, \"Password must be at least 6 characters\")\r\n    .required(\"Password is required\"),\r\n});\r\n\r\nfunction SignInForm() {\r\n  const { setUserSession } = useUser();\r\n  const navigate = useNavigate();\r\n\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [forgotPasswordDialogOpen, setForgotPasswordDialogOpen] =\r\n    useState(false);\r\n  const [forgotPasswordSuccessDialogOpen, setForgotPasswordSuccessDialogOpen] =\r\n    useState(false);\r\n  const [loginError, setLoginError] = useState(\"\");\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    trigger, // ✅ Add this\r\n  } = useForm({\r\n    resolver: yupResolver(schema),\r\n  });\r\n\r\n  // Google OAuth handlers\r\n  const handleGoogleSuccess = async (credentialResponse) => {\r\n    try {\r\n      setLoginError(\"\"); // Clear previous error messages\r\n      const googleCredential = credentialResponse.credential;\r\n      const response = await axios.post(\r\n        \"https://api.thedesigngrit.com/api/signin\",\r\n        { googleCredential },\r\n        { withCredentials: true }\r\n      );\r\n      setUserSession(response.data.user);\r\n      navigate(\"/\");\r\n    } catch (error) {\r\n      console.error(\"Error during Google sign-in:\", error.response || error);\r\n      if (error.response) {\r\n        if (error.response.status === 401) {\r\n          setLoginError(\"Google authentication failed. Please try again.\");\r\n        } else if (error.response.data && error.response.data.message) {\r\n          setLoginError(error.response.data.message);\r\n        } else {\r\n          setLoginError(\"Google login failed. Please try again.\");\r\n        }\r\n      } else {\r\n        setLoginError(\r\n          \"Network error. Please check your connection and try again.\"\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleGoogleError = () => {\r\n    setLoginError(\"Google sign-in was cancelled or failed. Please try again.\");\r\n  };\r\n\r\n  const onSubmit = async (data) => {\r\n    try {\r\n      setLoginError(\"\"); // Clear previous error messages\r\n\r\n      const response = await axios.post(\r\n        \"https://api.thedesigngrit.com/api/signin\",\r\n        data,\r\n        { withCredentials: true }\r\n      );\r\n\r\n      setUserSession(response.data.user);\r\n      navigate(\"/\");\r\n    } catch (error) {\r\n      console.error(\"Error during sign-in:\", error.response || error);\r\n\r\n      // Set appropriate error message based on the error response\r\n      if (error.response) {\r\n        if (error.response.status === 401) {\r\n          setLoginError(\"Invalid email or password. Please try again.\");\r\n        } else if (error.response.data && error.response.data.message) {\r\n          setLoginError(error.response.data.message);\r\n        } else {\r\n          setLoginError(\r\n            \"Login failed. Please check your credentials and try again.\"\r\n          );\r\n        }\r\n      } else {\r\n        setLoginError(\r\n          \"Network error. Please check your connection and try again.\"\r\n        );\r\n      }\r\n    }\r\n  };\r\n  const handleValidateAndSubmit = async (data) => {\r\n    const isValid = await trigger(); // Validate fields manually\r\n    if (!isValid) {\r\n      const firstError = errors.email?.message || errors.password?.message;\r\n      setLoginError(firstError);\r\n      return;\r\n    }\r\n    onSubmit(data);\r\n  };\r\n  return (\r\n    <div>\r\n      <h1 className=\"form-title-signin\">Login</h1>\r\n      <div className=\"signin-form\">\r\n        <div className=\"social-btns-section\">\r\n          <GoogleLogin\r\n            onSuccess={handleGoogleSuccess}\r\n            onError={handleGoogleError}\r\n            useOneTap={false}\r\n            render={(renderProps) => (\r\n              <button\r\n                type=\"button\"\r\n                className=\"btn social-btn google-btn\"\r\n                onClick={renderProps.onClick}\r\n                disabled={renderProps.disabled}\r\n                style={{\r\n                  width: \"100%\",\r\n                  height: \"40px\",\r\n                  fontFamily: \"Montserrat\",\r\n                  fontSize: \"14px\",\r\n                  fontWeight: \"500\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  gap: \"8px\",\r\n                }}\r\n              >\r\n                <FcGoogle style={{ fontSize: \"20px\" }} />\r\n                Continue with Google\r\n              </button>\r\n            )}\r\n          />\r\n          {/* <button className=\"btn social-btn facebook-btn\">\r\n            <FaFacebook className=\"facebook-icon\" />\r\n            Continue with Facebook\r\n          </button> */}\r\n        </div>\r\n        <div className=\"divider-signIn\"> OR</div>\r\n\r\n        <form onSubmit={handleSubmit(handleValidateAndSubmit)}>\r\n          {/* Display general login error at the top of the form */}\r\n          {(errors.email || errors.password || loginError) && (\r\n            <div className=\"login-error-message\">\r\n              {errors.email?.message && <div>{errors.email.message}</div>}\r\n              {errors.password?.message && <div>{errors.password.message}</div>}\r\n              {loginError && <div>{loginError}</div>}\r\n            </div>\r\n          )}\r\n\r\n          <input\r\n            type=\"email\"\r\n            name=\"email\"\r\n            {...register(\"email\")}\r\n            placeholder=\"E-mail\"\r\n            className=\"input-field\"\r\n          />\r\n          {/* {errors.email && (\r\n            <p className=\"error-message-login\">{errors.email.message}</p>\r\n          )} */}\r\n\r\n          <div style={{ position: \"relative\" }}>\r\n            <input\r\n              type={showPassword ? \"text\" : \"password\"}\r\n              name=\"password\"\r\n              {...register(\"password\")}\r\n              placeholder=\"Password\"\r\n              className=\"input-field\"\r\n            />\r\n            <span\r\n              onClick={() => setShowPassword((prevState) => !prevState)}\r\n              style={{\r\n                position: \"absolute\",\r\n                right: \"18px\",\r\n                top: \"53%\",\r\n                transform: \"translateY(-50%)\",\r\n                cursor: \"pointer\",\r\n                color: \"#6b7b58\",\r\n                fontFamily: \"Montserrat\",\r\n              }}\r\n            >\r\n              {showPassword ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}\r\n            </span>\r\n          </div>\r\n          <span\r\n            onClick={() => setForgotPasswordDialogOpen(true)}\r\n            style={{\r\n              position: \"absolute\",\r\n              right: \"59px\",\r\n              fontSize: \"12px\",\r\n              color: \"#e0e0e0\",\r\n              cursor: \"pointer\",\r\n              fontFamily: \"Montserrat\",\r\n\r\n              \"@media (max-width: 768px)\": {\r\n                right: \"40px\",\r\n              },\r\n            }}\r\n          >\r\n            Forgot Password?\r\n          </span>\r\n          {/* {errors.password && (\r\n            <p className=\"error-message-login\">{errors.password.message}</p>\r\n          )} */}\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className=\"btn signin-btn\"\r\n            style={{\r\n              marginTop: \"24px\",\r\n              marginBottom: \"-20px\",\r\n              \"@media (max-width: 768px)\": {\r\n                marginBottom: \"-15px\",\r\n              },\r\n            }}\r\n          >\r\n            Sign In\r\n          </button>\r\n        </form>\r\n\r\n        <p className=\"register-link\">\r\n          If you don't have an account? <a href=\"/signup\">Register</a>\r\n        </p>\r\n      </div>\r\n      <ForgotPasswordDialog\r\n        open={forgotPasswordDialogOpen}\r\n        onClose={() => setForgotPasswordDialogOpen(false)}\r\n        onSend={() => setForgotPasswordSuccessDialogOpen(true)}\r\n      />\r\n      <ConfirmationDialog\r\n        open={forgotPasswordSuccessDialogOpen}\r\n        title=\"Reset Link Sent\"\r\n        content=\"A password reset link has been sent to your email.\"\r\n        onConfirm={() => setForgotPasswordSuccessDialogOpen(false)}\r\n        onCancel={() => setForgotPasswordSuccessDialogOpen(false)}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SignInForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,gBAAgB;AACpE,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,OAAOC,oBAAoB,MAAM,kBAAkB;AACnD,OAAOC,kBAAkB,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACnD,MAAMC,MAAM,GAAGL,GAAG,CAACM,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAChCC,KAAK,EAAER,GAAG,CACPS,MAAM,CAAC,CAAC,CACRD,KAAK,CAAC,sBAAsB,CAAC,CAC7BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,QAAQ,EAAEX,GAAG,CACVS,MAAM,CAAC,CAAC,CACRG,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDF,QAAQ,CAAC,sBAAsB;AACpC,CAAC,CAAC;AAEF,SAASG,UAAUA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,iBAAA;EACpB,MAAM;IAAEC;EAAe,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACpC,MAAMqB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgC,wBAAwB,EAAEC,2BAA2B,CAAC,GAC3DjC,QAAQ,CAAC,KAAK,CAAC;EACjB,MAAM,CAACkC,+BAA+B,EAAEC,kCAAkC,CAAC,GACzEnC,QAAQ,CAAC,KAAK,CAAC;EACjB,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM;IACJsC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC,OAAO,CAAE;EACX,CAAC,GAAGjC,OAAO,CAAC;IACVkC,QAAQ,EAAEjC,WAAW,CAACM,MAAM;EAC9B,CAAC,CAAC;;EAEF;EACA,MAAM4B,mBAAmB,GAAG,MAAOC,kBAAkB,IAAK;IACxD,IAAI;MACFR,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACE,UAAU;MACtD,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,IAAI,CAC/B,0CAA0C,EAC1C;QAAEH;MAAiB,CAAC,EACpB;QAAEI,eAAe,EAAE;MAAK,CAC1B,CAAC;MACDtB,cAAc,CAACoB,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;MAClCvB,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAACL,QAAQ,IAAIK,KAAK,CAAC;MACtE,IAAIA,KAAK,CAACL,QAAQ,EAAE;QAClB,IAAIK,KAAK,CAACL,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;UACjClB,aAAa,CAAC,iDAAiD,CAAC;QAClE,CAAC,MAAM,IAAIgB,KAAK,CAACL,QAAQ,CAACG,IAAI,IAAIE,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACK,OAAO,EAAE;UAC7DnB,aAAa,CAACgB,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACK,OAAO,CAAC;QAC5C,CAAC,MAAM;UACLnB,aAAa,CAAC,wCAAwC,CAAC;QACzD;MACF,CAAC,MAAM;QACLA,aAAa,CACX,4DACF,CAAC;MACH;IACF;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpB,aAAa,CAAC,2DAA2D,CAAC;EAC5E,CAAC;EAED,MAAMqB,QAAQ,GAAG,MAAOP,IAAI,IAAK;IAC/B,IAAI;MACFd,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEnB,MAAMW,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,IAAI,CAC/B,0CAA0C,EAC1CE,IAAI,EACJ;QAAED,eAAe,EAAE;MAAK,CAC1B,CAAC;MAEDtB,cAAc,CAACoB,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;MAClCvB,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACL,QAAQ,IAAIK,KAAK,CAAC;;MAE/D;MACA,IAAIA,KAAK,CAACL,QAAQ,EAAE;QAClB,IAAIK,KAAK,CAACL,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;UACjClB,aAAa,CAAC,8CAA8C,CAAC;QAC/D,CAAC,MAAM,IAAIgB,KAAK,CAACL,QAAQ,CAACG,IAAI,IAAIE,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACK,OAAO,EAAE;UAC7DnB,aAAa,CAACgB,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACK,OAAO,CAAC;QAC5C,CAAC,MAAM;UACLnB,aAAa,CACX,4DACF,CAAC;QACH;MACF,CAAC,MAAM;QACLA,aAAa,CACX,4DACF,CAAC;MACH;IACF;EACF,CAAC;EACD,MAAMsB,uBAAuB,GAAG,MAAOR,IAAI,IAAK;IAC9C,MAAMS,OAAO,GAAG,MAAMlB,OAAO,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,CAACkB,OAAO,EAAE;MAAA,IAAAC,aAAA,EAAAC,gBAAA;MACZ,MAAMC,UAAU,GAAG,EAAAF,aAAA,GAAApB,MAAM,CAACtB,KAAK,cAAA0C,aAAA,uBAAZA,aAAA,CAAcL,OAAO,OAAAM,gBAAA,GAAIrB,MAAM,CAACnB,QAAQ,cAAAwC,gBAAA,uBAAfA,gBAAA,CAAiBN,OAAO;MACpEnB,aAAa,CAAC0B,UAAU,CAAC;MACzB;IACF;IACAL,QAAQ,CAACP,IAAI,CAAC;EAChB,CAAC;EACD,oBACEpC,OAAA;IAAAiD,QAAA,gBACEjD,OAAA;MAAIkD,SAAS,EAAC,mBAAmB;MAAAD,QAAA,EAAC;IAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5CtD,OAAA;MAAKkD,SAAS,EAAC,aAAa;MAAAD,QAAA,gBAC1BjD,OAAA;QAAKkD,SAAS,EAAC,qBAAqB;QAAAD,QAAA,eAClCjD,OAAA,CAACV,WAAW;UACViE,SAAS,EAAE1B,mBAAoB;UAC/B2B,OAAO,EAAEd,iBAAkB;UAC3Be,SAAS,EAAE,KAAM;UACjBC,MAAM,EAAGC,WAAW,iBAClB3D,OAAA;YACE4D,IAAI,EAAC,QAAQ;YACbV,SAAS,EAAC,2BAA2B;YACrCW,OAAO,EAAEF,WAAW,CAACE,OAAQ;YAC7BC,QAAQ,EAAEH,WAAW,CAACG,QAAS;YAC/BC,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,UAAU,EAAE,YAAY;cACxBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAvB,QAAA,gBAEFjD,OAAA,CAACb,QAAQ;cAAC4E,KAAK,EAAE;gBAAEI,QAAQ,EAAE;cAAO;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKC,CAAC,eACNtD,OAAA;QAAKkD,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAC;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEzCtD,OAAA;QAAM2C,QAAQ,EAAEnB,YAAY,CAACoB,uBAAuB,CAAE;QAAAK,QAAA,GAEnD,CAACvB,MAAM,CAACtB,KAAK,IAAIsB,MAAM,CAACnB,QAAQ,IAAIc,UAAU,kBAC7CrB,OAAA;UAAKkD,SAAS,EAAC,qBAAqB;UAAAD,QAAA,GACjC,EAAAtC,cAAA,GAAAe,MAAM,CAACtB,KAAK,cAAAO,cAAA,uBAAZA,cAAA,CAAc8B,OAAO,kBAAIzC,OAAA;YAAAiD,QAAA,EAAMvB,MAAM,CAACtB,KAAK,CAACqC;UAAO;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC1D,EAAA1C,iBAAA,GAAAc,MAAM,CAACnB,QAAQ,cAAAK,iBAAA,uBAAfA,iBAAA,CAAiB6B,OAAO,kBAAIzC,OAAA;YAAAiD,QAAA,EAAMvB,MAAM,CAACnB,QAAQ,CAACkC;UAAO;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAChEjC,UAAU,iBAAIrB,OAAA;YAAAiD,QAAA,EAAM5B;UAAU;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACN,eAEDtD,OAAA;UACE4D,IAAI,EAAC,OAAO;UACZa,IAAI,EAAC,OAAO;UAAA,GACRlD,QAAQ,CAAC,OAAO,CAAC;UACrBmD,WAAW,EAAC,QAAQ;UACpBxB,SAAS,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAKFtD,OAAA;UAAK+D,KAAK,EAAE;YAAEY,QAAQ,EAAE;UAAW,CAAE;UAAA1B,QAAA,gBACnCjD,OAAA;YACE4D,IAAI,EAAE7C,YAAY,GAAG,MAAM,GAAG,UAAW;YACzC0D,IAAI,EAAC,UAAU;YAAA,GACXlD,QAAQ,CAAC,UAAU,CAAC;YACxBmD,WAAW,EAAC,UAAU;YACtBxB,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFtD,OAAA;YACE6D,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAAE4D,SAAS,IAAK,CAACA,SAAS,CAAE;YAC1Db,KAAK,EAAE;cACLY,QAAQ,EAAE,UAAU;cACpBE,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE,KAAK;cACVC,SAAS,EAAE,kBAAkB;cAC7BC,MAAM,EAAE,SAAS;cACjBC,KAAK,EAAE,SAAS;cAChBf,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,EAEDlC,YAAY,gBAAGf,OAAA,CAACX,qBAAqB;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtD,OAAA,CAACZ,YAAY;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNtD,OAAA;UACE6D,OAAO,EAAEA,CAAA,KAAM3C,2BAA2B,CAAC,IAAI,CAAE;UACjD6C,KAAK,EAAE;YACLY,QAAQ,EAAE,UAAU;YACpBE,KAAK,EAAE,MAAM;YACbV,QAAQ,EAAE,MAAM;YAChBc,KAAK,EAAE,SAAS;YAChBD,MAAM,EAAE,SAAS;YACjBd,UAAU,EAAE,YAAY;YAExB,2BAA2B,EAAE;cAC3BW,KAAK,EAAE;YACT;UACF,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAKPtD,OAAA;UACE4D,IAAI,EAAC,QAAQ;UACbV,SAAS,EAAC,gBAAgB;UAC1Ba,KAAK,EAAE;YACLmB,SAAS,EAAE,MAAM;YACjBC,YAAY,EAAE,OAAO;YACrB,2BAA2B,EAAE;cAC3BA,YAAY,EAAE;YAChB;UACF,CAAE;UAAAlC,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPtD,OAAA;QAAGkD,SAAS,EAAC,eAAe;QAAAD,QAAA,GAAC,gCACG,eAAAjD,OAAA;UAAGoF,IAAI,EAAC,SAAS;UAAAnC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACNtD,OAAA,CAACH,oBAAoB;MACnBwF,IAAI,EAAEpE,wBAAyB;MAC/BqE,OAAO,EAAEA,CAAA,KAAMpE,2BAA2B,CAAC,KAAK,CAAE;MAClDqE,MAAM,EAAEA,CAAA,KAAMnE,kCAAkC,CAAC,IAAI;IAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eACFtD,OAAA,CAACF,kBAAkB;MACjBuF,IAAI,EAAElE,+BAAgC;MACtCqE,KAAK,EAAC,iBAAiB;MACvBC,OAAO,EAAC,oDAAoD;MAC5DC,SAAS,EAAEA,CAAA,KAAMtE,kCAAkC,CAAC,KAAK,CAAE;MAC3DuE,QAAQ,EAAEA,CAAA,KAAMvE,kCAAkC,CAAC,KAAK;IAAE;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC5C,EAAA,CAzOQD,UAAU;EAAA,QACUhB,OAAO,EACjBD,WAAW,EAcxBE,OAAO;AAAA;AAAAkG,EAAA,GAhBJnF,UAAU;AA2OnB,eAAeA,UAAU;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}