{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\VendorProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography } from \"@mui/material\";\nimport VendorProfileHero from \"../Components/Vendor-Profile/Hero\";\nimport Header from \"../Components/navBar\";\nimport VendorProfileHeader from \"../Components/Vendor-Profile/profileheader\";\nimport VendorCatalogs from \"../Components/Vendor-Profile/Catalogs\";\nimport VendorCategoriesGrid from \"../Components/Vendor-Profile/Categories\";\nimport VendorsProductsGrid from \"../Components/Vendor-Profile/Products\";\nimport Footer from \"../Components/Footer\";\nimport LoadingScreen from \"./loadingScreen\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VendorProfile() {\n  _s();\n  const {\n    id\n  } = useParams(); // Get vendor ID from the URL\n  const [vendor, setVendor] = useState(null);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchVendor = async () => {\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/brand/${id}`);\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log(data);\n        setVendor(data);\n        setError(null);\n      } catch (error) {\n        console.error(\"Error fetching vendor data:\", error);\n        setError(\"Failed to load vendor data. Please try again later.\");\n        setVendor(null);\n      }\n    };\n    fetchVendor();\n  }, [id]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100vh\",\n        flexDirection: \"column\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        color: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 2\n        },\n        children: \"There was an issue fetching the vendor's details.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n  }\n  if (!vendor) {\n    return /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VendorProfileHero, {\n      vendor: vendor\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VendorProfileHeader, {\n      vendor: vendor\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), (vendor === null || vendor === void 0 ? void 0 : vendor._id) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(VendorCatalogs, {\n        vendorID: vendor._id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(VendorCategoriesGrid, {\n        vendor: vendor\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(VendorsProductsGrid, {\n        vendor: vendor\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(VendorProfile, \"KicI2PeMSaAonXSDMJhEy53iKPw=\", false, function () {\n  return [useParams];\n});\n_c = VendorProfile;\nexport default VendorProfile;\nvar _c;\n$RefreshReg$(_c, \"VendorProfile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Box", "Typography", "VendorProfileHero", "Header", "VendorProfileHeader", "VendorCatalogs", "VendorCategoriesGrid", "VendorsProductsGrid", "Footer", "LoadingScreen", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VendorProfile", "_s", "id", "vendor", "setVendor", "error", "setError", "fetchVendor", "response", "fetch", "ok", "Error", "status", "data", "json", "console", "log", "sx", "display", "justifyContent", "alignItems", "height", "flexDirection", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mt", "_id", "vendorID", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/VendorProfile.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { Box, Typography } from \"@mui/material\";\r\nimport VendorProfileHero from \"../Components/Vendor-Profile/Hero\";\r\nimport Header from \"../Components/navBar\";\r\nimport VendorProfileHeader from \"../Components/Vendor-Profile/profileheader\";\r\nimport VendorCatalogs from \"../Components/Vendor-Profile/Catalogs\";\r\nimport VendorCategoriesGrid from \"../Components/Vendor-Profile/Categories\";\r\nimport VendorsProductsGrid from \"../Components/Vendor-Profile/Products\";\r\nimport Footer from \"../Components/Footer\";\r\nimport LoadingScreen from \"./loadingScreen\";\r\n\r\nfunction VendorProfile() {\r\n  const { id } = useParams(); // Get vendor ID from the URL\r\n  const [vendor, setVendor] = useState(null);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const fetchVendor = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/brand/${id}`\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n        const data = await response.json();\r\n        console.log(data);\r\n        setVendor(data);\r\n        setError(null);\r\n      } catch (error) {\r\n        console.error(\"Error fetching vendor data:\", error);\r\n        setError(\"Failed to load vendor data. Please try again later.\");\r\n        setVendor(null);\r\n      }\r\n    };\r\n\r\n    fetchVendor();\r\n  }, [id]);\r\n\r\n  if (error) {\r\n    return (\r\n      <Box\r\n        sx={{\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"center\",\r\n          height: \"100vh\",\r\n          flexDirection: \"column\",\r\n        }}\r\n      >\r\n        <Typography variant=\"h5\" color=\"error\">\r\n          {error}\r\n        </Typography>\r\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mt: 2 }}>\r\n          There was an issue fetching the vendor's details.\r\n        </Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  if (!vendor) {\r\n    return <LoadingScreen />;\r\n  }\r\n\r\n  return (\r\n    <Box>\r\n      <Header />\r\n      <VendorProfileHero vendor={vendor} />\r\n      <VendorProfileHeader vendor={vendor} />\r\n      {vendor?._id && (\r\n        <>\r\n          <VendorCatalogs vendorID={vendor._id} />\r\n          <VendorCategoriesGrid vendor={vendor} />\r\n          <VendorsProductsGrid vendor={vendor} />\r\n        </>\r\n      )}\r\n      <Footer />\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default VendorProfile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAG,CAAC,GAAGjB,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACd,MAAMwB,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,2CAA2CP,EAAE,EAC/C,CAAC;QACD,IAAI,CAACM,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC3D;QACA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;QACjBT,SAAS,CAACS,IAAI,CAAC;QACfP,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdU,OAAO,CAACV,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDC,QAAQ,CAAC,qDAAqD,CAAC;QAC/DF,SAAS,CAAC,IAAI,CAAC;MACjB;IACF,CAAC;IAEDG,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACL,EAAE,CAAC,CAAC;EAER,IAAIG,KAAK,EAAE;IACT,oBACER,OAAA,CAACX,GAAG;MACF+B,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,aAAa,EAAE;MACjB,CAAE;MAAAC,QAAA,gBAEF1B,OAAA,CAACV,UAAU;QAACqC,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,OAAO;QAAAF,QAAA,EACnClB;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbhC,OAAA,CAACV,UAAU;QAACqC,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAACR,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,EAAC;MAElE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,IAAI,CAAC1B,MAAM,EAAE;IACX,oBAAON,OAAA,CAACF,aAAa;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;EAEA,oBACEhC,OAAA,CAACX,GAAG;IAAAqC,QAAA,gBACF1B,OAAA,CAACR,MAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVhC,OAAA,CAACT,iBAAiB;MAACe,MAAM,EAAEA;IAAO;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrChC,OAAA,CAACP,mBAAmB;MAACa,MAAM,EAAEA;IAAO;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACtC,CAAA1B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE4B,GAAG,kBACVlC,OAAA,CAAAE,SAAA;MAAAwB,QAAA,gBACE1B,OAAA,CAACN,cAAc;QAACyC,QAAQ,EAAE7B,MAAM,CAAC4B;MAAI;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxChC,OAAA,CAACL,oBAAoB;QAACW,MAAM,EAAEA;MAAO;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxChC,OAAA,CAACJ,mBAAmB;QAACU,MAAM,EAAEA;MAAO;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,eACvC,CACH,eACDhC,OAAA,CAACH,MAAM;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAC5B,EAAA,CApEQD,aAAa;EAAA,QACLf,SAAS;AAAA;AAAAgD,EAAA,GADjBjC,aAAa;AAsEtB,eAAeA,aAAa;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}