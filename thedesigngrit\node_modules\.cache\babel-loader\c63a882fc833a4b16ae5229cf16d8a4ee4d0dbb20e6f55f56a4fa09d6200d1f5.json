{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { Suspense, lazy, useTransition, useEffect, useState } from \"react\";\nimport { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\nimport { GoogleOAuthProvider } from \"@react-oauth/google\";\nimport { CartProvider } from \"./Context/cartcontext\";\nimport { UserProvider } from \"./utils/userContext\";\nimport { VendorProvider } from \"./utils/vendorContext\";\nimport ScrollToTop from \"./Context/scrollToTop\";\nimport { Analytics } from \"@vercel/analytics/react\";\nimport LoadingScreen from \"./Pages/loadingScreen\";\nimport ReadyToShip from \"./Pages/ReadyToship\";\nimport { AdminProvider } from \"./utils/adminContext\";\nimport OnSale from \"./Pages/onSale\";\nimport { FavoritesProvider } from \"./Components/favoriteOverlay\";\nimport Home from \"./Pages/home\";\nimport { HelmetProvider } from \"react-helmet-async\";\n\n// Safari-compatible lazy loading with retry mechanism\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst createSafeLazy = (importFn, componentName) => {\n  return /*#__PURE__*/lazy(() => importFn().catch(error => {\n    console.warn(`Failed to load ${componentName}, retrying...`, error);\n    // Safari-specific retry with delay\n    return new Promise(resolve => {\n      setTimeout(() => {\n        resolve(importFn());\n      }, 100);\n    }).catch(retryError => {\n      console.error(`Failed to load ${componentName} after retry:`, retryError);\n      // Fallback component\n      return {\n        default: () => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            height: \"100vh\",\n            fontFamily: \"Montserrat, sans-serif\",\n            textAlign: \"center\",\n            padding: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [\"Unable to load \", componentName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please refresh the page or check your connection.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.reload(),\n            style: {\n              padding: \"12px 24px\",\n              marginTop: \"16px\",\n              backgroundColor: \"#007bff\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              fontSize: \"16px\"\n            },\n            children: \"Refresh Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)\n      };\n    });\n  }));\n};\n\n// Enhanced lazy loading with Safari fixes\n// Instead of lazy loading Home, import it directly\nconst LoginPage = createSafeLazy(_c = () => import(\"./Pages/login\"), \"LoginPage\");\n_c2 = LoginPage;\nconst SignUpPage = createSafeLazy(_c3 = () => import(\"./Pages/signup\"), \"SignUpPage\");\n_c4 = SignUpPage;\nconst AboutUsPage = createSafeLazy(_c5 = () => import(\"./Pages/aboutUs\"), \"AboutUsPage\");\n_c6 = AboutUsPage;\nconst ContactUs = createSafeLazy(_c7 = () => import(\"./Pages/ContactUs\"), \"ContactUs\");\n_c8 = ContactUs;\nconst Vendorspage = createSafeLazy(_c9 = () => import(\"./Pages/Vendorspage\"), \"Vendorspage\");\n_c10 = Vendorspage;\nconst VendorProfile = createSafeLazy(_c11 = () => import(\"./Pages/VendorProfile\"), \"VendorProfile\");\n_c12 = VendorProfile;\nconst ShoppingCart = createSafeLazy(_c13 = () => import(\"./Pages/ShoppingCart\"), \"ShoppingCart\");\n_c14 = ShoppingCart;\nconst CheckoutPage = createSafeLazy(_c15 = () => import(\"./Pages/Checkout\"), \"CheckoutPage\");\n_c16 = CheckoutPage;\nconst CareersPage = createSafeLazy(_c17 = () => import(\"./Pages/careers\"), \"CareersPage\");\n_c18 = CareersPage;\nconst FAQs = createSafeLazy(_c19 = () => import(\"./Pages/FAQs\"), \"FAQs\");\n_c20 = FAQs;\nconst TrackOrder = createSafeLazy(_c21 = () => import(\"./Pages/TrackOrder\"), \"TrackOrder\");\n_c22 = TrackOrder;\nconst MyAccount = createSafeLazy(_c23 = () => import(\"./Pages/myAccount\"), \"MyAccount\");\n_c24 = MyAccount;\nconst UserProfile = createSafeLazy(_c25 = () => import(\"./Pages/userss\"), \"UserProfile\");\n_c26 = UserProfile;\nconst ProductPage = createSafeLazy(_c27 = () => import(\"./Pages/ProductPage\"), \"ProductPage\");\n_c28 = ProductPage;\nconst ProductsPage = createSafeLazy(_c29 = () => import(\"./Pages/ProductsPage\"), \"ProductsPage\");\n_c30 = ProductsPage;\nconst Subcategories = createSafeLazy(_c31 = () => import(\"./Pages/subcategories\"), \"Subcategories\");\n_c32 = Subcategories;\nconst TypesPage = createSafeLazy(_c33 = () => import(\"./Pages/types\"), \"TypesPage\");\n_c34 = TypesPage;\nconst TermsOfService = createSafeLazy(_c35 = () => import(\"./Pages/Policy\"), \"TermsOfService\");\n_c36 = TermsOfService;\nconst JobDesc = createSafeLazy(_c37 = () => import(\"./Pages/JobDescription\"), \"JobDescription\");\n_c38 = JobDesc;\nconst PartnersApplication = createSafeLazy(_c39 = () => import(\"./Pages/Partners\"), \"PartnersApplication\");\n_c40 = PartnersApplication;\nconst AdminLogin = createSafeLazy(_c41 = () => import(\"./Components/adminSide/AdminLogin\"), \"AdminLogin\");\n_c42 = AdminLogin;\nconst PrivateRouteAdmin = createSafeLazy(_c43 = () => import(\"./utils/PrivateRouteAdmin\"), \"PrivateRouteAdmin\");\n\n// Vendor routes with Safari-compatible lazy loading\n_c44 = PrivateRouteAdmin;\nconst VendorHome = createSafeLazy(_c45 = () => import(\"./Pages/vendorSide/VendorHome\"), \"VendorHome\");\n_c46 = VendorHome;\nconst OrderDetails = createSafeLazy(_c47 = () => import(\"./Components/vendorSide/orderDetails\"), \"OrderDetails\");\n_c48 = OrderDetails;\nconst UpdateProductForm = createSafeLazy(_c49 = () => import(\"./Components/vendorSide/UpdateProduct\"), \"UpdateProductForm\");\n_c50 = UpdateProductForm;\nconst AdminHome = createSafeLazy(_c51 = () => import(\"./Pages/vendorSide/AdminHome\"), \"AdminHome\");\n_c52 = AdminHome;\nconst NotificationsPage = createSafeLazy(_c53 = () => import(\"./Components/vendorSide/notificationPage\"), \"NotificationsPage\");\n_c54 = NotificationsPage;\nconst SigninVendor = createSafeLazy(_c55 = () => import(\"./Components/vendorSide/signinVendor\"), \"SigninVendor\");\n_c56 = SigninVendor;\nconst EditEmployee = createSafeLazy(_c57 = () => import(\"./Components/vendorSide/editEmployee\"), \"EditEmployee\");\n_c58 = EditEmployee;\nconst BrandForm = createSafeLazy(_c59 = () => import(\"./Components/vendorSide/addbrand\"), \"BrandForm\");\n_c60 = BrandForm;\nconst SignupVendor = createSafeLazy(_c61 = () => import(\"./Components/vendorSide/SignupVendor\"), \"SignupVendor\");\n_c62 = SignupVendor;\nconst VerifyPartners = createSafeLazy(_c63 = () => import(\"./Components/adminSide/VerifyPartners\"), \"VerifyPartners\");\n\n// Enhanced loading screen with transition support\n_c64 = VerifyPartners;\nconst TransitionAwareLoadingScreen = () => {\n  _s();\n  const [isPending] = useTransition();\n  const [showFallback, setShowFallback] = useState(false);\n  useEffect(() => {\n    setShowFallback(true);\n  }, []);\n  if (!showFallback && !isPending) return null;\n  return /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 10\n  }, this);\n};\n\n// Safari-specific navigation wrapper\n_s(TransitionAwareLoadingScreen, \"HxDO36Zf/H840QLkoeBD0vfI4ow=\", false, function () {\n  return [useTransition];\n});\n_c65 = TransitionAwareLoadingScreen;\nconst SafariNavigationWrapper = ({\n  children\n}) => {\n  _s2();\n  const [startTransition] = useTransition();\n  useEffect(() => {\n    // Handle Safari's bfcache (back-forward cache) issues\n    const handlePageShow = event => {\n      if (event.persisted) {\n        window.location.reload();\n      }\n    };\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        // Safari sometimes needs a nudge when returning to the tab\n        startTransition(() => {\n          // Force a re-render without full reload\n          setTimeout(() => {\n            // Nudge Safari\n          }, 0);\n        });\n      }\n    };\n    window.addEventListener(\"pageshow\", handlePageShow);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    return () => {\n      window.removeEventListener(\"pageshow\", handlePageShow);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n    };\n  }, [startTransition]);\n  return children;\n};\n\n// Route wrapper with transition support\n_s2(SafariNavigationWrapper, \"Bz5SaOMjzfuGjwimxpkr1fbsjws=\", false, function () {\n  return [useTransition];\n});\n_c66 = SafariNavigationWrapper;\nconst TransitionRoute = ({\n  element\n}) => {\n  return /*#__PURE__*/_jsxDEV(Suspense, {\n    fallback: /*#__PURE__*/_jsxDEV(TransitionAwareLoadingScreen, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 25\n    }, this),\n    children: element\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n\n// Main routes component\n_c67 = TransitionRoute;\nconst AppRoutes = () => {\n  return /*#__PURE__*/_jsxDEV(SafariNavigationWrapper, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/home\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signup\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(SignUpPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/vendors\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(Vendorspage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(AboutUsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/mycart\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/careers\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(CareersPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/contactus\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(ContactUs, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/policy\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(TermsOfService, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/product/:id\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(ProductPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/ProductsPage\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/vendor/:id\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(VendorProfile, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/checkout\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/jobdesc/:jobId\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(JobDesc, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/policy/:section\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(TermsOfService, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/products/:typeId/:typeName\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/products/readytoship\",\n        element: /*#__PURE__*/_jsxDEV(ReadyToShip, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 54\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/products/onsale\",\n        element: /*#__PURE__*/_jsxDEV(OnSale, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/partners\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(PartnersApplication, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/faqs\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(FAQs, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/trackorder\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(TrackOrder, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/myaccount/:section?\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(MyAccount, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/usersss\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/category/:categoryId/subcategories\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(Subcategories, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/types/:subCategoryId\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(TypesPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/vendor-dashboard/:vendorId\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(VendorHome, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/orderDetail/:id\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(OrderDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/update-product\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(UpdateProductForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin-login\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/adminpanel\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(PrivateRouteAdmin, {\n            children: /*#__PURE__*/_jsxDEV(AdminHome, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/verify-partner\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(VerifyPartners, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/notifications\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(NotificationsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signin-vendor\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(SigninVendor, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signupvendor\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(SignupVendor, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/addbrand\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(BrandForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/edit-employee/:id\",\n        element: /*#__PURE__*/_jsxDEV(TransitionRoute, {\n          element: /*#__PURE__*/_jsxDEV(EditEmployee, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_c68 = AppRoutes;\nfunction App() {\n  _s3();\n  useEffect(() => {\n    // Safari-specific initialization\n    if (typeof window !== \"undefined\") {\n      // Prevent Safari from caching dynamic imports\n      if (\"serviceWorker\" in navigator) {\n        navigator.serviceWorker.getRegistrations().then(registrations => {\n          registrations.forEach(registration => {\n            registration.update();\n          });\n        });\n      }\n\n      // Handle Safari's module loading issues\n      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n      if (isSafari) {\n        // Force Safari to handle dynamic imports properly\n        window.addEventListener(\"beforeunload\", () => {\n          // Clear any pending module loads\n          if (window.__webpack_require__) {\n            window.__webpack_require__.cache = {};\n          }\n        });\n      }\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(HelmetProvider, {\n    children: /*#__PURE__*/_jsxDEV(GoogleOAuthProvider, {\n      clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n      children: /*#__PURE__*/_jsxDEV(UserProvider, {\n        children: /*#__PURE__*/_jsxDEV(VendorProvider, {\n          children: /*#__PURE__*/_jsxDEV(AdminProvider, {\n            children: /*#__PURE__*/_jsxDEV(CartProvider, {\n              children: /*#__PURE__*/_jsxDEV(FavoritesProvider, {\n                children: /*#__PURE__*/_jsxDEV(Router, {\n                  children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 440,\n    columnNumber: 5\n  }, this);\n}\n_s3(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c69 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58, _c59, _c60, _c61, _c62, _c63, _c64, _c65, _c66, _c67, _c68, _c69;\n$RefreshReg$(_c, \"LoginPage$createSafeLazy\");\n$RefreshReg$(_c2, \"LoginPage\");\n$RefreshReg$(_c3, \"SignUpPage$createSafeLazy\");\n$RefreshReg$(_c4, \"SignUpPage\");\n$RefreshReg$(_c5, \"AboutUsPage$createSafeLazy\");\n$RefreshReg$(_c6, \"AboutUsPage\");\n$RefreshReg$(_c7, \"ContactUs$createSafeLazy\");\n$RefreshReg$(_c8, \"ContactUs\");\n$RefreshReg$(_c9, \"Vendorspage$createSafeLazy\");\n$RefreshReg$(_c10, \"Vendorspage\");\n$RefreshReg$(_c11, \"VendorProfile$createSafeLazy\");\n$RefreshReg$(_c12, \"VendorProfile\");\n$RefreshReg$(_c13, \"ShoppingCart$createSafeLazy\");\n$RefreshReg$(_c14, \"ShoppingCart\");\n$RefreshReg$(_c15, \"CheckoutPage$createSafeLazy\");\n$RefreshReg$(_c16, \"CheckoutPage\");\n$RefreshReg$(_c17, \"CareersPage$createSafeLazy\");\n$RefreshReg$(_c18, \"CareersPage\");\n$RefreshReg$(_c19, \"FAQs$createSafeLazy\");\n$RefreshReg$(_c20, \"FAQs\");\n$RefreshReg$(_c21, \"TrackOrder$createSafeLazy\");\n$RefreshReg$(_c22, \"TrackOrder\");\n$RefreshReg$(_c23, \"MyAccount$createSafeLazy\");\n$RefreshReg$(_c24, \"MyAccount\");\n$RefreshReg$(_c25, \"UserProfile$createSafeLazy\");\n$RefreshReg$(_c26, \"UserProfile\");\n$RefreshReg$(_c27, \"ProductPage$createSafeLazy\");\n$RefreshReg$(_c28, \"ProductPage\");\n$RefreshReg$(_c29, \"ProductsPage$createSafeLazy\");\n$RefreshReg$(_c30, \"ProductsPage\");\n$RefreshReg$(_c31, \"Subcategories$createSafeLazy\");\n$RefreshReg$(_c32, \"Subcategories\");\n$RefreshReg$(_c33, \"TypesPage$createSafeLazy\");\n$RefreshReg$(_c34, \"TypesPage\");\n$RefreshReg$(_c35, \"TermsOfService$createSafeLazy\");\n$RefreshReg$(_c36, \"TermsOfService\");\n$RefreshReg$(_c37, \"JobDesc$createSafeLazy\");\n$RefreshReg$(_c38, \"JobDesc\");\n$RefreshReg$(_c39, \"PartnersApplication$createSafeLazy\");\n$RefreshReg$(_c40, \"PartnersApplication\");\n$RefreshReg$(_c41, \"AdminLogin$createSafeLazy\");\n$RefreshReg$(_c42, \"AdminLogin\");\n$RefreshReg$(_c43, \"PrivateRouteAdmin$createSafeLazy\");\n$RefreshReg$(_c44, \"PrivateRouteAdmin\");\n$RefreshReg$(_c45, \"VendorHome$createSafeLazy\");\n$RefreshReg$(_c46, \"VendorHome\");\n$RefreshReg$(_c47, \"OrderDetails$createSafeLazy\");\n$RefreshReg$(_c48, \"OrderDetails\");\n$RefreshReg$(_c49, \"UpdateProductForm$createSafeLazy\");\n$RefreshReg$(_c50, \"UpdateProductForm\");\n$RefreshReg$(_c51, \"AdminHome$createSafeLazy\");\n$RefreshReg$(_c52, \"AdminHome\");\n$RefreshReg$(_c53, \"NotificationsPage$createSafeLazy\");\n$RefreshReg$(_c54, \"NotificationsPage\");\n$RefreshReg$(_c55, \"SigninVendor$createSafeLazy\");\n$RefreshReg$(_c56, \"SigninVendor\");\n$RefreshReg$(_c57, \"EditEmployee$createSafeLazy\");\n$RefreshReg$(_c58, \"EditEmployee\");\n$RefreshReg$(_c59, \"BrandForm$createSafeLazy\");\n$RefreshReg$(_c60, \"BrandForm\");\n$RefreshReg$(_c61, \"SignupVendor$createSafeLazy\");\n$RefreshReg$(_c62, \"SignupVendor\");\n$RefreshReg$(_c63, \"VerifyPartners$createSafeLazy\");\n$RefreshReg$(_c64, \"VerifyPartners\");\n$RefreshReg$(_c65, \"TransitionAwareLoadingScreen\");\n$RefreshReg$(_c66, \"SafariNavigationWrapper\");\n$RefreshReg$(_c67, \"TransitionRoute\");\n$RefreshReg$(_c68, \"AppRoutes\");\n$RefreshReg$(_c69, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "useTransition", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "GoogleOAuthProvider", "CartProvider", "UserProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScrollToTop", "Analytics", "LoadingScreen", "ReadyToShip", "Admin<PERSON><PERSON><PERSON>", "OnSale", "FavoritesProvider", "Home", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "createSafeLazy", "importFn", "componentName", "catch", "error", "console", "warn", "Promise", "resolve", "setTimeout", "retryError", "default", "style", "display", "flexDirection", "justifyContent", "alignItems", "height", "fontFamily", "textAlign", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "marginTop", "backgroundColor", "color", "border", "borderRadius", "cursor", "fontSize", "LoginPage", "_c", "_c2", "SignUpPage", "_c3", "_c4", "AboutUsPage", "_c5", "_c6", "ContactUs", "_c7", "_c8", "Vendorspage", "_c9", "_c10", "VendorProfile", "_c11", "_c12", "ShoppingCart", "_c13", "_c14", "CheckoutPage", "_c15", "_c16", "CareersPage", "_c17", "_c18", "FAQs", "_c19", "_c20", "TrackOrder", "_c21", "_c22", "MyAccount", "_c23", "_c24", "UserProfile", "_c25", "_c26", "ProductPage", "_c27", "_c28", "ProductsPage", "_c29", "_c30", "Subcategories", "_c31", "_c32", "TypesPage", "_c33", "_c34", "TermsOfService", "_c35", "_c36", "JobDesc", "_c37", "_c38", "PartnersApplication", "_c39", "_c40", "AdminLogin", "_c41", "_c42", "PrivateRouteAdmin", "_c43", "_c44", "VendorHome", "_c45", "_c46", "OrderDetails", "_c47", "_c48", "UpdateProductForm", "_c49", "_c50", "AdminHome", "_c51", "_c52", "NotificationsPage", "_c53", "_c54", "SigninVendor", "_c55", "_c56", "EditEmployee", "_c57", "_c58", "BrandForm", "_c59", "_c60", "SignupVendor", "_c61", "_c62", "VerifyPartners", "_c63", "_c64", "TransitionAwareLoadingScreen", "_s", "isPending", "showFallback", "setShowFallback", "_c65", "SafariNavigationWrapper", "_s2", "startTransition", "handlePageShow", "event", "persisted", "handleVisibilityChange", "document", "visibilityState", "addEventListener", "removeEventListener", "_c66", "TransitionRoute", "element", "fallback", "_c67", "AppRoutes", "path", "_c68", "App", "_s3", "navigator", "serviceWorker", "getRegistrations", "then", "registrations", "for<PERSON>ach", "registration", "update", "<PERSON><PERSON><PERSON><PERSON>", "test", "userAgent", "__webpack_require__", "cache", "clientId", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "_c69", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/App.js"], "sourcesContent": ["import React, {\r\n  Suspense,\r\n  lazy,\r\n  useTransition,\r\n  useEffect,\r\n  useState,\r\n} from \"react\";\r\nimport { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\r\nimport { GoogleOAuthProvider } from \"@react-oauth/google\";\r\nimport { CartProvider } from \"./Context/cartcontext\";\r\nimport { UserProvider } from \"./utils/userContext\";\r\nimport { VendorProvider } from \"./utils/vendorContext\";\r\nimport ScrollToTop from \"./Context/scrollToTop\";\r\nimport { Analytics } from \"@vercel/analytics/react\";\r\nimport LoadingScreen from \"./Pages/loadingScreen\";\r\nimport ReadyToShip from \"./Pages/ReadyToship\";\r\nimport { AdminProvider } from \"./utils/adminContext\";\r\nimport OnSale from \"./Pages/onSale\";\r\nimport { FavoritesProvider } from \"./Components/favoriteOverlay\";\r\nimport Home from \"./Pages/home\";\r\nimport { HelmetProvider } from \"react-helmet-async\";\r\n\r\n// Safari-compatible lazy loading with retry mechanism\r\nconst createSafeLazy = (importFn, componentName) => {\r\n  return lazy(() =>\r\n    importFn().catch((error) => {\r\n      console.warn(`Failed to load ${componentName}, retrying...`, error);\r\n      // Safari-specific retry with delay\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve(importFn());\r\n        }, 100);\r\n      }).catch((retryError) => {\r\n        console.error(\r\n          `Failed to load ${componentName} after retry:`,\r\n          retryError\r\n        );\r\n        // Fallback component\r\n        return {\r\n          default: () => (\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                flexDirection: \"column\",\r\n                justifyContent: \"center\",\r\n                alignItems: \"center\",\r\n                height: \"100vh\",\r\n                fontFamily: \"Montserrat, sans-serif\",\r\n                textAlign: \"center\",\r\n                padding: \"20px\",\r\n              }}\r\n            >\r\n              <h2>Unable to load {componentName}</h2>\r\n              <p>Please refresh the page or check your connection.</p>\r\n              <button\r\n                onClick={() => window.location.reload()}\r\n                style={{\r\n                  padding: \"12px 24px\",\r\n                  marginTop: \"16px\",\r\n                  backgroundColor: \"#007bff\",\r\n                  color: \"white\",\r\n                  border: \"none\",\r\n                  borderRadius: \"4px\",\r\n                  cursor: \"pointer\",\r\n                  fontSize: \"16px\",\r\n                }}\r\n              >\r\n                Refresh Page\r\n              </button>\r\n            </div>\r\n          ),\r\n        };\r\n      });\r\n    })\r\n  );\r\n};\r\n\r\n// Enhanced lazy loading with Safari fixes\r\n// Instead of lazy loading Home, import it directly\r\nconst LoginPage = createSafeLazy(() => import(\"./Pages/login\"), \"LoginPage\");\r\nconst SignUpPage = createSafeLazy(() => import(\"./Pages/signup\"), \"SignUpPage\");\r\nconst AboutUsPage = createSafeLazy(\r\n  () => import(\"./Pages/aboutUs\"),\r\n  \"AboutUsPage\"\r\n);\r\nconst ContactUs = createSafeLazy(\r\n  () => import(\"./Pages/ContactUs\"),\r\n  \"ContactUs\"\r\n);\r\nconst Vendorspage = createSafeLazy(\r\n  () => import(\"./Pages/Vendorspage\"),\r\n  \"Vendorspage\"\r\n);\r\nconst VendorProfile = createSafeLazy(\r\n  () => import(\"./Pages/VendorProfile\"),\r\n  \"VendorProfile\"\r\n);\r\nconst ShoppingCart = createSafeLazy(\r\n  () => import(\"./Pages/ShoppingCart\"),\r\n  \"ShoppingCart\"\r\n);\r\nconst CheckoutPage = createSafeLazy(\r\n  () => import(\"./Pages/Checkout\"),\r\n  \"CheckoutPage\"\r\n);\r\nconst CareersPage = createSafeLazy(\r\n  () => import(\"./Pages/careers\"),\r\n  \"CareersPage\"\r\n);\r\nconst FAQs = createSafeLazy(() => import(\"./Pages/FAQs\"), \"FAQs\");\r\nconst TrackOrder = createSafeLazy(\r\n  () => import(\"./Pages/TrackOrder\"),\r\n  \"TrackOrder\"\r\n);\r\nconst MyAccount = createSafeLazy(\r\n  () => import(\"./Pages/myAccount\"),\r\n  \"MyAccount\"\r\n);\r\nconst UserProfile = createSafeLazy(\r\n  () => import(\"./Pages/userss\"),\r\n  \"UserProfile\"\r\n);\r\nconst ProductPage = createSafeLazy(\r\n  () => import(\"./Pages/ProductPage\"),\r\n  \"ProductPage\"\r\n);\r\nconst ProductsPage = createSafeLazy(\r\n  () => import(\"./Pages/ProductsPage\"),\r\n  \"ProductsPage\"\r\n);\r\nconst Subcategories = createSafeLazy(\r\n  () => import(\"./Pages/subcategories\"),\r\n  \"Subcategories\"\r\n);\r\nconst TypesPage = createSafeLazy(() => import(\"./Pages/types\"), \"TypesPage\");\r\nconst TermsOfService = createSafeLazy(\r\n  () => import(\"./Pages/Policy\"),\r\n  \"TermsOfService\"\r\n);\r\nconst JobDesc = createSafeLazy(\r\n  () => import(\"./Pages/JobDescription\"),\r\n  \"JobDescription\"\r\n);\r\nconst PartnersApplication = createSafeLazy(\r\n  () => import(\"./Pages/Partners\"),\r\n  \"PartnersApplication\"\r\n);\r\nconst AdminLogin = createSafeLazy(\r\n  () => import(\"./Components/adminSide/AdminLogin\"),\r\n  \"AdminLogin\"\r\n);\r\nconst PrivateRouteAdmin = createSafeLazy(\r\n  () => import(\"./utils/PrivateRouteAdmin\"),\r\n  \"PrivateRouteAdmin\"\r\n);\r\n\r\n// Vendor routes with Safari-compatible lazy loading\r\nconst VendorHome = createSafeLazy(\r\n  () => import(\"./Pages/vendorSide/VendorHome\"),\r\n  \"VendorHome\"\r\n);\r\nconst OrderDetails = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/orderDetails\"),\r\n  \"OrderDetails\"\r\n);\r\nconst UpdateProductForm = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/UpdateProduct\"),\r\n  \"UpdateProductForm\"\r\n);\r\nconst AdminHome = createSafeLazy(\r\n  () => import(\"./Pages/vendorSide/AdminHome\"),\r\n  \"AdminHome\"\r\n);\r\nconst NotificationsPage = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/notificationPage\"),\r\n  \"NotificationsPage\"\r\n);\r\nconst SigninVendor = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/signinVendor\"),\r\n  \"SigninVendor\"\r\n);\r\nconst EditEmployee = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/editEmployee\"),\r\n  \"EditEmployee\"\r\n);\r\nconst BrandForm = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/addbrand\"),\r\n  \"BrandForm\"\r\n);\r\nconst SignupVendor = createSafeLazy(\r\n  () => import(\"./Components/vendorSide/SignupVendor\"),\r\n  \"SignupVendor\"\r\n);\r\nconst VerifyPartners = createSafeLazy(\r\n  () => import(\"./Components/adminSide/VerifyPartners\"),\r\n  \"VerifyPartners\"\r\n);\r\n\r\n// Enhanced loading screen with transition support\r\nconst TransitionAwareLoadingScreen = () => {\r\n  const [isPending] = useTransition();\r\n  const [showFallback, setShowFallback] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setShowFallback(true);\r\n  }, []);\r\n\r\n  if (!showFallback && !isPending) return null;\r\n\r\n  return <LoadingScreen />;\r\n};\r\n\r\n// Safari-specific navigation wrapper\r\nconst SafariNavigationWrapper = ({ children }) => {\r\n  const [startTransition] = useTransition();\r\n\r\n  useEffect(() => {\r\n    // Handle Safari's bfcache (back-forward cache) issues\r\n    const handlePageShow = (event) => {\r\n      if (event.persisted) {\r\n        window.location.reload();\r\n      }\r\n    };\r\n\r\n    const handleVisibilityChange = () => {\r\n      if (document.visibilityState === \"visible\") {\r\n        // Safari sometimes needs a nudge when returning to the tab\r\n        startTransition(() => {\r\n          // Force a re-render without full reload\r\n          setTimeout(() => {\r\n            // Nudge Safari\r\n          }, 0);\r\n        });\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"pageshow\", handlePageShow);\r\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"pageshow\", handlePageShow);\r\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n    };\r\n  }, [startTransition]);\r\n\r\n  return children;\r\n};\r\n\r\n// Route wrapper with transition support\r\nconst TransitionRoute = ({ element }) => {\r\n  return (\r\n    <Suspense fallback={<TransitionAwareLoadingScreen />}>{element}</Suspense>\r\n  );\r\n};\r\n\r\n// Main routes component\r\nconst AppRoutes = () => {\r\n  return (\r\n    <SafariNavigationWrapper>\r\n      <Routes>\r\n        {/* Public Routes */}\r\n        <Route path=\"/\" element={<Home />} />\r\n        <Route path=\"/home\" element={<TransitionRoute element={<Home />} />} />\r\n        <Route\r\n          path=\"/login\"\r\n          element={<TransitionRoute element={<LoginPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/signup\"\r\n          element={<TransitionRoute element={<SignUpPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/vendors\"\r\n          element={<TransitionRoute element={<Vendorspage />} />}\r\n        />\r\n        <Route\r\n          path=\"/about\"\r\n          element={<TransitionRoute element={<AboutUsPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/mycart\"\r\n          element={<TransitionRoute element={<ShoppingCart />} />}\r\n        />\r\n        <Route\r\n          path=\"/careers\"\r\n          element={<TransitionRoute element={<CareersPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/contactus\"\r\n          element={<TransitionRoute element={<ContactUs />} />}\r\n        />\r\n        <Route\r\n          path=\"/policy\"\r\n          element={<TransitionRoute element={<TermsOfService />} />}\r\n        />\r\n        <Route\r\n          path=\"/product/:id\"\r\n          element={<TransitionRoute element={<ProductPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/ProductsPage\"\r\n          element={<TransitionRoute element={<ProductsPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/vendor/:id\"\r\n          element={<TransitionRoute element={<VendorProfile />} />}\r\n        />\r\n        <Route\r\n          path=\"/checkout\"\r\n          element={<TransitionRoute element={<CheckoutPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/jobdesc/:jobId\"\r\n          element={<TransitionRoute element={<JobDesc />} />}\r\n        />\r\n        <Route\r\n          path=\"/policy/:section\"\r\n          element={<TransitionRoute element={<TermsOfService />} />}\r\n        />\r\n        <Route\r\n          path=\"/products/:typeId/:typeName\"\r\n          element={<TransitionRoute element={<ProductsPage />} />}\r\n        />\r\n        <Route path=\"/products/readytoship\" element={<ReadyToShip />} />\r\n        <Route path=\"/products/onsale\" element={<OnSale />} />\r\n        <Route\r\n          path=\"/partners\"\r\n          element={<TransitionRoute element={<PartnersApplication />} />}\r\n        />\r\n        <Route path=\"/faqs\" element={<TransitionRoute element={<FAQs />} />} />\r\n        <Route\r\n          path=\"/trackorder\"\r\n          element={<TransitionRoute element={<TrackOrder />} />}\r\n        />\r\n        <Route\r\n          path=\"/myaccount/:section?\"\r\n          element={<TransitionRoute element={<MyAccount />} />}\r\n        />\r\n        <Route\r\n          path=\"/usersss\"\r\n          element={<TransitionRoute element={<UserProfile />} />}\r\n        />\r\n        <Route\r\n          path=\"/category/:categoryId/subcategories\"\r\n          element={<TransitionRoute element={<Subcategories />} />}\r\n        />\r\n        <Route\r\n          path=\"/types/:subCategoryId\"\r\n          element={<TransitionRoute element={<TypesPage />} />}\r\n        />\r\n\r\n        {/* Vendor Routes */}\r\n        <Route\r\n          path=\"/vendor-dashboard/:vendorId\"\r\n          element={<TransitionRoute element={<VendorHome />} />}\r\n        />\r\n        <Route\r\n          path=\"/orderDetail/:id\"\r\n          element={<TransitionRoute element={<OrderDetails />} />}\r\n        />\r\n        <Route\r\n          path=\"/update-product\"\r\n          element={<TransitionRoute element={<UpdateProductForm />} />}\r\n        />\r\n        <Route\r\n          path=\"/admin-login\"\r\n          element={<TransitionRoute element={<AdminLogin />} />}\r\n        />\r\n        <Route\r\n          path=\"/adminpanel\"\r\n          element={\r\n            <TransitionRoute\r\n              element={\r\n                <PrivateRouteAdmin>\r\n                  <AdminHome />\r\n                </PrivateRouteAdmin>\r\n              }\r\n            />\r\n          }\r\n        />\r\n        <Route\r\n          path=\"/verify-partner\"\r\n          element={<TransitionRoute element={<VerifyPartners />} />}\r\n        />\r\n        <Route\r\n          path=\"/notifications\"\r\n          element={<TransitionRoute element={<NotificationsPage />} />}\r\n        />\r\n        <Route\r\n          path=\"/signin-vendor\"\r\n          element={<TransitionRoute element={<SigninVendor />} />}\r\n        />\r\n        <Route\r\n          path=\"/signupvendor\"\r\n          element={<TransitionRoute element={<SignupVendor />} />}\r\n        />\r\n        <Route\r\n          path=\"/addbrand\"\r\n          element={<TransitionRoute element={<BrandForm />} />}\r\n        />\r\n        <Route\r\n          path=\"/edit-employee/:id\"\r\n          element={<TransitionRoute element={<EditEmployee />} />}\r\n        />\r\n      </Routes>\r\n    </SafariNavigationWrapper>\r\n  );\r\n};\r\n\r\nfunction App() {\r\n  useEffect(() => {\r\n    // Safari-specific initialization\r\n    if (typeof window !== \"undefined\") {\r\n      // Prevent Safari from caching dynamic imports\r\n      if (\"serviceWorker\" in navigator) {\r\n        navigator.serviceWorker.getRegistrations().then((registrations) => {\r\n          registrations.forEach((registration) => {\r\n            registration.update();\r\n          });\r\n        });\r\n      }\r\n\r\n      // Handle Safari's module loading issues\r\n      const isSafari = /^((?!chrome|android).)*safari/i.test(\r\n        navigator.userAgent\r\n      );\r\n      if (isSafari) {\r\n        // Force Safari to handle dynamic imports properly\r\n        window.addEventListener(\"beforeunload\", () => {\r\n          // Clear any pending module loads\r\n          if (window.__webpack_require__) {\r\n            window.__webpack_require__.cache = {};\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <HelmetProvider>\r\n      <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID}>\r\n        <UserProvider>\r\n          <VendorProvider>\r\n            <AdminProvider>\r\n              <CartProvider>\r\n                <FavoritesProvider>\r\n                  <Router>\r\n                    <ScrollToTop />\r\n                    <AppRoutes />\r\n                    <Analytics />\r\n                  </Router>\r\n                </FavoritesProvider>\r\n              </CartProvider>\r\n            </AdminProvider>\r\n          </VendorProvider>\r\n        </UserProvider>\r\n      </GoogleOAuthProvider>\r\n    </HelmetProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IACVC,QAAQ,EACRC,IAAI,EACJC,aAAa,EACbC,SAAS,EACTC,QAAQ,QACH,OAAO;AACd,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,MAAM,MAAM,gBAAgB;AACnC,SAASC,iBAAiB,QAAQ,8BAA8B;AAChE,OAAOC,IAAI,MAAM,cAAc;AAC/B,SAASC,cAAc,QAAQ,oBAAoB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,aAAa,KAAK;EAClD,oBAAOzB,IAAI,CAAC,MACVwB,QAAQ,CAAC,CAAC,CAACE,KAAK,CAAEC,KAAK,IAAK;IAC1BC,OAAO,CAACC,IAAI,CAAC,kBAAkBJ,aAAa,eAAe,EAAEE,KAAK,CAAC;IACnE;IACA,OAAO,IAAIG,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAM;QACfD,OAAO,CAACP,QAAQ,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CAACE,KAAK,CAAEO,UAAU,IAAK;MACvBL,OAAO,CAACD,KAAK,CACX,kBAAkBF,aAAa,eAAe,EAC9CQ,UACF,CAAC;MACD;MACA,OAAO;QACLC,OAAO,EAAEA,CAAA,kBACPZ,OAAA;UACEa,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,cAAc,EAAE,QAAQ;YACxBC,UAAU,EAAE,QAAQ;YACpBC,MAAM,EAAE,OAAO;YACfC,UAAU,EAAE,wBAAwB;YACpCC,SAAS,EAAE,QAAQ;YACnBC,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,gBAEFtB,OAAA;YAAAsB,QAAA,GAAI,iBAAe,EAACnB,aAAa;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC1B,OAAA;YAAAsB,QAAA,EAAG;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxD1B,OAAA;YACE2B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCjB,KAAK,EAAE;cACLQ,OAAO,EAAE,WAAW;cACpBU,SAAS,EAAE,MAAM;cACjBC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBC,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAET,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CACH,CAAC;AACH,CAAC;;AAED;AACA;AACA,MAAMY,SAAS,GAAGrC,cAAc,CAAAsC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC,EAAE,WAAW,CAAC;AAACC,GAAA,GAAvEF,SAAS;AACf,MAAMG,UAAU,GAAGxC,cAAc,CAAAyC,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,EAAE,YAAY,CAAC;AAACC,GAAA,GAA1EF,UAAU;AAChB,MAAMG,WAAW,GAAG3C,cAAc,CAAA4C,GAAA,GAChCA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,EAC/B,aACF,CAAC;AAACC,GAAA,GAHIF,WAAW;AAIjB,MAAMG,SAAS,GAAG9C,cAAc,CAAA+C,GAAA,GAC9BA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,EACjC,WACF,CAAC;AAACC,GAAA,GAHIF,SAAS;AAIf,MAAMG,WAAW,GAAGjD,cAAc,CAAAkD,GAAA,GAChCA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,EACnC,aACF,CAAC;AAACC,IAAA,GAHIF,WAAW;AAIjB,MAAMG,aAAa,GAAGpD,cAAc,CAAAqD,IAAA,GAClCA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,EACrC,eACF,CAAC;AAACC,IAAA,GAHIF,aAAa;AAInB,MAAMG,YAAY,GAAGvD,cAAc,CAAAwD,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,EACpC,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,YAAY,GAAG1D,cAAc,CAAA2D,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,EAChC,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,WAAW,GAAG7D,cAAc,CAAA8D,IAAA,GAChCA,CAAA,KAAM,MAAM,CAAC,iBAAiB,CAAC,EAC/B,aACF,CAAC;AAACC,IAAA,GAHIF,WAAW;AAIjB,MAAMG,IAAI,GAAGhE,cAAc,CAAAiE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC;AAACC,IAAA,GAA5DF,IAAI;AACV,MAAMG,UAAU,GAAGnE,cAAc,CAAAoE,IAAA,GAC/BA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,EAClC,YACF,CAAC;AAACC,IAAA,GAHIF,UAAU;AAIhB,MAAMG,SAAS,GAAGtE,cAAc,CAAAuE,IAAA,GAC9BA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,EACjC,WACF,CAAC;AAACC,IAAA,GAHIF,SAAS;AAIf,MAAMG,WAAW,GAAGzE,cAAc,CAAA0E,IAAA,GAChCA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,EAC9B,aACF,CAAC;AAACC,IAAA,GAHIF,WAAW;AAIjB,MAAMG,WAAW,GAAG5E,cAAc,CAAA6E,IAAA,GAChCA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,EACnC,aACF,CAAC;AAACC,IAAA,GAHIF,WAAW;AAIjB,MAAMG,YAAY,GAAG/E,cAAc,CAAAgF,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,EACpC,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,aAAa,GAAGlF,cAAc,CAAAmF,IAAA,GAClCA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC,EACrC,eACF,CAAC;AAACC,IAAA,GAHIF,aAAa;AAInB,MAAMG,SAAS,GAAGrF,cAAc,CAAAsF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC,EAAE,WAAW,CAAC;AAACC,IAAA,GAAvEF,SAAS;AACf,MAAMG,cAAc,GAAGxF,cAAc,CAAAyF,IAAA,GACnCA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC,EAC9B,gBACF,CAAC;AAACC,IAAA,GAHIF,cAAc;AAIpB,MAAMG,OAAO,GAAG3F,cAAc,CAAA4F,IAAA,GAC5BA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,EACtC,gBACF,CAAC;AAACC,IAAA,GAHIF,OAAO;AAIb,MAAMG,mBAAmB,GAAG9F,cAAc,CAAA+F,IAAA,GACxCA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,EAChC,qBACF,CAAC;AAACC,IAAA,GAHIF,mBAAmB;AAIzB,MAAMG,UAAU,GAAGjG,cAAc,CAAAkG,IAAA,GAC/BA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,EACjD,YACF,CAAC;AAACC,IAAA,GAHIF,UAAU;AAIhB,MAAMG,iBAAiB,GAAGpG,cAAc,CAAAqG,IAAA,GACtCA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,EACzC,mBACF,CAAC;;AAED;AAAAC,IAAA,GALMF,iBAAiB;AAMvB,MAAMG,UAAU,GAAGvG,cAAc,CAAAwG,IAAA,GAC/BA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,EAC7C,YACF,CAAC;AAACC,IAAA,GAHIF,UAAU;AAIhB,MAAMG,YAAY,GAAG1G,cAAc,CAAA2G,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,EACpD,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,iBAAiB,GAAG7G,cAAc,CAAA8G,IAAA,GACtCA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,EACrD,mBACF,CAAC;AAACC,IAAA,GAHIF,iBAAiB;AAIvB,MAAMG,SAAS,GAAGhH,cAAc,CAAAiH,IAAA,GAC9BA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,EAC5C,WACF,CAAC;AAACC,IAAA,GAHIF,SAAS;AAIf,MAAMG,iBAAiB,GAAGnH,cAAc,CAAAoH,IAAA,GACtCA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,EACxD,mBACF,CAAC;AAACC,IAAA,GAHIF,iBAAiB;AAIvB,MAAMG,YAAY,GAAGtH,cAAc,CAAAuH,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,EACpD,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,YAAY,GAAGzH,cAAc,CAAA0H,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,EACpD,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,SAAS,GAAG5H,cAAc,CAAA6H,IAAA,GAC9BA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,EAChD,WACF,CAAC;AAACC,IAAA,GAHIF,SAAS;AAIf,MAAMG,YAAY,GAAG/H,cAAc,CAAAgI,IAAA,GACjCA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,EACpD,cACF,CAAC;AAACC,IAAA,GAHIF,YAAY;AAIlB,MAAMG,cAAc,GAAGlI,cAAc,CAAAmI,IAAA,GACnCA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,EACrD,gBACF,CAAC;;AAED;AAAAC,IAAA,GALMF,cAAc;AAMpB,MAAMG,4BAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,SAAS,CAAC,GAAG7J,aAAa,CAAC,CAAC;EACnC,MAAM,CAAC8J,YAAY,EAAEC,eAAe,CAAC,GAAG7J,QAAQ,CAAC,KAAK,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd8J,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAI,CAACD,YAAY,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;EAE5C,oBAAOxI,OAAA,CAACR,aAAa;IAAA+B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC1B,CAAC;;AAED;AAAA6G,EAAA,CAbMD,4BAA4B;EAAA,QACZ3J,aAAa;AAAA;AAAAgK,IAAA,GAD7BL,4BAA4B;AAclC,MAAMM,uBAAuB,GAAGA,CAAC;EAAEtH;AAAS,CAAC,KAAK;EAAAuH,GAAA;EAChD,MAAM,CAACC,eAAe,CAAC,GAAGnK,aAAa,CAAC,CAAC;EAEzCC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmK,cAAc,GAAIC,KAAK,IAAK;MAChC,IAAIA,KAAK,CAACC,SAAS,EAAE;QACnBrH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC;IAED,MAAMoH,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAIC,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;QAC1C;QACAN,eAAe,CAAC,MAAM;UACpB;UACApI,UAAU,CAAC,MAAM;YACf;UAAA,CACD,EAAE,CAAC,CAAC;QACP,CAAC,CAAC;MACJ;IACF,CAAC;IAEDkB,MAAM,CAACyH,gBAAgB,CAAC,UAAU,EAAEN,cAAc,CAAC;IACnDI,QAAQ,CAACE,gBAAgB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;IAErE,OAAO,MAAM;MACXtH,MAAM,CAAC0H,mBAAmB,CAAC,UAAU,EAAEP,cAAc,CAAC;MACtDI,QAAQ,CAACG,mBAAmB,CAAC,kBAAkB,EAAEJ,sBAAsB,CAAC;IAC1E,CAAC;EACH,CAAC,EAAE,CAACJ,eAAe,CAAC,CAAC;EAErB,OAAOxH,QAAQ;AACjB,CAAC;;AAED;AAAAuH,GAAA,CAnCMD,uBAAuB;EAAA,QACDjK,aAAa;AAAA;AAAA4K,IAAA,GADnCX,uBAAuB;AAoC7B,MAAMY,eAAe,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACvC,oBACEzJ,OAAA,CAACvB,QAAQ;IAACiL,QAAQ,eAAE1J,OAAA,CAACsI,4BAA4B;MAAA/G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAAAJ,QAAA,EAAEmI;EAAO;IAAAlI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAW,CAAC;AAE9E,CAAC;;AAED;AAAAiI,IAAA,GANMH,eAAe;AAOrB,MAAMI,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACE5J,OAAA,CAAC4I,uBAAuB;IAAAtH,QAAA,eACtBtB,OAAA,CAACf,MAAM;MAAAqC,QAAA,gBAELtB,OAAA,CAAChB,KAAK;QAAC6K,IAAI,EAAC,GAAG;QAACJ,OAAO,eAAEzJ,OAAA,CAACH,IAAI;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrC1B,OAAA,CAAChB,KAAK;QAAC6K,IAAI,EAAC,OAAO;QAACJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACH,IAAI;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvE1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,QAAQ;QACbJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACsC,SAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,SAAS;QACdJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACyC,UAAU;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,UAAU;QACfJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACkD,WAAW;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,QAAQ;QACbJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC4C,WAAW;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,SAAS;QACdJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACwD,YAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,UAAU;QACfJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC8D,WAAW;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,YAAY;QACjBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC+C,SAAS;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,SAAS;QACdJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACyF,cAAc;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,cAAc;QACnBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC6E,WAAW;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,eAAe;QACpBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACgF,YAAY;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,aAAa;QAClBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACqD,aAAa;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,WAAW;QAChBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC2D,YAAY;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,iBAAiB;QACtBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC4F,OAAO;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,kBAAkB;QACvBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACyF,cAAc;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,6BAA6B;QAClCJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACgF,YAAY;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QAAC6K,IAAI,EAAC,uBAAuB;QAACJ,OAAO,eAAEzJ,OAAA,CAACP,WAAW;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChE1B,OAAA,CAAChB,KAAK;QAAC6K,IAAI,EAAC,kBAAkB;QAACJ,OAAO,eAAEzJ,OAAA,CAACL,MAAM;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,WAAW;QAChBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC+F,mBAAmB;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACF1B,OAAA,CAAChB,KAAK;QAAC6K,IAAI,EAAC,OAAO;QAACJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACiE,IAAI;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvE1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,aAAa;QAClBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACoE,UAAU;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,sBAAsB;QAC3BJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACuE,SAAS;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,UAAU;QACfJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC0E,WAAW;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,qCAAqC;QAC1CJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACmF,aAAa;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,uBAAuB;QAC5BJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACsF,SAAS;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAGF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,6BAA6B;QAClCJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACwG,UAAU;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,kBAAkB;QACvBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC2G,YAAY;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,iBAAiB;QACtBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC8G,iBAAiB;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,cAAc;QACnBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACkG,UAAU;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,aAAa;QAClBJ,OAAO,eACLzJ,OAAA,CAACwJ,eAAe;UACdC,OAAO,eACLzJ,OAAA,CAACqG,iBAAiB;YAAA/E,QAAA,eAChBtB,OAAA,CAACiH,SAAS;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACpB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,iBAAiB;QACtBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACmI,cAAc;YAAA5G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,gBAAgB;QACrBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACoH,iBAAiB;YAAA7F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,gBAAgB;QACrBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACuH,YAAY;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,eAAe;QACpBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAACgI,YAAY;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,WAAW;QAChBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC6H,SAAS;YAAAtG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACF1B,OAAA,CAAChB,KAAK;QACJ6K,IAAI,EAAC,oBAAoB;QACzBJ,OAAO,eAAEzJ,OAAA,CAACwJ,eAAe;UAACC,OAAO,eAAEzJ,OAAA,CAAC0H,YAAY;YAAAnG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;AAACoI,IAAA,GAvJIF,SAAS;AAyJf,SAASG,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACbpL,SAAS,CAAC,MAAM;IACd;IACA,IAAI,OAAOgD,MAAM,KAAK,WAAW,EAAE;MACjC;MACA,IAAI,eAAe,IAAIqI,SAAS,EAAE;QAChCA,SAAS,CAACC,aAAa,CAACC,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAEC,aAAa,IAAK;UACjEA,aAAa,CAACC,OAAO,CAAEC,YAAY,IAAK;YACtCA,YAAY,CAACC,MAAM,CAAC,CAAC;UACvB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMC,QAAQ,GAAG,gCAAgC,CAACC,IAAI,CACpDT,SAAS,CAACU,SACZ,CAAC;MACD,IAAIF,QAAQ,EAAE;QACZ;QACA7I,MAAM,CAACyH,gBAAgB,CAAC,cAAc,EAAE,MAAM;UAC5C;UACA,IAAIzH,MAAM,CAACgJ,mBAAmB,EAAE;YAC9BhJ,MAAM,CAACgJ,mBAAmB,CAACC,KAAK,GAAG,CAAC,CAAC;UACvC;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7K,OAAA,CAACF,cAAc;IAAAwB,QAAA,eACbtB,OAAA,CAACd,mBAAmB;MAAC4L,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA2B;MAAA3J,QAAA,eACpEtB,OAAA,CAACZ,YAAY;QAAAkC,QAAA,eACXtB,OAAA,CAACX,cAAc;UAAAiC,QAAA,eACbtB,OAAA,CAACN,aAAa;YAAA4B,QAAA,eACZtB,OAAA,CAACb,YAAY;cAAAmC,QAAA,eACXtB,OAAA,CAACJ,iBAAiB;gBAAA0B,QAAA,eAChBtB,OAAA,CAACjB,MAAM;kBAAAuC,QAAA,gBACLtB,OAAA,CAACV,WAAW;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACf1B,OAAA,CAAC4J,SAAS;oBAAArI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACb1B,OAAA,CAACT,SAAS;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAErB;AAACsI,GAAA,CAlDQD,GAAG;AAAAmB,IAAA,GAAHnB,GAAG;AAoDZ,eAAeA,GAAG;AAAC,IAAAxH,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAM,IAAA,EAAAY,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAoB,IAAA;AAAAC,YAAA,CAAA5I,EAAA;AAAA4I,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA/H,IAAA;AAAA+H,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA5H,IAAA;AAAA4H,YAAA,CAAA1H,IAAA;AAAA0H,YAAA,CAAAzH,IAAA;AAAAyH,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAAtH,IAAA;AAAAsH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAvG,IAAA;AAAAuG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAAxB,IAAA;AAAAwB,YAAA,CAAArB,IAAA;AAAAqB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}