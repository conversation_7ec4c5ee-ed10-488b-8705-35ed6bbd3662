{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\vendorSide\\\\AdminHome.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport NavbarAdmin from \"../../Components/adminSide/adminNav\";\nimport SidebarAdmin from \"../../Components/adminSide/adminSideBar\";\nimport DashboardAdmin from \"../../Components/adminSide/dashboardAdmin\";\nimport RecentPurchasesAdmin from \"../../Components/adminSide/orderListAdmin\";\nimport ProductsPageAdmin from \"../../Components/adminSide/ProductsAdmin\";\nimport RequestsPartners from \"../../Components/adminSide/Requests\";\nimport CategoryForm from \"../../Components/adminSide/createCategory\";\nimport CategoryListPage from \"../../Components/adminSide/categoriesList\";\nimport TagsTable from \"../../Components/adminSide/tags\";\nimport ConceptManager from \"../../Components/adminSide/concepts\";\nimport AllEmployees from \"../../Components/adminSide/allEmployees\";\nimport PromotionsPageAdmin from \"../../Components/adminSide/promotionsAdmin\";\nimport BrandManagement from \"../../Components/adminSide/brandsAdmin\";\nimport AdminNotificationPage from \"../../Components/adminSide/adminNotifications\";\nimport ContactUsRequests from \"../../Components/adminSide/contactusRequests\";\nimport OurEmployees from \"../../Components/adminSide/ourEmployees\";\nimport PendingBrandUpdates from \"../../Components/adminSide/PendingBrandUpdates\";\nimport PendingProductUpdates from \"../../Components/adminSide/PendingProductUpdates\";\nimport AccountingAdmin from \"../../Components/adminSide/accountingAdmin\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHome = () => {\n  _s();\n  const navigate = useNavigate();\n  const [activePage, setActivePage] = useState(\"dashboard\");\n  useEffect(() => {\n    const admin = localStorage.getItem(\"admin\");\n    if (!admin) {\n      navigate(\"/admin-login\"); // redirect to login page if no admin found\n    }\n  }, [navigate]);\n  const renderContent = () => {\n    switch (activePage) {\n      case \"dashboard\":\n        return /*#__PURE__*/_jsxDEV(DashboardAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 16\n        }, this);\n      case \"allProducts\":\n        return /*#__PURE__*/_jsxDEV(ProductsPageAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 16\n        }, this);\n      case \"orderList\":\n        return /*#__PURE__*/_jsxDEV(RecentPurchasesAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 16\n        }, this);\n      case \"Requests\":\n        return /*#__PURE__*/_jsxDEV(RequestsPartners, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 16\n        }, this);\n      case \"createCategory\":\n        return /*#__PURE__*/_jsxDEV(CategoryForm, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 16\n        }, this);\n      case \"categoriesList\":\n        return /*#__PURE__*/_jsxDEV(CategoryListPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 16\n        }, this);\n      case \"tags\":\n        return /*#__PURE__*/_jsxDEV(TagsTable, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 16\n        }, this);\n      case \"concepts\":\n        return /*#__PURE__*/_jsxDEV(ConceptManager, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 16\n        }, this);\n      case \"AddCategory\":\n        return /*#__PURE__*/_jsxDEV(CategoryForm, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 16\n        }, this);\n      case \"AllEmployees\":\n        return /*#__PURE__*/_jsxDEV(AllEmployees, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 16\n        }, this);\n      case \"promotions\":\n        return /*#__PURE__*/_jsxDEV(PromotionsPageAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 16\n        }, this);\n      case \"brandsManagement\":\n        return /*#__PURE__*/_jsxDEV(BrandManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 16\n        }, this);\n      case \"adminNotificationPage\":\n        return /*#__PURE__*/_jsxDEV(AdminNotificationPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 16\n        }, this);\n      case \"contactusRequests\":\n        return /*#__PURE__*/_jsxDEV(ContactUsRequests, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 16\n        }, this);\n      case \"ourEmployees\":\n        return /*#__PURE__*/_jsxDEV(OurEmployees, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 16\n        }, this);\n      case \"PendingBrandUpdates\":\n        return /*#__PURE__*/_jsxDEV(PendingBrandUpdates, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 16\n        }, this);\n      case \"PendingProductsUpdates\":\n        return /*#__PURE__*/_jsxDEV(PendingProductUpdates, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      case \"AccountingAdmin\":\n        return /*#__PURE__*/_jsxDEV(AccountingAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 16\n        }, this);\n      default:\n        return \"DashboardVendor\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container-vendor\",\n    children: [/*#__PURE__*/_jsxDEV(NavbarAdmin, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(SidebarAdmin, {\n        setActivePage: setActivePage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-vendor\",\n        children: renderContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHome, \"gw62DXnGF8zRoWI/gvi+639FEHk=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminHome;\nexport default AdminHome;\nvar _c;\n$RefreshReg$(_c, \"AdminHome\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SidebarAdmin", "DashboardAdmin", "RecentPurchasesAdmin", "ProductsPageAdmin", "RequestsPartners", "CategoryForm", "CategoryListPage", "TagsTable", "ConceptManager", "AllEmployees", "PromotionsPageAdmin", "BrandManagement", "AdminNotificationPage", "ContactUsRequests", "OurEmployees", "PendingBrandUpdates", "PendingProductUpdates", "AccountingAdmin", "jsxDEV", "_jsxDEV", "AdminHome", "_s", "navigate", "activePage", "setActivePage", "admin", "localStorage", "getItem", "renderContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/vendorSide/AdminHome.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nimport NavbarAdmin from \"../../Components/adminSide/adminNav\";\r\nimport SidebarAdmin from \"../../Components/adminSide/adminSideBar\";\r\nimport DashboardAdmin from \"../../Components/adminSide/dashboardAdmin\";\r\nimport RecentPurchasesAdmin from \"../../Components/adminSide/orderListAdmin\";\r\nimport ProductsPageAdmin from \"../../Components/adminSide/ProductsAdmin\";\r\nimport RequestsPartners from \"../../Components/adminSide/Requests\";\r\nimport CategoryForm from \"../../Components/adminSide/createCategory\";\r\nimport CategoryListPage from \"../../Components/adminSide/categoriesList\";\r\nimport TagsTable from \"../../Components/adminSide/tags\";\r\nimport ConceptManager from \"../../Components/adminSide/concepts\";\r\nimport AllEmployees from \"../../Components/adminSide/allEmployees\";\r\nimport PromotionsPageAdmin from \"../../Components/adminSide/promotionsAdmin\";\r\nimport BrandManagement from \"../../Components/adminSide/brandsAdmin\";\r\nimport AdminNotificationPage from \"../../Components/adminSide/adminNotifications\";\r\nimport ContactUsRequests from \"../../Components/adminSide/contactusRequests\";\r\nimport OurEmployees from \"../../Components/adminSide/ourEmployees\";\r\nimport PendingBrandUpdates from \"../../Components/adminSide/PendingBrandUpdates\";\r\nimport PendingProductUpdates from \"../../Components/adminSide/PendingProductUpdates\";\r\nimport AccountingAdmin from \"../../Components/adminSide/accountingAdmin\";\r\n\r\nconst AdminHome = () => {\r\n  const navigate = useNavigate();\r\n  const [activePage, setActivePage] = useState(\"dashboard\");\r\n  useEffect(() => {\r\n    const admin = localStorage.getItem(\"admin\");\r\n\r\n    if (!admin) {\r\n      navigate(\"/admin-login\"); // redirect to login page if no admin found\r\n    }\r\n  }, [navigate]);\r\n  const renderContent = () => {\r\n    switch (activePage) {\r\n      case \"dashboard\":\r\n        return <DashboardAdmin />;\r\n      case \"allProducts\":\r\n        return <ProductsPageAdmin />;\r\n      case \"orderList\":\r\n        return <RecentPurchasesAdmin />;\r\n      case \"Requests\":\r\n        return <RequestsPartners />;\r\n      case \"createCategory\":\r\n        return <CategoryForm />;\r\n      case \"categoriesList\":\r\n        return <CategoryListPage />;\r\n      case \"tags\":\r\n        return <TagsTable />;\r\n      case \"concepts\":\r\n        return <ConceptManager />;\r\n      case \"AddCategory\":\r\n        return <CategoryForm />;\r\n      case \"AllEmployees\":\r\n        return <AllEmployees />;\r\n      case \"promotions\":\r\n        return <PromotionsPageAdmin />;\r\n      case \"brandsManagement\":\r\n        return <BrandManagement />;\r\n      case \"adminNotificationPage\":\r\n        return <AdminNotificationPage />;\r\n      case \"contactusRequests\":\r\n        return <ContactUsRequests />;\r\n      case \"ourEmployees\":\r\n        return <OurEmployees />;\r\n      case \"PendingBrandUpdates\":\r\n        return <PendingBrandUpdates />;\r\n      case \"PendingProductsUpdates\":\r\n        return <PendingProductUpdates />;\r\n      case \"AccountingAdmin\":\r\n        return <AccountingAdmin />;\r\n      default:\r\n        return \"DashboardVendor\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container-vendor\">\r\n      <NavbarAdmin />\r\n      <div className=\"main-content-vendor\">\r\n        <SidebarAdmin setActivePage={setActivePage} />\r\n        <div className=\"content-vendor\">{renderContent()}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminHome;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,YAAY,MAAM,2CAA2C;AACpE,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,OAAOC,SAAS,MAAM,iCAAiC;AACvD,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,qBAAqB,MAAM,+CAA+C;AACjF,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,mBAAmB,MAAM,gDAAgD;AAChF,OAAOC,qBAAqB,MAAM,kDAAkD;AACpF,OAAOC,eAAe,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,WAAW,CAAC;EACzDC,SAAS,CAAC,MAAM;IACd,MAAM4B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI,CAACF,KAAK,EAAE;MACVH,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQL,UAAU;MAChB,KAAK,WAAW;QACd,oBAAOJ,OAAA,CAAClB,cAAc;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,aAAa;QAChB,oBAAOb,OAAA,CAAChB,iBAAiB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B,KAAK,WAAW;QACd,oBAAOb,OAAA,CAACjB,oBAAoB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,UAAU;QACb,oBAAOb,OAAA,CAACf,gBAAgB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,gBAAgB;QACnB,oBAAOb,OAAA,CAACd,YAAY;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,gBAAgB;QACnB,oBAAOb,OAAA,CAACb,gBAAgB;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,MAAM;QACT,oBAAOb,OAAA,CAACZ,SAAS;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,UAAU;QACb,oBAAOb,OAAA,CAACX,cAAc;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,aAAa;QAChB,oBAAOb,OAAA,CAACd,YAAY;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,cAAc;QACjB,oBAAOb,OAAA,CAACV,YAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,YAAY;QACf,oBAAOb,OAAA,CAACT,mBAAmB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC,KAAK,kBAAkB;QACrB,oBAAOb,OAAA,CAACR,eAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,uBAAuB;QAC1B,oBAAOb,OAAA,CAACP,qBAAqB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClC,KAAK,mBAAmB;QACtB,oBAAOb,OAAA,CAACN,iBAAiB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B,KAAK,cAAc;QACjB,oBAAOb,OAAA,CAACL,YAAY;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,qBAAqB;QACxB,oBAAOb,OAAA,CAACJ,mBAAmB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC,KAAK,wBAAwB;QAC3B,oBAAOb,OAAA,CAACH,qBAAqB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClC,KAAK,iBAAiB;QACpB,oBAAOb,OAAA,CAACF,eAAe;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B;QACE,OAAO,iBAAiB;IAC5B;EACF,CAAC;EAED,oBACEb,OAAA;IAAKc,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCf,OAAA,CAACpB,WAAW;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfb,OAAA;MAAKc,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCf,OAAA,CAACnB,YAAY;QAACwB,aAAa,EAAEA;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Cb,OAAA;QAAKc,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAEN,aAAa,CAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CA9DID,SAAS;EAAA,QACItB,WAAW;AAAA;AAAAqC,EAAA,GADxBf,SAAS;AAgEf,eAAeA,SAAS;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}