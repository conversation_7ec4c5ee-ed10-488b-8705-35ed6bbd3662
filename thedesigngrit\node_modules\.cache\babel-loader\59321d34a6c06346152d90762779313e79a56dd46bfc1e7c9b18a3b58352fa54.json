{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\VariantDialog.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Dialog, DialogTitle, DialogContent, TextField, DialogActions, Button, Grid, IconButton, Box, Select, MenuItem, FormControl, InputLabel } from \"@mui/material\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport axios from \"axios\";\nimport Cropper from \"react-easy-crop\";\nimport CircularProgress from \"@mui/material/CircularProgress\";\n// Define the sage green color\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst sageGreen = \"#6a8452\";\nexport default function VariantDialog({\n  open,\n  onClose,\n  onSubmit,\n  sku,\n  brandId\n}) {\n  _s();\n  var _variants$currentVari2;\n  const [variants, setVariants] = useState([{\n    sku: sku || \"\",\n    title: \"\",\n    color: \"\",\n    size: \"\",\n    price: \"\",\n    stock: \"\",\n    // Add stock field\n    dimensions: {\n      length: \"\",\n      width: \"\",\n      height: \"\",\n      weight: \"\" // Add weight field\n    },\n    images: [],\n    mainImage: null,\n    productId: null\n  }]);\n  const [currentVariant, setCurrentVariant] = useState(0);\n  const [imagePreviews, setImagePreviews] = useState([[]]);\n  const [skuOptions, setSkuOptions] = useState([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [productId, setProductId] = useState(null);\n\n  // Add state for product colors and sizes\n  const [productColors, setProductColors] = useState([]);\n  const [productSizes, setProductSizes] = useState([]);\n  const [showCropModal, setShowCropModal] = useState(false);\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\n  const [pendingFiles, setPendingFiles] = useState([]); // queue of files to crop\n  const [pendingFileIndex, setPendingFileIndex] = useState(0);\n  const [crop, setCrop] = useState({\n    x: 0,\n    y: 0\n  });\n  const [zoom, setZoom] = useState(1);\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\n\n  // Fetch SKUs when dialog opens\n  useEffect(() => {\n    if (open) {\n      fetchSkus();\n    }\n  }, [open]);\n\n  // Reset form when opened\n  useEffect(() => {\n    if (open) {\n      setVariants([{\n        sku: sku || \"\",\n        title: \"\",\n        color: \"\",\n        size: \"\",\n        price: \"\",\n        stock: \"\",\n        // Add stock field\n        dimensions: {\n          length: \"\",\n          width: \"\",\n          height: \"\",\n          weight: \"\" // Add weight field\n        },\n        images: [],\n        mainImage: null,\n        productId: null\n      }]);\n      setCurrentVariant(0);\n      setImagePreviews([[]]);\n      setProductId(null);\n    }\n  }, [open, sku]);\n\n  // Fetch product ID when SKU changes\n  useEffect(() => {\n    var _variants$currentVari;\n    const currentSku = (_variants$currentVari = variants[currentVariant]) === null || _variants$currentVari === void 0 ? void 0 : _variants$currentVari.sku;\n\n    // Only fetch if we have a SKU and don't already have a productId for this variant\n    if (currentSku && !variants[currentVariant].productId) {\n      const fetchData = async () => {\n        try {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/product-variants/product-by-sku/${currentSku}`);\n          if (response.data && response.data.productId) {\n            // Update the productId in the current variant\n            setVariants(prevVariants => {\n              // Make sure we're still on the same variant\n              if (currentVariant >= prevVariants.length) return prevVariants;\n\n              // Only update if the SKU hasn't changed\n              if (prevVariants[currentVariant].sku !== currentSku) return prevVariants;\n              const updatedVariants = [...prevVariants];\n              updatedVariants[currentVariant] = {\n                ...updatedVariants[currentVariant],\n                productId: response.data.productId\n              };\n              return updatedVariants;\n            });\n\n            // Also store the productId at the component level\n            setProductId(response.data.productId);\n            console.log(`Product ID for SKU ${currentSku}: ${response.data.productId}`);\n          } else {\n            console.warn(`No product ID found for SKU: ${currentSku}`);\n          }\n        } catch (err) {\n          console.error(`Error fetching product ID for SKU ${currentSku}:`, err);\n        }\n      };\n      fetchData();\n    }\n  }, [currentVariant, (_variants$currentVari2 = variants[currentVariant]) === null || _variants$currentVari2 === void 0 ? void 0 : _variants$currentVari2.sku]);\n\n  // Modify useEffect to fetch product colors and sizes when productId is set\n  useEffect(() => {\n    if (productId) {\n      fetchProductDetails();\n    }\n  }, [productId]);\n  const fetchSkus = async () => {\n    try {\n      const response = await axios.get(`https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId}`);\n      // Extract just the SKU strings from the response if it's an array of objects\n      if (Array.isArray(response.data) && response.data.length > 0 && typeof response.data[0] === \"object\") {\n        // If the response contains objects with a 'sku' property\n        const skuStrings = response.data.map(item => item.sku);\n        setSkuOptions(skuStrings);\n      } else {\n        // If the response is already an array of strings or another format\n        setSkuOptions(response.data);\n      }\n    } catch (err) {\n      console.error(\"Error fetching SKUs:\", err);\n    }\n  };\n\n  // Add function to fetch product details\n  const fetchProductDetails = async () => {\n    try {\n      console.log(\"Fetching product details for productId:\", productId);\n      const response = await axios.get(`https://api.thedesigngrit.com/api/products/getsingle/${productId}`);\n      if (response.data) {\n        console.log(\"Product details received:\", response.data);\n        console.log(\"Colors:\", response.data.colors);\n        console.log(\"Sizes:\", response.data.sizes);\n        setProductColors(response.data.colors || []);\n        setProductSizes(response.data.sizes || []);\n      }\n    } catch (err) {\n      console.error(\"Error fetching product details:\", err);\n    }\n  };\n\n  // Add useEffect to log when colors and sizes change\n  useEffect(() => {\n    console.log(\"Product colors updated:\", productColors);\n  }, [productColors]);\n  useEffect(() => {\n    console.log(\"Product sizes updated:\", productSizes);\n  }, [productSizes]);\n\n  // Update handleChange to handle nested dimensions\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    const updatedVariants = [...variants];\n\n    // Check if this is a dimensions field\n    if (name.startsWith(\"dimensions.\")) {\n      const dimensionField = name.split(\".\")[1]; // Get the specific dimension field (length, width, etc.)\n      updatedVariants[currentVariant] = {\n        ...updatedVariants[currentVariant],\n        dimensions: {\n          ...updatedVariants[currentVariant].dimensions,\n          [dimensionField]: value\n        }\n      };\n    } else {\n      // Handle regular fields\n      updatedVariants[currentVariant] = {\n        ...updatedVariants[currentVariant],\n        [name]: value\n      };\n    }\n    setVariants(updatedVariants);\n  };\n  const handleSkuSelect = e => {\n    const {\n      value\n    } = e.target;\n\n    // Only update if the SKU has actually changed\n    if (variants[currentVariant].sku !== value) {\n      const updatedVariants = [...variants];\n      updatedVariants[currentVariant] = {\n        ...updatedVariants[currentVariant],\n        sku: value,\n        productId: null // Reset productId when SKU changes\n      };\n      setVariants(updatedVariants);\n\n      // Reset the component-level productId if we're changing the first variant's SKU\n      if (currentVariant === 0) {\n        setProductId(null);\n      }\n    }\n  };\n\n  // Handle array fields (colors, sizes)\n  const handleArrayChange = (e, field) => {\n    const {\n      value\n    } = e.target;\n\n    // Split the input value by commas and trim each item\n    const arrayValues = value.split(\",\").map(item => item.trim());\n    const updatedVariants = [...variants];\n    updatedVariants[currentVariant] = {\n      ...updatedVariants[currentVariant],\n      [field]: arrayValues\n    };\n    setVariants(updatedVariants);\n  };\n  const getCroppedImg = (imageSrc, croppedAreaPixels) => {\n    return new Promise(resolve => {\n      const image = new window.Image();\n      image.src = imageSrc;\n      image.onload = () => {\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = croppedAreaPixels.width;\n        canvas.height = croppedAreaPixels.height;\n        const ctx = canvas.getContext(\"2d\");\n        ctx.drawImage(image, croppedAreaPixels.x, croppedAreaPixels.y, croppedAreaPixels.width, croppedAreaPixels.height, 0, 0, croppedAreaPixels.width, croppedAreaPixels.height);\n        canvas.toBlob(blob => resolve(blob), \"image/jpeg\");\n      };\n    });\n  };\n  const handleImageUpload = e => {\n    const files = Array.from(e.target.files);\n    if (files.length === 0) return;\n    setPendingFiles(files);\n    setPendingFileIndex(0);\n    setSelectedImageSrc(URL.createObjectURL(files[0]));\n    setShowCropModal(true);\n  };\n  const handleSetMainImage = index => {\n    if (index < 0 || !variants[currentVariant].images[index]) return;\n    const updatedVariants = [...variants];\n    updatedVariants[currentVariant] = {\n      ...updatedVariants[currentVariant],\n      mainImage: updatedVariants[currentVariant].images[index]\n    };\n    setVariants(updatedVariants);\n    console.log(\"Set main image to index:\", index);\n  };\n  const handleRemoveImage = index => {\n    if (index < 0 || !variants[currentVariant].images[index]) return;\n    const updatedVariants = [...variants];\n    const updatedImages = updatedVariants[currentVariant].images.filter((_, i) => i !== index);\n\n    // Check if we're removing the main image\n    const isRemovingMainImage = updatedVariants[currentVariant].mainImage === updatedVariants[currentVariant].images[index];\n    updatedVariants[currentVariant] = {\n      ...updatedVariants[currentVariant],\n      images: updatedImages,\n      mainImage: isRemovingMainImage ? updatedImages[0] || null : updatedVariants[currentVariant].mainImage\n    };\n    const updatedPreviews = [...imagePreviews];\n    updatedPreviews[currentVariant] = updatedPreviews[currentVariant].filter((_, i) => i !== index);\n    setVariants(updatedVariants);\n    setImagePreviews(updatedPreviews);\n    console.log(\"Removed image at index:\", index);\n    console.log(\"Remaining images:\", updatedImages.length);\n  };\n  const handleCropComplete = async () => {\n    const file = pendingFiles[pendingFileIndex];\n    const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\n    const croppedFile = new File([blob], file.name, {\n      type: \"image/jpeg\"\n    });\n    const previewUrl = URL.createObjectURL(blob);\n\n    // Add to images and previews for the current variant\n    const updatedVariants = [...variants];\n    updatedVariants[currentVariant].images = [...updatedVariants[currentVariant].images, croppedFile];\n    if (!updatedVariants[currentVariant].mainImage) {\n      updatedVariants[currentVariant].mainImage = croppedFile;\n    }\n    const updatedPreviews = [...imagePreviews];\n    if (!updatedPreviews[currentVariant]) updatedPreviews[currentVariant] = [];\n    updatedPreviews[currentVariant] = [...updatedPreviews[currentVariant], previewUrl];\n    setVariants(updatedVariants);\n    setImagePreviews(updatedPreviews);\n\n    // Move to next file or close modal\n    if (pendingFileIndex + 1 < pendingFiles.length) {\n      setPendingFileIndex(pendingFileIndex + 1);\n      setSelectedImageSrc(URL.createObjectURL(pendingFiles[pendingFileIndex + 1]));\n    } else {\n      setShowCropModal(false);\n      setPendingFiles([]);\n      setPendingFileIndex(0);\n      setSelectedImageSrc(null);\n    }\n  };\n  // Modify addVariant to use same SKU and productId as first variant\n  const addVariant = () => {\n    const firstVariant = variants[0];\n    setVariants([...variants, {\n      sku: firstVariant.sku,\n      title: \"\",\n      color: \"\",\n      size: \"\",\n      price: \"\",\n      stock: \"\",\n      // Add stock field\n      dimensions: {\n        length: \"\",\n        width: \"\",\n        height: \"\",\n        weight: \"\" // Add weight field\n      },\n      images: [],\n      mainImage: null,\n      productId: firstVariant.productId\n    }]);\n    setImagePreviews([...imagePreviews, []]);\n    setCurrentVariant(variants.length);\n  };\n  const handleSubmit = async () => {\n    if (!variants[currentVariant].sku) {\n      alert(\"Missing SKU\");\n      return;\n    }\n    if (!productId) {\n      alert(\"Product ID is required to create variants. Please select a valid SKU.\");\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Create FormData for all variants\n      const formData = new FormData();\n\n      // Prepare variants data with imageIndices to map uploaded images to variants\n      const variantsData = [];\n      let currentImageIndex = 0;\n      variants.forEach(variant => {\n        if (variant.sku) {\n          // Calculate image indices for this variant\n          const imageCount = variant.images.length;\n          const imageIndices = Array.from({\n            length: imageCount\n          }, (_, i) => currentImageIndex + i);\n\n          // Add variant data with image indices\n          variantsData.push({\n            sku: variant.sku,\n            title: variant.title || \"\",\n            color: variant.color || \"\",\n            // Now a single color from dropdown\n            size: variant.size || \"\",\n            // Now a single size from dropdown\n            price: variant.price || \"\",\n            stock: variant.stock || 0,\n            // Add stock to the API submission\n            dimensions: JSON.stringify({\n              length: variant.dimensions.length || 0,\n              width: variant.dimensions.width || 0,\n              height: variant.dimensions.height || 0,\n              weight: variant.dimensions.weight || 0 // Add weight to the JSON string\n            }),\n            imageIndices: imageIndices,\n            mainImageIndex: variant.mainImage ? currentImageIndex + variant.images.indexOf(variant.mainImage) : null\n          });\n\n          // Update current image index for next variant\n          currentImageIndex += imageCount;\n        }\n      });\n\n      // Append variants as JSON string\n      formData.append(\"variants\", JSON.stringify(variantsData));\n\n      // Append all images from all variants\n      variants.forEach(variant => {\n        if (variant.sku) {\n          variant.images.forEach(image => {\n            formData.append(\"images\", image);\n          });\n        }\n      });\n\n      // Submit to the API with productId in the URL\n      const response = await axios.post(`https://api.thedesigngrit.com/api/product-variants/product/${productId}/variants`, formData, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      console.log(\"Variants created successfully:\", response.data);\n\n      // Show success message\n      alert(\"All variants saved successfully!\");\n\n      // Call the onSubmit callback if provided\n      if (typeof onSubmit === \"function\") {\n        onSubmit(response.data.variants);\n      }\n\n      // Close the dialog\n      onClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error creating variants:\", error);\n      alert(`Failed to save variants: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message}`);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: onClose,\n      fullWidth: true,\n      maxWidth: \"md\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Add Product Variant \", currentVariant + 1, \"/\", variants.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"primary\",\n            onClick: addVariant,\n            sx: {\n              backgroundColor: sageGreen,\n              color: \"white\",\n              \"&:hover\": {\n                backgroundColor: \"#5a7342\"\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              sx: {\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"SKU\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: variants[currentVariant].sku,\n                    onChange: handleSkuSelect,\n                    label: \"SKU\",\n                    name: \"sku\",\n                    children: skuOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: option,\n                      children: option\n                    }, option, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Title\",\n                  name: \"title\",\n                  fullWidth: true,\n                  value: variants[currentVariant].title,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: variants[currentVariant].color,\n                    onChange: handleChange,\n                    label: \"Color\",\n                    name: \"color\",\n                    children: productColors && productColors.length > 0 ? productColors.map(color => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: color,\n                      children: color\n                    }, color, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 27\n                    }, this)) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"\",\n                      disabled: true,\n                      children: \"No colors available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Size\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    value: variants[currentVariant].size,\n                    onChange: handleChange,\n                    label: \"Size\",\n                    name: \"size\",\n                    children: productSizes && productSizes.length > 0 ? productSizes.map(size => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: size,\n                      children: size\n                    }, size, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 27\n                    }, this)) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"\",\n                      disabled: true,\n                      children: \"No sizes available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Price\",\n                  name: \"price\",\n                  type: \"number\",\n                  fullWidth: true,\n                  value: variants[currentVariant].price,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Stock\",\n                  name: \"stock\",\n                  type: \"number\",\n                  fullWidth: true,\n                  value: variants[currentVariant].stock,\n                  onChange: handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Technical Dimensions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    label: \"Length (CM)\",\n                    name: \"dimensions.length\",\n                    type: \"number\",\n                    fullWidth: true,\n                    value: variants[currentVariant].dimensions.length,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    label: \"Width (CM)\",\n                    name: \"dimensions.width\",\n                    type: \"number\",\n                    fullWidth: true,\n                    value: variants[currentVariant].dimensions.width,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    label: \"Height (CM)\",\n                    name: \"dimensions.height\",\n                    type: \"number\",\n                    fullWidth: true,\n                    value: variants[currentVariant].dimensions.height,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    label: \"Weight (KG)\",\n                    name: \"dimensions.weight\",\n                    type: \"number\",\n                    fullWidth: true,\n                    value: variants[currentVariant].dimensions.weight,\n                    onChange: handleChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-placeholder\",\n                style: {\n                  height: \"150px\",\n                  border: \"1px dashed #ccc\",\n                  display: \"flex\",\n                  justifyContent: \"center\",\n                  alignItems: \"center\",\n                  marginBottom: \"10px\"\n                },\n                children: variants[currentVariant].mainImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: URL.createObjectURL(variants[currentVariant].mainImage),\n                  alt: \"Main Preview\",\n                  style: {\n                    maxHeight: \"100%\",\n                    maxWidth: \"100%\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Main Image Preview\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"drop-zone\",\n                style: {\n                  border: \"1px dashed #ccc\",\n                  padding: \"10px\",\n                  textAlign: \"center\"\n                },\n                onDragOver: e => e.preventDefault(),\n                onDrop: e => {\n                  e.preventDefault();\n                  const files = Array.from(e.dataTransfer.files);\n                  handleImageUpload({\n                    target: {\n                      files\n                    }\n                  });\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  multiple: true,\n                  accept: \"image/jpeg, image/png, image/webp\",\n                  onChange: handleImageUpload,\n                  style: {\n                    display: \"none\"\n                  },\n                  id: \"variantFileInput\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"variantFileInput\",\n                  style: {\n                    cursor: \"pointer\"\n                  },\n                  children: \"Drop images here, or click to browse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  display: \"flex\",\n                  flexWrap: \"wrap\",\n                  gap: 1\n                },\n                children: imagePreviews[currentVariant] && imagePreviews[currentVariant].map((preview, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    position: \"relative\",\n                    width: \"80px\",\n                    height: \"80px\",\n                    border: variants[currentVariant].mainImage === variants[currentVariant].images[index] ? \"2px solid #6a8452\" : \"1px solid #ccc\",\n                    borderRadius: \"4px\",\n                    overflow: \"hidden\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: preview,\n                    alt: `Thumbnail ${index}`,\n                    style: {\n                      width: \"100%\",\n                      height: \"100%\",\n                      objectFit: \"cover\",\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => handleSetMainImage(index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    sx: {\n                      position: \"absolute\",\n                      top: \"2px\",\n                      right: \"2px\",\n                      backgroundColor: \"rgba(255,255,255,0.7)\",\n                      padding: \"2px\",\n                      \"&:hover\": {\n                        backgroundColor: \"rgba(255,0,0,0.2)\"\n                      }\n                    },\n                    onClick: () => handleRemoveImage(index),\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 25\n                  }, this), variants[currentVariant].mainImage === variants[currentVariant].images[index] && /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: \"absolute\",\n                      bottom: \"2px\",\n                      left: \"2px\",\n                      backgroundColor: \"rgba(106,132,82,0.7)\",\n                      color: \"white\",\n                      fontSize: \"10px\",\n                      padding: \"2px 4px\",\n                      borderRadius: \"2px\"\n                    },\n                    children: \"Main\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: onClose,\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          color: \"primary\",\n          variant: \"contained\",\n          disabled: isSubmitting,\n          children: isSubmitting ? \"Saving...\" : \"Save Variants\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this), showCropModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        width: \"100vw\",\n        height: \"100vh\",\n        background: \"rgba(0,0,0,0.7)\",\n        zIndex: 9999,\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        backdropFilter: \"blur (5px)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: \"#fff\",\n          padding: 24,\n          borderRadius: 8,\n          maxWidth: 500,\n          width: \"90vw\",\n          maxHeight: \"90vh\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: 400,\n            height: 300,\n            position: \"relative\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Cropper, {\n            image: selectedImageSrc,\n            crop: crop,\n            zoom: zoom,\n            aspect: 4 / 3,\n            onCropChange: setCrop,\n            onZoomChange: setZoom,\n            onCropComplete: (_, area) => setCroppedAreaPixels(area)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16,\n            display: \"flex\",\n            gap: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleCropComplete,\n            children: \"Crop Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: () => setShowCropModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(VariantDialog, \"2VXmSoYmGAWl1kwp6KKXa3AdE3c=\");\n_c = VariantDialog;\nvar _c;\n$RefreshReg$(_c, \"VariantDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextField", "DialogActions", "<PERSON><PERSON>", "Grid", "IconButton", "Box", "Select", "MenuItem", "FormControl", "InputLabel", "AddIcon", "axios", "C<PERSON>per", "CircularProgress", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "sage<PERSON><PERSON>", "VariantDialog", "open", "onClose", "onSubmit", "sku", "brandId", "_s", "_variants$currentVari2", "variants", "setVariants", "title", "color", "size", "price", "stock", "dimensions", "length", "width", "height", "weight", "images", "mainImage", "productId", "currentV<PERSON>t", "setCurrentV<PERSON>t", "imagePreviews", "setImagePreviews", "skuOptions", "setSkuOptions", "isSubmitting", "setIsSubmitting", "setProductId", "productColors", "setProductColors", "productSizes", "setProductSizes", "showCropModal", "setShowCropModal", "selectedImageSrc", "setSelectedImageSrc", "pendingFiles", "setPendingFiles", "pendingFileIndex", "setPendingFileIndex", "crop", "setCrop", "x", "y", "zoom", "setZoom", "croppedAreaPixels", "setCroppedAreaPixels", "fetchSkus", "_variants$currentVari", "currentSku", "fetchData", "response", "get", "data", "prevVariants", "updatedVariants", "console", "log", "warn", "err", "error", "fetchProductDetails", "Array", "isArray", "skuStrings", "map", "item", "colors", "sizes", "handleChange", "e", "name", "value", "target", "startsWith", "dimensionField", "split", "handleSkuSelect", "handleArrayChange", "field", "arrayValues", "trim", "getCroppedImg", "imageSrc", "Promise", "resolve", "image", "window", "Image", "src", "onload", "canvas", "document", "createElement", "ctx", "getContext", "drawImage", "toBlob", "blob", "handleImageUpload", "files", "from", "URL", "createObjectURL", "handleSetMainImage", "index", "handleRemoveImage", "updatedImages", "filter", "_", "i", "isRemovingMainImage", "updatedPreviews", "handleCropComplete", "file", "croppedFile", "File", "type", "previewUrl", "addVariant", "firstVariant", "handleSubmit", "alert", "formData", "FormData", "variantsData", "currentImageIndex", "for<PERSON>ach", "variant", "imageCount", "imageIndices", "push", "JSON", "stringify", "mainImageIndex", "indexOf", "append", "post", "headers", "_error$response", "_error$response$data", "message", "children", "fullWidth", "max<PERSON><PERSON><PERSON>", "display", "justifyContent", "alignItems", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "sx", "backgroundColor", "dividers", "container", "spacing", "xs", "md", "mt", "onChange", "label", "option", "disabled", "mb", "className", "style", "border", "marginBottom", "alt", "maxHeight", "padding", "textAlign", "onDragOver", "preventDefault", "onDrop", "dataTransfer", "multiple", "accept", "id", "htmlFor", "cursor", "flexWrap", "gap", "preview", "position", "borderRadius", "overflow", "objectFit", "top", "right", "bottom", "left", "fontSize", "background", "zIndex", "<PERSON><PERSON>ilter", "flexDirection", "aspect", "onCropChange", "onZoomChange", "onCropComplete", "area", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/VariantDialog.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON>alog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  TextField,\r\n  DialogActions,\r\n  Button,\r\n  Grid,\r\n  IconButton,\r\n  Box,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n} from \"@mui/material\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport axios from \"axios\";\r\nimport <PERSON><PERSON>per from \"react-easy-crop\";\r\nimport CircularProgress from \"@mui/material/CircularProgress\";\r\n// Define the sage green color\r\nconst sageGreen = \"#6a8452\";\r\n\r\nexport default function VariantDialog({\r\n  open,\r\n  onClose,\r\n  onSubmit,\r\n  sku,\r\n  brandId,\r\n}) {\r\n  const [variants, setVariants] = useState([\r\n    {\r\n      sku: sku || \"\",\r\n      title: \"\",\r\n      color: \"\",\r\n      size: \"\",\r\n      price: \"\",\r\n      stock: \"\", // Add stock field\r\n      dimensions: {\r\n        length: \"\",\r\n        width: \"\",\r\n        height: \"\",\r\n        weight: \"\", // Add weight field\r\n      },\r\n      images: [],\r\n      mainImage: null,\r\n      productId: null,\r\n    },\r\n  ]);\r\n  const [currentVariant, setCurrentVariant] = useState(0);\r\n  const [imagePreviews, setImagePreviews] = useState([[]]);\r\n  const [skuOptions, setSkuOptions] = useState([]);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [productId, setProductId] = useState(null);\r\n\r\n  // Add state for product colors and sizes\r\n  const [productColors, setProductColors] = useState([]);\r\n  const [productSizes, setProductSizes] = useState([]);\r\n\r\n  const [showCropModal, setShowCropModal] = useState(false);\r\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\r\n  const [pendingFiles, setPendingFiles] = useState([]); // queue of files to crop\r\n  const [pendingFileIndex, setPendingFileIndex] = useState(0);\r\n  const [crop, setCrop] = useState({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\r\n\r\n  // Fetch SKUs when dialog opens\r\n  useEffect(() => {\r\n    if (open) {\r\n      fetchSkus();\r\n    }\r\n  }, [open]);\r\n\r\n  // Reset form when opened\r\n  useEffect(() => {\r\n    if (open) {\r\n      setVariants([\r\n        {\r\n          sku: sku || \"\",\r\n          title: \"\",\r\n          color: \"\",\r\n          size: \"\",\r\n          price: \"\",\r\n          stock: \"\", // Add stock field\r\n          dimensions: {\r\n            length: \"\",\r\n            width: \"\",\r\n            height: \"\",\r\n            weight: \"\", // Add weight field\r\n          },\r\n          images: [],\r\n          mainImage: null,\r\n          productId: null,\r\n        },\r\n      ]);\r\n      setCurrentVariant(0);\r\n      setImagePreviews([[]]);\r\n      setProductId(null);\r\n    }\r\n  }, [open, sku]);\r\n\r\n  // Fetch product ID when SKU changes\r\n  useEffect(() => {\r\n    const currentSku = variants[currentVariant]?.sku;\r\n\r\n    // Only fetch if we have a SKU and don't already have a productId for this variant\r\n    if (currentSku && !variants[currentVariant].productId) {\r\n      const fetchData = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/product-variants/product-by-sku/${currentSku}`\r\n          );\r\n\r\n          if (response.data && response.data.productId) {\r\n            // Update the productId in the current variant\r\n            setVariants((prevVariants) => {\r\n              // Make sure we're still on the same variant\r\n              if (currentVariant >= prevVariants.length) return prevVariants;\r\n\r\n              // Only update if the SKU hasn't changed\r\n              if (prevVariants[currentVariant].sku !== currentSku)\r\n                return prevVariants;\r\n\r\n              const updatedVariants = [...prevVariants];\r\n              updatedVariants[currentVariant] = {\r\n                ...updatedVariants[currentVariant],\r\n                productId: response.data.productId,\r\n              };\r\n              return updatedVariants;\r\n            });\r\n\r\n            // Also store the productId at the component level\r\n            setProductId(response.data.productId);\r\n\r\n            console.log(\r\n              `Product ID for SKU ${currentSku}: ${response.data.productId}`\r\n            );\r\n          } else {\r\n            console.warn(`No product ID found for SKU: ${currentSku}`);\r\n          }\r\n        } catch (err) {\r\n          console.error(\r\n            `Error fetching product ID for SKU ${currentSku}:`,\r\n            err\r\n          );\r\n        }\r\n      };\r\n\r\n      fetchData();\r\n    }\r\n  }, [currentVariant, variants[currentVariant]?.sku]);\r\n\r\n  // Modify useEffect to fetch product colors and sizes when productId is set\r\n  useEffect(() => {\r\n    if (productId) {\r\n      fetchProductDetails();\r\n    }\r\n  }, [productId]);\r\n\r\n  const fetchSkus = async () => {\r\n    try {\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId}`\r\n      );\r\n      // Extract just the SKU strings from the response if it's an array of objects\r\n      if (\r\n        Array.isArray(response.data) &&\r\n        response.data.length > 0 &&\r\n        typeof response.data[0] === \"object\"\r\n      ) {\r\n        // If the response contains objects with a 'sku' property\r\n        const skuStrings = response.data.map((item) => item.sku);\r\n        setSkuOptions(skuStrings);\r\n      } else {\r\n        // If the response is already an array of strings or another format\r\n        setSkuOptions(response.data);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching SKUs:\", err);\r\n    }\r\n  };\r\n\r\n  // Add function to fetch product details\r\n  const fetchProductDetails = async () => {\r\n    try {\r\n      console.log(\"Fetching product details for productId:\", productId);\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/products/getsingle/${productId}`\r\n      );\r\n      if (response.data) {\r\n        console.log(\"Product details received:\", response.data);\r\n        console.log(\"Colors:\", response.data.colors);\r\n        console.log(\"Sizes:\", response.data.sizes);\r\n\r\n        setProductColors(response.data.colors || []);\r\n        setProductSizes(response.data.sizes || []);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching product details:\", err);\r\n    }\r\n  };\r\n\r\n  // Add useEffect to log when colors and sizes change\r\n  useEffect(() => {\r\n    console.log(\"Product colors updated:\", productColors);\r\n  }, [productColors]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"Product sizes updated:\", productSizes);\r\n  }, [productSizes]);\r\n\r\n  // Update handleChange to handle nested dimensions\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    const updatedVariants = [...variants];\r\n\r\n    // Check if this is a dimensions field\r\n    if (name.startsWith(\"dimensions.\")) {\r\n      const dimensionField = name.split(\".\")[1]; // Get the specific dimension field (length, width, etc.)\r\n      updatedVariants[currentVariant] = {\r\n        ...updatedVariants[currentVariant],\r\n        dimensions: {\r\n          ...updatedVariants[currentVariant].dimensions,\r\n          [dimensionField]: value,\r\n        },\r\n      };\r\n    } else {\r\n      // Handle regular fields\r\n      updatedVariants[currentVariant] = {\r\n        ...updatedVariants[currentVariant],\r\n        [name]: value,\r\n      };\r\n    }\r\n\r\n    setVariants(updatedVariants);\r\n  };\r\n\r\n  const handleSkuSelect = (e) => {\r\n    const { value } = e.target;\r\n\r\n    // Only update if the SKU has actually changed\r\n    if (variants[currentVariant].sku !== value) {\r\n      const updatedVariants = [...variants];\r\n      updatedVariants[currentVariant] = {\r\n        ...updatedVariants[currentVariant],\r\n        sku: value,\r\n        productId: null, // Reset productId when SKU changes\r\n      };\r\n      setVariants(updatedVariants);\r\n\r\n      // Reset the component-level productId if we're changing the first variant's SKU\r\n      if (currentVariant === 0) {\r\n        setProductId(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle array fields (colors, sizes)\r\n  const handleArrayChange = (e, field) => {\r\n    const { value } = e.target;\r\n\r\n    // Split the input value by commas and trim each item\r\n    const arrayValues = value.split(\",\").map((item) => item.trim());\r\n\r\n    const updatedVariants = [...variants];\r\n    updatedVariants[currentVariant] = {\r\n      ...updatedVariants[currentVariant],\r\n      [field]: arrayValues,\r\n    };\r\n\r\n    setVariants(updatedVariants);\r\n  };\r\n  const getCroppedImg = (imageSrc, croppedAreaPixels) => {\r\n    return new Promise((resolve) => {\r\n      const image = new window.Image();\r\n      image.src = imageSrc;\r\n      image.onload = () => {\r\n        const canvas = document.createElement(\"canvas\");\r\n        canvas.width = croppedAreaPixels.width;\r\n        canvas.height = croppedAreaPixels.height;\r\n        const ctx = canvas.getContext(\"2d\");\r\n        ctx.drawImage(\r\n          image,\r\n          croppedAreaPixels.x,\r\n          croppedAreaPixels.y,\r\n          croppedAreaPixels.width,\r\n          croppedAreaPixels.height,\r\n          0,\r\n          0,\r\n          croppedAreaPixels.width,\r\n          croppedAreaPixels.height\r\n        );\r\n        canvas.toBlob((blob) => resolve(blob), \"image/jpeg\");\r\n      };\r\n    });\r\n  };\r\n  const handleImageUpload = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    if (files.length === 0) return;\r\n    setPendingFiles(files);\r\n    setPendingFileIndex(0);\r\n    setSelectedImageSrc(URL.createObjectURL(files[0]));\r\n    setShowCropModal(true);\r\n  };\r\n\r\n  const handleSetMainImage = (index) => {\r\n    if (index < 0 || !variants[currentVariant].images[index]) return;\r\n\r\n    const updatedVariants = [...variants];\r\n    updatedVariants[currentVariant] = {\r\n      ...updatedVariants[currentVariant],\r\n      mainImage: updatedVariants[currentVariant].images[index],\r\n    };\r\n    setVariants(updatedVariants);\r\n\r\n    console.log(\"Set main image to index:\", index);\r\n  };\r\n\r\n  const handleRemoveImage = (index) => {\r\n    if (index < 0 || !variants[currentVariant].images[index]) return;\r\n\r\n    const updatedVariants = [...variants];\r\n    const updatedImages = updatedVariants[currentVariant].images.filter(\r\n      (_, i) => i !== index\r\n    );\r\n\r\n    // Check if we're removing the main image\r\n    const isRemovingMainImage =\r\n      updatedVariants[currentVariant].mainImage ===\r\n      updatedVariants[currentVariant].images[index];\r\n\r\n    updatedVariants[currentVariant] = {\r\n      ...updatedVariants[currentVariant],\r\n      images: updatedImages,\r\n      mainImage: isRemovingMainImage\r\n        ? updatedImages[0] || null\r\n        : updatedVariants[currentVariant].mainImage,\r\n    };\r\n\r\n    const updatedPreviews = [...imagePreviews];\r\n    updatedPreviews[currentVariant] = updatedPreviews[currentVariant].filter(\r\n      (_, i) => i !== index\r\n    );\r\n\r\n    setVariants(updatedVariants);\r\n    setImagePreviews(updatedPreviews);\r\n\r\n    console.log(\"Removed image at index:\", index);\r\n    console.log(\"Remaining images:\", updatedImages.length);\r\n  };\r\n  const handleCropComplete = async () => {\r\n    const file = pendingFiles[pendingFileIndex];\r\n    const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\r\n    const croppedFile = new File([blob], file.name, { type: \"image/jpeg\" });\r\n    const previewUrl = URL.createObjectURL(blob);\r\n\r\n    // Add to images and previews for the current variant\r\n    const updatedVariants = [...variants];\r\n    updatedVariants[currentVariant].images = [\r\n      ...updatedVariants[currentVariant].images,\r\n      croppedFile,\r\n    ];\r\n    if (!updatedVariants[currentVariant].mainImage) {\r\n      updatedVariants[currentVariant].mainImage = croppedFile;\r\n    }\r\n\r\n    const updatedPreviews = [...imagePreviews];\r\n    if (!updatedPreviews[currentVariant]) updatedPreviews[currentVariant] = [];\r\n    updatedPreviews[currentVariant] = [\r\n      ...updatedPreviews[currentVariant],\r\n      previewUrl,\r\n    ];\r\n\r\n    setVariants(updatedVariants);\r\n    setImagePreviews(updatedPreviews);\r\n\r\n    // Move to next file or close modal\r\n    if (pendingFileIndex + 1 < pendingFiles.length) {\r\n      setPendingFileIndex(pendingFileIndex + 1);\r\n      setSelectedImageSrc(\r\n        URL.createObjectURL(pendingFiles[pendingFileIndex + 1])\r\n      );\r\n    } else {\r\n      setShowCropModal(false);\r\n      setPendingFiles([]);\r\n      setPendingFileIndex(0);\r\n      setSelectedImageSrc(null);\r\n    }\r\n  };\r\n  // Modify addVariant to use same SKU and productId as first variant\r\n  const addVariant = () => {\r\n    const firstVariant = variants[0];\r\n    setVariants([\r\n      ...variants,\r\n      {\r\n        sku: firstVariant.sku,\r\n        title: \"\",\r\n        color: \"\",\r\n        size: \"\",\r\n        price: \"\",\r\n        stock: \"\", // Add stock field\r\n        dimensions: {\r\n          length: \"\",\r\n          width: \"\",\r\n          height: \"\",\r\n          weight: \"\", // Add weight field\r\n        },\r\n        images: [],\r\n        mainImage: null,\r\n        productId: firstVariant.productId,\r\n      },\r\n    ]);\r\n    setImagePreviews([...imagePreviews, []]);\r\n    setCurrentVariant(variants.length);\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!variants[currentVariant].sku) {\r\n      alert(\"Missing SKU\");\r\n      return;\r\n    }\r\n\r\n    if (!productId) {\r\n      alert(\r\n        \"Product ID is required to create variants. Please select a valid SKU.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Create FormData for all variants\r\n      const formData = new FormData();\r\n\r\n      // Prepare variants data with imageIndices to map uploaded images to variants\r\n      const variantsData = [];\r\n      let currentImageIndex = 0;\r\n\r\n      variants.forEach((variant) => {\r\n        if (variant.sku) {\r\n          // Calculate image indices for this variant\r\n          const imageCount = variant.images.length;\r\n          const imageIndices = Array.from(\r\n            { length: imageCount },\r\n            (_, i) => currentImageIndex + i\r\n          );\r\n\r\n          // Add variant data with image indices\r\n          variantsData.push({\r\n            sku: variant.sku,\r\n            title: variant.title || \"\",\r\n            color: variant.color || \"\", // Now a single color from dropdown\r\n            size: variant.size || \"\", // Now a single size from dropdown\r\n            price: variant.price || \"\",\r\n            stock: variant.stock || 0, // Add stock to the API submission\r\n            dimensions: JSON.stringify({\r\n              length: variant.dimensions.length || 0,\r\n              width: variant.dimensions.width || 0,\r\n              height: variant.dimensions.height || 0,\r\n              weight: variant.dimensions.weight || 0, // Add weight to the JSON string\r\n            }),\r\n            imageIndices: imageIndices,\r\n            mainImageIndex: variant.mainImage\r\n              ? currentImageIndex + variant.images.indexOf(variant.mainImage)\r\n              : null,\r\n          });\r\n\r\n          // Update current image index for next variant\r\n          currentImageIndex += imageCount;\r\n        }\r\n      });\r\n\r\n      // Append variants as JSON string\r\n      formData.append(\"variants\", JSON.stringify(variantsData));\r\n\r\n      // Append all images from all variants\r\n      variants.forEach((variant) => {\r\n        if (variant.sku) {\r\n          variant.images.forEach((image) => {\r\n            formData.append(\"images\", image);\r\n          });\r\n        }\r\n      });\r\n\r\n      // Submit to the API with productId in the URL\r\n      const response = await axios.post(\r\n        `https://api.thedesigngrit.com/api/product-variants/product/${productId}/variants`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n          },\r\n        }\r\n      );\r\n\r\n      console.log(\"Variants created successfully:\", response.data);\r\n\r\n      // Show success message\r\n      alert(\"All variants saved successfully!\");\r\n\r\n      // Call the onSubmit callback if provided\r\n      if (typeof onSubmit === \"function\") {\r\n        onSubmit(response.data.variants);\r\n      }\r\n\r\n      // Close the dialog\r\n      onClose();\r\n    } catch (error) {\r\n      console.error(\"Error creating variants:\", error);\r\n      alert(\r\n        `Failed to save variants: ${\r\n          error.response?.data?.error || error.message\r\n        }`\r\n      );\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Dialog open={open} onClose={onClose} fullWidth maxWidth=\"md\">\r\n        <DialogTitle>\r\n          <Box\r\n            display=\"flex\"\r\n            justifyContent=\"space-between\"\r\n            alignItems=\"center\"\r\n          >\r\n            <span>\r\n              Add Product Variant {currentVariant + 1}/{variants.length}\r\n            </span>\r\n            <IconButton\r\n              color=\"primary\"\r\n              onClick={addVariant}\r\n              sx={{\r\n                backgroundColor: sageGreen,\r\n                color: \"white\",\r\n                \"&:hover\": {\r\n                  backgroundColor: \"#5a7342\",\r\n                },\r\n              }}\r\n            >\r\n              <AddIcon />\r\n            </IconButton>\r\n          </Box>\r\n        </DialogTitle>\r\n        <DialogContent dividers>\r\n          <Grid container spacing={2}>\r\n            <Grid item xs={12} md={6}>\r\n              <Grid container spacing={2} sx={{ mt: 1 }}>\r\n                <Grid item xs={12}>\r\n                  <FormControl fullWidth>\r\n                    <InputLabel>SKU</InputLabel>\r\n                    <Select\r\n                      value={variants[currentVariant].sku}\r\n                      onChange={handleSkuSelect}\r\n                      label=\"SKU\"\r\n                      name=\"sku\"\r\n                    >\r\n                      {skuOptions.map((option) => (\r\n                        <MenuItem key={option} value={option}>\r\n                          {option}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={12}>\r\n                  <TextField\r\n                    label=\"Title\"\r\n                    name=\"title\"\r\n                    fullWidth\r\n                    value={variants[currentVariant].title}\r\n                    onChange={handleChange}\r\n                  />\r\n                </Grid>\r\n                <Grid item xs={12}>\r\n                  <FormControl fullWidth>\r\n                    <InputLabel>Color</InputLabel>\r\n                    <Select\r\n                      value={variants[currentVariant].color}\r\n                      onChange={handleChange}\r\n                      label=\"Color\"\r\n                      name=\"color\"\r\n                    >\r\n                      {productColors && productColors.length > 0 ? (\r\n                        productColors.map((color) => (\r\n                          <MenuItem key={color} value={color}>\r\n                            {color}\r\n                          </MenuItem>\r\n                        ))\r\n                      ) : (\r\n                        <MenuItem value=\"\" disabled>\r\n                          No colors available\r\n                        </MenuItem>\r\n                      )}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={12}>\r\n                  <FormControl fullWidth>\r\n                    <InputLabel>Size</InputLabel>\r\n                    <Select\r\n                      value={variants[currentVariant].size}\r\n                      onChange={handleChange}\r\n                      label=\"Size\"\r\n                      name=\"size\"\r\n                    >\r\n                      {productSizes && productSizes.length > 0 ? (\r\n                        productSizes.map((size) => (\r\n                          <MenuItem key={size} value={size}>\r\n                            {size}\r\n                          </MenuItem>\r\n                        ))\r\n                      ) : (\r\n                        <MenuItem value=\"\" disabled>\r\n                          No sizes available\r\n                        </MenuItem>\r\n                      )}\r\n                    </Select>\r\n                  </FormControl>\r\n                </Grid>\r\n                <Grid item xs={6}>\r\n                  <TextField\r\n                    label=\"Price\"\r\n                    name=\"price\"\r\n                    type=\"number\"\r\n                    fullWidth\r\n                    value={variants[currentVariant].price}\r\n                    onChange={handleChange}\r\n                  />\r\n                </Grid>\r\n                <Grid item xs={6}>\r\n                  <TextField\r\n                    label=\"Stock\"\r\n                    name=\"stock\"\r\n                    type=\"number\"\r\n                    fullWidth\r\n                    value={variants[currentVariant].stock}\r\n                    onChange={handleChange}\r\n                  />\r\n                </Grid>\r\n              </Grid>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} md={6}>\r\n              {/* Technical Dimensions */}\r\n              <Box sx={{ mb: 2 }}>\r\n                <h3>Technical Dimensions</h3>\r\n                <Grid container spacing={2}>\r\n                  <Grid item xs={6}>\r\n                    <TextField\r\n                      label=\"Length (CM)\"\r\n                      name=\"dimensions.length\"\r\n                      type=\"number\"\r\n                      fullWidth\r\n                      value={variants[currentVariant].dimensions.length}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Grid>\r\n                  <Grid item xs={6}>\r\n                    <TextField\r\n                      label=\"Width (CM)\"\r\n                      name=\"dimensions.width\"\r\n                      type=\"number\"\r\n                      fullWidth\r\n                      value={variants[currentVariant].dimensions.width}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Grid>\r\n                  <Grid item xs={6}>\r\n                    <TextField\r\n                      label=\"Height (CM)\"\r\n                      name=\"dimensions.height\"\r\n                      type=\"number\"\r\n                      fullWidth\r\n                      value={variants[currentVariant].dimensions.height}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Grid>\r\n                  <Grid item xs={6}>\r\n                    <TextField\r\n                      label=\"Weight (KG)\"\r\n                      name=\"dimensions.weight\"\r\n                      type=\"number\"\r\n                      fullWidth\r\n                      value={variants[currentVariant].dimensions.weight}\r\n                      onChange={handleChange}\r\n                    />\r\n                  </Grid>\r\n                </Grid>\r\n              </Box>\r\n\r\n              {/* Image Upload Section */}\r\n              <Box sx={{ mb: 2 }}>\r\n                <div\r\n                  className=\"image-placeholder\"\r\n                  style={{\r\n                    height: \"150px\",\r\n                    border: \"1px dashed #ccc\",\r\n                    display: \"flex\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                    marginBottom: \"10px\",\r\n                  }}\r\n                >\r\n                  {variants[currentVariant].mainImage ? (\r\n                    <img\r\n                      src={URL.createObjectURL(\r\n                        variants[currentVariant].mainImage\r\n                      )}\r\n                      alt=\"Main Preview\"\r\n                      style={{ maxHeight: \"100%\", maxWidth: \"100%\" }}\r\n                    />\r\n                  ) : (\r\n                    <p>Main Image Preview</p>\r\n                  )}\r\n                </div>\r\n\r\n                <div\r\n                  className=\"drop-zone\"\r\n                  style={{\r\n                    border: \"1px dashed #ccc\",\r\n                    padding: \"10px\",\r\n                    textAlign: \"center\",\r\n                  }}\r\n                  onDragOver={(e) => e.preventDefault()}\r\n                  onDrop={(e) => {\r\n                    e.preventDefault();\r\n                    const files = Array.from(e.dataTransfer.files);\r\n                    handleImageUpload({ target: { files } });\r\n                  }}\r\n                >\r\n                  <input\r\n                    type=\"file\"\r\n                    multiple\r\n                    accept=\"image/jpeg, image/png, image/webp\"\r\n                    onChange={handleImageUpload}\r\n                    style={{ display: \"none\" }}\r\n                    id=\"variantFileInput\"\r\n                  />\r\n                  <label\r\n                    htmlFor=\"variantFileInput\"\r\n                    style={{ cursor: \"pointer\" }}\r\n                  >\r\n                    Drop images here, or click to browse\r\n                  </label>\r\n                </div>\r\n\r\n                {/* Image Thumbnails for Selection */}\r\n                <Box sx={{ mt: 2, display: \"flex\", flexWrap: \"wrap\", gap: 1 }}>\r\n                  {imagePreviews[currentVariant] &&\r\n                    imagePreviews[currentVariant].map((preview, index) => (\r\n                      <Box\r\n                        key={index}\r\n                        sx={{\r\n                          position: \"relative\",\r\n                          width: \"80px\",\r\n                          height: \"80px\",\r\n                          border:\r\n                            variants[currentVariant].mainImage ===\r\n                            variants[currentVariant].images[index]\r\n                              ? \"2px solid #6a8452\"\r\n                              : \"1px solid #ccc\",\r\n                          borderRadius: \"4px\",\r\n                          overflow: \"hidden\",\r\n                        }}\r\n                      >\r\n                        <img\r\n                          src={preview}\r\n                          alt={`Thumbnail ${index}`}\r\n                          style={{\r\n                            width: \"100%\",\r\n                            height: \"100%\",\r\n                            objectFit: \"cover\",\r\n                            cursor: \"pointer\",\r\n                          }}\r\n                          onClick={() => handleSetMainImage(index)}\r\n                        />\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          sx={{\r\n                            position: \"absolute\",\r\n                            top: \"2px\",\r\n                            right: \"2px\",\r\n                            backgroundColor: \"rgba(255,255,255,0.7)\",\r\n                            padding: \"2px\",\r\n                            \"&:hover\": { backgroundColor: \"rgba(255,0,0,0.2)\" },\r\n                          }}\r\n                          onClick={() => handleRemoveImage(index)}\r\n                        >\r\n                          ✕\r\n                        </IconButton>\r\n                        {variants[currentVariant].mainImage ===\r\n                          variants[currentVariant].images[index] && (\r\n                          <Box\r\n                            sx={{\r\n                              position: \"absolute\",\r\n                              bottom: \"2px\",\r\n                              left: \"2px\",\r\n                              backgroundColor: \"rgba(106,132,82,0.7)\",\r\n                              color: \"white\",\r\n                              fontSize: \"10px\",\r\n                              padding: \"2px 4px\",\r\n                              borderRadius: \"2px\",\r\n                            }}\r\n                          >\r\n                            Main\r\n                          </Box>\r\n                        )}\r\n                      </Box>\r\n                    ))}\r\n                </Box>\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={onClose} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleSubmit}\r\n            color=\"primary\"\r\n            variant=\"contained\"\r\n            disabled={isSubmitting}\r\n          >\r\n            {isSubmitting ? \"Saving...\" : \"Save Variants\"}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      {showCropModal && (\r\n        <div\r\n          style={{\r\n            position: \"fixed\",\r\n            top: 0,\r\n            left: 0,\r\n            width: \"100vw\",\r\n            height: \"100vh\",\r\n            background: \"rgba(0,0,0,0.7)\",\r\n            zIndex: 9999,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"center\",\r\n            backdropFilter: \"blur (5px)\",\r\n          }}\r\n        >\r\n          <div\r\n            style={{\r\n              background: \"#fff\",\r\n              padding: 24,\r\n              borderRadius: 8,\r\n              maxWidth: 500,\r\n              width: \"90vw\",\r\n              maxHeight: \"90vh\",\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <div style={{ width: 400, height: 300, position: \"relative\" }}>\r\n              <Cropper\r\n                image={selectedImageSrc}\r\n                crop={crop}\r\n                zoom={zoom}\r\n                aspect={4 / 3}\r\n                onCropChange={setCrop}\r\n                onZoomChange={setZoom}\r\n                onCropComplete={(_, area) => setCroppedAreaPixels(area)}\r\n              />\r\n            </div>\r\n            <div style={{ marginTop: 16, display: \"flex\", gap: 8 }}>\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                onClick={handleCropComplete}\r\n              >\r\n                Crop Image\r\n              </Button>\r\n              <Button\r\n                variant=\"outlined\"\r\n                color=\"secondary\"\r\n                onClick={() => setShowCropModal(false)}\r\n              >\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG,SAAS;AAE3B,eAAe,SAASC,aAAaA,CAAC;EACpCC,IAAI;EACJC,OAAO;EACPC,QAAQ;EACRC,GAAG;EACHC;AACF,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EACD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,CACvC;IACE4B,GAAG,EAAEA,GAAG,IAAI,EAAE;IACdM,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IAAE;IACXC,UAAU,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE,CAAE;IACd,CAAC;IACDC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,SAAS,EAAES,YAAY,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACoE,IAAI,EAAEC,OAAO,CAAC,GAAGrE,QAAQ,CAAC;IAAEsE,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACAC,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,EAAE;MACRmD,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACnD,IAAI,CAAC,CAAC;;EAEV;EACAxB,SAAS,CAAC,MAAM;IACd,IAAIwB,IAAI,EAAE;MACRQ,WAAW,CAAC,CACV;QACEL,GAAG,EAAEA,GAAG,IAAI,EAAE;QACdM,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QAAE;QACXC,UAAU,EAAE;UACVC,MAAM,EAAE,EAAE;UACVC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,EAAE,CAAE;QACd,CAAC;QACDC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,IAAI;QACfC,SAAS,EAAE;MACb,CAAC,CACF,CAAC;MACFE,iBAAiB,CAAC,CAAC,CAAC;MACpBE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;MACtBK,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAAC9B,IAAI,EAAEG,GAAG,CAAC,CAAC;;EAEf;EACA3B,SAAS,CAAC,MAAM;IAAA,IAAA4E,qBAAA;IACd,MAAMC,UAAU,IAAAD,qBAAA,GAAG7C,QAAQ,CAACe,cAAc,CAAC,cAAA8B,qBAAA,uBAAxBA,qBAAA,CAA0BjD,GAAG;;IAEhD;IACA,IAAIkD,UAAU,IAAI,CAAC9C,QAAQ,CAACe,cAAc,CAAC,CAACD,SAAS,EAAE;MACrD,MAAMiC,SAAS,GAAG,MAAAA,CAAA,KAAY;QAC5B,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAC9B,qEAAqEH,UAAU,EACjF,CAAC;UAED,IAAIE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACpC,SAAS,EAAE;YAC5C;YACAb,WAAW,CAAEkD,YAAY,IAAK;cAC5B;cACA,IAAIpC,cAAc,IAAIoC,YAAY,CAAC3C,MAAM,EAAE,OAAO2C,YAAY;;cAE9D;cACA,IAAIA,YAAY,CAACpC,cAAc,CAAC,CAACnB,GAAG,KAAKkD,UAAU,EACjD,OAAOK,YAAY;cAErB,MAAMC,eAAe,GAAG,CAAC,GAAGD,YAAY,CAAC;cACzCC,eAAe,CAACrC,cAAc,CAAC,GAAG;gBAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;gBAClCD,SAAS,EAAEkC,QAAQ,CAACE,IAAI,CAACpC;cAC3B,CAAC;cACD,OAAOsC,eAAe;YACxB,CAAC,CAAC;;YAEF;YACA7B,YAAY,CAACyB,QAAQ,CAACE,IAAI,CAACpC,SAAS,CAAC;YAErCuC,OAAO,CAACC,GAAG,CACT,sBAAsBR,UAAU,KAAKE,QAAQ,CAACE,IAAI,CAACpC,SAAS,EAC9D,CAAC;UACH,CAAC,MAAM;YACLuC,OAAO,CAACE,IAAI,CAAC,gCAAgCT,UAAU,EAAE,CAAC;UAC5D;QACF,CAAC,CAAC,OAAOU,GAAG,EAAE;UACZH,OAAO,CAACI,KAAK,CACX,qCAAqCX,UAAU,GAAG,EAClDU,GACF,CAAC;QACH;MACF,CAAC;MAEDT,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAAChC,cAAc,GAAAhB,sBAAA,GAAEC,QAAQ,CAACe,cAAc,CAAC,cAAAhB,sBAAA,uBAAxBA,sBAAA,CAA0BH,GAAG,CAAC,CAAC;;EAEnD;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI6C,SAAS,EAAE;MACb4C,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAC5C,SAAS,CAAC,CAAC;EAEf,MAAM8B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAC9B,gEAAgEpD,OAAO,EACzE,CAAC;MACD;MACA,IACE8D,KAAK,CAACC,OAAO,CAACZ,QAAQ,CAACE,IAAI,CAAC,IAC5BF,QAAQ,CAACE,IAAI,CAAC1C,MAAM,GAAG,CAAC,IACxB,OAAOwC,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EACpC;QACA;QACA,MAAMW,UAAU,GAAGb,QAAQ,CAACE,IAAI,CAACY,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACnE,GAAG,CAAC;QACxDwB,aAAa,CAACyC,UAAU,CAAC;MAC3B,CAAC,MAAM;QACL;QACAzC,aAAa,CAAC4B,QAAQ,CAACE,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZH,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAED,GAAG,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAExC,SAAS,CAAC;MACjE,MAAMkC,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAC9B,wDAAwDnC,SAAS,EACnE,CAAC;MACD,IAAIkC,QAAQ,CAACE,IAAI,EAAE;QACjBG,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEN,QAAQ,CAACE,IAAI,CAAC;QACvDG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEN,QAAQ,CAACE,IAAI,CAACc,MAAM,CAAC;QAC5CX,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEN,QAAQ,CAACE,IAAI,CAACe,KAAK,CAAC;QAE1CxC,gBAAgB,CAACuB,QAAQ,CAACE,IAAI,CAACc,MAAM,IAAI,EAAE,CAAC;QAC5CrC,eAAe,CAACqB,QAAQ,CAACE,IAAI,CAACe,KAAK,IAAI,EAAE,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZH,OAAO,CAACI,KAAK,CAAC,iCAAiC,EAAED,GAAG,CAAC;IACvD;EACF,CAAC;;EAED;EACAvF,SAAS,CAAC,MAAM;IACdoF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE9B,aAAa,CAAC;EACvD,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnBvD,SAAS,CAAC,MAAM;IACdoF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE5B,YAAY,CAAC;EACrD,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMwC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,MAAMlB,eAAe,GAAG,CAAC,GAAGpD,QAAQ,CAAC;;IAErC;IACA,IAAIoE,IAAI,CAACG,UAAU,CAAC,aAAa,CAAC,EAAE;MAClC,MAAMC,cAAc,GAAGJ,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3CrB,eAAe,CAACrC,cAAc,CAAC,GAAG;QAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;QAClCR,UAAU,EAAE;UACV,GAAG6C,eAAe,CAACrC,cAAc,CAAC,CAACR,UAAU;UAC7C,CAACiE,cAAc,GAAGH;QACpB;MACF,CAAC;IACH,CAAC,MAAM;MACL;MACAjB,eAAe,CAACrC,cAAc,CAAC,GAAG;QAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;QAClC,CAACqD,IAAI,GAAGC;MACV,CAAC;IACH;IAEApE,WAAW,CAACmD,eAAe,CAAC;EAC9B,CAAC;EAED,MAAMsB,eAAe,GAAIP,CAAC,IAAK;IAC7B,MAAM;MAAEE;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAE1B;IACA,IAAItE,QAAQ,CAACe,cAAc,CAAC,CAACnB,GAAG,KAAKyE,KAAK,EAAE;MAC1C,MAAMjB,eAAe,GAAG,CAAC,GAAGpD,QAAQ,CAAC;MACrCoD,eAAe,CAACrC,cAAc,CAAC,GAAG;QAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;QAClCnB,GAAG,EAAEyE,KAAK;QACVvD,SAAS,EAAE,IAAI,CAAE;MACnB,CAAC;MACDb,WAAW,CAACmD,eAAe,CAAC;;MAE5B;MACA,IAAIrC,cAAc,KAAK,CAAC,EAAE;QACxBQ,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;EACF,CAAC;;EAED;EACA,MAAMoD,iBAAiB,GAAGA,CAACR,CAAC,EAAES,KAAK,KAAK;IACtC,MAAM;MAAEP;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAE1B;IACA,MAAMO,WAAW,GAAGR,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAACX,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACe,IAAI,CAAC,CAAC,CAAC;IAE/D,MAAM1B,eAAe,GAAG,CAAC,GAAGpD,QAAQ,CAAC;IACrCoD,eAAe,CAACrC,cAAc,CAAC,GAAG;MAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;MAClC,CAAC6D,KAAK,GAAGC;IACX,CAAC;IAED5E,WAAW,CAACmD,eAAe,CAAC;EAC9B,CAAC;EACD,MAAM2B,aAAa,GAAGA,CAACC,QAAQ,EAAEtC,iBAAiB,KAAK;IACrD,OAAO,IAAIuC,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;MAChCF,KAAK,CAACG,GAAG,GAAGN,QAAQ;MACpBG,KAAK,CAACI,MAAM,GAAG,MAAM;QACnB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/CF,MAAM,CAAC/E,KAAK,GAAGiC,iBAAiB,CAACjC,KAAK;QACtC+E,MAAM,CAAC9E,MAAM,GAAGgC,iBAAiB,CAAChC,MAAM;QACxC,MAAMiF,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;QACnCD,GAAG,CAACE,SAAS,CACXV,KAAK,EACLzC,iBAAiB,CAACJ,CAAC,EACnBI,iBAAiB,CAACH,CAAC,EACnBG,iBAAiB,CAACjC,KAAK,EACvBiC,iBAAiB,CAAChC,MAAM,EACxB,CAAC,EACD,CAAC,EACDgC,iBAAiB,CAACjC,KAAK,EACvBiC,iBAAiB,CAAChC,MACpB,CAAC;QACD8E,MAAM,CAACM,MAAM,CAAEC,IAAI,IAAKb,OAAO,CAACa,IAAI,CAAC,EAAE,YAAY,CAAC;MACtD,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,iBAAiB,GAAI7B,CAAC,IAAK;IAC/B,MAAM8B,KAAK,GAAGtC,KAAK,CAACuC,IAAI,CAAC/B,CAAC,CAACG,MAAM,CAAC2B,KAAK,CAAC;IACxC,IAAIA,KAAK,CAACzF,MAAM,KAAK,CAAC,EAAE;IACxByB,eAAe,CAACgE,KAAK,CAAC;IACtB9D,mBAAmB,CAAC,CAAC,CAAC;IACtBJ,mBAAmB,CAACoE,GAAG,CAACC,eAAe,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAClDpE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMwE,kBAAkB,GAAIC,KAAK,IAAK;IACpC,IAAIA,KAAK,GAAG,CAAC,IAAI,CAACtG,QAAQ,CAACe,cAAc,CAAC,CAACH,MAAM,CAAC0F,KAAK,CAAC,EAAE;IAE1D,MAAMlD,eAAe,GAAG,CAAC,GAAGpD,QAAQ,CAAC;IACrCoD,eAAe,CAACrC,cAAc,CAAC,GAAG;MAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;MAClCF,SAAS,EAAEuC,eAAe,CAACrC,cAAc,CAAC,CAACH,MAAM,CAAC0F,KAAK;IACzD,CAAC;IACDrG,WAAW,CAACmD,eAAe,CAAC;IAE5BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgD,KAAK,CAAC;EAChD,CAAC;EAED,MAAMC,iBAAiB,GAAID,KAAK,IAAK;IACnC,IAAIA,KAAK,GAAG,CAAC,IAAI,CAACtG,QAAQ,CAACe,cAAc,CAAC,CAACH,MAAM,CAAC0F,KAAK,CAAC,EAAE;IAE1D,MAAMlD,eAAe,GAAG,CAAC,GAAGpD,QAAQ,CAAC;IACrC,MAAMwG,aAAa,GAAGpD,eAAe,CAACrC,cAAc,CAAC,CAACH,MAAM,CAAC6F,MAAM,CACjE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKL,KAClB,CAAC;;IAED;IACA,MAAMM,mBAAmB,GACvBxD,eAAe,CAACrC,cAAc,CAAC,CAACF,SAAS,KACzCuC,eAAe,CAACrC,cAAc,CAAC,CAACH,MAAM,CAAC0F,KAAK,CAAC;IAE/ClD,eAAe,CAACrC,cAAc,CAAC,GAAG;MAChC,GAAGqC,eAAe,CAACrC,cAAc,CAAC;MAClCH,MAAM,EAAE4F,aAAa;MACrB3F,SAAS,EAAE+F,mBAAmB,GAC1BJ,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,GACxBpD,eAAe,CAACrC,cAAc,CAAC,CAACF;IACtC,CAAC;IAED,MAAMgG,eAAe,GAAG,CAAC,GAAG5F,aAAa,CAAC;IAC1C4F,eAAe,CAAC9F,cAAc,CAAC,GAAG8F,eAAe,CAAC9F,cAAc,CAAC,CAAC0F,MAAM,CACtE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKL,KAClB,CAAC;IAEDrG,WAAW,CAACmD,eAAe,CAAC;IAC5BlC,gBAAgB,CAAC2F,eAAe,CAAC;IAEjCxD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgD,KAAK,CAAC;IAC7CjD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkD,aAAa,CAAChG,MAAM,CAAC;EACxD,CAAC;EACD,MAAMsG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,IAAI,GAAG/E,YAAY,CAACE,gBAAgB,CAAC;IAC3C,MAAM6D,IAAI,GAAG,MAAMhB,aAAa,CAACjD,gBAAgB,EAAEY,iBAAiB,CAAC;IACrE,MAAMsE,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAClB,IAAI,CAAC,EAAEgB,IAAI,CAAC3C,IAAI,EAAE;MAAE8C,IAAI,EAAE;IAAa,CAAC,CAAC;IACvE,MAAMC,UAAU,GAAGhB,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;;IAE5C;IACA,MAAM3C,eAAe,GAAG,CAAC,GAAGpD,QAAQ,CAAC;IACrCoD,eAAe,CAACrC,cAAc,CAAC,CAACH,MAAM,GAAG,CACvC,GAAGwC,eAAe,CAACrC,cAAc,CAAC,CAACH,MAAM,EACzCoG,WAAW,CACZ;IACD,IAAI,CAAC5D,eAAe,CAACrC,cAAc,CAAC,CAACF,SAAS,EAAE;MAC9CuC,eAAe,CAACrC,cAAc,CAAC,CAACF,SAAS,GAAGmG,WAAW;IACzD;IAEA,MAAMH,eAAe,GAAG,CAAC,GAAG5F,aAAa,CAAC;IAC1C,IAAI,CAAC4F,eAAe,CAAC9F,cAAc,CAAC,EAAE8F,eAAe,CAAC9F,cAAc,CAAC,GAAG,EAAE;IAC1E8F,eAAe,CAAC9F,cAAc,CAAC,GAAG,CAChC,GAAG8F,eAAe,CAAC9F,cAAc,CAAC,EAClCoG,UAAU,CACX;IAEDlH,WAAW,CAACmD,eAAe,CAAC;IAC5BlC,gBAAgB,CAAC2F,eAAe,CAAC;;IAEjC;IACA,IAAI3E,gBAAgB,GAAG,CAAC,GAAGF,YAAY,CAACxB,MAAM,EAAE;MAC9C2B,mBAAmB,CAACD,gBAAgB,GAAG,CAAC,CAAC;MACzCH,mBAAmB,CACjBoE,GAAG,CAACC,eAAe,CAACpE,YAAY,CAACE,gBAAgB,GAAG,CAAC,CAAC,CACxD,CAAC;IACH,CAAC,MAAM;MACLL,gBAAgB,CAAC,KAAK,CAAC;MACvBI,eAAe,CAAC,EAAE,CAAC;MACnBE,mBAAmB,CAAC,CAAC,CAAC;MACtBJ,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EACD;EACA,MAAMqF,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAGrH,QAAQ,CAAC,CAAC,CAAC;IAChCC,WAAW,CAAC,CACV,GAAGD,QAAQ,EACX;MACEJ,GAAG,EAAEyH,YAAY,CAACzH,GAAG;MACrBM,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MAAE;MACXC,UAAU,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE,CAAE;MACd,CAAC;MACDC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAEuG,YAAY,CAACvG;IAC1B,CAAC,CACF,CAAC;IACFI,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE,EAAE,CAAC,CAAC;IACxCD,iBAAiB,CAAChB,QAAQ,CAACQ,MAAM,CAAC;EACpC,CAAC;EAED,MAAM8G,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACtH,QAAQ,CAACe,cAAc,CAAC,CAACnB,GAAG,EAAE;MACjC2H,KAAK,CAAC,aAAa,CAAC;MACpB;IACF;IAEA,IAAI,CAACzG,SAAS,EAAE;MACdyG,KAAK,CACH,uEACF,CAAC;MACD;IACF;IAEAjG,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMkG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACA,MAAMC,YAAY,GAAG,EAAE;MACvB,IAAIC,iBAAiB,GAAG,CAAC;MAEzB3H,QAAQ,CAAC4H,OAAO,CAAEC,OAAO,IAAK;QAC5B,IAAIA,OAAO,CAACjI,GAAG,EAAE;UACf;UACA,MAAMkI,UAAU,GAAGD,OAAO,CAACjH,MAAM,CAACJ,MAAM;UACxC,MAAMuH,YAAY,GAAGpE,KAAK,CAACuC,IAAI,CAC7B;YAAE1F,MAAM,EAAEsH;UAAW,CAAC,EACtB,CAACpB,CAAC,EAAEC,CAAC,KAAKgB,iBAAiB,GAAGhB,CAChC,CAAC;;UAED;UACAe,YAAY,CAACM,IAAI,CAAC;YAChBpI,GAAG,EAAEiI,OAAO,CAACjI,GAAG;YAChBM,KAAK,EAAE2H,OAAO,CAAC3H,KAAK,IAAI,EAAE;YAC1BC,KAAK,EAAE0H,OAAO,CAAC1H,KAAK,IAAI,EAAE;YAAE;YAC5BC,IAAI,EAAEyH,OAAO,CAACzH,IAAI,IAAI,EAAE;YAAE;YAC1BC,KAAK,EAAEwH,OAAO,CAACxH,KAAK,IAAI,EAAE;YAC1BC,KAAK,EAAEuH,OAAO,CAACvH,KAAK,IAAI,CAAC;YAAE;YAC3BC,UAAU,EAAE0H,IAAI,CAACC,SAAS,CAAC;cACzB1H,MAAM,EAAEqH,OAAO,CAACtH,UAAU,CAACC,MAAM,IAAI,CAAC;cACtCC,KAAK,EAAEoH,OAAO,CAACtH,UAAU,CAACE,KAAK,IAAI,CAAC;cACpCC,MAAM,EAAEmH,OAAO,CAACtH,UAAU,CAACG,MAAM,IAAI,CAAC;cACtCC,MAAM,EAAEkH,OAAO,CAACtH,UAAU,CAACI,MAAM,IAAI,CAAC,CAAE;YAC1C,CAAC,CAAC;YACFoH,YAAY,EAAEA,YAAY;YAC1BI,cAAc,EAAEN,OAAO,CAAChH,SAAS,GAC7B8G,iBAAiB,GAAGE,OAAO,CAACjH,MAAM,CAACwH,OAAO,CAACP,OAAO,CAAChH,SAAS,CAAC,GAC7D;UACN,CAAC,CAAC;;UAEF;UACA8G,iBAAiB,IAAIG,UAAU;QACjC;MACF,CAAC,CAAC;;MAEF;MACAN,QAAQ,CAACa,MAAM,CAAC,UAAU,EAAEJ,IAAI,CAACC,SAAS,CAACR,YAAY,CAAC,CAAC;;MAEzD;MACA1H,QAAQ,CAAC4H,OAAO,CAAEC,OAAO,IAAK;QAC5B,IAAIA,OAAO,CAACjI,GAAG,EAAE;UACfiI,OAAO,CAACjH,MAAM,CAACgH,OAAO,CAAEzC,KAAK,IAAK;YAChCqC,QAAQ,CAACa,MAAM,CAAC,QAAQ,EAAElD,KAAK,CAAC;UAClC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA,MAAMnC,QAAQ,GAAG,MAAMhE,KAAK,CAACsJ,IAAI,CAC/B,8DAA8DxH,SAAS,WAAW,EAClF0G,QAAQ,EACR;QACEe,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAEDlF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEN,QAAQ,CAACE,IAAI,CAAC;;MAE5D;MACAqE,KAAK,CAAC,kCAAkC,CAAC;;MAEzC;MACA,IAAI,OAAO5H,QAAQ,KAAK,UAAU,EAAE;QAClCA,QAAQ,CAACqD,QAAQ,CAACE,IAAI,CAAClD,QAAQ,CAAC;MAClC;;MAEA;MACAN,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO+D,KAAK,EAAE;MAAA,IAAA+E,eAAA,EAAAC,oBAAA;MACdpF,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD8D,KAAK,CACH,4BACE,EAAAiB,eAAA,GAAA/E,KAAK,CAACT,QAAQ,cAAAwF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBtF,IAAI,cAAAuF,oBAAA,uBAApBA,oBAAA,CAAsBhF,KAAK,KAAIA,KAAK,CAACiF,OAAO,EAEhD,CAAC;IACH,CAAC,SAAS;MACRpH,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACElC,OAAA,CAAAE,SAAA;IAAAqJ,QAAA,gBACEvJ,OAAA,CAAClB,MAAM;MAACuB,IAAI,EAAEA,IAAK;MAACC,OAAO,EAAEA,OAAQ;MAACkJ,SAAS;MAACC,QAAQ,EAAC,IAAI;MAAAF,QAAA,gBAC3DvJ,OAAA,CAACjB,WAAW;QAAAwK,QAAA,eACVvJ,OAAA,CAACV,GAAG;UACFoK,OAAO,EAAC,MAAM;UACdC,cAAc,EAAC,eAAe;UAC9BC,UAAU,EAAC,QAAQ;UAAAL,QAAA,gBAEnBvJ,OAAA;YAAAuJ,QAAA,GAAM,sBACgB,EAAC5H,cAAc,GAAG,CAAC,EAAC,GAAC,EAACf,QAAQ,CAACQ,MAAM;UAAA;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACPhK,OAAA,CAACX,UAAU;YACT0B,KAAK,EAAC,SAAS;YACfkJ,OAAO,EAAEjC,UAAW;YACpBkC,EAAE,EAAE;cACFC,eAAe,EAAEhK,SAAS;cAC1BY,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACToJ,eAAe,EAAE;cACnB;YACF,CAAE;YAAAZ,QAAA,eAEFvJ,OAAA,CAACL,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdhK,OAAA,CAAChB,aAAa;QAACoL,QAAQ;QAAAb,QAAA,eACrBvJ,OAAA,CAACZ,IAAI;UAACiL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAf,QAAA,gBACzBvJ,OAAA,CAACZ,IAAI;YAACuF,IAAI;YAAC4F,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eACvBvJ,OAAA,CAACZ,IAAI;cAACiL,SAAS;cAACC,OAAO,EAAE,CAAE;cAACJ,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAlB,QAAA,gBACxCvJ,OAAA,CAACZ,IAAI;gBAACuF,IAAI;gBAAC4F,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eAChBvJ,OAAA,CAACP,WAAW;kBAAC+J,SAAS;kBAAAD,QAAA,gBACpBvJ,OAAA,CAACN,UAAU;oBAAA6J,QAAA,EAAC;kBAAG;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5BhK,OAAA,CAACT,MAAM;oBACL0F,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACnB,GAAI;oBACpCkK,QAAQ,EAAEpF,eAAgB;oBAC1BqF,KAAK,EAAC,KAAK;oBACX3F,IAAI,EAAC,KAAK;oBAAAuE,QAAA,EAETxH,UAAU,CAAC2C,GAAG,CAAEkG,MAAM,iBACrB5K,OAAA,CAACR,QAAQ;sBAAcyF,KAAK,EAAE2F,MAAO;sBAAArB,QAAA,EAClCqB;oBAAM,GADMA,MAAM;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhK,OAAA,CAACZ,IAAI;gBAACuF,IAAI;gBAAC4F,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eAChBvJ,OAAA,CAACf,SAAS;kBACR0L,KAAK,EAAC,OAAO;kBACb3F,IAAI,EAAC,OAAO;kBACZwE,SAAS;kBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACb,KAAM;kBACtC4J,QAAQ,EAAE5F;gBAAa;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPhK,OAAA,CAACZ,IAAI;gBAACuF,IAAI;gBAAC4F,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eAChBvJ,OAAA,CAACP,WAAW;kBAAC+J,SAAS;kBAAAD,QAAA,gBACpBvJ,OAAA,CAACN,UAAU;oBAAA6J,QAAA,EAAC;kBAAK;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BhK,OAAA,CAACT,MAAM;oBACL0F,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACZ,KAAM;oBACtC2J,QAAQ,EAAE5F,YAAa;oBACvB6F,KAAK,EAAC,OAAO;oBACb3F,IAAI,EAAC,OAAO;oBAAAuE,QAAA,EAEXnH,aAAa,IAAIA,aAAa,CAAChB,MAAM,GAAG,CAAC,GACxCgB,aAAa,CAACsC,GAAG,CAAE3D,KAAK,iBACtBf,OAAA,CAACR,QAAQ;sBAAayF,KAAK,EAAElE,KAAM;sBAAAwI,QAAA,EAChCxI;oBAAK,GADOA,KAAK;sBAAA8I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CACX,CAAC,gBAEFhK,OAAA,CAACR,QAAQ;sBAACyF,KAAK,EAAC,EAAE;sBAAC4F,QAAQ;sBAAAtB,QAAA,EAAC;oBAE5B;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU;kBACX;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhK,OAAA,CAACZ,IAAI;gBAACuF,IAAI;gBAAC4F,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eAChBvJ,OAAA,CAACP,WAAW;kBAAC+J,SAAS;kBAAAD,QAAA,gBACpBvJ,OAAA,CAACN,UAAU;oBAAA6J,QAAA,EAAC;kBAAI;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7BhK,OAAA,CAACT,MAAM;oBACL0F,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACX,IAAK;oBACrC0J,QAAQ,EAAE5F,YAAa;oBACvB6F,KAAK,EAAC,MAAM;oBACZ3F,IAAI,EAAC,MAAM;oBAAAuE,QAAA,EAEVjH,YAAY,IAAIA,YAAY,CAAClB,MAAM,GAAG,CAAC,GACtCkB,YAAY,CAACoC,GAAG,CAAE1D,IAAI,iBACpBhB,OAAA,CAACR,QAAQ;sBAAYyF,KAAK,EAAEjE,IAAK;sBAAAuI,QAAA,EAC9BvI;oBAAI,GADQA,IAAI;sBAAA6I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CACX,CAAC,gBAEFhK,OAAA,CAACR,QAAQ;sBAACyF,KAAK,EAAC,EAAE;sBAAC4F,QAAQ;sBAAAtB,QAAA,EAAC;oBAE5B;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU;kBACX;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPhK,OAAA,CAACZ,IAAI;gBAACuF,IAAI;gBAAC4F,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACfvJ,OAAA,CAACf,SAAS;kBACR0L,KAAK,EAAC,OAAO;kBACb3F,IAAI,EAAC,OAAO;kBACZ8C,IAAI,EAAC,QAAQ;kBACb0B,SAAS;kBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACV,KAAM;kBACtCyJ,QAAQ,EAAE5F;gBAAa;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPhK,OAAA,CAACZ,IAAI;gBAACuF,IAAI;gBAAC4F,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACfvJ,OAAA,CAACf,SAAS;kBACR0L,KAAK,EAAC,OAAO;kBACb3F,IAAI,EAAC,OAAO;kBACZ8C,IAAI,EAAC,QAAQ;kBACb0B,SAAS;kBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACT,KAAM;kBACtCwJ,QAAQ,EAAE5F;gBAAa;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPhK,OAAA,CAACZ,IAAI;YAACuF,IAAI;YAAC4F,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,gBAEvBvJ,OAAA,CAACV,GAAG;cAAC4K,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBACjBvJ,OAAA;gBAAAuJ,QAAA,EAAI;cAAoB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BhK,OAAA,CAACZ,IAAI;gBAACiL,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAf,QAAA,gBACzBvJ,OAAA,CAACZ,IAAI;kBAACuF,IAAI;kBAAC4F,EAAE,EAAE,CAAE;kBAAAhB,QAAA,eACfvJ,OAAA,CAACf,SAAS;oBACR0L,KAAK,EAAC,aAAa;oBACnB3F,IAAI,EAAC,mBAAmB;oBACxB8C,IAAI,EAAC,QAAQ;oBACb0B,SAAS;oBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACR,UAAU,CAACC,MAAO;oBAClDsJ,QAAQ,EAAE5F;kBAAa;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACPhK,OAAA,CAACZ,IAAI;kBAACuF,IAAI;kBAAC4F,EAAE,EAAE,CAAE;kBAAAhB,QAAA,eACfvJ,OAAA,CAACf,SAAS;oBACR0L,KAAK,EAAC,YAAY;oBAClB3F,IAAI,EAAC,kBAAkB;oBACvB8C,IAAI,EAAC,QAAQ;oBACb0B,SAAS;oBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACR,UAAU,CAACE,KAAM;oBACjDqJ,QAAQ,EAAE5F;kBAAa;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACPhK,OAAA,CAACZ,IAAI;kBAACuF,IAAI;kBAAC4F,EAAE,EAAE,CAAE;kBAAAhB,QAAA,eACfvJ,OAAA,CAACf,SAAS;oBACR0L,KAAK,EAAC,aAAa;oBACnB3F,IAAI,EAAC,mBAAmB;oBACxB8C,IAAI,EAAC,QAAQ;oBACb0B,SAAS;oBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACR,UAAU,CAACG,MAAO;oBAClDoJ,QAAQ,EAAE5F;kBAAa;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACPhK,OAAA,CAACZ,IAAI;kBAACuF,IAAI;kBAAC4F,EAAE,EAAE,CAAE;kBAAAhB,QAAA,eACfvJ,OAAA,CAACf,SAAS;oBACR0L,KAAK,EAAC,aAAa;oBACnB3F,IAAI,EAAC,mBAAmB;oBACxB8C,IAAI,EAAC,QAAQ;oBACb0B,SAAS;oBACTvE,KAAK,EAAErE,QAAQ,CAACe,cAAc,CAAC,CAACR,UAAU,CAACI,MAAO;oBAClDmJ,QAAQ,EAAE5F;kBAAa;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNhK,OAAA,CAACV,GAAG;cAAC4K,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBACjBvJ,OAAA;gBACE+K,SAAS,EAAC,mBAAmB;gBAC7BC,KAAK,EAAE;kBACL1J,MAAM,EAAE,OAAO;kBACf2J,MAAM,EAAE,iBAAiB;kBACzBvB,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,QAAQ;kBACxBC,UAAU,EAAE,QAAQ;kBACpBsB,YAAY,EAAE;gBAChB,CAAE;gBAAA3B,QAAA,EAED3I,QAAQ,CAACe,cAAc,CAAC,CAACF,SAAS,gBACjCzB,OAAA;kBACEkG,GAAG,EAAEa,GAAG,CAACC,eAAe,CACtBpG,QAAQ,CAACe,cAAc,CAAC,CAACF,SAC3B,CAAE;kBACF0J,GAAG,EAAC,cAAc;kBAClBH,KAAK,EAAE;oBAAEI,SAAS,EAAE,MAAM;oBAAE3B,QAAQ,EAAE;kBAAO;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,gBAEFhK,OAAA;kBAAAuJ,QAAA,EAAG;gBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cACzB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENhK,OAAA;gBACE+K,SAAS,EAAC,WAAW;gBACrBC,KAAK,EAAE;kBACLC,MAAM,EAAE,iBAAiB;kBACzBI,OAAO,EAAE,MAAM;kBACfC,SAAS,EAAE;gBACb,CAAE;gBACFC,UAAU,EAAGxG,CAAC,IAAKA,CAAC,CAACyG,cAAc,CAAC,CAAE;gBACtCC,MAAM,EAAG1G,CAAC,IAAK;kBACbA,CAAC,CAACyG,cAAc,CAAC,CAAC;kBAClB,MAAM3E,KAAK,GAAGtC,KAAK,CAACuC,IAAI,CAAC/B,CAAC,CAAC2G,YAAY,CAAC7E,KAAK,CAAC;kBAC9CD,iBAAiB,CAAC;oBAAE1B,MAAM,EAAE;sBAAE2B;oBAAM;kBAAE,CAAC,CAAC;gBAC1C,CAAE;gBAAA0C,QAAA,gBAEFvJ,OAAA;kBACE8H,IAAI,EAAC,MAAM;kBACX6D,QAAQ;kBACRC,MAAM,EAAC,mCAAmC;kBAC1ClB,QAAQ,EAAE9D,iBAAkB;kBAC5BoE,KAAK,EAAE;oBAAEtB,OAAO,EAAE;kBAAO,CAAE;kBAC3BmC,EAAE,EAAC;gBAAkB;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACFhK,OAAA;kBACE8L,OAAO,EAAC,kBAAkB;kBAC1Bd,KAAK,EAAE;oBAAEe,MAAM,EAAE;kBAAU,CAAE;kBAAAxC,QAAA,EAC9B;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNhK,OAAA,CAACV,GAAG;gBAAC4K,EAAE,EAAE;kBAAEO,EAAE,EAAE,CAAC;kBAAEf,OAAO,EAAE,MAAM;kBAAEsC,QAAQ,EAAE,MAAM;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA1C,QAAA,EAC3D1H,aAAa,CAACF,cAAc,CAAC,IAC5BE,aAAa,CAACF,cAAc,CAAC,CAAC+C,GAAG,CAAC,CAACwH,OAAO,EAAEhF,KAAK,kBAC/ClH,OAAA,CAACV,GAAG;kBAEF4K,EAAE,EAAE;oBACFiC,QAAQ,EAAE,UAAU;oBACpB9K,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACd2J,MAAM,EACJrK,QAAQ,CAACe,cAAc,CAAC,CAACF,SAAS,KAClCb,QAAQ,CAACe,cAAc,CAAC,CAACH,MAAM,CAAC0F,KAAK,CAAC,GAClC,mBAAmB,GACnB,gBAAgB;oBACtBkF,YAAY,EAAE,KAAK;oBACnBC,QAAQ,EAAE;kBACZ,CAAE;kBAAA9C,QAAA,gBAEFvJ,OAAA;oBACEkG,GAAG,EAAEgG,OAAQ;oBACbf,GAAG,EAAE,aAAajE,KAAK,EAAG;oBAC1B8D,KAAK,EAAE;sBACL3J,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdgL,SAAS,EAAE,OAAO;sBAClBP,MAAM,EAAE;oBACV,CAAE;oBACF9B,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACC,KAAK;kBAAE;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACFhK,OAAA,CAACX,UAAU;oBACT2B,IAAI,EAAC,OAAO;oBACZkJ,EAAE,EAAE;sBACFiC,QAAQ,EAAE,UAAU;sBACpBI,GAAG,EAAE,KAAK;sBACVC,KAAK,EAAE,KAAK;sBACZrC,eAAe,EAAE,uBAAuB;sBACxCkB,OAAO,EAAE,KAAK;sBACd,SAAS,EAAE;wBAAElB,eAAe,EAAE;sBAAoB;oBACpD,CAAE;oBACFF,OAAO,EAAEA,CAAA,KAAM9C,iBAAiB,CAACD,KAAK,CAAE;oBAAAqC,QAAA,EACzC;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACZpJ,QAAQ,CAACe,cAAc,CAAC,CAACF,SAAS,KACjCb,QAAQ,CAACe,cAAc,CAAC,CAACH,MAAM,CAAC0F,KAAK,CAAC,iBACtClH,OAAA,CAACV,GAAG;oBACF4K,EAAE,EAAE;sBACFiC,QAAQ,EAAE,UAAU;sBACpBM,MAAM,EAAE,KAAK;sBACbC,IAAI,EAAE,KAAK;sBACXvC,eAAe,EAAE,sBAAsB;sBACvCpJ,KAAK,EAAE,OAAO;sBACd4L,QAAQ,EAAE,MAAM;sBAChBtB,OAAO,EAAE,SAAS;sBAClBe,YAAY,EAAE;oBAChB,CAAE;oBAAA7C,QAAA,EACH;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA,GAvDI9C,KAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwDP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhK,OAAA,CAACd,aAAa;QAAAqK,QAAA,gBACZvJ,OAAA,CAACb,MAAM;UAAC8K,OAAO,EAAE3J,OAAQ;UAACS,KAAK,EAAC,SAAS;UAAAwI,QAAA,EAAC;QAE1C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThK,OAAA,CAACb,MAAM;UACL8K,OAAO,EAAE/B,YAAa;UACtBnH,KAAK,EAAC,SAAS;UACf0H,OAAO,EAAC,WAAW;UACnBoC,QAAQ,EAAE5I,YAAa;UAAAsH,QAAA,EAEtBtH,YAAY,GAAG,WAAW,GAAG;QAAe;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EACRxH,aAAa,iBACZxC,OAAA;MACEgL,KAAK,EAAE;QACLmB,QAAQ,EAAE,OAAO;QACjBI,GAAG,EAAE,CAAC;QACNG,IAAI,EAAE,CAAC;QACPrL,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACfsL,UAAU,EAAE,iBAAiB;QAC7BC,MAAM,EAAE,IAAI;QACZnD,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,cAAc,EAAE,QAAQ;QACxBmD,cAAc,EAAE;MAClB,CAAE;MAAAvD,QAAA,eAEFvJ,OAAA;QACEgL,KAAK,EAAE;UACL4B,UAAU,EAAE,MAAM;UAClBvB,OAAO,EAAE,EAAE;UACXe,YAAY,EAAE,CAAC;UACf3C,QAAQ,EAAE,GAAG;UACbpI,KAAK,EAAE,MAAM;UACb+J,SAAS,EAAE,MAAM;UACjB1B,OAAO,EAAE,MAAM;UACfqD,aAAa,EAAE,QAAQ;UACvBnD,UAAU,EAAE;QACd,CAAE;QAAAL,QAAA,gBAEFvJ,OAAA;UAAKgL,KAAK,EAAE;YAAE3J,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE,GAAG;YAAE6K,QAAQ,EAAE;UAAW,CAAE;UAAA5C,QAAA,eAC5DvJ,OAAA,CAACH,OAAO;YACNkG,KAAK,EAAErD,gBAAiB;YACxBM,IAAI,EAAEA,IAAK;YACXI,IAAI,EAAEA,IAAK;YACX4J,MAAM,EAAE,CAAC,GAAG,CAAE;YACdC,YAAY,EAAEhK,OAAQ;YACtBiK,YAAY,EAAE7J,OAAQ;YACtB8J,cAAc,EAAEA,CAAC7F,CAAC,EAAE8F,IAAI,KAAK7J,oBAAoB,CAAC6J,IAAI;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhK,OAAA;UAAKgL,KAAK,EAAE;YAAEqC,SAAS,EAAE,EAAE;YAAE3D,OAAO,EAAE,MAAM;YAAEuC,GAAG,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACrDvJ,OAAA,CAACb,MAAM;YACLsJ,OAAO,EAAC,WAAW;YACnB1H,KAAK,EAAC,SAAS;YACfkJ,OAAO,EAAEvC,kBAAmB;YAAA6B,QAAA,EAC7B;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThK,OAAA,CAACb,MAAM;YACLsJ,OAAO,EAAC,UAAU;YAClB1H,KAAK,EAAC,WAAW;YACjBkJ,OAAO,EAAEA,CAAA,KAAMxH,gBAAgB,CAAC,KAAK,CAAE;YAAA8G,QAAA,EACxC;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP;AAACtJ,EAAA,CAv2BuBN,aAAa;AAAAkN,EAAA,GAAblN,aAAa;AAAA,IAAAkN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}