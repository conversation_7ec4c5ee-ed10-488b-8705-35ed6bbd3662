{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Checkout\\\\Checkout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport BillingForm from \"./Billingform.jsx\";\nimport ShippingForm from \"./Shippingform.jsx\";\nimport SummaryForm from \"./ordersummary.jsx\";\nimport PaymentForm from \"./Paymentmethod.jsx\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useCart } from \"../../Context/cartcontext.js\";\nimport { useUser } from \"../../utils/userContext\";\nimport axios from \"axios\"; // Import axios for making HTTP requests\nimport OrderSentPopup from \"../successMsgs/orderSubmit.jsx\";\nimport { useLocation } from \"react-router-dom\";\nimport OrderFailedPopup from \"../successMsgs/orderFailed.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Checkout() {\n  _s();\n  const {\n    userSession\n  } = useUser();\n  const {\n    cartItems,\n    resetCart\n  } = useCart(); //  Get cart items from CartContexts\n  const [currentStep, setCurrentStep] = useState(1);\n  const validateCheckboxRef = useRef(null);\n  const [showPopup, setShowPopup] = useState(false);\n  const location = useLocation();\n  const [showFailedPopup, setShowFailedPopup] = useState(false);\n  useEffect(() => {\n    const query = new URLSearchParams(location.search);\n    const order = query.get(\"order\");\n    const status = query.get(\"status\");\n    if (order && status === \"success\") {\n      setShowPopup(true);\n      // Clean up the URL so the popup doesn't keep appearing\n      window.history.replaceState({}, document.title, \"/checkout\");\n    }\n  }, [location.search]);\n  const navigate = useNavigate();\n  const [billingData, setBillingData] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    address: \"\",\n    phoneNumber: \"\",\n    countryCode: \"+1\",\n    country: \"\",\n    city: \"\",\n    zipCode: \"\",\n    apartment: \"\",\n    floor: \"\",\n    building: \"\",\n    state: \"\"\n  });\n  const [billingErrors, setBillingErrors] = useState({});\n  const validateBillingData = () => {\n    const errors = {};\n    const {\n      firstName,\n      lastName,\n      email,\n      address,\n      phoneNumber,\n      country,\n      city,\n      zipCode\n    } = billingData;\n    if (!firstName) errors.firstName = \"First name is required\";\n    if (!lastName) errors.lastName = \"Last name is required\";\n    if (!email) errors.email = \"Email is required\";else if (!/\\S+@\\S+\\.\\S+/.test(email)) errors.email = \"Email is invalid\";\n    if (!address) errors.address = \"Address is required\";\n    if (!phoneNumber) {\n      errors.phoneNumber = \"Phone number is required\";\n    } else {\n      const digitsOnly = phoneNumber.replace(/\\D/g, \"\");\n      if (digitsOnly.length < 10) {\n        errors.phoneNumber = \"Phone number must have at least 10 digits\";\n      }\n    }\n    if (!country) errors.country = \"Country is required\";\n    if (!city) errors.city = \"City is required\";\n    if (!zipCode) {\n      errors.zipCode = \"Zip code is required\";\n    } else {\n      const digitsOnly = zipCode.replace(/\\D/g, \"\");\n      if (digitsOnly.length !== 5) {\n        errors.zipCode = \"Zip Code Must be 5 digits\";\n      }\n    }\n    setBillingErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const [shippingData, setShippingData] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    address: \"\",\n    label: \"\",\n    apartment: \"\",\n    floor: \"\",\n    country: \"\",\n    city: \"\",\n    zipCode: \"\"\n  });\n  const [shippingErrors, setShippingErrors] = useState({});\n  const validateShippingData = () => {\n    const errors = {};\n    const {\n      firstName,\n      lastName,\n      address,\n      city,\n      zipCode\n    } = shippingData;\n    if (!firstName) errors.firstName = \"First name is required\";\n    if (!lastName) errors.lastName = \"Last name is required\";\n    if (!address) errors.address = \"Address is required\";\n    if (!city) errors.city = \"City is required\";\n    if (!zipCode) {\n      errors.zipCode = \"Zip code is required\";\n    } else {\n      const digitsOnly = zipCode.replace(/\\D/g, \"\");\n      if (digitsOnly.length !== 5) {\n        errors.zipCode = \"Zip Code Must be 5 digits\";\n      }\n    }\n    setShippingErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const [paymentData, setPaymentData] = useState({\n    cardNumber: \"\",\n    expiry: \"\",\n    cvv: \"\",\n    paymentMethod: \"card\"\n  });\n  const [paymentErrors, setPaymentErrors] = useState({});\n  const validatePaymentData = () => {\n    const errors = {};\n    const {\n      cardNumber,\n      expiry,\n      cvv\n    } = paymentData;\n    if (paymentData.paymentMethod === \"card\") {\n      if (!cardNumber) errors.cardNumber = \"Card number is required\";else if (cardNumber.replace(/\\s/g, \"\").length !== 16) errors.cardNumber = \"Card number must be 16 digits\";\n      if (!expiry) errors.expiry = \"Expiry date is required\";else if (!/^(0[1-9]|1[0-2])\\/\\d{2}$/.test(expiry)) errors.expiry = \"Invalid format (MM/YY)\";\n      if (!cvv) errors.cvv = \"CVV is required\";else if (!/^\\d{3,4}$/.test(cvv)) errors.cvv = \"CVV must be 3 or 4 digits\";\n    }\n    setPaymentErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const shippingFee = 100;\n  const handleBillingChange = data => {\n    setBillingData(data);\n  };\n  const handleShippingChange = data => {\n    setShippingData(data);\n  };\n  const handlePaymentChange = data => {\n    setPaymentData(data);\n  };\n  const handlePaymentFailed = () => setShowFailedPopup(true);\n  const handlePaymentSubmit = async () => {\n    console.log(\"handlePaymentSubmit called\");\n    if (!cartItems || cartItems.length === 0) {\n      console.error(\"Cart is empty.\");\n      return;\n    }\n\n    // ✅ Group items by brand ID\n    const groupedOrders = cartItems.reduce((acc, item) => {\n      const brandId = item.brandId;\n      if (!acc[brandId]) {\n        acc[brandId] = [];\n      }\n      acc[brandId].push({\n        productId: item.productId || item.id,\n        // Use productId if available (for variants), otherwise use id\n        variantId: item.variantId || null,\n        // Include variantId if it exists\n        name: item.name,\n        price: item.unitPrice,\n        quantity: item.quantity,\n        totalPrice: item.unitPrice * item.quantity,\n        shippingFee: item.shippingFee,\n        color: item.color || \"default\",\n        // Add color information\n        size: item.size || \"default\" // Add size information\n      });\n      return acc;\n    }, {});\n\n    // Parent order reference (for tracking user purchases)\n    const parentOrderId = `ORDER-${Date.now()}`;\n    try {\n      // Loop through each vendor's order and send a request\n      const orderRequests = Object.keys(groupedOrders).map(async brandId => {\n        const orderData = {\n          parentOrderId,\n          // Single order ID for user tracking\n          customerId: userSession.id,\n          // Ensure this is taken from the authenticated user\n          billingDetails: billingData,\n          shippingDetails: shippingData,\n          paymentDetails: paymentData,\n          cartItems: groupedOrders[brandId],\n          // Only this vendor's items\n          subtotal: groupedOrders[brandId].reduce((sum, item) => sum + item.totalPrice, 0),\n          shippingFee,\n          // Modify if each vendor has different shipping fees\n          total: groupedOrders[brandId].reduce((sum, item) => sum + item.totalPrice, 0) + shippingFee,\n          orderStatus: \"Pending\"\n        };\n        console.log(\"Creating order:\", orderData);\n        console.log(\"Shipping Data:\", shippingData);\n        console.log(\"Billing Data:\", billingData);\n        return axios.post(\"https://api.thedesigngrit.com/api/orders/\", orderData);\n      });\n\n      // Wait for all orders to be sent\n      const responses = await Promise.all(orderRequests);\n      console.log(\"Orders created successfully:\", responses.map(res => res.data));\n      // Show the order submit popup\n      setShowPopup(true);\n      // Reset the cart after successful order placement\n      resetCart();\n    } catch (error) {\n      console.log(\"Error creating orders:\", error);\n      console.error(\"Failed to create orders:\", error);\n    }\n  };\n  const subtotal = cartItems !== null && cartItems !== void 0 && cartItems.length ? cartItems.reduce((sum, item) => sum + (item.unitPrice || 0) * (item.quantity || 1), 0) : 0;\n  const total = subtotal + (shippingFee || 0);\n\n  // Add useEffect to recalculate totals when cartItems change\n  useEffect(() => {\n    // Recalculate subtotal and total when cart items change\n    const newSubtotal = cartItems !== null && cartItems !== void 0 && cartItems.length ? cartItems.reduce((sum, item) => sum + (item.unitPrice || 0) * (item.quantity || 1), 0) : 0;\n\n    // const newTotal = newSubtotal + (shippingFee || 0);\n\n    // Update state if needed\n    if (subtotal !== newSubtotal) {\n      // If you have state variables for these, update them\n      // setSubtotal(newSubtotal);\n      // setTotal(newTotal);\n    }\n  }, [cartItems, subtotal]);\n  useEffect(() => {\n    console.log(\"showPopup changed:\", showPopup);\n  }, [showPopup]);\n  useEffect(() => {\n    // Check if the URL contains the payment-failed path\n    if (window.location.pathname === \"/home?payment-failed\") {\n      setShowFailedPopup(true);\n      // Clean up the URL so the popup doesn't keep appearing\n      window.history.replaceState({}, document.title, \"/checkout\");\n    }\n  }, []);\n  const steps = [{\n    id: 1,\n    label: \"Shipping Information\",\n    content: /*#__PURE__*/_jsxDEV(ShippingForm, {\n      shippingData: shippingData,\n      onChange: handleShippingChange,\n      errors: shippingErrors,\n      validateOnChange: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: 2,\n    label: \"Billing Information\",\n    content: /*#__PURE__*/_jsxDEV(BillingForm, {\n      billingData: billingData,\n      onChange: handleBillingChange,\n      billData: {\n        cartItems,\n        subtotal,\n        shippingFee,\n        total\n      },\n      errors: billingErrors,\n      validateOnChange: true,\n      shippingData: shippingData // Pass shipping data to BillingForm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: 3,\n    label: \"Order Summary\",\n    content: /*#__PURE__*/_jsxDEV(SummaryForm, {\n      billData: {\n        cartItems,\n        subtotal,\n        shippingFee,\n        total\n      },\n      onValidate: fn => validateCheckboxRef.current = fn\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: 4,\n    label: \"Payment Method\",\n    content: /*#__PURE__*/_jsxDEV(PaymentForm, {\n      onSubmit: handlePaymentSubmit,\n      onSuccess: () => setShowPopup(true),\n      onFailed: handlePaymentFailed,\n      resetCart: resetCart,\n      paymentData: paymentData,\n      onChange: handlePaymentChange,\n      errors: paymentErrors,\n      billData: {\n        cartItems,\n        subtotal,\n        shippingFee,\n        total,\n        billingDetails: {\n          apartment: shippingData.apartment || \"NA\",\n          first_name: billingData.firstName,\n          last_name: billingData.lastName,\n          street: billingData.address,\n          building: shippingData.apartment || \"NA\",\n          phone_number: billingData.phoneNumber,\n          city: billingData.city,\n          country: billingData.country,\n          email: billingData.email,\n          floor: shippingData.floor || \"NA\",\n          state: shippingData.city || \"NA\"\n        },\n        shippingDetails: {\n          apartment: shippingData.apartment || \"NA\",\n          first_name: shippingData.firstName,\n          last_name: shippingData.lastName,\n          street: shippingData.address,\n          label: shippingData.label || \"NA\",\n          city: shippingData.city,\n          zipCode: shippingData.zipCode,\n          country: shippingData.country\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"checkout-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: steps[currentStep - 1].label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/React.cloneElement(steps[currentStep - 1].content, {\n        billData: currentStep === 4 ? {\n          cartItems,\n          subtotal,\n          shippingFee,\n          total,\n          billingDetails: {\n            apartment: shippingData.apartment || \"NA\",\n            first_name: billingData.firstName,\n            last_name: billingData.lastName,\n            street: billingData.address,\n            building: shippingData.apartment || \"NA\",\n            phone_number: billingData.phoneNumber,\n            city: billingData.city,\n            country: billingData.country,\n            email: billingData.email,\n            floor: shippingData.floor || \"NA\",\n            state: shippingData.city || \"NA\"\n          },\n          shippingDetails: {\n            apartment: shippingData.apartment || \"NA\",\n            first_name: shippingData.firstName,\n            last_name: shippingData.lastName,\n            street: shippingData.address,\n            label: shippingData.label || \"NA\",\n            city: shippingData.city,\n            zipCode: shippingData.zipCode,\n            country: shippingData.country\n          }\n        } : {\n          cartItems,\n          subtotal,\n          shippingFee,\n          total\n        }\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-navigation\",\n        children: currentStep < steps.length && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            if (currentStep === 1 && !validateShippingData()) return;\n            if (currentStep === 2 && !validateBillingData()) return;\n            if (currentStep === 3 && validateCheckboxRef.current && !validateCheckboxRef.current()) {\n              alert(\"Please agree to the terms before proceeding.\");\n              return;\n            }\n            if (currentStep === 4 && !validatePaymentData()) return;\n            setCurrentStep(currentStep + 1);\n          },\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OrderSentPopup, {\n      show: showPopup,\n      closePopup: () => {\n        setShowPopup(false);\n        navigate(\"/\"); // Only navigate after closing the popup\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OrderFailedPopup, {\n      show: showFailedPopup,\n      closePopup: () => {\n        setShowFailedPopup(false);\n        navigate(\"/\"); // Or wherever you want to send the user after closing\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n}\n_s(Checkout, \"ML1LdQJNxENgzaJ9JZAlAEd1Mhs=\", false, function () {\n  return [useUser, useCart, useLocation, useNavigate];\n});\n_c = Checkout;\nexport default Checkout;\nvar _c;\n$RefreshReg$(_c, \"Checkout\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "BillingForm", "ShippingForm", "SummaryForm", "PaymentForm", "useNavigate", "useCart", "useUser", "axios", "OrderSentPopup", "useLocation", "OrderFailedPopup", "jsxDEV", "_jsxDEV", "Checkout", "_s", "userSession", "cartItems", "resetCart", "currentStep", "setCurrentStep", "validateCheckboxRef", "showPopup", "setShowPopup", "location", "showFailedPopup", "setShowFailedPopup", "query", "URLSearchParams", "search", "order", "get", "status", "window", "history", "replaceState", "document", "title", "navigate", "billingData", "setBillingData", "firstName", "lastName", "email", "address", "phoneNumber", "countryCode", "country", "city", "zipCode", "apartment", "floor", "building", "state", "billingErrors", "setBillingErrors", "validateBillingData", "errors", "test", "digitsOnly", "replace", "length", "Object", "keys", "shippingData", "setShippingData", "label", "shippingErrors", "setShippingErrors", "validateShippingData", "paymentData", "setPaymentData", "cardNumber", "expiry", "cvv", "paymentMethod", "paymentErrors", "setPaymentErrors", "validatePaymentData", "shippingFee", "handleBillingChange", "data", "handleShippingChange", "handlePaymentChange", "handlePaymentFailed", "handlePaymentSubmit", "console", "log", "error", "groupedOrders", "reduce", "acc", "item", "brandId", "push", "productId", "id", "variantId", "name", "price", "unitPrice", "quantity", "totalPrice", "color", "size", "parentOrderId", "Date", "now", "orderRequests", "map", "orderData", "customerId", "billingDetails", "shippingDetails", "paymentDetails", "subtotal", "sum", "total", "orderStatus", "post", "responses", "Promise", "all", "res", "newSubtotal", "pathname", "steps", "content", "onChange", "validateOnChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "billData", "onValidate", "fn", "current", "onSubmit", "onSuccess", "onFailed", "first_name", "last_name", "street", "phone_number", "className", "children", "cloneElement", "onClick", "alert", "show", "closePopup", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Checkout/Checkout.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport BillingForm from \"./Billingform.jsx\";\r\nimport ShippingForm from \"./Shippingform.jsx\";\r\nimport SummaryForm from \"./ordersummary.jsx\";\r\nimport PaymentForm from \"./Paymentmethod.jsx\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useCart } from \"../../Context/cartcontext.js\";\r\nimport { useUser } from \"../../utils/userContext\";\r\nimport axios from \"axios\"; // Import axios for making HTTP requests\r\nimport OrderSentPopup from \"../successMsgs/orderSubmit.jsx\";\r\nimport { useLocation } from \"react-router-dom\";\r\nimport OrderFailedPopup from \"../successMsgs/orderFailed.jsx\";\r\n\r\nfunction Checkout() {\r\n  const { userSession } = useUser();\r\n  const { cartItems, resetCart } = useCart(); //  Get cart items from CartContexts\r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const validateCheckboxRef = useRef(null);\r\n  const [showPopup, setShowPopup] = useState(false);\r\n  const location = useLocation();\r\n  const [showFailedPopup, setShowFailedPopup] = useState(false);\r\n  useEffect(() => {\r\n    const query = new URLSearchParams(location.search);\r\n    const order = query.get(\"order\");\r\n    const status = query.get(\"status\");\r\n\r\n    if (order && status === \"success\") {\r\n      setShowPopup(true);\r\n      // Clean up the URL so the popup doesn't keep appearing\r\n      window.history.replaceState({}, document.title, \"/checkout\");\r\n    }\r\n  }, [location.search]);\r\n  const navigate = useNavigate();\r\n  const [billingData, setBillingData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    email: \"\",\r\n    address: \"\",\r\n    phoneNumber: \"\",\r\n    countryCode: \"+1\",\r\n    country: \"\",\r\n    city: \"\",\r\n    zipCode: \"\",\r\n    apartment: \"\",\r\n    floor: \"\",\r\n    building: \"\",\r\n    state: \"\",\r\n  });\r\n  const [billingErrors, setBillingErrors] = useState({});\r\n  const validateBillingData = () => {\r\n    const errors = {};\r\n    const {\r\n      firstName,\r\n      lastName,\r\n      email,\r\n      address,\r\n      phoneNumber,\r\n      country,\r\n      city,\r\n      zipCode,\r\n    } = billingData;\r\n\r\n    if (!firstName) errors.firstName = \"First name is required\";\r\n    if (!lastName) errors.lastName = \"Last name is required\";\r\n    if (!email) errors.email = \"Email is required\";\r\n    else if (!/\\S+@\\S+\\.\\S+/.test(email)) errors.email = \"Email is invalid\";\r\n    if (!address) errors.address = \"Address is required\";\r\n    if (!phoneNumber) {\r\n      errors.phoneNumber = \"Phone number is required\";\r\n    } else {\r\n      const digitsOnly = phoneNumber.replace(/\\D/g, \"\");\r\n      if (digitsOnly.length < 10) {\r\n        errors.phoneNumber = \"Phone number must have at least 10 digits\";\r\n      }\r\n    }\r\n    if (!country) errors.country = \"Country is required\";\r\n    if (!city) errors.city = \"City is required\";\r\n    if (!zipCode) {\r\n      errors.zipCode = \"Zip code is required\";\r\n    } else {\r\n      const digitsOnly = zipCode.replace(/\\D/g, \"\");\r\n      if (digitsOnly.length !== 5) {\r\n        errors.zipCode = \"Zip Code Must be 5 digits\";\r\n      }\r\n    }\r\n\r\n    setBillingErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const [shippingData, setShippingData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    address: \"\",\r\n    label: \"\",\r\n    apartment: \"\",\r\n    floor: \"\",\r\n    country: \"\",\r\n    city: \"\",\r\n    zipCode: \"\",\r\n  });\r\n  const [shippingErrors, setShippingErrors] = useState({});\r\n  const validateShippingData = () => {\r\n    const errors = {};\r\n    const { firstName, lastName, address, city, zipCode } = shippingData;\r\n\r\n    if (!firstName) errors.firstName = \"First name is required\";\r\n    if (!lastName) errors.lastName = \"Last name is required\";\r\n    if (!address) errors.address = \"Address is required\";\r\n    if (!city) errors.city = \"City is required\";\r\n    if (!zipCode) {\r\n      errors.zipCode = \"Zip code is required\";\r\n    } else {\r\n      const digitsOnly = zipCode.replace(/\\D/g, \"\");\r\n      if (digitsOnly.length !== 5) {\r\n        errors.zipCode = \"Zip Code Must be 5 digits\";\r\n      }\r\n    }\r\n\r\n    setShippingErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n  const [paymentData, setPaymentData] = useState({\r\n    cardNumber: \"\",\r\n    expiry: \"\",\r\n    cvv: \"\",\r\n    paymentMethod: \"card\",\r\n  });\r\n  const [paymentErrors, setPaymentErrors] = useState({});\r\n  const validatePaymentData = () => {\r\n    const errors = {};\r\n    const { cardNumber, expiry, cvv } = paymentData;\r\n\r\n    if (paymentData.paymentMethod === \"card\") {\r\n      if (!cardNumber) errors.cardNumber = \"Card number is required\";\r\n      else if (cardNumber.replace(/\\s/g, \"\").length !== 16)\r\n        errors.cardNumber = \"Card number must be 16 digits\";\r\n\r\n      if (!expiry) errors.expiry = \"Expiry date is required\";\r\n      else if (!/^(0[1-9]|1[0-2])\\/\\d{2}$/.test(expiry))\r\n        errors.expiry = \"Invalid format (MM/YY)\";\r\n\r\n      if (!cvv) errors.cvv = \"CVV is required\";\r\n      else if (!/^\\d{3,4}$/.test(cvv)) errors.cvv = \"CVV must be 3 or 4 digits\";\r\n    }\r\n\r\n    setPaymentErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const shippingFee = 100;\r\n\r\n  const handleBillingChange = (data) => {\r\n    setBillingData(data);\r\n  };\r\n\r\n  const handleShippingChange = (data) => {\r\n    setShippingData(data);\r\n  };\r\n\r\n  const handlePaymentChange = (data) => {\r\n    setPaymentData(data);\r\n  };\r\n\r\n  const handlePaymentFailed = () => setShowFailedPopup(true);\r\n\r\n  const handlePaymentSubmit = async () => {\r\n    console.log(\"handlePaymentSubmit called\");\r\n    if (!cartItems || cartItems.length === 0) {\r\n      console.error(\"Cart is empty.\");\r\n      return;\r\n    }\r\n\r\n    // ✅ Group items by brand ID\r\n    const groupedOrders = cartItems.reduce((acc, item) => {\r\n      const brandId = item.brandId;\r\n      if (!acc[brandId]) {\r\n        acc[brandId] = [];\r\n      }\r\n      acc[brandId].push({\r\n        productId: item.productId || item.id, // Use productId if available (for variants), otherwise use id\r\n        variantId: item.variantId || null, // Include variantId if it exists\r\n        name: item.name,\r\n        price: item.unitPrice,\r\n        quantity: item.quantity,\r\n        totalPrice: item.unitPrice * item.quantity,\r\n        shippingFee: item.shippingFee,\r\n        color: item.color || \"default\", // Add color information\r\n        size: item.size || \"default\", // Add size information\r\n      });\r\n      return acc;\r\n    }, {});\r\n\r\n    // Parent order reference (for tracking user purchases)\r\n    const parentOrderId = `ORDER-${Date.now()}`;\r\n\r\n    try {\r\n      // Loop through each vendor's order and send a request\r\n      const orderRequests = Object.keys(groupedOrders).map(async (brandId) => {\r\n        const orderData = {\r\n          parentOrderId, // Single order ID for user tracking\r\n          customerId: userSession.id, // Ensure this is taken from the authenticated user\r\n          billingDetails: billingData,\r\n          shippingDetails: shippingData,\r\n          paymentDetails: paymentData,\r\n          cartItems: groupedOrders[brandId], // Only this vendor's items\r\n          subtotal: groupedOrders[brandId].reduce(\r\n            (sum, item) => sum + item.totalPrice,\r\n            0\r\n          ),\r\n          shippingFee, // Modify if each vendor has different shipping fees\r\n          total:\r\n            groupedOrders[brandId].reduce(\r\n              (sum, item) => sum + item.totalPrice,\r\n              0\r\n            ) + shippingFee,\r\n          orderStatus: \"Pending\",\r\n        };\r\n        console.log(\"Creating order:\", orderData);\r\n        console.log(\"Shipping Data:\", shippingData);\r\n        console.log(\"Billing Data:\", billingData);\r\n        return axios.post(\r\n          \"https://api.thedesigngrit.com/api/orders/\",\r\n          orderData\r\n        );\r\n      });\r\n\r\n      // Wait for all orders to be sent\r\n      const responses = await Promise.all(orderRequests);\r\n      console.log(\r\n        \"Orders created successfully:\",\r\n        responses.map((res) => res.data)\r\n      );\r\n      // Show the order submit popup\r\n      setShowPopup(true);\r\n      // Reset the cart after successful order placement\r\n      resetCart();\r\n    } catch (error) {\r\n      console.log(\"Error creating orders:\", error);\r\n      console.error(\"Failed to create orders:\", error);\r\n    }\r\n  };\r\n\r\n  const subtotal = cartItems?.length\r\n    ? cartItems.reduce(\r\n        (sum, item) => sum + (item.unitPrice || 0) * (item.quantity || 1),\r\n        0\r\n      )\r\n    : 0;\r\n\r\n  const total = subtotal + (shippingFee || 0);\r\n\r\n  // Add useEffect to recalculate totals when cartItems change\r\n  useEffect(() => {\r\n    // Recalculate subtotal and total when cart items change\r\n    const newSubtotal = cartItems?.length\r\n      ? cartItems.reduce(\r\n          (sum, item) => sum + (item.unitPrice || 0) * (item.quantity || 1),\r\n          0\r\n        )\r\n      : 0;\r\n\r\n    // const newTotal = newSubtotal + (shippingFee || 0);\r\n\r\n    // Update state if needed\r\n    if (subtotal !== newSubtotal) {\r\n      // If you have state variables for these, update them\r\n      // setSubtotal(newSubtotal);\r\n      // setTotal(newTotal);\r\n    }\r\n  }, [cartItems, subtotal]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"showPopup changed:\", showPopup);\r\n  }, [showPopup]);\r\n\r\n  useEffect(() => {\r\n    // Check if the URL contains the payment-failed path\r\n    if (window.location.pathname === \"/home?payment-failed\") {\r\n      setShowFailedPopup(true);\r\n      // Clean up the URL so the popup doesn't keep appearing\r\n      window.history.replaceState({}, document.title, \"/checkout\");\r\n    }\r\n  }, []);\r\n\r\n  const steps = [\r\n    {\r\n      id: 1,\r\n      label: \"Shipping Information\",\r\n      content: (\r\n        <ShippingForm\r\n          shippingData={shippingData}\r\n          onChange={handleShippingChange}\r\n          errors={shippingErrors}\r\n          validateOnChange={true}\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      id: 2,\r\n      label: \"Billing Information\",\r\n      content: (\r\n        <BillingForm\r\n          billingData={billingData}\r\n          onChange={handleBillingChange}\r\n          billData={{ cartItems, subtotal, shippingFee, total }}\r\n          errors={billingErrors}\r\n          validateOnChange={true}\r\n          shippingData={shippingData} // Pass shipping data to BillingForm\r\n        />\r\n      ),\r\n    },\r\n\r\n    {\r\n      id: 3,\r\n      label: \"Order Summary\",\r\n      content: (\r\n        <SummaryForm\r\n          billData={{ cartItems, subtotal, shippingFee, total }}\r\n          onValidate={(fn) => (validateCheckboxRef.current = fn)}\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      id: 4,\r\n      label: \"Payment Method\",\r\n      content: (\r\n        <PaymentForm\r\n          onSubmit={handlePaymentSubmit}\r\n          onSuccess={() => setShowPopup(true)}\r\n          onFailed={handlePaymentFailed}\r\n          resetCart={resetCart}\r\n          paymentData={paymentData}\r\n          onChange={handlePaymentChange}\r\n          errors={paymentErrors}\r\n          billData={{\r\n            cartItems,\r\n            subtotal,\r\n            shippingFee,\r\n            total,\r\n            billingDetails: {\r\n              apartment: shippingData.apartment || \"NA\",\r\n              first_name: billingData.firstName,\r\n              last_name: billingData.lastName,\r\n              street: billingData.address,\r\n              building: shippingData.apartment || \"NA\",\r\n              phone_number: billingData.phoneNumber,\r\n              city: billingData.city,\r\n              country: billingData.country,\r\n              email: billingData.email,\r\n              floor: shippingData.floor || \"NA\",\r\n              state: shippingData.city || \"NA\",\r\n            },\r\n            shippingDetails: {\r\n              apartment: shippingData.apartment || \"NA\",\r\n              first_name: shippingData.firstName,\r\n              last_name: shippingData.lastName,\r\n              street: shippingData.address,\r\n              label: shippingData.label || \"NA\",\r\n              city: shippingData.city,\r\n              zipCode: shippingData.zipCode,\r\n              country: shippingData.country,\r\n            },\r\n          }}\r\n        />\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"checkout-container\">\r\n      <div className=\"checkout-form\">\r\n        <h2>{steps[currentStep - 1].label}</h2>\r\n        {React.cloneElement(steps[currentStep - 1].content, {\r\n          billData:\r\n            currentStep === 4\r\n              ? {\r\n                  cartItems,\r\n                  subtotal,\r\n                  shippingFee,\r\n                  total,\r\n                  billingDetails: {\r\n                    apartment: shippingData.apartment || \"NA\",\r\n                    first_name: billingData.firstName,\r\n                    last_name: billingData.lastName,\r\n                    street: billingData.address,\r\n                    building: shippingData.apartment || \"NA\",\r\n                    phone_number: billingData.phoneNumber,\r\n                    city: billingData.city,\r\n                    country: billingData.country,\r\n                    email: billingData.email,\r\n                    floor: shippingData.floor || \"NA\",\r\n                    state: shippingData.city || \"NA\",\r\n                  },\r\n                  shippingDetails: {\r\n                    apartment: shippingData.apartment || \"NA\",\r\n                    first_name: shippingData.firstName,\r\n                    last_name: shippingData.lastName,\r\n                    street: shippingData.address,\r\n                    label: shippingData.label || \"NA\",\r\n                    city: shippingData.city,\r\n                    zipCode: shippingData.zipCode,\r\n                    country: shippingData.country,\r\n                  },\r\n                }\r\n              : { cartItems, subtotal, shippingFee, total },\r\n        })}\r\n        <div className=\"form-navigation\">\r\n          {currentStep < steps.length && (\r\n            <button\r\n              onClick={() => {\r\n                if (currentStep === 1 && !validateShippingData()) return;\r\n                if (currentStep === 2 && !validateBillingData()) return;\r\n                if (\r\n                  currentStep === 3 &&\r\n                  validateCheckboxRef.current &&\r\n                  !validateCheckboxRef.current()\r\n                ) {\r\n                  alert(\"Please agree to the terms before proceeding.\");\r\n                  return;\r\n                }\r\n                if (currentStep === 4 && !validatePaymentData()) return;\r\n\r\n                setCurrentStep(currentStep + 1);\r\n              }}\r\n            >\r\n              Next\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <OrderSentPopup\r\n        show={showPopup}\r\n        closePopup={() => {\r\n          setShowPopup(false);\r\n          navigate(\"/\"); // Only navigate after closing the popup\r\n        }}\r\n      />\r\n\r\n      <OrderFailedPopup\r\n        show={showFailedPopup}\r\n        closePopup={() => {\r\n          setShowFailedPopup(false);\r\n          navigate(\"/\"); // Or wherever you want to send the user after closing\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Checkout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO,CAAC,CAAC;AAC3B,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAY,CAAC,GAAGT,OAAO,CAAC,CAAC;EACjC,MAAM;IAAEU,SAAS;IAAEC;EAAU,CAAC,GAAGZ,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAMuB,mBAAmB,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM0B,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7DE,SAAS,CAAC,MAAM;IACd,MAAM2B,KAAK,GAAG,IAAIC,eAAe,CAACJ,QAAQ,CAACK,MAAM,CAAC;IAClD,MAAMC,KAAK,GAAGH,KAAK,CAACI,GAAG,CAAC,OAAO,CAAC;IAChC,MAAMC,MAAM,GAAGL,KAAK,CAACI,GAAG,CAAC,QAAQ,CAAC;IAElC,IAAID,KAAK,IAAIE,MAAM,KAAK,SAAS,EAAE;MACjCT,YAAY,CAAC,IAAI,CAAC;MAClB;MACAU,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAE,WAAW,CAAC;IAC9D;EACF,CAAC,EAAE,CAACb,QAAQ,CAACK,MAAM,CAAC,CAAC;EACrB,MAAMS,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC;IAC7C2C,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM0D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM;MACJhB,SAAS;MACTC,QAAQ;MACRC,KAAK;MACLC,OAAO;MACPC,WAAW;MACXE,OAAO;MACPC,IAAI;MACJC;IACF,CAAC,GAAGV,WAAW;IAEf,IAAI,CAACE,SAAS,EAAEgB,MAAM,CAAChB,SAAS,GAAG,wBAAwB;IAC3D,IAAI,CAACC,QAAQ,EAAEe,MAAM,CAACf,QAAQ,GAAG,uBAAuB;IACxD,IAAI,CAACC,KAAK,EAAEc,MAAM,CAACd,KAAK,GAAG,mBAAmB,CAAC,KAC1C,IAAI,CAAC,cAAc,CAACe,IAAI,CAACf,KAAK,CAAC,EAAEc,MAAM,CAACd,KAAK,GAAG,kBAAkB;IACvE,IAAI,CAACC,OAAO,EAAEa,MAAM,CAACb,OAAO,GAAG,qBAAqB;IACpD,IAAI,CAACC,WAAW,EAAE;MAChBY,MAAM,CAACZ,WAAW,GAAG,0BAA0B;IACjD,CAAC,MAAM;MACL,MAAMc,UAAU,GAAGd,WAAW,CAACe,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACjD,IAAID,UAAU,CAACE,MAAM,GAAG,EAAE,EAAE;QAC1BJ,MAAM,CAACZ,WAAW,GAAG,2CAA2C;MAClE;IACF;IACA,IAAI,CAACE,OAAO,EAAEU,MAAM,CAACV,OAAO,GAAG,qBAAqB;IACpD,IAAI,CAACC,IAAI,EAAES,MAAM,CAACT,IAAI,GAAG,kBAAkB;IAC3C,IAAI,CAACC,OAAO,EAAE;MACZQ,MAAM,CAACR,OAAO,GAAG,sBAAsB;IACzC,CAAC,MAAM;MACL,MAAMU,UAAU,GAAGV,OAAO,CAACW,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC7C,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;QAC3BJ,MAAM,CAACR,OAAO,GAAG,2BAA2B;MAC9C;IACF;IAEAM,gBAAgB,CAACE,MAAM,CAAC;IACxB,OAAOK,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC;IAC/C2C,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZE,OAAO,EAAE,EAAE;IACXsB,KAAK,EAAE,EAAE;IACThB,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTJ,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAMuE,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMZ,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM;MAAEhB,SAAS;MAAEC,QAAQ;MAAEE,OAAO;MAAEI,IAAI;MAAEC;IAAQ,CAAC,GAAGe,YAAY;IAEpE,IAAI,CAACvB,SAAS,EAAEgB,MAAM,CAAChB,SAAS,GAAG,wBAAwB;IAC3D,IAAI,CAACC,QAAQ,EAAEe,MAAM,CAACf,QAAQ,GAAG,uBAAuB;IACxD,IAAI,CAACE,OAAO,EAAEa,MAAM,CAACb,OAAO,GAAG,qBAAqB;IACpD,IAAI,CAACI,IAAI,EAAES,MAAM,CAACT,IAAI,GAAG,kBAAkB;IAC3C,IAAI,CAACC,OAAO,EAAE;MACZQ,MAAM,CAACR,OAAO,GAAG,sBAAsB;IACzC,CAAC,MAAM;MACL,MAAMU,UAAU,GAAGV,OAAO,CAACW,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC7C,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;QAC3BJ,MAAM,CAACR,OAAO,GAAG,2BAA2B;MAC9C;IACF;IAEAmB,iBAAiB,CAACX,MAAM,CAAC;IACzB,OAAOK,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EACD,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC;IAC7C0E,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE,EAAE;IACPC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMgF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMrB,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM;MAAEe,UAAU;MAAEC,MAAM;MAAEC;IAAI,CAAC,GAAGJ,WAAW;IAE/C,IAAIA,WAAW,CAACK,aAAa,KAAK,MAAM,EAAE;MACxC,IAAI,CAACH,UAAU,EAAEf,MAAM,CAACe,UAAU,GAAG,yBAAyB,CAAC,KAC1D,IAAIA,UAAU,CAACZ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,MAAM,KAAK,EAAE,EAClDJ,MAAM,CAACe,UAAU,GAAG,+BAA+B;MAErD,IAAI,CAACC,MAAM,EAAEhB,MAAM,CAACgB,MAAM,GAAG,yBAAyB,CAAC,KAClD,IAAI,CAAC,0BAA0B,CAACf,IAAI,CAACe,MAAM,CAAC,EAC/ChB,MAAM,CAACgB,MAAM,GAAG,wBAAwB;MAE1C,IAAI,CAACC,GAAG,EAAEjB,MAAM,CAACiB,GAAG,GAAG,iBAAiB,CAAC,KACpC,IAAI,CAAC,WAAW,CAAChB,IAAI,CAACgB,GAAG,CAAC,EAAEjB,MAAM,CAACiB,GAAG,GAAG,2BAA2B;IAC3E;IAEAG,gBAAgB,CAACpB,MAAM,CAAC;IACxB,OAAOK,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMkB,WAAW,GAAG,GAAG;EAEvB,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpCzC,cAAc,CAACyC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMC,oBAAoB,GAAID,IAAI,IAAK;IACrChB,eAAe,CAACgB,IAAI,CAAC;EACvB,CAAC;EAED,MAAME,mBAAmB,GAAIF,IAAI,IAAK;IACpCV,cAAc,CAACU,IAAI,CAAC;EACtB,CAAC;EAED,MAAMG,mBAAmB,GAAGA,CAAA,KAAM1D,kBAAkB,CAAC,IAAI,CAAC;EAE1D,MAAM2D,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,IAAI,CAACtE,SAAS,IAAIA,SAAS,CAAC4C,MAAM,KAAK,CAAC,EAAE;MACxCyB,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAC;MAC/B;IACF;;IAEA;IACA,MAAMC,aAAa,GAAGxE,SAAS,CAACyE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACpD,MAAMC,OAAO,GAAGD,IAAI,CAACC,OAAO;MAC5B,IAAI,CAACF,GAAG,CAACE,OAAO,CAAC,EAAE;QACjBF,GAAG,CAACE,OAAO,CAAC,GAAG,EAAE;MACnB;MACAF,GAAG,CAACE,OAAO,CAAC,CAACC,IAAI,CAAC;QAChBC,SAAS,EAAEH,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACI,EAAE;QAAE;QACtCC,SAAS,EAAEL,IAAI,CAACK,SAAS,IAAI,IAAI;QAAE;QACnCC,IAAI,EAAEN,IAAI,CAACM,IAAI;QACfC,KAAK,EAAEP,IAAI,CAACQ,SAAS;QACrBC,QAAQ,EAAET,IAAI,CAACS,QAAQ;QACvBC,UAAU,EAAEV,IAAI,CAACQ,SAAS,GAAGR,IAAI,CAACS,QAAQ;QAC1CtB,WAAW,EAAEa,IAAI,CAACb,WAAW;QAC7BwB,KAAK,EAAEX,IAAI,CAACW,KAAK,IAAI,SAAS;QAAE;QAChCC,IAAI,EAAEZ,IAAI,CAACY,IAAI,IAAI,SAAS,CAAE;MAChC,CAAC,CAAC;MACF,OAAOb,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMc,aAAa,GAAG,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IAE3C,IAAI;MACF;MACA,MAAMC,aAAa,GAAG9C,MAAM,CAACC,IAAI,CAAC0B,aAAa,CAAC,CAACoB,GAAG,CAAC,MAAOhB,OAAO,IAAK;QACtE,MAAMiB,SAAS,GAAG;UAChBL,aAAa;UAAE;UACfM,UAAU,EAAE/F,WAAW,CAACgF,EAAE;UAAE;UAC5BgB,cAAc,EAAEzE,WAAW;UAC3B0E,eAAe,EAAEjD,YAAY;UAC7BkD,cAAc,EAAE5C,WAAW;UAC3BrD,SAAS,EAAEwE,aAAa,CAACI,OAAO,CAAC;UAAE;UACnCsB,QAAQ,EAAE1B,aAAa,CAACI,OAAO,CAAC,CAACH,MAAM,CACrC,CAAC0B,GAAG,EAAExB,IAAI,KAAKwB,GAAG,GAAGxB,IAAI,CAACU,UAAU,EACpC,CACF,CAAC;UACDvB,WAAW;UAAE;UACbsC,KAAK,EACH5B,aAAa,CAACI,OAAO,CAAC,CAACH,MAAM,CAC3B,CAAC0B,GAAG,EAAExB,IAAI,KAAKwB,GAAG,GAAGxB,IAAI,CAACU,UAAU,EACpC,CACF,CAAC,GAAGvB,WAAW;UACjBuC,WAAW,EAAE;QACf,CAAC;QACDhC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEuB,SAAS,CAAC;QACzCxB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEvB,YAAY,CAAC;QAC3CsB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhD,WAAW,CAAC;QACzC,OAAO/B,KAAK,CAAC+G,IAAI,CACf,2CAA2C,EAC3CT,SACF,CAAC;MACH,CAAC,CAAC;;MAEF;MACA,MAAMU,SAAS,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACd,aAAa,CAAC;MAClDtB,OAAO,CAACC,GAAG,CACT,8BAA8B,EAC9BiC,SAAS,CAACX,GAAG,CAAEc,GAAG,IAAKA,GAAG,CAAC1C,IAAI,CACjC,CAAC;MACD;MACA1D,YAAY,CAAC,IAAI,CAAC;MAClB;MACAL,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOsE,KAAK,EAAE;MACdF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,KAAK,CAAC;MAC5CF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAM2B,QAAQ,GAAGlG,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE4C,MAAM,GAC9B5C,SAAS,CAACyE,MAAM,CACd,CAAC0B,GAAG,EAAExB,IAAI,KAAKwB,GAAG,GAAG,CAACxB,IAAI,CAACQ,SAAS,IAAI,CAAC,KAAKR,IAAI,CAACS,QAAQ,IAAI,CAAC,CAAC,EACjE,CACF,CAAC,GACD,CAAC;EAEL,MAAMgB,KAAK,GAAGF,QAAQ,IAAIpC,WAAW,IAAI,CAAC,CAAC;;EAE3C;EACA/E,SAAS,CAAC,MAAM;IACd;IACA,MAAM4H,WAAW,GAAG3G,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE4C,MAAM,GACjC5C,SAAS,CAACyE,MAAM,CACd,CAAC0B,GAAG,EAAExB,IAAI,KAAKwB,GAAG,GAAG,CAACxB,IAAI,CAACQ,SAAS,IAAI,CAAC,KAAKR,IAAI,CAACS,QAAQ,IAAI,CAAC,CAAC,EACjE,CACF,CAAC,GACD,CAAC;;IAEL;;IAEA;IACA,IAAIc,QAAQ,KAAKS,WAAW,EAAE;MAC5B;MACA;MACA;IAAA;EAEJ,CAAC,EAAE,CAAC3G,SAAS,EAAEkG,QAAQ,CAAC,CAAC;EAEzBnH,SAAS,CAAC,MAAM;IACdsF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEjE,SAAS,CAAC;EAC9C,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEftB,SAAS,CAAC,MAAM;IACd;IACA,IAAIiC,MAAM,CAACT,QAAQ,CAACqG,QAAQ,KAAK,sBAAsB,EAAE;MACvDnG,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAO,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAE,WAAW,CAAC;IAC9D;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyF,KAAK,GAAG,CACZ;IACE9B,EAAE,EAAE,CAAC;IACL9B,KAAK,EAAE,sBAAsB;IAC7B6D,OAAO,eACLlH,OAAA,CAACX,YAAY;MACX8D,YAAY,EAAEA,YAAa;MAC3BgE,QAAQ,EAAE9C,oBAAqB;MAC/BzB,MAAM,EAAEU,cAAe;MACvB8D,gBAAgB,EAAE;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAEL,CAAC,EACD;IACErC,EAAE,EAAE,CAAC;IACL9B,KAAK,EAAE,qBAAqB;IAC5B6D,OAAO,eACLlH,OAAA,CAACZ,WAAW;MACVsC,WAAW,EAAEA,WAAY;MACzByF,QAAQ,EAAEhD,mBAAoB;MAC9BsD,QAAQ,EAAE;QAAErH,SAAS;QAAEkG,QAAQ;QAAEpC,WAAW;QAAEsC;MAAM,CAAE;MACtD5D,MAAM,EAAEH,aAAc;MACtB2E,gBAAgB,EAAE,IAAK;MACvBjE,YAAY,EAAEA,YAAa,CAAC;IAAA;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAEL,CAAC,EAED;IACErC,EAAE,EAAE,CAAC;IACL9B,KAAK,EAAE,eAAe;IACtB6D,OAAO,eACLlH,OAAA,CAACV,WAAW;MACVmI,QAAQ,EAAE;QAAErH,SAAS;QAAEkG,QAAQ;QAAEpC,WAAW;QAAEsC;MAAM,CAAE;MACtDkB,UAAU,EAAGC,EAAE,IAAMnH,mBAAmB,CAACoH,OAAO,GAAGD;IAAI;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAEL,CAAC,EACD;IACErC,EAAE,EAAE,CAAC;IACL9B,KAAK,EAAE,gBAAgB;IACvB6D,OAAO,eACLlH,OAAA,CAACT,WAAW;MACVsI,QAAQ,EAAErD,mBAAoB;MAC9BsD,SAAS,EAAEA,CAAA,KAAMpH,YAAY,CAAC,IAAI,CAAE;MACpCqH,QAAQ,EAAExD,mBAAoB;MAC9BlE,SAAS,EAAEA,SAAU;MACrBoD,WAAW,EAAEA,WAAY;MACzB0D,QAAQ,EAAE7C,mBAAoB;MAC9B1B,MAAM,EAAEmB,aAAc;MACtB0D,QAAQ,EAAE;QACRrH,SAAS;QACTkG,QAAQ;QACRpC,WAAW;QACXsC,KAAK;QACLL,cAAc,EAAE;UACd9D,SAAS,EAAEc,YAAY,CAACd,SAAS,IAAI,IAAI;UACzC2F,UAAU,EAAEtG,WAAW,CAACE,SAAS;UACjCqG,SAAS,EAAEvG,WAAW,CAACG,QAAQ;UAC/BqG,MAAM,EAAExG,WAAW,CAACK,OAAO;UAC3BQ,QAAQ,EAAEY,YAAY,CAACd,SAAS,IAAI,IAAI;UACxC8F,YAAY,EAAEzG,WAAW,CAACM,WAAW;UACrCG,IAAI,EAAET,WAAW,CAACS,IAAI;UACtBD,OAAO,EAAER,WAAW,CAACQ,OAAO;UAC5BJ,KAAK,EAAEJ,WAAW,CAACI,KAAK;UACxBQ,KAAK,EAAEa,YAAY,CAACb,KAAK,IAAI,IAAI;UACjCE,KAAK,EAAEW,YAAY,CAAChB,IAAI,IAAI;QAC9B,CAAC;QACDiE,eAAe,EAAE;UACf/D,SAAS,EAAEc,YAAY,CAACd,SAAS,IAAI,IAAI;UACzC2F,UAAU,EAAE7E,YAAY,CAACvB,SAAS;UAClCqG,SAAS,EAAE9E,YAAY,CAACtB,QAAQ;UAChCqG,MAAM,EAAE/E,YAAY,CAACpB,OAAO;UAC5BsB,KAAK,EAAEF,YAAY,CAACE,KAAK,IAAI,IAAI;UACjClB,IAAI,EAAEgB,YAAY,CAAChB,IAAI;UACvBC,OAAO,EAAEe,YAAY,CAACf,OAAO;UAC7BF,OAAO,EAAEiB,YAAY,CAACjB;QACxB;MACF;IAAE;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAEL,CAAC,CACF;EAED,oBACExH,OAAA;IAAKoI,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCrI,OAAA;MAAKoI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BrI,OAAA;QAAAqI,QAAA,EAAKpB,KAAK,CAAC3G,WAAW,GAAG,CAAC,CAAC,CAAC+C;MAAK;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtCxI,KAAK,CAACsJ,YAAY,CAACrB,KAAK,CAAC3G,WAAW,GAAG,CAAC,CAAC,CAAC4G,OAAO,EAAE;QAClDO,QAAQ,EACNnH,WAAW,KAAK,CAAC,GACb;UACEF,SAAS;UACTkG,QAAQ;UACRpC,WAAW;UACXsC,KAAK;UACLL,cAAc,EAAE;YACd9D,SAAS,EAAEc,YAAY,CAACd,SAAS,IAAI,IAAI;YACzC2F,UAAU,EAAEtG,WAAW,CAACE,SAAS;YACjCqG,SAAS,EAAEvG,WAAW,CAACG,QAAQ;YAC/BqG,MAAM,EAAExG,WAAW,CAACK,OAAO;YAC3BQ,QAAQ,EAAEY,YAAY,CAACd,SAAS,IAAI,IAAI;YACxC8F,YAAY,EAAEzG,WAAW,CAACM,WAAW;YACrCG,IAAI,EAAET,WAAW,CAACS,IAAI;YACtBD,OAAO,EAAER,WAAW,CAACQ,OAAO;YAC5BJ,KAAK,EAAEJ,WAAW,CAACI,KAAK;YACxBQ,KAAK,EAAEa,YAAY,CAACb,KAAK,IAAI,IAAI;YACjCE,KAAK,EAAEW,YAAY,CAAChB,IAAI,IAAI;UAC9B,CAAC;UACDiE,eAAe,EAAE;YACf/D,SAAS,EAAEc,YAAY,CAACd,SAAS,IAAI,IAAI;YACzC2F,UAAU,EAAE7E,YAAY,CAACvB,SAAS;YAClCqG,SAAS,EAAE9E,YAAY,CAACtB,QAAQ;YAChCqG,MAAM,EAAE/E,YAAY,CAACpB,OAAO;YAC5BsB,KAAK,EAAEF,YAAY,CAACE,KAAK,IAAI,IAAI;YACjClB,IAAI,EAAEgB,YAAY,CAAChB,IAAI;YACvBC,OAAO,EAAEe,YAAY,CAACf,OAAO;YAC7BF,OAAO,EAAEiB,YAAY,CAACjB;UACxB;QACF,CAAC,GACD;UAAE9B,SAAS;UAAEkG,QAAQ;UAAEpC,WAAW;UAAEsC;QAAM;MAClD,CAAC,CAAC,eACFxG,OAAA;QAAKoI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B/H,WAAW,GAAG2G,KAAK,CAACjE,MAAM,iBACzBhD,OAAA;UACEuI,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIjI,WAAW,KAAK,CAAC,IAAI,CAACkD,oBAAoB,CAAC,CAAC,EAAE;YAClD,IAAIlD,WAAW,KAAK,CAAC,IAAI,CAACqC,mBAAmB,CAAC,CAAC,EAAE;YACjD,IACErC,WAAW,KAAK,CAAC,IACjBE,mBAAmB,CAACoH,OAAO,IAC3B,CAACpH,mBAAmB,CAACoH,OAAO,CAAC,CAAC,EAC9B;cACAY,KAAK,CAAC,8CAA8C,CAAC;cACrD;YACF;YACA,IAAIlI,WAAW,KAAK,CAAC,IAAI,CAAC2D,mBAAmB,CAAC,CAAC,EAAE;YAEjD1D,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;UACjC,CAAE;UAAA+H,QAAA,EACH;QAED;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxH,OAAA,CAACJ,cAAc;MACb6I,IAAI,EAAEhI,SAAU;MAChBiI,UAAU,EAAEA,CAAA,KAAM;QAChBhI,YAAY,CAAC,KAAK,CAAC;QACnBe,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;MACjB;IAAE;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEFxH,OAAA,CAACF,gBAAgB;MACf2I,IAAI,EAAE7H,eAAgB;MACtB8H,UAAU,EAAEA,CAAA,KAAM;QAChB7H,kBAAkB,CAAC,KAAK,CAAC;QACzBY,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;MACjB;IAAE;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACtH,EAAA,CApbQD,QAAQ;EAAA,QACSP,OAAO,EACED,OAAO,EAIvBI,WAAW,EAaXL,WAAW;AAAA;AAAAmJ,EAAA,GAnBrB1I,QAAQ;AAsbjB,eAAeA,QAAQ;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}