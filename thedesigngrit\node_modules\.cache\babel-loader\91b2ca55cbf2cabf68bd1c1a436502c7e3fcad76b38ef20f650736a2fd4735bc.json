{"ast": null, "code": "module.exports = require('./src/normalizeWheel.js');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["D:/TDGweb/TDG/thedesigngrit/node_modules/normalize-wheel/index.js"], "sourcesContent": ["module.exports = require('./src/normalizeWheel.js');\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}