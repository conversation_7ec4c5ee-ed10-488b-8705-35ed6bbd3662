{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Vendor-Profile\\\\profileheader.jsx\";\nimport React from \"react\";\nimport { Box, Typography, Button, CardMedia } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VendorProfileHeader({\n  vendor\n}) {\n  const fullImagePath = vendor.brandlogo ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${vendor.brandlogo}` // Full image path for rendering\n  : \"/Assets/TDG_Logo_Black.webp\"; // Default (static) image if brandlogo isn't available\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: \"#fff\",\n      borderRadius: \"24px\",\n      boxShadow: \"0 4px 24px rgba(0,0,0,0.10)\",\n      width: {\n        xs: \"70%\",\n        sm: \"80%\",\n        md: \"80%\",\n        lg: \"70%\"\n      },\n      maxWidth: \"1200px\",\n      margin: \"0 auto\",\n      mt: {\n        xs: 2,\n        md: 4\n      },\n      mb: {\n        xs: 2,\n        md: 4\n      },\n      px: {\n        xs: 2,\n        md: 6\n      },\n      py: {\n        xs: 2,\n        md: 4\n      },\n      display: \"flex\",\n      flexDirection: {\n        xs: \"column\",\n        md: \"row\"\n      },\n      justifyContent: {\n        md: \"space-between\"\n      },\n      alignItems: {\n        xs: \"center\",\n        md: \"flex-start\"\n      },\n      gap: {\n        xs: \"20px\",\n        md: \"0\"\n      },\n      borderBottom: \"none\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: {\n          xs: \"120px\",\n          md: \"150px\"\n        },\n        height: {\n          xs: \"120px\",\n          md: \"150px\"\n        },\n        backgroundColor: \"transparent\",\n        borderRadius: \"16px\",\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        transition: \"transform 0.3s ease\",\n        \"&:hover\": {\n          transform: \"translateY(-5px)\"\n        },\n        marginRight: {\n          xs: 0,\n          md: \"20px\"\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardMedia, {\n        component: \"img\",\n        src: fullImagePath || \"//Assets/TDG_Logo_Black.webp\",\n        alt: \"Logo\",\n        sx: {\n          width: {\n            xs: \"100px\",\n            md: \"120px\"\n          },\n          borderRadius: \"16px\",\n          height: \"auto\",\n          objectFit: \"contain\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        flexDirection: \"column\",\n        flex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          flexDirection: {\n            xs: \"column\",\n            md: \"row\"\n          },\n          justifyContent: {\n            xs: \"center\",\n            md: \"space-between\"\n          },\n          alignItems: \"center\",\n          gap: {\n            xs: \"10px\",\n            md: \"0\"\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontFamily: \"Horizon\",\n            fontWeight: \"bold\",\n            fontSize: {\n              xs: \"18px\",\n              md: \"20px\"\n            },\n            margin: 0\n          },\n          children: vendor.brandName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            gap: \"10px\",\n            flexWrap: {\n              xs: \"wrap\",\n              md: \"nowrap\"\n            },\n            justifyContent: {\n              xs: \"center\",\n              md: \"flex-end\"\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            sx: {\n              width: \"8rem\",\n              height: \"40px\",\n              borderRadius: \"6px\",\n              color: \"white\",\n              backgroundColor: \"black\",\n              fontSize: \"0.6rem\",\n              fontFamily: \"montserrat, san-serif\"\n            },\n            className: \"Vendorprofile-contact-button\",\n            onClick: () => window.location.href = `mailto:${vendor.email}`,\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            sx: {\n              width: \"8rem\",\n              height: \"40px\",\n              borderRadius: \"6px\",\n              color: \"Black\",\n              backgroundColor: \"#E5E5E5\",\n              fontSize: \"0.6rem\",\n              fontFamily: \"montserrat, san-serif\"\n            },\n            className: \"Vendorprofile-website-button\",\n            onClick: () => window.location.href = vendor.websiteURL,\n            children: \"Vendor Website\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontFamily: \"Arial, sans-serif\",\n          color: \"gray\",\n          fontSize: \"14px\",\n          marginTop: \"8px\",\n          lineHeight: \"1.5\",\n          textAlign: {\n            xs: \"justify\",\n            md: \"left\"\n          },\n          padding: \"10px\"\n        },\n        children: vendor.brandDescription\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = VendorProfileHeader;\nexport default VendorProfileHeader;\nvar _c;\n$RefreshReg$(_c, \"VendorProfileHeader\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "CardMedia", "jsxDEV", "_jsxDEV", "VendorProfileHeader", "vendor", "fullImagePath", "brandlogo", "sx", "background", "borderRadius", "boxShadow", "width", "xs", "sm", "md", "lg", "max<PERSON><PERSON><PERSON>", "margin", "mt", "mb", "px", "py", "display", "flexDirection", "justifyContent", "alignItems", "gap", "borderBottom", "children", "height", "backgroundColor", "transition", "transform", "marginRight", "component", "src", "alt", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "fontFamily", "fontWeight", "fontSize", "brandName", "flexWrap", "variant", "color", "className", "onClick", "window", "location", "href", "email", "websiteURL", "marginTop", "lineHeight", "textAlign", "padding", "brandDescription", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Vendor-Profile/profileheader.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Box, Typography, Button, CardMedia } from \"@mui/material\";\r\n\r\nfunction VendorProfileHeader({ vendor }) {\r\n  const fullImagePath = vendor.brandlogo\r\n    ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${vendor.brandlogo}` // Full image path for rendering\r\n    : \"/Assets/TDG_Logo_Black.webp\"; // Default (static) image if brandlogo isn't available\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        background: \"#fff\",\r\n        borderRadius: \"24px\",\r\n        boxShadow: \"0 4px 24px rgba(0,0,0,0.10)\",\r\n        width: { xs: \"70%\", sm: \"80%\", md: \"80%\", lg: \"70%\" },\r\n        maxWidth: \"1200px\",\r\n        margin: \"0 auto\",\r\n        mt: { xs: 2, md: 4 },\r\n        mb: { xs: 2, md: 4 },\r\n        px: { xs: 2, md: 6 },\r\n        py: { xs: 2, md: 4 },\r\n        display: \"flex\",\r\n        flexDirection: { xs: \"column\", md: \"row\" },\r\n        justifyContent: { md: \"space-between\" },\r\n        alignItems: { xs: \"center\", md: \"flex-start\" },\r\n        gap: { xs: \"20px\", md: \"0\" },\r\n        borderBottom: \"none\",\r\n      }}\r\n    >\r\n      {/* Logo Section */}\r\n      <Box\r\n        sx={{\r\n          width: { xs: \"120px\", md: \"150px\" },\r\n          height: { xs: \"120px\", md: \"150px\" },\r\n          backgroundColor: \"transparent\",\r\n          borderRadius: \"16px\",\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"center\",\r\n          transition: \"transform 0.3s ease\",\r\n          \"&:hover\": {\r\n            transform: \"translateY(-5px)\",\r\n          },\r\n          marginRight: { xs: 0, md: \"20px\" },\r\n        }}\r\n      >\r\n        <CardMedia\r\n          component=\"img\"\r\n          src={fullImagePath || \"//Assets/TDG_Logo_Black.webp\"}\r\n          alt=\"Logo\"\r\n          sx={{\r\n            width: { xs: \"100px\", md: \"120px\" },\r\n            borderRadius: \"16px\",\r\n            height: \"auto\",\r\n            objectFit: \"contain\",\r\n          }}\r\n        />\r\n      </Box>\r\n      {/* Content Section */}\r\n      <Box\r\n        sx={{\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n          flex: 1,\r\n        }}\r\n      >\r\n        {/* Title and Buttons in One Row */}\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            flexDirection: { xs: \"column\", md: \"row\" },\r\n            justifyContent: { xs: \"center\", md: \"space-between\" },\r\n            alignItems: \"center\",\r\n            gap: { xs: \"10px\", md: \"0\" },\r\n          }}\r\n        >\r\n          <Typography\r\n            sx={{\r\n              fontFamily: \"Horizon\",\r\n              fontWeight: \"bold\",\r\n              fontSize: { xs: \"18px\", md: \"20px\" },\r\n              margin: 0,\r\n            }}\r\n          >\r\n            {vendor.brandName}\r\n          </Typography>\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              gap: \"10px\",\r\n              flexWrap: { xs: \"wrap\", md: \"nowrap\" },\r\n              justifyContent: { xs: \"center\", md: \"flex-end\" },\r\n            }}\r\n          >\r\n            <Button\r\n              variant=\"outlined\"\r\n              sx={{\r\n                width: \"8rem\",\r\n                height: \"40px\",\r\n                borderRadius: \"6px\",\r\n                color: \"white\",\r\n                backgroundColor: \"black\",\r\n                fontSize: \"0.6rem\",\r\n                fontFamily: \"montserrat, san-serif\",\r\n              }}\r\n              className=\"Vendorprofile-contact-button\"\r\n              onClick={() => (window.location.href = `mailto:${vendor.email}`)}\r\n            >\r\n              Contact\r\n            </Button>\r\n            <Button\r\n              variant=\"contained\"\r\n              sx={{\r\n                width: \"8rem\",\r\n                height: \"40px\",\r\n                borderRadius: \"6px\",\r\n                color: \"Black\",\r\n                backgroundColor: \"#E5E5E5\",\r\n                fontSize: \"0.6rem\",\r\n                fontFamily: \"montserrat, san-serif\",\r\n              }}\r\n              className=\"Vendorprofile-website-button\"\r\n              onClick={() => (window.location.href = vendor.websiteURL)}\r\n            >\r\n              Vendor Website\r\n            </Button>\r\n          </Box>\r\n        </Box>\r\n        {/* Description Text */}\r\n        <Typography\r\n          sx={{\r\n            fontFamily: \"Arial, sans-serif\",\r\n            color: \"gray\",\r\n            fontSize: \"14px\",\r\n            marginTop: \"8px\",\r\n            lineHeight: \"1.5\",\r\n            textAlign: { xs: \"justify\", md: \"left\" },\r\n            padding: \"10px\",\r\n          }}\r\n        >\r\n          {vendor.brandDescription}\r\n        </Typography>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default VendorProfileHeader;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,mBAAmBA,CAAC;EAAEC;AAAO,CAAC,EAAE;EACvC,MAAMC,aAAa,GAAGD,MAAM,CAACE,SAAS,GAClC,uDAAuDF,MAAM,CAACE,SAAS,EAAE,CAAC;EAAA,EAC1E,6BAA6B,CAAC,CAAC;;EAEnC,oBACEJ,OAAA,CAACL,GAAG;IACFU,EAAE,EAAE;MACFC,UAAU,EAAE,MAAM;MAClBC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,6BAA6B;MACxCC,KAAK,EAAE;QAAEC,EAAE,EAAE,KAAK;QAAEC,EAAE,EAAE,KAAK;QAAEC,EAAE,EAAE,KAAK;QAAEC,EAAE,EAAE;MAAM,CAAC;MACrDC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,QAAQ;MAChBC,EAAE,EAAE;QAAEN,EAAE,EAAE,CAAC;QAAEE,EAAE,EAAE;MAAE,CAAC;MACpBK,EAAE,EAAE;QAAEP,EAAE,EAAE,CAAC;QAAEE,EAAE,EAAE;MAAE,CAAC;MACpBM,EAAE,EAAE;QAAER,EAAE,EAAE,CAAC;QAAEE,EAAE,EAAE;MAAE,CAAC;MACpBO,EAAE,EAAE;QAAET,EAAE,EAAE,CAAC;QAAEE,EAAE,EAAE;MAAE,CAAC;MACpBQ,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE;QAAEX,EAAE,EAAE,QAAQ;QAAEE,EAAE,EAAE;MAAM,CAAC;MAC1CU,cAAc,EAAE;QAAEV,EAAE,EAAE;MAAgB,CAAC;MACvCW,UAAU,EAAE;QAAEb,EAAE,EAAE,QAAQ;QAAEE,EAAE,EAAE;MAAa,CAAC;MAC9CY,GAAG,EAAE;QAAEd,EAAE,EAAE,MAAM;QAAEE,EAAE,EAAE;MAAI,CAAC;MAC5Ba,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBAGF1B,OAAA,CAACL,GAAG;MACFU,EAAE,EAAE;QACFI,KAAK,EAAE;UAAEC,EAAE,EAAE,OAAO;UAAEE,EAAE,EAAE;QAAQ,CAAC;QACnCe,MAAM,EAAE;UAAEjB,EAAE,EAAE,OAAO;UAAEE,EAAE,EAAE;QAAQ,CAAC;QACpCgB,eAAe,EAAE,aAAa;QAC9BrB,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBM,UAAU,EAAE,qBAAqB;QACjC,SAAS,EAAE;UACTC,SAAS,EAAE;QACb,CAAC;QACDC,WAAW,EAAE;UAAErB,EAAE,EAAE,CAAC;UAAEE,EAAE,EAAE;QAAO;MACnC,CAAE;MAAAc,QAAA,eAEF1B,OAAA,CAACF,SAAS;QACRkC,SAAS,EAAC,KAAK;QACfC,GAAG,EAAE9B,aAAa,IAAI,8BAA+B;QACrD+B,GAAG,EAAC,MAAM;QACV7B,EAAE,EAAE;UACFI,KAAK,EAAE;YAAEC,EAAE,EAAE,OAAO;YAAEE,EAAE,EAAE;UAAQ,CAAC;UACnCL,YAAY,EAAE,MAAM;UACpBoB,MAAM,EAAE,MAAM;UACdQ,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvC,OAAA,CAACL,GAAG;MACFU,EAAE,EAAE;QACFe,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBmB,IAAI,EAAE;MACR,CAAE;MAAAd,QAAA,gBAGF1B,OAAA,CAACL,GAAG;QACFU,EAAE,EAAE;UACFe,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE;YAAEX,EAAE,EAAE,QAAQ;YAAEE,EAAE,EAAE;UAAM,CAAC;UAC1CU,cAAc,EAAE;YAAEZ,EAAE,EAAE,QAAQ;YAAEE,EAAE,EAAE;UAAgB,CAAC;UACrDW,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;YAAEd,EAAE,EAAE,MAAM;YAAEE,EAAE,EAAE;UAAI;QAC7B,CAAE;QAAAc,QAAA,gBAEF1B,OAAA,CAACJ,UAAU;UACTS,EAAE,EAAE;YACFoC,UAAU,EAAE,SAAS;YACrBC,UAAU,EAAE,MAAM;YAClBC,QAAQ,EAAE;cAAEjC,EAAE,EAAE,MAAM;cAAEE,EAAE,EAAE;YAAO,CAAC;YACpCG,MAAM,EAAE;UACV,CAAE;UAAAW,QAAA,EAEDxB,MAAM,CAAC0C;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbvC,OAAA,CAACL,GAAG;UACFU,EAAE,EAAE;YACFe,OAAO,EAAE,MAAM;YACfI,GAAG,EAAE,MAAM;YACXqB,QAAQ,EAAE;cAAEnC,EAAE,EAAE,MAAM;cAAEE,EAAE,EAAE;YAAS,CAAC;YACtCU,cAAc,EAAE;cAAEZ,EAAE,EAAE,QAAQ;cAAEE,EAAE,EAAE;YAAW;UACjD,CAAE;UAAAc,QAAA,gBAEF1B,OAAA,CAACH,MAAM;YACLiD,OAAO,EAAC,UAAU;YAClBzC,EAAE,EAAE;cACFI,KAAK,EAAE,MAAM;cACbkB,MAAM,EAAE,MAAM;cACdpB,YAAY,EAAE,KAAK;cACnBwC,KAAK,EAAE,OAAO;cACdnB,eAAe,EAAE,OAAO;cACxBe,QAAQ,EAAE,QAAQ;cAClBF,UAAU,EAAE;YACd,CAAE;YACFO,SAAS,EAAC,8BAA8B;YACxCC,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAUlD,MAAM,CAACmD,KAAK,EAAI;YAAA3B,QAAA,EAClE;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvC,OAAA,CAACH,MAAM;YACLiD,OAAO,EAAC,WAAW;YACnBzC,EAAE,EAAE;cACFI,KAAK,EAAE,MAAM;cACbkB,MAAM,EAAE,MAAM;cACdpB,YAAY,EAAE,KAAK;cACnBwC,KAAK,EAAE,OAAO;cACdnB,eAAe,EAAE,SAAS;cAC1Be,QAAQ,EAAE,QAAQ;cAClBF,UAAU,EAAE;YACd,CAAE;YACFO,SAAS,EAAC,8BAA8B;YACxCC,OAAO,EAAEA,CAAA,KAAOC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGlD,MAAM,CAACoD,UAAY;YAAA5B,QAAA,EAC3D;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA,CAACJ,UAAU;QACTS,EAAE,EAAE;UACFoC,UAAU,EAAE,mBAAmB;UAC/BM,KAAK,EAAE,MAAM;UACbJ,QAAQ,EAAE,MAAM;UAChBY,SAAS,EAAE,KAAK;UAChBC,UAAU,EAAE,KAAK;UACjBC,SAAS,EAAE;YAAE/C,EAAE,EAAE,SAAS;YAAEE,EAAE,EAAE;UAAO,CAAC;UACxC8C,OAAO,EAAE;QACX,CAAE;QAAAhC,QAAA,EAEDxB,MAAM,CAACyD;MAAgB;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACqB,EAAA,GA9IQ3D,mBAAmB;AAgJ5B,eAAeA,mBAAmB;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}