{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\product\\\\optionPopUp.jsx\",\n  _s = $RefreshSig$();\n// RequestInfoPopup.js\nimport React, { useState, useContext } from \"react\";\nimport { Dialog, DialogContent, DialogTitle, IconButton, useMediaQuery } from \"@mui/material\";\nimport { IoIosClose } from \"react-icons/io\";\nimport ViewInStorePopup from \"./viewInStore\";\nimport RequestQuote from \"./RequestInfo\";\nimport { UserContext } from \"../../utils/userContext\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RequestInfoPopup = ({\n  open,\n  onClose,\n  productId\n}) => {\n  _s();\n  const {\n    userSession\n  } = useContext(UserContext);\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n  const navigate = useNavigate();\n  // Add productId here\n  const [isViewInStoreOpen, setIsViewInStoreOpen] = useState(false);\n  const [isRequestQuoteOpen, setIsRequestQuoteOpen] = useState(false);\n  const handleViewInStoreClick = () => {\n    if (!userSession) {\n      navigate(\"/login\");\n    } else {\n      setIsViewInStoreOpen(true);\n      onClose();\n    }\n  };\n  const handleCloseViewInStore = () => {\n    setIsViewInStoreOpen(false);\n  };\n  const handleRequestQuoteClick = () => {\n    if (!userSession) {\n      navigate(\"/login\");\n    } else {\n      setIsRequestQuoteOpen(true);\n      onClose();\n    }\n  };\n  const handleCloseRequestQuote = () => {\n    setIsRequestQuoteOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: onClose,\n      maxWidth: \"lg\",\n      fullWidth: true,\n      className: \"request-popup-dialog\",\n      classes: {\n        paper: \"request-popup-paper\"\n      },\n      PaperProps: {\n        sx: {\n          backdropFilter: \"blur(12px)\",\n          // backgroundColor: \"rgba(255, 255, 255, 0.15)\",\n          backgroundColor: \"white\",\n          boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.2)\",\n          borderRadius: \"20px\",\n          border: \"1px solid rgba(255, 255, 255, 0.18)\"\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: onClose,\n        sx: {\n          position: \"absolute\",\n          top: \"16px\",\n          right: \"16px\",\n          color: \"#2d2d2d\"\n        },\n        children: /*#__PURE__*/_jsxDEV(IoIosClose, {\n          size: 50\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogTitle, {\n        className: \"request-popup-title\",\n        sx: {\n          color: \"#fff\",\n          textAlign: \"center\",\n          fontWeight: 600,\n          fontSize: \"24px\",\n          paddingTop: \"32px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: \"#2d2d2d\",\n            fontSize: isMobile ? \"16px\" : \"24px\"\n          },\n          children: [\" \", \"Choose an Option\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        className: \"request-popup-content\",\n        sx: {\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"16px\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          paddingBottom: \"32px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRequestQuoteClick,\n          className: \"request-popup-button\",\n          style: {\n            backgroundColor: \"rgba(255, 255, 255, 0.3)\",\n            border: \"1px solid rgba(255,255,255,0.3)\",\n            backdropFilter: \"blur(6px)\",\n            borderRadius: \"12px\",\n            padding: \"12px 24px\",\n            color: \"#2d2d2d\",\n            fontWeight: \"500\",\n            cursor: \"pointer\",\n            width: isMobile ? \"100%\" : \"40%\"\n          },\n          children: \"Request Quote\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleViewInStoreClick,\n          className: \"request-popup-button\",\n          style: {\n            backgroundColor: \"rgba(255, 255, 255, 0.3)\",\n            border: \"1px solid rgba(255,255,255,0.3)\",\n            backdropFilter: \"blur(6px)\",\n            borderRadius: \"12px\",\n            padding: \"12px 24px\",\n            color: \"#2d2d2d\",\n            fontWeight: \"500\",\n            cursor: \"pointer\",\n            width: isMobile ? \"100%\" : \"40%\"\n          },\n          children: \"View in Store\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ViewInStorePopup, {\n      open: isViewInStoreOpen,\n      onClose: handleCloseViewInStore,\n      productId: productId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), isRequestQuoteOpen && /*#__PURE__*/_jsxDEV(RequestQuote, {\n      onClose: handleCloseRequestQuote,\n      productId: productId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(RequestInfoPopup, \"Iqdmxg6rANG+XkEJnl/gn+7JbN0=\", false, function () {\n  return [useMediaQuery, useNavigate];\n});\n_c = RequestInfoPopup;\nexport default RequestInfoPopup;\nvar _c;\n$RefreshReg$(_c, \"RequestInfoPopup\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "IconButton", "useMediaQuery", "IoIosClose", "ViewInStorePopup", "RequestQuote", "UserContext", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RequestInfoPopup", "open", "onClose", "productId", "_s", "userSession", "isMobile", "navigate", "isViewInStoreOpen", "setIsViewInStoreOpen", "isRequestQuoteOpen", "setIsRequestQuoteOpen", "handleViewInStoreClick", "handleCloseViewInStore", "handleRequestQuoteClick", "handleCloseRequestQuote", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "className", "classes", "paper", "PaperProps", "sx", "<PERSON><PERSON>ilter", "backgroundColor", "boxShadow", "borderRadius", "border", "onClick", "position", "top", "right", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "fontWeight", "fontSize", "paddingTop", "style", "display", "flexDirection", "gap", "justifyContent", "alignItems", "paddingBottom", "padding", "cursor", "width", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/product/optionPopUp.jsx"], "sourcesContent": ["// RequestInfoPopup.js\r\nimport React, { useState, useContext } from \"react\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogTitle,\r\n  IconButton,\r\n  useMediaQuery,\r\n} from \"@mui/material\";\r\nimport { IoIosClose } from \"react-icons/io\";\r\nimport ViewInStorePopup from \"./viewInStore\";\r\nimport RequestQuote from \"./RequestInfo\";\r\nimport { UserContext } from \"../../utils/userContext\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nconst RequestInfoPopup = ({ open, onClose, productId }) => {\r\n  const { userSession } = useContext(UserContext);\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n  const navigate = useNavigate();\r\n  // Add productId here\r\n  const [isViewInStoreOpen, setIsViewInStoreOpen] = useState(false);\r\n  const [isRequestQuoteOpen, setIsRequestQuoteOpen] = useState(false);\r\n\r\n  const handleViewInStoreClick = () => {\r\n    if (!userSession) {\r\n      navigate(\"/login\");\r\n    } else {\r\n      setIsViewInStoreOpen(true);\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  const handleCloseViewInStore = () => {\r\n    setIsViewInStoreOpen(false);\r\n  };\r\n\r\n  const handleRequestQuoteClick = () => {\r\n    if (!userSession) {\r\n      navigate(\"/login\");\r\n    } else {\r\n      setIsRequestQuoteOpen(true);\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  const handleCloseRequestQuote = () => {\r\n    setIsRequestQuoteOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Dialog\r\n        open={open}\r\n        onClose={onClose}\r\n        maxWidth=\"lg\"\r\n        fullWidth\r\n        className=\"request-popup-dialog\"\r\n        classes={{\r\n          paper: \"request-popup-paper\",\r\n        }}\r\n        PaperProps={{\r\n          sx: {\r\n            backdropFilter: \"blur(12px)\",\r\n            // backgroundColor: \"rgba(255, 255, 255, 0.15)\",\r\n            backgroundColor: \"white\",\r\n            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.2)\",\r\n            borderRadius: \"20px\",\r\n            border: \"1px solid rgba(255, 255, 255, 0.18)\",\r\n          },\r\n        }}\r\n      >\r\n        <IconButton\r\n          onClick={onClose}\r\n          sx={{\r\n            position: \"absolute\",\r\n            top: \"16px\",\r\n            right: \"16px\",\r\n            color: \"#2d2d2d\",\r\n          }}\r\n        >\r\n          <IoIosClose size={50} />\r\n        </IconButton>\r\n        <DialogTitle\r\n          className=\"request-popup-title\"\r\n          sx={{\r\n            color: \"#fff\",\r\n            textAlign: \"center\",\r\n            fontWeight: 600,\r\n            fontSize: \"24px\",\r\n            paddingTop: \"32px\",\r\n          }}\r\n        >\r\n          <h2\r\n            style={{ color: \"#2d2d2d\", fontSize: isMobile ? \"16px\" : \"24px\" }}\r\n          >\r\n            {\" \"}\r\n            Choose an Option\r\n          </h2>\r\n        </DialogTitle>\r\n        <DialogContent\r\n          className=\"request-popup-content\"\r\n          sx={{\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            gap: \"16px\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            paddingBottom: \"32px\",\r\n          }}\r\n        >\r\n          <button\r\n            onClick={handleRequestQuoteClick}\r\n            className=\"request-popup-button\"\r\n            style={{\r\n              backgroundColor: \"rgba(255, 255, 255, 0.3)\",\r\n              border: \"1px solid rgba(255,255,255,0.3)\",\r\n              backdropFilter: \"blur(6px)\",\r\n              borderRadius: \"12px\",\r\n              padding: \"12px 24px\",\r\n              color: \"#2d2d2d\",\r\n              fontWeight: \"500\",\r\n              cursor: \"pointer\",\r\n              width: isMobile ? \"100%\" : \"40%\",\r\n            }}\r\n          >\r\n            Request Quote\r\n          </button>\r\n          <button\r\n            onClick={handleViewInStoreClick}\r\n            className=\"request-popup-button\"\r\n            style={{\r\n              backgroundColor: \"rgba(255, 255, 255, 0.3)\",\r\n              border: \"1px solid rgba(255,255,255,0.3)\",\r\n              backdropFilter: \"blur(6px)\",\r\n              borderRadius: \"12px\",\r\n              padding: \"12px 24px\",\r\n              color: \"#2d2d2d\",\r\n              fontWeight: \"500\",\r\n              cursor: \"pointer\",\r\n              width: isMobile ? \"100%\" : \"40%\",\r\n            }}\r\n          >\r\n            View in Store\r\n          </button>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* ViewInStorePopup */}\r\n      <ViewInStorePopup\r\n        open={isViewInStoreOpen}\r\n        onClose={handleCloseViewInStore}\r\n        productId={productId}\r\n      />\r\n\r\n      {/* RequestQuote Popup */}\r\n      {isRequestQuoteOpen && (\r\n        <RequestQuote onClose={handleCloseRequestQuote} productId={productId} />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default RequestInfoPopup;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SACEC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,aAAa,QACR,eAAe;AACtB,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAOC,gBAAgB,MAAM,eAAe;AAC5C,OAAOC,YAAY,MAAM,eAAe;AACxC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC/C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM;IAAEC;EAAY,CAAC,GAAGpB,UAAU,CAACS,WAAW,CAAC;EAC/C,MAAMY,QAAQ,GAAGhB,aAAa,CAAC,mBAAmB,CAAC;EACnD,MAAMiB,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAM4B,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACP,WAAW,EAAE;MAChBE,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLE,oBAAoB,CAAC,IAAI,CAAC;MAC1BP,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMW,sBAAsB,GAAGA,CAAA,KAAM;IACnCJ,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMK,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACT,WAAW,EAAE;MAChBE,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLI,qBAAqB,CAAC,IAAI,CAAC;MAC3BT,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMa,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;EAED,oBACEd,OAAA,CAAAE,SAAA;IAAAiB,QAAA,gBACEnB,OAAA,CAACX,MAAM;MACLe,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEA,OAAQ;MACjBe,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,SAAS,EAAC,sBAAsB;MAChCC,OAAO,EAAE;QACPC,KAAK,EAAE;MACT,CAAE;MACFC,UAAU,EAAE;QACVC,EAAE,EAAE;UACFC,cAAc,EAAE,YAAY;UAC5B;UACAC,eAAe,EAAE,OAAO;UACxBC,SAAS,EAAE,+BAA+B;UAC1CC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV;MACF,CAAE;MAAAZ,QAAA,gBAEFnB,OAAA,CAACR,UAAU;QACTwC,OAAO,EAAE3B,OAAQ;QACjBqB,EAAE,EAAE;UACFO,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,MAAM;UACXC,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACT,CAAE;QAAAjB,QAAA,eAEFnB,OAAA,CAACN,UAAU;UAAC2C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACbzC,OAAA,CAACT,WAAW;QACV+B,SAAS,EAAC,qBAAqB;QAC/BI,EAAE,EAAE;UACFU,KAAK,EAAE,MAAM;UACbM,SAAS,EAAE,QAAQ;UACnBC,UAAU,EAAE,GAAG;UACfC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAA1B,QAAA,eAEFnB,OAAA;UACE8C,KAAK,EAAE;YAAEV,KAAK,EAAE,SAAS;YAAEQ,QAAQ,EAAEnC,QAAQ,GAAG,MAAM,GAAG;UAAO,CAAE;UAAAU,QAAA,GAEjE,GAAG,EAAC,kBAEP;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACdzC,OAAA,CAACV,aAAa;QACZgC,SAAS,EAAC,uBAAuB;QACjCI,EAAE,EAAE;UACFqB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE,MAAM;UACXC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,aAAa,EAAE;QACjB,CAAE;QAAAjC,QAAA,gBAEFnB,OAAA;UACEgC,OAAO,EAAEf,uBAAwB;UACjCK,SAAS,EAAC,sBAAsB;UAChCwB,KAAK,EAAE;YACLlB,eAAe,EAAE,0BAA0B;YAC3CG,MAAM,EAAE,iCAAiC;YACzCJ,cAAc,EAAE,WAAW;YAC3BG,YAAY,EAAE,MAAM;YACpBuB,OAAO,EAAE,WAAW;YACpBjB,KAAK,EAAE,SAAS;YAChBO,UAAU,EAAE,KAAK;YACjBW,MAAM,EAAE,SAAS;YACjBC,KAAK,EAAE9C,QAAQ,GAAG,MAAM,GAAG;UAC7B,CAAE;UAAAU,QAAA,EACH;QAED;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzC,OAAA;UACEgC,OAAO,EAAEjB,sBAAuB;UAChCO,SAAS,EAAC,sBAAsB;UAChCwB,KAAK,EAAE;YACLlB,eAAe,EAAE,0BAA0B;YAC3CG,MAAM,EAAE,iCAAiC;YACzCJ,cAAc,EAAE,WAAW;YAC3BG,YAAY,EAAE,MAAM;YACpBuB,OAAO,EAAE,WAAW;YACpBjB,KAAK,EAAE,SAAS;YAChBO,UAAU,EAAE,KAAK;YACjBW,MAAM,EAAE,SAAS;YACjBC,KAAK,EAAE9C,QAAQ,GAAG,MAAM,GAAG;UAC7B,CAAE;UAAAU,QAAA,EACH;QAED;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzC,OAAA,CAACL,gBAAgB;MACfS,IAAI,EAAEO,iBAAkB;MACxBN,OAAO,EAAEW,sBAAuB;MAChCV,SAAS,EAAEA;IAAU;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,EAGD5B,kBAAkB,iBACjBb,OAAA,CAACJ,YAAY;MAACS,OAAO,EAAEa,uBAAwB;MAACZ,SAAS,EAAEA;IAAU;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACxE;EAAA,eACD,CAAC;AAEP,CAAC;AAAClC,EAAA,CAjJIJ,gBAAgB;EAAA,QAEHV,aAAa,EACbK,WAAW;AAAA;AAAA0D,EAAA,GAHxBrD,gBAAgB;AAmJtB,eAAeA,gBAAgB;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}