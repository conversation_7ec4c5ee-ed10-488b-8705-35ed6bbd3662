{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\successMsgs\\\\orderSubmit.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { IoClose } from \"react-icons/io5\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderSentPopup = ({\n  show: propShow,\n  closePopup: propClosePopup\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [show, setShow] = useState(propShow || false);\n\n  // ✅ Add this effect to listen to prop changes\n  useEffect(() => {\n    setShow(propShow);\n  }, [propShow]);\n  const handleClose = () => {\n    setShow(false);\n    if (propClosePopup) {\n      propClosePopup();\n    }\n  };\n  if (!show) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Job-sent-popup-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Job-sent-popup-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"Job-sent-popup-close-icon\",\n        onClick: handleClose,\n        children: /*#__PURE__*/_jsxDEV(IoClose, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"center\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mail-icon\",\n          style: {\n            width: \"100px\",\n            height: \"100px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"Assets/orderIconCycle.gif\",\n            alt: \"animated delivery icon\",\n            className: \"animated-mail-icon\",\n            style: {\n              width: \"100%\",\n              height: \"100%\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"YOUR ORDER HAS BEEN SUBMITTED SUCCESSFULLY!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"THANKS,\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), \" For Choosing us.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"Job-sent-popup-button\",\n        onClick: () => {\n          navigate(\"/\");\n          handleClose();\n        },\n        children: \"Done\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(OrderSentPopup, \"PIqIV/KYN8G9wyyAL2ohWkPzQd0=\", false, function () {\n  return [useNavigate];\n});\n_c = OrderSentPopup;\nexport default OrderSentPopup;\nvar _c;\n$RefreshReg$(_c, \"OrderSentPopup\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "IoClose", "useNavigate", "jsxDEV", "_jsxDEV", "OrderSentPopup", "show", "propShow", "closePopup", "propClosePopup", "_s", "navigate", "setShow", "handleClose", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "display", "flexDirection", "alignItems", "width", "height", "src", "alt", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/successMsgs/orderSubmit.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { IoClose } from \"react-icons/io5\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nconst OrderSentPopup = ({ show: propShow, closePopup: propClosePopup }) => {\r\n  const navigate = useNavigate();\r\n  const [show, setShow] = useState(propShow || false);\r\n\r\n  // ✅ Add this effect to listen to prop changes\r\n  useEffect(() => {\r\n    setShow(propShow);\r\n  }, [propShow]);\r\n\r\n  const handleClose = () => {\r\n    setShow(false);\r\n    if (propClosePopup) {\r\n      propClosePopup();\r\n    }\r\n  };\r\n\r\n  if (!show) return null;\r\n\r\n  return (\r\n    <div className=\"Job-sent-popup-overlay\">\r\n      <div className=\"Job-sent-popup-container\">\r\n        <div className=\"Job-sent-popup-close-icon\" onClick={handleClose}>\r\n          <IoClose />\r\n        </div>\r\n        <div\r\n          style={{\r\n            textAlign: \"center\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <div\r\n            className=\"mail-icon\"\r\n            style={{ width: \"100px\", height: \"100px\" }}\r\n          >\r\n            <img\r\n              src={\"Assets/orderIconCycle.gif\"}\r\n              alt=\"animated delivery icon\"\r\n              className=\"animated-mail-icon\"\r\n              style={{ width: \"100%\", height: \"100%\" }}\r\n            />\r\n            {/* <TbTruckDelivery className=\"animated-mail-icon\" /> */}\r\n          </div>\r\n\r\n          <h1>YOUR ORDER HAS BEEN SUBMITTED SUCCESSFULLY!</h1>\r\n          <p>\r\n            <strong>THANKS,</strong> For Choosing us.\r\n            <br />\r\n          </p>\r\n        </div>\r\n        <button\r\n          className=\"Job-sent-popup-button\"\r\n          onClick={() => {\r\n            navigate(\"/\");\r\n            handleClose();\r\n          }}\r\n        >\r\n          Done\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrderSentPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,EAAEC,QAAQ;EAAEC,UAAU,EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACI,IAAI,EAAEM,OAAO,CAAC,GAAGZ,QAAQ,CAACO,QAAQ,IAAI,KAAK,CAAC;;EAEnD;EACAR,SAAS,CAAC,MAAM;IACda,OAAO,CAACL,QAAQ,CAAC;EACnB,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBD,OAAO,CAAC,KAAK,CAAC;IACd,IAAIH,cAAc,EAAE;MAClBA,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;EAED,IAAI,CAACH,IAAI,EAAE,OAAO,IAAI;EAEtB,oBACEF,OAAA;IAAKU,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrCX,OAAA;MAAKU,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvCX,OAAA;QAAKU,SAAS,EAAC,2BAA2B;QAACE,OAAO,EAAEH,WAAY;QAAAE,QAAA,eAC9DX,OAAA,CAACH,OAAO;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACNhB,OAAA;QACEiB,KAAK,EAAE;UACLC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,gBAEFX,OAAA;UACEU,SAAS,EAAC,WAAW;UACrBO,KAAK,EAAE;YAAEK,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAQ,CAAE;UAAAZ,QAAA,eAE3CX,OAAA;YACEwB,GAAG,EAAE,2BAA4B;YACjCC,GAAG,EAAC,wBAAwB;YAC5Bf,SAAS,EAAC,oBAAoB;YAC9BO,KAAK,EAAE;cAAEK,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEC,CAAC,eAENhB,OAAA;UAAAW,QAAA,EAAI;QAA2C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDhB,OAAA;UAAAW,QAAA,gBACEX,OAAA;YAAAW,QAAA,EAAQ;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBACxB,eAAAhB,OAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNhB,OAAA;QACEU,SAAS,EAAC,uBAAuB;QACjCE,OAAO,EAAEA,CAAA,KAAM;UACbL,QAAQ,CAAC,GAAG,CAAC;UACbE,WAAW,CAAC,CAAC;QACf,CAAE;QAAAE,QAAA,EACH;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACV,EAAA,CA/DIL,cAAc;EAAA,QACDH,WAAW;AAAA;AAAA4B,EAAA,GADxBzB,cAAc;AAiEpB,eAAeA,cAAc;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}