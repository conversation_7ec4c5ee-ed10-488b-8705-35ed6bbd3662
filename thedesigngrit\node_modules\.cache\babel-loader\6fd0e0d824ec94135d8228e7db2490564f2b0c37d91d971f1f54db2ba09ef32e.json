{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\UpdateProduct.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { TiDeleteOutline } from \"react-icons/ti\"; // Import the delete icon\nimport { useVendor } from \"../../utils/vendorContext\";\nimport { Box, IconButton } from \"@mui/material\";\nimport { IoIosArrowRoundBack } from \"react-icons/io\";\nimport ConfirmationDialog from \"../confirmationMsg\";\nimport Cropper from \"react-easy-crop\";\nimport CircularProgress from \"@mui/material/CircularProgress\";\nimport Dialog from \"@mui/material/Dialog\";\nimport DialogActions from \"@mui/material/DialogActions\";\nimport DialogContent from \"@mui/material/DialogContent\";\nimport DialogContentText from \"@mui/material/DialogContentText\";\nimport DialogTitle from \"@mui/material/DialogTitle\";\nimport Button from \"@mui/material/Button\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getCroppedImg = (imageSrc, croppedAreaPixels) => {\n  return new Promise(resolve => {\n    const image = new window.Image();\n    image.src = imageSrc;\n    image.onload = () => {\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = croppedAreaPixels.width;\n      canvas.height = croppedAreaPixels.height;\n      const ctx = canvas.getContext(\"2d\");\n      ctx.drawImage(image, croppedAreaPixels.x, croppedAreaPixels.y, croppedAreaPixels.width, croppedAreaPixels.height, 0, 0, croppedAreaPixels.width, croppedAreaPixels.height);\n      canvas.toBlob(blob => resolve(blob), \"image/jpeg\");\n    };\n  });\n};\nconst UpdateProduct = ({\n  existingProduct,\n  onBack\n}) => {\n  _s();\n  const {\n    vendor\n  } = useVendor(); // Access vendor data from context\n\n  // State variables\n  const [brandName, setBrandName] = useState(\"\");\n  const [categories, setCategories] = useState([]); // Categories from API\n  const [subCategories, setSubCategories] = useState([]); // Subcategories from API\n  const [types, setTypes] = useState([]); // Types from API\n  const [selectedCategory, setSelectedCategory] = useState(\"\");\n  const [selectedSubCategory, setSelectedSubCategory] = useState(\"\");\n  const [tags, setTags] = useState([]); // Tags array\n\n  // Store initial form data for comparison\n  const [initialFormData, setInitialFormData] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    price: \"\",\n    salePrice: null,\n    category: \"\",\n    subcategory: \"\",\n    collection: \"\",\n    type: \"\",\n    manufactureYear: \"\",\n    tags: [],\n    // Tags array in formData\n    reviews: [],\n    // Initialize reviews as an empty array\n    colors: [],\n    sizes: [],\n    images: [],\n    mainImage: \"\",\n    description: \"\",\n    technicalDimensions: {\n      length: \"\",\n      width: \"\",\n      height: \"\",\n      weight: \"\"\n    },\n    brandId: \"\",\n    brandName: \"\",\n    leadTime: \"\",\n    stock: \"\",\n    sku: \"\",\n    readyToShip: false,\n    // Add readyToShip field with default value false\n    warrantyInfo: {\n      warrantyYears: \"\",\n      warrantyCoverage: []\n    },\n    materialCareInstructions: \"\",\n    productSpecificRecommendations: \"\",\n    Estimatedtimeleadforcustomization: \"\",\n    cadFile: null // Add CAD field\n  });\n  const [showCropModal, setShowCropModal] = useState(false);\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [crop, setCrop] = useState({\n    x: 0,\n    y: 0\n  });\n  const [zoom, setZoom] = useState(1);\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\n  const [imagePreviews, setImagePreviews] = useState([]);\n  const [mainImagePreview, setMainImagePreview] = useState(null);\n  const [isDialogOpen, setDialogOpen] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showSuccessDialog, setShowSuccessDialog] = useState(false);\n  const [tagOptions, setTagOptions] = useState({});\n  const [validationErrors, setValidationErrors] = useState([]);\n\n  // Fetch categories on mount\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await axios.get(\"https://api.thedesigngrit.com/api/categories/categories\");\n        setCategories(response.data); // Assuming the response contains categories\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n      }\n    };\n    fetchCategories();\n  }, []);\n\n  // Fetch brand details using brandId from the vendor session\n  useEffect(() => {\n    if (vendor !== null && vendor !== void 0 && vendor.brandId) {\n      const fetchBrandName = async () => {\n        try {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/brand/${vendor.brandId}`);\n          setBrandName(response.data.brandName); // Set the brand name in state\n          setFormData(prevData => ({\n            ...prevData,\n            brandName: response.data.brandName // Update formData with brand name\n          }));\n        } catch (error) {\n          console.error(\"Error fetching brand name:\", error);\n        }\n      };\n      fetchBrandName();\n    }\n  }, [vendor === null || vendor === void 0 ? void 0 : vendor.brandId]); // Removed brandName dependency to prevent infinite loop\n\n  // Set brandId from vendor context\n  useEffect(() => {\n    if (vendor) {\n      setFormData(prevFormData => ({\n        ...prevFormData,\n        brandId: vendor.brandId || \"\" // Ensure the brandId exists in the vendor data\n      }));\n    }\n  }, [vendor]);\n\n  // Populate form with existing product data\n  useEffect(() => {\n    if (existingProduct) {\n      setFormData(existingProduct);\n      // Store initial form data for comparison (deep copy)\n      setInitialFormData(JSON.parse(JSON.stringify(existingProduct)));\n      setTags(existingProduct.tags || []);\n      setSelectedCategory(existingProduct.category);\n      setSelectedSubCategory(existingProduct.subcategory);\n      setImages(existingProduct.images || []); // Set existing images\n      setMainImage(existingProduct.mainImage || \"\"); // Set existing main image\n\n      // Set up image previews for existing images\n      if (existingProduct.images && existingProduct.images.length > 0) {\n        const previews = existingProduct.images.map(image => typeof image === \"string\" ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}` : URL.createObjectURL(image));\n        setImagePreviews(previews);\n\n        // Set main image preview\n        if (existingProduct.mainImage) {\n          const mainPreview = typeof existingProduct.mainImage === \"string\" ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${existingProduct.mainImage}` : URL.createObjectURL(existingProduct.mainImage);\n          setMainImagePreview(mainPreview);\n        }\n      }\n\n      // Fetch subcategories and types based on existing product data\n      const fetchSubCategoriesAndTypes = async () => {\n        try {\n          const subCategoryResponse = await axios.get(`https://api.thedesigngrit.com/api/subcategories/byCategory/${existingProduct.category}`);\n          setSubCategories(subCategoryResponse.data);\n          const typeResponse = await axios.get(`https://api.thedesigngrit.com/api/subcategories/bySubcategory/${existingProduct.subcategory}`);\n          setTypes(typeResponse.data);\n        } catch (error) {\n          console.error(\"Error fetching subcategories or types:\", error);\n        }\n      };\n      fetchSubCategoriesAndTypes();\n    }\n  }, [existingProduct]);\n\n  // Fetch tag options for dropdowns\n  useEffect(() => {\n    const fetchTags = async () => {\n      try {\n        const categories = [\"Color\", \"Shape\", \"Size\", \"Material\", \"Style\", \"Finish\", \"Functionality\"];\n        const fetchedTags = {};\n        for (const category of categories) {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/tags/tags/${category}`);\n          fetchedTags[category] = response.data.map(tag => tag.name);\n        }\n        setTagOptions(fetchedTags);\n      } catch (error) {\n        console.error(\"Error fetching tags:\", error);\n      }\n    };\n    fetchTags();\n  }, []);\n\n  // Handle category change\n  const handleCategoryChange = async e => {\n    const selectedCategoryId = e.target.value;\n    setSelectedCategory(selectedCategoryId);\n    setFormData({\n      ...formData,\n      category: selectedCategoryId // Update formData.category\n    });\n    setSubCategories([]); // Reset subcategories\n    setSelectedSubCategory(\"\"); // Reset subcategory\n    try {\n      // Fetch subcategories for the selected category\n      const response = await axios.get(`https://api.thedesigngrit.com/api/subcategories/byCategory/${selectedCategoryId}`);\n      setSubCategories(response.data);\n    } catch (error) {\n      console.error(\"Error fetching subcategories:\", error);\n    }\n  };\n\n  // Handle subcategory change\n  const handleSubCategoryChange = async e => {\n    const selectedSubCategoryId = e.target.value;\n    setSelectedSubCategory(selectedSubCategoryId);\n    setFormData({\n      ...formData,\n      subcategory: selectedSubCategoryId // Update formData.subcategory\n    });\n    setTypes([]); // Reset types\n\n    try {\n      // Fetch types that are associated with the selected subcategory\n      const response = await axios.get(`https://api.thedesigngrit.com/api/subcategories/bySubcategory/${selectedSubCategoryId}`);\n      setTypes(response.data); // Set types based on the fetched data\n    } catch (error) {\n      console.error(\"Error fetching types:\", error);\n    }\n  };\n\n  // Handle input change for basic fields\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Special handling for leadTime and Estimatedtimeleadforcustomization to ensure they're valid ranges\n    if (name === \"leadTime\" || name === \"Estimatedtimeleadforcustomization\") {\n      // Allow numbers and a single hyphen\n      const validValue = value.replace(/[^\\d-]/g, \"\");\n      // Ensure only one hyphen\n      const parts = validValue.split(\"-\");\n      if (parts.length > 2) {\n        // If more than one hyphen, keep only the first one\n        const firstPart = parts[0];\n        const secondPart = parts[1];\n        setFormData({\n          ...formData,\n          [name]: `${firstPart}-${secondPart}`\n        });\n      } else {\n        setFormData({\n          ...formData,\n          [name]: validValue\n        });\n      }\n      return;\n    }\n\n    // Handle bullet points for specific fields\n    if (name === \"materialCareInstructions\") {\n      // For material care instructions, just allow normal line breaks\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    } else if (name === \"productSpecificRecommendations\") {\n      // For product recommendations, add bullet points\n      const formattedValue = value.split(\"\\n\").map(line => line.startsWith(\"•\") ? line : `• ${line.trim()}`).join(\"\\n\");\n      setFormData({\n        ...formData,\n        [name]: formattedValue\n      });\n    } else {\n      // For all other fields, just update normally\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  // Handle nested input change\n  const handleNestedChange = (e, parentField) => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [parentField]: {\n        ...formData[parentField],\n        [name]: value\n      }\n    });\n  };\n\n  // Handle array fields (tags, colors, sizes, warrantyCoverage)\n  const handleArrayChange = (e, field, parentField = null) => {\n    const {\n      value\n    } = e.target;\n\n    // Split the input value by commas and trim each item\n    const arrayValues = value.split(\",\").map(item => item.trim());\n    if (parentField) {\n      // Handle nested fields (e.g., warrantyInfo.warrantyCoverage)\n      setFormData({\n        ...formData,\n        [parentField]: {\n          ...formData[parentField],\n          [field]: arrayValues // Ensure this is an array\n        }\n      });\n    } else {\n      // Handle top-level fields (e.g., tags, colors, sizes)\n      setFormData({\n        ...formData,\n        [field]: arrayValues // Ensure this is an array\n      });\n    }\n  };\n\n  // Handle adding new tags\n  const handleAddTag = e => {\n    if (e.key === \"Enter\" && e.target.value.trim() !== \"\") {\n      const newTag = e.target.value.trim();\n      if (!tags.includes(newTag)) {\n        setTags([...tags, newTag]);\n        setFormData({\n          ...formData,\n          tags: [...formData.tags, newTag]\n        });\n      }\n      e.target.value = \"\";\n    }\n  };\n\n  // Function to remove a tag by index\n  const handleRemoveTag = index => {\n    const newTags = tags.filter((_, i) => i !== index);\n    setTags(newTags);\n    setFormData({\n      ...formData,\n      tags: newTags\n    });\n  };\n  const [images, setImages] = useState(existingProduct.images || []); // Initialize with existing images\n  const [mainImage, setMainImage] = useState(existingProduct.mainImage || \"\"); // Initialize with existing main image\n\n  // Handle image upload\n  const handleImageUpload = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const img = new window.Image();\n    img.onload = () => {\n      if (img.width < 400 || img.height < 300) {\n        alert(\"Image too small. Minimum size: 400x300px for 4:3 ratio.\");\n        return;\n      }\n      const previewUrl = URL.createObjectURL(file);\n      setSelectedImageSrc(previewUrl);\n      setPendingFile(file);\n      setShowCropModal(true);\n    };\n    img.src = URL.createObjectURL(file);\n  };\n  const handleCropComplete = async () => {\n    const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\n    const url = URL.createObjectURL(blob);\n    const croppedFile = new File([blob], pendingFile.name, {\n      type: \"image/jpeg\"\n    });\n    setImages(prev => [...prev, croppedFile]);\n    setImagePreviews(prev => [...prev, url]);\n    if (!mainImage) {\n      setMainImage(croppedFile);\n      setMainImagePreview(url);\n    }\n    setFormData(prevData => ({\n      ...prevData,\n      images: [...prevData.images, croppedFile],\n      mainImage: prevData.mainImage || croppedFile\n    }));\n    setShowCropModal(false);\n    setPendingFile(null);\n    setSelectedImageSrc(null);\n  };\n\n  // Handle setting the main image\n  const handleSetMainImage = index => {\n    setMainImage(images[index]); // Set main image file\n    setMainImagePreview(imagePreviews[index]); // Set preview\n\n    setFormData(prevData => ({\n      ...prevData,\n      mainImage: images[index] // Update formData with the selected main image\n    }));\n  };\n\n  // Handle removing an image\n  const handleRemoveImage = index => {\n    const updatedImages = images.filter((_, i) => i !== index);\n    const updatedPreviews = imagePreviews.filter((_, i) => i !== index);\n    setImages(updatedImages);\n    setImagePreviews(updatedPreviews);\n\n    // If the removed image was the main image, update the main image\n    if (images[index] === mainImage) {\n      setMainImage(updatedImages[0] || null);\n      setMainImagePreview(updatedPreviews[0] || null);\n      setFormData(prevData => ({\n        ...prevData,\n        mainImage: updatedImages[0] || \"\" // Update mainImage in formData\n      }));\n    }\n\n    // Update formData images list\n    setFormData(prevData => ({\n      ...prevData,\n      images: updatedImages\n    }));\n  };\n\n  // Handle cancel\n  const handleCancel = () => {\n    onBack();\n  };\n\n  // Add handlers for new features\n  const handleCheckboxChange = e => {\n    const {\n      name,\n      checked\n    } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: checked\n    }));\n  };\n  // Add CAD file handling functions\n  const handleCADUpload = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Check file type\n      const allowedTypes = [\".dwg\", \".dxf\", \".stp\", \".step\", \".igs\", \".iges\"];\n      const fileExtension = \".\" + file.name.split(\".\").pop().toLowerCase();\n      if (!allowedTypes.includes(fileExtension)) {\n        alert(\"Please upload a valid CAD file (DWG, DXF, STP, STEP, IGS, or IGES format)\");\n        return;\n      }\n      setFormData(prevData => ({\n        ...prevData,\n        cadFile: file // Changed from 'cad' to 'cadFile' to match backend\n      }));\n    }\n  };\n  const handleWarrantyCoverageChange = coverage => {\n    setFormData(prevState => {\n      const currentCoverage = prevState.warrantyInfo.warrantyCoverage;\n      let newCoverage;\n      if (currentCoverage.includes(coverage)) {\n        newCoverage = currentCoverage.filter(item => item !== coverage);\n      } else {\n        newCoverage = [...currentCoverage, coverage];\n      }\n      return {\n        ...prevState,\n        warrantyInfo: {\n          ...prevState.warrantyInfo,\n          warrantyCoverage: newCoverage\n        }\n      };\n    });\n  };\n  const handleSelectTag = (category, value) => {\n    if (value && !tags.includes(value)) {\n      setTags([...tags, value]);\n      setFormData({\n        ...formData,\n        tags: [...formData.tags, value]\n      });\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === \"Enter\") {\n      e.preventDefault();\n      const {\n        name\n      } = e.target;\n      if (name === \"productSpecificRecommendations\") {\n        setFormData(prev => ({\n          ...prev,\n          productSpecificRecommendations: (prev.productSpecificRecommendations ? prev.productSpecificRecommendations + \"\\n• \" : \"• \") + e.target.value.slice(e.target.selectionStart)\n        }));\n      } else if (name === \"materialCareInstructions\") {\n        setFormData(prev => ({\n          ...prev,\n          materialCareInstructions: (prev.materialCareInstructions ? prev.materialCareInstructions + \"\\n\" : \"\") + e.target.value.slice(e.target.selectionStart)\n        }));\n      }\n    }\n  };\n\n  // Update handleSubmit to match AddProduct logic\n  const handleOpenDialog = () => setDialogOpen(true);\n  const handleCloseDialog = () => setDialogOpen(false);\n  const handleCloseSuccessDialog = () => {\n    setShowSuccessDialog(false);\n    onBack();\n  };\n  const validateForm = () => {\n    const errors = [];\n    if (!formData.name.trim()) errors.push(\"Product Name\");\n    if (!formData.price || isNaN(formData.price)) errors.push(\"Product Price\");\n    if (!formData.sku.trim()) errors.push(\"SKU\");\n    if (!formData.collection.trim()) errors.push(\"Collection\");\n    if (!formData.stock || isNaN(formData.stock)) errors.push(\"Stock\");\n    if (!formData.leadTime.trim()) errors.push(\"Lead Time\");\n    if (!formData.description.trim()) errors.push(\"Description\");\n    if (!formData.images || formData.images.length === 0) errors.push(\"At least one image\");\n    return errors;\n  };\n\n  // Helper function to deep compare arrays\n  const arraysEqual = (arr1, arr2) => {\n    if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;\n    if (arr1.length !== arr2.length) return false;\n    return arr1.every((val, index) => val === arr2[index]);\n  };\n\n  // Helper function to deep compare objects\n  const objectsEqual = (obj1, obj2) => {\n    if (!obj1 || !obj2) return obj1 === obj2;\n    const keys1 = Object.keys(obj1);\n    const keys2 = Object.keys(obj2);\n    if (keys1.length !== keys2.length) return false;\n    return keys1.every(key => {\n      if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {\n        return arraysEqual(obj1[key], obj2[key]);\n      }\n      return obj1[key] === obj2[key];\n    });\n  };\n\n  // Helper function to compare image arrays (considering both files and strings)\n  const imagesChanged = () => {\n    if (!initialFormData || !initialFormData.images) return true;\n    const currentImages = formData.images || [];\n    const initialImages = initialFormData.images || [];\n\n    // Check if lengths are different\n    if (currentImages.length !== initialImages.length) return true;\n\n    // Check if any image is new (File object) or if order changed\n    return currentImages.some((img, index) => {\n      if (img instanceof File) return true; // New image\n\n      const initialImg = initialImages[index];\n      // Compare string paths (remove URL prefix if present)\n      const currentPath = typeof img === \"string\" ? img.replace(/^.*\\//, \"\") : img;\n      const initialPath = typeof initialImg === \"string\" ? initialImg.replace(/^.*\\//, \"\") : initialImg;\n      return currentPath !== initialPath;\n    });\n  };\n\n  // Function to get only changed fields\n  const getChangedFields = () => {\n    if (!initialFormData) return formData;\n    const changes = {};\n    const hasNewCADFile = formData.cadFile instanceof File;\n    const hasImageChanges = imagesChanged();\n\n    // Check each field for changes\n    Object.keys(formData).forEach(key => {\n      const currentValue = formData[key];\n      const initialValue = initialFormData[key];\n\n      // Handle images with detailed change detection\n      if (key === \"images\") {\n        if (hasImageChanges) {\n          changes[key] = {\n            images: formData.images,\n            imageOperation: \"update\" // Indicates we need to update the entire image set\n          };\n        }\n        return;\n      }\n\n      // Handle mainImage changes\n      if (key === \"mainImage\") {\n        const currentMainImage = typeof currentValue === \"string\" ? currentValue.replace(/^.*\\//, \"\") : currentValue;\n        const initialMainImage = typeof initialValue === \"string\" ? initialValue.replace(/^.*\\//, \"\") : initialValue;\n        if (currentMainImage !== initialMainImage) {\n          changes[key] = currentValue;\n        }\n        return;\n      }\n      if (key === \"cadFile\") {\n        if (hasNewCADFile) {\n          changes[key] = formData[key];\n        }\n        return;\n      }\n\n      // Handle arrays\n      if (Array.isArray(currentValue)) {\n        if (!arraysEqual(currentValue, initialValue)) {\n          changes[key] = currentValue;\n        }\n      }\n      // Handle objects (like technicalDimensions, warrantyInfo)\n      else if (typeof currentValue === \"object\" && currentValue !== null) {\n        if (!objectsEqual(currentValue, initialValue)) {\n          changes[key] = currentValue;\n        }\n      }\n      // Handle primitive values\n      else if (currentValue !== initialValue) {\n        // Skip empty/null values that haven't actually changed\n        if (currentValue === \"\" && (initialValue === \"\" || initialValue === null || initialValue === undefined)) {\n          return;\n        }\n        if (currentValue === null && (initialValue === null || initialValue === undefined || initialValue === \"\")) {\n          return;\n        }\n\n        // Additional check for string values that might be the same after trimming\n        if (typeof currentValue === \"string\" && typeof initialValue === \"string\") {\n          if (currentValue.trim() === initialValue.trim()) {\n            return;\n          }\n        }\n\n        // Additional check for number values that might be the same when converted\n        if (typeof currentValue === \"number\" || typeof initialValue === \"number\") {\n          if (Number(currentValue) === Number(initialValue)) {\n            return;\n          }\n        }\n        changes[key] = currentValue;\n      }\n    });\n    return changes;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setDialogOpen(false);\n    setIsSubmitting(true);\n\n    // Get only the changed fields\n    const changedFields = getChangedFields();\n\n    // Debug logging to help identify false positives\n    console.log(\"=== CHANGE DETECTION DEBUG ===\");\n    console.log(\"Initial form data:\", initialFormData);\n    console.log(\"Current form data:\", formData);\n    console.log(\"Detected changes:\", changedFields);\n    console.log(\"===============================\");\n\n    // If no changes detected, show message and return\n    if (Object.keys(changedFields).length === 0) {\n      alert(\"No changes detected to update.\");\n      setIsSubmitting(false);\n      return;\n    }\n    console.log(\"Changed fields:\", changedFields);\n    const data = new FormData();\n\n    // Only append changed fields to FormData\n    Object.keys(changedFields).forEach(key => {\n      const value = changedFields[key];\n      if (key === \"images\") {\n        // Handle image changes - send all current images\n        if (value.imageOperation === \"update\") {\n          // Send all current images (mix of existing strings and new File objects)\n          value.images.forEach(image => {\n            data.append(\"images\", image);\n          });\n\n          // Also send information about which images to keep/remove\n          const existingImagePaths = initialFormData.images || [];\n          const currentImagePaths = value.images.map(img => typeof img === \"string\" ? img.replace(/^.*\\//, \"\") : \"NEW_FILE\");\n\n          // Send metadata about image operations\n          data.append(\"imageMetadata\", JSON.stringify({\n            existingImages: existingImagePaths,\n            currentImages: currentImagePaths,\n            operation: \"update\"\n          }));\n        }\n      } else if (key === \"cadFile\" && value) {\n        data.append(\"cadFile\", value);\n      } else if (key === \"tags\" && Array.isArray(value)) {\n        value.forEach((tag, index) => data.append(`tags[${index}]`, tag));\n      } else if (key === \"colors\" && Array.isArray(value)) {\n        value.forEach((color, index) => data.append(`colors[${index}]`, color));\n      } else if (key === \"sizes\" && Array.isArray(value)) {\n        value.forEach((size, index) => data.append(`sizes[${index}]`, size));\n      } else if (key === \"technicalDimensions\" && typeof value === \"object\") {\n        data.append(\"technicalDimensions\", JSON.stringify(value));\n      } else if (key === \"warrantyInfo\" && typeof value === \"object\") {\n        data.append(\"warrantyInfo\", JSON.stringify(value));\n      } else {\n        // Handle primitive values\n        data.append(key, value || \"\");\n      }\n    });\n\n    // Always include mainImage if it exists (for backend processing)\n    if (formData.mainImage) {\n      data.append(\"mainImage\", formData.mainImage);\n    }\n\n    // Log FormData for debugging\n    console.log(\"Sending only changed fields:\");\n    for (let [key, value] of data.entries()) {\n      console.log(`${key}:`, value);\n    }\n    try {\n      const response = await axios.put(`https://api.thedesigngrit.com/api/products/${formData._id}`, data, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      console.log(\"Product updated successfully:\", response.data);\n      setShowSuccessDialog(true);\n    } catch (error) {\n      var _error$response;\n      console.error(\"Error updating product:\", ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error);\n      alert(\"Failed to update product. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            flexDirection: \"row\",\n            gap: \"10px\",\n            justifyContent: \"flex-start\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            children: /*#__PURE__*/_jsxDEV(IoIosArrowRoundBack, {\n              size: \"30px\",\n              onClick: onBack\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Update Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Home > Update Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 834,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 833,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: e => e.preventDefault(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Update Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                placeholder: \"Ex: L-shaped Sofa \",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \" Product Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"price\",\n                value: formData.price,\n                onChange: handleChange,\n                placeholder: \"Ex: 10,000.00\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Category:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"category\",\n                value: formData.category,\n                onChange: handleCategoryChange,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Select Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category._id,\n                  children: category.name\n                }, category._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Subcategory:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"subcategory\",\n                value: formData.subcategory,\n                onChange: handleSubCategoryChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Select Subcategory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory._id,\n                  children: subCategory.name\n                }, subCategory._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"type\",\n                value: formData.type,\n                onChange: handleChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Select Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 19\n                }, this), types.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type._id,\n                  children: type.name\n                }, type._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Tag\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dropdown-container\",\n                children: Object.entries(tagOptions).map(([category, options]) => /*#__PURE__*/_jsxDEV(\"select\", {\n                  onChange: e => {\n                    handleSelectTag(category, e.target.value);\n                    e.target.value = \"\";\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: `Select ${category}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 959,\n                    columnNumber: 23\n                  }, this), options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, option, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 25\n                  }, this))]\n                }, category, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"tags\",\n                placeholder: \"Add tag and press Enter  Ex: Sofa, Living Room\",\n                onKeyDown: handleAddTag,\n                className: \"tag-input\",\n                style: {\n                  margin: \"10px 0px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tags\",\n                children: tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tag\",\n                  children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => handleRemoveTag(index),\n                    className: \"remove-tag-btn\",\n                    children: /*#__PURE__*/_jsxDEV(TiDeleteOutline, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 986,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Product Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Collection:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"collection\",\n                value: formData.collection,\n                onChange: handleChange,\n                placeholder: \"Ex: Living Room\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Manufacture Year:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"manufactureYear\",\n                value: formData.manufactureYear,\n                onChange: handleChange,\n                placeholder: \"Ex: 2023\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Colors (comma separated):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"grey\",\n                  margin: \"5px\"\n                },\n                children: \"Enter the colors of the product which be vaild for variants.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"colors\",\n                value: formData.colors ? formData.colors.join(\",\") : \"\" // Check if colors is defined\n                ,\n                onChange: e => handleArrayChange(e, \"colors\"),\n                placeholder: \"Ex: Red, Blue, Green\",\n                style: {\n                  marginTop: \"10px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1029,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Sizes (comma separated):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"grey\",\n                  margin: \"5px\"\n                },\n                children: \"Enter the sizes of the product which be vaild for variants.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"sizes\",\n                value: formData.sizes.join(\",\"),\n                onChange: e => handleArrayChange(e, \"sizes\"),\n                placeholder: \"Ex: Small, Medium, Large\",\n                style: {\n                  marginTop: \"10px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Description:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"description\",\n                value: formData.description,\n                onChange: e => {\n                  const words = e.target.value.trim().split(/\\s+/).filter(word => word.length > 0);\n                  if (words.length <= 10) {\n                    handleChange(e);\n                  }\n                },\n                placeholder: \"Provide a brief product description (max 10 words). Include key features and benefits.\",\n                maxLength: \"2000\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: \"12px\",\n                  color: \"#666\",\n                  marginTop: \"5px\"\n                },\n                children: [\"Word count:\", \" \", formData.description.trim().split(/\\s+/).filter(word => word.length > 0).length, \"/10 words\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1094,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1077,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                textAlign: \"left\",\n                marginBottom: \"10px\",\n                marginTop: \"20px\"\n              },\n              children: \"Technical Dimensions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                gap: \"10px\",\n                flexDirection: \"row\",\n                justifyContent: \"space-between\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Length:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"length\",\n                  value: formData.technicalDimensions.length,\n                  placeholder: \"CM\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Width:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"width\",\n                  value: formData.technicalDimensions.width,\n                  placeholder: \"CM\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"height\",\n                  value: formData.technicalDimensions.height,\n                  placeholder: \"CM\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"weight\",\n                  value: formData.technicalDimensions.weight,\n                  placeholder: \"Kg\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Brand Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Brand ID:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"brandId\",\n                value: formData.brandId,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Brand Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"brandName\",\n                value: formData.brandName,\n                onChange: handleChange,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"readyToShip\",\n                  checked: formData.readyToShip,\n                  onChange: handleCheckboxChange,\n                  style: {\n                    width: \"auto\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 19\n                }, this), \"Ready to Ship\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: \"normal\"\n                  },\n                  children: \"(That The Product is Ready to Ship )\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Lead Time:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"leadTime\",\n                value: formData.leadTime,\n                onChange: handleChange,\n                placeholder: \"Enter lead time range (e.g., 5-7 days)\",\n                required: true,\n                pattern: \"\\\\d+-\\\\d+\",\n                title: \"Please enter a valid range (e.g., 5-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Stock:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"stock\",\n                value: formData.stock,\n                onChange: handleChange,\n                placeholder: \"Enter the stock quantity  Ex:100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"SKU:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"sku\",\n                value: formData.sku,\n                onChange: handleChange,\n                placeholder: \"Enter the Stock Keeping Unit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Warranty Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Warranty Years:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"warrantyYears\",\n                value: formData.warrantyInfo.warrantyYears,\n                onChange: e => handleNestedChange(e, \"warrantyInfo\"),\n                placeholder: \"Enter the number of years of warranty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Warranty Coverage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  flexDirection: \"row\",\n                  gap: \"10px\",\n                  marginTop: \"10px\"\n                },\n                children: [\"Manufacturer Defects\", \"Wear and Tear\", \"Damage During Shipping\"].map(coverage => /*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"8px\",\n                    cursor: \"pointer\",\n                    fontSize: \"14px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.warrantyInfo.warrantyCoverage.includes(coverage),\n                    onChange: () => handleWarrantyCoverageChange(coverage),\n                    style: {\n                      cursor: \"pointer\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1294,\n                    columnNumber: 23\n                  }, this), coverage]\n                }, coverage, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Material Care Instructions:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"materialCareInstructions\",\n                value: formData.materialCareInstructions,\n                onChange: handleChange,\n                onKeyDown: handleKeyDown,\n                placeholder: \"Enter the material care instructions\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Product Specific Recommendations:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"productSpecificRecommendations\",\n                value: formData.productSpecificRecommendations,\n                onChange: handleChange,\n                onKeyDown: handleKeyDown,\n                placeholder: \"Enter the product specific recommendations\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1326,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Estimated Time Lead for Customization:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"Estimatedtimeleadforcustomization\",\n                value: formData.Estimatedtimeleadforcustomization,\n                onChange: handleChange,\n                placeholder: \"Enter time range (e.g., 5-7 days)\",\n                pattern: \"\\\\d+-\\\\d+\",\n                title: \"Please enter a valid range (e.g., 5-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-placeholder\",\n            children: mainImagePreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: mainImagePreview // Use preview URL instead of file object\n              ,\n              alt: \"Main Preview\",\n              className: \"main-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1360,\n              columnNumber: 17\n            }, this) : mainImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: typeof mainImage === \"string\" ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${mainImage}` : URL.createObjectURL(mainImage),\n              alt: \"Main Preview\",\n              className: \"main-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1366,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                border: \"1px solid #8A9A5B\",\n                borderRadius: \"10px\",\n                color: \"#71797E\",\n                margin: \" auto\",\n                width: \"30%\",\n                textAlign: \"center\",\n                padding: \"10px\",\n                marginTop: \"80px\"\n              },\n              children: \"Image Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1376,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-gallery\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Product Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"drop-zone\",\n              onDragOver: e => e.preventDefault() // Prevent default to allow drop\n              ,\n              onDrop: e => {\n                e.preventDefault(); // Prevent default behavior\n                const files = Array.from(e.dataTransfer.files); // Access dropped files\n                handleImageUpload({\n                  target: {\n                    files\n                  }\n                }); // Pass to handleImageUpload\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"(Upload high-quality images with a minimum resolution of 1080x1080px. Use .jpeg or .png format. Ensure clear visibility of the product from multiple angles. White background)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1405,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                multiple: true,\n                accept: \"image/jpeg, image/png, image/webp\",\n                onChange: handleImageUpload,\n                className: \"file-input\",\n                style: {\n                  display: \"none\"\n                } // Hide the input visually\n                ,\n                id: \"fileInput\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"fileInput\",\n                className: \"drop-zone-label\",\n                children: [\"Drop your image here, or browse\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1423,\n                  columnNumber: 19\n                }, this), \"Jpeg, png are allowed\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => document.getElementById(\"fileInput\").click(),\n                className: \"upload-btn\",\n                children: \"Upload Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1426,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"thumbnail-list\",\n              children: [imagePreviews.map((preview, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `thumbnail ${preview === mainImagePreview ? \"main-thumbnail\" : \"\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    gap: \"10px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: preview,\n                    alt: `Thumbnail ${index}`,\n                    className: \"image-thumbnail\",\n                    onClick: () => handleSetMainImage(index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Product thumbnail.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1456,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkmark\",\n                    children: preview === mainImagePreview ? \"✔ Main\" : \"✔\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1457,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    gap: \"10px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"remove-thumbnail\",\n                    onClick: () => handleRemoveImage(index),\n                    children: \"\\u2716\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1469,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1461,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1436,\n                columnNumber: 19\n              }, this)), images.filter((_, index) => !imagePreviews[index]).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `thumbnail ${image === mainImage ? \"main-thumbnail\" : \"\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    gap: \"10px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: typeof image === \"string\" ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}` : URL.createObjectURL(image),\n                    alt: `Thumbnail ${index}`,\n                    className: \"image-thumbnail\",\n                    onClick: () => handleSetMainImage(imagePreviews.length + index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Product thumbnail.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1508,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkmark\",\n                    children: image === mainImage ? \"✔ Main\" : \"✔\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1509,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1488,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    gap: \"10px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"remove-thumbnail\",\n                    onClick: () => handleRemoveImage(imagePreviews.length + index),\n                    children: \"\\u2716\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1513,\n                  columnNumber: 23\n                }, this)]\n              }, `existing-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1482,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1434,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cad-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"CAD File Upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1537,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cad-drop-zone\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".dwg,.dxf,.stp,.step,.igs,.iges\",\n                onChange: handleCADUpload,\n                className: \"cad-file-input\",\n                style: {\n                  display: \"none\"\n                },\n                id: \"cadFileInput\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"cadFileInput\",\n                className: \"cad-drop-zone-label\",\n                children: [\"Drop your CAD file here, or browse\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1549,\n                  columnNumber: 19\n                }, this), \"Supported formats: DWG, DXF, STP, STEP, IGS, IGES\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => document.getElementById(\"cadFileInput\").click(),\n                className: \"upload-btn\",\n                children: \"Upload CAD File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1552,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 15\n            }, this), formData.cadFile &&\n            /*#__PURE__*/\n            // Changed from 'cad' to 'cadFile'\n            _jsxDEV(\"div\", {\n              className: \"cad-file-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Selected file: \", formData.cadFile.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1564,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setFormData(prev => ({\n                  ...prev,\n                  cadFile: null\n                })),\n                className: \"remove-cad-btn\",\n                children: \"\\u2716\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1565,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1563,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1536,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [validationErrors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: \"red\",\n            marginBottom: \"10px\"\n          },\n          children: [\"Please fill the following required fields:\", \" \", validationErrors.join(\", \")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1581,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn update\",\n          type: \"button\",\n          onClick: () => {\n            const errors = validateForm();\n            if (errors.length > 0) {\n              setValidationErrors(errors);\n              return;\n            }\n            setValidationErrors([]);\n            handleOpenDialog();\n          },\n          disabled: isSubmitting,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1600,\n            columnNumber: 29\n          }, this) : \"UPDATE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1586,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn cancel\",\n          onClick: handleCancel,\n          children: \"CANCEL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1602,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1579,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationDialog, {\n      open: isDialogOpen,\n      title: \"Confirm Product Update\",\n      content: \"Are you sure you want to update this product?\",\n      onConfirm: handleSubmit,\n      onCancel: handleCloseDialog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1607,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showSuccessDialog,\n      onClose: handleCloseSuccessDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Success\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1615,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Product updated successfully!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1617,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1616,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseSuccessDialog,\n          color: \"primary\",\n          children: \"Done\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1620,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1619,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1614,\n      columnNumber: 7\n    }, this), showCropModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay-uploadimage\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content-uploadimage\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cropper-container-uploadimage\",\n          children: /*#__PURE__*/_jsxDEV(Cropper, {\n            image: selectedImageSrc,\n            crop: crop,\n            zoom: zoom,\n            aspect: 4 / 3,\n            onCropChange: setCrop,\n            onZoomChange: setZoom,\n            onCropComplete: (_, area) => setCroppedAreaPixels(area)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1629,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1628,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cropper-buttons-uploadimage\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCropComplete,\n            children: \"Crop Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1640,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCropModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1641,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1639,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1627,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1626,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(UpdateProduct, \"gBZRHinzA41MalNRPrVJ3ZtrTdY=\", false, function () {\n  return [useVendor];\n});\n_c = UpdateProduct;\nexport default UpdateProduct;\nvar _c;\n$RefreshReg$(_c, \"UpdateProduct\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "TiDeleteOutline", "useVendor", "Box", "IconButton", "IoIosArrowRoundBack", "ConfirmationDialog", "C<PERSON>per", "CircularProgress", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getCroppedImg", "imageSrc", "croppedAreaPixels", "Promise", "resolve", "image", "window", "Image", "src", "onload", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "drawImage", "x", "y", "toBlob", "blob", "UpdateProduct", "existingProduct", "onBack", "_s", "vendor", "brandName", "setBrandName", "categories", "setCategories", "subCategories", "setSubCategories", "types", "setTypes", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "tags", "setTags", "initialFormData", "setInitialFormData", "formData", "setFormData", "name", "price", "salePrice", "category", "subcategory", "collection", "type", "manufactureYear", "reviews", "colors", "sizes", "images", "mainImage", "description", "technicalDimensions", "length", "weight", "brandId", "leadTime", "stock", "sku", "readyToShip", "warrantyInfo", "warrantyYears", "warrantyCoverage", "materialCareInstructions", "productSpecificRecommendations", "Estimatedtimeleadforcustomization", "cadFile", "showCropModal", "setShowCropModal", "selectedImageSrc", "setSelectedImageSrc", "pendingFile", "setPendingFile", "crop", "setCrop", "zoom", "setZoom", "setCroppedAreaPixels", "imagePreviews", "setImagePreviews", "mainImagePreview", "setMainImagePreview", "isDialogOpen", "setDialogOpen", "isSubmitting", "setIsSubmitting", "showSuccessDialog", "setShowSuccessDialog", "tagOptions", "setTagOptions", "validationErrors", "setValidationErrors", "fetchCategories", "response", "get", "data", "error", "console", "fetchBrandName", "prevData", "prevFormData", "JSON", "parse", "stringify", "setImages", "setMainImage", "previews", "map", "URL", "createObjectURL", "mainPreview", "fetchSubCategoriesAndTypes", "subCategoryResponse", "typeResponse", "fetchTags", "fetchedTags", "tag", "handleCategoryChange", "e", "selectedCategoryId", "target", "value", "handleSubCategoryChange", "selectedSubCategoryId", "handleChange", "validValue", "replace", "parts", "split", "firstPart", "second<PERSON><PERSON>", "formattedValue", "line", "startsWith", "trim", "join", "handleNestedChange", "parentField", "handleArrayChange", "field", "arrayValues", "item", "handleAddTag", "key", "newTag", "includes", "handleRemoveTag", "index", "newTags", "filter", "_", "i", "handleImageUpload", "file", "files", "img", "alert", "previewUrl", "handleCropComplete", "url", "croppedFile", "File", "prev", "handleSetMainImage", "handleRemoveImage", "updatedImages", "updatedPreviews", "handleCancel", "handleCheckboxChange", "checked", "prevState", "handleCADUpload", "allowedTypes", "fileExtension", "pop", "toLowerCase", "handleWarrantyCoverageChange", "coverage", "currentCoverage", "newCoverage", "handleSelectTag", "handleKeyDown", "preventDefault", "slice", "selectionStart", "handleOpenDialog", "handleCloseDialog", "handleCloseSuccessDialog", "validateForm", "errors", "push", "isNaN", "arraysEqual", "arr1", "arr2", "Array", "isArray", "every", "val", "objectsEqual", "obj1", "obj2", "keys1", "Object", "keys", "keys2", "imagesChanged", "currentImages", "initialImages", "some", "initialImg", "currentPath", "initialPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "changes", "hasNewCADFile", "hasImageChanges", "for<PERSON>ach", "currentValue", "initialValue", "imageOperation", "currentMainImage", "initialMainImage", "undefined", "Number", "handleSubmit", "changed<PERSON>ields", "log", "FormData", "append", "existingImagePaths", "currentImagePaths", "existingImages", "operation", "color", "size", "entries", "put", "_id", "headers", "_error$response", "children", "className", "style", "display", "alignItems", "flexDirection", "gap", "justifyContent", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "sx", "onChange", "placeholder", "required", "disabled", "subCategory", "options", "option", "onKeyDown", "margin", "marginTop", "words", "word", "max<PERSON><PERSON><PERSON>", "fontSize", "textAlign", "marginBottom", "readOnly", "fontWeight", "pattern", "title", "cursor", "alt", "border", "borderRadius", "padding", "onDragOver", "onDrop", "from", "dataTransfer", "multiple", "accept", "id", "htmlFor", "getElementById", "click", "preview", "open", "content", "onConfirm", "onCancel", "onClose", "aspect", "onCropChange", "onZoomChange", "onCropComplete", "area", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/UpdateProduct.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { TiDeleteOutline } from \"react-icons/ti\"; // Import the delete icon\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\nimport { Box, IconButton } from \"@mui/material\";\r\nimport { IoIosArrowRoundBack } from \"react-icons/io\";\r\nimport ConfirmationDialog from \"../confirmationMsg\";\r\nimport <PERSON><PERSON><PERSON> from \"react-easy-crop\";\r\nimport CircularProgress from \"@mui/material/CircularProgress\";\r\nimport Dialog from \"@mui/material/Dialog\";\r\nimport DialogActions from \"@mui/material/DialogActions\";\r\nimport DialogContent from \"@mui/material/DialogContent\";\r\nimport DialogContentText from \"@mui/material/DialogContentText\";\r\nimport DialogTitle from \"@mui/material/DialogTitle\";\r\nimport Button from \"@mui/material/Button\";\r\n\r\nconst getCroppedImg = (imageSrc, croppedAreaPixels) => {\r\n  return new Promise((resolve) => {\r\n    const image = new window.Image();\r\n    image.src = imageSrc;\r\n    image.onload = () => {\r\n      const canvas = document.createElement(\"canvas\");\r\n      canvas.width = croppedAreaPixels.width;\r\n      canvas.height = croppedAreaPixels.height;\r\n      const ctx = canvas.getContext(\"2d\");\r\n      ctx.drawImage(\r\n        image,\r\n        croppedAreaPixels.x,\r\n        croppedAreaPixels.y,\r\n        croppedAreaPixels.width,\r\n        croppedAreaPixels.height,\r\n        0,\r\n        0,\r\n        croppedAreaPixels.width,\r\n        croppedAreaPixels.height\r\n      );\r\n      canvas.toBlob((blob) => resolve(blob), \"image/jpeg\");\r\n    };\r\n  });\r\n};\r\n\r\nconst UpdateProduct = ({ existingProduct, onBack }) => {\r\n  const { vendor } = useVendor(); // Access vendor data from context\r\n\r\n  // State variables\r\n  const [brandName, setBrandName] = useState(\"\");\r\n  const [categories, setCategories] = useState([]); // Categories from API\r\n  const [subCategories, setSubCategories] = useState([]); // Subcategories from API\r\n  const [types, setTypes] = useState([]); // Types from API\r\n  const [selectedCategory, setSelectedCategory] = useState(\"\");\r\n  const [selectedSubCategory, setSelectedSubCategory] = useState(\"\");\r\n\r\n  const [tags, setTags] = useState([]); // Tags array\r\n\r\n  // Store initial form data for comparison\r\n  const [initialFormData, setInitialFormData] = useState(null);\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    price: \"\",\r\n    salePrice: null,\r\n    category: \"\",\r\n    subcategory: \"\",\r\n    collection: \"\",\r\n    type: \"\",\r\n    manufactureYear: \"\",\r\n    tags: [], // Tags array in formData\r\n    reviews: [], // Initialize reviews as an empty array\r\n    colors: [],\r\n    sizes: [],\r\n    images: [],\r\n    mainImage: \"\",\r\n    description: \"\",\r\n    technicalDimensions: {\r\n      length: \"\",\r\n      width: \"\",\r\n      height: \"\",\r\n      weight: \"\",\r\n    },\r\n    brandId: \"\",\r\n    brandName: \"\",\r\n    leadTime: \"\",\r\n    stock: \"\",\r\n    sku: \"\",\r\n    readyToShip: false, // Add readyToShip field with default value false\r\n    warrantyInfo: {\r\n      warrantyYears: \"\",\r\n      warrantyCoverage: [],\r\n    },\r\n    materialCareInstructions: \"\",\r\n    productSpecificRecommendations: \"\",\r\n    Estimatedtimeleadforcustomization: \"\",\r\n    cadFile: null, // Add CAD field\r\n  });\r\n\r\n  const [showCropModal, setShowCropModal] = useState(false);\r\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\r\n  const [pendingFile, setPendingFile] = useState(null);\r\n  const [crop, setCrop] = useState({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\r\n  const [imagePreviews, setImagePreviews] = useState([]);\r\n  const [mainImagePreview, setMainImagePreview] = useState(null);\r\n  const [isDialogOpen, setDialogOpen] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [showSuccessDialog, setShowSuccessDialog] = useState(false);\r\n  const [tagOptions, setTagOptions] = useState({});\r\n  const [validationErrors, setValidationErrors] = useState([]);\r\n\r\n  // Fetch categories on mount\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        const response = await axios.get(\r\n          \"https://api.thedesigngrit.com/api/categories/categories\"\r\n        );\r\n        setCategories(response.data); // Assuming the response contains categories\r\n      } catch (error) {\r\n        console.error(\"Error fetching categories:\", error);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  // Fetch brand details using brandId from the vendor session\r\n  useEffect(() => {\r\n    if (vendor?.brandId) {\r\n      const fetchBrandName = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/brand/${vendor.brandId}`\r\n          );\r\n          setBrandName(response.data.brandName); // Set the brand name in state\r\n          setFormData((prevData) => ({\r\n            ...prevData,\r\n            brandName: response.data.brandName, // Update formData with brand name\r\n          }));\r\n        } catch (error) {\r\n          console.error(\"Error fetching brand name:\", error);\r\n        }\r\n      };\r\n      fetchBrandName();\r\n    }\r\n  }, [vendor?.brandId]); // Removed brandName dependency to prevent infinite loop\r\n\r\n  // Set brandId from vendor context\r\n  useEffect(() => {\r\n    if (vendor) {\r\n      setFormData((prevFormData) => ({\r\n        ...prevFormData,\r\n        brandId: vendor.brandId || \"\", // Ensure the brandId exists in the vendor data\r\n      }));\r\n    }\r\n  }, [vendor]);\r\n\r\n  // Populate form with existing product data\r\n  useEffect(() => {\r\n    if (existingProduct) {\r\n      setFormData(existingProduct);\r\n      // Store initial form data for comparison (deep copy)\r\n      setInitialFormData(JSON.parse(JSON.stringify(existingProduct)));\r\n      setTags(existingProduct.tags || []);\r\n      setSelectedCategory(existingProduct.category);\r\n      setSelectedSubCategory(existingProduct.subcategory);\r\n      setImages(existingProduct.images || []); // Set existing images\r\n      setMainImage(existingProduct.mainImage || \"\"); // Set existing main image\r\n\r\n      // Set up image previews for existing images\r\n      if (existingProduct.images && existingProduct.images.length > 0) {\r\n        const previews = existingProduct.images.map((image) =>\r\n          typeof image === \"string\"\r\n            ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}`\r\n            : URL.createObjectURL(image)\r\n        );\r\n        setImagePreviews(previews);\r\n\r\n        // Set main image preview\r\n        if (existingProduct.mainImage) {\r\n          const mainPreview =\r\n            typeof existingProduct.mainImage === \"string\"\r\n              ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${existingProduct.mainImage}`\r\n              : URL.createObjectURL(existingProduct.mainImage);\r\n          setMainImagePreview(mainPreview);\r\n        }\r\n      }\r\n\r\n      // Fetch subcategories and types based on existing product data\r\n      const fetchSubCategoriesAndTypes = async () => {\r\n        try {\r\n          const subCategoryResponse = await axios.get(\r\n            `https://api.thedesigngrit.com/api/subcategories/byCategory/${existingProduct.category}`\r\n          );\r\n          setSubCategories(subCategoryResponse.data);\r\n\r\n          const typeResponse = await axios.get(\r\n            `https://api.thedesigngrit.com/api/subcategories/bySubcategory/${existingProduct.subcategory}`\r\n          );\r\n          setTypes(typeResponse.data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching subcategories or types:\", error);\r\n        }\r\n      };\r\n      fetchSubCategoriesAndTypes();\r\n    }\r\n  }, [existingProduct]);\r\n\r\n  // Fetch tag options for dropdowns\r\n  useEffect(() => {\r\n    const fetchTags = async () => {\r\n      try {\r\n        const categories = [\r\n          \"Color\",\r\n          \"Shape\",\r\n          \"Size\",\r\n          \"Material\",\r\n          \"Style\",\r\n          \"Finish\",\r\n          \"Functionality\",\r\n        ];\r\n        const fetchedTags = {};\r\n        for (const category of categories) {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/tags/tags/${category}`\r\n          );\r\n          fetchedTags[category] = response.data.map((tag) => tag.name);\r\n        }\r\n        setTagOptions(fetchedTags);\r\n      } catch (error) {\r\n        console.error(\"Error fetching tags:\", error);\r\n      }\r\n    };\r\n    fetchTags();\r\n  }, []);\r\n\r\n  // Handle category change\r\n  const handleCategoryChange = async (e) => {\r\n    const selectedCategoryId = e.target.value;\r\n    setSelectedCategory(selectedCategoryId);\r\n    setFormData({\r\n      ...formData,\r\n      category: selectedCategoryId, // Update formData.category\r\n    });\r\n\r\n    setSubCategories([]); // Reset subcategories\r\n    setSelectedSubCategory(\"\"); // Reset subcategory\r\n    try {\r\n      // Fetch subcategories for the selected category\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/subcategories/byCategory/${selectedCategoryId}`\r\n      );\r\n      setSubCategories(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching subcategories:\", error);\r\n    }\r\n  };\r\n\r\n  // Handle subcategory change\r\n  const handleSubCategoryChange = async (e) => {\r\n    const selectedSubCategoryId = e.target.value;\r\n    setSelectedSubCategory(selectedSubCategoryId);\r\n\r\n    setFormData({\r\n      ...formData,\r\n      subcategory: selectedSubCategoryId, // Update formData.subcategory\r\n    });\r\n    setTypes([]); // Reset types\r\n\r\n    try {\r\n      // Fetch types that are associated with the selected subcategory\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/subcategories/bySubcategory/${selectedSubCategoryId}`\r\n      );\r\n      setTypes(response.data); // Set types based on the fetched data\r\n    } catch (error) {\r\n      console.error(\"Error fetching types:\", error);\r\n    }\r\n  };\r\n\r\n  // Handle input change for basic fields\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Special handling for leadTime and Estimatedtimeleadforcustomization to ensure they're valid ranges\r\n    if (name === \"leadTime\" || name === \"Estimatedtimeleadforcustomization\") {\r\n      // Allow numbers and a single hyphen\r\n      const validValue = value.replace(/[^\\d-]/g, \"\");\r\n      // Ensure only one hyphen\r\n      const parts = validValue.split(\"-\");\r\n      if (parts.length > 2) {\r\n        // If more than one hyphen, keep only the first one\r\n        const firstPart = parts[0];\r\n        const secondPart = parts[1];\r\n        setFormData({\r\n          ...formData,\r\n          [name]: `${firstPart}-${secondPart}`,\r\n        });\r\n      } else {\r\n        setFormData({\r\n          ...formData,\r\n          [name]: validValue,\r\n        });\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Handle bullet points for specific fields\r\n    if (name === \"materialCareInstructions\") {\r\n      // For material care instructions, just allow normal line breaks\r\n      setFormData({\r\n        ...formData,\r\n        [name]: value,\r\n      });\r\n    } else if (name === \"productSpecificRecommendations\") {\r\n      // For product recommendations, add bullet points\r\n      const formattedValue = value\r\n        .split(\"\\n\")\r\n        .map((line) => (line.startsWith(\"•\") ? line : `• ${line.trim()}`))\r\n        .join(\"\\n\");\r\n\r\n      setFormData({\r\n        ...formData,\r\n        [name]: formattedValue,\r\n      });\r\n    } else {\r\n      // For all other fields, just update normally\r\n      setFormData({\r\n        ...formData,\r\n        [name]: value,\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle nested input change\r\n  const handleNestedChange = (e, parentField) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [parentField]: {\r\n        ...formData[parentField],\r\n        [name]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle array fields (tags, colors, sizes, warrantyCoverage)\r\n  const handleArrayChange = (e, field, parentField = null) => {\r\n    const { value } = e.target;\r\n\r\n    // Split the input value by commas and trim each item\r\n    const arrayValues = value.split(\",\").map((item) => item.trim());\r\n\r\n    if (parentField) {\r\n      // Handle nested fields (e.g., warrantyInfo.warrantyCoverage)\r\n      setFormData({\r\n        ...formData,\r\n        [parentField]: {\r\n          ...formData[parentField],\r\n          [field]: arrayValues, // Ensure this is an array\r\n        },\r\n      });\r\n    } else {\r\n      // Handle top-level fields (e.g., tags, colors, sizes)\r\n      setFormData({\r\n        ...formData,\r\n        [field]: arrayValues, // Ensure this is an array\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle adding new tags\r\n  const handleAddTag = (e) => {\r\n    if (e.key === \"Enter\" && e.target.value.trim() !== \"\") {\r\n      const newTag = e.target.value.trim();\r\n      if (!tags.includes(newTag)) {\r\n        setTags([...tags, newTag]);\r\n        setFormData({ ...formData, tags: [...formData.tags, newTag] });\r\n      }\r\n      e.target.value = \"\";\r\n    }\r\n  };\r\n\r\n  // Function to remove a tag by index\r\n  const handleRemoveTag = (index) => {\r\n    const newTags = tags.filter((_, i) => i !== index);\r\n    setTags(newTags);\r\n    setFormData({ ...formData, tags: newTags });\r\n  };\r\n\r\n  const [images, setImages] = useState(existingProduct.images || []); // Initialize with existing images\r\n  const [mainImage, setMainImage] = useState(existingProduct.mainImage || \"\"); // Initialize with existing main image\r\n\r\n  // Handle image upload\r\n  const handleImageUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    const img = new window.Image();\r\n    img.onload = () => {\r\n      if (img.width < 400 || img.height < 300) {\r\n        alert(\"Image too small. Minimum size: 400x300px for 4:3 ratio.\");\r\n        return;\r\n      }\r\n      const previewUrl = URL.createObjectURL(file);\r\n      setSelectedImageSrc(previewUrl);\r\n      setPendingFile(file);\r\n      setShowCropModal(true);\r\n    };\r\n    img.src = URL.createObjectURL(file);\r\n  };\r\n\r\n  const handleCropComplete = async () => {\r\n    const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\r\n    const url = URL.createObjectURL(blob);\r\n    const croppedFile = new File([blob], pendingFile.name, {\r\n      type: \"image/jpeg\",\r\n    });\r\n\r\n    setImages((prev) => [...prev, croppedFile]);\r\n    setImagePreviews((prev) => [...prev, url]);\r\n\r\n    if (!mainImage) {\r\n      setMainImage(croppedFile);\r\n      setMainImagePreview(url);\r\n    }\r\n\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      images: [...prevData.images, croppedFile],\r\n      mainImage: prevData.mainImage || croppedFile,\r\n    }));\r\n\r\n    setShowCropModal(false);\r\n    setPendingFile(null);\r\n    setSelectedImageSrc(null);\r\n  };\r\n\r\n  // Handle setting the main image\r\n  const handleSetMainImage = (index) => {\r\n    setMainImage(images[index]); // Set main image file\r\n    setMainImagePreview(imagePreviews[index]); // Set preview\r\n\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      mainImage: images[index], // Update formData with the selected main image\r\n    }));\r\n  };\r\n\r\n  // Handle removing an image\r\n  const handleRemoveImage = (index) => {\r\n    const updatedImages = images.filter((_, i) => i !== index);\r\n    const updatedPreviews = imagePreviews.filter((_, i) => i !== index);\r\n\r\n    setImages(updatedImages);\r\n    setImagePreviews(updatedPreviews);\r\n\r\n    // If the removed image was the main image, update the main image\r\n    if (images[index] === mainImage) {\r\n      setMainImage(updatedImages[0] || null);\r\n      setMainImagePreview(updatedPreviews[0] || null);\r\n\r\n      setFormData((prevData) => ({\r\n        ...prevData,\r\n        mainImage: updatedImages[0] || \"\", // Update mainImage in formData\r\n      }));\r\n    }\r\n\r\n    // Update formData images list\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      images: updatedImages,\r\n    }));\r\n  };\r\n\r\n  // Handle cancel\r\n  const handleCancel = () => {\r\n    onBack();\r\n  };\r\n\r\n  // Add handlers for new features\r\n  const handleCheckboxChange = (e) => {\r\n    const { name, checked } = e.target;\r\n    setFormData((prevState) => ({\r\n      ...prevState,\r\n      [name]: checked,\r\n    }));\r\n  };\r\n  // Add CAD file handling functions\r\n  const handleCADUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Check file type\r\n      const allowedTypes = [\".dwg\", \".dxf\", \".stp\", \".step\", \".igs\", \".iges\"];\r\n      const fileExtension = \".\" + file.name.split(\".\").pop().toLowerCase();\r\n\r\n      if (!allowedTypes.includes(fileExtension)) {\r\n        alert(\r\n          \"Please upload a valid CAD file (DWG, DXF, STP, STEP, IGS, or IGES format)\"\r\n        );\r\n        return;\r\n      }\r\n\r\n      setFormData((prevData) => ({\r\n        ...prevData,\r\n        cadFile: file, // Changed from 'cad' to 'cadFile' to match backend\r\n      }));\r\n    }\r\n  };\r\n  const handleWarrantyCoverageChange = (coverage) => {\r\n    setFormData((prevState) => {\r\n      const currentCoverage = prevState.warrantyInfo.warrantyCoverage;\r\n      let newCoverage;\r\n      if (currentCoverage.includes(coverage)) {\r\n        newCoverage = currentCoverage.filter((item) => item !== coverage);\r\n      } else {\r\n        newCoverage = [...currentCoverage, coverage];\r\n      }\r\n      return {\r\n        ...prevState,\r\n        warrantyInfo: {\r\n          ...prevState.warrantyInfo,\r\n          warrantyCoverage: newCoverage,\r\n        },\r\n      };\r\n    });\r\n  };\r\n  const handleSelectTag = (category, value) => {\r\n    if (value && !tags.includes(value)) {\r\n      setTags([...tags, value]);\r\n      setFormData({ ...formData, tags: [...formData.tags, value] });\r\n    }\r\n  };\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === \"Enter\") {\r\n      e.preventDefault();\r\n      const { name } = e.target;\r\n      if (name === \"productSpecificRecommendations\") {\r\n        setFormData((prev) => ({\r\n          ...prev,\r\n          productSpecificRecommendations:\r\n            (prev.productSpecificRecommendations\r\n              ? prev.productSpecificRecommendations + \"\\n• \"\r\n              : \"• \") + e.target.value.slice(e.target.selectionStart),\r\n        }));\r\n      } else if (name === \"materialCareInstructions\") {\r\n        setFormData((prev) => ({\r\n          ...prev,\r\n          materialCareInstructions:\r\n            (prev.materialCareInstructions\r\n              ? prev.materialCareInstructions + \"\\n\"\r\n              : \"\") + e.target.value.slice(e.target.selectionStart),\r\n        }));\r\n      }\r\n    }\r\n  };\r\n\r\n  // Update handleSubmit to match AddProduct logic\r\n  const handleOpenDialog = () => setDialogOpen(true);\r\n  const handleCloseDialog = () => setDialogOpen(false);\r\n  const handleCloseSuccessDialog = () => {\r\n    setShowSuccessDialog(false);\r\n    onBack();\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const errors = [];\r\n    if (!formData.name.trim()) errors.push(\"Product Name\");\r\n    if (!formData.price || isNaN(formData.price)) errors.push(\"Product Price\");\r\n    if (!formData.sku.trim()) errors.push(\"SKU\");\r\n    if (!formData.collection.trim()) errors.push(\"Collection\");\r\n    if (!formData.stock || isNaN(formData.stock)) errors.push(\"Stock\");\r\n    if (!formData.leadTime.trim()) errors.push(\"Lead Time\");\r\n    if (!formData.description.trim()) errors.push(\"Description\");\r\n    if (!formData.images || formData.images.length === 0)\r\n      errors.push(\"At least one image\");\r\n    return errors;\r\n  };\r\n\r\n  // Helper function to deep compare arrays\r\n  const arraysEqual = (arr1, arr2) => {\r\n    if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;\r\n    if (arr1.length !== arr2.length) return false;\r\n    return arr1.every((val, index) => val === arr2[index]);\r\n  };\r\n\r\n  // Helper function to deep compare objects\r\n  const objectsEqual = (obj1, obj2) => {\r\n    if (!obj1 || !obj2) return obj1 === obj2;\r\n    const keys1 = Object.keys(obj1);\r\n    const keys2 = Object.keys(obj2);\r\n    if (keys1.length !== keys2.length) return false;\r\n    return keys1.every((key) => {\r\n      if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {\r\n        return arraysEqual(obj1[key], obj2[key]);\r\n      }\r\n      return obj1[key] === obj2[key];\r\n    });\r\n  };\r\n\r\n  // Helper function to compare image arrays (considering both files and strings)\r\n  const imagesChanged = () => {\r\n    if (!initialFormData || !initialFormData.images) return true;\r\n\r\n    const currentImages = formData.images || [];\r\n    const initialImages = initialFormData.images || [];\r\n\r\n    // Check if lengths are different\r\n    if (currentImages.length !== initialImages.length) return true;\r\n\r\n    // Check if any image is new (File object) or if order changed\r\n    return currentImages.some((img, index) => {\r\n      if (img instanceof File) return true; // New image\r\n\r\n      const initialImg = initialImages[index];\r\n      // Compare string paths (remove URL prefix if present)\r\n      const currentPath =\r\n        typeof img === \"string\" ? img.replace(/^.*\\//, \"\") : img;\r\n      const initialPath =\r\n        typeof initialImg === \"string\"\r\n          ? initialImg.replace(/^.*\\//, \"\")\r\n          : initialImg;\r\n\r\n      return currentPath !== initialPath;\r\n    });\r\n  };\r\n\r\n  // Function to get only changed fields\r\n  const getChangedFields = () => {\r\n    if (!initialFormData) return formData;\r\n\r\n    const changes = {};\r\n    const hasNewCADFile = formData.cadFile instanceof File;\r\n    const hasImageChanges = imagesChanged();\r\n\r\n    // Check each field for changes\r\n    Object.keys(formData).forEach((key) => {\r\n      const currentValue = formData[key];\r\n      const initialValue = initialFormData[key];\r\n\r\n      // Handle images with detailed change detection\r\n      if (key === \"images\") {\r\n        if (hasImageChanges) {\r\n          changes[key] = {\r\n            images: formData.images,\r\n            imageOperation: \"update\", // Indicates we need to update the entire image set\r\n          };\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Handle mainImage changes\r\n      if (key === \"mainImage\") {\r\n        const currentMainImage =\r\n          typeof currentValue === \"string\"\r\n            ? currentValue.replace(/^.*\\//, \"\")\r\n            : currentValue;\r\n        const initialMainImage =\r\n          typeof initialValue === \"string\"\r\n            ? initialValue.replace(/^.*\\//, \"\")\r\n            : initialValue;\r\n\r\n        if (currentMainImage !== initialMainImage) {\r\n          changes[key] = currentValue;\r\n        }\r\n        return;\r\n      }\r\n\r\n      if (key === \"cadFile\") {\r\n        if (hasNewCADFile) {\r\n          changes[key] = formData[key];\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Handle arrays\r\n      if (Array.isArray(currentValue)) {\r\n        if (!arraysEqual(currentValue, initialValue)) {\r\n          changes[key] = currentValue;\r\n        }\r\n      }\r\n      // Handle objects (like technicalDimensions, warrantyInfo)\r\n      else if (typeof currentValue === \"object\" && currentValue !== null) {\r\n        if (!objectsEqual(currentValue, initialValue)) {\r\n          changes[key] = currentValue;\r\n        }\r\n      }\r\n      // Handle primitive values\r\n      else if (currentValue !== initialValue) {\r\n        // Skip empty/null values that haven't actually changed\r\n        if (\r\n          currentValue === \"\" &&\r\n          (initialValue === \"\" ||\r\n            initialValue === null ||\r\n            initialValue === undefined)\r\n        ) {\r\n          return;\r\n        }\r\n        if (\r\n          currentValue === null &&\r\n          (initialValue === null ||\r\n            initialValue === undefined ||\r\n            initialValue === \"\")\r\n        ) {\r\n          return;\r\n        }\r\n\r\n        // Additional check for string values that might be the same after trimming\r\n        if (\r\n          typeof currentValue === \"string\" &&\r\n          typeof initialValue === \"string\"\r\n        ) {\r\n          if (currentValue.trim() === initialValue.trim()) {\r\n            return;\r\n          }\r\n        }\r\n\r\n        // Additional check for number values that might be the same when converted\r\n        if (\r\n          typeof currentValue === \"number\" ||\r\n          typeof initialValue === \"number\"\r\n        ) {\r\n          if (Number(currentValue) === Number(initialValue)) {\r\n            return;\r\n          }\r\n        }\r\n\r\n        changes[key] = currentValue;\r\n      }\r\n    });\r\n\r\n    return changes;\r\n  };\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setDialogOpen(false);\r\n    setIsSubmitting(true);\r\n\r\n    // Get only the changed fields\r\n    const changedFields = getChangedFields();\r\n\r\n    // Debug logging to help identify false positives\r\n    console.log(\"=== CHANGE DETECTION DEBUG ===\");\r\n    console.log(\"Initial form data:\", initialFormData);\r\n    console.log(\"Current form data:\", formData);\r\n    console.log(\"Detected changes:\", changedFields);\r\n    console.log(\"===============================\");\r\n\r\n    // If no changes detected, show message and return\r\n    if (Object.keys(changedFields).length === 0) {\r\n      alert(\"No changes detected to update.\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    console.log(\"Changed fields:\", changedFields);\r\n\r\n    const data = new FormData();\r\n\r\n    // Only append changed fields to FormData\r\n    Object.keys(changedFields).forEach((key) => {\r\n      const value = changedFields[key];\r\n\r\n      if (key === \"images\") {\r\n        // Handle image changes - send all current images\r\n        if (value.imageOperation === \"update\") {\r\n          // Send all current images (mix of existing strings and new File objects)\r\n          value.images.forEach((image) => {\r\n            data.append(\"images\", image);\r\n          });\r\n\r\n          // Also send information about which images to keep/remove\r\n          const existingImagePaths = initialFormData.images || [];\r\n          const currentImagePaths = value.images.map((img) =>\r\n            typeof img === \"string\" ? img.replace(/^.*\\//, \"\") : \"NEW_FILE\"\r\n          );\r\n\r\n          // Send metadata about image operations\r\n          data.append(\r\n            \"imageMetadata\",\r\n            JSON.stringify({\r\n              existingImages: existingImagePaths,\r\n              currentImages: currentImagePaths,\r\n              operation: \"update\",\r\n            })\r\n          );\r\n        }\r\n      } else if (key === \"cadFile\" && value) {\r\n        data.append(\"cadFile\", value);\r\n      } else if (key === \"tags\" && Array.isArray(value)) {\r\n        value.forEach((tag, index) => data.append(`tags[${index}]`, tag));\r\n      } else if (key === \"colors\" && Array.isArray(value)) {\r\n        value.forEach((color, index) => data.append(`colors[${index}]`, color));\r\n      } else if (key === \"sizes\" && Array.isArray(value)) {\r\n        value.forEach((size, index) => data.append(`sizes[${index}]`, size));\r\n      } else if (key === \"technicalDimensions\" && typeof value === \"object\") {\r\n        data.append(\"technicalDimensions\", JSON.stringify(value));\r\n      } else if (key === \"warrantyInfo\" && typeof value === \"object\") {\r\n        data.append(\"warrantyInfo\", JSON.stringify(value));\r\n      } else {\r\n        // Handle primitive values\r\n        data.append(key, value || \"\");\r\n      }\r\n    });\r\n\r\n    // Always include mainImage if it exists (for backend processing)\r\n    if (formData.mainImage) {\r\n      data.append(\"mainImage\", formData.mainImage);\r\n    }\r\n\r\n    // Log FormData for debugging\r\n    console.log(\"Sending only changed fields:\");\r\n    for (let [key, value] of data.entries()) {\r\n      console.log(`${key}:`, value);\r\n    }\r\n\r\n    try {\r\n      const response = await axios.put(\r\n        `https://api.thedesigngrit.com/api/products/${formData._id}`,\r\n        data,\r\n        { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n      );\r\n      console.log(\"Product updated successfully:\", response.data);\r\n      setShowSuccessDialog(true);\r\n    } catch (error) {\r\n      console.error(\"Error updating product:\", error.response?.data || error);\r\n      alert(\"Failed to update product. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              flexDirection: \"row\",\r\n              gap: \"10px\",\r\n              justifyContent: \"flex-start\",\r\n            }}\r\n          >\r\n            <IconButton>\r\n              <IoIosArrowRoundBack size={\"30px\"} onClick={onBack} />\r\n            </IconButton>\r\n            <h2>Update Products</h2>\r\n          </div>\r\n          <p>Home &gt; Update Products</p>\r\n        </div>\r\n      </header>\r\n      <form onSubmit={(e) => e.preventDefault()}>\r\n        <div className=\"product-form\">\r\n          <div className=\"form-left\">\r\n            <h1>Update Product</h1>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Basic Information */}\r\n              <div className=\"form-group\">\r\n                <label>Product Name:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: L-shaped Sofa \"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label> Product Price:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"price\"\r\n                  value={formData.price}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: 10,000.00\"\r\n                  required\r\n                />\r\n              </div>\r\n              {/* <div className=\"form-group\">\r\n                <label>Sale Price:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"salePrice\"\r\n                  value={formData.salePrice}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: 1000.00\"\r\n                />\r\n              </div> */}\r\n              {/* Category Dropdown */}\r\n              <div className=\"form-group\">\r\n                <label>Category:</label>\r\n                <select\r\n                  name=\"category\"\r\n                  value={formData.category}\r\n                  onChange={handleCategoryChange}\r\n                  required\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Select Category\r\n                  </option>\r\n                  {categories.map((category) => (\r\n                    <option key={category._id} value={category._id}>\r\n                      {category.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                {/* Subcategory Dropdown */}\r\n                <label>Subcategory:</label>\r\n                <select\r\n                  name=\"subcategory\"\r\n                  value={formData.subcategory}\r\n                  onChange={handleSubCategoryChange}\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Select Subcategory\r\n                  </option>\r\n                  {subCategories.map((subCategory) => (\r\n                    <option key={subCategory._id} value={subCategory._id}>\r\n                      {subCategory.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                {/* Type Dropdown */}\r\n                <label>Type:</label>\r\n                <select\r\n                  name=\"type\"\r\n                  value={formData.type}\r\n                  onChange={handleChange}\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Select Type\r\n                  </option>\r\n                  {types.map((type) => (\r\n                    <option key={type._id} value={type._id}>\r\n                      {type.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              {/* Tags Input and Dropdowns */}\r\n              <div className=\"form-group\">\r\n                <label>Tag</label>\r\n                <div className=\"dropdown-container\">\r\n                  {Object.entries(tagOptions).map(([category, options]) => (\r\n                    <select\r\n                      key={category}\r\n                      onChange={(e) => {\r\n                        handleSelectTag(category, e.target.value);\r\n                        e.target.value = \"\";\r\n                      }}\r\n                    >\r\n                      <option value=\"\">{`Select ${category}`}</option>\r\n                      {options.map((option) => (\r\n                        <option key={option} value={option}>\r\n                          {option}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  ))}\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"tags\"\r\n                  placeholder=\"Add tag and press Enter  Ex: Sofa, Living Room\"\r\n                  onKeyDown={handleAddTag}\r\n                  className=\"tag-input\"\r\n                  style={{ margin: \"10px 0px\" }}\r\n                />\r\n                <br />\r\n                <div className=\"tags\">\r\n                  {tags.map((tag, index) => (\r\n                    <span key={index} className=\"tag\">\r\n                      {tag}\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleRemoveTag(index)}\r\n                        className=\"remove-tag-btn\"\r\n                      >\r\n                        <TiDeleteOutline size={18} />\r\n                      </button>\r\n                    </span>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </Box>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              <h2>Product Details</h2>\r\n\r\n              {/* <div className=\"form-group\">\r\n   \r\n                <label>Manufacturer:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"manufacturer\"\r\n                  value={formData.manufacturer}\r\n                  onChange={handleChange}\r\n                />\r\n              </div> */}\r\n              <div className=\"form-group\">\r\n                <label>Collection:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"collection\"\r\n                  value={formData.collection}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: Living Room\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Manufacture Year:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"manufactureYear\"\r\n                  value={formData.manufactureYear}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: 2023\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Colors (comma separated):</label>\r\n                <span\r\n                  style={{\r\n                    color: \"grey\",\r\n                    margin: \"5px\",\r\n                  }}\r\n                >\r\n                  Enter the colors of the product which be vaild for variants.\r\n                </span>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"colors\"\r\n                  value={formData.colors ? formData.colors.join(\",\") : \"\"} // Check if colors is defined\r\n                  onChange={(e) => handleArrayChange(e, \"colors\")}\r\n                  placeholder=\"Ex: Red, Blue, Green\"\r\n                  style={{ marginTop: \"10px\" }}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Sizes (comma separated):</label>\r\n                <span\r\n                  style={{\r\n                    color: \"grey\",\r\n                    margin: \"5px\",\r\n                  }}\r\n                >\r\n                  Enter the sizes of the product which be vaild for variants.\r\n                </span>\r\n\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"sizes\"\r\n                  value={formData.sizes.join(\",\")}\r\n                  onChange={(e) => handleArrayChange(e, \"sizes\")}\r\n                  placeholder=\"Ex: Small, Medium, Large\"\r\n                  style={{ marginTop: \"10px\" }}\r\n                />\r\n              </div>\r\n              {/* <div className=\"form-group\">\r\n                <label>Main Image URL:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"mainImage\"\r\n                  value={formData.mainImage}\r\n                  onChange={handleChange}\r\n                />\r\n              </div> */}\r\n              <div className=\"form-group\">\r\n                <label>Description:</label>\r\n                <textarea\r\n                  name=\"description\"\r\n                  value={formData.description}\r\n                  onChange={(e) => {\r\n                    const words = e.target.value\r\n                      .trim()\r\n                      .split(/\\s+/)\r\n                      .filter((word) => word.length > 0);\r\n                    if (words.length <= 10) {\r\n                      handleChange(e);\r\n                    }\r\n                  }}\r\n                  placeholder=\"Provide a brief product description (max 10 words). Include key features and benefits.\"\r\n                  maxLength=\"2000\"\r\n                />\r\n                <div\r\n                  style={{ fontSize: \"12px\", color: \"#666\", marginTop: \"5px\" }}\r\n                >\r\n                  Word count:{\" \"}\r\n                  {\r\n                    formData.description\r\n                      .trim()\r\n                      .split(/\\s+/)\r\n                      .filter((word) => word.length > 0).length\r\n                  }\r\n                  /10 words\r\n                </div>\r\n              </div>\r\n            </Box>\r\n\r\n            {/* Technical Dimensions */}\r\n            <div className=\"form-group\">\r\n              <h2\r\n                style={{\r\n                  textAlign: \"left\",\r\n                  marginBottom: \"10px\",\r\n                  marginTop: \"20px\",\r\n                }}\r\n              >\r\n                Technical Dimensions\r\n              </h2>\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  gap: \"10px\",\r\n                  flexDirection: \"row\",\r\n                  justifyContent: \"space-between\",\r\n                  width: \"100%\",\r\n                }}\r\n              >\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Length:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"length\"\r\n                    value={formData.technicalDimensions.length}\r\n                    placeholder=\"CM\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Width:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"width\"\r\n                    value={formData.technicalDimensions.width}\r\n                    placeholder=\"CM\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Height:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"height\"\r\n                    value={formData.technicalDimensions.height}\r\n                    placeholder=\"CM\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Weight:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"weight\"\r\n                    value={formData.technicalDimensions.weight}\r\n                    placeholder=\"Kg\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n              </Box>\r\n            </div>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Brand Information */}\r\n              <h2>Brand Information</h2>\r\n              <div className=\"form-group\">\r\n                <label>Brand ID:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"brandId\"\r\n                  value={formData.brandId}\r\n                  readOnly\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Brand Name:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"brandName\"\r\n                  value={formData.brandName}\r\n                  onChange={handleChange}\r\n                  readOnly\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label\r\n                  style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    name=\"readyToShip\"\r\n                    checked={formData.readyToShip}\r\n                    onChange={handleCheckboxChange}\r\n                    style={{ width: \"auto\" }}\r\n                  />\r\n                  Ready to Ship\r\n                  <span style={{ fontWeight: \"normal\" }}>\r\n                    (That The Product is Ready to Ship )\r\n                  </span>\r\n                </label>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Lead Time:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"leadTime\"\r\n                  value={formData.leadTime}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter lead time range (e.g., 5-7 days)\"\r\n                  required\r\n                  pattern=\"\\d+-\\d+\"\r\n                  title=\"Please enter a valid range (e.g., 5-7)\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Stock:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"stock\"\r\n                  value={formData.stock}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter the stock quantity  Ex:100\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>SKU:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"sku\"\r\n                  value={formData.sku}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter the Stock Keeping Unit\"\r\n                />\r\n              </div>\r\n            </Box>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Warranty Information */}\r\n              <h2>Warranty Information</h2>\r\n              <div className=\"form-group\">\r\n                <label>Warranty Years:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"warrantyYears\"\r\n                  value={formData.warrantyInfo.warrantyYears}\r\n                  onChange={(e) => handleNestedChange(e, \"warrantyInfo\")}\r\n                  placeholder=\"Enter the number of years of warranty\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Warranty Coverage:</label>\r\n                <div\r\n                  style={{\r\n                    display: \"flex\",\r\n                    flexDirection: \"row\",\r\n                    gap: \"10px\",\r\n                    marginTop: \"10px\",\r\n                  }}\r\n                >\r\n                  {[\r\n                    \"Manufacturer Defects\",\r\n                    \"Wear and Tear\",\r\n                    \"Damage During Shipping\",\r\n                  ].map((coverage) => (\r\n                    <label\r\n                      key={coverage}\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: \"8px\",\r\n                        cursor: \"pointer\",\r\n                        fontSize: \"14px\",\r\n                      }}\r\n                    >\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={formData.warrantyInfo.warrantyCoverage.includes(\r\n                          coverage\r\n                        )}\r\n                        onChange={() => handleWarrantyCoverageChange(coverage)}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      />\r\n                      {coverage}\r\n                    </label>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </Box>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Additional Information */}\r\n              <h2>Additional Information</h2>\r\n              <div className=\"form-group\">\r\n                <label>Material Care Instructions:</label>\r\n                <textarea\r\n                  name=\"materialCareInstructions\"\r\n                  value={formData.materialCareInstructions}\r\n                  onChange={handleChange}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"Enter the material care instructions\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Product Specific Recommendations:</label>\r\n                <textarea\r\n                  name=\"productSpecificRecommendations\"\r\n                  value={formData.productSpecificRecommendations}\r\n                  onChange={handleChange}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"Enter the product specific recommendations\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Estimated Time Lead for Customization:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"Estimatedtimeleadforcustomization\"\r\n                  value={formData.Estimatedtimeleadforcustomization}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter time range (e.g., 5-7 days)\"\r\n                  pattern=\"\\d+-\\d+\"\r\n                  title=\"Please enter a valid range (e.g., 5-7)\"\r\n                />\r\n              </div>\r\n              {/* <div className=\"form-group\">\r\n                <label>Claim Process:</label>\r\n                <textarea\r\n                  name=\"claimProcess\"\r\n                  value={formData.claimProcess}\r\n                  onChange={handleChange}\r\n                />\r\n              </div> */}\r\n            </Box>\r\n          </div>\r\n          <div className=\"form-right\">\r\n            <div className=\"image-placeholder\">\r\n              {mainImagePreview ? (\r\n                <img\r\n                  src={mainImagePreview} // Use preview URL instead of file object\r\n                  alt=\"Main Preview\"\r\n                  className=\"main-image\"\r\n                />\r\n              ) : mainImage ? (\r\n                <img\r\n                  src={\r\n                    typeof mainImage === \"string\"\r\n                      ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${mainImage}`\r\n                      : URL.createObjectURL(mainImage)\r\n                  }\r\n                  alt=\"Main Preview\"\r\n                  className=\"main-image\"\r\n                />\r\n              ) : (\r\n                <p\r\n                  style={{\r\n                    border: \"1px solid #8A9A5B\",\r\n                    borderRadius: \"10px\",\r\n                    color: \"#71797E\",\r\n                    margin: \" auto\",\r\n                    width: \"30%\",\r\n                    textAlign: \"center\",\r\n                    padding: \"10px\",\r\n                    marginTop: \"80px\",\r\n                  }}\r\n                >\r\n                  Image Preview\r\n                </p>\r\n              )}\r\n            </div>\r\n            <div className=\"product-gallery\">\r\n              <label>Product Gallery</label>\r\n\r\n              <div\r\n                className=\"drop-zone\"\r\n                onDragOver={(e) => e.preventDefault()} // Prevent default to allow drop\r\n                onDrop={(e) => {\r\n                  e.preventDefault(); // Prevent default behavior\r\n                  const files = Array.from(e.dataTransfer.files); // Access dropped files\r\n                  handleImageUpload({ target: { files } }); // Pass to handleImageUpload\r\n                }}\r\n              >\r\n                <span>\r\n                  <strong>\r\n                    (Upload high-quality images with a minimum resolution of\r\n                    1080x1080px. Use .jpeg or .png format. Ensure clear\r\n                    visibility of the product from multiple angles. White\r\n                    background)\r\n                  </strong>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  multiple\r\n                  accept=\"image/jpeg, image/png, image/webp\"\r\n                  onChange={handleImageUpload}\r\n                  className=\"file-input\"\r\n                  style={{ display: \"none\" }} // Hide the input visually\r\n                  id=\"fileInput\"\r\n                />\r\n                <label htmlFor=\"fileInput\" className=\"drop-zone-label\">\r\n                  Drop your image here, or browse\r\n                  <br />\r\n                  Jpeg, png are allowed\r\n                </label>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => document.getElementById(\"fileInput\").click()}\r\n                  className=\"upload-btn\"\r\n                >\r\n                  Upload Images\r\n                </button>\r\n              </div>\r\n              <div className=\"thumbnail-list\">\r\n                {imagePreviews.map((preview, index) => (\r\n                  <div\r\n                    className={`thumbnail ${\r\n                      preview === mainImagePreview ? \"main-thumbnail\" : \"\"\r\n                    }`}\r\n                    key={index}\r\n                  >\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        justifyContent: \"space-between\",\r\n                        gap: \"10px\",\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={preview}\r\n                        alt={`Thumbnail ${index}`}\r\n                        className=\"image-thumbnail\"\r\n                        onClick={() => handleSetMainImage(index)}\r\n                      />\r\n                      <span>Product thumbnail.png</span>\r\n                      <span className=\"checkmark\">\r\n                        {preview === mainImagePreview ? \"✔ Main\" : \"✔\"}\r\n                      </span>\r\n                    </div>\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        justifyContent: \"space-between\",\r\n                        gap: \"10px\",\r\n                      }}\r\n                    >\r\n                      <span\r\n                        className=\"remove-thumbnail\"\r\n                        onClick={() => handleRemoveImage(index)}\r\n                      >\r\n                        ✖\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n                {/* Display existing images that don't have previews yet */}\r\n                {images\r\n                  .filter((_, index) => !imagePreviews[index])\r\n                  .map((image, index) => (\r\n                    <div\r\n                      className={`thumbnail ${\r\n                        image === mainImage ? \"main-thumbnail\" : \"\"\r\n                      }`}\r\n                      key={`existing-${index}`}\r\n                    >\r\n                      <div\r\n                        style={{\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                          justifyContent: \"space-between\",\r\n                          gap: \"10px\",\r\n                        }}\r\n                      >\r\n                        <img\r\n                          src={\r\n                            typeof image === \"string\"\r\n                              ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}`\r\n                              : URL.createObjectURL(image)\r\n                          }\r\n                          alt={`Thumbnail ${index}`}\r\n                          className=\"image-thumbnail\"\r\n                          onClick={() =>\r\n                            handleSetMainImage(imagePreviews.length + index)\r\n                          }\r\n                        />\r\n                        <span>Product thumbnail.png</span>\r\n                        <span className=\"checkmark\">\r\n                          {image === mainImage ? \"✔ Main\" : \"✔\"}\r\n                        </span>\r\n                      </div>\r\n                      <div\r\n                        style={{\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                          justifyContent: \"space-between\",\r\n                          gap: \"10px\",\r\n                        }}\r\n                      >\r\n                        <span\r\n                          className=\"remove-thumbnail\"\r\n                          onClick={() =>\r\n                            handleRemoveImage(imagePreviews.length + index)\r\n                          }\r\n                        >\r\n                          ✖\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Update CAD Upload Section */}\r\n            <div className=\"cad-upload-section\">\r\n              <label>CAD File Upload</label>\r\n              <div className=\"cad-drop-zone\">\r\n                <input\r\n                  type=\"file\"\r\n                  accept=\".dwg,.dxf,.stp,.step,.igs,.iges\"\r\n                  onChange={handleCADUpload}\r\n                  className=\"cad-file-input\"\r\n                  style={{ display: \"none\" }}\r\n                  id=\"cadFileInput\"\r\n                />\r\n                <label htmlFor=\"cadFileInput\" className=\"cad-drop-zone-label\">\r\n                  Drop your CAD file here, or browse\r\n                  <br />\r\n                  Supported formats: DWG, DXF, STP, STEP, IGS, IGES\r\n                </label>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() =>\r\n                    document.getElementById(\"cadFileInput\").click()\r\n                  }\r\n                  className=\"upload-btn\"\r\n                >\r\n                  Upload CAD File\r\n                </button>\r\n              </div>\r\n              {formData.cadFile && ( // Changed from 'cad' to 'cadFile'\r\n                <div className=\"cad-file-info\">\r\n                  <span>Selected file: {formData.cadFile.name}</span>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() =>\r\n                      setFormData((prev) => ({ ...prev, cadFile: null }))\r\n                    }\r\n                    className=\"remove-cad-btn\"\r\n                  >\r\n                    ✖\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"form-actions\">\r\n          {validationErrors.length > 0 && (\r\n            <div style={{ color: \"red\", marginBottom: \"10px\" }}>\r\n              Please fill the following required fields:{\" \"}\r\n              {validationErrors.join(\", \")}\r\n            </div>\r\n          )}\r\n          <button\r\n            className=\"btn update\"\r\n            type=\"button\"\r\n            onClick={() => {\r\n              const errors = validateForm();\r\n              if (errors.length > 0) {\r\n                setValidationErrors(errors);\r\n                return;\r\n              }\r\n              setValidationErrors([]);\r\n              handleOpenDialog();\r\n            }}\r\n            disabled={isSubmitting}\r\n          >\r\n            {isSubmitting ? <CircularProgress size={24} /> : \"UPDATE\"}\r\n          </button>\r\n          <button className=\"btn cancel\" onClick={handleCancel}>\r\n            CANCEL\r\n          </button>\r\n        </div>\r\n      </form>\r\n      <ConfirmationDialog\r\n        open={isDialogOpen}\r\n        title=\"Confirm Product Update\"\r\n        content=\"Are you sure you want to update this product?\"\r\n        onConfirm={handleSubmit}\r\n        onCancel={handleCloseDialog}\r\n      />\r\n      <Dialog open={showSuccessDialog} onClose={handleCloseSuccessDialog}>\r\n        <DialogTitle>Success</DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText>Product updated successfully!</DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseSuccessDialog} color=\"primary\">\r\n            Done\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      {showCropModal && (\r\n        <div className=\"modal-overlay-uploadimage\">\r\n          <div className=\"modal-content-uploadimage\">\r\n            <div className=\"cropper-container-uploadimage\">\r\n              <Cropper\r\n                image={selectedImageSrc}\r\n                crop={crop}\r\n                zoom={zoom}\r\n                aspect={4 / 3}\r\n                onCropChange={setCrop}\r\n                onZoomChange={setZoom}\r\n                onCropComplete={(_, area) => setCroppedAreaPixels(area)}\r\n              />\r\n            </div>\r\n            <div className=\"cropper-buttons-uploadimage\">\r\n              <button onClick={handleCropComplete}>Crop Image</button>\r\n              <button onClick={() => setShowCropModal(false)}>Cancel</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UpdateProduct;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,gBAAgB,CAAC,CAAC;AAClD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEC,iBAAiB,KAAK;EACrD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;IAChCF,KAAK,CAACG,GAAG,GAAGP,QAAQ;IACpBI,KAAK,CAACI,MAAM,GAAG,MAAM;MACnB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,KAAK,GAAGX,iBAAiB,CAACW,KAAK;MACtCH,MAAM,CAACI,MAAM,GAAGZ,iBAAiB,CAACY,MAAM;MACxC,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MACnCD,GAAG,CAACE,SAAS,CACXZ,KAAK,EACLH,iBAAiB,CAACgB,CAAC,EACnBhB,iBAAiB,CAACiB,CAAC,EACnBjB,iBAAiB,CAACW,KAAK,EACvBX,iBAAiB,CAACY,MAAM,EACxB,CAAC,EACD,CAAC,EACDZ,iBAAiB,CAACW,KAAK,EACvBX,iBAAiB,CAACY,MACpB,CAAC;MACDJ,MAAM,CAACU,MAAM,CAAEC,IAAI,IAAKjB,OAAO,CAACiB,IAAI,CAAC,EAAE,YAAY,CAAC;IACtD,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM;IAAEC;EAAO,CAAC,GAAG3C,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEhC;EACA,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC4D,IAAI,EAAEC,OAAO,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEtC;EACA,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC;IACvCkE,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnBb,IAAI,EAAE,EAAE;IAAE;IACVc,OAAO,EAAE,EAAE;IAAE;IACbC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,mBAAmB,EAAE;MACnBC,MAAM,EAAE,EAAE;MACV/C,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACV+C,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,EAAE;IACXnC,SAAS,EAAE,EAAE;IACboC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,WAAW,EAAE,KAAK;IAAE;IACpBC,YAAY,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,wBAAwB,EAAE,EAAE;IAC5BC,8BAA8B,EAAE,EAAE;IAClCC,iCAAiC,EAAE,EAAE;IACrCC,OAAO,EAAE,IAAI,CAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmG,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqG,IAAI,EAAEC,OAAO,CAAC,GAAGtG,QAAQ,CAAC;IAAEuC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChD,MAAM,CAAC+D,IAAI,EAAEC,OAAO,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACuB,iBAAiB,EAAEkF,oBAAoB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC0G,aAAa,EAAEC,gBAAgB,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8G,YAAY,EAAEC,aAAa,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgH,YAAY,EAAEC,eAAe,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnH,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoH,UAAU,EAAEC,aAAa,CAAC,GAAGrH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACsH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuH,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMvH,KAAK,CAACwH,GAAG,CAC9B,yDACF,CAAC;QACDvE,aAAa,CAACsE,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;IACF,CAAC;IAEDJ,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvH,SAAS,CAAC,MAAM;IACd,IAAI8C,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEoC,OAAO,EAAE;MACnB,MAAM2C,cAAc,GAAG,MAAAA,CAAA,KAAY;QACjC,IAAI;UACF,MAAML,QAAQ,GAAG,MAAMvH,KAAK,CAACwH,GAAG,CAC9B,2CAA2C3E,MAAM,CAACoC,OAAO,EAC3D,CAAC;UACDlC,YAAY,CAACwE,QAAQ,CAACE,IAAI,CAAC3E,SAAS,CAAC,CAAC,CAAC;UACvCiB,WAAW,CAAE8D,QAAQ,KAAM;YACzB,GAAGA,QAAQ;YACX/E,SAAS,EAAEyE,QAAQ,CAACE,IAAI,CAAC3E,SAAS,CAAE;UACtC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,OAAO4E,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC;MACDE,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC/E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEvB;EACAlF,SAAS,CAAC,MAAM;IACd,IAAI8C,MAAM,EAAE;MACVkB,WAAW,CAAE+D,YAAY,KAAM;QAC7B,GAAGA,YAAY;QACf7C,OAAO,EAAEpC,MAAM,CAACoC,OAAO,IAAI,EAAE,CAAE;MACjC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACpC,MAAM,CAAC,CAAC;;EAEZ;EACA9C,SAAS,CAAC,MAAM;IACd,IAAI2C,eAAe,EAAE;MACnBqB,WAAW,CAACrB,eAAe,CAAC;MAC5B;MACAmB,kBAAkB,CAACkE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvF,eAAe,CAAC,CAAC,CAAC;MAC/DiB,OAAO,CAACjB,eAAe,CAACgB,IAAI,IAAI,EAAE,CAAC;MACnCH,mBAAmB,CAACb,eAAe,CAACyB,QAAQ,CAAC;MAC7CV,sBAAsB,CAACf,eAAe,CAAC0B,WAAW,CAAC;MACnD8D,SAAS,CAACxF,eAAe,CAACiC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;MACzCwD,YAAY,CAACzF,eAAe,CAACkC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAIlC,eAAe,CAACiC,MAAM,IAAIjC,eAAe,CAACiC,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;QAC/D,MAAMqD,QAAQ,GAAG1F,eAAe,CAACiC,MAAM,CAAC0D,GAAG,CAAE7G,KAAK,IAChD,OAAOA,KAAK,KAAK,QAAQ,GACrB,uDAAuDA,KAAK,EAAE,GAC9D8G,GAAG,CAACC,eAAe,CAAC/G,KAAK,CAC/B,CAAC;QACDiF,gBAAgB,CAAC2B,QAAQ,CAAC;;QAE1B;QACA,IAAI1F,eAAe,CAACkC,SAAS,EAAE;UAC7B,MAAM4D,WAAW,GACf,OAAO9F,eAAe,CAACkC,SAAS,KAAK,QAAQ,GACzC,uDAAuDlC,eAAe,CAACkC,SAAS,EAAE,GAClF0D,GAAG,CAACC,eAAe,CAAC7F,eAAe,CAACkC,SAAS,CAAC;UACpD+B,mBAAmB,CAAC6B,WAAW,CAAC;QAClC;MACF;;MAEA;MACA,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;QAC7C,IAAI;UACF,MAAMC,mBAAmB,GAAG,MAAM1I,KAAK,CAACwH,GAAG,CACzC,8DAA8D9E,eAAe,CAACyB,QAAQ,EACxF,CAAC;UACDhB,gBAAgB,CAACuF,mBAAmB,CAACjB,IAAI,CAAC;UAE1C,MAAMkB,YAAY,GAAG,MAAM3I,KAAK,CAACwH,GAAG,CAClC,iEAAiE9E,eAAe,CAAC0B,WAAW,EAC9F,CAAC;UACDf,QAAQ,CAACsF,YAAY,CAAClB,IAAI,CAAC;QAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAChE;MACF,CAAC;MACDe,0BAA0B,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC/F,eAAe,CAAC,CAAC;;EAErB;EACA3C,SAAS,CAAC,MAAM;IACd,MAAM6I,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAM5F,UAAU,GAAG,CACjB,OAAO,EACP,OAAO,EACP,MAAM,EACN,UAAU,EACV,OAAO,EACP,QAAQ,EACR,eAAe,CAChB;QACD,MAAM6F,WAAW,GAAG,CAAC,CAAC;QACtB,KAAK,MAAM1E,QAAQ,IAAInB,UAAU,EAAE;UACjC,MAAMuE,QAAQ,GAAG,MAAMvH,KAAK,CAACwH,GAAG,CAC9B,+CAA+CrD,QAAQ,EACzD,CAAC;UACD0E,WAAW,CAAC1E,QAAQ,CAAC,GAAGoD,QAAQ,CAACE,IAAI,CAACY,GAAG,CAAES,GAAG,IAAKA,GAAG,CAAC9E,IAAI,CAAC;QAC9D;QACAmD,aAAa,CAAC0B,WAAW,CAAC;MAC5B,CAAC,CAAC,OAAOnB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC;IACDkB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACxC,MAAMC,kBAAkB,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACzC5F,mBAAmB,CAAC0F,kBAAkB,CAAC;IACvClF,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXK,QAAQ,EAAE8E,kBAAkB,CAAE;IAChC,CAAC,CAAC;IAEF9F,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBM,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,IAAI;MACF;MACA,MAAM8D,QAAQ,GAAG,MAAMvH,KAAK,CAACwH,GAAG,CAC9B,8DAA8DyB,kBAAkB,EAClF,CAAC;MACD9F,gBAAgB,CAACoE,QAAQ,CAACE,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM0B,uBAAuB,GAAG,MAAOJ,CAAC,IAAK;IAC3C,MAAMK,qBAAqB,GAAGL,CAAC,CAACE,MAAM,CAACC,KAAK;IAC5C1F,sBAAsB,CAAC4F,qBAAqB,CAAC;IAE7CtF,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXM,WAAW,EAAEiF,qBAAqB,CAAE;IACtC,CAAC,CAAC;IACFhG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAI;MACF;MACA,MAAMkE,QAAQ,GAAG,MAAMvH,KAAK,CAACwH,GAAG,CAC9B,iEAAiE6B,qBAAqB,EACxF,CAAC;MACDhG,QAAQ,CAACkE,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAIN,CAAC,IAAK;IAC1B,MAAM;MAAEhF,IAAI;MAAEmF;IAAM,CAAC,GAAGH,CAAC,CAACE,MAAM;;IAEhC;IACA,IAAIlF,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,mCAAmC,EAAE;MACvE;MACA,MAAMuF,UAAU,GAAGJ,KAAK,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAC/C;MACA,MAAMC,KAAK,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;MACnC,IAAID,KAAK,CAAC1E,MAAM,GAAG,CAAC,EAAE;QACpB;QACA,MAAM4E,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMG,UAAU,GAAGH,KAAK,CAAC,CAAC,CAAC;QAC3B1F,WAAW,CAAC;UACV,GAAGD,QAAQ;UACX,CAACE,IAAI,GAAG,GAAG2F,SAAS,IAAIC,UAAU;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL7F,WAAW,CAAC;UACV,GAAGD,QAAQ;UACX,CAACE,IAAI,GAAGuF;QACV,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAIvF,IAAI,KAAK,0BAA0B,EAAE;MACvC;MACAD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGmF;MACV,CAAC,CAAC;IACJ,CAAC,MAAM,IAAInF,IAAI,KAAK,gCAAgC,EAAE;MACpD;MACA,MAAM6F,cAAc,GAAGV,KAAK,CACzBO,KAAK,CAAC,IAAI,CAAC,CACXrB,GAAG,CAAEyB,IAAI,IAAMA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGD,IAAI,GAAG,KAAKA,IAAI,CAACE,IAAI,CAAC,CAAC,EAAG,CAAC,CACjEC,IAAI,CAAC,IAAI,CAAC;MAEblG,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAG6F;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA9F,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGmF;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMe,kBAAkB,GAAGA,CAAClB,CAAC,EAAEmB,WAAW,KAAK;IAC7C,MAAM;MAAEnG,IAAI;MAAEmF;IAAM,CAAC,GAAGH,CAAC,CAACE,MAAM;IAChCnF,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACqG,WAAW,GAAG;QACb,GAAGrG,QAAQ,CAACqG,WAAW,CAAC;QACxB,CAACnG,IAAI,GAAGmF;MACV;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiB,iBAAiB,GAAGA,CAACpB,CAAC,EAAEqB,KAAK,EAAEF,WAAW,GAAG,IAAI,KAAK;IAC1D,MAAM;MAAEhB;IAAM,CAAC,GAAGH,CAAC,CAACE,MAAM;;IAE1B;IACA,MAAMoB,WAAW,GAAGnB,KAAK,CAACO,KAAK,CAAC,GAAG,CAAC,CAACrB,GAAG,CAAEkC,IAAI,IAAKA,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC;IAE/D,IAAIG,WAAW,EAAE;MACf;MACApG,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACqG,WAAW,GAAG;UACb,GAAGrG,QAAQ,CAACqG,WAAW,CAAC;UACxB,CAACE,KAAK,GAAGC,WAAW,CAAE;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAvG,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACuG,KAAK,GAAGC,WAAW,CAAE;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAME,YAAY,GAAIxB,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,IAAIzB,CAAC,CAACE,MAAM,CAACC,KAAK,CAACa,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrD,MAAMU,MAAM,GAAG1B,CAAC,CAACE,MAAM,CAACC,KAAK,CAACa,IAAI,CAAC,CAAC;MACpC,IAAI,CAACtG,IAAI,CAACiH,QAAQ,CAACD,MAAM,CAAC,EAAE;QAC1B/G,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAEgH,MAAM,CAAC,CAAC;QAC1B3G,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEJ,IAAI,EAAE,CAAC,GAAGI,QAAQ,CAACJ,IAAI,EAAEgH,MAAM;QAAE,CAAC,CAAC;MAChE;MACA1B,CAAC,CAACE,MAAM,CAACC,KAAK,GAAG,EAAE;IACrB;EACF,CAAC;;EAED;EACA,MAAMyB,eAAe,GAAIC,KAAK,IAAK;IACjC,MAAMC,OAAO,GAAGpH,IAAI,CAACqH,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAClDlH,OAAO,CAACmH,OAAO,CAAC;IAChB/G,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEJ,IAAI,EAAEoH;IAAQ,CAAC,CAAC;EAC7C,CAAC;EAED,MAAM,CAACnG,MAAM,EAAEuD,SAAS,CAAC,GAAGpI,QAAQ,CAAC4C,eAAe,CAACiC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC;EACpE,MAAM,CAACC,SAAS,EAAEuD,YAAY,CAAC,GAAGrI,QAAQ,CAAC4C,eAAe,CAACkC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;;EAE7E;EACA,MAAMsG,iBAAiB,GAAIlC,CAAC,IAAK;IAC/B,MAAMmC,IAAI,GAAGnC,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACD,IAAI,EAAE;IACX,MAAME,GAAG,GAAG,IAAI5J,MAAM,CAACC,KAAK,CAAC,CAAC;IAC9B2J,GAAG,CAACzJ,MAAM,GAAG,MAAM;MACjB,IAAIyJ,GAAG,CAACrJ,KAAK,GAAG,GAAG,IAAIqJ,GAAG,CAACpJ,MAAM,GAAG,GAAG,EAAE;QACvCqJ,KAAK,CAAC,yDAAyD,CAAC;QAChE;MACF;MACA,MAAMC,UAAU,GAAGjD,GAAG,CAACC,eAAe,CAAC4C,IAAI,CAAC;MAC5CnF,mBAAmB,CAACuF,UAAU,CAAC;MAC/BrF,cAAc,CAACiF,IAAI,CAAC;MACpBrF,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC;IACDuF,GAAG,CAAC1J,GAAG,GAAG2G,GAAG,CAACC,eAAe,CAAC4C,IAAI,CAAC;EACrC,CAAC;EAED,MAAMK,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMhJ,IAAI,GAAG,MAAMrB,aAAa,CAAC4E,gBAAgB,EAAE1E,iBAAiB,CAAC;IACrE,MAAMoK,GAAG,GAAGnD,GAAG,CAACC,eAAe,CAAC/F,IAAI,CAAC;IACrC,MAAMkJ,WAAW,GAAG,IAAIC,IAAI,CAAC,CAACnJ,IAAI,CAAC,EAAEyD,WAAW,CAACjC,IAAI,EAAE;MACrDM,IAAI,EAAE;IACR,CAAC,CAAC;IAEF4D,SAAS,CAAE0D,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3CjF,gBAAgB,CAAEmF,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEH,GAAG,CAAC,CAAC;IAE1C,IAAI,CAAC7G,SAAS,EAAE;MACduD,YAAY,CAACuD,WAAW,CAAC;MACzB/E,mBAAmB,CAAC8E,GAAG,CAAC;IAC1B;IAEA1H,WAAW,CAAE8D,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACXlD,MAAM,EAAE,CAAC,GAAGkD,QAAQ,CAAClD,MAAM,EAAE+G,WAAW,CAAC;MACzC9G,SAAS,EAAEiD,QAAQ,CAACjD,SAAS,IAAI8G;IACnC,CAAC,CAAC,CAAC;IAEH5F,gBAAgB,CAAC,KAAK,CAAC;IACvBI,cAAc,CAAC,IAAI,CAAC;IACpBF,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM6F,kBAAkB,GAAIhB,KAAK,IAAK;IACpC1C,YAAY,CAACxD,MAAM,CAACkG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7BlE,mBAAmB,CAACH,aAAa,CAACqE,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3C9G,WAAW,CAAE8D,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACXjD,SAAS,EAAED,MAAM,CAACkG,KAAK,CAAC,CAAE;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMiB,iBAAiB,GAAIjB,KAAK,IAAK;IACnC,MAAMkB,aAAa,GAAGpH,MAAM,CAACoG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAC1D,MAAMmB,eAAe,GAAGxF,aAAa,CAACuE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAEnE3C,SAAS,CAAC6D,aAAa,CAAC;IACxBtF,gBAAgB,CAACuF,eAAe,CAAC;;IAEjC;IACA,IAAIrH,MAAM,CAACkG,KAAK,CAAC,KAAKjG,SAAS,EAAE;MAC/BuD,YAAY,CAAC4D,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MACtCpF,mBAAmB,CAACqF,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MAE/CjI,WAAW,CAAE8D,QAAQ,KAAM;QACzB,GAAGA,QAAQ;QACXjD,SAAS,EAAEmH,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAE;MACrC,CAAC,CAAC,CAAC;IACL;;IAEA;IACAhI,WAAW,CAAE8D,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACXlD,MAAM,EAAEoH;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBtJ,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMuJ,oBAAoB,GAAIlD,CAAC,IAAK;IAClC,MAAM;MAAEhF,IAAI;MAAEmI;IAAQ,CAAC,GAAGnD,CAAC,CAACE,MAAM;IAClCnF,WAAW,CAAEqI,SAAS,KAAM;MAC1B,GAAGA,SAAS;MACZ,CAACpI,IAAI,GAAGmI;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EACD;EACA,MAAME,eAAe,GAAIrD,CAAC,IAAK;IAC7B,MAAMmC,IAAI,GAAGnC,CAAC,CAACE,MAAM,CAACkC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,MAAMmB,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;MACvE,MAAMC,aAAa,GAAG,GAAG,GAAGpB,IAAI,CAACnH,IAAI,CAAC0F,KAAK,CAAC,GAAG,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAEpE,IAAI,CAACH,YAAY,CAAC3B,QAAQ,CAAC4B,aAAa,CAAC,EAAE;QACzCjB,KAAK,CACH,2EACF,CAAC;QACD;MACF;MAEAvH,WAAW,CAAE8D,QAAQ,KAAM;QACzB,GAAGA,QAAQ;QACXjC,OAAO,EAAEuF,IAAI,CAAE;MACjB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD,MAAMuB,4BAA4B,GAAIC,QAAQ,IAAK;IACjD5I,WAAW,CAAEqI,SAAS,IAAK;MACzB,MAAMQ,eAAe,GAAGR,SAAS,CAAC9G,YAAY,CAACE,gBAAgB;MAC/D,IAAIqH,WAAW;MACf,IAAID,eAAe,CAACjC,QAAQ,CAACgC,QAAQ,CAAC,EAAE;QACtCE,WAAW,GAAGD,eAAe,CAAC7B,MAAM,CAAER,IAAI,IAAKA,IAAI,KAAKoC,QAAQ,CAAC;MACnE,CAAC,MAAM;QACLE,WAAW,GAAG,CAAC,GAAGD,eAAe,EAAED,QAAQ,CAAC;MAC9C;MACA,OAAO;QACL,GAAGP,SAAS;QACZ9G,YAAY,EAAE;UACZ,GAAG8G,SAAS,CAAC9G,YAAY;UACzBE,gBAAgB,EAAEqH;QACpB;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,eAAe,GAAGA,CAAC3I,QAAQ,EAAEgF,KAAK,KAAK;IAC3C,IAAIA,KAAK,IAAI,CAACzF,IAAI,CAACiH,QAAQ,CAACxB,KAAK,CAAC,EAAE;MAClCxF,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAEyF,KAAK,CAAC,CAAC;MACzBpF,WAAW,CAAC;QAAE,GAAGD,QAAQ;QAAEJ,IAAI,EAAE,CAAC,GAAGI,QAAQ,CAACJ,IAAI,EAAEyF,KAAK;MAAE,CAAC,CAAC;IAC/D;EACF,CAAC;EACD,MAAM4D,aAAa,GAAI/D,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,EAAE;MACrBzB,CAAC,CAACgE,cAAc,CAAC,CAAC;MAClB,MAAM;QAAEhJ;MAAK,CAAC,GAAGgF,CAAC,CAACE,MAAM;MACzB,IAAIlF,IAAI,KAAK,gCAAgC,EAAE;QAC7CD,WAAW,CAAE6H,IAAI,KAAM;UACrB,GAAGA,IAAI;UACPlG,8BAA8B,EAC5B,CAACkG,IAAI,CAAClG,8BAA8B,GAChCkG,IAAI,CAAClG,8BAA8B,GAAG,MAAM,GAC5C,IAAI,IAAIsD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC8D,KAAK,CAACjE,CAAC,CAACE,MAAM,CAACgE,cAAc;QAC5D,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAIlJ,IAAI,KAAK,0BAA0B,EAAE;QAC9CD,WAAW,CAAE6H,IAAI,KAAM;UACrB,GAAGA,IAAI;UACPnG,wBAAwB,EACtB,CAACmG,IAAI,CAACnG,wBAAwB,GAC1BmG,IAAI,CAACnG,wBAAwB,GAAG,IAAI,GACpC,EAAE,IAAIuD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC8D,KAAK,CAACjE,CAAC,CAACE,MAAM,CAACgE,cAAc;QAC1D,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAMtG,aAAa,CAAC,IAAI,CAAC;EAClD,MAAMuG,iBAAiB,GAAGA,CAAA,KAAMvG,aAAa,CAAC,KAAK,CAAC;EACpD,MAAMwG,wBAAwB,GAAGA,CAAA,KAAM;IACrCpG,oBAAoB,CAAC,KAAK,CAAC;IAC3BtE,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAM2K,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAI,CAACzJ,QAAQ,CAACE,IAAI,CAACgG,IAAI,CAAC,CAAC,EAAEuD,MAAM,CAACC,IAAI,CAAC,cAAc,CAAC;IACtD,IAAI,CAAC1J,QAAQ,CAACG,KAAK,IAAIwJ,KAAK,CAAC3J,QAAQ,CAACG,KAAK,CAAC,EAAEsJ,MAAM,CAACC,IAAI,CAAC,eAAe,CAAC;IAC1E,IAAI,CAAC1J,QAAQ,CAACsB,GAAG,CAAC4E,IAAI,CAAC,CAAC,EAAEuD,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC;IAC5C,IAAI,CAAC1J,QAAQ,CAACO,UAAU,CAAC2F,IAAI,CAAC,CAAC,EAAEuD,MAAM,CAACC,IAAI,CAAC,YAAY,CAAC;IAC1D,IAAI,CAAC1J,QAAQ,CAACqB,KAAK,IAAIsI,KAAK,CAAC3J,QAAQ,CAACqB,KAAK,CAAC,EAAEoI,MAAM,CAACC,IAAI,CAAC,OAAO,CAAC;IAClE,IAAI,CAAC1J,QAAQ,CAACoB,QAAQ,CAAC8E,IAAI,CAAC,CAAC,EAAEuD,MAAM,CAACC,IAAI,CAAC,WAAW,CAAC;IACvD,IAAI,CAAC1J,QAAQ,CAACe,WAAW,CAACmF,IAAI,CAAC,CAAC,EAAEuD,MAAM,CAACC,IAAI,CAAC,aAAa,CAAC;IAC5D,IAAI,CAAC1J,QAAQ,CAACa,MAAM,IAAIb,QAAQ,CAACa,MAAM,CAACI,MAAM,KAAK,CAAC,EAClDwI,MAAM,CAACC,IAAI,CAAC,oBAAoB,CAAC;IACnC,OAAOD,MAAM;EACf,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE,OAAO,KAAK;IAC9D,IAAID,IAAI,CAAC5I,MAAM,KAAK6I,IAAI,CAAC7I,MAAM,EAAE,OAAO,KAAK;IAC7C,OAAO4I,IAAI,CAACI,KAAK,CAAC,CAACC,GAAG,EAAEnD,KAAK,KAAKmD,GAAG,KAAKJ,IAAI,CAAC/C,KAAK,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMoD,YAAY,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACnC,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE,OAAOD,IAAI,KAAKC,IAAI;IACxC,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC;IAC/B,MAAMK,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;IAC/B,IAAIC,KAAK,CAACrJ,MAAM,KAAKwJ,KAAK,CAACxJ,MAAM,EAAE,OAAO,KAAK;IAC/C,OAAOqJ,KAAK,CAACL,KAAK,CAAEtD,GAAG,IAAK;MAC1B,IAAIoD,KAAK,CAACC,OAAO,CAACI,IAAI,CAACzD,GAAG,CAAC,CAAC,IAAIoD,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC1D,GAAG,CAAC,CAAC,EAAE;QACxD,OAAOiD,WAAW,CAACQ,IAAI,CAACzD,GAAG,CAAC,EAAE0D,IAAI,CAAC1D,GAAG,CAAC,CAAC;MAC1C;MACA,OAAOyD,IAAI,CAACzD,GAAG,CAAC,KAAK0D,IAAI,CAAC1D,GAAG,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAC5K,eAAe,IAAI,CAACA,eAAe,CAACe,MAAM,EAAE,OAAO,IAAI;IAE5D,MAAM8J,aAAa,GAAG3K,QAAQ,CAACa,MAAM,IAAI,EAAE;IAC3C,MAAM+J,aAAa,GAAG9K,eAAe,CAACe,MAAM,IAAI,EAAE;;IAElD;IACA,IAAI8J,aAAa,CAAC1J,MAAM,KAAK2J,aAAa,CAAC3J,MAAM,EAAE,OAAO,IAAI;;IAE9D;IACA,OAAO0J,aAAa,CAACE,IAAI,CAAC,CAACtD,GAAG,EAAER,KAAK,KAAK;MACxC,IAAIQ,GAAG,YAAYM,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;MAEtC,MAAMiD,UAAU,GAAGF,aAAa,CAAC7D,KAAK,CAAC;MACvC;MACA,MAAMgE,WAAW,GACf,OAAOxD,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAAC7B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG6B,GAAG;MAC1D,MAAMyD,WAAW,GACf,OAAOF,UAAU,KAAK,QAAQ,GAC1BA,UAAU,CAACpF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAC/BoF,UAAU;MAEhB,OAAOC,WAAW,KAAKC,WAAW;IACpC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACnL,eAAe,EAAE,OAAOE,QAAQ;IAErC,MAAMkL,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,aAAa,GAAGnL,QAAQ,CAAC8B,OAAO,YAAY+F,IAAI;IACtD,MAAMuD,eAAe,GAAGV,aAAa,CAAC,CAAC;;IAEvC;IACAH,MAAM,CAACC,IAAI,CAACxK,QAAQ,CAAC,CAACqL,OAAO,CAAE1E,GAAG,IAAK;MACrC,MAAM2E,YAAY,GAAGtL,QAAQ,CAAC2G,GAAG,CAAC;MAClC,MAAM4E,YAAY,GAAGzL,eAAe,CAAC6G,GAAG,CAAC;;MAEzC;MACA,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACpB,IAAIyE,eAAe,EAAE;UACnBF,OAAO,CAACvE,GAAG,CAAC,GAAG;YACb9F,MAAM,EAAEb,QAAQ,CAACa,MAAM;YACvB2K,cAAc,EAAE,QAAQ,CAAE;UAC5B,CAAC;QACH;QACA;MACF;;MAEA;MACA,IAAI7E,GAAG,KAAK,WAAW,EAAE;QACvB,MAAM8E,gBAAgB,GACpB,OAAOH,YAAY,KAAK,QAAQ,GAC5BA,YAAY,CAAC5F,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GACjC4F,YAAY;QAClB,MAAMI,gBAAgB,GACpB,OAAOH,YAAY,KAAK,QAAQ,GAC5BA,YAAY,CAAC7F,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GACjC6F,YAAY;QAElB,IAAIE,gBAAgB,KAAKC,gBAAgB,EAAE;UACzCR,OAAO,CAACvE,GAAG,CAAC,GAAG2E,YAAY;QAC7B;QACA;MACF;MAEA,IAAI3E,GAAG,KAAK,SAAS,EAAE;QACrB,IAAIwE,aAAa,EAAE;UACjBD,OAAO,CAACvE,GAAG,CAAC,GAAG3G,QAAQ,CAAC2G,GAAG,CAAC;QAC9B;QACA;MACF;;MAEA;MACA,IAAIoD,KAAK,CAACC,OAAO,CAACsB,YAAY,CAAC,EAAE;QAC/B,IAAI,CAAC1B,WAAW,CAAC0B,YAAY,EAAEC,YAAY,CAAC,EAAE;UAC5CL,OAAO,CAACvE,GAAG,CAAC,GAAG2E,YAAY;QAC7B;MACF;MACA;MAAA,KACK,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,EAAE;QAClE,IAAI,CAACnB,YAAY,CAACmB,YAAY,EAAEC,YAAY,CAAC,EAAE;UAC7CL,OAAO,CAACvE,GAAG,CAAC,GAAG2E,YAAY;QAC7B;MACF;MACA;MAAA,KACK,IAAIA,YAAY,KAAKC,YAAY,EAAE;QACtC;QACA,IACED,YAAY,KAAK,EAAE,KAClBC,YAAY,KAAK,EAAE,IAClBA,YAAY,KAAK,IAAI,IACrBA,YAAY,KAAKI,SAAS,CAAC,EAC7B;UACA;QACF;QACA,IACEL,YAAY,KAAK,IAAI,KACpBC,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAKI,SAAS,IAC1BJ,YAAY,KAAK,EAAE,CAAC,EACtB;UACA;QACF;;QAEA;QACA,IACE,OAAOD,YAAY,KAAK,QAAQ,IAChC,OAAOC,YAAY,KAAK,QAAQ,EAChC;UACA,IAAID,YAAY,CAACpF,IAAI,CAAC,CAAC,KAAKqF,YAAY,CAACrF,IAAI,CAAC,CAAC,EAAE;YAC/C;UACF;QACF;;QAEA;QACA,IACE,OAAOoF,YAAY,KAAK,QAAQ,IAChC,OAAOC,YAAY,KAAK,QAAQ,EAChC;UACA,IAAIK,MAAM,CAACN,YAAY,CAAC,KAAKM,MAAM,CAACL,YAAY,CAAC,EAAE;YACjD;UACF;QACF;QAEAL,OAAO,CAACvE,GAAG,CAAC,GAAG2E,YAAY;MAC7B;IACF,CAAC,CAAC;IAEF,OAAOJ,OAAO;EAChB,CAAC;EACD,MAAMW,YAAY,GAAG,MAAO3G,CAAC,IAAK;IAChCA,CAAC,CAACgE,cAAc,CAAC,CAAC;IAClBnG,aAAa,CAAC,KAAK,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,MAAM6I,aAAa,GAAGb,gBAAgB,CAAC,CAAC;;IAExC;IACApH,OAAO,CAACkI,GAAG,CAAC,gCAAgC,CAAC;IAC7ClI,OAAO,CAACkI,GAAG,CAAC,oBAAoB,EAAEjM,eAAe,CAAC;IAClD+D,OAAO,CAACkI,GAAG,CAAC,oBAAoB,EAAE/L,QAAQ,CAAC;IAC3C6D,OAAO,CAACkI,GAAG,CAAC,mBAAmB,EAAED,aAAa,CAAC;IAC/CjI,OAAO,CAACkI,GAAG,CAAC,iCAAiC,CAAC;;IAE9C;IACA,IAAIxB,MAAM,CAACC,IAAI,CAACsB,aAAa,CAAC,CAAC7K,MAAM,KAAK,CAAC,EAAE;MAC3CuG,KAAK,CAAC,gCAAgC,CAAC;MACvCvE,eAAe,CAAC,KAAK,CAAC;MACtB;IACF;IAEAY,OAAO,CAACkI,GAAG,CAAC,iBAAiB,EAAED,aAAa,CAAC;IAE7C,MAAMnI,IAAI,GAAG,IAAIqI,QAAQ,CAAC,CAAC;;IAE3B;IACAzB,MAAM,CAACC,IAAI,CAACsB,aAAa,CAAC,CAACT,OAAO,CAAE1E,GAAG,IAAK;MAC1C,MAAMtB,KAAK,GAAGyG,aAAa,CAACnF,GAAG,CAAC;MAEhC,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACpB;QACA,IAAItB,KAAK,CAACmG,cAAc,KAAK,QAAQ,EAAE;UACrC;UACAnG,KAAK,CAACxE,MAAM,CAACwK,OAAO,CAAE3N,KAAK,IAAK;YAC9BiG,IAAI,CAACsI,MAAM,CAAC,QAAQ,EAAEvO,KAAK,CAAC;UAC9B,CAAC,CAAC;;UAEF;UACA,MAAMwO,kBAAkB,GAAGpM,eAAe,CAACe,MAAM,IAAI,EAAE;UACvD,MAAMsL,iBAAiB,GAAG9G,KAAK,CAACxE,MAAM,CAAC0D,GAAG,CAAEgD,GAAG,IAC7C,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAAC7B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,UACvD,CAAC;;UAED;UACA/B,IAAI,CAACsI,MAAM,CACT,eAAe,EACfhI,IAAI,CAACE,SAAS,CAAC;YACbiI,cAAc,EAAEF,kBAAkB;YAClCvB,aAAa,EAAEwB,iBAAiB;YAChCE,SAAS,EAAE;UACb,CAAC,CACH,CAAC;QACH;MACF,CAAC,MAAM,IAAI1F,GAAG,KAAK,SAAS,IAAItB,KAAK,EAAE;QACrC1B,IAAI,CAACsI,MAAM,CAAC,SAAS,EAAE5G,KAAK,CAAC;MAC/B,CAAC,MAAM,IAAIsB,GAAG,KAAK,MAAM,IAAIoD,KAAK,CAACC,OAAO,CAAC3E,KAAK,CAAC,EAAE;QACjDA,KAAK,CAACgG,OAAO,CAAC,CAACrG,GAAG,EAAE+B,KAAK,KAAKpD,IAAI,CAACsI,MAAM,CAAC,QAAQlF,KAAK,GAAG,EAAE/B,GAAG,CAAC,CAAC;MACnE,CAAC,MAAM,IAAI2B,GAAG,KAAK,QAAQ,IAAIoD,KAAK,CAACC,OAAO,CAAC3E,KAAK,CAAC,EAAE;QACnDA,KAAK,CAACgG,OAAO,CAAC,CAACiB,KAAK,EAAEvF,KAAK,KAAKpD,IAAI,CAACsI,MAAM,CAAC,UAAUlF,KAAK,GAAG,EAAEuF,KAAK,CAAC,CAAC;MACzE,CAAC,MAAM,IAAI3F,GAAG,KAAK,OAAO,IAAIoD,KAAK,CAACC,OAAO,CAAC3E,KAAK,CAAC,EAAE;QAClDA,KAAK,CAACgG,OAAO,CAAC,CAACkB,IAAI,EAAExF,KAAK,KAAKpD,IAAI,CAACsI,MAAM,CAAC,SAASlF,KAAK,GAAG,EAAEwF,IAAI,CAAC,CAAC;MACtE,CAAC,MAAM,IAAI5F,GAAG,KAAK,qBAAqB,IAAI,OAAOtB,KAAK,KAAK,QAAQ,EAAE;QACrE1B,IAAI,CAACsI,MAAM,CAAC,qBAAqB,EAAEhI,IAAI,CAACE,SAAS,CAACkB,KAAK,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAIsB,GAAG,KAAK,cAAc,IAAI,OAAOtB,KAAK,KAAK,QAAQ,EAAE;QAC9D1B,IAAI,CAACsI,MAAM,CAAC,cAAc,EAAEhI,IAAI,CAACE,SAAS,CAACkB,KAAK,CAAC,CAAC;MACpD,CAAC,MAAM;QACL;QACA1B,IAAI,CAACsI,MAAM,CAACtF,GAAG,EAAEtB,KAAK,IAAI,EAAE,CAAC;MAC/B;IACF,CAAC,CAAC;;IAEF;IACA,IAAIrF,QAAQ,CAACc,SAAS,EAAE;MACtB6C,IAAI,CAACsI,MAAM,CAAC,WAAW,EAAEjM,QAAQ,CAACc,SAAS,CAAC;IAC9C;;IAEA;IACA+C,OAAO,CAACkI,GAAG,CAAC,8BAA8B,CAAC;IAC3C,KAAK,IAAI,CAACpF,GAAG,EAAEtB,KAAK,CAAC,IAAI1B,IAAI,CAAC6I,OAAO,CAAC,CAAC,EAAE;MACvC3I,OAAO,CAACkI,GAAG,CAAC,GAAGpF,GAAG,GAAG,EAAEtB,KAAK,CAAC;IAC/B;IAEA,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMvH,KAAK,CAACuQ,GAAG,CAC9B,8CAA8CzM,QAAQ,CAAC0M,GAAG,EAAE,EAC5D/I,IAAI,EACJ;QAAEgJ,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MAAE,CACvD,CAAC;MACD9I,OAAO,CAACkI,GAAG,CAAC,+BAA+B,EAAEtI,QAAQ,CAACE,IAAI,CAAC;MAC3DR,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAAgJ,eAAA;MACd/I,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE,EAAAgJ,eAAA,GAAAhJ,KAAK,CAACH,QAAQ,cAAAmJ,eAAA,uBAAdA,eAAA,CAAgBjJ,IAAI,KAAIC,KAAK,CAAC;MACvE4D,KAAK,CAAC,6CAA6C,CAAC;IACtD,CAAC,SAAS;MACRvE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE/F,OAAA,CAAAE,SAAA;IAAAyP,QAAA,gBACE3P,OAAA;MAAQ4P,SAAS,EAAC,yBAAyB;MAAAD,QAAA,eACzC3P,OAAA;QAAK4P,SAAS,EAAC,wBAAwB;QAAAD,QAAA,gBACrC3P,OAAA;UACE6P,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,aAAa,EAAE,KAAK;YACpBC,GAAG,EAAE,MAAM;YACXC,cAAc,EAAE;UAClB,CAAE;UAAAP,QAAA,gBAEF3P,OAAA,CAACZ,UAAU;YAAAuQ,QAAA,eACT3P,OAAA,CAACX,mBAAmB;cAACgQ,IAAI,EAAE,MAAO;cAACc,OAAO,EAAExO;YAAO;cAAAyO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACbvQ,OAAA;YAAA2P,QAAA,EAAI;UAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNvQ,OAAA;UAAA2P,QAAA,EAAG;QAAyB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACTvQ,OAAA;MAAMwQ,QAAQ,EAAGxI,CAAC,IAAKA,CAAC,CAACgE,cAAc,CAAC,CAAE;MAAA2D,QAAA,gBACxC3P,OAAA;QAAK4P,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B3P,OAAA;UAAK4P,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB3P,OAAA;YAAA2P,QAAA,EAAI;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBvQ,OAAA,CAACb,GAAG;YACFsR,EAAE,EAAE;cAAEX,OAAO,EAAE,MAAM;cAAEE,aAAa,EAAE,QAAQ;cAAEhP,KAAK,EAAE;YAAO,CAAE;YAAA2O,QAAA,gBAGhE3P,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,MAAM;gBACXmF,KAAK,EAAErF,QAAQ,CAACE,IAAK;gBACrB0N,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC,oBAAoB;gBAChCC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,OAAO;gBACZmF,KAAK,EAAErF,QAAQ,CAACG,KAAM;gBACtByN,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC,eAAe;gBAC3BC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAYNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBvQ,OAAA;gBACEgD,IAAI,EAAC,UAAU;gBACfmF,KAAK,EAAErF,QAAQ,CAACK,QAAS;gBACzBuN,QAAQ,EAAE3I,oBAAqB;gBAC/B6I,QAAQ;gBAAAjB,QAAA,gBAER3P,OAAA;kBAAQmI,KAAK,EAAC,EAAE;kBAAC0I,QAAQ;kBAAAlB,QAAA,EAAC;gBAE1B;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRvO,UAAU,CAACqF,GAAG,CAAElE,QAAQ,iBACvBnD,OAAA;kBAA2BmI,KAAK,EAAEhF,QAAQ,CAACqM,GAAI;kBAAAG,QAAA,EAC5CxM,QAAQ,CAACH;gBAAI,GADHG,QAAQ,CAACqM,GAAG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBAEzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BvQ,OAAA;gBACEgD,IAAI,EAAC,aAAa;gBAClBmF,KAAK,EAAErF,QAAQ,CAACM,WAAY;gBAC5BsN,QAAQ,EAAEtI,uBAAwB;gBAAAuH,QAAA,gBAElC3P,OAAA;kBAAQmI,KAAK,EAAC,EAAE;kBAAC0I,QAAQ;kBAAAlB,QAAA,EAAC;gBAE1B;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRrO,aAAa,CAACmF,GAAG,CAAEyJ,WAAW,iBAC7B9Q,OAAA;kBAA8BmI,KAAK,EAAE2I,WAAW,CAACtB,GAAI;kBAAAG,QAAA,EAClDmB,WAAW,CAAC9N;gBAAI,GADN8N,WAAW,CAACtB,GAAG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEpB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBAEzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBvQ,OAAA;gBACEgD,IAAI,EAAC,MAAM;gBACXmF,KAAK,EAAErF,QAAQ,CAACQ,IAAK;gBACrBoN,QAAQ,EAAEpI,YAAa;gBAAAqH,QAAA,gBAEvB3P,OAAA;kBAAQmI,KAAK,EAAC,EAAE;kBAAC0I,QAAQ;kBAAAlB,QAAA,EAAC;gBAE1B;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRnO,KAAK,CAACiF,GAAG,CAAE/D,IAAI,iBACdtD,OAAA;kBAAuBmI,KAAK,EAAE7E,IAAI,CAACkM,GAAI;kBAAAG,QAAA,EACpCrM,IAAI,CAACN;gBAAI,GADCM,IAAI,CAACkM,GAAG;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAG;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClBvQ,OAAA;gBAAK4P,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAChCtC,MAAM,CAACiC,OAAO,CAACpJ,UAAU,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAClE,QAAQ,EAAE4N,OAAO,CAAC,kBAClD/Q,OAAA;kBAEE0Q,QAAQ,EAAG1I,CAAC,IAAK;oBACf8D,eAAe,CAAC3I,QAAQ,EAAE6E,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;oBACzCH,CAAC,CAACE,MAAM,CAACC,KAAK,GAAG,EAAE;kBACrB,CAAE;kBAAAwH,QAAA,gBAEF3P,OAAA;oBAAQmI,KAAK,EAAC,EAAE;oBAAAwH,QAAA,EAAE,UAAUxM,QAAQ;kBAAE;oBAAAiN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,EAC/CQ,OAAO,CAAC1J,GAAG,CAAE2J,MAAM,iBAClBhR,OAAA;oBAAqBmI,KAAK,EAAE6I,MAAO;oBAAArB,QAAA,EAChCqB;kBAAM,GADIA,MAAM;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACT,CAAC;gBAAA,GAXGpN,QAAQ;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYP,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,MAAM;gBACX2N,WAAW,EAAC,gDAAgD;gBAC5DM,SAAS,EAAEzH,YAAa;gBACxBoG,SAAS,EAAC,WAAW;gBACrBC,KAAK,EAAE;kBAAEqB,MAAM,EAAE;gBAAW;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACFvQ,OAAA;gBAAAoQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvQ,OAAA;gBAAK4P,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAClBjN,IAAI,CAAC2E,GAAG,CAAC,CAACS,GAAG,EAAE+B,KAAK,kBACnB7J,OAAA;kBAAkB4P,SAAS,EAAC,KAAK;kBAAAD,QAAA,GAC9B7H,GAAG,eACJ9H,OAAA;oBACEsD,IAAI,EAAC,QAAQ;oBACb6M,OAAO,EAAEA,CAAA,KAAMvG,eAAe,CAACC,KAAK,CAAE;oBACtC+F,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAE1B3P,OAAA,CAACf,eAAe;sBAACoQ,IAAI,EAAE;oBAAG;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA,GARA1G,KAAK;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvQ,OAAA,CAACb,GAAG;YACFsR,EAAE,EAAE;cAAEX,OAAO,EAAE,MAAM;cAAEE,aAAa,EAAE,QAAQ;cAAEhP,KAAK,EAAE;YAAO,CAAE;YAAA2O,QAAA,gBAEhE3P,OAAA;cAAA2P,QAAA,EAAI;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAYxBvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,YAAY;gBACjBmF,KAAK,EAAErF,QAAQ,CAACO,UAAW;gBAC3BqN,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC;cAAiB;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAiB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,iBAAiB;gBACtBmF,KAAK,EAAErF,QAAQ,CAACS,eAAgB;gBAChCmN,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC,UAAU;gBACtBC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAyB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxCvQ,OAAA;gBACE6P,KAAK,EAAE;kBACLT,KAAK,EAAE,MAAM;kBACb8B,MAAM,EAAE;gBACV,CAAE;gBAAAvB,QAAA,EACH;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,QAAQ;gBACbmF,KAAK,EAAErF,QAAQ,CAACW,MAAM,GAAGX,QAAQ,CAACW,MAAM,CAACwF,IAAI,CAAC,GAAG,CAAC,GAAG,EAAG,CAAC;gBAAA;gBACzDyH,QAAQ,EAAG1I,CAAC,IAAKoB,iBAAiB,CAACpB,CAAC,EAAE,QAAQ,CAAE;gBAChD2I,WAAW,EAAC,sBAAsB;gBAClCd,KAAK,EAAE;kBAAEsB,SAAS,EAAE;gBAAO;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAwB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvCvQ,OAAA;gBACE6P,KAAK,EAAE;kBACLT,KAAK,EAAE,MAAM;kBACb8B,MAAM,EAAE;gBACV,CAAE;gBAAAvB,QAAA,EACH;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAEPvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,OAAO;gBACZmF,KAAK,EAAErF,QAAQ,CAACY,KAAK,CAACuF,IAAI,CAAC,GAAG,CAAE;gBAChCyH,QAAQ,EAAG1I,CAAC,IAAKoB,iBAAiB,CAACpB,CAAC,EAAE,OAAO,CAAE;gBAC/C2I,WAAW,EAAC,0BAA0B;gBACtCd,KAAK,EAAE;kBAAEsB,SAAS,EAAE;gBAAO;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAUNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BvQ,OAAA;gBACEgD,IAAI,EAAC,aAAa;gBAClBmF,KAAK,EAAErF,QAAQ,CAACe,WAAY;gBAC5B6M,QAAQ,EAAG1I,CAAC,IAAK;kBACf,MAAMoJ,KAAK,GAAGpJ,CAAC,CAACE,MAAM,CAACC,KAAK,CACzBa,IAAI,CAAC,CAAC,CACNN,KAAK,CAAC,KAAK,CAAC,CACZqB,MAAM,CAAEsH,IAAI,IAAKA,IAAI,CAACtN,MAAM,GAAG,CAAC,CAAC;kBACpC,IAAIqN,KAAK,CAACrN,MAAM,IAAI,EAAE,EAAE;oBACtBuE,YAAY,CAACN,CAAC,CAAC;kBACjB;gBACF,CAAE;gBACF2I,WAAW,EAAC,wFAAwF;gBACpGW,SAAS,EAAC;cAAM;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACFvQ,OAAA;gBACE6P,KAAK,EAAE;kBAAE0B,QAAQ,EAAE,MAAM;kBAAEnC,KAAK,EAAE,MAAM;kBAAE+B,SAAS,EAAE;gBAAM,CAAE;gBAAAxB,QAAA,GAC9D,aACY,EAAC,GAAG,EAEb7M,QAAQ,CAACe,WAAW,CACjBmF,IAAI,CAAC,CAAC,CACNN,KAAK,CAAC,KAAK,CAAC,CACZqB,MAAM,CAAEsH,IAAI,IAAKA,IAAI,CAACtN,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAC5C,WAEH;cAAA;gBAAAqM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvQ,OAAA;YAAK4P,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzB3P,OAAA;cACE6P,KAAK,EAAE;gBACL2B,SAAS,EAAE,MAAM;gBACjBC,YAAY,EAAE,MAAM;gBACpBN,SAAS,EAAE;cACb,CAAE;cAAAxB,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvQ,OAAA,CAACb,GAAG;cACFsR,EAAE,EAAE;gBACFX,OAAO,EAAE,MAAM;gBACfG,GAAG,EAAE,MAAM;gBACXD,aAAa,EAAE,KAAK;gBACpBE,cAAc,EAAE,eAAe;gBAC/BlP,KAAK,EAAE;cACT,CAAE;cAAA2O,QAAA,gBAEF3P,OAAA,CAACb,GAAG;gBAACsR,EAAE,EAAE;kBAAEX,OAAO,EAAE,MAAM;kBAAEE,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpD3P,OAAA;kBAAA2P,QAAA,EAAO;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBvQ,OAAA;kBACEsD,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,QAAQ;kBACbmF,KAAK,EAAErF,QAAQ,CAACgB,mBAAmB,CAACC,MAAO;kBAC3C4M,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAG1I,CAAC,IACVkB,kBAAkB,CAAClB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvQ,OAAA,CAACb,GAAG;gBAACsR,EAAE,EAAE;kBAAEX,OAAO,EAAE,MAAM;kBAAEE,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpD3P,OAAA;kBAAA2P,QAAA,EAAO;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBvQ,OAAA;kBACEsD,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,OAAO;kBACZmF,KAAK,EAAErF,QAAQ,CAACgB,mBAAmB,CAAC9C,KAAM;kBAC1C2P,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAG1I,CAAC,IACVkB,kBAAkB,CAAClB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvQ,OAAA,CAACb,GAAG;gBAACsR,EAAE,EAAE;kBAAEX,OAAO,EAAE,MAAM;kBAAEE,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpD3P,OAAA;kBAAA2P,QAAA,EAAO;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBvQ,OAAA;kBACEsD,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,QAAQ;kBACbmF,KAAK,EAAErF,QAAQ,CAACgB,mBAAmB,CAAC7C,MAAO;kBAC3C0P,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAG1I,CAAC,IACVkB,kBAAkB,CAAClB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvQ,OAAA,CAACb,GAAG;gBAACsR,EAAE,EAAE;kBAAEX,OAAO,EAAE,MAAM;kBAAEE,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpD3P,OAAA;kBAAA2P,QAAA,EAAO;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBvQ,OAAA;kBACEsD,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,QAAQ;kBACbmF,KAAK,EAAErF,QAAQ,CAACgB,mBAAmB,CAACE,MAAO;kBAC3C2M,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAG1I,CAAC,IACVkB,kBAAkB,CAAClB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvQ,OAAA,CAACb,GAAG;YACFsR,EAAE,EAAE;cAAEX,OAAO,EAAE,MAAM;cAAEE,aAAa,EAAE,QAAQ;cAAEhP,KAAK,EAAE;YAAO,CAAE;YAAA2O,QAAA,gBAGhE3P,OAAA;cAAA2P,QAAA,EAAI;YAAiB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,SAAS;gBACdmF,KAAK,EAAErF,QAAQ,CAACmB,OAAQ;gBACxByN,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,WAAW;gBAChBmF,KAAK,EAAErF,QAAQ,CAAChB,SAAU;gBAC1B4O,QAAQ,EAAEpI,YAAa;gBACvBoJ,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,eACzB3P,OAAA;gBACE6P,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEE,GAAG,EAAE;gBAAO,CAAE;gBAAAN,QAAA,gBAE9D3P,OAAA;kBACEsD,IAAI,EAAC,UAAU;kBACfN,IAAI,EAAC,aAAa;kBAClBmI,OAAO,EAAErI,QAAQ,CAACuB,WAAY;kBAC9BqM,QAAQ,EAAExF,oBAAqB;kBAC/B2E,KAAK,EAAE;oBAAE7O,KAAK,EAAE;kBAAO;gBAAE;kBAAAoP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,iBAEF,eAAAvQ,OAAA;kBAAM6P,KAAK,EAAE;oBAAE8B,UAAU,EAAE;kBAAS,CAAE;kBAAAhC,QAAA,EAAC;gBAEvC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAU;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzBvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,UAAU;gBACfmF,KAAK,EAAErF,QAAQ,CAACoB,QAAS;gBACzBwM,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC,wCAAwC;gBACpDC,QAAQ;gBACRgB,OAAO,EAAC,WAAS;gBACjBC,KAAK,EAAC;cAAwC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,OAAO;gBACZmF,KAAK,EAAErF,QAAQ,CAACqB,KAAM;gBACtBuM,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC;cAAkC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,KAAK;gBACVmF,KAAK,EAAErF,QAAQ,CAACsB,GAAI;gBACpBsM,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC;cAA8B;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvQ,OAAA,CAACb,GAAG;YACFsR,EAAE,EAAE;cAAEX,OAAO,EAAE,MAAM;cAAEE,aAAa,EAAE,QAAQ;cAAEhP,KAAK,EAAE;YAAO,CAAE;YAAA2O,QAAA,gBAGhE3P,OAAA;cAAA2P,QAAA,EAAI;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,eAAe;gBACpBmF,KAAK,EAAErF,QAAQ,CAACwB,YAAY,CAACC,aAAc;gBAC3CmM,QAAQ,EAAG1I,CAAC,IAAKkB,kBAAkB,CAAClB,CAAC,EAAE,cAAc,CAAE;gBACvD2I,WAAW,EAAC;cAAuC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAkB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCvQ,OAAA;gBACE6P,KAAK,EAAE;kBACLC,OAAO,EAAE,MAAM;kBACfE,aAAa,EAAE,KAAK;kBACpBC,GAAG,EAAE,MAAM;kBACXkB,SAAS,EAAE;gBACb,CAAE;gBAAAxB,QAAA,EAED,CACC,sBAAsB,EACtB,eAAe,EACf,wBAAwB,CACzB,CAACtI,GAAG,CAAEsE,QAAQ,iBACb3L,OAAA;kBAEE6P,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBE,GAAG,EAAE,KAAK;oBACV6B,MAAM,EAAE,SAAS;oBACjBP,QAAQ,EAAE;kBACZ,CAAE;kBAAA5B,QAAA,gBAEF3P,OAAA;oBACEsD,IAAI,EAAC,UAAU;oBACf6H,OAAO,EAAErI,QAAQ,CAACwB,YAAY,CAACE,gBAAgB,CAACmF,QAAQ,CACtDgC,QACF,CAAE;oBACF+E,QAAQ,EAAEA,CAAA,KAAMhF,4BAA4B,CAACC,QAAQ,CAAE;oBACvDkE,KAAK,EAAE;sBAAEiC,MAAM,EAAE;oBAAU;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,EACD5E,QAAQ;gBAAA,GAjBJA,QAAQ;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvQ,OAAA,CAACb,GAAG;YACFsR,EAAE,EAAE;cAAEX,OAAO,EAAE,MAAM;cAAEE,aAAa,EAAE,QAAQ;cAAEhP,KAAK,EAAE;YAAO,CAAE;YAAA2O,QAAA,gBAGhE3P,OAAA;cAAA2P,QAAA,EAAI;YAAsB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAA2B;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CvQ,OAAA;gBACEgD,IAAI,EAAC,0BAA0B;gBAC/BmF,KAAK,EAAErF,QAAQ,CAAC2B,wBAAyB;gBACzCiM,QAAQ,EAAEpI,YAAa;gBACvB2I,SAAS,EAAElF,aAAc;gBACzB4E,WAAW,EAAC,sCAAsC;gBAClDC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAiC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChDvQ,OAAA;gBACEgD,IAAI,EAAC,gCAAgC;gBACrCmF,KAAK,EAAErF,QAAQ,CAAC4B,8BAA+B;gBAC/CgM,QAAQ,EAAEpI,YAAa;gBACvB2I,SAAS,EAAElF,aAAc;gBACzB4E,WAAW,EAAC,4CAA4C;gBACxDC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzB3P,OAAA;gBAAA2P,QAAA,EAAO;cAAsC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,mCAAmC;gBACxCmF,KAAK,EAAErF,QAAQ,CAAC6B,iCAAkC;gBAClD+L,QAAQ,EAAEpI,YAAa;gBACvBqI,WAAW,EAAC,mCAAmC;gBAC/CiB,OAAO,EAAC,WAAS;gBACjBC,KAAK,EAAC;cAAwC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvQ,OAAA;UAAK4P,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzB3P,OAAA;YAAK4P,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAC/BjK,gBAAgB,gBACf1F,OAAA;cACEW,GAAG,EAAE+E,gBAAiB,CAAC;cAAA;cACvBqM,GAAG,EAAC,cAAc;cAClBnC,SAAS,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,GACA3M,SAAS,gBACX5D,OAAA;cACEW,GAAG,EACD,OAAOiD,SAAS,KAAK,QAAQ,GACzB,uDAAuDA,SAAS,EAAE,GAClE0D,GAAG,CAACC,eAAe,CAAC3D,SAAS,CAClC;cACDmO,GAAG,EAAC,cAAc;cAClBnC,SAAS,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,gBAEFvQ,OAAA;cACE6P,KAAK,EAAE;gBACLmC,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,MAAM;gBACpB7C,KAAK,EAAE,SAAS;gBAChB8B,MAAM,EAAE,OAAO;gBACflQ,KAAK,EAAE,KAAK;gBACZwQ,SAAS,EAAE,QAAQ;gBACnBU,OAAO,EAAE,MAAM;gBACff,SAAS,EAAE;cACb,CAAE;cAAAxB,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNvQ,OAAA;YAAK4P,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9B3P,OAAA;cAAA2P,QAAA,EAAO;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAE9BvQ,OAAA;cACE4P,SAAS,EAAC,WAAW;cACrBuC,UAAU,EAAGnK,CAAC,IAAKA,CAAC,CAACgE,cAAc,CAAC,CAAE,CAAC;cAAA;cACvCoG,MAAM,EAAGpK,CAAC,IAAK;gBACbA,CAAC,CAACgE,cAAc,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM5B,KAAK,GAAGyC,KAAK,CAACwF,IAAI,CAACrK,CAAC,CAACsK,YAAY,CAAClI,KAAK,CAAC,CAAC,CAAC;gBAChDF,iBAAiB,CAAC;kBAAEhC,MAAM,EAAE;oBAAEkC;kBAAM;gBAAE,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAE;cAAAuF,QAAA,gBAEF3P,OAAA;gBAAA2P,QAAA,eACE3P,OAAA;kBAAA2P,QAAA,EAAQ;gBAKR;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPvQ,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXiP,QAAQ;gBACRC,MAAM,EAAC,mCAAmC;gBAC1C9B,QAAQ,EAAExG,iBAAkB;gBAC5B0F,SAAS,EAAC,YAAY;gBACtBC,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO,CAAE,CAAC;gBAAA;gBAC5B2C,EAAE,EAAC;cAAW;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACFvQ,OAAA;gBAAO0S,OAAO,EAAC,WAAW;gBAAC9C,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,GAAC,iCAErD,eAAA3P,OAAA;kBAAAoQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,yBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACb6M,OAAO,EAAEA,CAAA,KAAMrP,QAAQ,CAAC6R,cAAc,CAAC,WAAW,CAAC,CAACC,KAAK,CAAC,CAAE;gBAC5DhD,SAAS,EAAC,YAAY;gBAAAD,QAAA,EACvB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvQ,OAAA;cAAK4P,SAAS,EAAC,gBAAgB;cAAAD,QAAA,GAC5BnK,aAAa,CAAC6B,GAAG,CAAC,CAACwL,OAAO,EAAEhJ,KAAK,kBAChC7J,OAAA;gBACE4P,SAAS,EAAE,aACTiD,OAAO,KAAKnN,gBAAgB,GAAG,gBAAgB,GAAG,EAAE,EACnD;gBAAAiK,QAAA,gBAGH3P,OAAA;kBACE6P,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBG,cAAc,EAAE,eAAe;oBAC/BD,GAAG,EAAE;kBACP,CAAE;kBAAAN,QAAA,gBAEF3P,OAAA;oBACEW,GAAG,EAAEkS,OAAQ;oBACbd,GAAG,EAAE,aAAalI,KAAK,EAAG;oBAC1B+F,SAAS,EAAC,iBAAiB;oBAC3BO,OAAO,EAAEA,CAAA,KAAMtF,kBAAkB,CAAChB,KAAK;kBAAE;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACFvQ,OAAA;oBAAA2P,QAAA,EAAM;kBAAqB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCvQ,OAAA;oBAAM4P,SAAS,EAAC,WAAW;oBAAAD,QAAA,EACxBkD,OAAO,KAAKnN,gBAAgB,GAAG,QAAQ,GAAG;kBAAG;oBAAA0K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNvQ,OAAA;kBACE6P,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBG,cAAc,EAAE,eAAe;oBAC/BD,GAAG,EAAE;kBACP,CAAE;kBAAAN,QAAA,eAEF3P,OAAA;oBACE4P,SAAS,EAAC,kBAAkB;oBAC5BO,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAACjB,KAAK,CAAE;oBAAA8F,QAAA,EACzC;kBAED;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAnCD1G,KAAK;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoCP,CACN,CAAC,EAED5M,MAAM,CACJoG,MAAM,CAAC,CAACC,CAAC,EAAEH,KAAK,KAAK,CAACrE,aAAa,CAACqE,KAAK,CAAC,CAAC,CAC3CxC,GAAG,CAAC,CAAC7G,KAAK,EAAEqJ,KAAK,kBAChB7J,OAAA;gBACE4P,SAAS,EAAE,aACTpP,KAAK,KAAKoD,SAAS,GAAG,gBAAgB,GAAG,EAAE,EAC1C;gBAAA+L,QAAA,gBAGH3P,OAAA;kBACE6P,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBG,cAAc,EAAE,eAAe;oBAC/BD,GAAG,EAAE;kBACP,CAAE;kBAAAN,QAAA,gBAEF3P,OAAA;oBACEW,GAAG,EACD,OAAOH,KAAK,KAAK,QAAQ,GACrB,uDAAuDA,KAAK,EAAE,GAC9D8G,GAAG,CAACC,eAAe,CAAC/G,KAAK,CAC9B;oBACDuR,GAAG,EAAE,aAAalI,KAAK,EAAG;oBAC1B+F,SAAS,EAAC,iBAAiB;oBAC3BO,OAAO,EAAEA,CAAA,KACPtF,kBAAkB,CAACrF,aAAa,CAACzB,MAAM,GAAG8F,KAAK;kBAChD;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFvQ,OAAA;oBAAA2P,QAAA,EAAM;kBAAqB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCvQ,OAAA;oBAAM4P,SAAS,EAAC,WAAW;oBAAAD,QAAA,EACxBnP,KAAK,KAAKoD,SAAS,GAAG,QAAQ,GAAG;kBAAG;oBAAAwM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNvQ,OAAA;kBACE6P,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBG,cAAc,EAAE,eAAe;oBAC/BD,GAAG,EAAE;kBACP,CAAE;kBAAAN,QAAA,eAEF3P,OAAA;oBACE4P,SAAS,EAAC,kBAAkB;oBAC5BO,OAAO,EAAEA,CAAA,KACPrF,iBAAiB,CAACtF,aAAa,CAACzB,MAAM,GAAG8F,KAAK,CAC/C;oBAAA8F,QAAA,EACF;kBAED;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GA3CD,YAAY1G,KAAK,EAAE;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4CrB,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvQ,OAAA;YAAK4P,SAAS,EAAC,oBAAoB;YAAAD,QAAA,gBACjC3P,OAAA;cAAA2P,QAAA,EAAO;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9BvQ,OAAA;cAAK4P,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5B3P,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXkP,MAAM,EAAC,iCAAiC;gBACxC9B,QAAQ,EAAErF,eAAgB;gBAC1BuE,SAAS,EAAC,gBAAgB;gBAC1BC,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO,CAAE;gBAC3B2C,EAAE,EAAC;cAAc;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACFvQ,OAAA;gBAAO0S,OAAO,EAAC,cAAc;gBAAC9C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,GAAC,oCAE5D,eAAA3P,OAAA;kBAAAoQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,qDAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACb6M,OAAO,EAAEA,CAAA,KACPrP,QAAQ,CAAC6R,cAAc,CAAC,cAAc,CAAC,CAACC,KAAK,CAAC,CAC/C;gBACDhD,SAAS,EAAC,YAAY;gBAAAD,QAAA,EACvB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLzN,QAAQ,CAAC8B,OAAO;YAAA;YAAM;YACrB5E,OAAA;cAAK4P,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5B3P,OAAA;gBAAA2P,QAAA,GAAM,iBAAe,EAAC7M,QAAQ,CAAC8B,OAAO,CAAC5B,IAAI;cAAA;gBAAAoN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDvQ,OAAA;gBACEsD,IAAI,EAAC,QAAQ;gBACb6M,OAAO,EAAEA,CAAA,KACPpN,WAAW,CAAE6H,IAAI,KAAM;kBAAE,GAAGA,IAAI;kBAAEhG,OAAO,EAAE;gBAAK,CAAC,CAAC,CACnD;gBACDgL,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAC3B;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNvQ,OAAA;QAAK4P,SAAS,EAAC,cAAc;QAAAD,QAAA,GAC1BvJ,gBAAgB,CAACrC,MAAM,GAAG,CAAC,iBAC1B/D,OAAA;UAAK6P,KAAK,EAAE;YAAET,KAAK,EAAE,KAAK;YAAEqC,YAAY,EAAE;UAAO,CAAE;UAAA9B,QAAA,GAAC,4CACR,EAAC,GAAG,EAC7CvJ,gBAAgB,CAAC6C,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACN,eACDvQ,OAAA;UACE4P,SAAS,EAAC,YAAY;UACtBtM,IAAI,EAAC,QAAQ;UACb6M,OAAO,EAAEA,CAAA,KAAM;YACb,MAAM5D,MAAM,GAAGD,YAAY,CAAC,CAAC;YAC7B,IAAIC,MAAM,CAACxI,MAAM,GAAG,CAAC,EAAE;cACrBsC,mBAAmB,CAACkG,MAAM,CAAC;cAC3B;YACF;YACAlG,mBAAmB,CAAC,EAAE,CAAC;YACvB8F,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACF0E,QAAQ,EAAE/K,YAAa;UAAA6J,QAAA,EAEtB7J,YAAY,gBAAG9F,OAAA,CAACR,gBAAgB;YAAC6P,IAAI,EAAE;UAAG;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAQ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACTvQ,OAAA;UAAQ4P,SAAS,EAAC,YAAY;UAACO,OAAO,EAAElF,YAAa;UAAA0E,QAAA,EAAC;QAEtD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACPvQ,OAAA,CAACV,kBAAkB;MACjBwT,IAAI,EAAElN,YAAa;MACnBiM,KAAK,EAAC,wBAAwB;MAC9BkB,OAAO,EAAC,+CAA+C;MACvDC,SAAS,EAAErE,YAAa;MACxBsE,QAAQ,EAAE7G;IAAkB;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eACFvQ,OAAA,CAACP,MAAM;MAACqT,IAAI,EAAE9M,iBAAkB;MAACkN,OAAO,EAAE7G,wBAAyB;MAAAsD,QAAA,gBACjE3P,OAAA,CAACH,WAAW;QAAA8P,QAAA,EAAC;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAClCvQ,OAAA,CAACL,aAAa;QAAAgQ,QAAA,eACZ3P,OAAA,CAACJ,iBAAiB;UAAA+P,QAAA,EAAC;QAA6B;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAChBvQ,OAAA,CAACN,aAAa;QAAAiQ,QAAA,eACZ3P,OAAA,CAACF,MAAM;UAACqQ,OAAO,EAAE9D,wBAAyB;UAAC+C,KAAK,EAAC,SAAS;UAAAO,QAAA,EAAC;QAE3D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EACR1L,aAAa,iBACZ7E,OAAA;MAAK4P,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxC3P,OAAA;QAAK4P,SAAS,EAAC,2BAA2B;QAAAD,QAAA,gBACxC3P,OAAA;UAAK4P,SAAS,EAAC,+BAA+B;UAAAD,QAAA,eAC5C3P,OAAA,CAACT,OAAO;YACNiB,KAAK,EAAEuE,gBAAiB;YACxBI,IAAI,EAAEA,IAAK;YACXE,IAAI,EAAEA,IAAK;YACX8N,MAAM,EAAE,CAAC,GAAG,CAAE;YACdC,YAAY,EAAEhO,OAAQ;YACtBiO,YAAY,EAAE/N,OAAQ;YACtBgO,cAAc,EAAEA,CAACtJ,CAAC,EAAEuJ,IAAI,KAAKhO,oBAAoB,CAACgO,IAAI;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvQ,OAAA;UAAK4P,SAAS,EAAC,6BAA6B;UAAAD,QAAA,gBAC1C3P,OAAA;YAAQmQ,OAAO,EAAE3F,kBAAmB;YAAAmF,QAAA,EAAC;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxDvQ,OAAA;YAAQmQ,OAAO,EAAEA,CAAA,KAAMrL,gBAAgB,CAAC,KAAK,CAAE;YAAA6K,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAC3O,EAAA,CAtkDIH,aAAa;EAAA,QACEvC,SAAS;AAAA;AAAAsU,EAAA,GADxB/R,aAAa;AAwkDnB,eAAeA,aAAa;AAAC,IAAA+R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}