{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Products\\\\Productsgrid.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Grid, Box, Pagination } from \"@mui/material\";\nimport ProductCard from \"./productcard\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductCards = ({\n  products = [],\n  onToggleFavorite\n}) => {\n  _s();\n  const [currentPage, setCurrentPage] = useState(1);\n  const [favorites] = useState([]);\n  const productsPerPage = 12;\n  const safeProducts = Array.isArray(products) ? products : [];\n  const indexOfLastProduct = currentPage * productsPerPage;\n  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;\n  const currentProducts = safeProducts.slice(indexOfFirstProduct, indexOfLastProduct);\n  const totalPages = Math.ceil(safeProducts.length / productsPerPage);\n  const handlePageChange = (event, value) => {\n    setCurrentPage(value);\n    window.scrollTo(0, 0);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: \"100%\",\n      padding: {\n        xs: 2,\n        md: 3\n      }\n    },\n    children: safeProducts.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: \"center\",\n        marginTop: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"No products found in this category.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: currentProducts.map(product => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6 // 2 per row on small screens\n          ,\n          sm: 6 // 2 per row on tablets\n          ,\n          md: 4 // 3 per row on medium screens\n          ,\n          lg: 4 // 4 per row on large screens\n          ,\n          children: /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product,\n            onToggleFavorite: onToggleFavorite,\n            isFavorite: favorites.some(fav => fav._id === product._id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 17\n          }, this)\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          marginTop: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: totalPages,\n          page: currentPage,\n          onChange: handlePageChange,\n          color: \"primary\",\n          sx: {\n            \"& .MuiPaginationItem-root\": {\n              backgroundColor: \"#fff\",\n              boxShadow: \"0 2px 5px rgba(0, 0, 0, 0.1)\",\n              borderRadius: 2\n            },\n            \"& .Mui-selected\": {\n              backgroundColor: \"#6B7B58\",\n              color: \"#fff\"\n            },\n            \"& .MuiPaginationItem-root:hover\": {\n              backgroundColor: \"#f5f5f5\"\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductCards, \"5VsvuvaCE9oXr9hD3nSlRGIPkmA=\");\n_c = ProductCards;\nexport default ProductCards;\nvar _c;\n$RefreshReg$(_c, \"ProductCards\");", "map": {"version": 3, "names": ["React", "useState", "Grid", "Box", "Pagination", "ProductCard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductCards", "products", "onToggleFavorite", "_s", "currentPage", "setCurrentPage", "favorites", "productsPerPage", "safeProducts", "Array", "isArray", "indexOfLastProduct", "indexOfFirstProduct", "currentProducts", "slice", "totalPages", "Math", "ceil", "length", "handlePageChange", "event", "value", "window", "scrollTo", "sx", "width", "padding", "xs", "md", "children", "textAlign", "marginTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "product", "item", "sm", "lg", "isFavorite", "some", "fav", "_id", "display", "justifyContent", "count", "page", "onChange", "color", "backgroundColor", "boxShadow", "borderRadius", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Products/Productsgrid.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Grid, Box, Pagination } from \"@mui/material\";\r\nimport ProductCard from \"./productcard\";\r\n\r\nconst ProductCards = ({ products = [], onToggleFavorite }) => {\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [favorites] = useState([]);\r\n  const productsPerPage = 12;\r\n\r\n  const safeProducts = Array.isArray(products) ? products : [];\r\n  const indexOfLastProduct = currentPage * productsPerPage;\r\n  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;\r\n  const currentProducts = safeProducts.slice(\r\n    indexOfFirstProduct,\r\n    indexOfLastProduct\r\n  );\r\n  const totalPages = Math.ceil(safeProducts.length / productsPerPage);\r\n\r\n  const handlePageChange = (event, value) => {\r\n    setCurrentPage(value);\r\n    window.scrollTo(0, 0);\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ width: \"100%\", padding: { xs: 2, md: 3 } }}>\r\n      {safeProducts.length === 0 ? (\r\n        <Box sx={{ textAlign: \"center\", marginTop: 4 }}>\r\n          <h2>No products found in this category.</h2>\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          <Grid container spacing={2}>\r\n            {currentProducts.map((product) => (\r\n              <Grid\r\n                item\r\n                key={product._id}\r\n                xs={6} // 2 per row on small screens\r\n                sm={6} // 2 per row on tablets\r\n                md={4} // 3 per row on medium screens\r\n                lg={4} // 4 per row on large screens\r\n              >\r\n                <ProductCard\r\n                  product={product}\r\n                  onToggleFavorite={onToggleFavorite}\r\n                  isFavorite={favorites.some((fav) => fav._id === product._id)}\r\n                />\r\n              </Grid>\r\n            ))}\r\n          </Grid>\r\n\r\n          {totalPages > 1 && (\r\n            <Box\r\n              sx={{ display: \"flex\", justifyContent: \"center\", marginTop: 4 }}\r\n            >\r\n              <Pagination\r\n                count={totalPages}\r\n                page={currentPage}\r\n                onChange={handlePageChange}\r\n                color=\"primary\"\r\n                sx={{\r\n                  \"& .MuiPaginationItem-root\": {\r\n                    backgroundColor: \"#fff\",\r\n                    boxShadow: \"0 2px 5px rgba(0, 0, 0, 0.1)\",\r\n                    borderRadius: 2,\r\n                  },\r\n                  \"& .Mui-selected\": {\r\n                    backgroundColor: \"#6B7B58\",\r\n                    color: \"#fff\",\r\n                  },\r\n                  \"& .MuiPaginationItem-root:hover\": {\r\n                    backgroundColor: \"#f5f5f5\",\r\n                  },\r\n                }}\r\n              />\r\n            </Box>\r\n          )}\r\n        </>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ProductCards;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AACrD,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ,GAAG,EAAE;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACe,SAAS,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChC,MAAMgB,eAAe,GAAG,EAAE;EAE1B,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE;EAC5D,MAAMU,kBAAkB,GAAGP,WAAW,GAAGG,eAAe;EACxD,MAAMK,mBAAmB,GAAGD,kBAAkB,GAAGJ,eAAe;EAChE,MAAMM,eAAe,GAAGL,YAAY,CAACM,KAAK,CACxCF,mBAAmB,EACnBD,kBACF,CAAC;EACD,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACT,YAAY,CAACU,MAAM,GAAGX,eAAe,CAAC;EAEnE,MAAMY,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzChB,cAAc,CAACgB,KAAK,CAAC;IACrBC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC;EAED,oBACE1B,OAAA,CAACJ,GAAG;IAAC+B,EAAE,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE;IAAE,CAAE;IAAAC,QAAA,EACnDrB,YAAY,CAACU,MAAM,KAAK,CAAC,gBACxBrB,OAAA,CAACJ,GAAG;MAAC+B,EAAE,EAAE;QAAEM,SAAS,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAF,QAAA,eAC7ChC,OAAA;QAAAgC,QAAA,EAAI;MAAmC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,gBAENtC,OAAA,CAAAE,SAAA;MAAA8B,QAAA,gBACEhC,OAAA,CAACL,IAAI;QAAC4C,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,EACxBhB,eAAe,CAACyB,GAAG,CAAEC,OAAO,iBAC3B1C,OAAA,CAACL,IAAI;UACHgD,IAAI;UAEJb,EAAE,EAAE,CAAE,CAAC;UAAA;UACPc,EAAE,EAAE,CAAE,CAAC;UAAA;UACPb,EAAE,EAAE,CAAE,CAAC;UAAA;UACPc,EAAE,EAAE,CAAE,CAAC;UAAA;UAAAb,QAAA,eAEPhC,OAAA,CAACF,WAAW;YACV4C,OAAO,EAAEA,OAAQ;YACjBrC,gBAAgB,EAAEA,gBAAiB;YACnCyC,UAAU,EAAErC,SAAS,CAACsC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,GAAG,KAAKP,OAAO,CAACO,GAAG;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC,GAVGI,OAAO,CAACO,GAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWZ,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAENpB,UAAU,GAAG,CAAC,iBACblB,OAAA,CAACJ,GAAG;QACF+B,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEjB,SAAS,EAAE;QAAE,CAAE;QAAAF,QAAA,eAEhEhC,OAAA,CAACH,UAAU;UACTuD,KAAK,EAAElC,UAAW;UAClBmC,IAAI,EAAE9C,WAAY;UAClB+C,QAAQ,EAAEhC,gBAAiB;UAC3BiC,KAAK,EAAC,SAAS;UACf5B,EAAE,EAAE;YACF,2BAA2B,EAAE;cAC3B6B,eAAe,EAAE,MAAM;cACvBC,SAAS,EAAE,8BAA8B;cACzCC,YAAY,EAAE;YAChB,CAAC;YACD,iBAAiB,EAAE;cACjBF,eAAe,EAAE,SAAS;cAC1BD,KAAK,EAAE;YACT,CAAC;YACD,iCAAiC,EAAE;cACjCC,eAAe,EAAE;YACnB;UACF;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChC,EAAA,CA5EIH,YAAY;AAAAwD,EAAA,GAAZxD,YAAY;AA8ElB,eAAeA,YAAY;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}