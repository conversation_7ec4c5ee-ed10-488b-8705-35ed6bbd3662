{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Vendor-Profile\\\\CatalogCard.jsx\";\nimport React from \"react\";\nimport { Card, CardMedia, Typography, Box } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VendorCatalogCard = ({\n  title,\n  year,\n  image,\n  type,\n  pdf,\n  isMobile\n}) => {\n  // Function to handle clicking on the card to open the PDF\n  const handleClick = () => {\n    const pdfUrl = `${pdf}`;\n    window.open(pdfUrl, \"_blank\"); // Opens the PDF in a new tab\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"flex-start\",\n      width: isMobile ? \"85%\" : \"100%\",\n      // Smaller width on mobile\n      margin: isMobile ? \"0 auto\" : \"0\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        height: isMobile ? 320 : 400,\n        // Smaller height on mobile\n        display: \"flex\",\n        flexDirection: \"column\",\n        boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\",\n        borderRadius: 2,\n        overflow: \"hidden\",\n        position: \"relative\",\n        width: isMobile ? \"100%\" : \"100%\",\n        cursor: \"pointer\" // Make the card clickable\n      },\n      onClick: handleClick // Set the click event on the entire card\n      ,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"relative\",\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n          component: \"img\",\n          image: image // Full image path\n          ,\n          alt: `${title} Cover`,\n          sx: {\n            width: \"100%\",\n            height: \"100%\",\n            objectFit: \"cover\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"absolute\",\n            top: 8,\n            left: 8,\n            backgroundColor: \"rgba(0, 0, 0, 0.6)\",\n            color: \"#fff\",\n            padding: \"4px 8px\",\n            fontSize: 12,\n            borderRadius: 1,\n            fontFamily: \"Montserrat\"\n          },\n          children: type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        paddingTop: 1,\n        width: \"100%\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 600,\n          fontSize: isMobile ? 13 : 14,\n          marginBottom: \"4px\",\n          textAlign: \"left\",\n          fontFamily: \"Montserrat\"\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          fontSize: isMobile ? 11 : 12,\n          color: \"text.secondary\",\n          textAlign: \"left\",\n          fontFamily: \"Montserrat\"\n        },\n        children: year\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = VendorCatalogCard;\nexport default VendorCatalogCard;\nvar _c;\n$RefreshReg$(_c, \"VendorCatalogCard\");", "map": {"version": 3, "names": ["React", "Card", "CardMedia", "Typography", "Box", "jsxDEV", "_jsxDEV", "VendorCatalogCard", "title", "year", "image", "type", "pdf", "isMobile", "handleClick", "pdfUrl", "window", "open", "sx", "display", "flexDirection", "alignItems", "width", "margin", "children", "height", "boxShadow", "borderRadius", "overflow", "position", "cursor", "onClick", "flex", "component", "alt", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "backgroundColor", "color", "padding", "fontSize", "fontFamily", "paddingTop", "variant", "fontWeight", "marginBottom", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Vendor-Profile/CatalogCard.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card, CardMedia, Typography, Box } from \"@mui/material\";\r\n\r\nconst VendorCatalogCard = ({ title, year, image, type, pdf, isMobile }) => {\r\n  // Function to handle clicking on the card to open the PDF\r\n  const handleClick = () => {\r\n    const pdfUrl = `${pdf}`;\r\n    window.open(pdfUrl, \"_blank\"); // Opens the PDF in a new tab\r\n  };\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"flex-start\",\r\n        width: isMobile ? \"85%\" : \"100%\", // Smaller width on mobile\r\n        margin: isMobile ? \"0 auto\" : \"0\",\r\n      }}\r\n    >\r\n      {/* Card Container */}\r\n      <Card\r\n        sx={{\r\n          height: isMobile ? 320 : 400, // Smaller height on mobile\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n          boxShadow: \"0px 2px 8px rgba(0, 0, 0, 0.1)\",\r\n          borderRadius: 2,\r\n          overflow: \"hidden\",\r\n          position: \"relative\",\r\n          width: isMobile ? \"100%\" : \"100%\",\r\n          cursor: \"pointer\", // Make the card clickable\r\n        }}\r\n        onClick={handleClick} // Set the click event on the entire card\r\n      >\r\n        {/* Image Container */}\r\n        <Box sx={{ position: \"relative\", flex: 1 }}>\r\n          <CardMedia\r\n            component=\"img\"\r\n            image={image} // Full image path\r\n            alt={`${title} Cover`}\r\n            sx={{\r\n              width: \"100%\",\r\n              height: \"100%\",\r\n              objectFit: \"cover\",\r\n            }}\r\n          />\r\n          {/* Badge */}\r\n          <Box\r\n            sx={{\r\n              position: \"absolute\",\r\n              top: 8,\r\n              left: 8,\r\n              backgroundColor: \"rgba(0, 0, 0, 0.6)\",\r\n              color: \"#fff\",\r\n              padding: \"4px 8px\",\r\n              fontSize: 12,\r\n              borderRadius: 1,\r\n              fontFamily: \"Montserrat\",\r\n            }}\r\n          >\r\n            {type}\r\n          </Box>\r\n        </Box>\r\n      </Card>\r\n\r\n      {/* Title and Year Section */}\r\n      <Box\r\n        sx={{\r\n          paddingTop: 1,\r\n          width: \"100%\",\r\n        }}\r\n      >\r\n        <Typography\r\n          variant=\"subtitle1\"\r\n          sx={{\r\n            fontWeight: 600,\r\n            fontSize: isMobile ? 13 : 14,\r\n            marginBottom: \"4px\",\r\n            textAlign: \"left\",\r\n            fontFamily: \"Montserrat\",\r\n          }}\r\n        >\r\n          {title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            fontSize: isMobile ? 11 : 12,\r\n            color: \"text.secondary\",\r\n            textAlign: \"left\",\r\n            fontFamily: \"Montserrat\",\r\n          }}\r\n        >\r\n          {year}\r\n        </Typography>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default VendorCatalogCard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC,IAAI;EAAEC,GAAG;EAAEC;AAAS,CAAC,KAAK;EACzE;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,MAAM,GAAG,GAAGH,GAAG,EAAE;IACvBI,MAAM,CAACC,IAAI,CAACF,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;EACjC,CAAC;EAED,oBACET,OAAA,CAACF,GAAG;IACFc,EAAE,EAAE;MACFC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,YAAY;MACxBC,KAAK,EAAET,QAAQ,GAAG,KAAK,GAAG,MAAM;MAAE;MAClCU,MAAM,EAAEV,QAAQ,GAAG,QAAQ,GAAG;IAChC,CAAE;IAAAW,QAAA,gBAGFlB,OAAA,CAACL,IAAI;MACHiB,EAAE,EAAE;QACFO,MAAM,EAAEZ,QAAQ,GAAG,GAAG,GAAG,GAAG;QAAE;QAC9BM,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBM,SAAS,EAAE,gCAAgC;QAC3CC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,UAAU;QACpBP,KAAK,EAAET,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjCiB,MAAM,EAAE,SAAS,CAAE;MACrB,CAAE;MACFC,OAAO,EAAEjB,WAAY,CAAC;MAAA;MAAAU,QAAA,eAGtBlB,OAAA,CAACF,GAAG;QAACc,EAAE,EAAE;UAAEW,QAAQ,EAAE,UAAU;UAAEG,IAAI,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzClB,OAAA,CAACJ,SAAS;UACR+B,SAAS,EAAC,KAAK;UACfvB,KAAK,EAAEA,KAAM,CAAC;UAAA;UACdwB,GAAG,EAAE,GAAG1B,KAAK,QAAS;UACtBU,EAAE,EAAE;YACFI,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,MAAM;YACdU,SAAS,EAAE;UACb;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFjC,OAAA,CAACF,GAAG;UACFc,EAAE,EAAE;YACFW,QAAQ,EAAE,UAAU;YACpBW,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,eAAe,EAAE,oBAAoB;YACrCC,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE,SAAS;YAClBC,QAAQ,EAAE,EAAE;YACZlB,YAAY,EAAE,CAAC;YACfmB,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,EAEDb;QAAI;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPjC,OAAA,CAACF,GAAG;MACFc,EAAE,EAAE;QACF6B,UAAU,EAAE,CAAC;QACbzB,KAAK,EAAE;MACT,CAAE;MAAAE,QAAA,gBAEFlB,OAAA,CAACH,UAAU;QACT6C,OAAO,EAAC,WAAW;QACnB9B,EAAE,EAAE;UACF+B,UAAU,EAAE,GAAG;UACfJ,QAAQ,EAAEhC,QAAQ,GAAG,EAAE,GAAG,EAAE;UAC5BqC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAE,MAAM;UACjBL,UAAU,EAAE;QACd,CAAE;QAAAtB,QAAA,EAEDhB;MAAK;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbjC,OAAA,CAACH,UAAU;QACT6C,OAAO,EAAC,OAAO;QACf9B,EAAE,EAAE;UACF2B,QAAQ,EAAEhC,QAAQ,GAAG,EAAE,GAAG,EAAE;UAC5B8B,KAAK,EAAE,gBAAgB;UACvBQ,SAAS,EAAE,MAAM;UACjBL,UAAU,EAAE;QACd,CAAE;QAAAtB,QAAA,EAEDf;MAAI;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GAhGI7C,iBAAiB;AAkGvB,eAAeA,iBAAiB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}