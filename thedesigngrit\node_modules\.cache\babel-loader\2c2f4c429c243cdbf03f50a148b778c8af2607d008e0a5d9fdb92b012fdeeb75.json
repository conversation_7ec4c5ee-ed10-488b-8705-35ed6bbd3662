{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Vendor-Profile\\\\Hero.jsx\";\nimport React from \"react\";\nimport { Box } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VendorProfileHero({\n  vendor\n}) {\n  const fullImagePath = vendor.coverPhoto ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${vendor.coverPhoto}` // Full image path for rendering\n  : \"/Assets/placeHolderCover.png\"; // Default (static) image if no coverPhoto is available\n\n  // Log the image path for debugging\n  console.log(\"Full Image Path:\", fullImagePath);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: {\n        xs: \"70%\",\n        md: \"80%\",\n        lg: \"70%\"\n      },\n      maxWidth: \"1200px\",\n      height: {\n        xs: \"220px\",\n        sm: \"320px\",\n        md: \"400px\",\n        lg: \"450px\"\n      },\n      margin: \"32px auto 0 auto\",\n      background: \"#fff\",\n      borderRadius: \"24px\",\n      boxShadow: \"0 4px 24px rgba(0,0,0,0.10)\",\n      overflow: \"hidden\",\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      padding: {\n        xs: \"0\",\n        md: \"0\"\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(\"img\", {\n      src: fullImagePath,\n      alt: \"Vendor Hero\",\n      style: {\n        width: \"100%\",\n        height: \"100%\",\n        objectFit: \"cover\",\n        borderRadius: \"24px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = VendorProfileHero;\nexport default VendorProfileHero;\nvar _c;\n$RefreshReg$(_c, \"VendorProfileHero\");", "map": {"version": 3, "names": ["React", "Box", "jsxDEV", "_jsxDEV", "VendorProfileHero", "vendor", "fullImagePath", "coverPhoto", "console", "log", "sx", "width", "xs", "md", "lg", "max<PERSON><PERSON><PERSON>", "height", "sm", "margin", "background", "borderRadius", "boxShadow", "overflow", "display", "justifyContent", "alignItems", "padding", "children", "src", "alt", "style", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Vendor-Profile/Hero.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Box } from \"@mui/material\";\r\n\r\nfunction VendorProfileHero({ vendor }) {\r\n  const fullImagePath = vendor.coverPhoto\r\n    ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${vendor.coverPhoto}` // Full image path for rendering\r\n    : \"/Assets/placeHolderCover.png\"; // Default (static) image if no coverPhoto is available\r\n\r\n  // Log the image path for debugging\r\n  console.log(\"Full Image Path:\", fullImagePath);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        width: { xs: \"70%\", md: \"80%\", lg: \"70%\" },\r\n        maxWidth: \"1200px\",\r\n        height: { xs: \"220px\", sm: \"320px\", md: \"400px\", lg: \"450px\" },\r\n        margin: \"32px auto 0 auto\",\r\n        background: \"#fff\",\r\n        borderRadius: \"24px\",\r\n        boxShadow: \"0 4px 24px rgba(0,0,0,0.10)\",\r\n        overflow: \"hidden\",\r\n        display: \"flex\",\r\n        justifyContent: \"center\",\r\n        alignItems: \"center\",\r\n        padding: { xs: \"0\", md: \"0\" },\r\n      }}\r\n    >\r\n      <img\r\n        src={fullImagePath}\r\n        alt=\"Vendor Hero\"\r\n        style={{\r\n          width: \"100%\",\r\n          height: \"100%\",\r\n          objectFit: \"cover\",\r\n          borderRadius: \"24px\",\r\n        }}\r\n      />\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default VendorProfileHero;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,SAASC,iBAAiBA,CAAC;EAAEC;AAAO,CAAC,EAAE;EACrC,MAAMC,aAAa,GAAGD,MAAM,CAACE,UAAU,GACnC,uDAAuDF,MAAM,CAACE,UAAU,EAAE,CAAC;EAAA,EAC3E,8BAA8B,CAAC,CAAC;;EAEpC;EACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,aAAa,CAAC;EAE9C,oBACEH,OAAA,CAACF,GAAG;IACFS,EAAE,EAAE;MACFC,KAAK,EAAE;QAAEC,EAAE,EAAE,KAAK;QAAEC,EAAE,EAAE,KAAK;QAAEC,EAAE,EAAE;MAAM,CAAC;MAC1CC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE;QAAEJ,EAAE,EAAE,OAAO;QAAEK,EAAE,EAAE,OAAO;QAAEJ,EAAE,EAAE,OAAO;QAAEC,EAAE,EAAE;MAAQ,CAAC;MAC9DI,MAAM,EAAE,kBAAkB;MAC1BC,UAAU,EAAE,MAAM;MAClBC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,6BAA6B;MACxCC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE;QAAEd,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI;IAC9B,CAAE;IAAAc,QAAA,eAEFxB,OAAA;MACEyB,GAAG,EAAEtB,aAAc;MACnBuB,GAAG,EAAC,aAAa;MACjBC,KAAK,EAAE;QACLnB,KAAK,EAAE,MAAM;QACbK,MAAM,EAAE,MAAM;QACde,SAAS,EAAE,OAAO;QAClBX,YAAY,EAAE;MAChB;IAAE;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACC,EAAA,GArCQhC,iBAAiB;AAuC1B,eAAeA,iBAAiB;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}