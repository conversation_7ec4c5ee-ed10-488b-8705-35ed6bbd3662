{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\types.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, Suspense } from \"react\";\nimport { Box, Grid, Typography, Container, Alert, CircularProgress } from \"@mui/material\";\nimport { Link, useParams, useNavigate } from \"react-router-dom\";\nimport axios from \"axios\";\nimport Header from \"../Components/navBar\";\nimport LoadingScreen from \"./loadingScreen\";\nimport Footer from \"../Components/Footer\";\nimport PageDescription from \"../Components/Topheader\";\nimport { motion } from \"framer-motion\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TypesPage() {\n  _s();\n  const {\n    subCategoryId\n  } = useParams();\n  const [types, setTypes] = useState([]);\n  const [subcategory, setSubcategory] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [initialLoad, setInitialLoad] = useState(true);\n  const navigate = useNavigate();\n  useEffect(() => {\n    let isMounted = true;\n\n    // Function to fetch the subcategory details\n    const fetchSubcategory = async () => {\n      try {\n        const {\n          data\n        } = await axios.get(`https://api.thedesigngrit.com/api/subcategories/${subCategoryId}`);\n        if (isMounted) {\n          setSubcategory(data);\n        }\n      } catch (error) {\n        console.error(\"Error fetching subcategory:\", error);\n        if (isMounted) {\n          setError(\"Failed to load subcategory details. Please try again later.\");\n        }\n      }\n    };\n\n    // Function to fetch types for the subcategory\n    const fetchTypes = async () => {\n      try {\n        if (isMounted) {\n          setLoading(true);\n        }\n        const {\n          data\n        } = await axios.get(`https://api.thedesigngrit.com/api/types/subcategories/${subCategoryId}/types`);\n\n        // Filter types to only include those with status = true\n        const activeTypes = data.filter(type => type.status !== false);\n        if (isMounted) {\n          setTypes(activeTypes);\n          setLoading(false);\n          setInitialLoad(false);\n        }\n      } catch (error) {\n        console.error(\"Error fetching types:\", error);\n        if (isMounted) {\n          var _error$response, _error$response$data;\n          setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Error fetching types. Please try again later.\");\n          setLoading(false);\n          setInitialLoad(false);\n        }\n      }\n    };\n\n    // Only fetch if we have a valid subCategoryId\n    if (subCategoryId) {\n      // Use Promise.all to fetch both data in parallel\n      Promise.all([fetchSubcategory(), fetchTypes()]).catch(error => {\n        console.error(\"Error in Promise.all:\", error);\n        if (isMounted) {\n          setError(\"An error occurred while loading the page. Please try again.\");\n          setLoading(false);\n          setInitialLoad(false);\n        }\n      });\n    } else {\n      if (isMounted) {\n        setError(\"Invalid subcategory ID\");\n        setLoading(false);\n        setInitialLoad(false);\n      }\n    }\n\n    // Cleanup function to prevent memory leaks and state updates after unmount\n    return () => {\n      isMounted = false;\n    };\n  }, [subCategoryId]);\n\n  // Render loading screen during initial load\n  if (initialLoad) {\n    return /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Render a basic structure with loading indicator for subsequent loads\n  if (loading && !initialLoad) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: \"100vh\",\n        display: \"flex\",\n        flexDirection: \"column\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        sx: {\n          py: 4,\n          flexGrow: 1,\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: \"100vh\",\n        display: \"flex\",\n        flexDirection: \"column\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        sx: {\n          py: 4,\n          flexGrow: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            style: {\n              textDecoration: \"none\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"button\",\n              color: \"primary\",\n              children: \"Return to Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {\n        sx: {\n          marginTop: \"auto\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle case where subcategory is not found\n  if (!subcategory) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: \"100vh\",\n        display: \"flex\",\n        flexDirection: \"column\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"lg\",\n        sx: {\n          py: 4,\n          flexGrow: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          children: \"Subcategory not found. It may have been removed or is unavailable.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            style: {\n              textDecoration: \"none\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"button\",\n              color: \"primary\",\n              children: \"Return to Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {\n        sx: {\n          marginTop: \"auto\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: \"100vh\",\n      display: \"flex\",\n      flexDirection: \"column\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageDescription, {\n      name: subcategory.name,\n      description: subcategory.description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        flexGrow: 1,\n        py: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: {\n          xs: 2,\n          sm: 3,\n          md: 4\n        },\n        sx: {\n          display: \"flex\",\n          justifyContent: {\n            xs: \"center\",\n            md: \"flex-start\"\n          },\n          alignItems: \"stretch\"\n        },\n        children: types.length > 0 ? types.map(type => {\n          const typeName = type !== null && type !== void 0 && type.name ? encodeURIComponent(type.name.toLowerCase().replace(/\\s+/g, \"-\")) : \"undefined\";\n          const imageUrl = type !== null && type !== void 0 && type.image ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${type.image}` : `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/Assets/signin.jpeg`;\n          const displayName = (type === null || type === void 0 ? void 0 : type.name) || \"Undefined\";\n          const displayDescription = (type === null || type === void 0 ? void 0 : type.description) || \"\";\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            lg: 3,\n            sx: {\n              display: \"flex\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: motion.div,\n              whileHover: {\n                y: -4,\n                boxShadow: \"0 8px 24px 0 rgba(0,0,0,0.13)\",\n                transition: {\n                  duration: 0.18\n                }\n              },\n              onClick: () => navigate(`/products/${type._id}/${typeName}`),\n              sx: {\n                position: \"relative\",\n                width: \"100%\",\n                height: \"220px\",\n                borderRadius: \"12px\",\n                overflow: \"hidden\",\n                boxShadow: \"0 5px 15px rgba(0,0,0,0.1)\",\n                cursor: \"pointer\",\n                background: \"#fff\",\n                \"&::before\": {\n                  content: '\"\"',\n                  position: \"absolute\",\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  bottom: 0,\n                  background: \"linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 30%, rgba(0,0,0,0) 60%)\",\n                  zIndex: 1,\n                  transition: \"opacity 0.18s ease\",\n                  opacity: 0.7\n                },\n                \"&:hover::before\": {\n                  opacity: 0.9\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                component: motion.img,\n                whileHover: {\n                  scale: 1.035,\n                  transition: {\n                    duration: 0.18\n                  }\n                },\n                src: imageUrl,\n                alt: displayName,\n                loading: \"lazy\",\n                sx: {\n                  width: \"100%\",\n                  height: \"100%\",\n                  objectFit: \"cover\",\n                  transition: \"transform 0.18s ease\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: \"absolute\",\n                  bottom: 0,\n                  left: 0,\n                  right: 0,\n                  padding: \"16px\",\n                  zIndex: 2,\n                  display: \"flex\",\n                  flexDirection: \"column\",\n                  alignItems: \"flex-start\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    color: \"white\",\n                    fontFamily: \"Horizon\",\n                    fontWeight: \"bold\",\n                    marginBottom: \"4px\",\n                    fontSize: \"16px\",\n                    textShadow: \"0 2px 4px rgba(0,0,0,0.3)\",\n                    position: \"relative\",\n                    \"&::after\": {\n                      content: '\"\"',\n                      position: \"absolute\",\n                      bottom: -5,\n                      left: 0,\n                      width: \"30px\",\n                      height: \"2px\",\n                      backgroundColor: \"#6b7b58\",\n                      transition: \"width 0.18s ease\"\n                    },\n                    \"&:hover::after\": {\n                      width: \"100%\"\n                    }\n                  },\n                  children: displayName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    height: \"2px\",\n                    backgroundColor: \"white\",\n                    marginTop: \"auto\",\n                    transition: \"width 0.18s ease\",\n                    alignSelf: \"flex-start\",\n                    width: 0,\n                    ...((type === null || type === void 0 ? void 0 : type.name) && {\n                      \"&:hover\": {\n                        width: \"100%\"\n                      }\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 19\n            }, this)\n          }, type._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 17\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: \"center\",\n            py: 5,\n            width: \"100%\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"No types found for this subcategory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: \"Please check back later or explore other categories.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              style: {\n                textDecoration: \"none\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"button\",\n                color: \"primary\",\n                children: \"Return to Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n}\n\n// Create an error boundary component\n_s(TypesPage, \"XlP0qF7VAeCOOE73FF6tbauLlZY=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = TypesPage;\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"Error caught by boundary:\", error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: \"100vh\",\n          display: \"flex\",\n          flexDirection: \"column\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Container, {\n          maxWidth: \"lg\",\n          sx: {\n            py: 4,\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: \"Something went wrong. Please try refreshing the page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              style: {\n                textDecoration: \"none\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"button\",\n                color: \"primary\",\n                children: \"Return to Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Footer, {\n          sx: {\n            marginTop: \"auto\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// Wrap the TypesPage component with the ErrorBoundary\nexport default function TypesPageWithErrorBoundary() {\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(TypesPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 424,\n    columnNumber: 5\n  }, this);\n}\n_c2 = TypesPageWithErrorBoundary;\nvar _c, _c2;\n$RefreshReg$(_c, \"TypesPage\");\n$RefreshReg$(_c2, \"TypesPageWithErrorBoundary\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Suspense", "Box", "Grid", "Typography", "Container", "<PERSON><PERSON>", "CircularProgress", "Link", "useParams", "useNavigate", "axios", "Header", "LoadingScreen", "Footer", "PageDescription", "motion", "jsxDEV", "_jsxDEV", "TypesPage", "_s", "subCategoryId", "types", "setTypes", "subcategory", "setSubcategory", "loading", "setLoading", "error", "setError", "initialLoad", "setInitialLoad", "navigate", "isMounted", "fetchSubcategory", "data", "get", "console", "fetchTypes", "activeTypes", "filter", "type", "status", "_error$response", "_error$response$data", "response", "message", "Promise", "all", "catch", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "minHeight", "display", "flexDirection", "children", "max<PERSON><PERSON><PERSON>", "py", "flexGrow", "justifyContent", "alignItems", "severity", "mb", "mt", "to", "style", "textDecoration", "variant", "color", "marginTop", "name", "description", "container", "spacing", "xs", "sm", "md", "length", "map", "typeName", "encodeURIComponent", "toLowerCase", "replace", "imageUrl", "image", "displayName", "displayDescription", "item", "lg", "component", "div", "whileHover", "y", "boxShadow", "transition", "duration", "onClick", "_id", "position", "width", "height", "borderRadius", "overflow", "cursor", "background", "content", "top", "left", "right", "bottom", "zIndex", "opacity", "img", "scale", "src", "alt", "objectFit", "padding", "fontFamily", "fontWeight", "marginBottom", "fontSize", "textShadow", "backgroundColor", "alignSelf", "textAlign", "gutterBottom", "_c", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "render", "TypesPageWithErrorBoundary", "fallback", "_c2", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/types.jsx"], "sourcesContent": ["import React, { useState, useEffect, Suspense } from \"react\";\r\nimport {\r\n  Box,\r\n  Grid,\r\n  Typography,\r\n  Container,\r\n  Alert,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport { Link, useParams, useNavigate } from \"react-router-dom\";\r\nimport axios from \"axios\";\r\nimport Header from \"../Components/navBar\";\r\nimport LoadingScreen from \"./loadingScreen\";\r\nimport Footer from \"../Components/Footer\";\r\nimport PageDescription from \"../Components/Topheader\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nfunction TypesPage() {\r\n  const { subCategoryId } = useParams();\r\n  const [types, setTypes] = useState([]);\r\n  const [subcategory, setSubcategory] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [initialLoad, setInitialLoad] = useState(true);\r\n  const navigate = useNavigate();\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    // Function to fetch the subcategory details\r\n    const fetchSubcategory = async () => {\r\n      try {\r\n        const { data } = await axios.get(\r\n          `https://api.thedesigngrit.com/api/subcategories/${subCategoryId}`\r\n        );\r\n        if (isMounted) {\r\n          setSubcategory(data);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching subcategory:\", error);\r\n        if (isMounted) {\r\n          setError(\r\n            \"Failed to load subcategory details. Please try again later.\"\r\n          );\r\n        }\r\n      }\r\n    };\r\n\r\n    // Function to fetch types for the subcategory\r\n    const fetchTypes = async () => {\r\n      try {\r\n        if (isMounted) {\r\n          setLoading(true);\r\n        }\r\n        const { data } = await axios.get(\r\n          `https://api.thedesigngrit.com/api/types/subcategories/${subCategoryId}/types`\r\n        );\r\n\r\n        // Filter types to only include those with status = true\r\n        const activeTypes = data.filter((type) => type.status !== false);\r\n\r\n        if (isMounted) {\r\n          setTypes(activeTypes);\r\n          setLoading(false);\r\n          setInitialLoad(false);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching types:\", error);\r\n        if (isMounted) {\r\n          setError(\r\n            error.response?.data?.message ||\r\n              \"Error fetching types. Please try again later.\"\r\n          );\r\n          setLoading(false);\r\n          setInitialLoad(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Only fetch if we have a valid subCategoryId\r\n    if (subCategoryId) {\r\n      // Use Promise.all to fetch both data in parallel\r\n      Promise.all([fetchSubcategory(), fetchTypes()]).catch((error) => {\r\n        console.error(\"Error in Promise.all:\", error);\r\n        if (isMounted) {\r\n          setError(\r\n            \"An error occurred while loading the page. Please try again.\"\r\n          );\r\n          setLoading(false);\r\n          setInitialLoad(false);\r\n        }\r\n      });\r\n    } else {\r\n      if (isMounted) {\r\n        setError(\"Invalid subcategory ID\");\r\n        setLoading(false);\r\n        setInitialLoad(false);\r\n      }\r\n    }\r\n\r\n    // Cleanup function to prevent memory leaks and state updates after unmount\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [subCategoryId]);\r\n\r\n  // Render loading screen during initial load\r\n  if (initialLoad) {\r\n    return <LoadingScreen />;\r\n  }\r\n\r\n  // Render a basic structure with loading indicator for subsequent loads\r\n  if (loading && !initialLoad) {\r\n    return (\r\n      <Box\r\n        sx={{ minHeight: \"100vh\", display: \"flex\", flexDirection: \"column\" }}\r\n      >\r\n        <Header />\r\n        <Container\r\n          maxWidth=\"lg\"\r\n          sx={{\r\n            py: 4,\r\n            flexGrow: 1,\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <CircularProgress />\r\n        </Container>\r\n        <Footer />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Box\r\n        sx={{ minHeight: \"100vh\", display: \"flex\", flexDirection: \"column\" }}\r\n      >\r\n        <Header />\r\n        <Container maxWidth=\"lg\" sx={{ py: 4, flexGrow: 1 }}>\r\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\r\n            {error}\r\n          </Alert>\r\n          <Box sx={{ display: \"flex\", justifyContent: \"center\", mt: 2 }}>\r\n            <Link to=\"/\" style={{ textDecoration: \"none\" }}>\r\n              <Typography variant=\"button\" color=\"primary\">\r\n                Return to Home\r\n              </Typography>\r\n            </Link>\r\n          </Box>\r\n        </Container>\r\n        <Footer sx={{ marginTop: \"auto\" }} />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  // Handle case where subcategory is not found\r\n  if (!subcategory) {\r\n    return (\r\n      <Box\r\n        sx={{ minHeight: \"100vh\", display: \"flex\", flexDirection: \"column\" }}\r\n      >\r\n        <Header />\r\n        <Container maxWidth=\"lg\" sx={{ py: 4, flexGrow: 1 }}>\r\n          <Alert severity=\"warning\">\r\n            Subcategory not found. It may have been removed or is unavailable.\r\n          </Alert>\r\n          <Box sx={{ display: \"flex\", justifyContent: \"center\", mt: 2 }}>\r\n            <Link to=\"/\" style={{ textDecoration: \"none\" }}>\r\n              <Typography variant=\"button\" color=\"primary\">\r\n                Return to Home\r\n              </Typography>\r\n            </Link>\r\n          </Box>\r\n        </Container>\r\n        <Footer sx={{ marginTop: \"auto\" }} />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ minHeight: \"100vh\", display: \"flex\", flexDirection: \"column\" }}>\r\n      <Header />\r\n      <PageDescription\r\n        name={subcategory.name}\r\n        description={subcategory.description}\r\n      />\r\n\r\n      <Container maxWidth=\"xl\" sx={{ flexGrow: 1, py: 4 }}>\r\n        {/* Section Title and Description */}\r\n        <Grid\r\n          container\r\n          spacing={{ xs: 2, sm: 3, md: 4 }}\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: { xs: \"center\", md: \"flex-start\" },\r\n            alignItems: \"stretch\",\r\n          }}\r\n        >\r\n          {types.length > 0 ? (\r\n            types.map((type) => {\r\n              const typeName = type?.name\r\n                ? encodeURIComponent(\r\n                    type.name.toLowerCase().replace(/\\s+/g, \"-\")\r\n                  )\r\n                : \"undefined\";\r\n              const imageUrl = type?.image\r\n                ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${type.image}`\r\n                : `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/Assets/signin.jpeg`;\r\n              const displayName = type?.name || \"Undefined\";\r\n              const displayDescription = type?.description || \"\";\r\n\r\n              return (\r\n                <Grid\r\n                  item\r\n                  xs={12}\r\n                  sm={6}\r\n                  md={4}\r\n                  lg={3}\r\n                  key={type._id}\r\n                  sx={{ display: \"flex\" }}\r\n                >\r\n                  <Box\r\n                    component={motion.div}\r\n                    whileHover={{\r\n                      y: -4,\r\n                      boxShadow: \"0 8px 24px 0 rgba(0,0,0,0.13)\",\r\n                      transition: { duration: 0.18 },\r\n                    }}\r\n                    onClick={() =>\r\n                      navigate(`/products/${type._id}/${typeName}`)\r\n                    }\r\n                    sx={{\r\n                      position: \"relative\",\r\n                      width: \"100%\",\r\n                      height: \"220px\",\r\n                      borderRadius: \"12px\",\r\n                      overflow: \"hidden\",\r\n                      boxShadow: \"0 5px 15px rgba(0,0,0,0.1)\",\r\n                      cursor: \"pointer\",\r\n                      background: \"#fff\",\r\n                      \"&::before\": {\r\n                        content: '\"\"',\r\n                        position: \"absolute\",\r\n                        top: 0,\r\n                        left: 0,\r\n                        right: 0,\r\n                        bottom: 0,\r\n                        background:\r\n                          \"linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 30%, rgba(0,0,0,0) 60%)\",\r\n                        zIndex: 1,\r\n                        transition: \"opacity 0.18s ease\",\r\n                        opacity: 0.7,\r\n                      },\r\n                      \"&:hover::before\": {\r\n                        opacity: 0.9,\r\n                      },\r\n                    }}\r\n                  >\r\n                    <Box\r\n                      component={motion.img}\r\n                      whileHover={{\r\n                        scale: 1.035,\r\n                        transition: { duration: 0.18 },\r\n                      }}\r\n                      src={imageUrl}\r\n                      alt={displayName}\r\n                      loading=\"lazy\"\r\n                      sx={{\r\n                        width: \"100%\",\r\n                        height: \"100%\",\r\n                        objectFit: \"cover\",\r\n                        transition: \"transform 0.18s ease\",\r\n                      }}\r\n                    />\r\n                    <Box\r\n                      sx={{\r\n                        position: \"absolute\",\r\n                        bottom: 0,\r\n                        left: 0,\r\n                        right: 0,\r\n                        padding: \"16px\",\r\n                        zIndex: 2,\r\n                        display: \"flex\",\r\n                        flexDirection: \"column\",\r\n                        alignItems: \"flex-start\",\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"h6\"\r\n                        sx={{\r\n                          color: \"white\",\r\n                          fontFamily: \"Horizon\",\r\n                          fontWeight: \"bold\",\r\n                          marginBottom: \"4px\",\r\n                          fontSize: \"16px\",\r\n                          textShadow: \"0 2px 4px rgba(0,0,0,0.3)\",\r\n                          position: \"relative\",\r\n                          \"&::after\": {\r\n                            content: '\"\"',\r\n                            position: \"absolute\",\r\n                            bottom: -5,\r\n                            left: 0,\r\n                            width: \"30px\",\r\n                            height: \"2px\",\r\n                            backgroundColor: \"#6b7b58\",\r\n                            transition: \"width 0.18s ease\",\r\n                          },\r\n                          \"&:hover::after\": {\r\n                            width: \"100%\",\r\n                          },\r\n                        }}\r\n                      >\r\n                        {displayName}\r\n                      </Typography>\r\n                      {/* {displayDescription && (\r\n                        <Typography\r\n                          variant=\"body2\"\r\n                          sx={{\r\n                            color: \"rgba(255,255,255,0.8)\",\r\n                            fontFamily: \"Montserrat\",\r\n                            fontSize: \"12px\",\r\n                            maxWidth: \"90%\",\r\n                            overflow: \"hidden\",\r\n                            textOverflow: \"ellipsis\",\r\n                            display: \"-webkit-box\",\r\n                            WebkitLineClamp: 2,\r\n                            WebkitBoxOrient: \"vertical\",\r\n                          }}\r\n                        >\r\n                          {displayDescription}\r\n                        </Typography>\r\n                      )} */}\r\n                      <Box\r\n                        sx={{\r\n                          height: \"2px\",\r\n                          backgroundColor: \"white\",\r\n                          marginTop: \"auto\",\r\n                          transition: \"width 0.18s ease\",\r\n                          alignSelf: \"flex-start\",\r\n                          width: 0,\r\n                          ...(type?.name && {\r\n                            \"&:hover\": { width: \"100%\" },\r\n                          }),\r\n                        }}\r\n                      />\r\n                    </Box>\r\n                  </Box>\r\n                </Grid>\r\n              );\r\n            })\r\n          ) : (\r\n            <Box sx={{ textAlign: \"center\", py: 5, width: \"100%\" }}>\r\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\r\n                No types found for this subcategory.\r\n              </Typography>\r\n              <Typography variant=\"body1\" color=\"text.secondary\">\r\n                Please check back later or explore other categories.\r\n              </Typography>\r\n              <Box sx={{ mt: 3 }}>\r\n                <Link to=\"/\" style={{ textDecoration: \"none\" }}>\r\n                  <Typography variant=\"button\" color=\"primary\">\r\n                    Return to Home\r\n                  </Typography>\r\n                </Link>\r\n              </Box>\r\n            </Box>\r\n          )}\r\n        </Grid>\r\n      </Container>\r\n      <Footer />\r\n    </Box>\r\n  );\r\n}\r\n\r\n// Create an error boundary component\r\nclass ErrorBoundary extends React.Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = { hasError: false };\r\n  }\r\n\r\n  static getDerivedStateFromError(error) {\r\n    return { hasError: true };\r\n  }\r\n\r\n  componentDidCatch(error, errorInfo) {\r\n    console.error(\"Error caught by boundary:\", error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      return (\r\n        <Box\r\n          sx={{ minHeight: \"100vh\", display: \"flex\", flexDirection: \"column\" }}\r\n        >\r\n          <Header />\r\n          <Container maxWidth=\"lg\" sx={{ py: 4, flexGrow: 1 }}>\r\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\r\n              Something went wrong. Please try refreshing the page.\r\n            </Alert>\r\n            <Box sx={{ display: \"flex\", justifyContent: \"center\", mt: 2 }}>\r\n              <Link to=\"/\" style={{ textDecoration: \"none\" }}>\r\n                <Typography variant=\"button\" color=\"primary\">\r\n                  Return to Home\r\n                </Typography>\r\n              </Link>\r\n            </Box>\r\n          </Container>\r\n          <Footer sx={{ marginTop: \"auto\" }} />\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\n// Wrap the TypesPage component with the ErrorBoundary\r\nexport default function TypesPageWithErrorBoundary() {\r\n  return (\r\n    <ErrorBoundary>\r\n      <Suspense fallback={<LoadingScreen />}>\r\n        <TypesPage />\r\n      </Suspense>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,gBAAgB,QACX,eAAe;AACtB,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAc,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACrC,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMiC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9BV,SAAS,CAAC,MAAM;IACd,IAAIiC,SAAS,GAAG,IAAI;;IAEpB;IACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAM;UAAEC;QAAK,CAAC,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAC9B,mDAAmDf,aAAa,EAClE,CAAC;QACD,IAAIY,SAAS,EAAE;UACbR,cAAc,CAACU,IAAI,CAAC;QACtB;MACF,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdS,OAAO,CAACT,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAIK,SAAS,EAAE;UACbJ,QAAQ,CACN,6DACF,CAAC;QACH;MACF;IACF,CAAC;;IAED;IACA,MAAMS,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,IAAIL,SAAS,EAAE;UACbN,UAAU,CAAC,IAAI,CAAC;QAClB;QACA,MAAM;UAAEQ;QAAK,CAAC,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAC9B,yDAAyDf,aAAa,QACxE,CAAC;;QAED;QACA,MAAMkB,WAAW,GAAGJ,IAAI,CAACK,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,MAAM,KAAK,KAAK,CAAC;QAEhE,IAAIT,SAAS,EAAE;UACbV,QAAQ,CAACgB,WAAW,CAAC;UACrBZ,UAAU,CAAC,KAAK,CAAC;UACjBI,cAAc,CAAC,KAAK,CAAC;QACvB;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdS,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAIK,SAAS,EAAE;UAAA,IAAAU,eAAA,EAAAC,oBAAA;UACbf,QAAQ,CACN,EAAAc,eAAA,GAAAf,KAAK,CAACiB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBR,IAAI,cAAAS,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAC3B,+CACJ,CAAC;UACDnB,UAAU,CAAC,KAAK,CAAC;UACjBI,cAAc,CAAC,KAAK,CAAC;QACvB;MACF;IACF,CAAC;;IAED;IACA,IAAIV,aAAa,EAAE;MACjB;MACA0B,OAAO,CAACC,GAAG,CAAC,CAACd,gBAAgB,CAAC,CAAC,EAAEI,UAAU,CAAC,CAAC,CAAC,CAAC,CAACW,KAAK,CAAErB,KAAK,IAAK;QAC/DS,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAIK,SAAS,EAAE;UACbJ,QAAQ,CACN,6DACF,CAAC;UACDF,UAAU,CAAC,KAAK,CAAC;UACjBI,cAAc,CAAC,KAAK,CAAC;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIE,SAAS,EAAE;QACbJ,QAAQ,CAAC,wBAAwB,CAAC;QAClCF,UAAU,CAAC,KAAK,CAAC;QACjBI,cAAc,CAAC,KAAK,CAAC;MACvB;IACF;;IAEA;IACA,OAAO,MAAM;MACXE,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACZ,aAAa,CAAC,CAAC;;EAEnB;EACA,IAAIS,WAAW,EAAE;IACf,oBAAOZ,OAAA,CAACL,aAAa;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;;EAEA;EACA,IAAI3B,OAAO,IAAI,CAACI,WAAW,EAAE;IAC3B,oBACEZ,OAAA,CAAChB,GAAG;MACFoD,EAAE,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAErExC,OAAA,CAACN,MAAM;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVnC,OAAA,CAACb,SAAS;QACRsD,QAAQ,EAAC,IAAI;QACbL,EAAE,EAAE;UACFM,EAAE,EAAE,CAAC;UACLC,QAAQ,EAAE,CAAC;UACXL,OAAO,EAAE,MAAM;UACfM,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAL,QAAA,eAEFxC,OAAA,CAACX,gBAAgB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACZnC,OAAA,CAACJ,MAAM;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;;EAEA;EACA,IAAIzB,KAAK,EAAE;IACT,oBACEV,OAAA,CAAChB,GAAG;MACFoD,EAAE,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAErExC,OAAA,CAACN,MAAM;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVnC,OAAA,CAACb,SAAS;QAACsD,QAAQ,EAAC,IAAI;QAACL,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAClDxC,OAAA,CAACZ,KAAK;UAAC0D,QAAQ,EAAC,OAAO;UAACV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,EACnC9B;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACRnC,OAAA,CAAChB,GAAG;UAACoD,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEM,cAAc,EAAE,QAAQ;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAC5DxC,OAAA,CAACV,IAAI;YAAC2D,EAAE,EAAC,GAAG;YAACC,KAAK,EAAE;cAAEC,cAAc,EAAE;YAAO,CAAE;YAAAX,QAAA,eAC7CxC,OAAA,CAACd,UAAU;cAACkE,OAAO,EAAC,QAAQ;cAACC,KAAK,EAAC,SAAS;cAAAb,QAAA,EAAC;YAE7C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZnC,OAAA,CAACJ,MAAM;QAACwC,EAAE,EAAE;UAAEkB,SAAS,EAAE;QAAO;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC7B,WAAW,EAAE;IAChB,oBACEN,OAAA,CAAChB,GAAG;MACFoD,EAAE,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAErExC,OAAA,CAACN,MAAM;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVnC,OAAA,CAACb,SAAS;QAACsD,QAAQ,EAAC,IAAI;QAACL,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAClDxC,OAAA,CAACZ,KAAK;UAAC0D,QAAQ,EAAC,SAAS;UAAAN,QAAA,EAAC;QAE1B;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRnC,OAAA,CAAChB,GAAG;UAACoD,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEM,cAAc,EAAE,QAAQ;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAC5DxC,OAAA,CAACV,IAAI;YAAC2D,EAAE,EAAC,GAAG;YAACC,KAAK,EAAE;cAAEC,cAAc,EAAE;YAAO,CAAE;YAAAX,QAAA,eAC7CxC,OAAA,CAACd,UAAU;cAACkE,OAAO,EAAC,QAAQ;cAACC,KAAK,EAAC,SAAS;cAAAb,QAAA,EAAC;YAE7C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZnC,OAAA,CAACJ,MAAM;QAACwC,EAAE,EAAE;UAAEkB,SAAS,EAAE;QAAO;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAEV;EAEA,oBACEnC,OAAA,CAAChB,GAAG;IAACoD,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACxExC,OAAA,CAACN,MAAM;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVnC,OAAA,CAACH,eAAe;MACd0D,IAAI,EAAEjD,WAAW,CAACiD,IAAK;MACvBC,WAAW,EAAElD,WAAW,CAACkD;IAAY;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFnC,OAAA,CAACb,SAAS;MAACsD,QAAQ,EAAC,IAAI;MAACL,EAAE,EAAE;QAAEO,QAAQ,EAAE,CAAC;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAElDxC,OAAA,CAACf,IAAI;QACHwE,SAAS;QACTC,OAAO,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QACjCzB,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfM,cAAc,EAAE;YAAEe,EAAE,EAAE,QAAQ;YAAEE,EAAE,EAAE;UAAa,CAAC;UAClDhB,UAAU,EAAE;QACd,CAAE;QAAAL,QAAA,EAEDpC,KAAK,CAAC0D,MAAM,GAAG,CAAC,GACf1D,KAAK,CAAC2D,GAAG,CAAExC,IAAI,IAAK;UAClB,MAAMyC,QAAQ,GAAGzC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,IAAI,GACvBU,kBAAkB,CAChB1C,IAAI,CAACgC,IAAI,CAACW,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAC7C,CAAC,GACD,WAAW;UACf,MAAMC,QAAQ,GAAG7C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,KAAK,GACxB,uDAAuD9C,IAAI,CAAC8C,KAAK,EAAE,GACnE,wEAAwE;UAC5E,MAAMC,WAAW,GAAG,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,KAAI,WAAW;UAC7C,MAAMgB,kBAAkB,GAAG,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,WAAW,KAAI,EAAE;UAElD,oBACExD,OAAA,CAACf,IAAI;YACHuF,IAAI;YACJb,EAAE,EAAE,EAAG;YACPC,EAAE,EAAE,CAAE;YACNC,EAAE,EAAE,CAAE;YACNY,EAAE,EAAE,CAAE;YAENrC,EAAE,EAAE;cAAEE,OAAO,EAAE;YAAO,CAAE;YAAAE,QAAA,eAExBxC,OAAA,CAAChB,GAAG;cACF0F,SAAS,EAAE5E,MAAM,CAAC6E,GAAI;cACtBC,UAAU,EAAE;gBACVC,CAAC,EAAE,CAAC,CAAC;gBACLC,SAAS,EAAE,+BAA+B;gBAC1CC,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAK;cAC/B,CAAE;cACFC,OAAO,EAAEA,CAAA,KACPnE,QAAQ,CAAC,aAAaS,IAAI,CAAC2D,GAAG,IAAIlB,QAAQ,EAAE,CAC7C;cACD5B,EAAE,EAAE;gBACF+C,QAAQ,EAAE,UAAU;gBACpBC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,OAAO;gBACfC,YAAY,EAAE,MAAM;gBACpBC,QAAQ,EAAE,QAAQ;gBAClBT,SAAS,EAAE,4BAA4B;gBACvCU,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,MAAM;gBAClB,WAAW,EAAE;kBACXC,OAAO,EAAE,IAAI;kBACbP,QAAQ,EAAE,UAAU;kBACpBQ,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRC,MAAM,EAAE,CAAC;kBACTL,UAAU,EACR,qFAAqF;kBACvFM,MAAM,EAAE,CAAC;kBACThB,UAAU,EAAE,oBAAoB;kBAChCiB,OAAO,EAAE;gBACX,CAAC;gBACD,iBAAiB,EAAE;kBACjBA,OAAO,EAAE;gBACX;cACF,CAAE;cAAAxD,QAAA,gBAEFxC,OAAA,CAAChB,GAAG;gBACF0F,SAAS,EAAE5E,MAAM,CAACmG,GAAI;gBACtBrB,UAAU,EAAE;kBACVsB,KAAK,EAAE,KAAK;kBACZnB,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAK;gBAC/B,CAAE;gBACFmB,GAAG,EAAE/B,QAAS;gBACdgC,GAAG,EAAE9B,WAAY;gBACjB9D,OAAO,EAAC,MAAM;gBACd4B,EAAE,EAAE;kBACFgD,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdgB,SAAS,EAAE,OAAO;kBAClBtB,UAAU,EAAE;gBACd;cAAE;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFnC,OAAA,CAAChB,GAAG;gBACFoD,EAAE,EAAE;kBACF+C,QAAQ,EAAE,UAAU;kBACpBW,MAAM,EAAE,CAAC;kBACTF,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRS,OAAO,EAAE,MAAM;kBACfP,MAAM,EAAE,CAAC;kBACTzD,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,QAAQ;kBACvBM,UAAU,EAAE;gBACd,CAAE;gBAAAL,QAAA,gBAEFxC,OAAA,CAACd,UAAU;kBACTkE,OAAO,EAAC,IAAI;kBACZhB,EAAE,EAAE;oBACFiB,KAAK,EAAE,OAAO;oBACdkD,UAAU,EAAE,SAAS;oBACrBC,UAAU,EAAE,MAAM;oBAClBC,YAAY,EAAE,KAAK;oBACnBC,QAAQ,EAAE,MAAM;oBAChBC,UAAU,EAAE,2BAA2B;oBACvCxB,QAAQ,EAAE,UAAU;oBACpB,UAAU,EAAE;sBACVO,OAAO,EAAE,IAAI;sBACbP,QAAQ,EAAE,UAAU;sBACpBW,MAAM,EAAE,CAAC,CAAC;sBACVF,IAAI,EAAE,CAAC;sBACPR,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,KAAK;sBACbuB,eAAe,EAAE,SAAS;sBAC1B7B,UAAU,EAAE;oBACd,CAAC;oBACD,gBAAgB,EAAE;sBAChBK,KAAK,EAAE;oBACT;kBACF,CAAE;kBAAA5C,QAAA,EAED8B;gBAAW;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAmBbnC,OAAA,CAAChB,GAAG;kBACFoD,EAAE,EAAE;oBACFiD,MAAM,EAAE,KAAK;oBACbuB,eAAe,EAAE,OAAO;oBACxBtD,SAAS,EAAE,MAAM;oBACjByB,UAAU,EAAE,kBAAkB;oBAC9B8B,SAAS,EAAE,YAAY;oBACvBzB,KAAK,EAAE,CAAC;oBACR,IAAI,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,KAAI;sBAChB,SAAS,EAAE;wBAAE6B,KAAK,EAAE;sBAAO;oBAC7B,CAAC;kBACH;gBAAE;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAhIDZ,IAAI,CAAC2D,GAAG;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiIT,CAAC;QAEX,CAAC,CAAC,gBAEFnC,OAAA,CAAChB,GAAG;UAACoD,EAAE,EAAE;YAAE0E,SAAS,EAAE,QAAQ;YAAEpE,EAAE,EAAE,CAAC;YAAE0C,KAAK,EAAE;UAAO,CAAE;UAAA5C,QAAA,gBACrDxC,OAAA,CAACd,UAAU;YAACkE,OAAO,EAAC,IAAI;YAACC,KAAK,EAAC,gBAAgB;YAAC0D,YAAY;YAAAvE,QAAA,EAAC;UAE7D;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnC,OAAA,CAACd,UAAU;YAACkE,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAAAb,QAAA,EAAC;UAEnD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnC,OAAA,CAAChB,GAAG;YAACoD,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,eACjBxC,OAAA,CAACV,IAAI;cAAC2D,EAAE,EAAC,GAAG;cAACC,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO,CAAE;cAAAX,QAAA,eAC7CxC,OAAA,CAACd,UAAU;gBAACkE,OAAO,EAAC,QAAQ;gBAACC,KAAK,EAAC,SAAS;gBAAAb,QAAA,EAAC;cAE7C;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACZnC,OAAA,CAACJ,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;;AAEA;AAAAjC,EAAA,CAxWSD,SAAS;EAAA,QACUV,SAAS,EAMlBC,WAAW;AAAA;AAAAwH,EAAA,GAPrB/G,SAAS;AAyWlB,MAAMgH,aAAa,SAASrI,KAAK,CAACsI,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOC,wBAAwBA,CAAC7G,KAAK,EAAE;IACrC,OAAO;MAAE4G,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAE,iBAAiBA,CAAC9G,KAAK,EAAE+G,SAAS,EAAE;IAClCtG,OAAO,CAACT,KAAK,CAAC,2BAA2B,EAAEA,KAAK,EAAE+G,SAAS,CAAC;EAC9D;EAEAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACEtH,OAAA,CAAChB,GAAG;QACFoD,EAAE,EAAE;UAAEC,SAAS,EAAE,OAAO;UAAEC,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAErExC,OAAA,CAACN,MAAM;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVnC,OAAA,CAACb,SAAS;UAACsD,QAAQ,EAAC,IAAI;UAACL,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAClDxC,OAAA,CAACZ,KAAK;YAAC0D,QAAQ,EAAC,OAAO;YAACV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAEvC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnC,OAAA,CAAChB,GAAG;YAACoD,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEM,cAAc,EAAE,QAAQ;cAAEI,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,eAC5DxC,OAAA,CAACV,IAAI;cAAC2D,EAAE,EAAC,GAAG;cAACC,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO,CAAE;cAAAX,QAAA,eAC7CxC,OAAA,CAACd,UAAU;gBAACkE,OAAO,EAAC,QAAQ;gBAACC,KAAK,EAAC,SAAS;gBAAAb,QAAA,EAAC;cAE7C;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACZnC,OAAA,CAACJ,MAAM;UAACwC,EAAE,EAAE;YAAEkB,SAAS,EAAE;UAAO;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAEV;IAEA,OAAO,IAAI,CAACiF,KAAK,CAAC5E,QAAQ;EAC5B;AACF;;AAEA;AACA,eAAe,SAASmF,0BAA0BA,CAAA,EAAG;EACnD,oBACE3H,OAAA,CAACiH,aAAa;IAAAzE,QAAA,eACZxC,OAAA,CAACjB,QAAQ;MAAC6I,QAAQ,eAAE5H,OAAA,CAACL,aAAa;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAK,QAAA,eACpCxC,OAAA,CAACC,SAAS;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEpB;AAAC0F,GAAA,GARuBF,0BAA0B;AAAA,IAAAX,EAAA,EAAAa,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}