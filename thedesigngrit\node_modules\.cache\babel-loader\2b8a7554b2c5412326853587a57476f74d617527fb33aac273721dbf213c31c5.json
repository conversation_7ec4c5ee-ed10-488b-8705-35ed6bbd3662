{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\quotationsList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { useVendor } from \"../../utils/vendorContext\";\nimport { LuInfo } from \"react-icons/lu\";\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, IconButton, Box } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuotationsPage = () => {\n  _s();\n  var _selectedQuotation$pr, _selectedQuotation$pr2, _selectedQuotation$pr3, _selectedQuotation$pr4, _selectedQuotation$us, _selectedQuotation$us2, _selectedQuotation$us3, _selectedQuotation$us4, _selectedQuotation$pr5, _selectedQuotation$pr6, _selectedQuotation$pr7, _selectedQuotation$pr8, _selectedQuotation$pr9;\n  const {\n    vendor\n  } = useVendor();\n  const [quotations, setQuotations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedQuotation, setSelectedQuotation] = useState(null);\n  const [showProductInfo, setShowProductInfo] = useState(false);\n  const [note, setNote] = useState(\"\");\n  const [quotePrice, setQuotePrice] = useState(\"\");\n  const [file, setFile] = useState(null);\n  useEffect(() => {\n    const fetchQuotations = async () => {\n      try {\n        const response = await axios.get(`https://api.thedesigngrit.com/api/quotation/quotations/brand/${vendor.brandId}`);\n        setQuotations((response.data || []).slice().sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)));\n      } catch (err) {\n        console.error(\"Error fetching quotations:\", err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchQuotations();\n  }, [vendor.brandId]);\n  const handleVendorConfirm = async () => {\n    try {\n      const res = await axios.patch(`https://api.thedesigngrit.com/api/quotation/quotation/${selectedQuotation._id}/vendor-approval`);\n      console.log(\"Vendor approval response:\", res.data);\n      alert(\"Quotation approved by vendor!\");\n      handleClosePopup(); // optional: close modal\n      window.location.reload(); // or update state instead\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Vendor approval failed:\", error);\n      alert((error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Failed to approve quotation. Try again later.\");\n    }\n  };\n  const handleCardClick = quotation => {\n    setSelectedQuotation(quotation);\n    setNote(quotation.note || \"\");\n    setQuotePrice(quotation.quotePrice || \"\");\n    setFile(null);\n  };\n  const handleClosePopup = () => {\n    setSelectedQuotation(null);\n    setNote(\"\");\n    setQuotePrice(\"\");\n    setFile(null);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const formData = new FormData();\n    formData.append(\"note\", note);\n    formData.append(\"quotePrice\", quotePrice);\n    formData.append(\"dateOfQuotePrice\", new Date().toISOString());\n    if (file) formData.append(\"file\", file);\n    try {\n      const res = await axios.put(`https://api.thedesigngrit.com/api/quotation/update/${selectedQuotation._id}`, formData, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      console.log(\"Update response:\", res.data);\n      alert(\"Quotation updated successfully!\");\n      handleClosePopup();\n      window.location.reload();\n    } catch (error) {\n      console.error(\"Update failed:\", error);\n      alert(\"Failed to update quotation.\");\n    }\n  };\n  const handleDelete = async () => {\n    const confirmDelete = window.confirm(\"Are you sure you want to delete this quotation?\");\n    if (!confirmDelete) return;\n    try {\n      await axios.delete(`https://api.thedesigngrit.com/api/quotation/delete/${selectedQuotation._id}`);\n      alert(\"Quotation deleted successfully!\");\n      handleClosePopup();\n      window.location.reload();\n    } catch (error) {\n      console.error(\"Deletion failed:\", error);\n      alert(\"Failed to delete quotation.\");\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading quotations...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quotations-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header-title\",\n      style: {\n        marginBottom: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Quotations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          fontSize: \"12px\",\n          fontFamily: \"Montserrat\"\n        },\n        children: \"Home > Quotations Requests\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), quotations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-quotations\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No quotations yet!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quotations-list\",\n      children: quotations.map(quotation => {\n        var _quotation$productId, _quotation$productId2, _quotation$productId3, _quotation$productId4, _quotation$productId5, _quotation$userId;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quotation-card\",\n          onClick: () => handleCardClick(quotation),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: quotation !== null && quotation !== void 0 && (_quotation$productId = quotation.productId) !== null && _quotation$productId !== void 0 && _quotation$productId.mainImage ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${quotation === null || quotation === void 0 ? void 0 : (_quotation$productId2 = quotation.productId) === null || _quotation$productId2 === void 0 ? void 0 : _quotation$productId2.mainImage}` : \"/default-product-image.jpg\",\n            alt: (quotation === null || quotation === void 0 ? void 0 : (_quotation$productId3 = quotation.productId) === null || _quotation$productId3 === void 0 ? void 0 : _quotation$productId3.name) || \"Unnamed Product\",\n            className: \"quotation-card-img\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quotation-card-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: (quotation === null || quotation === void 0 ? void 0 : (_quotation$productId4 = quotation.productId) === null || _quotation$productId4 === void 0 ? void 0 : _quotation$productId4.name) || \"Unnamed Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"ID: \", (quotation === null || quotation === void 0 ? void 0 : (_quotation$productId5 = quotation.productId) === null || _quotation$productId5 === void 0 ? void 0 : _quotation$productId5._id) || \"No ID\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"User: \", (quotation === null || quotation === void 0 ? void 0 : (_quotation$userId = quotation.userId) === null || _quotation$userId === void 0 ? void 0 : _quotation$userId.firstName) || \"Unknown\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, quotation === null || quotation === void 0 ? void 0 : quotation._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }, this), selectedQuotation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quotation-popup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quotation-popup-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"close-btn\",\n          onClick: handleClosePopup,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"Quotation For:\", \" \", (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$pr = selectedQuotation.productId) === null || _selectedQuotation$pr === void 0 ? void 0 : _selectedQuotation$pr.name) || \"Unnamed Product\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: selectedQuotation !== null && selectedQuotation !== void 0 && (_selectedQuotation$pr2 = selectedQuotation.productId) !== null && _selectedQuotation$pr2 !== void 0 && _selectedQuotation$pr2.mainImage ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$pr3 = selectedQuotation.productId) === null || _selectedQuotation$pr3 === void 0 ? void 0 : _selectedQuotation$pr3.mainImage}` : \"/default-product-image.jpg\",\n            alt: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$pr4 = selectedQuotation.productId) === null || _selectedQuotation$pr4 === void 0 ? void 0 : _selectedQuotation$pr4.name) || \"Unnamed Product\",\n            className: \"quotation-popup-img\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"Product Info\",\n            onClick: () => setShowProductInfo(true),\n            sx: {\n              position: \"absolute\",\n              top: 8,\n              right: 8,\n              zIndex: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(LuInfo, {\n              color: \"white\",\n              size: 30\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"User:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), \" \", (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$us = selectedQuotation.userId) === null || _selectedQuotation$us === void 0 ? void 0 : _selectedQuotation$us.firstName) || \"Unknown\", \" \", (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$us2 = selectedQuotation.userId) === null || _selectedQuotation$us2 === void 0 ? void 0 : _selectedQuotation$us2.lastName) || \"User\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Email:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), \" \", (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$us3 = selectedQuotation.userId) === null || _selectedQuotation$us3 === void 0 ? void 0 : _selectedQuotation$us3.email) || \"No Email\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Phone:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), \" \", (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$us4 = selectedQuotation.userId) === null || _selectedQuotation$us4 === void 0 ? void 0 : _selectedQuotation$us4.phoneNumber) || \"No Phone Number\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Requested At:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), \" \", new Date(selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.createdAt).toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            marginTop: \"20px\",\n            marginBottom: \"10px\"\n          },\n          children: [\"Quotation Details:\", \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Requested Material:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), \" \", selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.material]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Requested size:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), \" \", selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.size]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Requested color:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), \" \", selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.color]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Requested customization:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), \" \", selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.customization]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            color: selectedQuotation !== null && selectedQuotation !== void 0 && selectedQuotation.ClientApproval ? \"#155724\" : \"#721c24\",\n            border: \"1px solid #d4edda\",\n            backgroundColor: selectedQuotation !== null && selectedQuotation !== void 0 && selectedQuotation.ClientApproval ? \"#def9bf\" : \"#f8d7da\",\n            borderRadius: \"5px\",\n            padding: \"5px\",\n            marginTop: \"15px\",\n            width: \"30%\",\n            textAlign: \"center\"\n          },\n          children: selectedQuotation !== null && selectedQuotation !== void 0 && selectedQuotation.ClientApproval ? \"Client Approved\" : \"Client Not Approved\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          style: {\n            marginTop: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: note,\n            onChange: e => setNote(e.target.value),\n            rows: 3,\n            style: {\n              backgroundColor: \"#fff\"\n            },\n            readOnly: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) === true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Quote Price:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: quotePrice,\n            onChange: e => setQuotePrice(e.target.value),\n            required: true,\n            readOnly: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) === true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Upload Quotation Invoice (optional):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            onChange: e => setFile(e.target.files[0]),\n            disabled: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) === true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              gap: \"10px\",\n              marginTop: \"15px\",\n              justifyContent: \"space-around\",\n              alignItems: \"baseline\"\n            },\n            children: [(selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.paymentDetails.paid) !== undefined && /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                backgroundColor: selectedQuotation.paymentDetails.paid ? \"#d4edda\" : \"#f8d7da\",\n                border: selectedQuotation.paymentDetails.paid ? \"1px solid #d4edda\" : \"1px solid #f5c6cb\",\n                color: selectedQuotation.paymentDetails.paid ? \"#155724\" : \"#721c24\",\n                padding: \"5px\",\n                borderRadius: \"5px\"\n              },\n              children: selectedQuotation.paymentDetails.paid ? \"Paid\" : \"Not Paid Yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this), (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginTop: \"15px\"\n              },\n              children: \"Deal is sealed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleDelete,\n              disabled: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.paymentDetails.paid) === true,\n              style: {\n                backgroundColor: \"#fff\",\n                color: \"#2d2d2d\",\n                border: \"1px solid red\",\n                padding: \"10px 15px\",\n                borderRadius: \"4px\",\n                cursor: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.paymentDetails.paid) === true ? \"not-allowed\" : \"pointer\",\n                opacity: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.paymentDetails.paid) === true ? 0.6 : 1,\n                \"&:hover\": {\n                  backgroundColor: \"red\",\n                  color: \"#fff\",\n                  transform: \"scale(1.1)\",\n                  boxShadow: \"0 0 10px rgba(0,0,0,0.2)\"\n                }\n              },\n              children: \"Delete Quotation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), !selectedQuotation.quotePrice && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              style: {\n                backgroundColor: \"#2d2d2d\",\n                color: \"#fff\",\n                \"&:hover\": {\n                  backgroundColor: \"#6b7b58\",\n                  transform: \"scale(1.1) !important\",\n                  boxShadow: \"0 0 10px rgba(0,0,0,0.2) !important\"\n                }\n              },\n              children: \"Submit Quotation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showProductInfo,\n      onClose: () => setShowProductInfo(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      sx: {\n        borderRadius: \"8px\",\n        height: \"80vh\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Product Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: selectedQuotation && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${selectedQuotation.productId.mainImage}`,\n            alt: selectedQuotation.productId.name,\n            style: {\n              width: \"100%\",\n              height: 200,\n              objectFit: \"cover\",\n              borderRadius: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            mt: 2,\n            children: selectedQuotation.productId.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Price: E\\xA3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), \" \", selectedQuotation.productId.salePrice ? /*#__PURE__*/_jsxDEV(\"del\", {\n              style: {\n                color: \"#a1a1a1\"\n              },\n              children: [selectedQuotation.productId.price.toLocaleString(), \"E\\xA3\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this) : selectedQuotation.productId.price.toLocaleString(), selectedQuotation.productId.salePrice && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"red\"\n              },\n              children: [\" \", selectedQuotation.productId.salePrice.toLocaleString(), \"E\\xA3\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" SKU:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this), \" \", selectedQuotation.productId.sku]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Collection:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), \" \", selectedQuotation.productId.collection]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Year:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this), \" \", selectedQuotation.productId.manufactureYear]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Colors:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), \" \", (_selectedQuotation$pr5 = selectedQuotation.productId.colors) === null || _selectedQuotation$pr5 === void 0 ? void 0 : _selectedQuotation$pr5.join(\", \")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Sizes: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), \" \", (_selectedQuotation$pr6 = selectedQuotation.productId.sizes) === null || _selectedQuotation$pr6 === void 0 ? void 0 : _selectedQuotation$pr6.join(\", \")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Dimensions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), (_selectedQuotation$pr7 = selectedQuotation.productId.technicalDimensions) === null || _selectedQuotation$pr7 === void 0 ? void 0 : _selectedQuotation$pr7.length, \" x\", \" \", (_selectedQuotation$pr8 = selectedQuotation.productId.technicalDimensions) === null || _selectedQuotation$pr8 === void 0 ? void 0 : _selectedQuotation$pr8.width, \" x\", \" \", (_selectedQuotation$pr9 = selectedQuotation.productId.technicalDimensions) === null || _selectedQuotation$pr9 === void 0 ? void 0 : _selectedQuotation$pr9.height, \" cm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          sx: {\n            backgroundColor: \"#2d2d2d\",\n            color: \"white\",\n            \"&:hover\": {\n              backgroundColor: \"#6b7b58\",\n              transform: \"scale(1.1)\",\n              boxShadow: \"0 0 10px rgba(0,0,0,0.2)\"\n            }\n          },\n          onClick: () => setShowProductInfo(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(QuotationsPage, \"Ox9/JeM8t0dq6iOuraID3VinQls=\", false, function () {\n  return [useVendor];\n});\n_c = QuotationsPage;\nexport default QuotationsPage;\nvar _c;\n$RefreshReg$(_c, \"QuotationsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useVendor", "LuInfo", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "IconButton", "Box", "jsxDEV", "_jsxDEV", "QuotationsPage", "_s", "_selectedQuotation$pr", "_selectedQuotation$pr2", "_selectedQuotation$pr3", "_selectedQuotation$pr4", "_selectedQuotation$us", "_selectedQuotation$us2", "_selectedQuotation$us3", "_selectedQuotation$us4", "_selectedQuotation$pr5", "_selectedQuotation$pr6", "_selectedQuotation$pr7", "_selectedQuotation$pr8", "_selectedQuotation$pr9", "vendor", "quotations", "setQuotations", "loading", "setLoading", "selectedQuotation", "setSelectedQuotation", "showProductInfo", "setShowProductInfo", "note", "setNote", "quotePrice", "setQuotePrice", "file", "setFile", "fetchQuotations", "response", "get", "brandId", "data", "slice", "sort", "a", "b", "Date", "createdAt", "err", "console", "error", "handleVendorConfirm", "res", "patch", "_id", "log", "alert", "handleClosePopup", "window", "location", "reload", "_error$response", "_error$response$data", "message", "handleCardClick", "quotation", "handleSubmit", "e", "preventDefault", "formData", "FormData", "append", "toISOString", "put", "headers", "handleDelete", "confirmDelete", "confirm", "delete", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "style", "marginBottom", "display", "alignItems", "gap", "fontSize", "fontFamily", "length", "map", "_quotation$productId", "_quotation$productId2", "_quotation$productId3", "_quotation$productId4", "_quotation$productId5", "_quotation$userId", "onClick", "src", "productId", "mainImage", "alt", "name", "userId", "firstName", "position", "sx", "top", "right", "zIndex", "color", "size", "lastName", "email", "phoneNumber", "toLocaleDateString", "marginTop", "material", "customization", "ClientApproval", "border", "backgroundColor", "borderRadius", "padding", "width", "textAlign", "onSubmit", "value", "onChange", "target", "rows", "readOnly", "type", "required", "files", "disabled", "justifyContent", "paymentDetails", "paid", "undefined", "cursor", "opacity", "transform", "boxShadow", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "height", "dividers", "objectFit", "variant", "mt", "salePrice", "price", "toLocaleString", "sku", "collection", "manufactureYear", "colors", "join", "sizes", "technicalDimensions", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/quotationsList.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\nimport { LuInfo } from \"react-icons/lu\";\r\nimport {\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Button,\r\n  Typography,\r\n  IconButton,\r\n  Box,\r\n} from \"@mui/material\";\r\nconst QuotationsPage = () => {\r\n  const { vendor } = useVendor();\r\n  const [quotations, setQuotations] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [selectedQuotation, setSelectedQuotation] = useState(null);\r\n  const [showProductInfo, setShowProductInfo] = useState(false);\r\n  const [note, setNote] = useState(\"\");\r\n  const [quotePrice, setQuotePrice] = useState(\"\");\r\n  const [file, setFile] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const fetchQuotations = async () => {\r\n      try {\r\n        const response = await axios.get(\r\n          `https://api.thedesigngrit.com/api/quotation/quotations/brand/${vendor.brandId}`\r\n        );\r\n        setQuotations(\r\n          (response.data || [])\r\n            .slice()\r\n            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))\r\n        );\r\n      } catch (err) {\r\n        console.error(\"Error fetching quotations:\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchQuotations();\r\n  }, [vendor.brandId]);\r\n  const handleVendorConfirm = async () => {\r\n    try {\r\n      const res = await axios.patch(\r\n        `https://api.thedesigngrit.com/api/quotation/quotation/${selectedQuotation._id}/vendor-approval`\r\n      );\r\n      console.log(\"Vendor approval response:\", res.data);\r\n      alert(\"Quotation approved by vendor!\");\r\n      handleClosePopup(); // optional: close modal\r\n      window.location.reload(); // or update state instead\r\n    } catch (error) {\r\n      console.error(\"Vendor approval failed:\", error);\r\n      alert(\r\n        error?.response?.data?.message ||\r\n          \"Failed to approve quotation. Try again later.\"\r\n      );\r\n    }\r\n  };\r\n\r\n  const handleCardClick = (quotation) => {\r\n    setSelectedQuotation(quotation);\r\n    setNote(quotation.note || \"\");\r\n    setQuotePrice(quotation.quotePrice || \"\");\r\n    setFile(null);\r\n  };\r\n\r\n  const handleClosePopup = () => {\r\n    setSelectedQuotation(null);\r\n    setNote(\"\");\r\n    setQuotePrice(\"\");\r\n    setFile(null);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"note\", note);\r\n    formData.append(\"quotePrice\", quotePrice);\r\n    formData.append(\"dateOfQuotePrice\", new Date().toISOString());\r\n    if (file) formData.append(\"file\", file);\r\n\r\n    try {\r\n      const res = await axios.put(\r\n        `https://api.thedesigngrit.com/api/quotation/update/${selectedQuotation._id}`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n          },\r\n        }\r\n      );\r\n      console.log(\"Update response:\", res.data);\r\n      alert(\"Quotation updated successfully!\");\r\n      handleClosePopup();\r\n      window.location.reload();\r\n    } catch (error) {\r\n      console.error(\"Update failed:\", error);\r\n      alert(\"Failed to update quotation.\");\r\n    }\r\n  };\r\n  const handleDelete = async () => {\r\n    const confirmDelete = window.confirm(\r\n      \"Are you sure you want to delete this quotation?\"\r\n    );\r\n    if (!confirmDelete) return;\r\n\r\n    try {\r\n      await axios.delete(\r\n        `https://api.thedesigngrit.com/api/quotation/delete/${selectedQuotation._id}`\r\n      );\r\n      alert(\"Quotation deleted successfully!\");\r\n      handleClosePopup();\r\n      window.location.reload();\r\n    } catch (error) {\r\n      console.error(\"Deletion failed:\", error);\r\n      alert(\"Failed to delete quotation.\");\r\n    }\r\n  };\r\n  if (loading) {\r\n    return <div>Loading quotations...</div>;\r\n  }\r\n\r\n  return (\r\n    <div className=\"quotations-page\">\r\n      <div className=\"dashboard-header-title\" style={{ marginBottom: \"20px\" }}>\r\n        <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}>\r\n          <h2>Quotations</h2>\r\n        </div>\r\n        <p style={{ fontSize: \"12px\", fontFamily: \"Montserrat\" }}>\r\n          Home &gt; Quotations Requests\r\n        </p>\r\n      </div>\r\n\r\n      {quotations.length === 0 ? (\r\n        <div className=\"no-quotations\">\r\n          <p>No quotations yet!</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"quotations-list\">\r\n          {quotations.map((quotation) => (\r\n            <div\r\n              key={quotation?._id}\r\n              className=\"quotation-card\"\r\n              onClick={() => handleCardClick(quotation)}\r\n            >\r\n              <img\r\n                src={\r\n                  quotation?.productId?.mainImage\r\n                    ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${quotation?.productId?.mainImage}`\r\n                    : \"/default-product-image.jpg\"\r\n                }\r\n                alt={quotation?.productId?.name || \"Unnamed Product\"}\r\n                className=\"quotation-card-img\"\r\n              />\r\n              <div className=\"quotation-card-info\">\r\n                <h2>{quotation?.productId?.name || \"Unnamed Product\"}</h2>\r\n                <p>ID: {quotation?.productId?._id || \"No ID\"}</p>\r\n                <p>User: {quotation?.userId?.firstName || \"Unknown\"}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {selectedQuotation && (\r\n        <div className=\"quotation-popup\">\r\n          <div className=\"quotation-popup-content\">\r\n            <span className=\"close-btn\" onClick={handleClosePopup}>\r\n              &times;\r\n            </span>\r\n            <h2>\r\n              Quotation For:{\" \"}\r\n              {selectedQuotation?.productId?.name || \"Unnamed Product\"}\r\n            </h2>\r\n            <div style={{ position: \"relative\" }}>\r\n              <img\r\n                src={\r\n                  selectedQuotation?.productId?.mainImage\r\n                    ? `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${selectedQuotation?.productId?.mainImage}`\r\n                    : \"/default-product-image.jpg\"\r\n                }\r\n                alt={selectedQuotation?.productId?.name || \"Unnamed Product\"}\r\n                className=\"quotation-popup-img\"\r\n              />\r\n              <IconButton\r\n                aria-label=\"Product Info\"\r\n                onClick={() => setShowProductInfo(true)}\r\n                sx={{\r\n                  position: \"absolute\",\r\n                  top: 8,\r\n                  right: 8,\r\n                  zIndex: 1,\r\n                }}\r\n              >\r\n                <LuInfo color=\"white\" size={30} />\r\n              </IconButton>\r\n            </div>\r\n            <p>\r\n              <strong>User:</strong>{\" \"}\r\n              {selectedQuotation?.userId?.firstName || \"Unknown\"}{\" \"}\r\n              {selectedQuotation?.userId?.lastName || \"User\"}\r\n            </p>\r\n            <p>\r\n              <strong>Email:</strong>{\" \"}\r\n              {selectedQuotation?.userId?.email || \"No Email\"}\r\n            </p>\r\n            <p>\r\n              <strong>Phone:</strong>{\" \"}\r\n              {selectedQuotation?.userId?.phoneNumber || \"No Phone Number\"}\r\n            </p>\r\n            <p>\r\n              <strong>Requested At:</strong>{\" \"}\r\n              {new Date(selectedQuotation?.createdAt).toLocaleDateString()}\r\n            </p>\r\n            <h3 style={{ marginTop: \"20px\", marginBottom: \"10px\" }}>\r\n              Quotation Details:{\" \"}\r\n            </h3>\r\n            <p>\r\n              <strong>Requested Material:</strong> {selectedQuotation?.material}\r\n            </p>\r\n            <p>\r\n              <strong>Requested size:</strong> {selectedQuotation?.size}\r\n            </p>\r\n            <p>\r\n              <strong>Requested color:</strong> {selectedQuotation?.color}\r\n            </p>\r\n            <p>\r\n              <strong>Requested customization:</strong>{\" \"}\r\n              {selectedQuotation?.customization}\r\n            </p>\r\n            <label\r\n              style={{\r\n                color: selectedQuotation?.ClientApproval\r\n                  ? \"#155724\"\r\n                  : \"#721c24\",\r\n                border: \"1px solid #d4edda\",\r\n                backgroundColor: selectedQuotation?.ClientApproval\r\n                  ? \"#def9bf\"\r\n                  : \"#f8d7da\",\r\n                borderRadius: \"5px\",\r\n                padding: \"5px\",\r\n                marginTop: \"15px\",\r\n                width: \"30%\",\r\n                textAlign: \"center\",\r\n              }}\r\n            >\r\n              {selectedQuotation?.ClientApproval\r\n                ? \"Client Approved\"\r\n                : \"Client Not Approved\"}\r\n            </label>\r\n            <form onSubmit={handleSubmit} style={{ marginTop: \"20px\" }}>\r\n              <label>Note:</label>\r\n              <textarea\r\n                value={note}\r\n                onChange={(e) => setNote(e.target.value)}\r\n                rows={3}\r\n                style={{ backgroundColor: \"#fff\" }}\r\n                readOnly={selectedQuotation?.ClientApproval === true}\r\n              />\r\n\r\n              <label>Quote Price:</label>\r\n              <input\r\n                type=\"number\"\r\n                value={quotePrice}\r\n                onChange={(e) => setQuotePrice(e.target.value)}\r\n                required\r\n                readOnly={selectedQuotation?.ClientApproval === true}\r\n              />\r\n\r\n              <label>Upload Quotation Invoice (optional):</label>\r\n              <input\r\n                type=\"file\"\r\n                onChange={(e) => setFile(e.target.files[0])}\r\n                disabled={selectedQuotation?.ClientApproval === true}\r\n              />\r\n\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  gap: \"10px\",\r\n                  marginTop: \"15px\",\r\n                  justifyContent: \"space-around\",\r\n                  alignItems: \"baseline\",\r\n                }}\r\n              >\r\n                {/* {selectedQuotation?.paymentDetails.paid === true && (\r\n                  <label\r\n                    style={{\r\n                      backgroundColor: \"#d4edda\",\r\n                      border: \"1px solid #d4edda\",\r\n                      color: \"#155724\",\r\n                      padding: \"5px\",\r\n                      borderRadius: \"5px\",\r\n                    }}\r\n                  >\r\n                    Paid\r\n                  </label>\r\n                )}\r\n                {selectedQuotation?.paymentDetails.paid === false && (\r\n                  <label\r\n                    style={{\r\n                      backgroundColor: \"#f8d7da\",\r\n                      border: \"1px solid #f5c6cb\",\r\n                      color: \"#721c24\",\r\n                      padding: \"5px\",\r\n                      borderRadius: \"5px\",\r\n                    }}\r\n                  >\r\n                    Not Paid Yet\r\n                  </label>\r\n                )} */}\r\n                {selectedQuotation?.paymentDetails.paid !== undefined && (\r\n                  <label\r\n                    style={{\r\n                      backgroundColor: selectedQuotation.paymentDetails.paid\r\n                        ? \"#d4edda\"\r\n                        : \"#f8d7da\",\r\n                      border: selectedQuotation.paymentDetails.paid\r\n                        ? \"1px solid #d4edda\"\r\n                        : \"1px solid #f5c6cb\",\r\n                      color: selectedQuotation.paymentDetails.paid\r\n                        ? \"#155724\"\r\n                        : \"#721c24\",\r\n                      padding: \"5px\",\r\n                      borderRadius: \"5px\",\r\n                    }}\r\n                  >\r\n                    {selectedQuotation.paymentDetails.paid\r\n                      ? \"Paid\"\r\n                      : \"Not Paid Yet\"}\r\n                  </label>\r\n                )}\r\n                {selectedQuotation?.ClientApproval && (\r\n                  <p style={{ marginTop: \"15px\" }}>Deal is sealed</p>\r\n                )}\r\n\r\n                {/* // ) : (\r\n                //   <button\r\n                //     onClick={handleVendorConfirm}\r\n                //     disabled={!selectedQuotation?.ClientApproval}\r\n                //     style={{\r\n                //       backgroundColor: selectedQuotation?.ClientApproval\r\n                //         ? \"#1e7e34\"\r\n                //         : \"#ccc\",\r\n                //       color: \"#fff\",\r\n                //       padding: \"10px 15px\",\r\n                //       borderRadius: \"4px\",\r\n                //       border: \"none\",\r\n                //       cursor: selectedQuotation?.ClientApproval\r\n                //         ? \"pointer\"\r\n                //         : \"not-allowed\",\r\n                //       marginTop: \"15px\",\r\n                //     }}\r\n                //   >\r\n                //     Confirm\r\n                //   </button>\r\n                // )\r\n                 */}\r\n\r\n                <button\r\n                  onClick={handleDelete}\r\n                  disabled={selectedQuotation?.paymentDetails.paid === true}\r\n                  style={{\r\n                    backgroundColor: \"#fff\",\r\n                    color: \"#2d2d2d\",\r\n                    border: \"1px solid red\",\r\n                    padding: \"10px 15px\",\r\n                    borderRadius: \"4px\",\r\n                    cursor:\r\n                      selectedQuotation?.paymentDetails.paid === true\r\n                        ? \"not-allowed\"\r\n                        : \"pointer\",\r\n                    opacity:\r\n                      selectedQuotation?.paymentDetails.paid === true ? 0.6 : 1,\r\n                    \"&:hover\": {\r\n                      backgroundColor: \"red\",\r\n                      color: \"#fff\",\r\n                      transform: \"scale(1.1)\",\r\n                      boxShadow: \"0 0 10px rgba(0,0,0,0.2)\",\r\n                    },\r\n                  }}\r\n                >\r\n                  Delete Quotation\r\n                </button>\r\n                {!selectedQuotation.quotePrice && (\r\n                  <button\r\n                    type=\"submit\"\r\n                    style={{\r\n                      backgroundColor: \"#2d2d2d\",\r\n                      color: \"#fff\",\r\n                      \"&:hover\": {\r\n                        backgroundColor: \"#6b7b58\",\r\n                        transform: \"scale(1.1) !important\",\r\n                        boxShadow: \"0 0 10px rgba(0,0,0,0.2) !important\",\r\n                      },\r\n                    }}\r\n                  >\r\n                    Submit Quotation\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n      <Dialog\r\n        open={showProductInfo}\r\n        onClose={() => setShowProductInfo(false)}\r\n        maxWidth=\"sm\"\r\n        fullWidth\r\n        sx={{\r\n          borderRadius: \"8px\",\r\n          height: \"80vh\",\r\n        }}\r\n      >\r\n        <DialogTitle>Product Information</DialogTitle>\r\n        <DialogContent dividers>\r\n          {selectedQuotation && (\r\n            <Box>\r\n              <img\r\n                src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${selectedQuotation.productId.mainImage}`}\r\n                alt={selectedQuotation.productId.name}\r\n                style={{\r\n                  width: \"100%\",\r\n                  height: 200,\r\n                  objectFit: \"cover\",\r\n                  borderRadius: 8,\r\n                }}\r\n              />\r\n              <Typography variant=\"h6\" mt={2}>\r\n                {selectedQuotation.productId.name}\r\n              </Typography>\r\n              <Typography>\r\n                <strong> Price: E£</strong>{\" \"}\r\n                {selectedQuotation.productId.salePrice ? (\r\n                  <del style={{ color: \"#a1a1a1\" }}>\r\n                    {selectedQuotation.productId.price.toLocaleString()}E£\r\n                  </del>\r\n                ) : (\r\n                  selectedQuotation.productId.price.toLocaleString()\r\n                )}\r\n                {selectedQuotation.productId.salePrice && (\r\n                  <span style={{ color: \"red\" }}>\r\n                    {\" \"}\r\n                    {selectedQuotation.productId.salePrice.toLocaleString()}E£\r\n                  </span>\r\n                )}\r\n              </Typography>\r\n              <Typography>\r\n                {\" \"}\r\n                <strong> SKU:</strong> {selectedQuotation.productId.sku}\r\n              </Typography>\r\n              {/* <Typography>\r\n                <strong> Manufacturer:</strong>{\" \"}\r\n                {selectedQuotation.productId.manufacturer}\r\n              </Typography> */}\r\n              <Typography>\r\n                <strong> Collection:</strong>{\" \"}\r\n                {selectedQuotation.productId.collection}\r\n              </Typography>\r\n              <Typography>\r\n                <strong> Year:</strong>{\" \"}\r\n                {selectedQuotation.productId.manufactureYear}\r\n              </Typography>\r\n              <Typography>\r\n                <strong> Colors:</strong>{\" \"}\r\n                {selectedQuotation.productId.colors?.join(\", \")}\r\n              </Typography>\r\n              <Typography>\r\n                <strong> Sizes: </strong>{\" \"}\r\n                {selectedQuotation.productId.sizes?.join(\", \")}\r\n              </Typography>\r\n              <Typography>\r\n                <strong> Dimensions:</strong>\r\n                {selectedQuotation.productId.technicalDimensions?.length} x{\" \"}\r\n                {selectedQuotation.productId.technicalDimensions?.width} x{\" \"}\r\n                {selectedQuotation.productId.technicalDimensions?.height} cm\r\n              </Typography>\r\n            </Box>\r\n          )}\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button\r\n            sx={{\r\n              backgroundColor: \"#2d2d2d\",\r\n              color: \"white\",\r\n              \"&:hover\": {\r\n                backgroundColor: \"#6b7b58\",\r\n                transform: \"scale(1.1)\",\r\n                boxShadow: \"0 0 10px rgba(0,0,0,0.2)\",\r\n              },\r\n            }}\r\n            onClick={() => setShowProductInfo(false)}\r\n          >\r\n            Close\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuotationsPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,GAAG,QACE,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACvB,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC3B,MAAM;IAAEC;EAAO,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC9B,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2C,IAAI,EAAEC,OAAO,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,MAAM4C,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,GAAG,CAC9B,gEAAgEjB,MAAM,CAACkB,OAAO,EAChF,CAAC;QACDhB,aAAa,CACX,CAACc,QAAQ,CAACG,IAAI,IAAI,EAAE,EACjBC,KAAK,CAAC,CAAC,CACPC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CACjE,CAAC;MACH,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAClD,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDW,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACf,MAAM,CAACkB,OAAO,CAAC,CAAC;EACpB,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,GAAG,GAAG,MAAM1D,KAAK,CAAC2D,KAAK,CAC3B,yDAAyD1B,iBAAiB,CAAC2B,GAAG,kBAChF,CAAC;MACDL,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAEH,GAAG,CAACX,IAAI,CAAC;MAClDe,KAAK,CAAC,+BAA+B,CAAC;MACtCC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACpBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACdb,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CM,KAAK,CACH,CAAAN,KAAK,aAALA,KAAK,wBAAAW,eAAA,GAALX,KAAK,CAAEZ,QAAQ,cAAAuB,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBpB,IAAI,cAAAqB,oBAAA,uBAArBA,oBAAA,CAAuBC,OAAO,KAC5B,+CACJ,CAAC;IACH;EACF,CAAC;EAED,MAAMC,eAAe,GAAIC,SAAS,IAAK;IACrCrC,oBAAoB,CAACqC,SAAS,CAAC;IAC/BjC,OAAO,CAACiC,SAAS,CAAClC,IAAI,IAAI,EAAE,CAAC;IAC7BG,aAAa,CAAC+B,SAAS,CAAChC,UAAU,IAAI,EAAE,CAAC;IACzCG,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMqB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B7B,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,OAAO,CAAC,EAAE,CAAC;IACXE,aAAa,CAAC,EAAE,CAAC;IACjBE,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAM8B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExC,IAAI,CAAC;IAC7BsC,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEtC,UAAU,CAAC;IACzCoC,QAAQ,CAACE,MAAM,CAAC,kBAAkB,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAAC0B,WAAW,CAAC,CAAC,CAAC;IAC7D,IAAIrC,IAAI,EAAEkC,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpC,IAAI,CAAC;IAEvC,IAAI;MACF,MAAMiB,GAAG,GAAG,MAAM1D,KAAK,CAAC+E,GAAG,CACzB,sDAAsD9C,iBAAiB,CAAC2B,GAAG,EAAE,EAC7Ee,QAAQ,EACR;QACEK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MACDzB,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEH,GAAG,CAACX,IAAI,CAAC;MACzCe,KAAK,CAAC,iCAAiC,CAAC;MACxCC,gBAAgB,CAAC,CAAC;MAClBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCM,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EACD,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,aAAa,GAAGlB,MAAM,CAACmB,OAAO,CAClC,iDACF,CAAC;IACD,IAAI,CAACD,aAAa,EAAE;IAEpB,IAAI;MACF,MAAMlF,KAAK,CAACoF,MAAM,CAChB,sDAAsDnD,iBAAiB,CAAC2B,GAAG,EAC7E,CAAC;MACDE,KAAK,CAAC,iCAAiC,CAAC;MACxCC,gBAAgB,CAAC,CAAC;MAClBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCM,KAAK,CAAC,6BAA6B,CAAC;IACtC;EACF,CAAC;EACD,IAAI/B,OAAO,EAAE;IACX,oBAAOnB,OAAA;MAAAyE,QAAA,EAAK;IAAqB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzC;EAEA,oBACE7E,OAAA;IAAK8E,SAAS,EAAC,iBAAiB;IAAAL,QAAA,gBAC9BzE,OAAA;MAAK8E,SAAS,EAAC,wBAAwB;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,gBACtEzE,OAAA;QAAK+E,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAV,QAAA,eACjEzE,OAAA;UAAAyE,QAAA,EAAI;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACN7E,OAAA;QAAG+E,KAAK,EAAE;UAAEK,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAa,CAAE;QAAAZ,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEL5D,UAAU,CAACqE,MAAM,KAAK,CAAC,gBACtBtF,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC5BzE,OAAA;QAAAyE,QAAA,EAAG;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,gBAEN7E,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAL,QAAA,EAC7BxD,UAAU,CAACsE,GAAG,CAAE5B,SAAS;QAAA,IAAA6B,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,iBAAA;QAAA,oBACxB7F,OAAA;UAEE8E,SAAS,EAAC,gBAAgB;UAC1BgB,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAACC,SAAS,CAAE;UAAAc,QAAA,gBAE1CzE,OAAA;YACE+F,GAAG,EACDpC,SAAS,aAATA,SAAS,gBAAA6B,oBAAA,GAAT7B,SAAS,CAAEqC,SAAS,cAAAR,oBAAA,eAApBA,oBAAA,CAAsBS,SAAS,GAC3B,uDAAuDtC,SAAS,aAATA,SAAS,wBAAA8B,qBAAA,GAAT9B,SAAS,CAAEqC,SAAS,cAAAP,qBAAA,uBAApBA,qBAAA,CAAsBQ,SAAS,EAAE,GACxF,4BACL;YACDC,GAAG,EAAE,CAAAvC,SAAS,aAATA,SAAS,wBAAA+B,qBAAA,GAAT/B,SAAS,CAAEqC,SAAS,cAAAN,qBAAA,uBAApBA,qBAAA,CAAsBS,IAAI,KAAI,iBAAkB;YACrDrB,SAAS,EAAC;UAAoB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACF7E,OAAA;YAAK8E,SAAS,EAAC,qBAAqB;YAAAL,QAAA,gBAClCzE,OAAA;cAAAyE,QAAA,EAAK,CAAAd,SAAS,aAATA,SAAS,wBAAAgC,qBAAA,GAAThC,SAAS,CAAEqC,SAAS,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBQ,IAAI,KAAI;YAAiB;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1D7E,OAAA;cAAAyE,QAAA,GAAG,MAAI,EAAC,CAAAd,SAAS,aAATA,SAAS,wBAAAiC,qBAAA,GAATjC,SAAS,CAAEqC,SAAS,cAAAJ,qBAAA,uBAApBA,qBAAA,CAAsB5C,GAAG,KAAI,OAAO;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD7E,OAAA;cAAAyE,QAAA,GAAG,QAAM,EAAC,CAAAd,SAAS,aAATA,SAAS,wBAAAkC,iBAAA,GAATlC,SAAS,CAAEyC,MAAM,cAAAP,iBAAA,uBAAjBA,iBAAA,CAAmBQ,SAAS,KAAI,SAAS;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA,GAjBDlB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEX,GAAG;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBhB,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAEAxD,iBAAiB,iBAChBrB,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAL,QAAA,eAC9BzE,OAAA;QAAK8E,SAAS,EAAC,yBAAyB;QAAAL,QAAA,gBACtCzE,OAAA;UAAM8E,SAAS,EAAC,WAAW;UAACgB,OAAO,EAAE3C,gBAAiB;UAAAsB,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP7E,OAAA;UAAAyE,QAAA,GAAI,gBACY,EAAC,GAAG,EACjB,CAAApD,iBAAiB,aAAjBA,iBAAiB,wBAAAlB,qBAAA,GAAjBkB,iBAAiB,CAAE2E,SAAS,cAAA7F,qBAAA,uBAA5BA,qBAAA,CAA8BgG,IAAI,KAAI,iBAAiB;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACL7E,OAAA;UAAK+E,KAAK,EAAE;YAAEuB,QAAQ,EAAE;UAAW,CAAE;UAAA7B,QAAA,gBACnCzE,OAAA;YACE+F,GAAG,EACD1E,iBAAiB,aAAjBA,iBAAiB,gBAAAjB,sBAAA,GAAjBiB,iBAAiB,CAAE2E,SAAS,cAAA5F,sBAAA,eAA5BA,sBAAA,CAA8B6F,SAAS,GACnC,uDAAuD5E,iBAAiB,aAAjBA,iBAAiB,wBAAAhB,sBAAA,GAAjBgB,iBAAiB,CAAE2E,SAAS,cAAA3F,sBAAA,uBAA5BA,sBAAA,CAA8B4F,SAAS,EAAE,GAChG,4BACL;YACDC,GAAG,EAAE,CAAA7E,iBAAiB,aAAjBA,iBAAiB,wBAAAf,sBAAA,GAAjBe,iBAAiB,CAAE2E,SAAS,cAAA1F,sBAAA,uBAA5BA,sBAAA,CAA8B6F,IAAI,KAAI,iBAAkB;YAC7DrB,SAAS,EAAC;UAAqB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACF7E,OAAA,CAACH,UAAU;YACT,cAAW,cAAc;YACzBiG,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,IAAI,CAAE;YACxC+E,EAAE,EAAE;cACFD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC;cACNC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE;YACV,CAAE;YAAAjC,QAAA,eAEFzE,OAAA,CAACV,MAAM;cAACqH,KAAK,EAAC,OAAO;cAACC,IAAI,EAAE;YAAG;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAAC,GAAG,EACzB,CAAAxD,iBAAiB,aAAjBA,iBAAiB,wBAAAd,qBAAA,GAAjBc,iBAAiB,CAAE+E,MAAM,cAAA7F,qBAAA,uBAAzBA,qBAAA,CAA2B8F,SAAS,KAAI,SAAS,EAAE,GAAG,EACtD,CAAAhF,iBAAiB,aAAjBA,iBAAiB,wBAAAb,sBAAA,GAAjBa,iBAAiB,CAAE+E,MAAM,cAAA5F,sBAAA,uBAAzBA,sBAAA,CAA2BqG,QAAQ,KAAI,MAAM;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACJ7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAAC,GAAG,EAC1B,CAAAxD,iBAAiB,aAAjBA,iBAAiB,wBAAAZ,sBAAA,GAAjBY,iBAAiB,CAAE+E,MAAM,cAAA3F,sBAAA,uBAAzBA,sBAAA,CAA2BqG,KAAK,KAAI,UAAU;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACJ7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAAC,GAAG,EAC1B,CAAAxD,iBAAiB,aAAjBA,iBAAiB,wBAAAX,sBAAA,GAAjBW,iBAAiB,CAAE+E,MAAM,cAAA1F,sBAAA,uBAAzBA,sBAAA,CAA2BqG,WAAW,KAAI,iBAAiB;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACJ7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAAC,GAAG,EACjC,IAAIrC,IAAI,CAACnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEoB,SAAS,CAAC,CAACuE,kBAAkB,CAAC,CAAC;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACJ7E,OAAA;UAAI+E,KAAK,EAAE;YAAEkC,SAAS,EAAE,MAAM;YAAEjC,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,GAAC,oBACpC,EAAC,GAAG;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACL7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6F,QAAQ;QAAA;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACJ7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEuF,IAAI;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACJ7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACxD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEsF,KAAK;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACJ7E,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAAyE,QAAA,EAAQ;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAAC,GAAG,EAC5CxD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE8F,aAAa;QAAA;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACJ7E,OAAA;UACE+E,KAAK,EAAE;YACL4B,KAAK,EAAEtF,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+F,cAAc,GACpC,SAAS,GACT,SAAS;YACbC,MAAM,EAAE,mBAAmB;YAC3BC,eAAe,EAAEjG,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+F,cAAc,GAC9C,SAAS,GACT,SAAS;YACbG,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,KAAK;YACdP,SAAS,EAAE,MAAM;YACjBQ,KAAK,EAAE,KAAK;YACZC,SAAS,EAAE;UACb,CAAE;UAAAjD,QAAA,EAEDpD,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAE+F,cAAc,GAC9B,iBAAiB,GACjB;QAAqB;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACR7E,OAAA;UAAM2H,QAAQ,EAAE/D,YAAa;UAACmB,KAAK,EAAE;YAAEkC,SAAS,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBACzDzE,OAAA;YAAAyE,QAAA,EAAO;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpB7E,OAAA;YACE4H,KAAK,EAAEnG,IAAK;YACZoG,QAAQ,EAAGhE,CAAC,IAAKnC,OAAO,CAACmC,CAAC,CAACiE,MAAM,CAACF,KAAK,CAAE;YACzCG,IAAI,EAAE,CAAE;YACRhD,KAAK,EAAE;cAAEuC,eAAe,EAAE;YAAO,CAAE;YACnCU,QAAQ,EAAE,CAAA3G,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+F,cAAc,MAAK;UAAK;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEF7E,OAAA;YAAAyE,QAAA,EAAO;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3B7E,OAAA;YACEiI,IAAI,EAAC,QAAQ;YACbL,KAAK,EAAEjG,UAAW;YAClBkG,QAAQ,EAAGhE,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACiE,MAAM,CAACF,KAAK,CAAE;YAC/CM,QAAQ;YACRF,QAAQ,EAAE,CAAA3G,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+F,cAAc,MAAK;UAAK;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEF7E,OAAA;YAAAyE,QAAA,EAAO;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnD7E,OAAA;YACEiI,IAAI,EAAC,MAAM;YACXJ,QAAQ,EAAGhE,CAAC,IAAK/B,OAAO,CAAC+B,CAAC,CAACiE,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAAE;YAC5CC,QAAQ,EAAE,CAAA/G,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+F,cAAc,MAAK;UAAK;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEF7E,OAAA;YACE+E,KAAK,EAAE;cACLE,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,MAAM;cACX8B,SAAS,EAAE,MAAM;cACjBoB,cAAc,EAAE,cAAc;cAC9BnD,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GA4BD,CAAApD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiH,cAAc,CAACC,IAAI,MAAKC,SAAS,iBACnDxI,OAAA;cACE+E,KAAK,EAAE;gBACLuC,eAAe,EAAEjG,iBAAiB,CAACiH,cAAc,CAACC,IAAI,GAClD,SAAS,GACT,SAAS;gBACblB,MAAM,EAAEhG,iBAAiB,CAACiH,cAAc,CAACC,IAAI,GACzC,mBAAmB,GACnB,mBAAmB;gBACvB5B,KAAK,EAAEtF,iBAAiB,CAACiH,cAAc,CAACC,IAAI,GACxC,SAAS,GACT,SAAS;gBACbf,OAAO,EAAE,KAAK;gBACdD,YAAY,EAAE;cAChB,CAAE;cAAA9C,QAAA,EAEDpD,iBAAiB,CAACiH,cAAc,CAACC,IAAI,GAClC,MAAM,GACN;YAAc;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACR,EACA,CAAAxD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+F,cAAc,kBAChCpH,OAAA;cAAG+E,KAAK,EAAE;gBAAEkC,SAAS,EAAE;cAAO,CAAE;cAAAxC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACnD,eAyBD7E,OAAA;cACE8F,OAAO,EAAEzB,YAAa;cACtB+D,QAAQ,EAAE,CAAA/G,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiH,cAAc,CAACC,IAAI,MAAK,IAAK;cAC1DxD,KAAK,EAAE;gBACLuC,eAAe,EAAE,MAAM;gBACvBX,KAAK,EAAE,SAAS;gBAChBU,MAAM,EAAE,eAAe;gBACvBG,OAAO,EAAE,WAAW;gBACpBD,YAAY,EAAE,KAAK;gBACnBkB,MAAM,EACJ,CAAApH,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiH,cAAc,CAACC,IAAI,MAAK,IAAI,GAC3C,aAAa,GACb,SAAS;gBACfG,OAAO,EACL,CAAArH,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiH,cAAc,CAACC,IAAI,MAAK,IAAI,GAAG,GAAG,GAAG,CAAC;gBAC3D,SAAS,EAAE;kBACTjB,eAAe,EAAE,KAAK;kBACtBX,KAAK,EAAE,MAAM;kBACbgC,SAAS,EAAE,YAAY;kBACvBC,SAAS,EAAE;gBACb;cACF,CAAE;cAAAnE,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACR,CAACxD,iBAAiB,CAACM,UAAU,iBAC5B3B,OAAA;cACEiI,IAAI,EAAC,QAAQ;cACblD,KAAK,EAAE;gBACLuC,eAAe,EAAE,SAAS;gBAC1BX,KAAK,EAAE,MAAM;gBACb,SAAS,EAAE;kBACTW,eAAe,EAAE,SAAS;kBAC1BqB,SAAS,EAAE,uBAAuB;kBAClCC,SAAS,EAAE;gBACb;cACF,CAAE;cAAAnE,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eACD7E,OAAA,CAACT,MAAM;MACLsJ,IAAI,EAAEtH,eAAgB;MACtBuH,OAAO,EAAEA,CAAA,KAAMtH,kBAAkB,CAAC,KAAK,CAAE;MACzCuH,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTzC,EAAE,EAAE;QACFgB,YAAY,EAAE,KAAK;QACnB0B,MAAM,EAAE;MACV,CAAE;MAAAxE,QAAA,gBAEFzE,OAAA,CAACR,WAAW;QAAAiF,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9C7E,OAAA,CAACP,aAAa;QAACyJ,QAAQ;QAAAzE,QAAA,EACpBpD,iBAAiB,iBAChBrB,OAAA,CAACF,GAAG;UAAA2E,QAAA,gBACFzE,OAAA;YACE+F,GAAG,EAAE,uDAAuD1E,iBAAiB,CAAC2E,SAAS,CAACC,SAAS,EAAG;YACpGC,GAAG,EAAE7E,iBAAiB,CAAC2E,SAAS,CAACG,IAAK;YACtCpB,KAAK,EAAE;cACL0C,KAAK,EAAE,MAAM;cACbwB,MAAM,EAAE,GAAG;cACXE,SAAS,EAAE,OAAO;cAClB5B,YAAY,EAAE;YAChB;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF7E,OAAA,CAACJ,UAAU;YAACwJ,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA5E,QAAA,EAC5BpD,iBAAiB,CAAC2E,SAAS,CAACG;UAAI;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,gBACTzE,OAAA;cAAAyE,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAC9BxD,iBAAiB,CAAC2E,SAAS,CAACsD,SAAS,gBACpCtJ,OAAA;cAAK+E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,GAC9BpD,iBAAiB,CAAC2E,SAAS,CAACuD,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,OACtD;YAAA;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENxD,iBAAiB,CAAC2E,SAAS,CAACuD,KAAK,CAACC,cAAc,CAAC,CAClD,EACAnI,iBAAiB,CAAC2E,SAAS,CAACsD,SAAS,iBACpCtJ,OAAA;cAAM+E,KAAK,EAAE;gBAAE4B,KAAK,EAAE;cAAM,CAAE;cAAAlC,QAAA,GAC3B,GAAG,EACHpD,iBAAiB,CAAC2E,SAAS,CAACsD,SAAS,CAACE,cAAc,CAAC,CAAC,EAAC,OAC1D;YAAA;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,GACR,GAAG,eACJzE,OAAA;cAAAyE,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACxD,iBAAiB,CAAC2E,SAAS,CAACyD,GAAG;UAAA;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAKb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,gBACTzE,OAAA;cAAAyE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAChCxD,iBAAiB,CAAC2E,SAAS,CAAC0D,UAAU;UAAA;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,gBACTzE,OAAA;cAAAyE,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAC1BxD,iBAAiB,CAAC2E,SAAS,CAAC2D,eAAe;UAAA;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,gBACTzE,OAAA;cAAAyE,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,GAAAlE,sBAAA,GAC5BU,iBAAiB,CAAC2E,SAAS,CAAC4D,MAAM,cAAAjJ,sBAAA,uBAAlCA,sBAAA,CAAoCkJ,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,gBACTzE,OAAA;cAAAyE,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,GAAAjE,sBAAA,GAC5BS,iBAAiB,CAAC2E,SAAS,CAAC8D,KAAK,cAAAlJ,sBAAA,uBAAjCA,sBAAA,CAAmCiJ,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACb7E,OAAA,CAACJ,UAAU;YAAA6E,QAAA,gBACTzE,OAAA;cAAAyE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,GAAAhE,sBAAA,GAC5BQ,iBAAiB,CAAC2E,SAAS,CAAC+D,mBAAmB,cAAAlJ,sBAAA,uBAA/CA,sBAAA,CAAiDyE,MAAM,EAAC,IAAE,EAAC,GAAG,GAAAxE,sBAAA,GAC9DO,iBAAiB,CAAC2E,SAAS,CAAC+D,mBAAmB,cAAAjJ,sBAAA,uBAA/CA,sBAAA,CAAiD2G,KAAK,EAAC,IAAE,EAAC,GAAG,GAAA1G,sBAAA,GAC7DM,iBAAiB,CAAC2E,SAAS,CAAC+D,mBAAmB,cAAAhJ,sBAAA,uBAA/CA,sBAAA,CAAiDkI,MAAM,EAAC,KAC3D;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB7E,OAAA,CAACN,aAAa;QAAA+E,QAAA,eACZzE,OAAA,CAACL,MAAM;UACL4G,EAAE,EAAE;YACFe,eAAe,EAAE,SAAS;YAC1BX,KAAK,EAAE,OAAO;YACd,SAAS,EAAE;cACTW,eAAe,EAAE,SAAS;cAC1BqB,SAAS,EAAE,YAAY;cACvBC,SAAS,EAAE;YACb;UACF,CAAE;UACF9C,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,KAAK,CAAE;UAAAiD,QAAA,EAC1C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA1eID,cAAc;EAAA,QACCZ,SAAS;AAAA;AAAA2K,EAAA,GADxB/J,cAAc;AA4epB,eAAeA,cAAc;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}