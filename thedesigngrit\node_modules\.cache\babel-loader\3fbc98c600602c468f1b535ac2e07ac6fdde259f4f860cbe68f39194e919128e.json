{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\ProfileCardVendor.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileCardVendor = ({\n  vendor,\n  brandName,\n  onClose\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"profile-card-flip-container\",\n  style: {\n    backdropFilter: \"blur(5px)\"\n  },\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-card-flip\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-card-front\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Employee Card\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Name:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), \" \", vendor.firstName, \" \", vendor.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Email:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), \" \", vendor.email]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Phone:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), \" \", vendor.phoneNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Employee #:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), \" \", vendor.employeeNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Role:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), \" \", vendor.role]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Tier:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), \" \", vendor.tier]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Brand:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), \" \", brandName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"profile-card-close-btn\",\n        onClick: onClose,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = ProfileCardVendor;\nexport default ProfileCardVendor;\nvar _c;\n$RefreshReg$(_c, \"ProfileCardVendor\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ProfileCardVendor", "vendor", "brandName", "onClose", "className", "style", "<PERSON><PERSON>ilter", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "lastName", "email", "phoneNumber", "employeeNumber", "role", "tier", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/ProfileCardVendor.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst ProfileCardVendor = ({ vendor, brandName, onClose }) => (\r\n  <div\r\n    className=\"profile-card-flip-container\"\r\n    style={{ backdropFilter: \"blur(5px)\" }}\r\n  >\r\n    <div className=\"profile-card-flip\">\r\n      <div className=\"profile-card-front\">\r\n        <h2>Employee Card</h2>\r\n        <div className=\"profile-card-content\">\r\n          <p>\r\n            <strong>Name:</strong> {vendor.firstName} {vendor.lastName}\r\n          </p>\r\n          <p>\r\n            <strong>Email:</strong> {vendor.email}\r\n          </p>\r\n          <p>\r\n            <strong>Phone:</strong> {vendor.phoneNumber}\r\n          </p>\r\n          <p>\r\n            <strong>Employee #:</strong> {vendor.employeeNumber}\r\n          </p>\r\n          <p>\r\n            <strong>Role:</strong> {vendor.role}\r\n          </p>\r\n          <p>\r\n            <strong>Tier:</strong> {vendor.tier}\r\n          </p>\r\n          <p>\r\n            <strong>Brand:</strong> {brandName}\r\n          </p>\r\n        </div>\r\n        <button className=\"profile-card-close-btn\" onClick={onClose}>\r\n          Close\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default ProfileCardVendor;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC;AAAQ,CAAC,kBACvDJ,OAAA;EACEK,SAAS,EAAC,6BAA6B;EACvCC,KAAK,EAAE;IAAEC,cAAc,EAAE;EAAY,CAAE;EAAAC,QAAA,eAEvCR,OAAA;IAAKK,SAAS,EAAC,mBAAmB;IAAAG,QAAA,eAChCR,OAAA;MAAKK,SAAS,EAAC,oBAAoB;MAAAG,QAAA,gBACjCR,OAAA;QAAAQ,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBZ,OAAA;QAAKK,SAAS,EAAC,sBAAsB;QAAAG,QAAA,gBACnCR,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,MAAM,CAACW,SAAS,EAAC,GAAC,EAACX,MAAM,CAACY,QAAQ;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACJZ,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,MAAM,CAACa,KAAK;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACJZ,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,MAAM,CAACc,WAAW;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACJZ,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,MAAM,CAACe,cAAc;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACJZ,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,MAAM,CAACgB,IAAI;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACJZ,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACV,MAAM,CAACiB,IAAI;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACJZ,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACT,SAAS;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNZ,OAAA;QAAQK,SAAS,EAAC,wBAAwB;QAACe,OAAO,EAAEhB,OAAQ;QAAAI,QAAA,EAAC;MAE7D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACS,EAAA,GArCIpB,iBAAiB;AAuCvB,eAAeA,iBAAiB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}