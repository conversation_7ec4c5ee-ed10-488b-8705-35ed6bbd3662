{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Products\\\\productcard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Card, CardMedia, CardContent, Typography, IconButton, Box } from \"@mui/material\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { useNavigate } from \"react-router-dom\";\nimport { UserContext } from \"../../utils/userContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  _s();\n  var _product$brandId;\n  const navigate = useNavigate();\n  const {\n    userSession\n  } = useContext(UserContext); // Access user session and logout function from context\n  const [isFavorite, setIsFavorite] = useState(false);\n\n  // Fetch the user's favorite products on component mount\n  useEffect(() => {\n    const fetchFavorites = async () => {\n      if (!userSession) return; // Make sure userSession is available\n\n      const response = await fetch(`https://api.thedesigngrit.com/api/favorites/${userSession.id}`);\n      if (response.ok) {\n        const favoritesData = await response.json();\n        const favoriteIds = favoritesData.map(prod => prod._id);\n        setIsFavorite(favoriteIds.includes(product._id));\n      }\n    };\n    fetchFavorites();\n  }, [userSession, product._id]);\n\n  // Toggle the favorite status\n  const toggleFavorite = async event => {\n    event.stopPropagation(); // Prevent triggering card click\n\n    if (!userSession) return; // If there's no user session, prevent posting\n\n    const endpoint = isFavorite ? \"/remove\" : \"/add\";\n    const requestPayload = {\n      userSession,\n      productId: product._id\n    };\n    try {\n      const response = await fetch(`https://api.thedesigngrit.com/api/favorites${endpoint}`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestPayload)\n      });\n      if (response.ok) {\n        setIsFavorite(!isFavorite); // Toggle the favorite status if successful\n      } else {\n        console.error(\"Error: Unable to update favorite status.\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n\n  // Navigate to product details page\n  const handleCardClick = () => {\n    navigate(`/product/${product._id}`);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: \"100%\",\n      height: \"100%\",\n      display: \"flex\",\n      flexDirection: \"column\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        width: \"100%\",\n        borderRadius: \"16px\",\n        boxShadow: \"0px 4px 8px rgba(0, 0, 0, 0.1)\",\n        position: \"relative\",\n        overflow: \"hidden\",\n        height: \"auto\",\n        display: \"flex\",\n        flexDirection: \"column\"\n      },\n      onClick: handleCardClick,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"absolute\",\n          top: 8,\n          left: 8,\n          zIndex: 2,\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"4px\"\n        },\n        children: [product.stock === 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            backgroundColor: \"#DD4A2A\",\n            color: \"#fff\",\n            padding: \"4px 10px\",\n            borderRadius: \"6px\",\n            fontSize: \"12px\",\n            fontWeight: 600,\n            fontFamily: \"Montserrat\",\n            boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\"\n          },\n          children: \"SOLD OUT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), product.stock > 0 && product.stock <= 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            backgroundColor: \"#FFAC1C\",\n            color: \"#fff\",\n            padding: \"4px 10px\",\n            borderRadius: \"6px\",\n            fontSize: \"12px\",\n            fontWeight: 600,\n            fontFamily: \"Montserrat\",\n            boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\",\n            animation: \"pulse 1.5s infinite\",\n            \"@keyframes pulse\": {\n              \"0%\": {\n                transform: \"scale(1)\",\n                opacity: 1\n              },\n              \"50%\": {\n                transform: \"scale(1.05)\",\n                opacity: 0.8\n              },\n              \"100%\": {\n                transform: \"scale(1)\",\n                opacity: 1\n              }\n            }\n          },\n          children: \"HURRY UP!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this), product.stock === 0 || product.stock > 0 && product.stock <= 5 ? product.discountPercentage && product.discountPercentage > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: \"#e74c3c\",\n            color: \"#fff\",\n            borderRadius: \"5px\",\n            padding: \"4px 10px\",\n            fontSize: \"13px\",\n            fontWeight: 700,\n            letterSpacing: \"1px\",\n            fontFamily: \"Montserrat\",\n            boxShadow: \"0px 2px 6px rgba(0,0,0,0.15)\"\n          },\n          children: [product.discountPercentage, \"% OFF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 17\n        }, this) : product.discountPercentage && product.discountPercentage > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: \"#e74c3c\",\n            color: \"#fff\",\n            borderRadius: \"5px\",\n            padding: \"4px 10px\",\n            fontSize: \"13px\",\n            fontWeight: 700,\n            letterSpacing: \"1px\",\n            fontFamily: \"Montserrat\",\n            boxShadow: \"0px 2px 6px rgba(0,0,0,0.15)\"\n          },\n          children: [product.discountPercentage, \"% OFF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          aspectRatio: {\n            xs: \"4 / 3\",\n            sm: \"4 / 3\",\n            md: \"16 / 9\"\n          },\n          overflow: \"hidden\"\n        },\n        children: /*#__PURE__*/_jsxDEV(CardMedia, {\n          component: \"img\",\n          sx: {\n            width: \"100%\",\n            aspectRatio: {\n              xs: \"1 / 1\",\n              // square on mobile\n              sm: \"4 / 3\",\n              // wider on tablet\n              md: \"16 / 9\" // widescreen on big screens\n            },\n            objectFit: \"cover\"\n          },\n          image: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n          alt: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        sx: {\n          position: \"absolute\",\n          top: 8,\n          right: 8,\n          backgroundColor: \"#fff\",\n          boxShadow: \"0px 2px 4px rgba(0, 0, 0, 0.1)\",\n          \"&:hover\": {\n            backgroundColor: \"#f0f0f0\"\n          }\n        },\n        onClick: event => {\n          event.stopPropagation();\n          toggleFavorite(event);\n        },\n        children: isFavorite ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {\n          sx: {\n            color: \"red\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n          sx: {\n            color: \"#000\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        p: {\n          xs: 1,\n          sm: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontSize: {\n            xs: \"14px\",\n            sm: \"16px\"\n          },\n          // smaller font on mobile, bigger on desktop\n          fontWeight: 700,\n          fontFamily: \"Montserrat\",\n          textTransform: \"uppercase\",\n          width: \"100%\",\n          color: \"#2d2d2d\",\n          maxWidth: {\n            xs: \"100%\",\n            sm: \"80%\"\n          },\n          // full width on mobile, limit on larger screens\n          whiteSpace: \"normal\",\n          // allow wrapping on mobile\n          overflowWrap: \"break-word\" // break long words\n        },\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          fontSize: {\n            xs: \"12px\",\n            sm: \"14px\"\n          },\n          // smaller font on mobile\n          fontFamily: \"Montserrat\",\n          color: \"#777777\",\n          fontWeight: 600,\n          marginTop: \"4px\",\n          whiteSpace: \"normal\",\n          overflowWrap: \"break-word\"\n        },\n        children: ((_product$brandId = product.brandId) === null || _product$brandId === void 0 ? void 0 : _product$brandId.brandName) || \"Unknown Brand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          fontSize: {\n            xs: \"12px\",\n            sm: \"14px\"\n          },\n          // smaller font on mobile\n          fontWeight: 700,\n          fontFamily: \"Montserrat\",\n          color: \"#2d2d2d\",\n          marginTop: \"8px\",\n          whiteSpace: \"normal\",\n          overflowWrap: \"break-word\",\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"12px\"\n        },\n        children: product.salePrice && product.promotionApproved === true ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              textDecoration: \"line-through\",\n              marginRight: \"5px\"\n            },\n            children: [Number(product.price).toLocaleString(), \" E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: \"red\",\n              fontWeight: \"700\"\n            },\n            children: [Number(product.salePrice).toLocaleString(), \"E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : Number(product.price).toLocaleString() + \"E£\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductCard, \"5m6krgbW2/fMPwY6vSDHrS30lts=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "IconButton", "Box", "FavoriteBorderIcon", "FavoriteIcon", "useNavigate", "UserContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductCard", "product", "_s", "_product$brandId", "navigate", "userSession", "isFavorite", "setIsFavorite", "fetchFavorites", "response", "fetch", "id", "ok", "favoritesData", "json", "favoriteIds", "map", "prod", "_id", "includes", "toggleFavorite", "event", "stopPropagation", "endpoint", "requestPayload", "productId", "method", "headers", "body", "JSON", "stringify", "console", "error", "handleCardClick", "sx", "width", "height", "display", "flexDirection", "children", "borderRadius", "boxShadow", "position", "overflow", "onClick", "top", "left", "zIndex", "gap", "stock", "backgroundColor", "color", "padding", "fontSize", "fontWeight", "fontFamily", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "animation", "transform", "opacity", "discountPercentage", "background", "letterSpacing", "aspectRatio", "xs", "sm", "md", "component", "objectFit", "image", "mainImage", "alt", "name", "right", "p", "variant", "textTransform", "max<PERSON><PERSON><PERSON>", "whiteSpace", "overflowWrap", "marginTop", "brandId", "brandName", "alignItems", "salePrice", "promotionApproved", "style", "textDecoration", "marginRight", "Number", "price", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Products/productcard.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport {\r\n  Card,\r\n  CardMedia,\r\n  CardContent,\r\n  Typography,\r\n  IconButton,\r\n  Box,\r\n} from \"@mui/material\";\r\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { UserContext } from \"../../utils/userContext\";\r\n\r\nconst ProductCard = ({ product }) => {\r\n  const navigate = useNavigate();\r\n  const { userSession } = useContext(UserContext); // Access user session and logout function from context\r\n  const [isFavorite, setIsFavorite] = useState(false);\r\n\r\n  // Fetch the user's favorite products on component mount\r\n  useEffect(() => {\r\n    const fetchFavorites = async () => {\r\n      if (!userSession) return; // Make sure userSession is available\r\n\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/favorites/${userSession.id}`\r\n      );\r\n      if (response.ok) {\r\n        const favoritesData = await response.json();\r\n        const favoriteIds = favoritesData.map((prod) => prod._id);\r\n        setIsFavorite(favoriteIds.includes(product._id));\r\n      }\r\n    };\r\n    fetchFavorites();\r\n  }, [userSession, product._id]);\r\n\r\n  // Toggle the favorite status\r\n  const toggleFavorite = async (event) => {\r\n    event.stopPropagation(); // Prevent triggering card click\r\n\r\n    if (!userSession) return; // If there's no user session, prevent posting\r\n\r\n    const endpoint = isFavorite ? \"/remove\" : \"/add\";\r\n    const requestPayload = {\r\n      userSession,\r\n      productId: product._id,\r\n    };\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/favorites${endpoint}`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(requestPayload),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        setIsFavorite(!isFavorite); // Toggle the favorite status if successful\r\n      } else {\r\n        console.error(\"Error: Unable to update favorite status.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  // Navigate to product details page\r\n  const handleCardClick = () => {\r\n    navigate(`/product/${product._id}`);\r\n  };\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        width: \"100%\",\r\n        height: \"100%\",\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n      }}\r\n    >\r\n      <Card\r\n        sx={{\r\n          width: \"100%\",\r\n          borderRadius: \"16px\",\r\n          boxShadow: \"0px 4px 8px rgba(0, 0, 0, 0.1)\",\r\n          position: \"relative\",\r\n          overflow: \"hidden\",\r\n          height: \"auto\",\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n        }}\r\n        onClick={handleCardClick}\r\n      >\r\n        {/* Discount Percentage Label - Top Left */}\r\n        <Box\r\n          sx={{\r\n            position: \"absolute\",\r\n            top: 8,\r\n            left: 8,\r\n            zIndex: 2,\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            gap: \"4px\",\r\n          }}\r\n        >\r\n          {product.stock === 0 && (\r\n            <Box\r\n              sx={{\r\n                backgroundColor: \"#DD4A2A\",\r\n                color: \"#fff\",\r\n                padding: \"4px 10px\",\r\n                borderRadius: \"6px\",\r\n                fontSize: \"12px\",\r\n                fontWeight: 600,\r\n                fontFamily: \"Montserrat\",\r\n                boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\",\r\n              }}\r\n            >\r\n              SOLD OUT\r\n            </Box>\r\n          )}\r\n          {product.stock > 0 && product.stock <= 5 && (\r\n            <Box\r\n              sx={{\r\n                backgroundColor: \"#FFAC1C\",\r\n                color: \"#fff\",\r\n                padding: \"4px 10px\",\r\n                borderRadius: \"6px\",\r\n                fontSize: \"12px\",\r\n                fontWeight: 600,\r\n                fontFamily: \"Montserrat\",\r\n                boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\",\r\n                animation: \"pulse 1.5s infinite\",\r\n                \"@keyframes pulse\": {\r\n                  \"0%\": { transform: \"scale(1)\", opacity: 1 },\r\n                  \"50%\": { transform: \"scale(1.05)\", opacity: 0.8 },\r\n                  \"100%\": { transform: \"scale(1)\", opacity: 1 },\r\n                },\r\n              }}\r\n            >\r\n              HURRY UP!\r\n            </Box>\r\n          )}\r\n          {product.stock === 0 || (product.stock > 0 && product.stock <= 5)\r\n            ? product.discountPercentage &&\r\n              product.discountPercentage > 0 && (\r\n                <Box\r\n                  sx={{\r\n                    background: \"#e74c3c\",\r\n                    color: \"#fff\",\r\n                    borderRadius: \"5px\",\r\n                    padding: \"4px 10px\",\r\n                    fontSize: \"13px\",\r\n                    fontWeight: 700,\r\n                    letterSpacing: \"1px\",\r\n                    fontFamily: \"Montserrat\",\r\n                    boxShadow: \"0px 2px 6px rgba(0,0,0,0.15)\",\r\n                  }}\r\n                >\r\n                  {product.discountPercentage}% OFF\r\n                </Box>\r\n              )\r\n            : product.discountPercentage &&\r\n              product.discountPercentage > 0 && (\r\n                <Box\r\n                  sx={{\r\n                    background: \"#e74c3c\",\r\n                    color: \"#fff\",\r\n                    borderRadius: \"5px\",\r\n                    padding: \"4px 10px\",\r\n                    fontSize: \"13px\",\r\n                    fontWeight: 700,\r\n                    letterSpacing: \"1px\",\r\n                    fontFamily: \"Montserrat\",\r\n                    boxShadow: \"0px 2px 6px rgba(0,0,0,0.15)\",\r\n                  }}\r\n                >\r\n                  {product.discountPercentage}% OFF\r\n                </Box>\r\n              )}\r\n        </Box>\r\n        {/* Product Image */}\r\n        <Box\r\n          sx={{\r\n            width: \"100%\",\r\n            aspectRatio: {\r\n              xs: \"4 / 3\",\r\n              sm: \"4 / 3\",\r\n              md: \"16 / 9\",\r\n            },\r\n            overflow: \"hidden\",\r\n          }}\r\n        >\r\n          <CardMedia\r\n            component=\"img\"\r\n            sx={{\r\n              width: \"100%\",\r\n              aspectRatio: {\r\n                xs: \"1 / 1\", // square on mobile\r\n                sm: \"4 / 3\", // wider on tablet\r\n                md: \"16 / 9\", // widescreen on big screens\r\n              },\r\n              objectFit: \"cover\",\r\n            }}\r\n            image={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n            alt={product.name}\r\n          />\r\n        </Box>\r\n        {/* Favorite Icon */}\r\n        <IconButton\r\n          sx={{\r\n            position: \"absolute\",\r\n            top: 8,\r\n            right: 8,\r\n            backgroundColor: \"#fff\",\r\n            boxShadow: \"0px 2px 4px rgba(0, 0, 0, 0.1)\",\r\n            \"&:hover\": { backgroundColor: \"#f0f0f0\" },\r\n          }}\r\n          onClick={(event) => {\r\n            event.stopPropagation();\r\n            toggleFavorite(event);\r\n          }}\r\n        >\r\n          {isFavorite ? (\r\n            <FavoriteIcon sx={{ color: \"red\" }} />\r\n          ) : (\r\n            <FavoriteBorderIcon sx={{ color: \"#000\" }} />\r\n          )}\r\n        </IconButton>\r\n      </Card>\r\n\r\n      {/* Product Information */}\r\n      <CardContent sx={{ p: { xs: 1, sm: 2 } }}>\r\n        <Typography\r\n          variant=\"h6\"\r\n          sx={{\r\n            fontSize: { xs: \"14px\", sm: \"16px\" }, // smaller font on mobile, bigger on desktop\r\n            fontWeight: 700,\r\n            fontFamily: \"Montserrat\",\r\n            textTransform: \"uppercase\",\r\n            width: \"100%\",\r\n            color: \"#2d2d2d\",\r\n            maxWidth: { xs: \"100%\", sm: \"80%\" }, // full width on mobile, limit on larger screens\r\n            whiteSpace: \"normal\", // allow wrapping on mobile\r\n            overflowWrap: \"break-word\", // break long words\r\n          }}\r\n        >\r\n          {product.name}\r\n        </Typography>\r\n\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            fontSize: { xs: \"12px\", sm: \"14px\" }, // smaller font on mobile\r\n            fontFamily: \"Montserrat\",\r\n            color: \"#777777\",\r\n            fontWeight: 600,\r\n            marginTop: \"4px\",\r\n            whiteSpace: \"normal\",\r\n            overflowWrap: \"break-word\",\r\n          }}\r\n        >\r\n          {product.brandId?.brandName || \"Unknown Brand\"}\r\n        </Typography>\r\n\r\n        <Typography\r\n          variant=\"body1\"\r\n          sx={{\r\n            fontSize: { xs: \"12px\", sm: \"14px\" }, // smaller font on mobile\r\n            fontWeight: 700,\r\n            fontFamily: \"Montserrat\",\r\n            color: \"#2d2d2d\",\r\n            marginTop: \"8px\",\r\n            whiteSpace: \"normal\",\r\n            overflowWrap: \"break-word\",\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: \"12px\",\r\n          }}\r\n        >\r\n          {product.salePrice && product.promotionApproved === true ? (\r\n            <>\r\n              <span\r\n                style={{ textDecoration: \"line-through\", marginRight: \"5px\" }}\r\n              >\r\n                {Number(product.price).toLocaleString()} E£\r\n              </span>\r\n              <span style={{ color: \"red\", fontWeight: \"700\" }}>\r\n                {Number(product.salePrice).toLocaleString()}E£\r\n              </span>\r\n            </>\r\n          ) : (\r\n            Number(product.price).toLocaleString() + \"E£\"\r\n          )}\r\n        </Typography>\r\n      </CardContent>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ProductCard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SACEC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,GAAG,QACE,eAAe;AACtB,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACnC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAY,CAAC,GAAGpB,UAAU,CAACU,WAAW,CAAC,CAAC,CAAC;EACjD,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACH,WAAW,EAAE,OAAO,CAAC;;MAE1B,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+CL,WAAW,CAACM,EAAE,EAC/D,CAAC;MACD,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,aAAa,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAC3C,MAAMC,WAAW,GAAGF,aAAa,CAACG,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,GAAG,CAAC;QACzDX,aAAa,CAACQ,WAAW,CAACI,QAAQ,CAAClB,OAAO,CAACiB,GAAG,CAAC,CAAC;MAClD;IACF,CAAC;IACDV,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,WAAW,EAAEJ,OAAO,CAACiB,GAAG,CAAC,CAAC;;EAE9B;EACA,MAAME,cAAc,GAAG,MAAOC,KAAK,IAAK;IACtCA,KAAK,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEzB,IAAI,CAACjB,WAAW,EAAE,OAAO,CAAC;;IAE1B,MAAMkB,QAAQ,GAAGjB,UAAU,GAAG,SAAS,GAAG,MAAM;IAChD,MAAMkB,cAAc,GAAG;MACrBnB,WAAW;MACXoB,SAAS,EAAExB,OAAO,CAACiB;IACrB,CAAC;IAED,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8Ca,QAAQ,EAAE,EACxD;QACEG,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,cAAc;MACrC,CACF,CAAC;MAED,IAAIf,QAAQ,CAACG,EAAE,EAAE;QACfL,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLyB,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B7B,QAAQ,CAAC,YAAYH,OAAO,CAACiB,GAAG,EAAE,CAAC;EACrC,CAAC;EAED,oBACErB,OAAA,CAACN,GAAG;IACF2C,EAAE,EAAE;MACFC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEF1C,OAAA,CAACX,IAAI;MACHgD,EAAE,EAAE;QACFC,KAAK,EAAE,MAAM;QACbK,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,gCAAgC;QAC3CC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBP,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE;MACjB,CAAE;MACFM,OAAO,EAAEX,eAAgB;MAAAM,QAAA,gBAGzB1C,OAAA,CAACN,GAAG;QACF2C,EAAE,EAAE;UACFQ,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,MAAM,EAAE,CAAC;UACTV,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBU,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,GAEDtC,OAAO,CAACgD,KAAK,KAAK,CAAC,iBAClBpD,OAAA,CAACN,GAAG;UACF2C,EAAE,EAAE;YACFgB,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE,UAAU;YACnBZ,YAAY,EAAE,KAAK;YACnBa,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,YAAY;YACxBd,SAAS,EAAE;UACb,CAAE;UAAAF,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACA1D,OAAO,CAACgD,KAAK,GAAG,CAAC,IAAIhD,OAAO,CAACgD,KAAK,IAAI,CAAC,iBACtCpD,OAAA,CAACN,GAAG;UACF2C,EAAE,EAAE;YACFgB,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE,UAAU;YACnBZ,YAAY,EAAE,KAAK;YACnBa,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,YAAY;YACxBd,SAAS,EAAE,6BAA6B;YACxCmB,SAAS,EAAE,qBAAqB;YAChC,kBAAkB,EAAE;cAClB,IAAI,EAAE;gBAAEC,SAAS,EAAE,UAAU;gBAAEC,OAAO,EAAE;cAAE,CAAC;cAC3C,KAAK,EAAE;gBAAED,SAAS,EAAE,aAAa;gBAAEC,OAAO,EAAE;cAAI,CAAC;cACjD,MAAM,EAAE;gBAAED,SAAS,EAAE,UAAU;gBAAEC,OAAO,EAAE;cAAE;YAC9C;UACF,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EACA1D,OAAO,CAACgD,KAAK,KAAK,CAAC,IAAKhD,OAAO,CAACgD,KAAK,GAAG,CAAC,IAAIhD,OAAO,CAACgD,KAAK,IAAI,CAAE,GAC7DhD,OAAO,CAAC8D,kBAAkB,IAC1B9D,OAAO,CAAC8D,kBAAkB,GAAG,CAAC,iBAC5BlE,OAAA,CAACN,GAAG;UACF2C,EAAE,EAAE;YACF8B,UAAU,EAAE,SAAS;YACrBb,KAAK,EAAE,MAAM;YACbX,YAAY,EAAE,KAAK;YACnBY,OAAO,EAAE,UAAU;YACnBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfW,aAAa,EAAE,KAAK;YACpBV,UAAU,EAAE,YAAY;YACxBd,SAAS,EAAE;UACb,CAAE;UAAAF,QAAA,GAEDtC,OAAO,CAAC8D,kBAAkB,EAAC,OAC9B;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,GACD1D,OAAO,CAAC8D,kBAAkB,IAC1B9D,OAAO,CAAC8D,kBAAkB,GAAG,CAAC,iBAC5BlE,OAAA,CAACN,GAAG;UACF2C,EAAE,EAAE;YACF8B,UAAU,EAAE,SAAS;YACrBb,KAAK,EAAE,MAAM;YACbX,YAAY,EAAE,KAAK;YACnBY,OAAO,EAAE,UAAU;YACnBC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACfW,aAAa,EAAE,KAAK;YACpBV,UAAU,EAAE,YAAY;YACxBd,SAAS,EAAE;UACb,CAAE;UAAAF,QAAA,GAEDtC,OAAO,CAAC8D,kBAAkB,EAAC,OAC9B;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN9D,OAAA,CAACN,GAAG;QACF2C,EAAE,EAAE;UACFC,KAAK,EAAE,MAAM;UACb+B,WAAW,EAAE;YACXC,EAAE,EAAE,OAAO;YACXC,EAAE,EAAE,OAAO;YACXC,EAAE,EAAE;UACN,CAAC;UACD1B,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,eAEF1C,OAAA,CAACV,SAAS;UACRmF,SAAS,EAAC,KAAK;UACfpC,EAAE,EAAE;YACFC,KAAK,EAAE,MAAM;YACb+B,WAAW,EAAE;cACXC,EAAE,EAAE,OAAO;cAAE;cACbC,EAAE,EAAE,OAAO;cAAE;cACbC,EAAE,EAAE,QAAQ,CAAE;YAChB,CAAC;YACDE,SAAS,EAAE;UACb,CAAE;UACFC,KAAK,EAAE,uDAAuDvE,OAAO,CAACwE,SAAS,EAAG;UAClFC,GAAG,EAAEzE,OAAO,CAAC0E;QAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9D,OAAA,CAACP,UAAU;QACT4C,EAAE,EAAE;UACFQ,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACN+B,KAAK,EAAE,CAAC;UACR1B,eAAe,EAAE,MAAM;UACvBT,SAAS,EAAE,gCAAgC;UAC3C,SAAS,EAAE;YAAES,eAAe,EAAE;UAAU;QAC1C,CAAE;QACFN,OAAO,EAAGvB,KAAK,IAAK;UAClBA,KAAK,CAACC,eAAe,CAAC,CAAC;UACvBF,cAAc,CAACC,KAAK,CAAC;QACvB,CAAE;QAAAkB,QAAA,EAEDjC,UAAU,gBACTT,OAAA,CAACJ,YAAY;UAACyC,EAAE,EAAE;YAAEiB,KAAK,EAAE;UAAM;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtC9D,OAAA,CAACL,kBAAkB;UAAC0C,EAAE,EAAE;YAAEiB,KAAK,EAAE;UAAO;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC7C;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGP9D,OAAA,CAACT,WAAW;MAAC8C,EAAE,EAAE;QAAE2C,CAAC,EAAE;UAAEV,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE;MAAE,CAAE;MAAA7B,QAAA,gBACvC1C,OAAA,CAACR,UAAU;QACTyF,OAAO,EAAC,IAAI;QACZ5C,EAAE,EAAE;UACFmB,QAAQ,EAAE;YAAEc,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO,CAAC;UAAE;UACtCd,UAAU,EAAE,GAAG;UACfC,UAAU,EAAE,YAAY;UACxBwB,aAAa,EAAE,WAAW;UAC1B5C,KAAK,EAAE,MAAM;UACbgB,KAAK,EAAE,SAAS;UAChB6B,QAAQ,EAAE;YAAEb,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAM,CAAC;UAAE;UACrCa,UAAU,EAAE,QAAQ;UAAE;UACtBC,YAAY,EAAE,YAAY,CAAE;QAC9B,CAAE;QAAA3C,QAAA,EAEDtC,OAAO,CAAC0E;MAAI;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEb9D,OAAA,CAACR,UAAU;QACTyF,OAAO,EAAC,OAAO;QACf5C,EAAE,EAAE;UACFmB,QAAQ,EAAE;YAAEc,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO,CAAC;UAAE;UACtCb,UAAU,EAAE,YAAY;UACxBJ,KAAK,EAAE,SAAS;UAChBG,UAAU,EAAE,GAAG;UACf6B,SAAS,EAAE,KAAK;UAChBF,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE;QAChB,CAAE;QAAA3C,QAAA,EAED,EAAApC,gBAAA,GAAAF,OAAO,CAACmF,OAAO,cAAAjF,gBAAA,uBAAfA,gBAAA,CAAiBkF,SAAS,KAAI;MAAe;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAEb9D,OAAA,CAACR,UAAU;QACTyF,OAAO,EAAC,OAAO;QACf5C,EAAE,EAAE;UACFmB,QAAQ,EAAE;YAAEc,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO,CAAC;UAAE;UACtCd,UAAU,EAAE,GAAG;UACfC,UAAU,EAAE,YAAY;UACxBJ,KAAK,EAAE,SAAS;UAChBgC,SAAS,EAAE,KAAK;UAChBF,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,YAAY;UAC1B7C,OAAO,EAAE,MAAM;UACfiD,UAAU,EAAE,QAAQ;UACpBtC,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,EAEDtC,OAAO,CAACsF,SAAS,IAAItF,OAAO,CAACuF,iBAAiB,KAAK,IAAI,gBACtD3F,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACE1C,OAAA;YACE4F,KAAK,EAAE;cAAEC,cAAc,EAAE,cAAc;cAAEC,WAAW,EAAE;YAAM,CAAE;YAAApD,QAAA,GAE7DqD,MAAM,CAAC3F,OAAO,CAAC4F,KAAK,CAAC,CAACC,cAAc,CAAC,CAAC,EAAC,QAC1C;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP9D,OAAA;YAAM4F,KAAK,EAAE;cAAEtC,KAAK,EAAE,KAAK;cAAEG,UAAU,EAAE;YAAM,CAAE;YAAAf,QAAA,GAC9CqD,MAAM,CAAC3F,OAAO,CAACsF,SAAS,CAAC,CAACO,cAAc,CAAC,CAAC,EAAC,OAC9C;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CAAC,GAEHiC,MAAM,CAAC3F,OAAO,CAAC4F,KAAK,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;MAC1C;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV,CAAC;AAACzD,EAAA,CAhSIF,WAAW;EAAA,QACEN,WAAW;AAAA;AAAAqG,EAAA,GADxB/F,WAAW;AAkSjB,eAAeA,WAAW;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}