{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Products\\\\filters.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Typography, Checkbox, FormControlLabel, Button, Slider, Accordion, AccordionSummary, AccordionDetails, Chip, Drawer, IconButton, useMediaQuery } from \"@mui/material\";\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport TopFilter from \"./TopFilters\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FilterSection = ({\n  onFilterChange,\n  products = [],\n  currentFilters,\n  sortOption,\n  setSortOption,\n  onCADFilterChange,\n  onSalePriceFilterChange\n}) => {\n  _s();\n  const [selectedFilters, setSelectedFilters] = useState(currentFilters);\n  const [brands, setBrands] = useState([]);\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n  const [drawerOpen, setDrawerOpen] = useState(false);\n  useEffect(() => {\n    setSelectedFilters(currentFilters);\n  }, [currentFilters]);\n  useEffect(() => {\n    const fetchBrands = async () => {\n      try {\n        const response = await fetch(\"https://api.thedesigngrit.com/api/brand/\");\n        const data = await response.json();\n        setBrands(data);\n      } catch (err) {\n        console.error(\"Error fetching brands:\", err);\n      }\n    };\n    fetchBrands();\n  }, []);\n  const handleFilterChange = (type, value) => {\n    const updated = selectedFilters[type].includes(value) ? selectedFilters[type].filter(item => item !== value) : [...selectedFilters[type], value];\n    const newFilters = {\n      ...selectedFilters,\n      [type]: updated\n    };\n    setSelectedFilters(newFilters);\n    onFilterChange(newFilters);\n  };\n  const handlePriceChange = (_, newRange) => {\n    const newFilters = {\n      ...selectedFilters,\n      priceRange: newRange\n    };\n    setSelectedFilters(newFilters);\n    onFilterChange(newFilters);\n  };\n  const clearFilters = () => {\n    const reset = {\n      brands: [],\n      colors: [],\n      tags: [],\n      priceRange: [0, 600000]\n    };\n    setSelectedFilters(reset);\n    onFilterChange(reset);\n  };\n  const allColors = Array.from(new Set(products.flatMap(p => p.colors || [])));\n  const allTags = Array.from(new Set(products.flatMap(p => p.tags || [])));\n  const getFilterLabel = (label, count) => count > 0 ? `${label} (${count})` : label;\n  const renderAccordion = (label, type, items) => /*#__PURE__*/_jsxDEV(Accordion, {\n    disableGutters: true,\n    elevation: 0,\n    square: true,\n    sx: accordionStyle,\n    children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n      expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 37\n      }, this),\n      sx: summaryStyle,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: getFilterLabel(label, selectedFilters[type].length)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n      sx: detailsStyle,\n      children: items.map(item => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Checkbox, {\n          checked: selectedFilters[type].includes(item.brandName || item),\n          onChange: () => handleFilterChange(type, item.brandName || item)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 15\n        }, this),\n        label: item.brandName || item\n      }, item._id || item, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n  const renderFilterContent = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: isMobile ? \"90vw\" : {\n        xs: \"90vw\",\n        sm: 300,\n        md: 280,\n        lg: 300\n      },\n      p: {\n        xs: \"80px 16px 80px 16px\",\n        sm: \"60px 24px 60px 24px\",\n        md: \"24px\"\n      },\n      position: \"relative\"\n      // paddingRight: isMobile ? 0 : \"0px\",\n    },\n    children: [isMobile && /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: () => setDrawerOpen(false),\n      sx: {\n        position: \"absolute\",\n        top: 10,\n        right: \"-24px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      fontWeight: \"bold\",\n      gutterBottom: true,\n      children: \"Filters\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(TopFilter, {\n      sortOption: sortOption,\n      setSortOption: setSortOption,\n      onCADFilterChange: onCADFilterChange,\n      onSalePriceFilterChange: onSalePriceFilterChange,\n      isMobile: true,\n      hasCAD: currentFilters.hasCAD,\n      hasSalePrice: currentFilters.hasSalePrice\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        flexWrap: \"wrap\",\n        gap: 1,\n        mb: 2\n      },\n      children: [\"brands\", \"colors\", \"tags\"].map(type => {\n        var _selectedFilters$type;\n        return (_selectedFilters$type = selectedFilters[type]) === null || _selectedFilters$type === void 0 ? void 0 : _selectedFilters$type.map(item => /*#__PURE__*/_jsxDEV(Chip, {\n          label: item,\n          onDelete: () => handleFilterChange(type, item)\n        }, item, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this));\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: clearFilters,\n      size: \"small\",\n      color: \"#2d2d2d\",\n      sx: {\n        mb: 2,\n        \"&:hover\": {\n          background: \"transparent\"\n        }\n      },\n      children: \"Clear All\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), renderAccordion(\"Brands\", \"brands\", brands), /*#__PURE__*/_jsxDEV(Accordion, {\n      disableGutters: true,\n      elevation: 0,\n      square: true,\n      sx: accordionStyle,\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 39\n        }, this),\n        sx: summaryStyle,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Price\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        sx: priceDetailsStyle,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"Range: \", selectedFilters.priceRange[0].toLocaleString(), \" E\\xA3 -\", \" \", selectedFilters.priceRange[1].toLocaleString(), \"E\\xA3\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Slider, {\n          value: selectedFilters.priceRange,\n          onChange: handlePriceChange,\n          valueLabelDisplay: \"auto\",\n          min: 0,\n          max: 600000,\n          sx: sliderStyle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), renderAccordion(\"Colors\", \"colors\", allColors), renderAccordion(\"Tags\", \"tags\", allTags)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n  return isMobile ? /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setDrawerOpen(true),\n      variant: \"outlined\",\n      sx: drawerButtonStyle,\n      children: \"Show Filters\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"left\",\n      open: drawerOpen,\n      onClose: () => setDrawerOpen(false),\n      children: renderFilterContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true) : renderFilterContent();\n};\n_s(FilterSection, \"7Myzx5pHjXmGWZGJV3ZdJbIUyc4=\", false, function () {\n  return [useMediaQuery];\n});\n_c = FilterSection;\nconst accordionStyle = {\n  border: \"1px solid #2d2d2d\",\n  borderRadius: \"10px\",\n  backgroundColor: \"transparent\",\n  fontFamily: \"Montserrat\",\n  mb: 2,\n  \"&:before\": {\n    display: \"none\"\n  },\n  \"&:hover\": {\n    backgroundColor: \"transparent\",\n    color: \"#2d2d2d\"\n  },\n  \"&.Mui-expanded\": {\n    backgroundColor: \"transparent\",\n    color: \"#2d2d2d\"\n  }\n};\nconst summaryStyle = {\n  backgroundColor: \"transparent\",\n  borderRadius: \"10px\",\n  \"& .MuiAccordionSummary-content\": {\n    fontFamily: \"Montserrat\",\n    fontWeight: \"normal\",\n    color: \"#2d2d2d\"\n  },\n  \"&:hover\": {\n    backgroundColor: \"transparent\",\n    color: \"#fff\"\n  },\n  \"&.Mui-expanded\": {\n    backgroundColor: \"transparent\",\n    color: \"#2d2d2d\"\n  },\n  \"& .MuiSvgIcon-root\": {\n    color: \"#2d2d2d\"\n  },\n  \"&:hover .MuiSvgIcon-root\": {\n    color: \"#fff\"\n  },\n  \"&.Mui-expanded .MuiSvgIcon-root\": {\n    color: \"#2d2d2d\"\n  }\n};\nconst detailsStyle = {\n  display: \"inline-grid\",\n  maxHeight: 300,\n  overflowY: \"auto\",\n  overflow: \"auto\",\n  width: \"100%\",\n  fontFamily: \"Montserrat\",\n  fontWeight: \"normal\"\n};\nconst priceDetailsStyle = {\n  display: \"flex\",\n  alignItems: \"center\",\n  justifyContent: \"space-between\",\n  flexDirection: \"column\",\n  gap: 1\n};\nconst sliderStyle = t => ({\n  color: \"rgba(0,0,0,0.87)\",\n  \"& .MuiSlider-track\": {\n    border: \"none\"\n  },\n  \"& .MuiSlider-thumb\": {\n    width: 24,\n    height: 24,\n    backgroundColor: \"#fff\",\n    \"&::before\": {\n      boxShadow: \"0 4px 8px rgba(0,0,0,0.4)\"\n    },\n    \"&:hover, &.Mui-focusVisible, &.Mui-active\": {\n      boxShadow: \"none\"\n    }\n  },\n  ...t.applyStyles(\"dark\", {\n    color: \"#fff\"\n  })\n});\nconst drawerButtonStyle = {\n  mb: 2,\n  mt: 2,\n  color: \"#2d2d2d\",\n  backgroundColor: \"transparent\",\n  border: \"1px solid #2d2d2d\",\n  \"&:hover\": {\n    backgroundColor: \"#2d2d2d\",\n    color: \"#fff\"\n  }\n};\nexport default FilterSection;\nvar _c;\n$RefreshReg$(_c, \"FilterSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Checkbox", "FormControlLabel", "<PERSON><PERSON>", "Slide<PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "Drawer", "IconButton", "useMediaQuery", "ExpandMoreIcon", "CloseIcon", "TopFilter", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FilterSection", "onFilterChange", "products", "currentFilters", "sortOption", "setSortOption", "onCADFilterChange", "onSalePriceFilterChange", "_s", "selectedFilters", "setSelectedFilters", "brands", "setBrands", "isMobile", "drawerOpen", "setDrawerOpen", "fetchBrands", "response", "fetch", "data", "json", "err", "console", "error", "handleFilterChange", "type", "value", "updated", "includes", "filter", "item", "newFilters", "handlePriceChange", "_", "newRange", "priceRange", "clearFilters", "reset", "colors", "tags", "allColors", "Array", "from", "Set", "flatMap", "p", "allTags", "getFilterLabel", "label", "count", "renderAccordion", "items", "disableGutters", "elevation", "square", "sx", "accordionStyle", "children", "expandIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "summaryStyle", "length", "detailsStyle", "map", "control", "checked", "brandName", "onChange", "_id", "renderFilterContent", "width", "xs", "sm", "md", "lg", "position", "onClick", "top", "right", "variant", "fontWeight", "gutterBottom", "hasCAD", "hasSalePrice", "display", "flexWrap", "gap", "mb", "_selectedFilters$type", "onDelete", "size", "color", "background", "priceDetailsStyle", "toLocaleString", "valueLabelDisplay", "min", "max", "sliderStyle", "drawerButtonStyle", "anchor", "open", "onClose", "_c", "border", "borderRadius", "backgroundColor", "fontFamily", "maxHeight", "overflowY", "overflow", "alignItems", "justifyContent", "flexDirection", "t", "height", "boxShadow", "applyStyles", "mt", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Products/filters.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Checkbox,\r\n  FormControlLabel,\r\n  Button,\r\n  Slider,\r\n  Accordion,\r\n  AccordionSummary,\r\n  AccordionDetails,\r\n  Chip,\r\n  Drawer,\r\n  IconButton,\r\n  useMediaQuery,\r\n} from \"@mui/material\";\r\nimport ExpandMoreIcon from \"@mui/icons-material/ExpandMore\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport TopFilter from \"./TopFilters\";\r\n\r\nconst FilterSection = ({\r\n  onFilterChange,\r\n  products = [],\r\n  currentFilters,\r\n  sortOption,\r\n  setSortOption,\r\n  onCADFilterChange,\r\n  onSalePriceFilterChange,\r\n}) => {\r\n  const [selectedFilters, setSelectedFilters] = useState(currentFilters);\r\n  const [brands, setBrands] = useState([]);\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n  const [drawerOpen, setDrawerOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setSelectedFilters(currentFilters);\r\n  }, [currentFilters]);\r\n\r\n  useEffect(() => {\r\n    const fetchBrands = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          \"https://api.thedesigngrit.com/api/brand/\"\r\n        );\r\n        const data = await response.json();\r\n        setBrands(data);\r\n      } catch (err) {\r\n        console.error(\"Error fetching brands:\", err);\r\n      }\r\n    };\r\n    fetchBrands();\r\n  }, []);\r\n\r\n  const handleFilterChange = (type, value) => {\r\n    const updated = selectedFilters[type].includes(value)\r\n      ? selectedFilters[type].filter((item) => item !== value)\r\n      : [...selectedFilters[type], value];\r\n\r\n    const newFilters = { ...selectedFilters, [type]: updated };\r\n    setSelectedFilters(newFilters);\r\n    onFilterChange(newFilters);\r\n  };\r\n\r\n  const handlePriceChange = (_, newRange) => {\r\n    const newFilters = { ...selectedFilters, priceRange: newRange };\r\n    setSelectedFilters(newFilters);\r\n    onFilterChange(newFilters);\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    const reset = {\r\n      brands: [],\r\n      colors: [],\r\n      tags: [],\r\n      priceRange: [0, 600000],\r\n    };\r\n    setSelectedFilters(reset);\r\n    onFilterChange(reset);\r\n  };\r\n\r\n  const allColors = Array.from(\r\n    new Set(products.flatMap((p) => p.colors || []))\r\n  );\r\n  const allTags = Array.from(new Set(products.flatMap((p) => p.tags || [])));\r\n\r\n  const getFilterLabel = (label, count) =>\r\n    count > 0 ? `${label} (${count})` : label;\r\n\r\n  const renderAccordion = (label, type, items) => (\r\n    <Accordion disableGutters elevation={0} square sx={accordionStyle}>\r\n      <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={summaryStyle}>\r\n        <Typography>\r\n          {getFilterLabel(label, selectedFilters[type].length)}\r\n        </Typography>\r\n      </AccordionSummary>\r\n      <AccordionDetails sx={detailsStyle}>\r\n        {items.map((item) => (\r\n          <FormControlLabel\r\n            key={item._id || item}\r\n            control={\r\n              <Checkbox\r\n                checked={selectedFilters[type].includes(item.brandName || item)}\r\n                onChange={() =>\r\n                  handleFilterChange(type, item.brandName || item)\r\n                }\r\n              />\r\n            }\r\n            label={item.brandName || item}\r\n          />\r\n        ))}\r\n      </AccordionDetails>\r\n    </Accordion>\r\n  );\r\n\r\n  const renderFilterContent = () => (\r\n    <Box\r\n      sx={{\r\n        width: isMobile ? \"90vw\" : { xs: \"90vw\", sm: 300, md: 280, lg: 300 },\r\n        p: {\r\n          xs: \"80px 16px 80px 16px\",\r\n          sm: \"60px 24px 60px 24px\",\r\n          md: \"24px\",\r\n        },\r\n        position: \"relative\",\r\n        // paddingRight: isMobile ? 0 : \"0px\",\r\n      }}\r\n    >\r\n      {isMobile && (\r\n        <IconButton\r\n          onClick={() => setDrawerOpen(false)}\r\n          sx={{ position: \"absolute\", top: 10, right: \"-24px\" }}\r\n        >\r\n          <CloseIcon />\r\n        </IconButton>\r\n      )}\r\n\r\n      <Typography variant=\"h6\" fontWeight=\"bold\" gutterBottom>\r\n        Filters\r\n      </Typography>\r\n\r\n      {isMobile && (\r\n        <TopFilter\r\n          sortOption={sortOption}\r\n          setSortOption={setSortOption}\r\n          onCADFilterChange={onCADFilterChange}\r\n          onSalePriceFilterChange={onSalePriceFilterChange}\r\n          isMobile={true}\r\n          hasCAD={currentFilters.hasCAD}\r\n          hasSalePrice={currentFilters.hasSalePrice}\r\n        />\r\n      )}\r\n\r\n      <Box sx={{ display: \"flex\", flexWrap: \"wrap\", gap: 1, mb: 2 }}>\r\n        {[\"brands\", \"colors\", \"tags\"].map((type) =>\r\n          selectedFilters[type]?.map((item) => (\r\n            <Chip\r\n              key={item}\r\n              label={item}\r\n              onDelete={() => handleFilterChange(type, item)}\r\n            />\r\n          ))\r\n        )}\r\n      </Box>\r\n\r\n      <Button\r\n        onClick={clearFilters}\r\n        size=\"small\"\r\n        color=\"#2d2d2d\"\r\n        sx={{ mb: 2, \"&:hover\": { background: \"transparent\" } }}\r\n      >\r\n        Clear All\r\n      </Button>\r\n\r\n      {renderAccordion(\"Brands\", \"brands\", brands)}\r\n\r\n      <Accordion disableGutters elevation={0} square sx={accordionStyle}>\r\n        <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={summaryStyle}>\r\n          <Typography>Price</Typography>\r\n        </AccordionSummary>\r\n        <AccordionDetails sx={priceDetailsStyle}>\r\n          <Typography variant=\"body2\">\r\n            Range: {selectedFilters.priceRange[0].toLocaleString()} E£ -{\" \"}\r\n            {selectedFilters.priceRange[1].toLocaleString()}E£\r\n          </Typography>\r\n          <Slider\r\n            value={selectedFilters.priceRange}\r\n            onChange={handlePriceChange}\r\n            valueLabelDisplay=\"auto\"\r\n            min={0}\r\n            max={600000}\r\n            sx={sliderStyle}\r\n          />\r\n        </AccordionDetails>\r\n      </Accordion>\r\n\r\n      {renderAccordion(\"Colors\", \"colors\", allColors)}\r\n      {renderAccordion(\"Tags\", \"tags\", allTags)}\r\n    </Box>\r\n  );\r\n\r\n  return isMobile ? (\r\n    <>\r\n      <Button\r\n        onClick={() => setDrawerOpen(true)}\r\n        variant=\"outlined\"\r\n        sx={drawerButtonStyle}\r\n      >\r\n        Show Filters\r\n      </Button>\r\n      <Drawer\r\n        anchor=\"left\"\r\n        open={drawerOpen}\r\n        onClose={() => setDrawerOpen(false)}\r\n      >\r\n        {renderFilterContent()}\r\n      </Drawer>\r\n    </>\r\n  ) : (\r\n    renderFilterContent()\r\n  );\r\n};\r\n\r\nconst accordionStyle = {\r\n  border: \"1px solid #2d2d2d\",\r\n  borderRadius: \"10px\",\r\n  backgroundColor: \"transparent\",\r\n  fontFamily: \"Montserrat\",\r\n  mb: 2,\r\n  \"&:before\": { display: \"none\" },\r\n  \"&:hover\": {\r\n    backgroundColor: \"transparent\",\r\n    color: \"#2d2d2d\",\r\n  },\r\n  \"&.Mui-expanded\": {\r\n    backgroundColor: \"transparent\",\r\n    color: \"#2d2d2d\",\r\n  },\r\n};\r\n\r\nconst summaryStyle = {\r\n  backgroundColor: \"transparent\",\r\n  borderRadius: \"10px\",\r\n  \"& .MuiAccordionSummary-content\": {\r\n    fontFamily: \"Montserrat\",\r\n    fontWeight: \"normal\",\r\n    color: \"#2d2d2d\",\r\n  },\r\n  \"&:hover\": {\r\n    backgroundColor: \"transparent\",\r\n    color: \"#fff\",\r\n  },\r\n  \"&.Mui-expanded\": {\r\n    backgroundColor: \"transparent\",\r\n    color: \"#2d2d2d\",\r\n  },\r\n  \"& .MuiSvgIcon-root\": {\r\n    color: \"#2d2d2d\",\r\n  },\r\n  \"&:hover .MuiSvgIcon-root\": {\r\n    color: \"#fff\",\r\n  },\r\n  \"&.Mui-expanded .MuiSvgIcon-root\": {\r\n    color: \"#2d2d2d\",\r\n  },\r\n};\r\n\r\nconst detailsStyle = {\r\n  display: \"inline-grid\",\r\n  maxHeight: 300,\r\n  overflowY: \"auto\",\r\n\r\n  overflow: \"auto\",\r\n  width: \"100%\",\r\n  fontFamily: \"Montserrat\",\r\n  fontWeight: \"normal\",\r\n};\r\n\r\nconst priceDetailsStyle = {\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"space-between\",\r\n  flexDirection: \"column\",\r\n  gap: 1,\r\n};\r\n\r\nconst sliderStyle = (t) => ({\r\n  color: \"rgba(0,0,0,0.87)\",\r\n  \"& .MuiSlider-track\": {\r\n    border: \"none\",\r\n  },\r\n  \"& .MuiSlider-thumb\": {\r\n    width: 24,\r\n    height: 24,\r\n    backgroundColor: \"#fff\",\r\n    \"&::before\": {\r\n      boxShadow: \"0 4px 8px rgba(0,0,0,0.4)\",\r\n    },\r\n    \"&:hover, &.Mui-focusVisible, &.Mui-active\": {\r\n      boxShadow: \"none\",\r\n    },\r\n  },\r\n  ...t.applyStyles(\"dark\", {\r\n    color: \"#fff\",\r\n  }),\r\n});\r\n\r\nconst drawerButtonStyle = {\r\n  mb: 2,\r\n  mt: 2,\r\n  color: \"#2d2d2d\",\r\n  backgroundColor: \"transparent\",\r\n  border: \"1px solid #2d2d2d\",\r\n  \"&:hover\": { backgroundColor: \"#2d2d2d\", color: \"#fff\" },\r\n};\r\n\r\nexport default FilterSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,aAAa,QACR,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,SAAS,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,MAAMC,aAAa,GAAGA,CAAC;EACrBC,cAAc;EACdC,QAAQ,GAAG,EAAE;EACbC,cAAc;EACdC,UAAU;EACVC,aAAa;EACbC,iBAAiB;EACjBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAACyB,cAAc,CAAC;EACtE,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAMmC,QAAQ,GAAGrB,aAAa,CAAC,mBAAmB,CAAC;EACnD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd+B,kBAAkB,CAACP,cAAc,CAAC;EACpC,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpBxB,SAAS,CAAC,MAAM;IACd,MAAMqC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,0CACF,CAAC;QACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCR,SAAS,CAACO,IAAI,CAAC;MACjB,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEF,GAAG,CAAC;MAC9C;IACF,CAAC;IACDL,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC1C,MAAMC,OAAO,GAAGlB,eAAe,CAACgB,IAAI,CAAC,CAACG,QAAQ,CAACF,KAAK,CAAC,GACjDjB,eAAe,CAACgB,IAAI,CAAC,CAACI,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,KAAK,CAAC,GACtD,CAAC,GAAGjB,eAAe,CAACgB,IAAI,CAAC,EAAEC,KAAK,CAAC;IAErC,MAAMK,UAAU,GAAG;MAAE,GAAGtB,eAAe;MAAE,CAACgB,IAAI,GAAGE;IAAQ,CAAC;IAC1DjB,kBAAkB,CAACqB,UAAU,CAAC;IAC9B9B,cAAc,CAAC8B,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACzC,MAAMH,UAAU,GAAG;MAAE,GAAGtB,eAAe;MAAE0B,UAAU,EAAED;IAAS,CAAC;IAC/DxB,kBAAkB,CAACqB,UAAU,CAAC;IAC9B9B,cAAc,CAAC8B,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAG;MACZ1B,MAAM,EAAE,EAAE;MACV2B,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRJ,UAAU,EAAE,CAAC,CAAC,EAAE,MAAM;IACxB,CAAC;IACDzB,kBAAkB,CAAC2B,KAAK,CAAC;IACzBpC,cAAc,CAACoC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMG,SAAS,GAAGC,KAAK,CAACC,IAAI,CAC1B,IAAIC,GAAG,CAACzC,QAAQ,CAAC0C,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACP,MAAM,IAAI,EAAE,CAAC,CACjD,CAAC;EACD,MAAMQ,OAAO,GAAGL,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACzC,QAAQ,CAAC0C,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACN,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;EAE1E,MAAMQ,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAClCA,KAAK,GAAG,CAAC,GAAG,GAAGD,KAAK,KAAKC,KAAK,GAAG,GAAGD,KAAK;EAE3C,MAAME,eAAe,GAAGA,CAACF,KAAK,EAAEvB,IAAI,EAAE0B,KAAK,kBACzCtD,OAAA,CAACX,SAAS;IAACkE,cAAc;IAACC,SAAS,EAAE,CAAE;IAACC,MAAM;IAACC,EAAE,EAAEC,cAAe;IAAAC,QAAA,gBAChE5D,OAAA,CAACV,gBAAgB;MAACuE,UAAU,eAAE7D,OAAA,CAACJ,cAAc;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAACP,EAAE,EAAEQ,YAAa;MAAAN,QAAA,eACjE5D,OAAA,CAAChB,UAAU;QAAA4E,QAAA,EACRV,cAAc,CAACC,KAAK,EAAEvC,eAAe,CAACgB,IAAI,CAAC,CAACuC,MAAM;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eACnBjE,OAAA,CAACT,gBAAgB;MAACmE,EAAE,EAAEU,YAAa;MAAAR,QAAA,EAChCN,KAAK,CAACe,GAAG,CAAEpC,IAAI,iBACdjC,OAAA,CAACd,gBAAgB;QAEfoF,OAAO,eACLtE,OAAA,CAACf,QAAQ;UACPsF,OAAO,EAAE3D,eAAe,CAACgB,IAAI,CAAC,CAACG,QAAQ,CAACE,IAAI,CAACuC,SAAS,IAAIvC,IAAI,CAAE;UAChEwC,QAAQ,EAAEA,CAAA,KACR9C,kBAAkB,CAACC,IAAI,EAAEK,IAAI,CAACuC,SAAS,IAAIvC,IAAI;QAChD;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACF;QACDd,KAAK,EAAElB,IAAI,CAACuC,SAAS,IAAIvC;MAAK,GATzBA,IAAI,CAACyC,GAAG,IAAIzC,IAAI;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUtB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACZ;EAED,MAAMU,mBAAmB,GAAGA,CAAA,kBAC1B3E,OAAA,CAACjB,GAAG;IACF2E,EAAE,EAAE;MACFkB,KAAK,EAAE5D,QAAQ,GAAG,MAAM,GAAG;QAAE6D,EAAE,EAAE,MAAM;QAAEC,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI,CAAC;MACpEhC,CAAC,EAAE;QACD6B,EAAE,EAAE,qBAAqB;QACzBC,EAAE,EAAE,qBAAqB;QACzBC,EAAE,EAAE;MACN,CAAC;MACDE,QAAQ,EAAE;MACV;IACF,CAAE;IAAArB,QAAA,GAED5C,QAAQ,iBACPhB,OAAA,CAACN,UAAU;MACTwF,OAAO,EAAEA,CAAA,KAAMhE,aAAa,CAAC,KAAK,CAAE;MACpCwC,EAAE,EAAE;QAAEuB,QAAQ,EAAE,UAAU;QAAEE,GAAG,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAQ,CAAE;MAAAxB,QAAA,eAEtD5D,OAAA,CAACH,SAAS;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACb,eAEDjE,OAAA,CAAChB,UAAU;MAACqG,OAAO,EAAC,IAAI;MAACC,UAAU,EAAC,MAAM;MAACC,YAAY;MAAA3B,QAAA,EAAC;IAExD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZjD,QAAQ,iBACPhB,OAAA,CAACF,SAAS;MACRS,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BC,iBAAiB,EAAEA,iBAAkB;MACrCC,uBAAuB,EAAEA,uBAAwB;MACjDM,QAAQ,EAAE,IAAK;MACfwE,MAAM,EAAElF,cAAc,CAACkF,MAAO;MAC9BC,YAAY,EAAEnF,cAAc,CAACmF;IAAa;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CACF,eAEDjE,OAAA,CAACjB,GAAG;MAAC2E,EAAE,EAAE;QAAEgC,OAAO,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,EAC3D,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACS,GAAG,CAAEzC,IAAI;QAAA,IAAAkE,qBAAA;QAAA,QAAAA,qBAAA,GACrClF,eAAe,CAACgB,IAAI,CAAC,cAAAkE,qBAAA,uBAArBA,qBAAA,CAAuBzB,GAAG,CAAEpC,IAAI,iBAC9BjC,OAAA,CAACR,IAAI;UAEH2D,KAAK,EAAElB,IAAK;UACZ8D,QAAQ,EAAEA,CAAA,KAAMpE,kBAAkB,CAACC,IAAI,EAAEK,IAAI;QAAE,GAF1CA,IAAI;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACF,CAAC;MAAA,CACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENjE,OAAA,CAACb,MAAM;MACL+F,OAAO,EAAE3C,YAAa;MACtByD,IAAI,EAAC,OAAO;MACZC,KAAK,EAAC,SAAS;MACfvC,EAAE,EAAE;QAAEmC,EAAE,EAAE,CAAC;QAAE,SAAS,EAAE;UAAEK,UAAU,EAAE;QAAc;MAAE,CAAE;MAAAtC,QAAA,EACzD;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAERZ,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAEvC,MAAM,CAAC,eAE5Cd,OAAA,CAACX,SAAS;MAACkE,cAAc;MAACC,SAAS,EAAE,CAAE;MAACC,MAAM;MAACC,EAAE,EAAEC,cAAe;MAAAC,QAAA,gBAChE5D,OAAA,CAACV,gBAAgB;QAACuE,UAAU,eAAE7D,OAAA,CAACJ,cAAc;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACP,EAAE,EAAEQ,YAAa;QAAAN,QAAA,eACjE5D,OAAA,CAAChB,UAAU;UAAA4E,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACnBjE,OAAA,CAACT,gBAAgB;QAACmE,EAAE,EAAEyC,iBAAkB;QAAAvC,QAAA,gBACtC5D,OAAA,CAAChB,UAAU;UAACqG,OAAO,EAAC,OAAO;UAAAzB,QAAA,GAAC,SACnB,EAAChD,eAAe,CAAC0B,UAAU,CAAC,CAAC,CAAC,CAAC8D,cAAc,CAAC,CAAC,EAAC,UAAK,EAAC,GAAG,EAC/DxF,eAAe,CAAC0B,UAAU,CAAC,CAAC,CAAC,CAAC8D,cAAc,CAAC,CAAC,EAAC,OAClD;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjE,OAAA,CAACZ,MAAM;UACLyC,KAAK,EAAEjB,eAAe,CAAC0B,UAAW;UAClCmC,QAAQ,EAAEtC,iBAAkB;UAC5BkE,iBAAiB,EAAC,MAAM;UACxBC,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,MAAO;UACZ7C,EAAE,EAAE8C;QAAY;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEXZ,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAEV,SAAS,CAAC,EAC9CU,eAAe,CAAC,MAAM,EAAE,MAAM,EAAEJ,OAAO,CAAC;EAAA;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtC,CACN;EAED,OAAOjD,QAAQ,gBACbhB,OAAA,CAAAE,SAAA;IAAA0D,QAAA,gBACE5D,OAAA,CAACb,MAAM;MACL+F,OAAO,EAAEA,CAAA,KAAMhE,aAAa,CAAC,IAAI,CAAE;MACnCmE,OAAO,EAAC,UAAU;MAClB3B,EAAE,EAAE+C,iBAAkB;MAAA7C,QAAA,EACvB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTjE,OAAA,CAACP,MAAM;MACLiH,MAAM,EAAC,MAAM;MACbC,IAAI,EAAE1F,UAAW;MACjB2F,OAAO,EAAEA,CAAA,KAAM1F,aAAa,CAAC,KAAK,CAAE;MAAA0C,QAAA,EAEnCe,mBAAmB,CAAC;IAAC;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA,eACT,CAAC,GAEHU,mBAAmB,CAAC,CACrB;AACH,CAAC;AAAChE,EAAA,CAxMIR,aAAa;EAAA,QAWAR,aAAa;AAAA;AAAAkH,EAAA,GAX1B1G,aAAa;AA0MnB,MAAMwD,cAAc,GAAG;EACrBmD,MAAM,EAAE,mBAAmB;EAC3BC,YAAY,EAAE,MAAM;EACpBC,eAAe,EAAE,aAAa;EAC9BC,UAAU,EAAE,YAAY;EACxBpB,EAAE,EAAE,CAAC;EACL,UAAU,EAAE;IAAEH,OAAO,EAAE;EAAO,CAAC;EAC/B,SAAS,EAAE;IACTsB,eAAe,EAAE,aAAa;IAC9Bf,KAAK,EAAE;EACT,CAAC;EACD,gBAAgB,EAAE;IAChBe,eAAe,EAAE,aAAa;IAC9Bf,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAM/B,YAAY,GAAG;EACnB8C,eAAe,EAAE,aAAa;EAC9BD,YAAY,EAAE,MAAM;EACpB,gCAAgC,EAAE;IAChCE,UAAU,EAAE,YAAY;IACxB3B,UAAU,EAAE,QAAQ;IACpBW,KAAK,EAAE;EACT,CAAC;EACD,SAAS,EAAE;IACTe,eAAe,EAAE,aAAa;IAC9Bf,KAAK,EAAE;EACT,CAAC;EACD,gBAAgB,EAAE;IAChBe,eAAe,EAAE,aAAa;IAC9Bf,KAAK,EAAE;EACT,CAAC;EACD,oBAAoB,EAAE;IACpBA,KAAK,EAAE;EACT,CAAC;EACD,0BAA0B,EAAE;IAC1BA,KAAK,EAAE;EACT,CAAC;EACD,iCAAiC,EAAE;IACjCA,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAM7B,YAAY,GAAG;EACnBsB,OAAO,EAAE,aAAa;EACtBwB,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,MAAM;EAEjBC,QAAQ,EAAE,MAAM;EAChBxC,KAAK,EAAE,MAAM;EACbqC,UAAU,EAAE,YAAY;EACxB3B,UAAU,EAAE;AACd,CAAC;AAED,MAAMa,iBAAiB,GAAG;EACxBT,OAAO,EAAE,MAAM;EACf2B,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE,eAAe;EAC/BC,aAAa,EAAE,QAAQ;EACvB3B,GAAG,EAAE;AACP,CAAC;AAED,MAAMY,WAAW,GAAIgB,CAAC,KAAM;EAC1BvB,KAAK,EAAE,kBAAkB;EACzB,oBAAoB,EAAE;IACpBa,MAAM,EAAE;EACV,CAAC;EACD,oBAAoB,EAAE;IACpBlC,KAAK,EAAE,EAAE;IACT6C,MAAM,EAAE,EAAE;IACVT,eAAe,EAAE,MAAM;IACvB,WAAW,EAAE;MACXU,SAAS,EAAE;IACb,CAAC;IACD,2CAA2C,EAAE;MAC3CA,SAAS,EAAE;IACb;EACF,CAAC;EACD,GAAGF,CAAC,CAACG,WAAW,CAAC,MAAM,EAAE;IACvB1B,KAAK,EAAE;EACT,CAAC;AACH,CAAC,CAAC;AAEF,MAAMQ,iBAAiB,GAAG;EACxBZ,EAAE,EAAE,CAAC;EACL+B,EAAE,EAAE,CAAC;EACL3B,KAAK,EAAE,SAAS;EAChBe,eAAe,EAAE,aAAa;EAC9BF,MAAM,EAAE,mBAAmB;EAC3B,SAAS,EAAE;IAAEE,eAAe,EAAE,SAAS;IAAEf,KAAK,EAAE;EAAO;AACzD,CAAC;AAED,eAAe9F,aAAa;AAAC,IAAA0G,EAAA;AAAAgB,YAAA,CAAAhB,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}