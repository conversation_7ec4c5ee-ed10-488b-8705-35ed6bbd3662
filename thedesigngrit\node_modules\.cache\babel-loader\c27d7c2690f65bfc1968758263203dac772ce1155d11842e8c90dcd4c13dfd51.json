{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\ProductPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Box, Button, useMediaQuery, IconButton } from \"@mui/material\";\nimport { FaStar, FaDownload } from \"react-icons/fa\";\nimport { IoIosArrowBack } from \"react-icons/io\";\nimport { IoIosArrowForward } from \"react-icons/io\";\nimport { IoMdClose } from \"react-icons/io\";\nimport Header from \"../Components/navBar\";\nimport KeyboardArrowDownIcon from \"@mui/icons-material/KeyboardArrowDown\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport ReviewBox from \"../Components/reviewBox\";\nimport RequestInfoPopup from \"../Components/product/optionPopUp\";\nimport Footer from \"../Components/Footer\";\nimport { useCart } from \"../Context/cartcontext\";\nimport LoadingScreen from \"./loadingScreen\";\nimport { UserContext } from \"../utils/userContext\";\nimport RelatedProducts from \"../Components/relatedProducts\";\nimport BrandCursol from \"../Components/brandCursol\";\nimport { BsExclamationOctagon } from \"react-icons/bs\";\nimport RequestQuote from \"../Components/product/RequestInfo\";\nimport ShoppingCartOverlay from \"../Components/Popups/CartOverlay\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProductPage() {\n  _s();\n  var _selectedVariant$imag2, _selectedVariant$imag3;\n  const [showRequestInfoPopup, setShowRequestInfoPopup] = useState(false); // State for Request Info Popup visibility\n  const [isRequestInfoOpen] = useState(true);\n  const {\n    userSession\n  } = useContext(UserContext);\n  const [isFavorite, setIsFavorite] = useState(false);\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n  const [selectedImageIndex, setSelectedImageIndex] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [product, setProduct] = useState(null);\n  const [reviews, setReviews] = useState([]);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [selectedColor, setSelectedColor] = useState(null);\n  const [selectedSize, setSelectedSize] = useState(null);\n  const [expandedSections, setExpandedSections] = useState({});\n  const [expandedMaterialSections, setExpandedMaterialSections] = useState({});\n  const {\n    addToCart\n  } = useCart();\n  const [loading, setLoading] = useState(true); // Loading state for when the product is being fetched\n  const [error, setError] = useState(null); // State for handling errors\n  const [showReviewForm, setShowReviewForm] = useState(false);\n  const [rating, setRating] = useState(0);\n  const [hover, setHover] = useState(0);\n  const [comment, setComment] = useState(\"\");\n  const [reviewerName, setReviewerName] = useState(\"\");\n  // Add this state near your other state declarations\n  const [variants, setVariants] = useState([]);\n  const [selectedVariant, setSelectedVariant] = useState(null);\n  const [activeProduct, setActiveProduct] = useState(null);\n  const [isRequestQuoteOpen, setIsRequestQuoteOpen] = useState(false);\n  const [quoteProduct, setQuoteProduct] = useState(null);\n  const [cartOpen, setCartOpen] = useState(false);\n  const [addedToCart, setAddedToCart] = useState(false);\n  // Add state for validation errors\n  const [validationErrors, setValidationErrors] = useState({\n    color: false,\n    size: false\n  });\n  const handleRequestQuote = productData => {\n    if (!userSession) {\n      navigate(\"/login\");\n      return;\n    }\n\n    // Ensure we have the brand data\n    let dataToPass = productData;\n\n    // If productData doesn't have brandId as an object, use the product's brandId\n    if (!productData.brandId || typeof productData.brandId !== \"object\") {\n      dataToPass = {\n        ...productData,\n        brandId: product.brandId // Use the main product's brandId\n      };\n    }\n    setQuoteProduct(dataToPass);\n    setIsRequestQuoteOpen(true);\n  };\n  const handleCloseRequestQuote = () => {\n    setIsRequestQuoteOpen(false);\n  };\n\n  // Fetch product details by ID\n  useEffect(() => {\n    const fetchProduct = async () => {\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/products/getsingle/${id}`); // Make an API call to fetch the product by ID\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch product details\");\n        }\n        const data = await response.json();\n        setProduct(data); // Set the fetched product to state\n        setActiveProduct(data); // Initialize active product with parent product\n        fetchReviews(data._id);\n      } catch (error) {\n        console.log(error);\n        setError(error.message); // Set error if something goes wrong\n      } finally {\n        setTimeout(() => {\n          setLoading(false);\n        }, 5000);\n      }\n    };\n    fetchProduct(); // Fetch product on component mount\n  }, [id, error, loading, activeProduct]); // Refetch if the ID in the URL changes\n\n  // Move fetchReviews outside useEffect so it can be reused\n  const fetchReviews = async productId => {\n    try {\n      const response = await fetch(`https://api.thedesigngrit.com/api/reviews/reviews/${productId}`);\n      if (!response.ok) {\n        throw new Error(\"Failed to fetch reviews\");\n      }\n      const data = await response.json();\n      setReviews(data); // Assuming the response is an array of reviews\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Add this useEffect to fetch variants when the product loads\n  useEffect(() => {\n    if (product) {\n      const fetchVariants = async () => {\n        try {\n          const response = await fetch(`https://api.thedesigngrit.com/api/product-variants/product/${product._id}`);\n          if (!response.ok) {\n            throw new Error(\"Failed to fetch variants\");\n          }\n          const data = await response.json();\n          setVariants(data);\n        } catch (error) {\n          console.error(\"Error fetching variants:\", error);\n        }\n      };\n      fetchVariants();\n    }\n  }, [product]);\n  // Add this effect to update the selected variant when color/size changes\n  useEffect(() => {\n    if (selectedColor || selectedSize) {\n      const matchingVariant = variants.find(variant => (!selectedColor || variant.color.toLowerCase() === selectedColor.toLowerCase()) && (!selectedSize || variant.size.toLowerCase() === selectedSize.toLowerCase()));\n      setSelectedVariant(matchingVariant || null);\n    } else {\n      setSelectedVariant(null);\n    }\n  }, [selectedColor, selectedSize, variants]);\n  useEffect(() => {\n    const fetchFavorites = async () => {\n      if (!userSession || !product) return; // Ensure both userSession and product are loaded\n\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/favorites/${userSession.id}`);\n        if (response.ok) {\n          const favoritesData = await response.json();\n          const favoriteIds = favoritesData.map(prod => prod._id);\n          setIsFavorite(favoriteIds.includes(product._id));\n        }\n      } catch (error) {\n        console.error(\"Error fetching favorites:\", error);\n      }\n    };\n    fetchFavorites();\n  }, [userSession, product]); // Only run when both userSession and product are ready\n\n  // Toggle the favorite status\n  const toggleFavorite = async event => {\n    event.stopPropagation(); // Prevent triggering card click\n\n    if (!userSession) return; // If there's no user session, prevent posting\n\n    const endpoint = isFavorite ? \"/remove\" : \"/add\";\n    const requestPayload = {\n      userSession,\n      productId: product._id\n    };\n    try {\n      const response = await fetch(`https://api.thedesigngrit.com/api/favorites${endpoint}`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(requestPayload)\n      });\n      if (response.ok) {\n        setIsFavorite(!isFavorite); // Toggle the favorite status if successful\n      } else {\n        console.error(\"Error: Unable to update favorite status.\");\n      }\n    } catch (error) {\n      console.error(\"Error:\", error);\n    }\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(LoadingScreen, {\n    onComplete: () => setLoading(false)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 23\n  }, this);\n  if (!product) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Product not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 24\n  }, this);\n  // Fetch the user's favorite products on component mount\n\n  const handleImageClick = index => {\n    setSelectedImageIndex(index);\n    setIsTransitioning(true);\n    setTimeout(() => setIsModalOpen(true), 300);\n  };\n  const handleCloseModal = () => {\n    setIsTransitioning(false);\n    setTimeout(() => {\n      setIsModalOpen(false);\n      setSelectedImageIndex(null);\n    }, 300);\n  };\n  const handlePrevImage = () => {\n    setSelectedImageIndex(prev => (prev - 1 + product.images.length) % product.images.length);\n  };\n  const handleNextImage = () => {\n    setSelectedImageIndex(prev => (prev + 1) % product.images.length);\n  };\n  const handleToggleSection = (index, type = \"general\") => {\n    if (type === \"general\") {\n      setExpandedSections(prev => ({\n        ...prev,\n        [index]: !prev[index]\n      }));\n    } else {\n      setExpandedMaterialSections(prev => ({\n        ...prev,\n        [index]: !prev[index]\n      }));\n    }\n  };\n  // Modify your color and size selection handlers\n  const handleColorSelect = color => {\n    setSelectedColor(color);\n    // Reset size when color changes since size options might be different\n    setSelectedSize(null);\n  };\n  const handleSizeSelect = size => {\n    setSelectedSize(size);\n  };\n\n  // Create a function to get available sizes for the selected color\n  const getAvailableSizes = () => {\n    if (!selectedColor || !variants || variants.length === 0) return product.sizes || [];\n    const colorVariants = variants.filter(variant => variant.color && variant.color.toLowerCase() === selectedColor.toLowerCase());\n    const uniqueSizes = [...new Set(colorVariants.map(v => v.size).filter(Boolean))];\n    return uniqueSizes.length > 0 ? uniqueSizes : product.sizes || [];\n  };\n\n  // Update your product display to use variant data when available\n  // const displayProduct = selectedVariant || product;\n  const displayImages = (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.images) || product.images;\n  const displayTitle = (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.title) || product.name;\n  const handleSectionToggle = index => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [index]: !prev[index]\n    }));\n  };\n  const handleAddToCart = () => {\n    var _product$colors;\n    if (product.stock <= 0) return;\n    // Validate color and size selection\n    const errors = {\n      color: ((_product$colors = product.colors) === null || _product$colors === void 0 ? void 0 : _product$colors.length) > 0 && !selectedColor,\n      size: getAvailableSizes().length > 0 && !selectedSize\n    };\n    setValidationErrors(errors);\n\n    // If there are validation errors, don't proceed\n    if (errors.color || errors.size) {\n      return;\n    }\n\n    // Determine if we're adding a variant or the main product\n    if (selectedVariant) {\n      var _selectedVariant$imag;\n      // Adding a variant\n      // Get the shipping fee safely\n      let shippingFee = 0;\n      if (product.brandId && typeof product.brandId === \"object\" && product.brandId.fees) {\n        shippingFee = product.brandId.fees;\n      }\n      addToCart({\n        id: selectedVariant._id,\n        // Variant ID as the main ID\n        variantId: selectedVariant._id,\n        // Same ID to identify it as a variant\n        productId: product._id,\n        // Parent product ID\n        name: selectedVariant.title || product.name,\n        unitPrice: selectedVariant.salePrice || selectedVariant.price || 0,\n        quantity: 1,\n        image: ((_selectedVariant$imag = selectedVariant.images) === null || _selectedVariant$imag === void 0 ? void 0 : _selectedVariant$imag[0]) || product.mainImage,\n        brandId: product.brandId,\n        color: selectedColor || \"default\",\n        size: selectedSize || \"default\",\n        code: selectedVariant.sku || product.sku || \"N/A\",\n        shippingFee: shippingFee || 0\n      });\n    } else {\n      // Adding the main product\n      let shippingFee = 0;\n      if (product.brandId && typeof product.brandId === \"object\" && product.brandId.fees) {\n        shippingFee = product.brandId.fees;\n      }\n      addToCart({\n        id: product._id,\n        name: product.name,\n        unitPrice: product.salePrice || product.price || 0,\n        quantity: 1,\n        image: product.mainImage,\n        brandId: product.brandId,\n        color: selectedColor || \"default\",\n        size: selectedSize || \"default\",\n        code: product.sku || \"N/A\",\n        shippingFee: shippingFee || 0\n      });\n    }\n\n    // Show cart overlay\n    setCartOpen(true);\n\n    // Show notification\n    setAddedToCart(true);\n\n    // Hide notification after 3 seconds\n    setTimeout(() => {\n      setAddedToCart(false);\n    }, 3000);\n  };\n\n  // Add this function to close the cart overlay\n  const handleCloseCart = () => {\n    setCartOpen(false);\n  };\n\n  //Review Function Post\n  const handleSubmitReview = async e => {\n    e.preventDefault();\n    try {\n      const response = await fetch(`https://api.thedesigngrit.com/api/reviews/createreviews/${product._id}`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          reviewerName,\n          userId: userSession.id,\n          rating,\n          comment\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\"Failed to submit review\");\n      }\n\n      // Update reviews in state robustly\n      const result = await response.json();\n      if (Array.isArray(result.reviews)) {\n        setReviews(result.reviews);\n      } else if (result && result._id && result.comment) {\n        setReviews(prev => [...prev, result]);\n      } else {\n        // Fallback: refetch reviews from the server\n        fetchReviews(product._id);\n      }\n\n      // Reset form\n      setShowReviewForm(false);\n      setRating(0);\n      setComment(\"\");\n      setReviewerName(\"\");\n    } catch (error) {\n      console.error(\"Error submitting review:\", error);\n    }\n  };\n  const ratingBreakdown = [5, 4, 3, 2, 1].map(stars => {\n    const count = reviews.filter(r => r.rating === stars).length;\n    return {\n      stars,\n      count\n    };\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-page\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-image-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${(selectedVariant === null || selectedVariant === void 0 ? void 0 : (_selectedVariant$imag2 = selectedVariant.images) === null || _selectedVariant$imag2 === void 0 ? void 0 : _selectedVariant$imag2[0]) || product.mainImage}`,\n            alt: displayTitle,\n            className: \"product-main-image\",\n            onClick: () => handleImageClick(0)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"thumbnail-container\",\n            children: displayImages && displayImages.length > 0 ? displayImages.map((image, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}`,\n              alt: `Thumbnail ${index + 1}`,\n              className: \"thumbnail-image\",\n              onClick: () => handleImageClick(index)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No thumbnails available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              flexDirection: \"row\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"product-title\",\n              children: selectedVariant ? selectedVariant.title : product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), selectedVariant && /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                marginBottom: \"8px\",\n                fontWeight: \"light\",\n                color: \"#ccc\"\n              },\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                marginLeft: \"8px\",\n                \"&:hover\": {\n                  backgroundColor: \"#f0f0f0\"\n                }\n              },\n              onClick: event => {\n                event.stopPropagation();\n                toggleFavorite(event);\n              },\n              children: isFavorite ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {\n                sx: {\n                  color: \"red\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n                sx: {\n                  color: \"#000\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"product-brand\",\n            children: product.brandId.brandName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), product.readyToShip === true && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"inline-block\",\n              padding: \"4px 12px\",\n              border: \"1px solid #2d2d2d\",\n              borderRadius: \"4px\",\n              marginTop: \"8px\",\n              marginBottom: \"8px\",\n              fontSize: \"14px\",\n              fontFamily: \"Montserrat\"\n            },\n            children: \"Ready to Ship\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this), product.stock === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"inline-block\",\n              padding: \"4px 12px\",\n              border: \"1px solid #2d2d2d\",\n              borderRadius: \"4px\",\n              marginTop: \"8px\",\n              marginBottom: \"8px\",\n              fontSize: \"14px\",\n              fontFamily: \"Montserrat\",\n              backgroundColor: \"#DD4A2A\",\n              color: \"#fff\",\n              boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\"\n            },\n            children: \"SOLD OUT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this) : product.stock <= 5 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"inline-block\",\n              padding: \"4px 12px\",\n              border: \"1px solid #2d2d2d\",\n              borderRadius: \"4px\",\n              marginTop: \"8px\",\n              marginBottom: \"8px\",\n              fontSize: \"14px\",\n              fontFamily: \"Montserrat\",\n              backgroundColor: \"#FFAC1C\",\n              color: \"#fff\",\n              boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\",\n              animation: \"pulse 1.5s infinite\",\n              \"@keyframes pulse\": {\n                \"0%\": {\n                  transform: \"scale(1)\",\n                  opacity: 1\n                },\n                \"50%\": {\n                  transform: \"scale(1.05)\",\n                  opacity: 0.8\n                },\n                \"100%\": {\n                  transform: \"scale(1)\",\n                  opacity: 1\n                }\n              }\n            },\n            children: \"HURRY UP!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 15\n          }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              flexDirection: \"row\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"product-rating\",\n              children: reviews.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\"★\".repeat(Math.round(reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length)), \" ( of \" + reviews.length + \" reviews)\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"No reviews yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                display: product.discountPercentage ? \"block\" : \"none\",\n                alignSelf: \"end\"\n              },\n              children: product.discountPercentage ? `${product.discountPercentage}% off` : /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"none\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"product-price\",\n            children: selectedVariant ?\n            // Show only variant pricing when a variant is selected\n            selectedVariant.salePrice ?\n            /*#__PURE__*/\n            // Variant has sale price\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  textDecoration: \"line-through\",\n                  color: \"gray\",\n                  marginRight: \"8px\"\n                },\n                children: [selectedVariant.price > 1000 ? new Intl.NumberFormat(\"en-US\").format(selectedVariant.price) : selectedVariant.price, \".00 E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"red\",\n                  fontWeight: \"bold\"\n                },\n                children: [selectedVariant.salePrice > 1000 ? new Intl.NumberFormat(\"en-US\").format(selectedVariant.salePrice) : selectedVariant.salePrice, \".00 E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Variant has only regular price\n            _jsxDEV(_Fragment, {\n              children: [selectedVariant.price > 1000 ? new Intl.NumberFormat(\"en-US\").format(selectedVariant.price) : selectedVariant.price, \".00 E\\xA3\"]\n            }, void 0, true) :\n            // Show parent product pricing when no variant is selected\n            product.salePrice && product.promotionApproved === true ?\n            /*#__PURE__*/\n            // Parent product has sale price\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  textDecoration: \"line-through\",\n                  color: \"gray\",\n                  marginRight: \"8px\"\n                },\n                children: [product.price > 1000 ? new Intl.NumberFormat(\"en-US\").format(product.price) : product.price, \".00 E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"red\",\n                  fontWeight: \"bold\"\n                },\n                children: [product.salePrice > 1000 ? new Intl.NumberFormat(\"en-US\").format(product.salePrice) : product.salePrice, \".00 E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Parent product has only regular price\n            _jsxDEV(_Fragment, {\n              children: [product.price > 1000 ? new Intl.NumberFormat(\"en-US\").format(product.price) : product.price, \".00 E\\xA3\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"color-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"color-selector-label\",\n              children: \"Color:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"color-options\",\n              children: product.colors && product.colors.length > 0 ? product.colors.map((color, index) => {\n                // Basic color extraction function\n                const extractColorValue = colorName => {\n                  // Convert to lowercase for comparison\n                  const lowerColor = colorName.toLowerCase();\n\n                  // Basic color map for common colors\n                  const basicColorMap = {\n                    white: \"#FFFFFF\",\n                    black: \"#000000\",\n                    red: \"#FF0000\",\n                    green: \"#008000\",\n                    blue: \"#0000FF\",\n                    yellow: \"#FFFF00\",\n                    purple: \"#800080\",\n                    orange: \"#FFA500\",\n                    pink: \"#FFC0CB\",\n                    brown: \"#A52A2A\",\n                    gray: \"#808080\",\n                    grey: \"#808080\",\n                    beige: \"#F5F5DC\",\n                    cream: \"#FFFDD0\",\n                    gold: \"#FFD700\",\n                    silver: \"#C0C0C0\",\n                    navy: \"#000080\",\n                    olive: \"#808000\",\n                    maroon: \"#800000\",\n                    teal: \"#008080\",\n                    tan: \"#D2B48C\",\n                    coral: \"#FF7F50\",\n                    sage: \"#BCB88A\",\n                    charcoal: \"#36454F\"\n                  };\n\n                  // Try exact match first\n                  if (basicColorMap[lowerColor]) {\n                    return basicColorMap[lowerColor];\n                  }\n\n                  // Try to extract a basic color from the name\n                  for (const [basicColor, hexValue] of Object.entries(basicColorMap)) {\n                    if (lowerColor.includes(basicColor)) {\n                      return hexValue;\n                    }\n                  }\n\n                  // If no match found, use a neutral gray with the color name displayed\n                  return \"#CCCCCC\";\n                };\n\n                // Get color value\n                const colorValue = extractColorValue(color);\n\n                // Check if color is light\n                const isLightColor = colorValue === \"#FFFFFF\" || colorValue === \"#F5F5DC\" || colorValue === \"#FFFDD0\" || color.toLowerCase().includes(\"white\") || color.toLowerCase().includes(\"cream\") || color.toLowerCase().includes(\"beige\") || color.toLowerCase().includes(\"ivory\") || color.toLowerCase().includes(\"off white\") || color.toLowerCase().includes(\"offwhite\");\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `color-circle ${selectedColor === color ? \"selected\" : \"\"}`,\n                  style: {\n                    backgroundColor: colorValue,\n                    border: isLightColor ? \"1px solid #2d2d2d\" : \"none\",\n                    position: \"relative\"\n                  },\n                  title: color,\n                  onClick: () => handleColorSelect(color),\n                  children: colorValue === \"#CCCCCC\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-name-overlay\",\n                    children: color.charAt(0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 27\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 23\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"This product is only available in one color, so you don't have to worry about choosing the perfect shade!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this), selectedColor ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"selected-color-text\",\n            children: [\"Selected Color: \", selectedColor]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this) : validationErrors.color ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"selected-color-text\",\n            style: {\n              color: \"red\"\n            },\n            children: \"Please select a color\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 15\n          }, this) : null, /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"size-selector\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"size-selector-label\",\n              children: \"Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"size-options\",\n              children: getAvailableSizes() && getAvailableSizes().length > 0 ? getAvailableSizes().map((size, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `size-button ${selectedSize === size ? \"selected\" : \"\"}`,\n                onClick: () => handleSizeSelect(size),\n                children: size\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 21\n              }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No size options available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this), selectedSize ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Selected Size: \", selectedSize]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 15\n          }, this) : validationErrors.size ? /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: \"red\"\n            },\n            children: \"Please select a size\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 15\n          }, this) : null, \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"action-buttons\",\n            children: [product.stock <= 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-button disabled-text\",\n              style: {\n                cursor: \"not-allowed\",\n                pointerEvents: \"none\",\n                backgroundColor: \"#f0f0f0\",\n                border: \"1px dashed #6b7b58\",\n                color: \"#6b7b58\",\n                width: \"100%\",\n                textAlign: \"center\"\n              },\n              children: \"Sold Out !\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button button-primary\",\n              onClick: () => handleAddToCart(product),\n              children: \"Add to Cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-button button-secondary\",\n              onClick: () => setShowRequestInfoPopup(true) // Open Request Info Popup\n              ,\n              children: \"Request Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this), isRequestInfoOpen && /*#__PURE__*/_jsxDEV(RequestInfoPopup, {\n            open: showRequestInfoPopup,\n            onClose: () => setShowRequestInfoPopup(false),\n            productId: product // Pass productId here\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"collapsible-container\",\n          children: [\"Overview\", \"Dimensions\", \"BIM/CAD\", \"Tags\"].map((section, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `collapsible-section ${expandedSections[index] ? \"open\" : \"\"}`,\n            onClick: () => handleSectionToggle(index),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"collapsible-header\",\n              children: [section, /*#__PURE__*/_jsxDEV(KeyboardArrowDownIcon, {\n                className: `collapsible-icon ${expandedSections[index] ? \"rotated\" : \"\"}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"collapsible-content\",\n              children: [section === \"Overview\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-contents\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      fontSize: isMobile ? \"13px\" : \"20px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"label\",\n                      children: \"Collection:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 29\n                    }, this), product.collection]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      fontSize: isMobile ? \"13px\" : \"20px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"label\",\n                      children: \"Manufacturer Year:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 899,\n                      columnNumber: 29\n                    }, this), \" \", product.manufactureYear]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: isMobile ? \"13px\" : \"20px\",\n                    textAlign: \"justify\"\n                  },\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 23\n              }, this), section === \"Dimensions\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-contents\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Width X Length X Height\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedVariant ? selectedVariant.dimensions.width : product.technicalDimensions.width\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 27\n                  }, this), \" \", \"cm x\", \"  \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [\" \", selectedVariant ? selectedVariant.dimensions.length : product.technicalDimensions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 27\n                  }, this), \" \", \"cm X\", \"  \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedVariant ? selectedVariant.dimensions.height : product.technicalDimensions.height\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 27\n                  }, this), \" \", \"cm\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Weight :\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedVariant ? selectedVariant.dimensions.weight : product.technicalDimensions.weight\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 27\n                  }, this), \" \", \"Kgs\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 23\n              }, this), section === \"BIM/CAD\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-contents\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  sx: {\n                    backgroundColor: \"transparent\",\n                    color: \"#2d2d2d\",\n                    borderRadius: \"10px\",\n                    border: \"1px solid #2d2d2d\",\n                    width: \"40%\",\n                    padding: \"10px 20px\",\n                    minWidth: \"150px\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    textTransform: \"none\",\n                    \"&:hover\": {\n                      backgroundColor: \"#000\",\n                      color: \"#fff\"\n                    }\n                  },\n                  onClick: () => {\n                    if (product.cadFile) {\n                      // Create a temporary anchor element\n                      const link = document.createElement(\"a\");\n                      link.href = `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.cadFile}`; // Add the URL prefix\n                      link.download = `product_cad_${product._id}`; // Suggested filename\n                      document.body.appendChild(link);\n                      link.click();\n                      document.body.removeChild(link);\n                    } else {\n                      alert(\"No CAD file available for download\");\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/Assets/autocadIcon.webp\",\n                    alt: \"AutoCAD Logo\",\n                    style: {\n                      width: \"24px\",\n                      height: \"24px\",\n                      marginRight: \"10px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Download CAD File\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(FaDownload, {\n                    style: {\n                      marginLeft: \"10px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 23\n              }, this), section === \"Tags\" && product.tags && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"span-container\",\n                children: product.tags.length > 0 ? product.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: tag\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1018,\n                  columnNumber: 29\n                }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"No tags available.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"right-side-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"material-collapsible-container\",\n            children: [\"Delivery & Returns\", \"Care Instructions\", \"Product Specifications\"].map((section, index) => {\n              var _product$leadTime, _product$Estimatedtim;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"material-collapsible-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-collapsible-header\",\n                  onClick: () => handleToggleSection(index, \"material\"),\n                  children: [section, /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-collapsible-icon\",\n                    children: expandedMaterialSections[index] ? \"-\" : \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 19\n                }, this), expandedMaterialSections[index] && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-collapsible-content\",\n                  children: section === \"Delivery & Returns\" ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [(_product$leadTime = product.leadTime) === null || _product$leadTime === void 0 ? void 0 : _product$leadTime.split(/(?<=\\w)\\s(?=[A-Z])/).map((point, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\" \", \"Lead Time:\", point, \" Business Days\"]\n                    }, idx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1067,\n                      columnNumber: 31\n                    }, this)), \" \", (_product$Estimatedtim = product.Estimatedtimeleadforcustomization) === null || _product$Estimatedtim === void 0 ? void 0 : _product$Estimatedtim.split(/(?<=\\w)\\s(?=[A-Z])/).map((point, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"Estimated Time for Customization:\", point, \" Business Days\"]\n                    }, idx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1075,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 25\n                  }, this) : section === \"Care Instructions\" ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: product.materialCareInstructions.split(\"\\n\").map((point, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: point\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1086,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1082,\n                    columnNumber: 25\n                  }, this) : section === \"Product Specifications\" ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: product.productSpecificRecommendations.split(\"\\n\").map((point, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: point.trim()\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1094,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 25\n                  }, this) : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 17\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1044,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"brand-cursol\",\n            children: /*#__PURE__*/_jsxDEV(BrandCursol, {\n              brandId: product.brandId,\n              onRequestQuote: handleRequestQuote,\n              mainProductId: product._id // Pass the main product ID\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1030,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this), (isModalOpen || isTransitioning) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `modal ${isTransitioning ? \"opening\" : \"closing\"}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close\",\n            onClick: handleCloseModal,\n            children: /*#__PURE__*/_jsxDEV(IoMdClose, {\n              size: 30,\n              color: \"#fff\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-prev\",\n            onClick: handlePrevImage,\n            children: /*#__PURE__*/_jsxDEV(IoIosArrowBack, {\n              size: 30,\n              color: \"#fff\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.images[selectedImageIndex]}`,\n            alt: `${selectedImageIndex + 1}`,\n            className: \"modal-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-next\",\n            onClick: handleNextImage,\n            children: /*#__PURE__*/_jsxDEV(IoIosArrowForward, {\n              size: 30,\n              color: \"#fff\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1118,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1117,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        style: {\n          width: \"90%\",\n          textAlign: \"center\",\n          margin: \"auto\",\n          marginTop: \"30px\",\n          marginBottom: \"20px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-section\",\n        style: {\n          padding: \"0 3rem\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Related Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RelatedProducts, {\n          productId: product._id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Reviews\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowReviewForm(true),\n            className: \"write-review-btn\",\n            children: \"Write a Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1150,\n          columnNumber: 11\n        }, this), showReviewForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"review-form-overlay\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"review-form-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"close-form-btn\",\n              onClick: () => setShowReviewForm(false),\n              style: {\n                backgroundColor: hover ? \"transparent\" : \"transparent\"\n              },\n              onMouseEnter: () => setHover(true),\n              onMouseLeave: () => setHover(false),\n              children: /*#__PURE__*/_jsxDEV(IoMdClose, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Write a Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmitReview,\n              className: \"review-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Your Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: reviewerName,\n                  onChange: e => setReviewerName(e.target.value),\n                  required: true,\n                  className: \"review-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"star-rating\",\n                  children: [...Array(5)].map((_, index) => {\n                    const ratingValue = index + 1;\n                    return /*#__PURE__*/_jsxDEV(FaStar, {\n                      className: \"star\",\n                      color: ratingValue <= (hover || rating) ? \"#ffc107\" : \"#e4e5e9\",\n                      size: 24,\n                      onClick: () => setRating(ratingValue),\n                      onMouseEnter: () => setHover(ratingValue),\n                      onMouseLeave: () => setHover(rating),\n                      style: {\n                        cursor: \"pointer\"\n                      }\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1193,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Your Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: comment,\n                  onChange: e => setComment(e.target.value),\n                  required: true,\n                  className: \"review-textarea\",\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1214,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"submit-review-btn\",\n                children: \"Submit Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1162,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            paddingLeft: \"1rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"review-summary\",\n            children: /*#__PURE__*/_jsxDEV(ReviewBox, {\n              reviewsData: ratingBreakdown\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 13\n          }, this), reviews.length > 0 ? reviews.map((review, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"review-card\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"review-subtitle\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: review.reviewerName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1239,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: review.reviewDate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1238,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"★\".repeat(review.rating)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1242,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: review.comment\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1237,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: \"center\",\n              marginTop: \"20px\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              gap: \"16px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(BsExclamationOctagon, {\n              size: 50,\n              color: \"#ccc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"no-reviews\",\n              children: \"No reviews yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1265,\n      columnNumber: 7\n    }, this), isRequestQuoteOpen && /*#__PURE__*/_jsxDEV(RequestQuote, {\n      onClose: handleCloseRequestQuote,\n      productId: quoteProduct\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1267,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ShoppingCartOverlay, {\n      open: cartOpen,\n      onClose: handleCloseCart\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1273,\n      columnNumber: 7\n    }, this), addedToCart && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"added-to-cart-notification\",\n      style: {\n        position: \"fixed\",\n        bottom: \"20px\",\n        right: \"20px\",\n        backgroundColor: \"#6B7B58\",\n        color: \"white\",\n        padding: \"15px 20px\",\n        borderRadius: \"8px\",\n        boxShadow: \"0 4px 8px rgba(0,0,0,0.2)\",\n        zIndex: 1000,\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: \"10px\",\n        animation: \"slideIn 0.3s ease-out\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"10px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${(selectedVariant === null || selectedVariant === void 0 ? void 0 : (_selectedVariant$imag3 = selectedVariant.images) === null || _selectedVariant$imag3 === void 0 ? void 0 : _selectedVariant$imag3[0]) || product.mainImage}`,\n          alt: product.name,\n          style: {\n            width: \"40px\",\n            height: \"40px\",\n            objectFit: \"cover\",\n            borderRadius: \"4px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1296,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: \"bold\"\n            },\n            children: \"Added to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: \"14px\"\n            },\n            children: selectedVariant ? selectedVariant.title : product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1295,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1277,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 433,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductPage, \"SBF1/o8nfXIz1grjdPp0Ch8Bp/Y=\", false, function () {\n  return [useMediaQuery, useParams, useNavigate, useCart];\n});\n_c = ProductPage;\nexport default ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Box", "<PERSON><PERSON>", "useMediaQuery", "IconButton", "FaStar", "FaDownload", "IoIosArrowBack", "IoIosArrowForward", "IoMdClose", "Header", "KeyboardArrowDownIcon", "useParams", "useNavigate", "ReviewBox", "RequestInfoPopup", "Footer", "useCart", "LoadingScreen", "UserContext", "RelatedProducts", "BrandCursol", "BsExclamationOctagon", "RequestQuote", "ShoppingCartOverlay", "FavoriteBorderIcon", "FavoriteIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductPage", "_s", "_selectedVariant$imag2", "_selectedVariant$imag3", "showRequestInfoPopup", "setShowRequestInfoPopup", "isRequestInfoOpen", "userSession", "isFavorite", "setIsFavorite", "isMobile", "selectedImageIndex", "setSelectedImageIndex", "isModalOpen", "setIsModalOpen", "isTransitioning", "setIsTransitioning", "product", "setProduct", "reviews", "setReviews", "id", "navigate", "selectedColor", "setSelectedColor", "selectedSize", "setSelectedSize", "expandedSections", "setExpandedSections", "expandedMaterialSections", "setExpandedMaterialSections", "addToCart", "loading", "setLoading", "error", "setError", "showReviewForm", "setShowReviewForm", "rating", "setRating", "hover", "setHover", "comment", "setComment", "reviewerName", "setReviewerName", "variants", "setVariants", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVariant", "activeProduct", "setActiveProduct", "isRequestQuoteOpen", "setIsRequestQuoteOpen", "quoteProduct", "setQuoteProduct", "cartOpen", "setCartOpen", "addedToCart", "setAddedToCart", "validationErrors", "setValidationErrors", "color", "size", "handleRequestQuote", "productData", "dataToPass", "brandId", "handleCloseRequestQuote", "fetchProduct", "response", "fetch", "ok", "Error", "data", "json", "fetchReviews", "_id", "console", "log", "message", "setTimeout", "productId", "fetchVariants", "matching<PERSON><PERSON><PERSON>", "find", "variant", "toLowerCase", "fetchFavorites", "favoritesData", "favoriteIds", "map", "prod", "includes", "toggleFavorite", "event", "stopPropagation", "endpoint", "requestPayload", "method", "headers", "body", "JSON", "stringify", "onComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "handleImageClick", "index", "handleCloseModal", "handlePrevImage", "prev", "images", "length", "handleNextImage", "handleToggleSection", "type", "handleColorSelect", "handleSizeSelect", "getAvailableSizes", "sizes", "colorVariants", "filter", "uniqueSizes", "Set", "v", "Boolean", "displayImages", "displayTitle", "title", "name", "handleSectionToggle", "handleAddToCart", "_product$colors", "stock", "errors", "colors", "_selectedVariant$imag", "shippingFee", "fees", "variantId", "unitPrice", "salePrice", "price", "quantity", "image", "mainImage", "code", "sku", "handleCloseCart", "handleSubmitReview", "e", "preventDefault", "userId", "result", "Array", "isArray", "ratingBreakdown", "stars", "count", "r", "className", "src", "alt", "onClick", "style", "display", "flexDirection", "alignItems", "justifyContent", "marginBottom", "fontWeight", "sx", "marginLeft", "backgroundColor", "brandName", "readyToShip", "padding", "border", "borderRadius", "marginTop", "fontSize", "fontFamily", "boxShadow", "animation", "transform", "opacity", "repeat", "Math", "round", "reduce", "acc", "review", "discountPercentage", "alignSelf", "textDecoration", "marginRight", "Intl", "NumberFormat", "format", "promotionApproved", "extractColorValue", "colorName", "lowerColor", "basicColorMap", "white", "black", "red", "green", "blue", "yellow", "purple", "orange", "pink", "brown", "gray", "grey", "beige", "cream", "gold", "silver", "navy", "olive", "maroon", "teal", "tan", "coral", "sage", "charcoal", "basicColor", "hexValue", "Object", "entries", "colorValue", "isLightColor", "position", "char<PERSON>t", "cursor", "pointerEvents", "width", "textAlign", "open", "onClose", "section", "collection", "manufactureYear", "description", "dimensions", "technicalDimensions", "height", "weight", "min<PERSON><PERSON><PERSON>", "textTransform", "cadFile", "link", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "alert", "tags", "tag", "_product$leadTime", "_product$Estimatedtim", "leadTime", "split", "point", "idx", "Estimatedtimeleadforcustomization", "materialCareInstructions", "productSpecificRecommendations", "trim", "onRequestQuote", "mainProductId", "margin", "onMouseEnter", "onMouseLeave", "onSubmit", "value", "onChange", "target", "required", "_", "ratingValue", "rows", "paddingLeft", "reviewsData", "reviewDate", "gap", "bottom", "right", "zIndex", "objectFit", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/ProductPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport { Box, Button, useMediaQuery, IconButton } from \"@mui/material\";\r\nimport { FaStar, FaDownload } from \"react-icons/fa\";\r\nimport { IoIosArrowBack } from \"react-icons/io\";\r\nimport { IoIosArrowForward } from \"react-icons/io\";\r\nimport { IoMdClose } from \"react-icons/io\";\r\nimport Header from \"../Components/navBar\";\r\nimport KeyboardArrowDownIcon from \"@mui/icons-material/KeyboardArrowDown\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport ReviewBox from \"../Components/reviewBox\";\r\nimport RequestInfoPopup from \"../Components/product/optionPopUp\";\r\nimport Footer from \"../Components/Footer\";\r\nimport { useCart } from \"../Context/cartcontext\";\r\nimport LoadingScreen from \"./loadingScreen\";\r\nimport { UserContext } from \"../utils/userContext\";\r\nimport RelatedProducts from \"../Components/relatedProducts\";\r\nimport BrandCursol from \"../Components/brandCursol\";\r\nimport { BsExclamationOctagon } from \"react-icons/bs\";\r\nimport RequestQuote from \"../Components/product/RequestInfo\";\r\nimport ShoppingCartOverlay from \"../Components/Popups/CartOverlay\";\r\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\n\r\nfunction ProductPage() {\r\n  const [showRequestInfoPopup, setShowRequestInfoPopup] = useState(false); // State for Request Info Popup visibility\r\n  const [isRequestInfoOpen] = useState(true);\r\n  const { userSession } = useContext(UserContext);\r\n  const [isFavorite, setIsFavorite] = useState(false);\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n  const [selectedImageIndex, setSelectedImageIndex] = useState(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [isTransitioning, setIsTransitioning] = useState(false);\r\n  const [product, setProduct] = useState(null);\r\n  const [reviews, setReviews] = useState([]);\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const [selectedColor, setSelectedColor] = useState(null);\r\n  const [selectedSize, setSelectedSize] = useState(null);\r\n  const [expandedSections, setExpandedSections] = useState({});\r\n  const [expandedMaterialSections, setExpandedMaterialSections] = useState({});\r\n  const { addToCart } = useCart();\r\n  const [loading, setLoading] = useState(true); // Loading state for when the product is being fetched\r\n  const [error, setError] = useState(null); // State for handling errors\r\n  const [showReviewForm, setShowReviewForm] = useState(false);\r\n  const [rating, setRating] = useState(0);\r\n  const [hover, setHover] = useState(0);\r\n  const [comment, setComment] = useState(\"\");\r\n  const [reviewerName, setReviewerName] = useState(\"\");\r\n  // Add this state near your other state declarations\r\n  const [variants, setVariants] = useState([]);\r\n  const [selectedVariant, setSelectedVariant] = useState(null);\r\n  const [activeProduct, setActiveProduct] = useState(null);\r\n  const [isRequestQuoteOpen, setIsRequestQuoteOpen] = useState(false);\r\n  const [quoteProduct, setQuoteProduct] = useState(null);\r\n  const [cartOpen, setCartOpen] = useState(false);\r\n  const [addedToCart, setAddedToCart] = useState(false);\r\n  // Add state for validation errors\r\n  const [validationErrors, setValidationErrors] = useState({\r\n    color: false,\r\n    size: false,\r\n  });\r\n\r\n  const handleRequestQuote = (productData) => {\r\n    if (!userSession) {\r\n      navigate(\"/login\");\r\n      return;\r\n    }\r\n\r\n    // Ensure we have the brand data\r\n    let dataToPass = productData;\r\n\r\n    // If productData doesn't have brandId as an object, use the product's brandId\r\n    if (!productData.brandId || typeof productData.brandId !== \"object\") {\r\n      dataToPass = {\r\n        ...productData,\r\n        brandId: product.brandId, // Use the main product's brandId\r\n      };\r\n    }\r\n\r\n    setQuoteProduct(dataToPass);\r\n    setIsRequestQuoteOpen(true);\r\n  };\r\n\r\n  const handleCloseRequestQuote = () => {\r\n    setIsRequestQuoteOpen(false);\r\n  };\r\n\r\n  // Fetch product details by ID\r\n  useEffect(() => {\r\n    const fetchProduct = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/products/getsingle/${id}`\r\n        ); // Make an API call to fetch the product by ID\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch product details\");\r\n        }\r\n        const data = await response.json();\r\n        setProduct(data); // Set the fetched product to state\r\n        setActiveProduct(data); // Initialize active product with parent product\r\n        fetchReviews(data._id);\r\n      } catch (error) {\r\n        console.log(error);\r\n        setError(error.message); // Set error if something goes wrong\r\n      } finally {\r\n        setTimeout(() => {\r\n          setLoading(false);\r\n        }, 5000);\r\n      }\r\n    };\r\n\r\n    fetchProduct(); // Fetch product on component mount\r\n  }, [id, error, loading, activeProduct]); // Refetch if the ID in the URL changes\r\n\r\n  // Move fetchReviews outside useEffect so it can be reused\r\n  const fetchReviews = async (productId) => {\r\n    try {\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/reviews/reviews/${productId}`\r\n      );\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to fetch reviews\");\r\n      }\r\n      const data = await response.json();\r\n      setReviews(data); // Assuming the response is an array of reviews\r\n    } catch (error) {\r\n      setError(error.message);\r\n    }\r\n  };\r\n\r\n  // Add this useEffect to fetch variants when the product loads\r\n  useEffect(() => {\r\n    if (product) {\r\n      const fetchVariants = async () => {\r\n        try {\r\n          const response = await fetch(\r\n            `https://api.thedesigngrit.com/api/product-variants/product/${product._id}`\r\n          );\r\n          if (!response.ok) {\r\n            throw new Error(\"Failed to fetch variants\");\r\n          }\r\n          const data = await response.json();\r\n          setVariants(data);\r\n        } catch (error) {\r\n          console.error(\"Error fetching variants:\", error);\r\n        }\r\n      };\r\n      fetchVariants();\r\n    }\r\n  }, [product]);\r\n  // Add this effect to update the selected variant when color/size changes\r\n  useEffect(() => {\r\n    if (selectedColor || selectedSize) {\r\n      const matchingVariant = variants.find(\r\n        (variant) =>\r\n          (!selectedColor ||\r\n            variant.color.toLowerCase() === selectedColor.toLowerCase()) &&\r\n          (!selectedSize ||\r\n            variant.size.toLowerCase() === selectedSize.toLowerCase())\r\n      );\r\n      setSelectedVariant(matchingVariant || null);\r\n    } else {\r\n      setSelectedVariant(null);\r\n    }\r\n  }, [selectedColor, selectedSize, variants]);\r\n  useEffect(() => {\r\n    const fetchFavorites = async () => {\r\n      if (!userSession || !product) return; // Ensure both userSession and product are loaded\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/favorites/${userSession.id}`\r\n        );\r\n        if (response.ok) {\r\n          const favoritesData = await response.json();\r\n          const favoriteIds = favoritesData.map((prod) => prod._id);\r\n          setIsFavorite(favoriteIds.includes(product._id));\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching favorites:\", error);\r\n      }\r\n    };\r\n\r\n    fetchFavorites();\r\n  }, [userSession, product]); // Only run when both userSession and product are ready\r\n\r\n  // Toggle the favorite status\r\n  const toggleFavorite = async (event) => {\r\n    event.stopPropagation(); // Prevent triggering card click\r\n\r\n    if (!userSession) return; // If there's no user session, prevent posting\r\n\r\n    const endpoint = isFavorite ? \"/remove\" : \"/add\";\r\n    const requestPayload = {\r\n      userSession,\r\n      productId: product._id,\r\n    };\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/favorites${endpoint}`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify(requestPayload),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        setIsFavorite(!isFavorite); // Toggle the favorite status if successful\r\n      } else {\r\n        console.error(\"Error: Unable to update favorite status.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error:\", error);\r\n    }\r\n  };\r\n\r\n  if (loading) return <LoadingScreen onComplete={() => setLoading(false)} />;\r\n  if (!product) return <div>Product not found</div>;\r\n  // Fetch the user's favorite products on component mount\r\n\r\n  const handleImageClick = (index) => {\r\n    setSelectedImageIndex(index);\r\n    setIsTransitioning(true);\r\n    setTimeout(() => setIsModalOpen(true), 300);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setIsTransitioning(false);\r\n    setTimeout(() => {\r\n      setIsModalOpen(false);\r\n      setSelectedImageIndex(null);\r\n    }, 300);\r\n  };\r\n  const handlePrevImage = () => {\r\n    setSelectedImageIndex(\r\n      (prev) => (prev - 1 + product.images.length) % product.images.length\r\n    );\r\n  };\r\n\r\n  const handleNextImage = () => {\r\n    setSelectedImageIndex((prev) => (prev + 1) % product.images.length);\r\n  };\r\n\r\n  const handleToggleSection = (index, type = \"general\") => {\r\n    if (type === \"general\") {\r\n      setExpandedSections((prev) => ({\r\n        ...prev,\r\n        [index]: !prev[index],\r\n      }));\r\n    } else {\r\n      setExpandedMaterialSections((prev) => ({\r\n        ...prev,\r\n        [index]: !prev[index],\r\n      }));\r\n    }\r\n  };\r\n  // Modify your color and size selection handlers\r\n  const handleColorSelect = (color) => {\r\n    setSelectedColor(color);\r\n    // Reset size when color changes since size options might be different\r\n    setSelectedSize(null);\r\n  };\r\n\r\n  const handleSizeSelect = (size) => {\r\n    setSelectedSize(size);\r\n  };\r\n\r\n  // Create a function to get available sizes for the selected color\r\n  const getAvailableSizes = () => {\r\n    if (!selectedColor || !variants || variants.length === 0)\r\n      return product.sizes || [];\r\n\r\n    const colorVariants = variants.filter(\r\n      (variant) =>\r\n        variant.color &&\r\n        variant.color.toLowerCase() === selectedColor.toLowerCase()\r\n    );\r\n\r\n    const uniqueSizes = [\r\n      ...new Set(colorVariants.map((v) => v.size).filter(Boolean)),\r\n    ];\r\n    return uniqueSizes.length > 0 ? uniqueSizes : product.sizes || [];\r\n  };\r\n\r\n  // Update your product display to use variant data when available\r\n  // const displayProduct = selectedVariant || product;\r\n  const displayImages = selectedVariant?.images || product.images;\r\n  const displayTitle = selectedVariant?.title || product.name;\r\n  const handleSectionToggle = (index) => {\r\n    setExpandedSections((prev) => ({\r\n      ...prev,\r\n      [index]: !prev[index],\r\n    }));\r\n  };\r\n\r\n  const handleAddToCart = () => {\r\n    if (product.stock <= 0) return;\r\n    // Validate color and size selection\r\n    const errors = {\r\n      color: product.colors?.length > 0 && !selectedColor,\r\n      size: getAvailableSizes().length > 0 && !selectedSize,\r\n    };\r\n\r\n    setValidationErrors(errors);\r\n\r\n    // If there are validation errors, don't proceed\r\n    if (errors.color || errors.size) {\r\n      return;\r\n    }\r\n\r\n    // Determine if we're adding a variant or the main product\r\n    if (selectedVariant) {\r\n      // Adding a variant\r\n      // Get the shipping fee safely\r\n      let shippingFee = 0;\r\n      if (\r\n        product.brandId &&\r\n        typeof product.brandId === \"object\" &&\r\n        product.brandId.fees\r\n      ) {\r\n        shippingFee = product.brandId.fees;\r\n      }\r\n\r\n      addToCart({\r\n        id: selectedVariant._id, // Variant ID as the main ID\r\n        variantId: selectedVariant._id, // Same ID to identify it as a variant\r\n        productId: product._id, // Parent product ID\r\n        name: selectedVariant.title || product.name,\r\n        unitPrice: selectedVariant.salePrice || selectedVariant.price || 0,\r\n        quantity: 1,\r\n        image: selectedVariant.images?.[0] || product.mainImage,\r\n        brandId: product.brandId,\r\n        color: selectedColor || \"default\",\r\n        size: selectedSize || \"default\",\r\n        code: selectedVariant.sku || product.sku || \"N/A\",\r\n        shippingFee: shippingFee || 0,\r\n      });\r\n    } else {\r\n      // Adding the main product\r\n      let shippingFee = 0;\r\n      if (\r\n        product.brandId &&\r\n        typeof product.brandId === \"object\" &&\r\n        product.brandId.fees\r\n      ) {\r\n        shippingFee = product.brandId.fees;\r\n      }\r\n\r\n      addToCart({\r\n        id: product._id,\r\n        name: product.name,\r\n        unitPrice: product.salePrice || product.price || 0,\r\n        quantity: 1,\r\n        image: product.mainImage,\r\n        brandId: product.brandId,\r\n        color: selectedColor || \"default\",\r\n        size: selectedSize || \"default\",\r\n        code: product.sku || \"N/A\",\r\n        shippingFee: shippingFee || 0,\r\n      });\r\n    }\r\n\r\n    // Show cart overlay\r\n    setCartOpen(true);\r\n\r\n    // Show notification\r\n    setAddedToCart(true);\r\n\r\n    // Hide notification after 3 seconds\r\n    setTimeout(() => {\r\n      setAddedToCart(false);\r\n    }, 3000);\r\n  };\r\n\r\n  // Add this function to close the cart overlay\r\n  const handleCloseCart = () => {\r\n    setCartOpen(false);\r\n  };\r\n\r\n  //Review Function Post\r\n  const handleSubmitReview = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/reviews/createreviews/${product._id}`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({\r\n            reviewerName,\r\n            userId: userSession.id,\r\n            rating,\r\n            comment,\r\n          }),\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Failed to submit review\");\r\n      }\r\n\r\n      // Update reviews in state robustly\r\n      const result = await response.json();\r\n      if (Array.isArray(result.reviews)) {\r\n        setReviews(result.reviews);\r\n      } else if (result && result._id && result.comment) {\r\n        setReviews((prev) => [...prev, result]);\r\n      } else {\r\n        // Fallback: refetch reviews from the server\r\n        fetchReviews(product._id);\r\n      }\r\n\r\n      // Reset form\r\n      setShowReviewForm(false);\r\n      setRating(0);\r\n      setComment(\"\");\r\n      setReviewerName(\"\");\r\n    } catch (error) {\r\n      console.error(\"Error submitting review:\", error);\r\n    }\r\n  };\r\n  const ratingBreakdown = [5, 4, 3, 2, 1].map((stars) => {\r\n    const count = reviews.filter((r) => r.rating === stars).length;\r\n    return { stars, count };\r\n  });\r\n  return (\r\n    <div className=\"product-page\">\r\n      <Header />\r\n\r\n      <div className=\"product-container\">\r\n        <div className=\"grid-container\">\r\n          <div className=\"product-image-container\">\r\n            <img\r\n              src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${\r\n                selectedVariant?.images?.[0] || product.mainImage\r\n              }`}\r\n              alt={displayTitle}\r\n              className=\"product-main-image\"\r\n              onClick={() => handleImageClick(0)}\r\n            />\r\n            <div className=\"thumbnail-container\">\r\n              {displayImages && displayImages.length > 0 ? (\r\n                displayImages.map((image, index) => (\r\n                  <img\r\n                    key={index}\r\n                    src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}`}\r\n                    alt={`Thumbnail ${index + 1}`}\r\n                    className=\"thumbnail-image\"\r\n                    onClick={() => handleImageClick(index)}\r\n                  />\r\n                ))\r\n              ) : (\r\n                <p>No thumbnails available</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"product-details\">\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                flexDirection: \"row\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n              }}\r\n            >\r\n              <h1 className=\"product-title\">\r\n                {selectedVariant ? selectedVariant.title : product.name}\r\n              </h1>\r\n              {selectedVariant && (\r\n                <h3\r\n                  style={{\r\n                    marginBottom: \"8px\",\r\n                    fontWeight: \"light\",\r\n                    color: \"#ccc\",\r\n                  }}\r\n                >\r\n                  {product.name}\r\n                </h3>\r\n              )}\r\n\r\n              <IconButton\r\n                sx={{\r\n                  marginLeft: \"8px\",\r\n                  \"&:hover\": { backgroundColor: \"#f0f0f0\" },\r\n                }}\r\n                onClick={(event) => {\r\n                  event.stopPropagation();\r\n                  toggleFavorite(event);\r\n                }}\r\n              >\r\n                {isFavorite ? (\r\n                  <FavoriteIcon sx={{ color: \"red\" }} />\r\n                ) : (\r\n                  <FavoriteBorderIcon sx={{ color: \"#000\" }} />\r\n                )}\r\n              </IconButton>\r\n            </div>\r\n            <p className=\"product-brand\">{product.brandId.brandName}</p>\r\n            <br />\r\n            {product.readyToShip === true && (\r\n              <div\r\n                style={{\r\n                  display: \"inline-block\",\r\n                  padding: \"4px 12px\",\r\n                  border: \"1px solid #2d2d2d\",\r\n                  borderRadius: \"4px\",\r\n                  marginTop: \"8px\",\r\n                  marginBottom: \"8px\",\r\n                  fontSize: \"14px\",\r\n                  fontFamily: \"Montserrat\",\r\n                }}\r\n              >\r\n                Ready to Ship\r\n              </div>\r\n            )}\r\n            {product.stock === 0 ? (\r\n              <Box\r\n                sx={{\r\n                  display: \"inline-block\",\r\n                  padding: \"4px 12px\",\r\n                  border: \"1px solid #2d2d2d\",\r\n                  borderRadius: \"4px\",\r\n                  marginTop: \"8px\",\r\n                  marginBottom: \"8px\",\r\n                  fontSize: \"14px\",\r\n                  fontFamily: \"Montserrat\",\r\n                  backgroundColor: \"#DD4A2A\",\r\n                  color: \"#fff\",\r\n                  boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\",\r\n                }}\r\n              >\r\n                SOLD OUT\r\n              </Box>\r\n            ) : product.stock <= 5 ? (\r\n              <Box\r\n                sx={{\r\n                  display: \"inline-block\",\r\n                  padding: \"4px 12px\",\r\n                  border: \"1px solid #2d2d2d\",\r\n                  borderRadius: \"4px\",\r\n                  marginTop: \"8px\",\r\n                  marginBottom: \"8px\",\r\n                  fontSize: \"14px\",\r\n                  fontFamily: \"Montserrat\",\r\n                  backgroundColor: \"#FFAC1C\",\r\n                  color: \"#fff\",\r\n                  boxShadow: \"0px 2px 6px rgba(0,0,0,0.2)\",\r\n                  animation: \"pulse 1.5s infinite\",\r\n                  \"@keyframes pulse\": {\r\n                    \"0%\": { transform: \"scale(1)\", opacity: 1 },\r\n                    \"50%\": { transform: \"scale(1.05)\", opacity: 0.8 },\r\n                    \"100%\": { transform: \"scale(1)\", opacity: 1 },\r\n                  },\r\n                }}\r\n              >\r\n                HURRY UP!\r\n              </Box>\r\n            ) : null}\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                justifyContent: \"space-between\",\r\n                flexDirection: \"row\",\r\n              }}\r\n            >\r\n              <p className=\"product-rating\">\r\n                {reviews.length > 0 ? (\r\n                  <>\r\n                    {\"★\".repeat(\r\n                      Math.round(\r\n                        reviews.reduce(\r\n                          (acc, review) => acc + review.rating,\r\n                          0\r\n                        ) / reviews.length\r\n                      )\r\n                    )}\r\n                    {\" ( of \" + reviews.length + \" reviews)\"}\r\n                  </>\r\n                ) : (\r\n                  <span>No reviews yet</span>\r\n                )}\r\n              </p>\r\n              <p\r\n                style={{\r\n                  display: product.discountPercentage ? \"block\" : \"none\",\r\n                  alignSelf: \"end\",\r\n                }}\r\n              >\r\n                {product.discountPercentage ? (\r\n                  `${product.discountPercentage}% off`\r\n                ) : (\r\n                  <span style={{ display: \"none\" }}></span>\r\n                )}\r\n              </p>\r\n            </div>\r\n            {/* Price Display */}\r\n            <p className=\"product-price\">\r\n              {selectedVariant ? (\r\n                // Show only variant pricing when a variant is selected\r\n                selectedVariant.salePrice ? (\r\n                  // Variant has sale price\r\n                  <>\r\n                    <span\r\n                      style={{\r\n                        textDecoration: \"line-through\",\r\n                        color: \"gray\",\r\n                        marginRight: \"8px\",\r\n                      }}\r\n                    >\r\n                      {selectedVariant.price > 1000\r\n                        ? new Intl.NumberFormat(\"en-US\").format(\r\n                            selectedVariant.price\r\n                          )\r\n                        : selectedVariant.price}\r\n                      .00 E£\r\n                    </span>\r\n                    <span style={{ color: \"red\", fontWeight: \"bold\" }}>\r\n                      {selectedVariant.salePrice > 1000\r\n                        ? new Intl.NumberFormat(\"en-US\").format(\r\n                            selectedVariant.salePrice\r\n                          )\r\n                        : selectedVariant.salePrice}\r\n                      .00 E£\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  // Variant has only regular price\r\n                  <>\r\n                    {selectedVariant.price > 1000\r\n                      ? new Intl.NumberFormat(\"en-US\").format(\r\n                          selectedVariant.price\r\n                        )\r\n                      : selectedVariant.price}\r\n                    .00 E£\r\n                  </>\r\n                )\r\n              ) : // Show parent product pricing when no variant is selected\r\n              product.salePrice && product.promotionApproved === true ? (\r\n                // Parent product has sale price\r\n                <>\r\n                  <span\r\n                    style={{\r\n                      textDecoration: \"line-through\",\r\n                      color: \"gray\",\r\n                      marginRight: \"8px\",\r\n                    }}\r\n                  >\r\n                    {product.price > 1000\r\n                      ? new Intl.NumberFormat(\"en-US\").format(product.price)\r\n                      : product.price}\r\n                    .00 E£\r\n                  </span>\r\n                  <span style={{ color: \"red\", fontWeight: \"bold\" }}>\r\n                    {product.salePrice > 1000\r\n                      ? new Intl.NumberFormat(\"en-US\").format(product.salePrice)\r\n                      : product.salePrice}\r\n                    .00 E£\r\n                  </span>\r\n                </>\r\n              ) : (\r\n                // Parent product has only regular price\r\n                <>\r\n                  {product.price > 1000\r\n                    ? new Intl.NumberFormat(\"en-US\").format(product.price)\r\n                    : product.price}\r\n                  .00 E£\r\n                </>\r\n              )}\r\n            </p>\r\n            <hr />\r\n            <div className=\"color-selector\">\r\n              <span className=\"color-selector-label\">Color:</span>\r\n              <div className=\"color-options\">\r\n                {product.colors && product.colors.length > 0 ? (\r\n                  product.colors.map((color, index) => {\r\n                    // Basic color extraction function\r\n                    const extractColorValue = (colorName) => {\r\n                      // Convert to lowercase for comparison\r\n                      const lowerColor = colorName.toLowerCase();\r\n\r\n                      // Basic color map for common colors\r\n                      const basicColorMap = {\r\n                        white: \"#FFFFFF\",\r\n                        black: \"#000000\",\r\n                        red: \"#FF0000\",\r\n                        green: \"#008000\",\r\n                        blue: \"#0000FF\",\r\n                        yellow: \"#FFFF00\",\r\n                        purple: \"#800080\",\r\n                        orange: \"#FFA500\",\r\n                        pink: \"#FFC0CB\",\r\n                        brown: \"#A52A2A\",\r\n                        gray: \"#808080\",\r\n                        grey: \"#808080\",\r\n                        beige: \"#F5F5DC\",\r\n                        cream: \"#FFFDD0\",\r\n                        gold: \"#FFD700\",\r\n                        silver: \"#C0C0C0\",\r\n                        navy: \"#000080\",\r\n                        olive: \"#808000\",\r\n                        maroon: \"#800000\",\r\n                        teal: \"#008080\",\r\n                        tan: \"#D2B48C\",\r\n                        coral: \"#FF7F50\",\r\n                        sage: \"#BCB88A\",\r\n                        charcoal: \"#36454F\",\r\n                      };\r\n\r\n                      // Try exact match first\r\n                      if (basicColorMap[lowerColor]) {\r\n                        return basicColorMap[lowerColor];\r\n                      }\r\n\r\n                      // Try to extract a basic color from the name\r\n                      for (const [basicColor, hexValue] of Object.entries(\r\n                        basicColorMap\r\n                      )) {\r\n                        if (lowerColor.includes(basicColor)) {\r\n                          return hexValue;\r\n                        }\r\n                      }\r\n\r\n                      // If no match found, use a neutral gray with the color name displayed\r\n                      return \"#CCCCCC\";\r\n                    };\r\n\r\n                    // Get color value\r\n                    const colorValue = extractColorValue(color);\r\n\r\n                    // Check if color is light\r\n                    const isLightColor =\r\n                      colorValue === \"#FFFFFF\" ||\r\n                      colorValue === \"#F5F5DC\" ||\r\n                      colorValue === \"#FFFDD0\" ||\r\n                      color.toLowerCase().includes(\"white\") ||\r\n                      color.toLowerCase().includes(\"cream\") ||\r\n                      color.toLowerCase().includes(\"beige\") ||\r\n                      color.toLowerCase().includes(\"ivory\") ||\r\n                      color.toLowerCase().includes(\"off white\") ||\r\n                      color.toLowerCase().includes(\"offwhite\");\r\n\r\n                    return (\r\n                      <div\r\n                        key={index}\r\n                        className={`color-circle ${\r\n                          selectedColor === color ? \"selected\" : \"\"\r\n                        }`}\r\n                        style={{\r\n                          backgroundColor: colorValue,\r\n                          border: isLightColor ? \"1px solid #2d2d2d\" : \"none\",\r\n                          position: \"relative\",\r\n                        }}\r\n                        title={color}\r\n                        onClick={() => handleColorSelect(color)}\r\n                      >\r\n                        {colorValue === \"#CCCCCC\" && (\r\n                          <div className=\"color-name-overlay\">\r\n                            {color.charAt(0)}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    );\r\n                  })\r\n                ) : (\r\n                  <p>\r\n                    This product is only available in one color, so you don't\r\n                    have to worry about choosing the perfect shade!\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n            {selectedColor ? (\r\n              <p className=\"selected-color-text\">\r\n                Selected Color: {selectedColor}\r\n              </p>\r\n            ) : validationErrors.color ? (\r\n              <p className=\"selected-color-text\" style={{ color: \"red\" }}>\r\n                Please select a color\r\n              </p>\r\n            ) : null}\r\n            <hr />\r\n            <div className=\"size-selector\">\r\n              <span className=\"size-selector-label\">Size:</span>\r\n              <div className=\"size-options\">\r\n                {getAvailableSizes() && getAvailableSizes().length > 0 ? (\r\n                  getAvailableSizes().map((size, index) => (\r\n                    <button\r\n                      key={index}\r\n                      className={`size-button ${\r\n                        selectedSize === size ? \"selected\" : \"\"\r\n                      }`}\r\n                      onClick={() => handleSizeSelect(size)}\r\n                    >\r\n                      {size}\r\n                    </button>\r\n                  ))\r\n                ) : (\r\n                  <p>No size options available</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n            {selectedSize ? (\r\n              <p>Selected Size: {selectedSize}</p>\r\n            ) : validationErrors.size ? (\r\n              <p style={{ color: \"red\" }}>Please select a size</p>\r\n            ) : null}{\" \"}\r\n            <div className=\"action-buttons\">\r\n              {product.stock <= 0 ? (\r\n                <div\r\n                  className=\"action-button disabled-text\"\r\n                  style={{\r\n                    cursor: \"not-allowed\",\r\n                    pointerEvents: \"none\",\r\n                    backgroundColor: \"#f0f0f0\",\r\n                    border: \"1px dashed #6b7b58\",\r\n                    color: \"#6b7b58\",\r\n                    width: \"100%\",\r\n                    textAlign: \"center\",\r\n                  }}\r\n                >\r\n                  Sold Out !\r\n                </div>\r\n              ) : (\r\n                <button\r\n                  className=\"action-button button-primary\"\r\n                  onClick={() => handleAddToCart(product)}\r\n                >\r\n                  Add to Cart\r\n                </button>\r\n              )}\r\n              <button\r\n                className=\"action-button button-secondary\"\r\n                onClick={() => setShowRequestInfoPopup(true)} // Open Request Info Popup\r\n              >\r\n                Request Info\r\n              </button>\r\n            </div>\r\n            {/* Request Info Popup */}\r\n            {isRequestInfoOpen && (\r\n              <RequestInfoPopup\r\n                open={showRequestInfoPopup}\r\n                onClose={() => setShowRequestInfoPopup(false)}\r\n                productId={product} // Pass productId here\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"page-container\">\r\n          {/* Collapsible Info Section */}\r\n          <div className=\"collapsible-container\">\r\n            {[\"Overview\", \"Dimensions\", \"BIM/CAD\", \"Tags\"].map(\r\n              (section, index) => (\r\n                <div\r\n                  key={index}\r\n                  className={`collapsible-section ${\r\n                    expandedSections[index] ? \"open\" : \"\"\r\n                  }`}\r\n                  onClick={() => handleSectionToggle(index)}\r\n                >\r\n                  <div className=\"collapsible-header\">\r\n                    {section}\r\n                    <KeyboardArrowDownIcon\r\n                      className={`collapsible-icon ${\r\n                        expandedSections[index] ? \"rotated\" : \"\"\r\n                      }`}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Content for each section */}\r\n                  <div className=\"collapsible-content\">\r\n                    {section === \"Overview\" && (\r\n                      <div className=\"product-contents\">\r\n                        {/* <h5\r\n                          style={{\r\n                            fontSize: isMobile ? \"20px\" : \"25px\",\r\n                            marginLeft: \"0px\",\r\n                          }}\r\n                        >\r\n                          Manufacturer :{product.brandId.brandName}\r\n                        </h5> */}\r\n                        <div className=\"product-details\">\r\n                          <p style={{ fontSize: isMobile ? \"13px\" : \"20px\" }}>\r\n                            <span className=\"label\">Collection:</span>\r\n                            {product.collection}\r\n                          </p>\r\n                          {/* <p style={{ fontSize: \"20px\" }}>\r\n                            <span className=\"label\">Type:</span> 2 Seater Fabric\r\n                            Sofa\r\n                          </p> */}\r\n                          <p style={{ fontSize: isMobile ? \"13px\" : \"20px\" }}>\r\n                            <span className=\"label\">Manufacturer Year:</span>{\" \"}\r\n                            {product.manufactureYear}\r\n                          </p>\r\n                        </div>\r\n\r\n                        <p\r\n                          style={{\r\n                            fontSize: isMobile ? \"13px\" : \"20px\",\r\n                            textAlign: \"justify\",\r\n                          }}\r\n                        >\r\n                          {product.description}\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                    {section === \"Dimensions\" && (\r\n                      <div className=\"product-contents\">\r\n                        {/* <img src=\"/Assets/productDemi.webp\" alt=\"Dimensions\" /> */}\r\n                        <p>Width X Length X Height</p>\r\n                        <p>\r\n                          <strong>\r\n                            {selectedVariant\r\n                              ? selectedVariant.dimensions.width\r\n                              : product.technicalDimensions.width}\r\n                          </strong>{\" \"}\r\n                          cm x{\"  \"}\r\n                          <strong>\r\n                            {\" \"}\r\n                            {selectedVariant\r\n                              ? selectedVariant.dimensions.length\r\n                              : product.technicalDimensions.length}\r\n                          </strong>{\" \"}\r\n                          cm X{\"  \"}\r\n                          <strong>\r\n                            {selectedVariant\r\n                              ? selectedVariant.dimensions.height\r\n                              : product.technicalDimensions.height}\r\n                          </strong>{\" \"}\r\n                          cm\r\n                        </p>\r\n                        <p>\r\n                          Weight :{\" \"}\r\n                          <strong>\r\n                            {selectedVariant\r\n                              ? selectedVariant.dimensions.weight\r\n                              : product.technicalDimensions.weight}\r\n                          </strong>{\" \"}\r\n                          Kgs\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                    {section === \"BIM/CAD\" && (\r\n                      <div className=\"product-contents\">\r\n                        <Button\r\n                          sx={{\r\n                            backgroundColor: \"transparent\",\r\n                            color: \"#2d2d2d\",\r\n                            borderRadius: \"10px\",\r\n                            border: \"1px solid #2d2d2d\",\r\n                            width: \"40%\",\r\n                            padding: \"10px 20px\",\r\n                            minWidth: \"150px\",\r\n                            display: \"flex\",\r\n                            justifyContent: \"space-between\",\r\n                            alignItems: \"center\",\r\n                            textTransform: \"none\",\r\n                            \"&:hover\": {\r\n                              backgroundColor: \"#000\",\r\n                              color: \"#fff\",\r\n                            },\r\n                          }}\r\n                          onClick={() => {\r\n                            if (product.cadFile) {\r\n                              // Create a temporary anchor element\r\n                              const link = document.createElement(\"a\");\r\n                              link.href = `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.cadFile}`; // Add the URL prefix\r\n                              link.download = `product_cad_${product._id}`; // Suggested filename\r\n                              document.body.appendChild(link);\r\n                              link.click();\r\n                              document.body.removeChild(link);\r\n                            } else {\r\n                              alert(\"No CAD file available for download\");\r\n                            }\r\n                          }}\r\n                        >\r\n                          {/* Left-aligned image */}\r\n                          <img\r\n                            src=\"/Assets/autocadIcon.webp\"\r\n                            alt=\"AutoCAD Logo\"\r\n                            style={{\r\n                              width: \"24px\",\r\n                              height: \"24px\",\r\n                              marginRight: \"10px\",\r\n                            }}\r\n                          />\r\n                          {/* Centered text */}\r\n                          <span>Download CAD File</span>\r\n                          {/* Right-aligned download icon */}\r\n                          <FaDownload style={{ marginLeft: \"10px\" }} />\r\n                        </Button>\r\n                      </div>\r\n                    )}\r\n                    {/* {section === \"Videos\" && (\r\n                      <div className=\"product-contents\">\r\n                        <iframe\r\n                          width=\"560\"\r\n                          height=\"315\"\r\n                          src=\"https://www.youtube.com/embed/dQw4w9WgXcQ\"\r\n                          title=\"Product Video\"\r\n                          frameBorder=\"0\"\r\n                          allow=\"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture\"\r\n                          allowFullScreen\r\n                        ></iframe>\r\n                      </div>\r\n                    )} */}\r\n                    {section === \"Tags\" && product.tags && (\r\n                      <div className=\"span-container\">\r\n                        {product.tags.length > 0 ? (\r\n                          product.tags.map((tag, index) => (\r\n                            <span key={index}>{tag}</span>\r\n                          ))\r\n                        ) : (\r\n                          <p>No tags available.</p>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )\r\n            )}\r\n          </div>\r\n          <div className=\"right-side-content\">\r\n            {/* <div className=\"Products-Materials\">\r\n              <h4>NATURAL AND RECYCLED MATERIALS</h4>\r\n              <ul>\r\n                <li>R-LENO - Recycled Wool</li>\r\n                <span>Soft, comfortable, and lightweight</span>\r\n                <li>Designed to last a long time</li>\r\n                <span>Resistant materials that are easily washable</span>\r\n                <li>Waterproof to accompany you even in light rain </li>\r\n                <span>Flexible, lightweight, and cushioned</span>\r\n                <li>Inner Sole - Ortholite®</li>\r\n                <span>Removable and ergonomic</span>\r\n              </ul>\r\n            </div> */}\r\n            <div className=\"material-collapsible-container\">\r\n              {[\r\n                \"Delivery & Returns\",\r\n                \"Care Instructions\",\r\n                \"Product Specifications\",\r\n              ].map((section, index) => (\r\n                <div key={index} className=\"material-collapsible-section\">\r\n                  <div\r\n                    className=\"material-collapsible-header\"\r\n                    onClick={() => handleToggleSection(index, \"material\")}\r\n                  >\r\n                    {section}\r\n                    <span className=\"material-collapsible-icon\">\r\n                      {expandedMaterialSections[index] ? \"-\" : \"+\"}\r\n                    </span>\r\n                  </div>\r\n                  {expandedMaterialSections[index] && (\r\n                    <div className=\"material-collapsible-content\">\r\n                      {section === \"Delivery & Returns\" ? (\r\n                        <ul>\r\n                          {product.leadTime\r\n                            ?.split(/(?<=\\w)\\s(?=[A-Z])/)\r\n                            .map((point, idx) => (\r\n                              <li key={idx}>\r\n                                {\" \"}\r\n                                Lead Time:{point} Business Days\r\n                              </li>\r\n                            ))}{\" \"}\r\n                          {product.Estimatedtimeleadforcustomization?.split(\r\n                            /(?<=\\w)\\s(?=[A-Z])/\r\n                          ).map((point, idx) => (\r\n                            <li key={idx}>\r\n                              Estimated Time for Customization:{point} Business\r\n                              Days\r\n                            </li>\r\n                          ))}\r\n                        </ul>\r\n                      ) : section === \"Care Instructions\" ? (\r\n                        <ul>\r\n                          {product.materialCareInstructions\r\n                            .split(\"\\n\")\r\n                            .map((point, idx) => (\r\n                              <li key={idx}>{point}</li>\r\n                            ))}\r\n                        </ul>\r\n                      ) : section === \"Product Specifications\" ? (\r\n                        <ul>\r\n                          {product.productSpecificRecommendations\r\n                            .split(\"\\n\")\r\n                            .map((point, index) => (\r\n                              <li key={index}>{point.trim()}</li>\r\n                            ))}\r\n                        </ul>\r\n                      ) : (\r\n                        \"\"\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n            <div className=\"brand-cursol\">\r\n              <BrandCursol\r\n                brandId={product.brandId}\r\n                onRequestQuote={handleRequestQuote}\r\n                mainProductId={product._id} // Pass the main product ID\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {(isModalOpen || isTransitioning) && (\r\n          <div className={`modal ${isTransitioning ? \"opening\" : \"closing\"}`}>\r\n            <div className=\"modal-content\">\r\n              <button className=\"modal-close\" onClick={handleCloseModal}>\r\n                <IoMdClose size={30} color=\"#fff\" />\r\n              </button>\r\n              <button className=\"modal-prev\" onClick={handlePrevImage}>\r\n                <IoIosArrowBack size={30} color=\"#fff\" />\r\n              </button>\r\n              <img\r\n                src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.images[selectedImageIndex]}`}\r\n                alt={`${selectedImageIndex + 1}`}\r\n                className=\"modal-image\"\r\n              />\r\n              <button className=\"modal-next\" onClick={handleNextImage}>\r\n                <IoIosArrowForward size={30} color=\"#fff\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <hr\r\n          style={{\r\n            width: \"90%\",\r\n            textAlign: \"center\",\r\n            margin: \"auto\",\r\n            marginTop: \"30px\",\r\n            marginBottom: \"20px\",\r\n          }}\r\n        ></hr>\r\n        <div className=\"reviews-section\" style={{ padding: \"0 3rem\" }}>\r\n          <h2>Related Products</h2>\r\n          <RelatedProducts productId={product._id} />\r\n        </div>\r\n        <div className=\"reviews-section\">\r\n          <div className=\"reviews-header\">\r\n            <h2>Reviews</h2>\r\n            <button\r\n              onClick={() => setShowReviewForm(true)}\r\n              className=\"write-review-btn\"\r\n            >\r\n              Write a Review\r\n            </button>\r\n          </div>\r\n\r\n          {showReviewForm && (\r\n            <div className=\"review-form-overlay\">\r\n              <div className=\"review-form-container\">\r\n                <button\r\n                  className=\"close-form-btn\"\r\n                  onClick={() => setShowReviewForm(false)}\r\n                  style={{\r\n                    backgroundColor: hover ? \"transparent\" : \"transparent\",\r\n                  }}\r\n                  onMouseEnter={() => setHover(true)}\r\n                  onMouseLeave={() => setHover(false)}\r\n                >\r\n                  <IoMdClose />\r\n                </button>\r\n                <h3>Write a Review</h3>\r\n                <form onSubmit={handleSubmitReview} className=\"review-form\">\r\n                  <div className=\"form-group\">\r\n                    <label>Your Name</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={reviewerName}\r\n                      onChange={(e) => setReviewerName(e.target.value)}\r\n                      required\r\n                      className=\"review-input\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label>Rating</label>\r\n                    <div className=\"star-rating\">\r\n                      {[...Array(5)].map((_, index) => {\r\n                        const ratingValue = index + 1;\r\n                        return (\r\n                          <FaStar\r\n                            key={index}\r\n                            className=\"star\"\r\n                            color={\r\n                              ratingValue <= (hover || rating)\r\n                                ? \"#ffc107\"\r\n                                : \"#e4e5e9\"\r\n                            }\r\n                            size={24}\r\n                            onClick={() => setRating(ratingValue)}\r\n                            onMouseEnter={() => setHover(ratingValue)}\r\n                            onMouseLeave={() => setHover(rating)}\r\n                            style={{ cursor: \"pointer\" }}\r\n                          />\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label>Your Review</label>\r\n                    <textarea\r\n                      value={comment}\r\n                      onChange={(e) => setComment(e.target.value)}\r\n                      required\r\n                      className=\"review-textarea\"\r\n                      rows={4}\r\n                    />\r\n                  </div>\r\n\r\n                  <button type=\"submit\" className=\"submit-review-btn\">\r\n                    Submit Review\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <div style={{ paddingLeft: \"1rem\" }}>\r\n            <Box className=\"review-summary\">\r\n              <ReviewBox reviewsData={ratingBreakdown} />\r\n            </Box>\r\n\r\n            {reviews.length > 0 ? (\r\n              reviews.map((review, index) => (\r\n                <div key={index} className=\"review-card\">\r\n                  <Box className=\"review-subtitle\">\r\n                    <h3>{review.reviewerName}</h3>\r\n                    <p>{review.reviewDate}</p>\r\n                  </Box>\r\n                  <p>{\"★\".repeat(review.rating)}</p>\r\n                  <p>{review.comment}</p>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  textAlign: \"center\",\r\n                  marginTop: \"20px\",\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignItems: \"center\",\r\n                  gap: \"16px\",\r\n                }}\r\n              >\r\n                <BsExclamationOctagon size={50} color=\"#ccc\" />\r\n\r\n                <p className=\"no-reviews\">No reviews yet.</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      {isRequestQuoteOpen && (\r\n        <RequestQuote\r\n          onClose={handleCloseRequestQuote}\r\n          productId={quoteProduct}\r\n        />\r\n      )}\r\n      {/* Cart Overlay */}\r\n      <ShoppingCartOverlay open={cartOpen} onClose={handleCloseCart} />\r\n\r\n      {/* Added to Cart Notification */}\r\n      {addedToCart && (\r\n        <div\r\n          className=\"added-to-cart-notification\"\r\n          style={{\r\n            position: \"fixed\",\r\n            bottom: \"20px\",\r\n            right: \"20px\",\r\n            backgroundColor: \"#6B7B58\",\r\n            color: \"white\",\r\n            padding: \"15px 20px\",\r\n            borderRadius: \"8px\",\r\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.2)\",\r\n            zIndex: 1000,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: \"10px\",\r\n            animation: \"slideIn 0.3s ease-out\",\r\n          }}\r\n        >\r\n          <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}>\r\n            <img\r\n              src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${\r\n                selectedVariant?.images?.[0] || product.mainImage\r\n              }`}\r\n              alt={product.name}\r\n              style={{\r\n                width: \"40px\",\r\n                height: \"40px\",\r\n                objectFit: \"cover\",\r\n                borderRadius: \"4px\",\r\n              }}\r\n            />\r\n            <div>\r\n              <div style={{ fontWeight: \"bold\" }}>Added to Cart</div>\r\n              <div style={{ fontSize: \"14px\" }}>\r\n                {selectedVariant ? selectedVariant.title : product.name}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      {/* <Dialog\r\n        open={infoOpen}\r\n        onClose={() => setInfoOpen(false)}\r\n        PaperProps={{\r\n          sx: {\r\n            borderRadius: 4,\r\n            background: \"#f8f7f3\",\r\n            minWidth: 500,\r\n            maxWidth: 600,\r\n            p: 2,\r\n          },\r\n        }}\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            fontFamily: \"Horizon, Montserrat\",\r\n            fontWeight: \"bold\",\r\n            color: \"#2d2d2d\",\r\n            background: \"#f3f1ea\",\r\n            borderRadius: \"16px 16px 0 0\",\r\n            fontSize: \"1.3rem\",\r\n            letterSpacing: \"1px\",\r\n            textAlign: \"center\",\r\n            mb: 1,\r\n            p: 2,\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          More Info\r\n          <IconButton onClick={() => setInfoOpen(false)}>\r\n            <CloseIcon />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent sx={{ p: 2 }}>\r\n          <Box sx={{ mb: 2 }}>\r\n            <Typography\r\n              variant=\"subtitle1\"\r\n              sx={{ fontWeight: \"bold\", color: \"#6b7b58\" }}\r\n            >\r\n              Customization Options\r\n            </Typography>\r\n            {Array.isArray(product.Customizationoptions) &&\r\n            product.Customizationoptions.length > 0 ? (\r\n              <ul style={{ margin: 0, paddingLeft: \"1.2em\" }}>\r\n                {product.Customizationoptions.map((option, idx) => (\r\n                  <li key={idx} style={{ fontSize: \"1rem\", color: \"#333\" }}>\r\n                    {option}\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            ) : (\r\n              <Typography>No customization options</Typography>\r\n            )}\r\n          </Box>\r\n          <Box sx={{ mb: 2 }}>\r\n            <Typography\r\n              variant=\"subtitle1\"\r\n              sx={{ fontWeight: \"bold\", color: \"#6b7b58\" }}\r\n            >\r\n              Additional Details\r\n            </Typography>\r\n            <Typography>\r\n              {product.Additionaldetails\r\n                ? product.Additionaldetails\r\n                : \"No additional details\"}\r\n            </Typography>\r\n          </Box>\r\n          <Box sx={{ mb: 2 }}>\r\n            <Typography\r\n              variant=\"subtitle1\"\r\n              sx={{ fontWeight: \"bold\", color: \"#6b7b58\" }}\r\n            >\r\n              Additional Costs\r\n            </Typography>\r\n            <Chip\r\n              label={product.Additionalcosts || \"N/A\"}\r\n              sx={{\r\n                background: product.Additionalcosts?.toLowerCase().includes(\r\n                  \"yes\"\r\n                )\r\n                  ? \"#d4edda\"\r\n                  : product.Additionalcosts?.toLowerCase().includes(\"no\")\r\n                  ? \"#f8d7da\"\r\n                  : product.Additionalcosts\r\n                  ? \"#fff3cd\"\r\n                  : \"#e0e0e0\",\r\n                color: product.Additionalcosts?.toLowerCase().includes(\"yes\")\r\n                  ? \"#155724\"\r\n                  : product.Additionalcosts?.toLowerCase().includes(\"no\")\r\n                  ? \"#721c24\"\r\n                  : product.Additionalcosts\r\n                  ? \"#856404\"\r\n                  : \"#333\",\r\n                fontWeight: \"bold\",\r\n              }}\r\n            />\r\n          </Box>\r\n          <Box sx={{ mb: 2 }}>\r\n            <Typography\r\n              variant=\"subtitle1\"\r\n              sx={{ fontWeight: \"bold\", color: \"#6b7b58\" }}\r\n            >\r\n              Warranty Info\r\n            </Typography>\r\n            {product.warrantyInfo ? (\r\n              <>\r\n                <Typography sx={{ fontWeight: \"bold\", mb: 1 }}>\r\n                  Years: {product.warrantyInfo.warrantyYears} years\r\n                </Typography>\r\n                <Typography sx={{ fontWeight: \"bold\", color: \"#6b7b58\" }}>\r\n                  Coverage:\r\n                </Typography>\r\n                <ul style={{ margin: 0, paddingLeft: \"1.2em\" }}>\r\n                  {Array.isArray(product.warrantyInfo.warrantyCoverage)\r\n                    ? product.warrantyInfo.warrantyCoverage.map((item, idx) => (\r\n                        <li\r\n                          key={idx}\r\n                          style={{ fontSize: \"1rem\", color: \"#333\" }}\r\n                        >\r\n                          {item}\r\n                        </li>\r\n                      ))\r\n                    : product.warrantyInfo.warrantyCoverage && (\r\n                        <li style={{ fontSize: \"1rem\", color: \"#333\" }}>\r\n                          {product.warrantyInfo.warrantyCoverage}\r\n                        </li>\r\n                      )}\r\n                </ul>\r\n              </>\r\n            ) : (\r\n              \"No warranty info\"\r\n            )}\r\n          </Box>\r\n        </DialogContent>\r\n      </Dialog> */}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProductPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,GAAG,EAAEC,MAAM,EAAEC,aAAa,EAAEC,UAAU,QAAQ,eAAe;AACtE,SAASC,MAAM,EAAEC,UAAU,QAAQ,gBAAgB;AACnD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,WAAW,QAAQ,sBAAsB;AAClD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA;EACrB,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzE,MAAM,CAACuC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM;IAAEwC;EAAY,CAAC,GAAGtC,UAAU,CAACmB,WAAW,CAAC;EAC/C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM2C,QAAQ,GAAGtC,aAAa,CAAC,mBAAmB,CAAC;EACnD,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM;IAAEsD;EAAG,CAAC,GAAGxC,SAAS,CAAC,CAAC;EAC1B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC8D,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5E,MAAM;IAAEgE;EAAU,CAAC,GAAG7C,OAAO,CAAC,CAAC;EAC/B,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuE,MAAM,EAAEC,SAAS,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACyE,KAAK,EAAEC,QAAQ,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACpD;EACA,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiF,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACrD;EACA,MAAM,CAAC6F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9F,QAAQ,CAAC;IACvD+F,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAIC,WAAW,IAAK;IAC1C,IAAI,CAAC1D,WAAW,EAAE;MAChBe,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;;IAEA;IACA,IAAI4C,UAAU,GAAGD,WAAW;;IAE5B;IACA,IAAI,CAACA,WAAW,CAACE,OAAO,IAAI,OAAOF,WAAW,CAACE,OAAO,KAAK,QAAQ,EAAE;MACnED,UAAU,GAAG;QACX,GAAGD,WAAW;QACdE,OAAO,EAAElD,OAAO,CAACkD,OAAO,CAAE;MAC5B,CAAC;IACH;IAEAZ,eAAe,CAACW,UAAU,CAAC;IAC3Bb,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMe,uBAAuB,GAAGA,CAAA,KAAM;IACpCf,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACArF,SAAS,CAAC,MAAM;IACd,MAAMqG,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,wDAAwDlD,EAAE,EAC5D,CAAC,CAAC,CAAC;QACH,IAAI,CAACiD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;QACpD;QACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCzD,UAAU,CAACwD,IAAI,CAAC,CAAC,CAAC;QAClBvB,gBAAgB,CAACuB,IAAI,CAAC,CAAC,CAAC;QACxBE,YAAY,CAACF,IAAI,CAACG,GAAG,CAAC;MACxB,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACd4C,OAAO,CAACC,GAAG,CAAC7C,KAAK,CAAC;QAClBC,QAAQ,CAACD,KAAK,CAAC8C,OAAO,CAAC,CAAC,CAAC;MAC3B,CAAC,SAAS;QACRC,UAAU,CAAC,MAAM;UACfhD,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IAEDoC,YAAY,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChD,EAAE,EAAEa,KAAK,EAAEF,OAAO,EAAEkB,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEzC;EACA,MAAM0B,YAAY,GAAG,MAAOM,SAAS,IAAK;IACxC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAC1B,qDAAqDW,SAAS,EAChE,CAAC;MACD,IAAI,CAACZ,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClCvD,UAAU,CAACsD,IAAI,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC8C,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACAhH,SAAS,CAAC,MAAM;IACd,IAAIiD,OAAO,EAAE;MACX,MAAMkE,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8DAA8DtD,OAAO,CAAC4D,GAAG,EAC3E,CAAC;UACD,IAAI,CAACP,QAAQ,CAACE,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;UAC7C;UACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UAClC5B,WAAW,CAAC2B,IAAI,CAAC;QACnB,CAAC,CAAC,OAAOxC,KAAK,EAAE;UACd4C,OAAO,CAAC5C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC;MACDiD,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAClE,OAAO,CAAC,CAAC;EACb;EACAjD,SAAS,CAAC,MAAM;IACd,IAAIuD,aAAa,IAAIE,YAAY,EAAE;MACjC,MAAM2D,eAAe,GAAGtC,QAAQ,CAACuC,IAAI,CAClCC,OAAO,IACN,CAAC,CAAC/D,aAAa,IACb+D,OAAO,CAACxB,KAAK,CAACyB,WAAW,CAAC,CAAC,KAAKhE,aAAa,CAACgE,WAAW,CAAC,CAAC,MAC5D,CAAC9D,YAAY,IACZ6D,OAAO,CAACvB,IAAI,CAACwB,WAAW,CAAC,CAAC,KAAK9D,YAAY,CAAC8D,WAAW,CAAC,CAAC,CAC/D,CAAC;MACDtC,kBAAkB,CAACmC,eAAe,IAAI,IAAI,CAAC;IAC7C,CAAC,MAAM;MACLnC,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC1B,aAAa,EAAEE,YAAY,EAAEqB,QAAQ,CAAC,CAAC;EAC3C9E,SAAS,CAAC,MAAM;IACd,MAAMwH,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACjF,WAAW,IAAI,CAACU,OAAO,EAAE,OAAO,CAAC;;MAEtC,IAAI;QACF,MAAMqD,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+ChE,WAAW,CAACc,EAAE,EAC/D,CAAC;QACD,IAAIiD,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMiB,aAAa,GAAG,MAAMnB,QAAQ,CAACK,IAAI,CAAC,CAAC;UAC3C,MAAMe,WAAW,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACf,GAAG,CAAC;UACzDpE,aAAa,CAACiF,WAAW,CAACG,QAAQ,CAAC5E,OAAO,CAAC4D,GAAG,CAAC,CAAC;QAClD;MACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACd4C,OAAO,CAAC5C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAEDsD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjF,WAAW,EAAEU,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE5B;EACA,MAAM6E,cAAc,GAAG,MAAOC,KAAK,IAAK;IACtCA,KAAK,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEzB,IAAI,CAACzF,WAAW,EAAE,OAAO,CAAC;;IAE1B,MAAM0F,QAAQ,GAAGzF,UAAU,GAAG,SAAS,GAAG,MAAM;IAChD,MAAM0F,cAAc,GAAG;MACrB3F,WAAW;MACX2E,SAAS,EAAEjE,OAAO,CAAC4D;IACrB,CAAC;IAED,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMC,KAAK,CAC1B,8CAA8C0B,QAAQ,EAAE,EACxD;QACEE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,cAAc;MACrC,CACF,CAAC;MAED,IAAI5B,QAAQ,CAACE,EAAE,EAAE;QACf/D,aAAa,CAAC,CAACD,UAAU,CAAC,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLsE,OAAO,CAAC5C,KAAK,CAAC,0CAA0C,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,IAAIF,OAAO,EAAE,oBAAOnC,OAAA,CAACV,aAAa;IAACqH,UAAU,EAAEA,CAAA,KAAMvE,UAAU,CAAC,KAAK;EAAE;IAAAwE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC1E,IAAI,CAAC3F,OAAO,EAAE,oBAAOpB,OAAA;IAAAgH,QAAA,EAAK;EAAiB;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACjD;;EAEA,MAAME,gBAAgB,GAAIC,KAAK,IAAK;IAClCnG,qBAAqB,CAACmG,KAAK,CAAC;IAC5B/F,kBAAkB,CAAC,IAAI,CAAC;IACxBiE,UAAU,CAAC,MAAMnE,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EAC7C,CAAC;EAED,MAAMkG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhG,kBAAkB,CAAC,KAAK,CAAC;IACzBiE,UAAU,CAAC,MAAM;MACfnE,cAAc,CAAC,KAAK,CAAC;MACrBF,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EACD,MAAMqG,eAAe,GAAGA,CAAA,KAAM;IAC5BrG,qBAAqB,CAClBsG,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,GAAGjG,OAAO,CAACkG,MAAM,CAACC,MAAM,IAAInG,OAAO,CAACkG,MAAM,CAACC,MAChE,CAAC;EACH,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BzG,qBAAqB,CAAEsG,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIjG,OAAO,CAACkG,MAAM,CAACC,MAAM,CAAC;EACrE,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAACP,KAAK,EAAEQ,IAAI,GAAG,SAAS,KAAK;IACvD,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtB3F,mBAAmB,CAAEsF,IAAI,KAAM;QAC7B,GAAGA,IAAI;QACP,CAACH,KAAK,GAAG,CAACG,IAAI,CAACH,KAAK;MACtB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLjF,2BAA2B,CAAEoF,IAAI,KAAM;QACrC,GAAGA,IAAI;QACP,CAACH,KAAK,GAAG,CAACG,IAAI,CAACH,KAAK;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD;EACA,MAAMS,iBAAiB,GAAI1D,KAAK,IAAK;IACnCtC,gBAAgB,CAACsC,KAAK,CAAC;IACvB;IACApC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+F,gBAAgB,GAAI1D,IAAI,IAAK;IACjCrC,eAAe,CAACqC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM2D,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACnG,aAAa,IAAI,CAACuB,QAAQ,IAAIA,QAAQ,CAACsE,MAAM,KAAK,CAAC,EACtD,OAAOnG,OAAO,CAAC0G,KAAK,IAAI,EAAE;IAE5B,MAAMC,aAAa,GAAG9E,QAAQ,CAAC+E,MAAM,CAClCvC,OAAO,IACNA,OAAO,CAACxB,KAAK,IACbwB,OAAO,CAACxB,KAAK,CAACyB,WAAW,CAAC,CAAC,KAAKhE,aAAa,CAACgE,WAAW,CAAC,CAC9D,CAAC;IAED,MAAMuC,WAAW,GAAG,CAClB,GAAG,IAAIC,GAAG,CAACH,aAAa,CAACjC,GAAG,CAAEqC,CAAC,IAAKA,CAAC,CAACjE,IAAI,CAAC,CAAC8D,MAAM,CAACI,OAAO,CAAC,CAAC,CAC7D;IACD,OAAOH,WAAW,CAACV,MAAM,GAAG,CAAC,GAAGU,WAAW,GAAG7G,OAAO,CAAC0G,KAAK,IAAI,EAAE;EACnE,CAAC;;EAED;EACA;EACA,MAAMO,aAAa,GAAG,CAAAlF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmE,MAAM,KAAIlG,OAAO,CAACkG,MAAM;EAC/D,MAAMgB,YAAY,GAAG,CAAAnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoF,KAAK,KAAInH,OAAO,CAACoH,IAAI;EAC3D,MAAMC,mBAAmB,GAAIvB,KAAK,IAAK;IACrCnF,mBAAmB,CAAEsF,IAAI,KAAM;MAC7B,GAAGA,IAAI;MACP,CAACH,KAAK,GAAG,CAACG,IAAI,CAACH,KAAK;IACtB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMwB,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,eAAA;IAC5B,IAAIvH,OAAO,CAACwH,KAAK,IAAI,CAAC,EAAE;IACxB;IACA,MAAMC,MAAM,GAAG;MACb5E,KAAK,EAAE,EAAA0E,eAAA,GAAAvH,OAAO,CAAC0H,MAAM,cAAAH,eAAA,uBAAdA,eAAA,CAAgBpB,MAAM,IAAG,CAAC,IAAI,CAAC7F,aAAa;MACnDwC,IAAI,EAAE2D,iBAAiB,CAAC,CAAC,CAACN,MAAM,GAAG,CAAC,IAAI,CAAC3F;IAC3C,CAAC;IAEDoC,mBAAmB,CAAC6E,MAAM,CAAC;;IAE3B;IACA,IAAIA,MAAM,CAAC5E,KAAK,IAAI4E,MAAM,CAAC3E,IAAI,EAAE;MAC/B;IACF;;IAEA;IACA,IAAIf,eAAe,EAAE;MAAA,IAAA4F,qBAAA;MACnB;MACA;MACA,IAAIC,WAAW,GAAG,CAAC;MACnB,IACE5H,OAAO,CAACkD,OAAO,IACf,OAAOlD,OAAO,CAACkD,OAAO,KAAK,QAAQ,IACnClD,OAAO,CAACkD,OAAO,CAAC2E,IAAI,EACpB;QACAD,WAAW,GAAG5H,OAAO,CAACkD,OAAO,CAAC2E,IAAI;MACpC;MAEA/G,SAAS,CAAC;QACRV,EAAE,EAAE2B,eAAe,CAAC6B,GAAG;QAAE;QACzBkE,SAAS,EAAE/F,eAAe,CAAC6B,GAAG;QAAE;QAChCK,SAAS,EAAEjE,OAAO,CAAC4D,GAAG;QAAE;QACxBwD,IAAI,EAAErF,eAAe,CAACoF,KAAK,IAAInH,OAAO,CAACoH,IAAI;QAC3CW,SAAS,EAAEhG,eAAe,CAACiG,SAAS,IAAIjG,eAAe,CAACkG,KAAK,IAAI,CAAC;QAClEC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE,EAAAR,qBAAA,GAAA5F,eAAe,CAACmE,MAAM,cAAAyB,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,KAAI3H,OAAO,CAACoI,SAAS;QACvDlF,OAAO,EAAElD,OAAO,CAACkD,OAAO;QACxBL,KAAK,EAAEvC,aAAa,IAAI,SAAS;QACjCwC,IAAI,EAAEtC,YAAY,IAAI,SAAS;QAC/B6H,IAAI,EAAEtG,eAAe,CAACuG,GAAG,IAAItI,OAAO,CAACsI,GAAG,IAAI,KAAK;QACjDV,WAAW,EAAEA,WAAW,IAAI;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAIA,WAAW,GAAG,CAAC;MACnB,IACE5H,OAAO,CAACkD,OAAO,IACf,OAAOlD,OAAO,CAACkD,OAAO,KAAK,QAAQ,IACnClD,OAAO,CAACkD,OAAO,CAAC2E,IAAI,EACpB;QACAD,WAAW,GAAG5H,OAAO,CAACkD,OAAO,CAAC2E,IAAI;MACpC;MAEA/G,SAAS,CAAC;QACRV,EAAE,EAAEJ,OAAO,CAAC4D,GAAG;QACfwD,IAAI,EAAEpH,OAAO,CAACoH,IAAI;QAClBW,SAAS,EAAE/H,OAAO,CAACgI,SAAS,IAAIhI,OAAO,CAACiI,KAAK,IAAI,CAAC;QAClDC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAEnI,OAAO,CAACoI,SAAS;QACxBlF,OAAO,EAAElD,OAAO,CAACkD,OAAO;QACxBL,KAAK,EAAEvC,aAAa,IAAI,SAAS;QACjCwC,IAAI,EAAEtC,YAAY,IAAI,SAAS;QAC/B6H,IAAI,EAAErI,OAAO,CAACsI,GAAG,IAAI,KAAK;QAC1BV,WAAW,EAAEA,WAAW,IAAI;MAC9B,CAAC,CAAC;IACJ;;IAEA;IACApF,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACAE,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACAsB,UAAU,CAAC,MAAM;MACftB,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM6F,eAAe,GAAGA,CAAA,KAAM;IAC5B/F,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;;EAED;EACA,MAAMgG,kBAAkB,GAAG,MAAOC,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMrF,QAAQ,GAAG,MAAMC,KAAK,CAC1B,2DAA2DtD,OAAO,CAAC4D,GAAG,EAAE,EACxE;QACEsB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB3D,YAAY;UACZgH,MAAM,EAAErJ,WAAW,CAACc,EAAE;UACtBiB,MAAM;UACNI;QACF,CAAC;MACH,CACF,CAAC;MAED,IAAI,CAAC4B,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;MAC5C;;MAEA;MACA,MAAMoF,MAAM,GAAG,MAAMvF,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpC,IAAImF,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC1I,OAAO,CAAC,EAAE;QACjCC,UAAU,CAACyI,MAAM,CAAC1I,OAAO,CAAC;MAC5B,CAAC,MAAM,IAAI0I,MAAM,IAAIA,MAAM,CAAChF,GAAG,IAAIgF,MAAM,CAACnH,OAAO,EAAE;QACjDtB,UAAU,CAAE8F,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE2C,MAAM,CAAC,CAAC;MACzC,CAAC,MAAM;QACL;QACAjF,YAAY,CAAC3D,OAAO,CAAC4D,GAAG,CAAC;MAC3B;;MAEA;MACAxC,iBAAiB,CAAC,KAAK,CAAC;MACxBE,SAAS,CAAC,CAAC,CAAC;MACZI,UAAU,CAAC,EAAE,CAAC;MACdE,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EACD,MAAM8H,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACrE,GAAG,CAAEsE,KAAK,IAAK;IACrD,MAAMC,KAAK,GAAG/I,OAAO,CAAC0G,MAAM,CAAEsC,CAAC,IAAKA,CAAC,CAAC7H,MAAM,KAAK2H,KAAK,CAAC,CAAC7C,MAAM;IAC9D,OAAO;MAAE6C,KAAK;MAAEC;IAAM,CAAC;EACzB,CAAC,CAAC;EACF,oBACErK,OAAA;IAAKuK,SAAS,EAAC,cAAc;IAAAvD,QAAA,gBAC3BhH,OAAA,CAAClB,MAAM;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEV/G,OAAA;MAAKuK,SAAS,EAAC,mBAAmB;MAAAvD,QAAA,gBAChChH,OAAA;QAAKuK,SAAS,EAAC,gBAAgB;QAAAvD,QAAA,gBAC7BhH,OAAA;UAAKuK,SAAS,EAAC,yBAAyB;UAAAvD,QAAA,gBACtChH,OAAA;YACEwK,GAAG,EAAE,uDACH,CAAArH,eAAe,aAAfA,eAAe,wBAAA9C,sBAAA,GAAf8C,eAAe,CAAEmE,MAAM,cAAAjH,sBAAA,uBAAvBA,sBAAA,CAA0B,CAAC,CAAC,KAAIe,OAAO,CAACoI,SAAS,EAChD;YACHiB,GAAG,EAAEnC,YAAa;YAClBiC,SAAS,EAAC,oBAAoB;YAC9BG,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACF/G,OAAA;YAAKuK,SAAS,EAAC,qBAAqB;YAAAvD,QAAA,EACjCqB,aAAa,IAAIA,aAAa,CAACd,MAAM,GAAG,CAAC,GACxCc,aAAa,CAACvC,GAAG,CAAC,CAACyD,KAAK,EAAErC,KAAK,kBAC7BlH,OAAA;cAEEwK,GAAG,EAAE,uDAAuDjB,KAAK,EAAG;cACpEkB,GAAG,EAAE,aAAavD,KAAK,GAAG,CAAC,EAAG;cAC9BqD,SAAS,EAAC,iBAAiB;cAC3BG,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACC,KAAK;YAAE,GAJlCA,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF,CAAC,gBAEF/G,OAAA;cAAAgH,QAAA,EAAG;YAAuB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC9B;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/G,OAAA;UAAKuK,SAAS,EAAC,iBAAiB;UAAAvD,QAAA,gBAC9BhH,OAAA;YACE2K,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,KAAK;cACpBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAA/D,QAAA,gBAEFhH,OAAA;cAAIuK,SAAS,EAAC,eAAe;cAAAvD,QAAA,EAC1B7D,eAAe,GAAGA,eAAe,CAACoF,KAAK,GAAGnH,OAAO,CAACoH;YAAI;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,EACJ5D,eAAe,iBACdnD,OAAA;cACE2K,KAAK,EAAE;gBACLK,YAAY,EAAE,KAAK;gBACnBC,UAAU,EAAE,OAAO;gBACnBhH,KAAK,EAAE;cACT,CAAE;cAAA+C,QAAA,EAED5F,OAAO,CAACoH;YAAI;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACL,eAED/G,OAAA,CAACxB,UAAU;cACT0M,EAAE,EAAE;gBACFC,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE;kBAAEC,eAAe,EAAE;gBAAU;cAC1C,CAAE;cACFV,OAAO,EAAGxE,KAAK,IAAK;gBAClBA,KAAK,CAACC,eAAe,CAAC,CAAC;gBACvBF,cAAc,CAACC,KAAK,CAAC;cACvB,CAAE;cAAAc,QAAA,EAEDrG,UAAU,gBACTX,OAAA,CAACF,YAAY;gBAACoL,EAAE,EAAE;kBAAEjH,KAAK,EAAE;gBAAM;cAAE;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtC/G,OAAA,CAACH,kBAAkB;gBAACqL,EAAE,EAAE;kBAAEjH,KAAK,EAAE;gBAAO;cAAE;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC7C;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN/G,OAAA;YAAGuK,SAAS,EAAC,eAAe;YAAAvD,QAAA,EAAE5F,OAAO,CAACkD,OAAO,CAAC+G;UAAS;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D/G,OAAA;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL3F,OAAO,CAACkK,WAAW,KAAK,IAAI,iBAC3BtL,OAAA;YACE2K,KAAK,EAAE;cACLC,OAAO,EAAE,cAAc;cACvBW,OAAO,EAAE,UAAU;cACnBC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,KAAK;cAChBV,YAAY,EAAE,KAAK;cACnBW,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAA5E,QAAA,EACH;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EACA3F,OAAO,CAACwH,KAAK,KAAK,CAAC,gBAClB5I,OAAA,CAAC3B,GAAG;YACF6M,EAAE,EAAE;cACFN,OAAO,EAAE,cAAc;cACvBW,OAAO,EAAE,UAAU;cACnBC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,KAAK;cAChBV,YAAY,EAAE,KAAK;cACnBW,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,YAAY;cACxBR,eAAe,EAAE,SAAS;cAC1BnH,KAAK,EAAE,MAAM;cACb4H,SAAS,EAAE;YACb,CAAE;YAAA7E,QAAA,EACH;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GACJ3F,OAAO,CAACwH,KAAK,IAAI,CAAC,gBACpB5I,OAAA,CAAC3B,GAAG;YACF6M,EAAE,EAAE;cACFN,OAAO,EAAE,cAAc;cACvBW,OAAO,EAAE,UAAU;cACnBC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,KAAK;cAChBV,YAAY,EAAE,KAAK;cACnBW,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,YAAY;cACxBR,eAAe,EAAE,SAAS;cAC1BnH,KAAK,EAAE,MAAM;cACb4H,SAAS,EAAE,6BAA6B;cACxCC,SAAS,EAAE,qBAAqB;cAChC,kBAAkB,EAAE;gBAClB,IAAI,EAAE;kBAAEC,SAAS,EAAE,UAAU;kBAAEC,OAAO,EAAE;gBAAE,CAAC;gBAC3C,KAAK,EAAE;kBAAED,SAAS,EAAE,aAAa;kBAAEC,OAAO,EAAE;gBAAI,CAAC;gBACjD,MAAM,EAAE;kBAAED,SAAS,EAAE,UAAU;kBAAEC,OAAO,EAAE;gBAAE;cAC9C;YACF,CAAE;YAAAhF,QAAA,EACH;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GACJ,IAAI,eACR/G,OAAA;YACE2K,KAAK,EAAE;cACLC,OAAO,EAAE,MAAM;cACfG,cAAc,EAAE,eAAe;cAC/BF,aAAa,EAAE;YACjB,CAAE;YAAA7D,QAAA,gBAEFhH,OAAA;cAAGuK,SAAS,EAAC,gBAAgB;cAAAvD,QAAA,EAC1B1F,OAAO,CAACiG,MAAM,GAAG,CAAC,gBACjBvH,OAAA,CAAAE,SAAA;gBAAA8G,QAAA,GACG,GAAG,CAACiF,MAAM,CACTC,IAAI,CAACC,KAAK,CACR7K,OAAO,CAAC8K,MAAM,CACZ,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAAC7J,MAAM,EACpC,CACF,CAAC,GAAGnB,OAAO,CAACiG,MACd,CACF,CAAC,EACA,QAAQ,GAAGjG,OAAO,CAACiG,MAAM,GAAG,WAAW;cAAA,eACxC,CAAC,gBAEHvH,OAAA;gBAAAgH,QAAA,EAAM;cAAc;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAC3B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACJ/G,OAAA;cACE2K,KAAK,EAAE;gBACLC,OAAO,EAAExJ,OAAO,CAACmL,kBAAkB,GAAG,OAAO,GAAG,MAAM;gBACtDC,SAAS,EAAE;cACb,CAAE;cAAAxF,QAAA,EAED5F,OAAO,CAACmL,kBAAkB,GACzB,GAAGnL,OAAO,CAACmL,kBAAkB,OAAO,gBAEpCvM,OAAA;gBAAM2K,KAAK,EAAE;kBAAEC,OAAO,EAAE;gBAAO;cAAE;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YACzC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN/G,OAAA;YAAGuK,SAAS,EAAC,eAAe;YAAAvD,QAAA,EACzB7D,eAAe;YACd;YACAA,eAAe,CAACiG,SAAS;YAAA;YACvB;YACApJ,OAAA,CAAAE,SAAA;cAAA8G,QAAA,gBACEhH,OAAA;gBACE2K,KAAK,EAAE;kBACL8B,cAAc,EAAE,cAAc;kBAC9BxI,KAAK,EAAE,MAAM;kBACbyI,WAAW,EAAE;gBACf,CAAE;gBAAA1F,QAAA,GAED7D,eAAe,CAACkG,KAAK,GAAG,IAAI,GACzB,IAAIsD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CACnC1J,eAAe,CAACkG,KAClB,CAAC,GACDlG,eAAe,CAACkG,KAAK,EAAC,WAE5B;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP/G,OAAA;gBAAM2K,KAAK,EAAE;kBAAE1G,KAAK,EAAE,KAAK;kBAAEgH,UAAU,EAAE;gBAAO,CAAE;gBAAAjE,QAAA,GAC/C7D,eAAe,CAACiG,SAAS,GAAG,IAAI,GAC7B,IAAIuD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CACnC1J,eAAe,CAACiG,SAClB,CAAC,GACDjG,eAAe,CAACiG,SAAS,EAAC,WAEhC;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACP,CAAC;YAAA;YAEH;YACA/G,OAAA,CAAAE,SAAA;cAAA8G,QAAA,GACG7D,eAAe,CAACkG,KAAK,GAAG,IAAI,GACzB,IAAIsD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CACnC1J,eAAe,CAACkG,KAClB,CAAC,GACDlG,eAAe,CAACkG,KAAK,EAAC,WAE5B;YAAA,eAAE,CACH;YACC;YACJjI,OAAO,CAACgI,SAAS,IAAIhI,OAAO,CAAC0L,iBAAiB,KAAK,IAAI;YAAA;YACrD;YACA9M,OAAA,CAAAE,SAAA;cAAA8G,QAAA,gBACEhH,OAAA;gBACE2K,KAAK,EAAE;kBACL8B,cAAc,EAAE,cAAc;kBAC9BxI,KAAK,EAAE,MAAM;kBACbyI,WAAW,EAAE;gBACf,CAAE;gBAAA1F,QAAA,GAED5F,OAAO,CAACiI,KAAK,GAAG,IAAI,GACjB,IAAIsD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACzL,OAAO,CAACiI,KAAK,CAAC,GACpDjI,OAAO,CAACiI,KAAK,EAAC,WAEpB;cAAA;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP/G,OAAA;gBAAM2K,KAAK,EAAE;kBAAE1G,KAAK,EAAE,KAAK;kBAAEgH,UAAU,EAAE;gBAAO,CAAE;gBAAAjE,QAAA,GAC/C5F,OAAO,CAACgI,SAAS,GAAG,IAAI,GACrB,IAAIuD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACzL,OAAO,CAACgI,SAAS,CAAC,GACxDhI,OAAO,CAACgI,SAAS,EAAC,WAExB;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eACP,CAAC;YAAA;YAEH;YACA/G,OAAA,CAAAE,SAAA;cAAA8G,QAAA,GACG5F,OAAO,CAACiI,KAAK,GAAG,IAAI,GACjB,IAAIsD,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACzL,OAAO,CAACiI,KAAK,CAAC,GACpDjI,OAAO,CAACiI,KAAK,EAAC,WAEpB;YAAA,eAAE;UACH;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACJ/G,OAAA;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/G,OAAA;YAAKuK,SAAS,EAAC,gBAAgB;YAAAvD,QAAA,gBAC7BhH,OAAA;cAAMuK,SAAS,EAAC,sBAAsB;cAAAvD,QAAA,EAAC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD/G,OAAA;cAAKuK,SAAS,EAAC,eAAe;cAAAvD,QAAA,EAC3B5F,OAAO,CAAC0H,MAAM,IAAI1H,OAAO,CAAC0H,MAAM,CAACvB,MAAM,GAAG,CAAC,GAC1CnG,OAAO,CAAC0H,MAAM,CAAChD,GAAG,CAAC,CAAC7B,KAAK,EAAEiD,KAAK,KAAK;gBACnC;gBACA,MAAM6F,iBAAiB,GAAIC,SAAS,IAAK;kBACvC;kBACA,MAAMC,UAAU,GAAGD,SAAS,CAACtH,WAAW,CAAC,CAAC;;kBAE1C;kBACA,MAAMwH,aAAa,GAAG;oBACpBC,KAAK,EAAE,SAAS;oBAChBC,KAAK,EAAE,SAAS;oBAChBC,GAAG,EAAE,SAAS;oBACdC,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE,SAAS;oBACfC,MAAM,EAAE,SAAS;oBACjBC,MAAM,EAAE,SAAS;oBACjBC,MAAM,EAAE,SAAS;oBACjBC,IAAI,EAAE,SAAS;oBACfC,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE,SAAS;oBACfC,IAAI,EAAE,SAAS;oBACfC,KAAK,EAAE,SAAS;oBAChBC,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE,SAAS;oBACfC,MAAM,EAAE,SAAS;oBACjBC,IAAI,EAAE,SAAS;oBACfC,KAAK,EAAE,SAAS;oBAChBC,MAAM,EAAE,SAAS;oBACjBC,IAAI,EAAE,SAAS;oBACfC,GAAG,EAAE,SAAS;oBACdC,KAAK,EAAE,SAAS;oBAChBC,IAAI,EAAE,SAAS;oBACfC,QAAQ,EAAE;kBACZ,CAAC;;kBAED;kBACA,IAAIxB,aAAa,CAACD,UAAU,CAAC,EAAE;oBAC7B,OAAOC,aAAa,CAACD,UAAU,CAAC;kBAClC;;kBAEA;kBACA,KAAK,MAAM,CAAC0B,UAAU,EAAEC,QAAQ,CAAC,IAAIC,MAAM,CAACC,OAAO,CACjD5B,aACF,CAAC,EAAE;oBACD,IAAID,UAAU,CAACjH,QAAQ,CAAC2I,UAAU,CAAC,EAAE;sBACnC,OAAOC,QAAQ;oBACjB;kBACF;;kBAEA;kBACA,OAAO,SAAS;gBAClB,CAAC;;gBAED;gBACA,MAAMG,UAAU,GAAGhC,iBAAiB,CAAC9I,KAAK,CAAC;;gBAE3C;gBACA,MAAM+K,YAAY,GAChBD,UAAU,KAAK,SAAS,IACxBA,UAAU,KAAK,SAAS,IACxBA,UAAU,KAAK,SAAS,IACxB9K,KAAK,CAACyB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC,OAAO,CAAC,IACrC/B,KAAK,CAACyB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC,OAAO,CAAC,IACrC/B,KAAK,CAACyB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC,OAAO,CAAC,IACrC/B,KAAK,CAACyB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC,OAAO,CAAC,IACrC/B,KAAK,CAACyB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC,WAAW,CAAC,IACzC/B,KAAK,CAACyB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC,UAAU,CAAC;gBAE1C,oBACEhG,OAAA;kBAEEuK,SAAS,EAAE,gBACT7I,aAAa,KAAKuC,KAAK,GAAG,UAAU,GAAG,EAAE,EACxC;kBACH0G,KAAK,EAAE;oBACLS,eAAe,EAAE2D,UAAU;oBAC3BvD,MAAM,EAAEwD,YAAY,GAAG,mBAAmB,GAAG,MAAM;oBACnDC,QAAQ,EAAE;kBACZ,CAAE;kBACF1G,KAAK,EAAEtE,KAAM;kBACbyG,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAC1D,KAAK,CAAE;kBAAA+C,QAAA,EAEvC+H,UAAU,KAAK,SAAS,iBACvB/O,OAAA;oBAAKuK,SAAS,EAAC,oBAAoB;oBAAAvD,QAAA,EAChC/C,KAAK,CAACiL,MAAM,CAAC,CAAC;kBAAC;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACN,GAhBIG,KAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBP,CAAC;cAEV,CAAC,CAAC,gBAEF/G,OAAA;gBAAAgH,QAAA,EAAG;cAGH;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACJ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLrF,aAAa,gBACZ1B,OAAA;YAAGuK,SAAS,EAAC,qBAAqB;YAAAvD,QAAA,GAAC,kBACjB,EAACtF,aAAa;UAAA;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,GACFhD,gBAAgB,CAACE,KAAK,gBACxBjE,OAAA;YAAGuK,SAAS,EAAC,qBAAqB;YAACI,KAAK,EAAE;cAAE1G,KAAK,EAAE;YAAM,CAAE;YAAA+C,QAAA,EAAC;UAE5D;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GACF,IAAI,eACR/G,OAAA;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/G,OAAA;YAAKuK,SAAS,EAAC,eAAe;YAAAvD,QAAA,gBAC5BhH,OAAA;cAAMuK,SAAS,EAAC,qBAAqB;cAAAvD,QAAA,EAAC;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD/G,OAAA;cAAKuK,SAAS,EAAC,cAAc;cAAAvD,QAAA,EAC1Ba,iBAAiB,CAAC,CAAC,IAAIA,iBAAiB,CAAC,CAAC,CAACN,MAAM,GAAG,CAAC,GACpDM,iBAAiB,CAAC,CAAC,CAAC/B,GAAG,CAAC,CAAC5B,IAAI,EAAEgD,KAAK,kBAClClH,OAAA;gBAEEuK,SAAS,EAAE,eACT3I,YAAY,KAAKsC,IAAI,GAAG,UAAU,GAAG,EAAE,EACtC;gBACHwG,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC1D,IAAI,CAAE;gBAAA8C,QAAA,EAErC9C;cAAI,GANAgD,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOJ,CACT,CAAC,gBAEF/G,OAAA;gBAAAgH,QAAA,EAAG;cAAyB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAChC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLnF,YAAY,gBACX5B,OAAA;YAAAgH,QAAA,GAAG,iBAAe,EAACpF,YAAY;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,GAClChD,gBAAgB,CAACG,IAAI,gBACvBlE,OAAA;YAAG2K,KAAK,EAAE;cAAE1G,KAAK,EAAE;YAAM,CAAE;YAAA+C,QAAA,EAAC;UAAoB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAClD,IAAI,EAAE,GAAG,eACb/G,OAAA;YAAKuK,SAAS,EAAC,gBAAgB;YAAAvD,QAAA,GAC5B5F,OAAO,CAACwH,KAAK,IAAI,CAAC,gBACjB5I,OAAA;cACEuK,SAAS,EAAC,6BAA6B;cACvCI,KAAK,EAAE;gBACLwE,MAAM,EAAE,aAAa;gBACrBC,aAAa,EAAE,MAAM;gBACrBhE,eAAe,EAAE,SAAS;gBAC1BI,MAAM,EAAE,oBAAoB;gBAC5BvH,KAAK,EAAE,SAAS;gBAChBoL,KAAK,EAAE,MAAM;gBACbC,SAAS,EAAE;cACb,CAAE;cAAAtI,QAAA,EACH;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEN/G,OAAA;cACEuK,SAAS,EAAC,8BAA8B;cACxCG,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACtH,OAAO,CAAE;cAAA4F,QAAA,EACzC;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACD/G,OAAA;cACEuK,SAAS,EAAC,gCAAgC;cAC1CG,OAAO,EAAEA,CAAA,KAAMlK,uBAAuB,CAAC,IAAI,CAAE,CAAC;cAAA;cAAAwG,QAAA,EAC/C;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELtG,iBAAiB,iBAChBT,OAAA,CAACb,gBAAgB;YACfoQ,IAAI,EAAEhP,oBAAqB;YAC3BiP,OAAO,EAAEA,CAAA,KAAMhP,uBAAuB,CAAC,KAAK,CAAE;YAC9C6E,SAAS,EAAEjE,OAAQ,CAAC;UAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/G,OAAA;QAAKuK,SAAS,EAAC,gBAAgB;QAAAvD,QAAA,gBAE7BhH,OAAA;UAAKuK,SAAS,EAAC,uBAAuB;UAAAvD,QAAA,EACnC,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAClB,GAAG,CAChD,CAAC2J,OAAO,EAAEvI,KAAK,kBACblH,OAAA;YAEEuK,SAAS,EAAE,uBACTzI,gBAAgB,CAACoF,KAAK,CAAC,GAAG,MAAM,GAAG,EAAE,EACpC;YACHwD,OAAO,EAAEA,CAAA,KAAMjC,mBAAmB,CAACvB,KAAK,CAAE;YAAAF,QAAA,gBAE1ChH,OAAA;cAAKuK,SAAS,EAAC,oBAAoB;cAAAvD,QAAA,GAChCyI,OAAO,eACRzP,OAAA,CAACjB,qBAAqB;gBACpBwL,SAAS,EAAE,oBACTzI,gBAAgB,CAACoF,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE;cACvC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN/G,OAAA;cAAKuK,SAAS,EAAC,qBAAqB;cAAAvD,QAAA,GACjCyI,OAAO,KAAK,UAAU,iBACrBzP,OAAA;gBAAKuK,SAAS,EAAC,kBAAkB;gBAAAvD,QAAA,gBAS/BhH,OAAA;kBAAKuK,SAAS,EAAC,iBAAiB;kBAAAvD,QAAA,gBAC9BhH,OAAA;oBAAG2K,KAAK,EAAE;sBAAEgB,QAAQ,EAAE9K,QAAQ,GAAG,MAAM,GAAG;oBAAO,CAAE;oBAAAmG,QAAA,gBACjDhH,OAAA;sBAAMuK,SAAS,EAAC,OAAO;sBAAAvD,QAAA,EAAC;oBAAW;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACzC3F,OAAO,CAACsO,UAAU;kBAAA;oBAAA9I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eAKJ/G,OAAA;oBAAG2K,KAAK,EAAE;sBAAEgB,QAAQ,EAAE9K,QAAQ,GAAG,MAAM,GAAG;oBAAO,CAAE;oBAAAmG,QAAA,gBACjDhH,OAAA;sBAAMuK,SAAS,EAAC,OAAO;sBAAAvD,QAAA,EAAC;oBAAkB;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAAC,GAAG,EACpD3F,OAAO,CAACuO,eAAe;kBAAA;oBAAA/I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEN/G,OAAA;kBACE2K,KAAK,EAAE;oBACLgB,QAAQ,EAAE9K,QAAQ,GAAG,MAAM,GAAG,MAAM;oBACpCyO,SAAS,EAAE;kBACb,CAAE;kBAAAtI,QAAA,EAED5F,OAAO,CAACwO;gBAAW;kBAAAhJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EACA0I,OAAO,KAAK,YAAY,iBACvBzP,OAAA;gBAAKuK,SAAS,EAAC,kBAAkB;gBAAAvD,QAAA,gBAE/BhH,OAAA;kBAAAgH,QAAA,EAAG;gBAAuB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9B/G,OAAA;kBAAAgH,QAAA,gBACEhH,OAAA;oBAAAgH,QAAA,EACG7D,eAAe,GACZA,eAAe,CAAC0M,UAAU,CAACR,KAAK,GAChCjO,OAAO,CAAC0O,mBAAmB,CAACT;kBAAK;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,EAAC,GAAG,EAAC,MACV,EAAC,IAAI,eACT/G,OAAA;oBAAAgH,QAAA,GACG,GAAG,EACH7D,eAAe,GACZA,eAAe,CAAC0M,UAAU,CAACtI,MAAM,GACjCnG,OAAO,CAAC0O,mBAAmB,CAACvI,MAAM;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,EAAC,GAAG,EAAC,MACV,EAAC,IAAI,eACT/G,OAAA;oBAAAgH,QAAA,EACG7D,eAAe,GACZA,eAAe,CAAC0M,UAAU,CAACE,MAAM,GACjC3O,OAAO,CAAC0O,mBAAmB,CAACC;kBAAM;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,EAAC,GAAG,EAAC,IAEhB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ/G,OAAA;kBAAAgH,QAAA,GAAG,UACO,EAAC,GAAG,eACZhH,OAAA;oBAAAgH,QAAA,EACG7D,eAAe,GACZA,eAAe,CAAC0M,UAAU,CAACG,MAAM,GACjC5O,OAAO,CAAC0O,mBAAmB,CAACE;kBAAM;oBAAApJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,EAAC,GAAG,EAAC,KAEhB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EACA0I,OAAO,KAAK,SAAS,iBACpBzP,OAAA;gBAAKuK,SAAS,EAAC,kBAAkB;gBAAAvD,QAAA,eAC/BhH,OAAA,CAAC1B,MAAM;kBACL4M,EAAE,EAAE;oBACFE,eAAe,EAAE,aAAa;oBAC9BnH,KAAK,EAAE,SAAS;oBAChBwH,YAAY,EAAE,MAAM;oBACpBD,MAAM,EAAE,mBAAmB;oBAC3B6D,KAAK,EAAE,KAAK;oBACZ9D,OAAO,EAAE,WAAW;oBACpB0E,QAAQ,EAAE,OAAO;oBACjBrF,OAAO,EAAE,MAAM;oBACfG,cAAc,EAAE,eAAe;oBAC/BD,UAAU,EAAE,QAAQ;oBACpBoF,aAAa,EAAE,MAAM;oBACrB,SAAS,EAAE;sBACT9E,eAAe,EAAE,MAAM;sBACvBnH,KAAK,EAAE;oBACT;kBACF,CAAE;kBACFyG,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAItJ,OAAO,CAAC+O,OAAO,EAAE;sBACnB;sBACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;sBACxCF,IAAI,CAACG,IAAI,GAAG,uDAAuDnP,OAAO,CAAC+O,OAAO,EAAE,CAAC,CAAC;sBACtFC,IAAI,CAACI,QAAQ,GAAG,eAAepP,OAAO,CAAC4D,GAAG,EAAE,CAAC,CAAC;sBAC9CqL,QAAQ,CAAC7J,IAAI,CAACiK,WAAW,CAACL,IAAI,CAAC;sBAC/BA,IAAI,CAACM,KAAK,CAAC,CAAC;sBACZL,QAAQ,CAAC7J,IAAI,CAACmK,WAAW,CAACP,IAAI,CAAC;oBACjC,CAAC,MAAM;sBACLQ,KAAK,CAAC,oCAAoC,CAAC;oBAC7C;kBACF,CAAE;kBAAA5J,QAAA,gBAGFhH,OAAA;oBACEwK,GAAG,EAAC,0BAA0B;oBAC9BC,GAAG,EAAC,cAAc;oBAClBE,KAAK,EAAE;sBACL0E,KAAK,EAAE,MAAM;sBACbU,MAAM,EAAE,MAAM;sBACdrD,WAAW,EAAE;oBACf;kBAAE;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF/G,OAAA;oBAAAgH,QAAA,EAAM;kBAAiB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAE9B/G,OAAA,CAACtB,UAAU;oBAACiM,KAAK,EAAE;sBAAEQ,UAAU,EAAE;oBAAO;kBAAE;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAcA0I,OAAO,KAAK,MAAM,IAAIrO,OAAO,CAACyP,IAAI,iBACjC7Q,OAAA;gBAAKuK,SAAS,EAAC,gBAAgB;gBAAAvD,QAAA,EAC5B5F,OAAO,CAACyP,IAAI,CAACtJ,MAAM,GAAG,CAAC,GACtBnG,OAAO,CAACyP,IAAI,CAAC/K,GAAG,CAAC,CAACgL,GAAG,EAAE5J,KAAK,kBAC1BlH,OAAA;kBAAAgH,QAAA,EAAmB8J;gBAAG,GAAX5J,KAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAC9B,CAAC,gBAEF/G,OAAA;kBAAAgH,QAAA,EAAG;gBAAkB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cACzB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAnKDG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoKP,CAET;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN/G,OAAA;UAAKuK,SAAS,EAAC,oBAAoB;UAAAvD,QAAA,gBAcjChH,OAAA;YAAKuK,SAAS,EAAC,gCAAgC;YAAAvD,QAAA,EAC5C,CACC,oBAAoB,EACpB,mBAAmB,EACnB,wBAAwB,CACzB,CAAClB,GAAG,CAAC,CAAC2J,OAAO,EAAEvI,KAAK;cAAA,IAAA6J,iBAAA,EAAAC,qBAAA;cAAA,oBACnBhR,OAAA;gBAAiBuK,SAAS,EAAC,8BAA8B;gBAAAvD,QAAA,gBACvDhH,OAAA;kBACEuK,SAAS,EAAC,6BAA6B;kBACvCG,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAACP,KAAK,EAAE,UAAU,CAAE;kBAAAF,QAAA,GAErDyI,OAAO,eACRzP,OAAA;oBAAMuK,SAAS,EAAC,2BAA2B;oBAAAvD,QAAA,EACxChF,wBAAwB,CAACkF,KAAK,CAAC,GAAG,GAAG,GAAG;kBAAG;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACL/E,wBAAwB,CAACkF,KAAK,CAAC,iBAC9BlH,OAAA;kBAAKuK,SAAS,EAAC,8BAA8B;kBAAAvD,QAAA,EAC1CyI,OAAO,KAAK,oBAAoB,gBAC/BzP,OAAA;oBAAAgH,QAAA,IAAA+J,iBAAA,GACG3P,OAAO,CAAC6P,QAAQ,cAAAF,iBAAA,uBAAhBA,iBAAA,CACGG,KAAK,CAAC,oBAAoB,CAAC,CAC5BpL,GAAG,CAAC,CAACqL,KAAK,EAAEC,GAAG,kBACdpR,OAAA;sBAAAgH,QAAA,GACG,GAAG,EAAC,YACK,EAACmK,KAAK,EAAC,gBACnB;oBAAA,GAHSC,GAAG;sBAAAxK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGR,CACL,CAAC,EAAE,GAAG,GAAAiK,qBAAA,GACR5P,OAAO,CAACiQ,iCAAiC,cAAAL,qBAAA,uBAAzCA,qBAAA,CAA2CE,KAAK,CAC/C,oBACF,CAAC,CAACpL,GAAG,CAAC,CAACqL,KAAK,EAAEC,GAAG,kBACfpR,OAAA;sBAAAgH,QAAA,GAAc,mCACqB,EAACmK,KAAK,EAAC,gBAE1C;oBAAA,GAHSC,GAAG;sBAAAxK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGR,CACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,GACH0I,OAAO,KAAK,mBAAmB,gBACjCzP,OAAA;oBAAAgH,QAAA,EACG5F,OAAO,CAACkQ,wBAAwB,CAC9BJ,KAAK,CAAC,IAAI,CAAC,CACXpL,GAAG,CAAC,CAACqL,KAAK,EAAEC,GAAG,kBACdpR,OAAA;sBAAAgH,QAAA,EAAemK;oBAAK,GAAXC,GAAG;sBAAAxK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,GACH0I,OAAO,KAAK,wBAAwB,gBACtCzP,OAAA;oBAAAgH,QAAA,EACG5F,OAAO,CAACmQ,8BAA8B,CACpCL,KAAK,CAAC,IAAI,CAAC,CACXpL,GAAG,CAAC,CAACqL,KAAK,EAAEjK,KAAK,kBAChBlH,OAAA;sBAAAgH,QAAA,EAAiBmK,KAAK,CAACK,IAAI,CAAC;oBAAC,GAApBtK,KAAK;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAoB,CACnC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,GAEL;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA,GAnDOG,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoDV,CAAC;YAAA,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/G,OAAA;YAAKuK,SAAS,EAAC,cAAc;YAAAvD,QAAA,eAC3BhH,OAAA,CAACP,WAAW;cACV6E,OAAO,EAAElD,OAAO,CAACkD,OAAQ;cACzBmN,cAAc,EAAEtN,kBAAmB;cACnCuN,aAAa,EAAEtQ,OAAO,CAAC4D,GAAI,CAAC;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL,CAAC/F,WAAW,IAAIE,eAAe,kBAC9BlB,OAAA;QAAKuK,SAAS,EAAE,SAASrJ,eAAe,GAAG,SAAS,GAAG,SAAS,EAAG;QAAA8F,QAAA,eACjEhH,OAAA;UAAKuK,SAAS,EAAC,eAAe;UAAAvD,QAAA,gBAC5BhH,OAAA;YAAQuK,SAAS,EAAC,aAAa;YAACG,OAAO,EAAEvD,gBAAiB;YAAAH,QAAA,eACxDhH,OAAA,CAACnB,SAAS;cAACqF,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAM;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACT/G,OAAA;YAAQuK,SAAS,EAAC,YAAY;YAACG,OAAO,EAAEtD,eAAgB;YAAAJ,QAAA,eACtDhH,OAAA,CAACrB,cAAc;cAACuF,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAM;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACT/G,OAAA;YACEwK,GAAG,EAAE,uDAAuDpJ,OAAO,CAACkG,MAAM,CAACxG,kBAAkB,CAAC,EAAG;YACjG2J,GAAG,EAAE,GAAG3J,kBAAkB,GAAG,CAAC,EAAG;YACjCyJ,SAAS,EAAC;UAAa;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACF/G,OAAA;YAAQuK,SAAS,EAAC,YAAY;YAACG,OAAO,EAAElD,eAAgB;YAAAR,QAAA,eACtDhH,OAAA,CAACpB,iBAAiB;cAACsF,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAM;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACD/G,OAAA;QACE2K,KAAK,EAAE;UACL0E,KAAK,EAAE,KAAK;UACZC,SAAS,EAAE,QAAQ;UACnBqC,MAAM,EAAE,MAAM;UACdjG,SAAS,EAAE,MAAM;UACjBV,YAAY,EAAE;QAChB;MAAE;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/G,OAAA;QAAKuK,SAAS,EAAC,iBAAiB;QAACI,KAAK,EAAE;UAAEY,OAAO,EAAE;QAAS,CAAE;QAAAvE,QAAA,gBAC5DhH,OAAA;UAAAgH,QAAA,EAAI;QAAgB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzB/G,OAAA,CAACR,eAAe;UAAC6F,SAAS,EAAEjE,OAAO,CAAC4D;QAAI;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACN/G,OAAA;QAAKuK,SAAS,EAAC,iBAAiB;QAAAvD,QAAA,gBAC9BhH,OAAA;UAAKuK,SAAS,EAAC,gBAAgB;UAAAvD,QAAA,gBAC7BhH,OAAA;YAAAgH,QAAA,EAAI;UAAO;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB/G,OAAA;YACE0K,OAAO,EAAEA,CAAA,KAAMlI,iBAAiB,CAAC,IAAI,CAAE;YACvC+H,SAAS,EAAC,kBAAkB;YAAAvD,QAAA,EAC7B;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELxE,cAAc,iBACbvC,OAAA;UAAKuK,SAAS,EAAC,qBAAqB;UAAAvD,QAAA,eAClChH,OAAA;YAAKuK,SAAS,EAAC,uBAAuB;YAAAvD,QAAA,gBACpChH,OAAA;cACEuK,SAAS,EAAC,gBAAgB;cAC1BG,OAAO,EAAEA,CAAA,KAAMlI,iBAAiB,CAAC,KAAK,CAAE;cACxCmI,KAAK,EAAE;gBACLS,eAAe,EAAEzI,KAAK,GAAG,aAAa,GAAG;cAC3C,CAAE;cACFiP,YAAY,EAAEA,CAAA,KAAMhP,QAAQ,CAAC,IAAI,CAAE;cACnCiP,YAAY,EAAEA,CAAA,KAAMjP,QAAQ,CAAC,KAAK,CAAE;cAAAoE,QAAA,eAEpChH,OAAA,CAACnB,SAAS;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACT/G,OAAA;cAAAgH,QAAA,EAAI;YAAc;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB/G,OAAA;cAAM8R,QAAQ,EAAElI,kBAAmB;cAACW,SAAS,EAAC,aAAa;cAAAvD,QAAA,gBACzDhH,OAAA;gBAAKuK,SAAS,EAAC,YAAY;gBAAAvD,QAAA,gBACzBhH,OAAA;kBAAAgH,QAAA,EAAO;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxB/G,OAAA;kBACE0H,IAAI,EAAC,MAAM;kBACXqK,KAAK,EAAEhP,YAAa;kBACpBiP,QAAQ,EAAGnI,CAAC,IAAK7G,eAAe,CAAC6G,CAAC,CAACoI,MAAM,CAACF,KAAK,CAAE;kBACjDG,QAAQ;kBACR3H,SAAS,EAAC;gBAAc;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/G,OAAA;gBAAKuK,SAAS,EAAC,YAAY;gBAAAvD,QAAA,gBACzBhH,OAAA;kBAAAgH,QAAA,EAAO;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB/G,OAAA;kBAAKuK,SAAS,EAAC,aAAa;kBAAAvD,QAAA,EACzB,CAAC,GAAGiD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACnE,GAAG,CAAC,CAACqM,CAAC,EAAEjL,KAAK,KAAK;oBAC/B,MAAMkL,WAAW,GAAGlL,KAAK,GAAG,CAAC;oBAC7B,oBACElH,OAAA,CAACvB,MAAM;sBAEL8L,SAAS,EAAC,MAAM;sBAChBtG,KAAK,EACHmO,WAAW,KAAKzP,KAAK,IAAIF,MAAM,CAAC,GAC5B,SAAS,GACT,SACL;sBACDyB,IAAI,EAAE,EAAG;sBACTwG,OAAO,EAAEA,CAAA,KAAMhI,SAAS,CAAC0P,WAAW,CAAE;sBACtCR,YAAY,EAAEA,CAAA,KAAMhP,QAAQ,CAACwP,WAAW,CAAE;sBAC1CP,YAAY,EAAEA,CAAA,KAAMjP,QAAQ,CAACH,MAAM,CAAE;sBACrCkI,KAAK,EAAE;wBAAEwE,MAAM,EAAE;sBAAU;oBAAE,GAXxBjI,KAAK;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAYX,CAAC;kBAEN,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN/G,OAAA;gBAAKuK,SAAS,EAAC,YAAY;gBAAAvD,QAAA,gBACzBhH,OAAA;kBAAAgH,QAAA,EAAO;gBAAW;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1B/G,OAAA;kBACE+R,KAAK,EAAElP,OAAQ;kBACfmP,QAAQ,EAAGnI,CAAC,IAAK/G,UAAU,CAAC+G,CAAC,CAACoI,MAAM,CAACF,KAAK,CAAE;kBAC5CG,QAAQ;kBACR3H,SAAS,EAAC,iBAAiB;kBAC3B8H,IAAI,EAAE;gBAAE;kBAAAzL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN/G,OAAA;gBAAQ0H,IAAI,EAAC,QAAQ;gBAAC6C,SAAS,EAAC,mBAAmB;gBAAAvD,QAAA,EAAC;cAEpD;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACD/G,OAAA;UAAK2K,KAAK,EAAE;YAAE2H,WAAW,EAAE;UAAO,CAAE;UAAAtL,QAAA,gBAClChH,OAAA,CAAC3B,GAAG;YAACkM,SAAS,EAAC,gBAAgB;YAAAvD,QAAA,eAC7BhH,OAAA,CAACd,SAAS;cAACqT,WAAW,EAAEpI;YAAgB;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EAELzF,OAAO,CAACiG,MAAM,GAAG,CAAC,GACjBjG,OAAO,CAACwE,GAAG,CAAC,CAACwG,MAAM,EAAEpF,KAAK,kBACxBlH,OAAA;YAAiBuK,SAAS,EAAC,aAAa;YAAAvD,QAAA,gBACtChH,OAAA,CAAC3B,GAAG;cAACkM,SAAS,EAAC,iBAAiB;cAAAvD,QAAA,gBAC9BhH,OAAA;gBAAAgH,QAAA,EAAKsF,MAAM,CAACvJ;cAAY;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9B/G,OAAA;gBAAAgH,QAAA,EAAIsF,MAAM,CAACkG;cAAU;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACN/G,OAAA;cAAAgH,QAAA,EAAI,GAAG,CAACiF,MAAM,CAACK,MAAM,CAAC7J,MAAM;YAAC;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC/G,OAAA;cAAAgH,QAAA,EAAIsF,MAAM,CAACzJ;YAAO;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GANfG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN,CAAC,gBAEF/G,OAAA;YACE2K,KAAK,EAAE;cACL2E,SAAS,EAAE,QAAQ;cACnB5D,SAAS,EAAE,MAAM;cACjBd,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpB2H,GAAG,EAAE;YACP,CAAE;YAAAzL,QAAA,gBAEFhH,OAAA,CAACN,oBAAoB;cAACwE,IAAI,EAAE,EAAG;cAACD,KAAK,EAAC;YAAM;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE/C/G,OAAA;cAAGuK,SAAS,EAAC,YAAY;cAAAvD,QAAA,EAAC;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN/G,OAAA,CAACZ,MAAM;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACTxD,kBAAkB,iBACjBvD,OAAA,CAACL,YAAY;MACX6P,OAAO,EAAEjL,uBAAwB;MACjCc,SAAS,EAAE5B;IAAa;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACF,eAED/G,OAAA,CAACJ,mBAAmB;MAAC2P,IAAI,EAAE5L,QAAS;MAAC6L,OAAO,EAAE7F;IAAgB;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGhElD,WAAW,iBACV7D,OAAA;MACEuK,SAAS,EAAC,4BAA4B;MACtCI,KAAK,EAAE;QACLsE,QAAQ,EAAE,OAAO;QACjByD,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbvH,eAAe,EAAE,SAAS;QAC1BnH,KAAK,EAAE,OAAO;QACdsH,OAAO,EAAE,WAAW;QACpBE,YAAY,EAAE,KAAK;QACnBI,SAAS,EAAE,2BAA2B;QACtC+G,MAAM,EAAE,IAAI;QACZhI,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpB2H,GAAG,EAAE,MAAM;QACX3G,SAAS,EAAE;MACb,CAAE;MAAA9E,QAAA,eAEFhH,OAAA;QAAK2K,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAE2H,GAAG,EAAE;QAAO,CAAE;QAAAzL,QAAA,gBACjEhH,OAAA;UACEwK,GAAG,EAAE,uDACH,CAAArH,eAAe,aAAfA,eAAe,wBAAA7C,sBAAA,GAAf6C,eAAe,CAAEmE,MAAM,cAAAhH,sBAAA,uBAAvBA,sBAAA,CAA0B,CAAC,CAAC,KAAIc,OAAO,CAACoI,SAAS,EAChD;UACHiB,GAAG,EAAErJ,OAAO,CAACoH,IAAK;UAClBmC,KAAK,EAAE;YACL0E,KAAK,EAAE,MAAM;YACbU,MAAM,EAAE,MAAM;YACd8C,SAAS,EAAE,OAAO;YAClBpH,YAAY,EAAE;UAChB;QAAE;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF/G,OAAA;UAAAgH,QAAA,gBACEhH,OAAA;YAAK2K,KAAK,EAAE;cAAEM,UAAU,EAAE;YAAO,CAAE;YAAAjE,QAAA,EAAC;UAAa;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvD/G,OAAA;YAAK2K,KAAK,EAAE;cAAEgB,QAAQ,EAAE;YAAO,CAAE;YAAA3E,QAAA,EAC9B7D,eAAe,GAAGA,eAAe,CAACoF,KAAK,GAAGnH,OAAO,CAACoH;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA0IE,CAAC;AAEV;AAAC3G,EAAA,CAx5CQD,WAAW;EAAA,QAKD5B,aAAa,EAMfS,SAAS,EACPC,WAAW,EAKNI,OAAO;AAAA;AAAAyT,EAAA,GAjBtB3S,WAAW;AA05CpB,eAAeA,WAAW;AAAC,IAAA2S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}