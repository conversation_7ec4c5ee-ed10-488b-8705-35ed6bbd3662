{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\home\\\\Sustainability.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Box, Typography, Card, CardContent, CardActions, CardMedia, useTheme, useMediaQuery } from \"@mui/material\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SustainabilitySection = () => {\n  _s();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isSmallScreen = useMediaQuery(theme.breakpoints.down(\"md\")); // Mobile-friendly\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"build-package-section\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        background: \"#6c7c59\",\n        display: \"flex\",\n        flexDirection: {\n          xs: \"column\",\n          md: \"row\"\n        },\n        // Stack on mobile\n        justifyContent: \"center\",\n        borderRadius: \"1.25rem\",\n        boxShadow: \"0px 4px 4px rgba(0, 0, 0, 0.25)\",\n        height: {\n          xs: \"auto\",\n          md: \"40rem\"\n        } // Auto height on small screens\n      },\n      className: \"package-card\",\n      children: [/*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          color: \"#eae3e4\",\n          padding: {\n            xs: \"2rem\",\n            md: \"3rem\"\n          },\n          // Reduce padding on smaller screens\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"space-between\",\n          flex: 1,\n          alignItems: \"flex-start\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          fontSize: isSmallScreen ? \"2rem\" : \"3.5rem\" // Smaller font on mobile\n          ,\n          fontWeight: \"600\",\n          fontFamily: \"Horizon\",\n          sx: {\n            paddingTop: {\n              xs: \"1rem\",\n              md: \"2rem\"\n            },\n            paddingBottom: {\n              xs: \"1rem\",\n              md: \"0\"\n            }\n          },\n          children: \"What is TDG?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          fontFamily: \"Montserrat\",\n          fontSize: isSmallScreen ? \"0.875rem\" : \"1rem\" // Adjust body text\n          ,\n          sx: {\n            marginTop: {\n              xs: \"-1rem\",\n              md: \"-2.5rem\"\n            }\n          },\n          children: \"TheDesignGrit is here to spotlight Egyptian design. We\\u2019re giving local brands the platform they deserve, connecting their craftsmanship with those who value it most. It\\u2019s about honoring tradition, celebrating innovation, and helping you discover pieces that make you feel most at home.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn\",\n            style: {\n              color: \"black\",\n              background: \"#eae3e4\",\n              fontWeight: \"bold\",\n              fontSize: isSmallScreen ? \"0.875rem\" : \"1rem\",\n              // Button text responsive\n              padding: isSmallScreen ? \"0.5rem 1rem\" : \"0.75rem 1.5rem\"\n            },\n            onClick: () => navigate(\"/about\"),\n            children: \"Find Out More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardMedia, {\n        sx: {\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"Assets/susSection-768.webp\",\n          srcSet: \"\\r Assets/susSection-480.webp 480w,\\r Assets/susSection-768.webp 768w,\\r Assets/susSection-1200.webp 1200w\\r \",\n          sizes: \"(max-width: 768px) 100vw, 50vw\",\n          alt: \"Green Bowl Beach\",\n          loading: \"lazy\",\n          style: {\n            width: \"100%\",\n            height: \"100%\",\n            objectFit: \"cover\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(SustainabilitySection, \"I2ZH0+YmK8Ar7ZJd+m0Gk650jNg=\", false, function () {\n  return [useNavigate, useTheme, useMediaQuery];\n});\n_c = SustainabilitySection;\nexport default SustainabilitySection;\nvar _c;\n$RefreshReg$(_c, \"SustainabilitySection\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "CardMedia", "useTheme", "useMediaQuery", "useNavigate", "jsxDEV", "_jsxDEV", "SustainabilitySection", "_s", "navigate", "theme", "isSmallScreen", "breakpoints", "down", "className", "children", "sx", "background", "display", "flexDirection", "xs", "md", "justifyContent", "borderRadius", "boxShadow", "height", "color", "padding", "flex", "alignItems", "variant", "fontSize", "fontWeight", "fontFamily", "paddingTop", "paddingBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "style", "onClick", "src", "srcSet", "sizes", "alt", "loading", "width", "objectFit", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/home/<USER>"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Card,\r\n  CardContent,\r\n  CardActions,\r\n  CardMedia,\r\n  useTheme,\r\n  useMediaQuery,\r\n} from \"@mui/material\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nconst SustainabilitySection = () => {\r\n  const navigate = useNavigate();\r\n  const theme = useTheme();\r\n  const isSmallScreen = useMediaQuery(theme.breakpoints.down(\"md\")); // Mobile-friendly\r\n\r\n  return (\r\n    <Box className=\"build-package-section\">\r\n      <Card\r\n        sx={{\r\n          background: \"#6c7c59\",\r\n          display: \"flex\",\r\n          flexDirection: { xs: \"column\", md: \"row\" }, // Stack on mobile\r\n          justifyContent: \"center\",\r\n          borderRadius: \"1.25rem\",\r\n          boxShadow: \"0px 4px 4px rgba(0, 0, 0, 0.25)\",\r\n          height: { xs: \"auto\", md: \"40rem\" }, // Auto height on small screens\r\n        }}\r\n        className=\"package-card\"\r\n      >\r\n        <CardContent\r\n          sx={{\r\n            color: \"#eae3e4\",\r\n            padding: { xs: \"2rem\", md: \"3rem\" }, // Reduce padding on smaller screens\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            justifyContent: \"space-between\",\r\n            flex: 1,\r\n            alignItems: \"flex-start\",\r\n          }}\r\n        >\r\n          <Typography\r\n            variant=\"h3\"\r\n            fontSize={isSmallScreen ? \"2rem\" : \"3.5rem\"} // Smaller font on mobile\r\n            fontWeight=\"600\"\r\n            fontFamily={\"Horizon\"}\r\n            sx={{\r\n              paddingTop: { xs: \"1rem\", md: \"2rem\" },\r\n              paddingBottom: { xs: \"1rem\", md: \"0\" },\r\n            }}\r\n          >\r\n            What is TDG?\r\n          </Typography>\r\n\r\n          <Typography\r\n            variant=\"body1\"\r\n            fontFamily={\"Montserrat\"}\r\n            fontSize={isSmallScreen ? \"0.875rem\" : \"1rem\"} // Adjust body text\r\n            sx={{ marginTop: { xs: \"-1rem\", md: \"-2.5rem\" } }}\r\n          >\r\n            TheDesignGrit is here to spotlight Egyptian design. We’re giving\r\n            local brands the platform they deserve, connecting their\r\n            craftsmanship with those who value it most. It’s about honoring\r\n            tradition, celebrating innovation, and helping you discover pieces\r\n            that make you feel most at home.\r\n          </Typography>\r\n\r\n          <CardActions>\r\n            <button\r\n              className=\"btn\"\r\n              style={{\r\n                color: \"black\",\r\n                background: \"#eae3e4\",\r\n                fontWeight: \"bold\",\r\n                fontSize: isSmallScreen ? \"0.875rem\" : \"1rem\", // Button text responsive\r\n                padding: isSmallScreen ? \"0.5rem 1rem\" : \"0.75rem 1.5rem\",\r\n              }}\r\n              onClick={() => navigate(\"/about\")}\r\n            >\r\n              Find Out More\r\n            </button>\r\n          </CardActions>\r\n        </CardContent>\r\n\r\n        <CardMedia sx={{ flex: 1 }}>\r\n          <img\r\n            src=\"Assets/susSection-768.webp\"\r\n            srcSet=\"\r\n    Assets/susSection-480.webp 480w,\r\n    Assets/susSection-768.webp 768w,\r\n    Assets/susSection-1200.webp 1200w\r\n  \"\r\n            sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n            alt=\"Green Bowl Beach\"\r\n            loading=\"lazy\"\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"100%\",\r\n              objectFit: \"cover\",\r\n            }}\r\n          />\r\n        </CardMedia>\r\n      </Card>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default SustainabilitySection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAC9B,MAAMM,KAAK,GAAGR,QAAQ,CAAC,CAAC;EACxB,MAAMS,aAAa,GAAGR,aAAa,CAACO,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEnE,oBACEP,OAAA,CAACV,GAAG;IAACkB,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eACpCT,OAAA,CAACR,IAAI;MACHkB,EAAE,EAAE;QACFC,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE;UAAEC,EAAE,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAM,CAAC;QAAE;QAC5CC,cAAc,EAAE,QAAQ;QACxBC,YAAY,EAAE,SAAS;QACvBC,SAAS,EAAE,iCAAiC;QAC5CC,MAAM,EAAE;UAAEL,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAQ,CAAC,CAAE;MACvC,CAAE;MACFP,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAExBT,OAAA,CAACP,WAAW;QACViB,EAAE,EAAE;UACFU,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE;YAAEP,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO,CAAC;UAAE;UACrCH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBG,cAAc,EAAE,eAAe;UAC/BM,IAAI,EAAE,CAAC;UACPC,UAAU,EAAE;QACd,CAAE;QAAAd,QAAA,gBAEFT,OAAA,CAACT,UAAU;UACTiC,OAAO,EAAC,IAAI;UACZC,QAAQ,EAAEpB,aAAa,GAAG,MAAM,GAAG,QAAS,CAAC;UAAA;UAC7CqB,UAAU,EAAC,KAAK;UAChBC,UAAU,EAAE,SAAU;UACtBjB,EAAE,EAAE;YACFkB,UAAU,EAAE;cAAEd,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAC;YACtCc,aAAa,EAAE;cAAEf,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAI;UACvC,CAAE;UAAAN,QAAA,EACH;QAED;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACT,UAAU;UACTiC,OAAO,EAAC,OAAO;UACfG,UAAU,EAAE,YAAa;UACzBF,QAAQ,EAAEpB,aAAa,GAAG,UAAU,GAAG,MAAO,CAAC;UAAA;UAC/CK,EAAE,EAAE;YAAEwB,SAAS,EAAE;cAAEpB,EAAE,EAAE,OAAO;cAAEC,EAAE,EAAE;YAAU;UAAE,CAAE;UAAAN,QAAA,EACnD;QAMD;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACN,WAAW;UAAAe,QAAA,eACVT,OAAA;YACEQ,SAAS,EAAC,KAAK;YACf2B,KAAK,EAAE;cACLf,KAAK,EAAE,OAAO;cACdT,UAAU,EAAE,SAAS;cACrBe,UAAU,EAAE,MAAM;cAClBD,QAAQ,EAAEpB,aAAa,GAAG,UAAU,GAAG,MAAM;cAAE;cAC/CgB,OAAO,EAAEhB,aAAa,GAAG,aAAa,GAAG;YAC3C,CAAE;YACF+B,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,QAAQ,CAAE;YAAAM,QAAA,EACnC;UAED;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEdjC,OAAA,CAACL,SAAS;QAACe,EAAE,EAAE;UAAEY,IAAI,EAAE;QAAE,CAAE;QAAAb,QAAA,eACzBT,OAAA;UACEqC,GAAG,EAAC,4BAA4B;UAChCC,MAAM,EAAC,+GAIhB;UACSC,KAAK,EAAC,gCAAgC;UACtCC,GAAG,EAAC,kBAAkB;UACtBC,OAAO,EAAC,MAAM;UACdN,KAAK,EAAE;YACLO,KAAK,EAAE,MAAM;YACbvB,MAAM,EAAE,MAAM;YACdwB,SAAS,EAAE;UACb;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA9FID,qBAAqB;EAAA,QACRH,WAAW,EACdF,QAAQ,EACAC,aAAa;AAAA;AAAA+C,EAAA,GAH/B3C,qBAAqB;AAgG3B,eAAeA,qBAAqB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}