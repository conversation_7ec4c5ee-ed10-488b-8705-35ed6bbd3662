{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\dashboardAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, Suspense } from \"react\";\nimport { SlCalender } from \"react-icons/sl\";\nimport { Box } from \"@mui/material\";\nimport { FaBox, FaTruck, FaCheckCircle, FaRedo, FaChartLine } from \"react-icons/fa\"; // React Icons\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\nimport AdminOrderDetails from \"./orderDetailsAdmin\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardAdmin = () => {\n  _s();\n  const [orders, setOrders] = useState([]);\n  const [bestSellers, setBestSellers] = useState([]);\n  const [selectedOrder, setSelectedOrder] = useState(null); // State for selected order\n  const [activeOrdersStats, setActiveOrdersStats] = useState({\n    count: 0,\n    totalSum: 0\n  });\n  const [completedOrdersStats, setCompletedOrdersStats] = useState({\n    count: 0,\n    totalSum: 0\n  });\n\n  // Fetch order data from JSON\n  const fetchOrders = async () => {\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/orders/admin-orders\");\n      const data = await response.json();\n      // Sort orders by date (latest to earliest)\n      data.sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate));\n      setOrders(data);\n    } catch (error) {\n      console.error(\"Error fetching orders:\", error);\n    }\n  };\n  const fetchBestSellers = async () => {\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/orders/bestsellers\");\n      const data = await response.json();\n      setBestSellers(data);\n    } catch (error) {\n      console.error(\"Error fetching best sellers:\", error);\n    }\n  };\n\n  // Fetch active and completed orders stats\n  const fetchActiveOrdersStats = async () => {\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/orders/admin/get-active-orders\");\n      const data = await response.json();\n      setActiveOrdersStats(data);\n    } catch (error) {\n      console.error(\"Error fetching active orders stats:\", error);\n    }\n  };\n  const fetchCompletedOrdersStats = async () => {\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/orders/admin/get-completed-orders\");\n      const data = await response.json();\n      setCompletedOrdersStats(data);\n    } catch (error) {\n      console.error(\"Error fetching completed orders stats:\", error);\n    }\n  };\n  useEffect(() => {\n    fetchOrders();\n    fetchBestSellers();\n    fetchActiveOrdersStats();\n    fetchCompletedOrdersStats();\n  }, []);\n  if (selectedOrder) {\n    return /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(AdminOrderDetails, {\n        order: selectedOrder,\n        onBack: () => setSelectedOrder(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-vendor\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Home > Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-date-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(SlCalender, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"June 12, 2024 - Oct 19, 2024\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-overview-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaBox, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Total Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"E\\xA3 126,500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u25B2 34.7% Compared to Oct 2023\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaTruck, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Active Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"E\\xA3 \", activeOrdersStats.totalSum.toLocaleString(), \" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 64\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: \"0.9em\"\n              },\n              children: [\"(\", activeOrdersStats.count, \" orders)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaCheckCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Completed Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"E\\xA3 \", completedOrdersStats.totalSum.toLocaleString(), \" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 67\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: \"0.9em\"\n              },\n              children: [\"(\", completedOrdersStats.count, \" orders)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overview-card-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-icon-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaRedo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-content-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Return Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"E\\xA3 126,500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u25B2 34.7% Compared to Oct 2023\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-chart-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-header-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header-title-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Sale Graph\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-tabs-vendor\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"chart-tab-vendor active\",\n              children: \"Weekly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"chart-tab-vendor\",\n              children: \"Monthly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"chart-tab-vendor\",\n              children: \"Yearly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-content-vendor\",\n          children: /*#__PURE__*/_jsxDEV(FaChartLine, {\n            className: \"chart-placeholder-icon-vendor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"best-sellers-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            flexDirection: \"row\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Best Sellers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowY: \"scroll\",\n            height: \"300px\",\n            margin: \"8px 0px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: bestSellers.length > 0 ? bestSellers.map((product, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"best-seller-item-vendor\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n                  alt: product.name,\n                  className: \"best-seller-img-vendor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"best-seller-info-vendor\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [product.brandName, \" - E\\xA3 \", product.price, \" (\", product.totalSold, \" sales )\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"No best sellers available.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-lists-vendor\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recent-orders-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            flexDirection: \"row\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Recent Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(BsThreeDotsVertical, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), orders.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Order ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Customer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: orders.slice(0, 5).map(order => {\n              var _order$cartItems$, _order$customerId, _order$customerId2;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                onClick: () => setSelectedOrder(order),\n                style: {\n                  cursor: \"pointer\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [\" \", ((_order$cartItems$ = order.cartItems[0]) === null || _order$cartItems$ === void 0 ? void 0 : _order$cartItems$.name) || \"N/A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: order._id || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(order.orderDate).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_order$customerId = order.customerId) === null || _order$customerId === void 0 ? void 0 : _order$customerId.firstName) || \"N/A\", \" \", \"\", ((_order$customerId2 = order.customerId) === null || _order$customerId2 === void 0 ? void 0 : _order$customerId2.lastName) || \"N/A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      display: \"inline-block\",\n                      marginTop: \"4px\",\n                      padding: \"4px 12px\",\n                      borderRadius: \"5px\",\n                      backgroundColor: order.orderStatus === \"Pending\" ? \"#f8d7da\" : order.orderStatus === \"Delivered\" ? \"#d4edda\" : \"#FFE5B4\",\n                      color: order.orderStatus === \"Pending\" ? \"#721c24\" : order.orderStatus === \"Delivered\" ? \"#155724\" : \"#FF7518\",\n                      fontWeight: \"500\",\n                      textAlign: \"center\",\n                      minWidth: \"80px\"\n                    },\n                    children: (order === null || order === void 0 ? void 0 : order.orderStatus) || \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [\"E\\xA3 \", (order === null || order === void 0 ? void 0 : order.total) || \"N/A\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, order._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            padding: \"2rem\",\n            backgroundColor: \"#f5f5f5\",\n            borderRadius: \"8px\",\n            margin: \"1rem 0\"\n          },\n          children: \"No orders available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardAdmin, \"+8XVhyIv1Jigp2s8OVvZW3I2ZwI=\");\n_c = DashboardAdmin;\nexport default DashboardAdmin;\nvar _c;\n$RefreshReg$(_c, \"DashboardAdmin\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Suspense", "SlCalender", "Box", "FaBox", "FaTruck", "FaCheckCircle", "FaRedo", "FaChartLine", "BsThreeDotsVertical", "AdminOrderDetails", "jsxDEV", "_jsxDEV", "DashboardAdmin", "_s", "orders", "setOrders", "bestSellers", "setBestSellers", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "activeOrdersStats", "setActiveOrdersStats", "count", "totalSum", "completedOrdersStats", "setCompletedOrdersStats", "fetchOrders", "response", "fetch", "data", "json", "sort", "a", "b", "Date", "orderDate", "error", "console", "fetchBestSellers", "fetchActiveOrdersStats", "fetchCompletedOrdersStats", "fallback", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "order", "onBack", "className", "toLocaleString", "style", "fontSize", "sx", "display", "flexDirection", "justifyContent", "alignItems", "overflowY", "height", "margin", "length", "map", "product", "index", "src", "mainImage", "alt", "name", "brandName", "price", "totalSold", "slice", "_order$cartItems$", "_order$customerId", "_order$customerId2", "onClick", "cursor", "cartItems", "_id", "toLocaleDateString", "customerId", "firstName", "lastName", "marginTop", "padding", "borderRadius", "backgroundColor", "orderStatus", "color", "fontWeight", "textAlign", "min<PERSON><PERSON><PERSON>", "total", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/dashboardAdmin.jsx"], "sourcesContent": ["import React, { useEffect, useState, Suspense } from \"react\";\r\nimport { SlCalender } from \"react-icons/sl\";\r\nimport { Box } from \"@mui/material\";\r\nimport {\r\n  FaBox,\r\n  FaTruck,\r\n  FaCheckCircle,\r\n  FaRedo,\r\n  FaChartLine,\r\n} from \"react-icons/fa\"; // React Icons\r\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\r\nimport AdminOrderDetails from \"./orderDetailsAdmin\";\r\n\r\nconst DashboardAdmin = () => {\r\n  const [orders, setOrders] = useState([]);\r\n  const [bestSellers, setBestSellers] = useState([]);\r\n  const [selectedOrder, setSelectedOrder] = useState(null); // State for selected order\r\n  const [activeOrdersStats, setActiveOrdersStats] = useState({\r\n    count: 0,\r\n    totalSum: 0,\r\n  });\r\n  const [completedOrdersStats, setCompletedOrdersStats] = useState({\r\n    count: 0,\r\n    totalSum: 0,\r\n  });\r\n\r\n  // Fetch order data from JSON\r\n  const fetchOrders = async () => {\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/orders/admin-orders\"\r\n      );\r\n      const data = await response.json();\r\n      // Sort orders by date (latest to earliest)\r\n      data.sort((a, b) => new Date(b.orderDate) - new Date(a.orderDate));\r\n      setOrders(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching orders:\", error);\r\n    }\r\n  };\r\n  const fetchBestSellers = async () => {\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/orders/bestsellers\"\r\n      );\r\n      const data = await response.json();\r\n      setBestSellers(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching best sellers:\", error);\r\n    }\r\n  };\r\n\r\n  // Fetch active and completed orders stats\r\n  const fetchActiveOrdersStats = async () => {\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/orders/admin/get-active-orders\"\r\n      );\r\n      const data = await response.json();\r\n      setActiveOrdersStats(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching active orders stats:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchCompletedOrdersStats = async () => {\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/orders/admin/get-completed-orders\"\r\n      );\r\n      const data = await response.json();\r\n      setCompletedOrdersStats(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching completed orders stats:\", error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchOrders();\r\n    fetchBestSellers();\r\n    fetchActiveOrdersStats();\r\n    fetchCompletedOrdersStats();\r\n  }, []);\r\n\r\n  if (selectedOrder) {\r\n    return (\r\n      <Suspense fallback={<div>Loading...</div>}>\r\n        <AdminOrderDetails\r\n          order={selectedOrder}\r\n          onBack={() => setSelectedOrder(null)}\r\n        />\r\n      </Suspense>\r\n    );\r\n  }\r\n  return (\r\n    <div className=\"dashboard-vendor\">\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <h2>Dashboard</h2>\r\n          <p>Home &gt; Dashboard</p>\r\n        </div>\r\n        <div className=\"dashboard-date-vendor\">\r\n          <SlCalender />\r\n          <span>June 12, 2024 - Oct 19, 2024</span>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Overview Section */}\r\n      <section className=\"dashboard-overview-vendor\">\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaBox />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Total Orders</h3>\r\n            <p>E£ 126,500</p>\r\n            <span>▲ 34.7% Compared to Oct 2023</span>\r\n          </div>\r\n        </div>\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaTruck />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Active Orders</h3>\r\n            <p>\r\n              E£ {activeOrdersStats.totalSum.toLocaleString()} <br />\r\n              <span style={{ fontSize: \"0.9em\" }}>\r\n                ({activeOrdersStats.count} orders)\r\n              </span>\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaCheckCircle />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Completed Orders</h3>\r\n            <p>\r\n              E£ {completedOrdersStats.totalSum.toLocaleString()} <br />\r\n              <span style={{ fontSize: \"0.9em\" }}>\r\n                ({completedOrdersStats.count} orders)\r\n              </span>\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"overview-card-vendor\">\r\n          <div className=\"card-icon-vendor\">\r\n            <FaRedo />\r\n          </div>\r\n          <div className=\"card-content-vendor\">\r\n            <h3>Return Orders</h3>\r\n            <p>E£ 126,500</p>\r\n            <span>▲ 34.7% Compared to Oct 2023</span>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Sales Chart Section */}\r\n      <section className=\"dashboard-chart-vendor\">\r\n        <div className=\"chart-header-vendor\">\r\n          <div className=\"chart-header-title-vendor\">\r\n            <h3>Sale Graph</h3>\r\n            <div className=\"chart-tabs-vendor\">\r\n              <button className=\"chart-tab-vendor active\">Weekly</button>\r\n              <button className=\"chart-tab-vendor\">Monthly</button>\r\n              <button className=\"chart-tab-vendor\">Yearly</button>\r\n            </div>\r\n          </div>\r\n          <div className=\"chart-content-vendor\">\r\n            {/* Placeholder for Sale Graph */}\r\n            <FaChartLine className=\"chart-placeholder-icon-vendor\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"best-sellers-vendor\">\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              flexDirection: \"row\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <h3>Best Sellers</h3>\r\n          </Box>\r\n          <hr></hr>\r\n          <div\r\n            style={{\r\n              overflowY: \"scroll\",\r\n              height: \"300px\",\r\n              margin: \"8px 0px\",\r\n            }}\r\n          >\r\n            <ul>\r\n              {bestSellers.length > 0 ? (\r\n                bestSellers.map((product, index) => (\r\n                  <li key={index}>\r\n                    <div className=\"best-seller-item-vendor\">\r\n                      <img\r\n                        src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                        alt={product.name}\r\n                        className=\"best-seller-img-vendor\"\r\n                      />\r\n                      <div className=\"best-seller-info-vendor\">\r\n                        <h4>{product.name}</h4>\r\n                        <p>\r\n                          {product.brandName} - E£ {product.price} (\r\n                          {product.totalSold} sales )\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </li>\r\n                ))\r\n              ) : (\r\n                <li>No best sellers available.</li>\r\n              )}\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Recent Orders & Best Sellers */}\r\n      <section className=\"dashboard-lists-vendor\">\r\n        <div className=\"recent-orders-vendor\">\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              flexDirection: \"row\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <h3>Recent Orders</h3>\r\n            <BsThreeDotsVertical />\r\n          </Box>\r\n          {orders.length > 0 ? (\r\n            <table>\r\n              <thead>\r\n                <tr>\r\n                  <th>Product</th>\r\n                  <th>Order ID</th>\r\n                  <th>Date</th>\r\n                  <th>Customer Name</th>\r\n                  <th>Status</th>\r\n                  <th>Amount</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {orders.slice(0, 5).map((order) => (\r\n                  <tr\r\n                    key={order._id}\r\n                    onClick={() => setSelectedOrder(order)}\r\n                    style={{ cursor: \"pointer\" }}\r\n                  >\r\n                    <td> {order.cartItems[0]?.name || \"N/A\"}</td>\r\n                    <td>{order._id || \"N/A\"}</td>\r\n                    <td>{new Date(order.orderDate).toLocaleDateString()}</td>\r\n                    <td>\r\n                      {order.customerId?.firstName || \"N/A\"} {\"\"}\r\n                      {order.customerId?.lastName || \"N/A\"}\r\n                    </td>\r\n                    <td>\r\n                      <span\r\n                        style={{\r\n                          display: \"inline-block\",\r\n                          marginTop: \"4px\",\r\n                          padding: \"4px 12px\",\r\n                          borderRadius: \"5px\",\r\n                          backgroundColor:\r\n                            order.orderStatus === \"Pending\"\r\n                              ? \"#f8d7da\"\r\n                              : order.orderStatus === \"Delivered\"\r\n                              ? \"#d4edda\"\r\n                              : \"#FFE5B4\",\r\n                          color:\r\n                            order.orderStatus === \"Pending\"\r\n                              ? \"#721c24\"\r\n                              : order.orderStatus === \"Delivered\"\r\n                              ? \"#155724\"\r\n                              : \"#FF7518\",\r\n                          fontWeight: \"500\",\r\n                          textAlign: \"center\",\r\n                          minWidth: \"80px\",\r\n                        }}\r\n                      >\r\n                        {order?.orderStatus || \"N/A\"}\r\n                      </span>\r\n                    </td>\r\n                    <td>E£ {order?.total || \"N/A\"}</td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          ) : (\r\n            <div\r\n              style={{\r\n                textAlign: \"center\",\r\n                padding: \"2rem\",\r\n                backgroundColor: \"#f5f5f5\",\r\n                borderRadius: \"8px\",\r\n                margin: \"1rem 0\",\r\n              }}\r\n            >\r\n              No orders available\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,OAAO;AAC5D,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,GAAG,QAAQ,eAAe;AACnC,SACEC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,MAAM,EACNC,WAAW,QACN,gBAAgB,CAAC,CAAC;AACzB,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC;IACzDuB,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1B,QAAQ,CAAC;IAC/DuB,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,uDACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC;MACAD,IAAI,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAAC;MAClEpB,SAAS,CAACc,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EACD,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAC1B,sDACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCb,cAAc,CAACY,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMG,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAC1B,kEACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCT,oBAAoB,CAACQ,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMI,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAC1B,qEACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCL,uBAAuB,CAACI,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACd4B,WAAW,CAAC,CAAC;IACbY,gBAAgB,CAAC,CAAC;IAClBC,sBAAsB,CAAC,CAAC;IACxBC,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,IAAItB,aAAa,EAAE;IACjB,oBACEP,OAAA,CAACX,QAAQ;MAACyC,QAAQ,eAAE9B,OAAA;QAAA+B,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAE;MAAAJ,QAAA,eACxC/B,OAAA,CAACF,iBAAiB;QAChBsC,KAAK,EAAE7B,aAAc;QACrB8B,MAAM,EAAEA,CAAA,KAAM7B,gBAAgB,CAAC,IAAI;MAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEf;EACA,oBACEnC,OAAA;IAAKsC,SAAS,EAAC,kBAAkB;IAAAP,QAAA,gBAC/B/B,OAAA;MAAQsC,SAAS,EAAC,yBAAyB;MAAAP,QAAA,gBACzC/B,OAAA;QAAKsC,SAAS,EAAC,wBAAwB;QAAAP,QAAA,gBACrC/B,OAAA;UAAA+B,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClBnC,OAAA;UAAA+B,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eACNnC,OAAA;QAAKsC,SAAS,EAAC,uBAAuB;QAAAP,QAAA,gBACpC/B,OAAA,CAACV,UAAU;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdnC,OAAA;UAAA+B,QAAA,EAAM;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTnC,OAAA;MAASsC,SAAS,EAAC,2BAA2B;MAAAP,QAAA,gBAC5C/B,OAAA;QAAKsC,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnC/B,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/B/B,OAAA,CAACR,KAAK;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnC,OAAA;UAAKsC,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClC/B,OAAA;YAAA+B,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBnC,OAAA;YAAA+B,QAAA,EAAG;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjBnC,OAAA;YAAA+B,QAAA,EAAM;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAKsC,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnC/B,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/B/B,OAAA,CAACP,OAAO;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACNnC,OAAA;UAAKsC,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClC/B,OAAA;YAAA+B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBnC,OAAA;YAAA+B,QAAA,GAAG,QACE,EAACtB,iBAAiB,CAACG,QAAQ,CAAC2B,cAAc,CAAC,CAAC,EAAC,GAAC,eAAAvC,OAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDnC,OAAA;cAAMwC,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAV,QAAA,GAAC,GACjC,EAACtB,iBAAiB,CAACE,KAAK,EAAC,UAC5B;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAKsC,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnC/B,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/B/B,OAAA,CAACN,aAAa;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNnC,OAAA;UAAKsC,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClC/B,OAAA;YAAA+B,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnC,OAAA;YAAA+B,QAAA,GAAG,QACE,EAAClB,oBAAoB,CAACD,QAAQ,CAAC2B,cAAc,CAAC,CAAC,EAAC,GAAC,eAAAvC,OAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1DnC,OAAA;cAAMwC,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAQ,CAAE;cAAAV,QAAA,GAAC,GACjC,EAAClB,oBAAoB,CAACF,KAAK,EAAC,UAC/B;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnC,OAAA;QAAKsC,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnC/B,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAP,QAAA,eAC/B/B,OAAA,CAACL,MAAM;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNnC,OAAA;UAAKsC,SAAS,EAAC,qBAAqB;UAAAP,QAAA,gBAClC/B,OAAA;YAAA+B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBnC,OAAA;YAAA+B,QAAA,EAAG;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjBnC,OAAA;YAAA+B,QAAA,EAAM;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnC,OAAA;MAASsC,SAAS,EAAC,wBAAwB;MAAAP,QAAA,gBACzC/B,OAAA;QAAKsC,SAAS,EAAC,qBAAqB;QAAAP,QAAA,gBAClC/B,OAAA;UAAKsC,SAAS,EAAC,2BAA2B;UAAAP,QAAA,gBACxC/B,OAAA;YAAA+B,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBnC,OAAA;YAAKsC,SAAS,EAAC,mBAAmB;YAAAP,QAAA,gBAChC/B,OAAA;cAAQsC,SAAS,EAAC,yBAAyB;cAAAP,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3DnC,OAAA;cAAQsC,SAAS,EAAC,kBAAkB;cAAAP,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrDnC,OAAA;cAAQsC,SAAS,EAAC,kBAAkB;cAAAP,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnC,OAAA;UAAKsC,SAAS,EAAC,sBAAsB;UAAAP,QAAA,eAEnC/B,OAAA,CAACJ,WAAW;YAAC0C,SAAS,EAAC;UAA+B;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnC,OAAA;QAAKsC,SAAS,EAAC,qBAAqB;QAAAP,QAAA,gBAClC/B,OAAA,CAACT,GAAG;UACFmD,EAAE,EAAE;YACFC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,KAAK;YACpBC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,eAEF/B,OAAA;YAAA+B,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNnC,OAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnC,OAAA;UACEwC,KAAK,EAAE;YACLO,SAAS,EAAE,QAAQ;YACnBC,MAAM,EAAE,OAAO;YACfC,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,eAEF/B,OAAA;YAAA+B,QAAA,EACG1B,WAAW,CAAC6C,MAAM,GAAG,CAAC,GACrB7C,WAAW,CAAC8C,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC7BrD,OAAA;cAAA+B,QAAA,eACE/B,OAAA;gBAAKsC,SAAS,EAAC,yBAAyB;gBAAAP,QAAA,gBACtC/B,OAAA;kBACEsD,GAAG,EAAE,uDAAuDF,OAAO,CAACG,SAAS,EAAG;kBAChFC,GAAG,EAAEJ,OAAO,CAACK,IAAK;kBAClBnB,SAAS,EAAC;gBAAwB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACFnC,OAAA;kBAAKsC,SAAS,EAAC,yBAAyB;kBAAAP,QAAA,gBACtC/B,OAAA;oBAAA+B,QAAA,EAAKqB,OAAO,CAACK;kBAAI;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvBnC,OAAA;oBAAA+B,QAAA,GACGqB,OAAO,CAACM,SAAS,EAAC,WAAM,EAACN,OAAO,CAACO,KAAK,EAAC,IACxC,EAACP,OAAO,CAACQ,SAAS,EAAC,UACrB;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAdCkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeV,CACL,CAAC,gBAEFnC,OAAA;cAAA+B,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UACnC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVnC,OAAA;MAASsC,SAAS,EAAC,wBAAwB;MAAAP,QAAA,eACzC/B,OAAA;QAAKsC,SAAS,EAAC,sBAAsB;QAAAP,QAAA,gBACnC/B,OAAA,CAACT,GAAG;UACFmD,EAAE,EAAE;YACFC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,KAAK;YACpBC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,gBAEF/B,OAAA;YAAA+B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBnC,OAAA,CAACH,mBAAmB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EACLhC,MAAM,CAAC+C,MAAM,GAAG,CAAC,gBAChBlD,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAA+B,QAAA,eACE/B,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAA+B,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBnC,OAAA;gBAAA+B,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBnC,OAAA;gBAAA+B,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbnC,OAAA;gBAAA+B,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBnC,OAAA;gBAAA+B,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfnC,OAAA;gBAAA+B,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnC,OAAA;YAAA+B,QAAA,EACG5B,MAAM,CAAC0D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAEf,KAAK;cAAA,IAAA0B,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA;cAAA,oBAC5BhE,OAAA;gBAEEiE,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAAC4B,KAAK,CAAE;gBACvCI,KAAK,EAAE;kBAAE0B,MAAM,EAAE;gBAAU,CAAE;gBAAAnC,QAAA,gBAE7B/B,OAAA;kBAAA+B,QAAA,GAAI,GAAC,EAAC,EAAA+B,iBAAA,GAAA1B,KAAK,CAAC+B,SAAS,CAAC,CAAC,CAAC,cAAAL,iBAAA,uBAAlBA,iBAAA,CAAoBL,IAAI,KAAI,KAAK;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CnC,OAAA;kBAAA+B,QAAA,EAAKK,KAAK,CAACgC,GAAG,IAAI;gBAAK;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7BnC,OAAA;kBAAA+B,QAAA,EAAK,IAAIR,IAAI,CAACa,KAAK,CAACZ,SAAS,CAAC,CAAC6C,kBAAkB,CAAC;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzDnC,OAAA;kBAAA+B,QAAA,GACG,EAAAgC,iBAAA,GAAA3B,KAAK,CAACkC,UAAU,cAAAP,iBAAA,uBAAhBA,iBAAA,CAAkBQ,SAAS,KAAI,KAAK,EAAC,GAAC,EAAC,EAAE,EACzC,EAAAP,kBAAA,GAAA5B,KAAK,CAACkC,UAAU,cAAAN,kBAAA,uBAAhBA,kBAAA,CAAkBQ,QAAQ,KAAI,KAAK;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACLnC,OAAA;kBAAA+B,QAAA,eACE/B,OAAA;oBACEwC,KAAK,EAAE;sBACLG,OAAO,EAAE,cAAc;sBACvB8B,SAAS,EAAE,KAAK;sBAChBC,OAAO,EAAE,UAAU;sBACnBC,YAAY,EAAE,KAAK;sBACnBC,eAAe,EACbxC,KAAK,CAACyC,WAAW,KAAK,SAAS,GAC3B,SAAS,GACTzC,KAAK,CAACyC,WAAW,KAAK,WAAW,GACjC,SAAS,GACT,SAAS;sBACfC,KAAK,EACH1C,KAAK,CAACyC,WAAW,KAAK,SAAS,GAC3B,SAAS,GACTzC,KAAK,CAACyC,WAAW,KAAK,WAAW,GACjC,SAAS,GACT,SAAS;sBACfE,UAAU,EAAE,KAAK;sBACjBC,SAAS,EAAE,QAAQ;sBACnBC,QAAQ,EAAE;oBACZ,CAAE;oBAAAlD,QAAA,EAED,CAAAK,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyC,WAAW,KAAI;kBAAK;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLnC,OAAA;kBAAA+B,QAAA,GAAI,QAAG,EAAC,CAAAK,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8C,KAAK,KAAI,KAAK;gBAAA;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAtC9BC,KAAK,CAACgC,GAAG;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCZ,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAERnC,OAAA;UACEwC,KAAK,EAAE;YACLwC,SAAS,EAAE,QAAQ;YACnBN,OAAO,EAAE,MAAM;YACfE,eAAe,EAAE,SAAS;YAC1BD,YAAY,EAAE,KAAK;YACnB1B,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjC,EAAA,CA3SID,cAAc;AAAAkF,EAAA,GAAdlF,cAAc;AA6SpB,eAAeA,cAAc;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}