{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\orderListAdmin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\nimport { SlCalender } from \"react-icons/sl\";\nimport { subDays, isWithinInterval, parseISO } from \"date-fns\";\nimport AdminOrderDetails from \"./orderDetailsAdmin\";\nimport { Box, Select, MenuItem, CircularProgress } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecentPurchasesAdmin = () => {\n  _s();\n  const [orders, setOrders] = useState([]);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [filterStatus, setFilterStatus] = useState(\"All\");\n  const [sortOption, setSortOption] = useState(\"Date\");\n  const [sortDirection] = useState(\"desc\");\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [dateFilter, setDateFilter] = useState(\"All\");\n  const [brands, setBrands] = useState([]);\n  const [selectedBrand, setSelectedBrand] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(true);\n  const ordersPerPage = 8;\n\n  // Fetch order data from JSON\n  const fetchOrders = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/orders/admin-orders\");\n      const data = await response.json();\n      setOrders(data);\n    } catch (error) {\n      console.error(\"Error fetching orders:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const fetchBrands = async () => {\n    try {\n      const response = await fetch(\"https://api.thedesigngrit.com/api/brand\");\n      const data = await response.json();\n      setBrands(data); // Assuming brands are returned as an array\n    } catch (error) {\n      console.error(\"Error fetching brands:\", error);\n    }\n  };\n  useEffect(() => {\n    fetchOrders();\n    fetchBrands();\n  }, []);\n  const getFilteredByStatus = () => {\n    return filterStatus === \"All\" ? orders : orders.filter(order => order.orderStatus === filterStatus);\n  };\n  const getFilteredByBrand = filtered => {\n    return selectedBrand ? filtered.filter(order => {\n      var _order$cartItems$;\n      return ((_order$cartItems$ = order.cartItems[0]) === null || _order$cartItems$ === void 0 ? void 0 : _order$cartItems$.brandId._id) === selectedBrand;\n    }) : filtered;\n  };\n  const getFilteredByDate = filtered => {\n    const today = new Date();\n    if (dateFilter === \"Today\") {\n      return filtered.filter(order => isWithinInterval(parseISO(order.orderDate), {\n        start: new Date(today.setHours(0, 0, 0, 0)),\n        end: new Date(today.setHours(23, 59, 59, 999))\n      }));\n    }\n    if (dateFilter === \"Last7Days\") {\n      return filtered.filter(order => isWithinInterval(parseISO(order.orderDate), {\n        start: subDays(today, 7),\n        end: today\n      }));\n    }\n    if (dateFilter === \"Last30Days\") {\n      return filtered.filter(order => isWithinInterval(parseISO(order.orderDate), {\n        start: subDays(today, 30),\n        end: today\n      }));\n    }\n    return filtered; // All\n  };\n  const sortedOrders = [...getFilteredByDate(getFilteredByStatus())].filter(order => getFilteredByBrand([order]).length > 0).sort((a, b) => {\n    var _a$cartItems$, _b$cartItems$, _b$cartItems$2, _a$cartItems$2;\n    switch (sortOption) {\n      case \"Date\":\n        return new Date(b.orderDate) - new Date(a.orderDate);\n      case \"Alphabetical\":\n        return sortDirection === \"asc\" ? (_a$cartItems$ = a.cartItems[0]) === null || _a$cartItems$ === void 0 ? void 0 : _a$cartItems$.name.localeCompare((_b$cartItems$ = b.cartItems[0]) === null || _b$cartItems$ === void 0 ? void 0 : _b$cartItems$.name) : (_b$cartItems$2 = b.cartItems[0]) === null || _b$cartItems$2 === void 0 ? void 0 : _b$cartItems$2.name.localeCompare((_a$cartItems$2 = a.cartItems[0]) === null || _a$cartItems$2 === void 0 ? void 0 : _a$cartItems$2.name);\n      case \"Price Ascending\":\n        return b.total - a.total;\n      case \"Price Descending\":\n        return a.total - b.total;\n      default:\n        return 0;\n    }\n  });\n  const indexOfLastOrder = currentPage * ordersPerPage;\n  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;\n  const currentOrders = sortedOrders.slice(indexOfFirstOrder, indexOfLastOrder);\n  const totalPages = Math.ceil(sortedOrders.length / ordersPerPage);\n  if (selectedOrder) {\n    return /*#__PURE__*/_jsxDEV(AdminOrderDetails, {\n      order: selectedOrder,\n      onBack: () => setSelectedOrder(null)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-vendor\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Order List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Home > Order List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-date-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(SlCalender, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: dateFilter === \"All\" ? \"All Time\" : dateFilter.replace(\"Last\", \"Last \").replace(\"Days\", \" Days\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: \"100%\",\n        display: \"flex\",\n        justifyContent: \"flex-end\",\n        gap: \"20px\",\n        marginBottom: \"1rem\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Select, {\n        sx: {\n          width: 200,\n          borderRadius: \"5px\",\n          color: \"#2d2d2d\"\n        },\n        value: filterStatus,\n        onChange: e => setFilterStatus(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"All\",\n          children: \"Sort By Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Pending\",\n          children: \"Pending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Confirmed\",\n          children: \"Confirmed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Shipped\",\n          children: \"Shipped\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Delivered\",\n          children: \"Delivered\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Cancelled\",\n          children: \"Cancelled\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        sx: {\n          width: 200,\n          borderRadius: \"5px\",\n          color: \"#2d2d2d\"\n        },\n        value: sortOption,\n        onChange: e => setSortOption(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Date\",\n          children: \"Sort By Date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Alphabetical\",\n          children: \"Alphabetical\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Price Ascending\",\n          children: \"Price: Ascending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Price Descending\",\n          children: \"Price: Descending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        sx: {\n          width: 200,\n          borderRadius: \"5px\",\n          color: \"#2d2d2d\"\n        },\n        value: dateFilter,\n        onChange: e => setDateFilter(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"All\",\n          children: \"All Time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Today\",\n          children: \"Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Last7Days\",\n          children: \"Last 7 Days\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"Last30Days\",\n          children: \"Last 30 Days\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        sx: {\n          width: 200,\n          borderRadius: \"5px\",\n          color: \"#2d2d2d\"\n        },\n        value: selectedBrand,\n        onChange: e => setSelectedBrand(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"\",\n          children: \"All Brands\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), brands.map(brand => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: brand._id,\n          children: brand.brandName\n        }, brand._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        minHeight: \"400px\",\n        width: \"100%\"\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        thickness: 4,\n        sx: {\n          color: \"#6b7b58\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recent-purchases\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          flexDirection: \"row\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Recent Purchases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(BsThreeDotsVertical, {\n          style: {\n            fontSize: \"1rem\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Order ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Customer Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Brand\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: currentOrders.map(order => {\n            var _order$cartItems$2, _order$customerId, _order$customerId2, _order$cartItems$3;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              onClick: () => setSelectedOrder(order),\n              style: {\n                cursor: \"pointer\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: (order === null || order === void 0 ? void 0 : (_order$cartItems$2 = order.cartItems[0]) === null || _order$cartItems$2 === void 0 ? void 0 : _order$cartItems$2.name) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: (order === null || order === void 0 ? void 0 : order._id) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(order === null || order === void 0 ? void 0 : order.orderDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [((_order$customerId = order.customerId) === null || _order$customerId === void 0 ? void 0 : _order$customerId.firstName) || \"N/A\", ((_order$customerId2 = order.customerId) === null || _order$customerId2 === void 0 ? void 0 : _order$customerId2.lastName) || \"N/A\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: ((_order$cartItems$3 = order.cartItems[0]) === null || _order$cartItems$3 === void 0 ? void 0 : _order$cartItems$3.brandId.brandName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: \"inline-block\",\n                    padding: \"4px 12px\",\n                    borderRadius: \"5px\",\n                    backgroundColor: order.orderStatus === \"Pending\" ? \"#f8d7da\" : order.orderStatus === \"Delivered\" ? \"#d4edda\" : \"#FFE5B4\",\n                    color: order.orderStatus === \"Pending\" ? \"#721c24\" : order.orderStatus === \"Delivered\" ? \"#155724\" : \"#FF7518\",\n                    fontWeight: \"500\",\n                    textAlign: \"center\",\n                    minWidth: \"80px\"\n                  },\n                  children: (order === null || order === void 0 ? void 0 : order.orderStatus) || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [(order === null || order === void 0 ? void 0 : order.total) || \"N/A\", \" E\\xA3\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, order === null || order === void 0 ? void 0 : order._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination\",\n      children: Array.from({\n        length: totalPages\n      }, (_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: currentPage === index + 1 ? \"active\" : \"\",\n        onClick: () => setCurrentPage(index + 1),\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(RecentPurchasesAdmin, \"L1LuNZlTyVmBj2Ym4Wjy+dW9yI0=\");\n_c = RecentPurchasesAdmin;\nexport default RecentPurchasesAdmin;\nvar _c;\n$RefreshReg$(_c, \"RecentPurchasesAdmin\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "BsThreeDotsVertical", "SlCalender", "subDays", "isWithinInterval", "parseISO", "AdminOrderDetails", "Box", "Select", "MenuItem", "CircularProgress", "jsxDEV", "_jsxDEV", "RecentPurchasesAdmin", "_s", "orders", "setOrders", "currentPage", "setCurrentPage", "filterStatus", "setFilterStatus", "sortOption", "setSortOption", "sortDirection", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "dateFilter", "setDateFilter", "brands", "setBrands", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rand", "isLoading", "setIsLoading", "ordersPerPage", "fetchOrders", "response", "fetch", "data", "json", "error", "console", "fetchBrands", "getFilteredByStatus", "filter", "order", "orderStatus", "getFilteredByBrand", "filtered", "_order$cartItems$", "cartItems", "brandId", "_id", "getFilteredByDate", "today", "Date", "orderDate", "start", "setHours", "end", "sortedOrders", "length", "sort", "a", "b", "_a$cartItems$", "_b$cartItems$", "_b$cartItems$2", "_a$cartItems$2", "name", "localeCompare", "total", "indexOfLastOrder", "indexOfFirstOrder", "currentOrders", "slice", "totalPages", "Math", "ceil", "onBack", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "replace", "sx", "width", "display", "justifyContent", "gap", "marginBottom", "borderRadius", "color", "value", "onChange", "e", "target", "map", "brand", "brandName", "alignItems", "minHeight", "size", "thickness", "flexDirection", "style", "fontSize", "_order$cartItems$2", "_order$customerId", "_order$customerId2", "_order$cartItems$3", "onClick", "cursor", "toLocaleDateString", "customerId", "firstName", "lastName", "padding", "backgroundColor", "fontWeight", "textAlign", "min<PERSON><PERSON><PERSON>", "Array", "from", "_", "index", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/orderListAdmin.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { BsThreeDotsVertical } from \"react-icons/bs\";\r\nimport { SlCalender } from \"react-icons/sl\";\r\nimport { subDays, isWithinInterval, parseISO } from \"date-fns\";\r\nimport AdminOrderDetails from \"./orderDetailsAdmin\";\r\nimport { Box, Select, MenuItem, CircularProgress } from \"@mui/material\";\r\n\r\nconst RecentPurchasesAdmin = () => {\r\n  const [orders, setOrders] = useState([]);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [filterStatus, setFilterStatus] = useState(\"All\");\r\n  const [sortOption, setSortOption] = useState(\"Date\");\r\n  const [sortDirection] = useState(\"desc\");\r\n  const [selectedOrder, setSelectedOrder] = useState(null);\r\n  const [dateFilter, setDateFilter] = useState(\"All\");\r\n  const [brands, setBrands] = useState([]);\r\n  const [selectedBrand, setSelectedBrand] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const ordersPerPage = 8;\r\n\r\n  // Fetch order data from JSON\r\n  const fetchOrders = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        \"https://api.thedesigngrit.com/api/orders/admin-orders\"\r\n      );\r\n      const data = await response.json();\r\n      setOrders(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching orders:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  const fetchBrands = async () => {\r\n    try {\r\n      const response = await fetch(\"https://api.thedesigngrit.com/api/brand\");\r\n      const data = await response.json();\r\n      setBrands(data); // Assuming brands are returned as an array\r\n    } catch (error) {\r\n      console.error(\"Error fetching brands:\", error);\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    fetchOrders();\r\n    fetchBrands();\r\n  }, []);\r\n\r\n  const getFilteredByStatus = () => {\r\n    return filterStatus === \"All\"\r\n      ? orders\r\n      : orders.filter((order) => order.orderStatus === filterStatus);\r\n  };\r\n  const getFilteredByBrand = (filtered) => {\r\n    return selectedBrand\r\n      ? filtered.filter(\r\n          (order) => order.cartItems[0]?.brandId._id === selectedBrand\r\n        )\r\n      : filtered;\r\n  };\r\n\r\n  const getFilteredByDate = (filtered) => {\r\n    const today = new Date();\r\n    if (dateFilter === \"Today\") {\r\n      return filtered.filter((order) =>\r\n        isWithinInterval(parseISO(order.orderDate), {\r\n          start: new Date(today.setHours(0, 0, 0, 0)),\r\n          end: new Date(today.setHours(23, 59, 59, 999)),\r\n        })\r\n      );\r\n    }\r\n    if (dateFilter === \"Last7Days\") {\r\n      return filtered.filter((order) =>\r\n        isWithinInterval(parseISO(order.orderDate), {\r\n          start: subDays(today, 7),\r\n          end: today,\r\n        })\r\n      );\r\n    }\r\n    if (dateFilter === \"Last30Days\") {\r\n      return filtered.filter((order) =>\r\n        isWithinInterval(parseISO(order.orderDate), {\r\n          start: subDays(today, 30),\r\n          end: today,\r\n        })\r\n      );\r\n    }\r\n\r\n    return filtered; // All\r\n  };\r\n\r\n  const sortedOrders = [...getFilteredByDate(getFilteredByStatus())]\r\n    .filter((order) => getFilteredByBrand([order]).length > 0)\r\n    .sort((a, b) => {\r\n      switch (sortOption) {\r\n        case \"Date\":\r\n          return new Date(b.orderDate) - new Date(a.orderDate);\r\n        case \"Alphabetical\":\r\n          return sortDirection === \"asc\"\r\n            ? a.cartItems[0]?.name.localeCompare(b.cartItems[0]?.name)\r\n            : b.cartItems[0]?.name.localeCompare(a.cartItems[0]?.name);\r\n        case \"Price Ascending\":\r\n          return b.total - a.total;\r\n        case \"Price Descending\":\r\n          return a.total - b.total;\r\n        default:\r\n          return 0;\r\n      }\r\n    });\r\n\r\n  const indexOfLastOrder = currentPage * ordersPerPage;\r\n  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;\r\n  const currentOrders = sortedOrders.slice(indexOfFirstOrder, indexOfLastOrder);\r\n  const totalPages = Math.ceil(sortedOrders.length / ordersPerPage);\r\n  if (selectedOrder) {\r\n    return (\r\n      <AdminOrderDetails\r\n        order={selectedOrder}\r\n        onBack={() => setSelectedOrder(null)}\r\n      />\r\n    );\r\n  }\r\n  return (\r\n    <div className=\"dashboard-vendor\">\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <h2>Order List</h2>\r\n          <p>Home &gt; Order List</p>\r\n        </div>\r\n        <div className=\"dashboard-date-vendor\">\r\n          <SlCalender />\r\n          <span>\r\n            {dateFilter === \"All\"\r\n              ? \"All Time\"\r\n              : dateFilter.replace(\"Last\", \"Last \").replace(\"Days\", \" Days\")}\r\n          </span>\r\n        </div>\r\n      </header>\r\n      <Box\r\n        sx={{\r\n          width: \"100%\",\r\n          display: \"flex\",\r\n          justifyContent: \"flex-end\",\r\n          gap: \"20px\",\r\n          marginBottom: \"1rem\",\r\n        }}\r\n      >\r\n        <Select\r\n          sx={{ width: 200, borderRadius: \"5px\", color: \"#2d2d2d\" }}\r\n          value={filterStatus}\r\n          onChange={(e) => setFilterStatus(e.target.value)}\r\n        >\r\n          <MenuItem value=\"All\">Sort By Status</MenuItem>\r\n          <MenuItem value=\"Pending\">Pending</MenuItem>\r\n          <MenuItem value=\"Confirmed\">Confirmed</MenuItem>\r\n          <MenuItem value=\"Shipped\">Shipped</MenuItem>\r\n          <MenuItem value=\"Delivered\">Delivered</MenuItem>\r\n          <MenuItem value=\"Cancelled\">Cancelled</MenuItem>\r\n        </Select>\r\n        <Select\r\n          sx={{ width: 200, borderRadius: \"5px\", color: \"#2d2d2d\" }}\r\n          value={sortOption}\r\n          onChange={(e) => setSortOption(e.target.value)}\r\n        >\r\n          <MenuItem value=\"Date\">Sort By Date</MenuItem>\r\n          <MenuItem value=\"Alphabetical\">Alphabetical</MenuItem>\r\n          <MenuItem value=\"Price Ascending\">Price: Ascending</MenuItem>\r\n          <MenuItem value=\"Price Descending\">Price: Descending</MenuItem>\r\n        </Select>\r\n        <Select\r\n          sx={{ width: 200, borderRadius: \"5px\", color: \"#2d2d2d\" }}\r\n          value={dateFilter}\r\n          onChange={(e) => setDateFilter(e.target.value)}\r\n        >\r\n          <MenuItem value=\"All\">All Time</MenuItem>\r\n          <MenuItem value=\"Today\">Today</MenuItem>\r\n          <MenuItem value=\"Last7Days\">Last 7 Days</MenuItem>\r\n          <MenuItem value=\"Last30Days\">Last 30 Days</MenuItem>\r\n        </Select>\r\n        <Select\r\n          sx={{ width: 200, borderRadius: \"5px\", color: \"#2d2d2d\" }}\r\n          value={selectedBrand}\r\n          onChange={(e) => setSelectedBrand(e.target.value)}\r\n        >\r\n          <MenuItem value=\"\">All Brands</MenuItem>\r\n          {brands.map((brand) => (\r\n            <MenuItem key={brand._id} value={brand._id}>\r\n              {brand.brandName}\r\n            </MenuItem>\r\n          ))}\r\n        </Select>\r\n      </Box>\r\n\r\n      {isLoading ? (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            minHeight: \"400px\",\r\n            width: \"100%\",\r\n          }}\r\n        >\r\n          <CircularProgress size={60} thickness={4} sx={{ color: \"#6b7b58\" }} />\r\n        </Box>\r\n      ) : (\r\n        <div className=\"recent-purchases\">\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"space-between\",\r\n              flexDirection: \"row\",\r\n            }}\r\n          >\r\n            <h2>Recent Purchases</h2>\r\n            <BsThreeDotsVertical style={{ fontSize: \"1rem\" }} />\r\n          </Box>\r\n          <hr />\r\n          <table>\r\n            <thead>\r\n              <tr>\r\n                <th>Product</th>\r\n                <th>Order ID</th>\r\n                <th>Date</th>\r\n                <th>Customer Name</th>\r\n                <th>Brand</th>\r\n                <th>Status</th>\r\n                <th>Amount</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {currentOrders.map((order) => (\r\n                <tr\r\n                  onClick={() => setSelectedOrder(order)}\r\n                  style={{ cursor: \"pointer\" }}\r\n                  key={order?._id}\r\n                >\r\n                  <td>{order?.cartItems[0]?.name || \"N/A\"}</td>\r\n                  <td>{order?._id || \"N/A\"}</td>\r\n                  <td>{new Date(order?.orderDate).toLocaleDateString()}</td>\r\n                  <td>\r\n                    {order.customerId?.firstName || \"N/A\"}\r\n                    {order.customerId?.lastName || \"N/A\"}\r\n                  </td>\r\n                  <td>{order.cartItems[0]?.brandId.brandName || \"N/A\"}</td>\r\n                  <td>\r\n                    <span\r\n                      style={{\r\n                        display: \"inline-block\",\r\n                        padding: \"4px 12px\",\r\n                        borderRadius: \"5px\",\r\n                        backgroundColor:\r\n                          order.orderStatus === \"Pending\"\r\n                            ? \"#f8d7da\"\r\n                            : order.orderStatus === \"Delivered\"\r\n                            ? \"#d4edda\"\r\n                            : \"#FFE5B4\",\r\n                        color:\r\n                          order.orderStatus === \"Pending\"\r\n                            ? \"#721c24\"\r\n                            : order.orderStatus === \"Delivered\"\r\n                            ? \"#155724\"\r\n                            : \"#FF7518\",\r\n                        fontWeight: \"500\",\r\n                        textAlign: \"center\",\r\n                        minWidth: \"80px\",\r\n                      }}\r\n                    >\r\n                      {order?.orderStatus || \"N/A\"}\r\n                    </span>\r\n                  </td>\r\n                  <td>{order?.total || \"N/A\"} E£</td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n      {/* Pagination */}\r\n      <div className=\"pagination\">\r\n        {Array.from({ length: totalPages }, (_, index) => (\r\n          <button\r\n            key={index}\r\n            className={currentPage === index + 1 ? \"active\" : \"\"}\r\n            onClick={() => setCurrentPage(index + 1)}\r\n          >\r\n            {index + 1}\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RecentPurchasesAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,UAAU;AAC9D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,MAAM,CAAC;EACpD,MAAM,CAACuB,aAAa,CAAC,GAAGvB,QAAQ,CAAC,MAAM,CAAC;EACxC,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMkC,aAAa,GAAG,CAAC;;EAEvB;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BF,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAC1B,uDACF,CAAC;MACD,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCvB,SAAS,CAACsB,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRP,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EACD,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyC,CAAC;MACvE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCV,SAAS,CAACS,IAAI,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EACDzC,SAAS,CAAC,MAAM;IACdoC,WAAW,CAAC,CAAC;IACbO,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOxB,YAAY,KAAK,KAAK,GACzBJ,MAAM,GACNA,MAAM,CAAC6B,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAACC,WAAW,KAAK3B,YAAY,CAAC;EAClE,CAAC;EACD,MAAM4B,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,OAAOlB,aAAa,GAChBkB,QAAQ,CAACJ,MAAM,CACZC,KAAK;MAAA,IAAAI,iBAAA;MAAA,OAAK,EAAAA,iBAAA,GAAAJ,KAAK,CAACK,SAAS,CAAC,CAAC,CAAC,cAAAD,iBAAA,uBAAlBA,iBAAA,CAAoBE,OAAO,CAACC,GAAG,MAAKtB,aAAa;IAAA,CAC9D,CAAC,GACDkB,QAAQ;EACd,CAAC;EAED,MAAMK,iBAAiB,GAAIL,QAAQ,IAAK;IACtC,MAAMM,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxB,IAAI7B,UAAU,KAAK,OAAO,EAAE;MAC1B,OAAOsB,QAAQ,CAACJ,MAAM,CAAEC,KAAK,IAC3BzC,gBAAgB,CAACC,QAAQ,CAACwC,KAAK,CAACW,SAAS,CAAC,EAAE;QAC1CC,KAAK,EAAE,IAAIF,IAAI,CAACD,KAAK,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3CC,GAAG,EAAE,IAAIJ,IAAI,CAACD,KAAK,CAACI,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MAC/C,CAAC,CACH,CAAC;IACH;IACA,IAAIhC,UAAU,KAAK,WAAW,EAAE;MAC9B,OAAOsB,QAAQ,CAACJ,MAAM,CAAEC,KAAK,IAC3BzC,gBAAgB,CAACC,QAAQ,CAACwC,KAAK,CAACW,SAAS,CAAC,EAAE;QAC1CC,KAAK,EAAEtD,OAAO,CAACmD,KAAK,EAAE,CAAC,CAAC;QACxBK,GAAG,EAAEL;MACP,CAAC,CACH,CAAC;IACH;IACA,IAAI5B,UAAU,KAAK,YAAY,EAAE;MAC/B,OAAOsB,QAAQ,CAACJ,MAAM,CAAEC,KAAK,IAC3BzC,gBAAgB,CAACC,QAAQ,CAACwC,KAAK,CAACW,SAAS,CAAC,EAAE;QAC1CC,KAAK,EAAEtD,OAAO,CAACmD,KAAK,EAAE,EAAE,CAAC;QACzBK,GAAG,EAAEL;MACP,CAAC,CACH,CAAC;IACH;IAEA,OAAON,QAAQ,CAAC,CAAC;EACnB,CAAC;EAED,MAAMY,YAAY,GAAG,CAAC,GAAGP,iBAAiB,CAACV,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAC/DC,MAAM,CAAEC,KAAK,IAAKE,kBAAkB,CAAC,CAACF,KAAK,CAAC,CAAC,CAACgB,MAAM,GAAG,CAAC,CAAC,CACzDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAAA,IAAAC,aAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;IACd,QAAQ/C,UAAU;MAChB,KAAK,MAAM;QACT,OAAO,IAAIkC,IAAI,CAACS,CAAC,CAACR,SAAS,CAAC,GAAG,IAAID,IAAI,CAACQ,CAAC,CAACP,SAAS,CAAC;MACtD,KAAK,cAAc;QACjB,OAAOjC,aAAa,KAAK,KAAK,IAAA0C,aAAA,GAC1BF,CAAC,CAACb,SAAS,CAAC,CAAC,CAAC,cAAAe,aAAA,uBAAdA,aAAA,CAAgBI,IAAI,CAACC,aAAa,EAAAJ,aAAA,GAACF,CAAC,CAACd,SAAS,CAAC,CAAC,CAAC,cAAAgB,aAAA,uBAAdA,aAAA,CAAgBG,IAAI,CAAC,IAAAF,cAAA,GACxDH,CAAC,CAACd,SAAS,CAAC,CAAC,CAAC,cAAAiB,cAAA,uBAAdA,cAAA,CAAgBE,IAAI,CAACC,aAAa,EAAAF,cAAA,GAACL,CAAC,CAACb,SAAS,CAAC,CAAC,CAAC,cAAAkB,cAAA,uBAAdA,cAAA,CAAgBC,IAAI,CAAC;MAC9D,KAAK,iBAAiB;QACpB,OAAOL,CAAC,CAACO,KAAK,GAAGR,CAAC,CAACQ,KAAK;MAC1B,KAAK,kBAAkB;QACrB,OAAOR,CAAC,CAACQ,KAAK,GAAGP,CAAC,CAACO,KAAK;MAC1B;QACE,OAAO,CAAC;IACZ;EACF,CAAC,CAAC;EAEJ,MAAMC,gBAAgB,GAAGvD,WAAW,GAAGiB,aAAa;EACpD,MAAMuC,iBAAiB,GAAGD,gBAAgB,GAAGtC,aAAa;EAC1D,MAAMwC,aAAa,GAAGd,YAAY,CAACe,KAAK,CAACF,iBAAiB,EAAED,gBAAgB,CAAC;EAC7E,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAClB,YAAY,CAACC,MAAM,GAAG3B,aAAa,CAAC;EACjE,IAAIV,aAAa,EAAE;IACjB,oBACEZ,OAAA,CAACN,iBAAiB;MAChBuC,KAAK,EAAErB,aAAc;MACrBuD,MAAM,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,IAAI;IAAE;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAEN;EACA,oBACEvE,OAAA;IAAKwE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BzE,OAAA;MAAQwE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACzCzE,OAAA;QAAKwE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCzE,OAAA;UAAAyE,QAAA,EAAI;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBvE,OAAA;UAAAyE,QAAA,EAAG;QAAoB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACNvE,OAAA;QAAKwE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCzE,OAAA,CAACV,UAAU;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdvE,OAAA;UAAAyE,QAAA,EACG3D,UAAU,KAAK,KAAK,GACjB,UAAU,GACVA,UAAU,CAAC4D,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,OAAO;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACTvE,OAAA,CAACL,GAAG;MACFgF,EAAE,EAAE;QACFC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,UAAU;QAC1BC,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,gBAEFzE,OAAA,CAACJ,MAAM;QACL+E,EAAE,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEK,YAAY,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAE;QAC1DC,KAAK,EAAE5E,YAAa;QACpB6E,QAAQ,EAAGC,CAAC,IAAK7E,eAAe,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAAAV,QAAA,gBAEjDzE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,KAAK;UAAAV,QAAA,EAAC;QAAc;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC/CvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,SAAS;UAAAV,QAAA,EAAC;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC5CvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,WAAW;UAAAV,QAAA,EAAC;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAChDvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,SAAS;UAAAV,QAAA,EAAC;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC5CvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,WAAW;UAAAV,QAAA,EAAC;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAChDvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,WAAW;UAAAV,QAAA,EAAC;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACTvE,OAAA,CAACJ,MAAM;QACL+E,EAAE,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEK,YAAY,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAE;QAC1DC,KAAK,EAAE1E,UAAW;QAClB2E,QAAQ,EAAGC,CAAC,IAAK3E,aAAa,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAAAV,QAAA,gBAE/CzE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,MAAM;UAAAV,QAAA,EAAC;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC9CvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,cAAc;UAAAV,QAAA,EAAC;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACtDvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,iBAAiB;UAAAV,QAAA,EAAC;QAAgB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC7DvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,kBAAkB;UAAAV,QAAA,EAAC;QAAiB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACTvE,OAAA,CAACJ,MAAM;QACL+E,EAAE,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEK,YAAY,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAE;QAC1DC,KAAK,EAAErE,UAAW;QAClBsE,QAAQ,EAAGC,CAAC,IAAKtE,aAAa,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAAAV,QAAA,gBAE/CzE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,KAAK;UAAAV,QAAA,EAAC;QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACzCvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,OAAO;UAAAV,QAAA,EAAC;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACxCvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,WAAW;UAAAV,QAAA,EAAC;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAClDvE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,YAAY;UAAAV,QAAA,EAAC;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACTvE,OAAA,CAACJ,MAAM;QACL+E,EAAE,EAAE;UAAEC,KAAK,EAAE,GAAG;UAAEK,YAAY,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAE;QAC1DC,KAAK,EAAEjE,aAAc;QACrBkE,QAAQ,EAAGC,CAAC,IAAKlE,gBAAgB,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAAAV,QAAA,gBAElDzE,OAAA,CAACH,QAAQ;UAACsF,KAAK,EAAC,EAAE;UAAAV,QAAA,EAAC;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,EACvCvD,MAAM,CAACuE,GAAG,CAAEC,KAAK,iBAChBxF,OAAA,CAACH,QAAQ;UAAiBsF,KAAK,EAAEK,KAAK,CAAChD,GAAI;UAAAiC,QAAA,EACxCe,KAAK,CAACC;QAAS,GADHD,KAAK,CAAChD,GAAG;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEd,CACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELnD,SAAS,gBACRpB,OAAA,CAACL,GAAG;MACFgF,EAAE,EAAE;QACFE,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBY,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,OAAO;QAClBf,KAAK,EAAE;MACT,CAAE;MAAAH,QAAA,eAEFzE,OAAA,CAACF,gBAAgB;QAAC8F,IAAI,EAAE,EAAG;QAACC,SAAS,EAAE,CAAE;QAAClB,EAAE,EAAE;UAAEO,KAAK,EAAE;QAAU;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,gBAENvE,OAAA;MAAKwE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzE,OAAA,CAACL,GAAG;QACFgF,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BgB,aAAa,EAAE;QACjB,CAAE;QAAArB,QAAA,gBAEFzE,OAAA;UAAAyE,QAAA,EAAI;QAAgB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBvE,OAAA,CAACX,mBAAmB;UAAC0G,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACNvE,OAAA;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNvE,OAAA;QAAAyE,QAAA,gBACEzE,OAAA;UAAAyE,QAAA,eACEzE,OAAA;YAAAyE,QAAA,gBACEzE,OAAA;cAAAyE,QAAA,EAAI;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBvE,OAAA;cAAAyE,QAAA,EAAI;YAAQ;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBvE,OAAA;cAAAyE,QAAA,EAAI;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbvE,OAAA;cAAAyE,QAAA,EAAI;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBvE,OAAA;cAAAyE,QAAA,EAAI;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACdvE,OAAA;cAAAyE,QAAA,EAAI;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfvE,OAAA;cAAAyE,QAAA,EAAI;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRvE,OAAA;UAAAyE,QAAA,EACGX,aAAa,CAACyB,GAAG,CAAEtD,KAAK;YAAA,IAAAgE,kBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;YAAA,oBACvBpG,OAAA;cACEqG,OAAO,EAAEA,CAAA,KAAMxF,gBAAgB,CAACoB,KAAK,CAAE;cACvC8D,KAAK,EAAE;gBAAEO,MAAM,EAAE;cAAU,CAAE;cAAA7B,QAAA,gBAG7BzE,OAAA;gBAAAyE,QAAA,EAAK,CAAAxC,KAAK,aAALA,KAAK,wBAAAgE,kBAAA,GAALhE,KAAK,CAAEK,SAAS,CAAC,CAAC,CAAC,cAAA2D,kBAAA,uBAAnBA,kBAAA,CAAqBxC,IAAI,KAAI;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7CvE,OAAA;gBAAAyE,QAAA,EAAK,CAAAxC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,GAAG,KAAI;cAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BvE,OAAA;gBAAAyE,QAAA,EAAK,IAAI9B,IAAI,CAACV,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEW,SAAS,CAAC,CAAC2D,kBAAkB,CAAC;cAAC;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DvE,OAAA;gBAAAyE,QAAA,GACG,EAAAyB,iBAAA,GAAAjE,KAAK,CAACuE,UAAU,cAAAN,iBAAA,uBAAhBA,iBAAA,CAAkBO,SAAS,KAAI,KAAK,EACpC,EAAAN,kBAAA,GAAAlE,KAAK,CAACuE,UAAU,cAAAL,kBAAA,uBAAhBA,kBAAA,CAAkBO,QAAQ,KAAI,KAAK;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACLvE,OAAA;gBAAAyE,QAAA,EAAK,EAAA2B,kBAAA,GAAAnE,KAAK,CAACK,SAAS,CAAC,CAAC,CAAC,cAAA8D,kBAAA,uBAAlBA,kBAAA,CAAoB7D,OAAO,CAACkD,SAAS,KAAI;cAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzDvE,OAAA;gBAAAyE,QAAA,eACEzE,OAAA;kBACE+F,KAAK,EAAE;oBACLlB,OAAO,EAAE,cAAc;oBACvB8B,OAAO,EAAE,UAAU;oBACnB1B,YAAY,EAAE,KAAK;oBACnB2B,eAAe,EACb3E,KAAK,CAACC,WAAW,KAAK,SAAS,GAC3B,SAAS,GACTD,KAAK,CAACC,WAAW,KAAK,WAAW,GACjC,SAAS,GACT,SAAS;oBACfgD,KAAK,EACHjD,KAAK,CAACC,WAAW,KAAK,SAAS,GAC3B,SAAS,GACTD,KAAK,CAACC,WAAW,KAAK,WAAW,GACjC,SAAS,GACT,SAAS;oBACf2E,UAAU,EAAE,KAAK;oBACjBC,SAAS,EAAE,QAAQ;oBACnBC,QAAQ,EAAE;kBACZ,CAAE;kBAAAtC,QAAA,EAED,CAAAxC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,WAAW,KAAI;gBAAK;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLvE,OAAA;gBAAAyE,QAAA,GAAK,CAAAxC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0B,KAAK,KAAI,KAAK,EAAC,QAAG;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GApC9BtC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,GAAG;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqCb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,eAEDvE,OAAA;MAAKwE,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxBuC,KAAK,CAACC,IAAI,CAAC;QAAEhE,MAAM,EAAEe;MAAW,CAAC,EAAE,CAACkD,CAAC,EAAEC,KAAK,kBAC3CnH,OAAA;QAEEwE,SAAS,EAAEnE,WAAW,KAAK8G,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAG;QACrDd,OAAO,EAAEA,CAAA,KAAM/F,cAAc,CAAC6G,KAAK,GAAG,CAAC,CAAE;QAAA1C,QAAA,EAExC0C,KAAK,GAAG;MAAC,GAJLA,KAAK;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrE,EAAA,CA9RID,oBAAoB;AAAAmH,EAAA,GAApBnH,oBAAoB;AAgS1B,eAAeA,oBAAoB;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}