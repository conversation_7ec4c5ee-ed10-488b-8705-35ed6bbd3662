{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\myAccount.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Box } from \"@mui/material\";\nimport Header from \"../Components/navBar\";\nimport axios from \"axios\";\nimport Profile from \"../Components/account/profile\";\nimport { UserContext } from \"../utils/userContext\";\nimport { useNavigate, useLocation, useParams } from \"react-router-dom\";\nimport ResetPasswordForm from \"../Components/profilePopup/resetPassowrd\";\nimport ShippingInfoPopup from \"../Components/profilePopup/Shipping\";\nimport WishlistPage from \"../Components/account/wishlist\";\nimport Footer from \"../Components/Footer\";\nimport TrackOrder from \"./TrackOrder\";\nimport LoadingScreen from \"./loadingScreen\";\nimport BillingInfo from \"../Components/profilePopup/billingInfo\";\nimport TrackQuotation from \"../Components/TrackQuotation\";\nimport ConfirmationDialog from \"../Components/confirmationMsg\";\nimport TrackViewInStore from \"../Components/trackViewInStore\";\n\n// import BillingInfo from \"../Components/profilePopup/billingInfo\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MyAccount = () => {\n  _s();\n  const {\n    section\n  } = useParams();\n  const [selectedSection, setSelectedSection] = useState(section || \"profile\");\n  const {\n    userSession,\n    logout\n  } = useContext(UserContext); // Access user session and logout function from context\n  const [userData, setUserData] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    address1: \"\",\n    phoneNumber: \"\",\n    gender: \"\"\n  });\n  const location = useLocation();\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);\n\n  // When the URL param changes, update selectedSection\n  useEffect(() => {\n    setSelectedSection(section || \"profile\");\n  }, [section]);\n  // If location.state.section is set (e.g., from navigation), update the URL\n  useEffect(() => {\n    var _location$state;\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.section) {\n      navigate(`/myaccount/${location.state.section}`);\n    }\n  }, [location, navigate]);\n  useEffect(() => {\n    // Redirect to login if user is not logged in\n    if (!userSession) {\n      navigate(\"/login\"); // Redirect to login page if no session\n      return;\n    }\n\n    // Fetch user data if logged in\n    const fetchData = async () => {\n      console.log(\"id in MyAccount:\", userSession.id);\n      try {\n        const response = await axios.get(`https://api.thedesigngrit.com/api/getUserById/${userSession.id}`, {\n          withCredentials: true\n        });\n        console.log(\"userSession in MyAccount:\", userSession);\n        setUserData(response.data);\n      } catch (error) {\n        console.error(\"Error fetching user data:\", error.response || error);\n      } finally {\n        setTimeout(() => {\n          setLoading(false);\n        }, 5000);\n      }\n    };\n    fetchData();\n  }, [userSession, navigate]); // Add `navigate` to the dependencies\n\n  const handleLogoutClick = () => {\n    setLogoutConfirmOpen(true);\n  };\n  const handleLogoutConfirm = () => {\n    logout(); // Call logout from context\n    navigate(\"/home\"); // Redirect to home or login page\n    setLogoutConfirmOpen(false);\n  };\n  const handleLogoutCancel = () => {\n    setLogoutConfirmOpen(false);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingScreen, {\n      onComplete: () => setLoading(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 12\n    }, this);\n  }\n  const sections = {\n    profile: /*#__PURE__*/_jsxDEV(Profile, {\n      userData: userData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 14\n    }, this),\n    // Pass userData as a prop\n    orders: /*#__PURE__*/_jsxDEV(TrackOrder, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    quotation: /*#__PURE__*/_jsxDEV(TrackQuotation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 16\n    }, this),\n    trackviewinstore: /*#__PURE__*/_jsxDEV(TrackViewInStore, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 23\n    }, this),\n    Password: /*#__PURE__*/_jsxDEV(ResetPasswordForm, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 15\n    }, this),\n    // Billing: <BillingInfo />,\n    shipping: /*#__PURE__*/_jsxDEV(ShippingInfoPopup, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 15\n    }, this),\n    billing: /*#__PURE__*/_jsxDEV(BillingInfo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 14\n    }, this),\n    wishlist: /*#__PURE__*/_jsxDEV(WishlistPage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 15\n    }, this),\n    // Render logout as a clickable word that performs the logout directly\n    logout: /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        cursor: \"pointer\"\n      },\n      onClick: handleLogoutClick,\n      children: \"Logout\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-job-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: [\"Hey,\", \" \", userData.firstName.charAt(0).toUpperCase() + userData.firstName.slice(1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"terms-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar\",\n          children: Object.keys(sections).map(section => section !== \"logout\" ? /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `sidebar-item ${selectedSection === section.toLowerCase() ? \"active\" : \"\"}`,\n            onClick: () => {\n              navigate(`/myaccount/${section.toLowerCase()}`);\n            },\n            children: section === \"trackviewinstore\" ? \"View In Store\" : section\n          }, section, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-item\",\n            children: sections[section]\n          }, section, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: sections[selectedSection]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationDialog, {\n      open: logoutConfirmOpen,\n      title: \"Confirm Logout\",\n      content: \"Are you sure you want to logout?\",\n      onConfirm: handleLogoutConfirm,\n      onCancel: handleLogoutCancel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(MyAccount, \"8gWIA+bNzAx6g8YMcx+0QOkY0V8=\", false, function () {\n  return [useParams, useLocation, useNavigate];\n});\n_c = MyAccount;\nexport default MyAccount;\nvar _c;\n$RefreshReg$(_c, \"MyAccount\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Box", "Header", "axios", "Profile", "UserContext", "useNavigate", "useLocation", "useParams", "ResetPasswordForm", "ShippingInfoPopup", "WishlistPage", "Footer", "TrackOrder", "LoadingScreen", "BillingInfo", "TrackQuotation", "ConfirmationDialog", "TrackViewInStore", "jsxDEV", "_jsxDEV", "MyAccount", "_s", "section", "selectedSection", "setSelectedSection", "userSession", "logout", "userData", "setUserData", "firstName", "lastName", "email", "address1", "phoneNumber", "gender", "location", "loading", "setLoading", "navigate", "logoutConfirmOpen", "setLogoutConfirmOpen", "_location$state", "state", "fetchData", "console", "log", "id", "response", "get", "withCredentials", "data", "error", "setTimeout", "handleLogoutClick", "handleLogoutConfirm", "handleLogoutCancel", "onComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sections", "profile", "orders", "quotation", "trackviewinstore", "Password", "shipping", "billing", "wishlist", "style", "cursor", "onClick", "children", "className", "char<PERSON>t", "toUpperCase", "slice", "Object", "keys", "map", "toLowerCase", "open", "title", "content", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/myAccount.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport { Box } from \"@mui/material\";\r\nimport Header from \"../Components/navBar\";\r\nimport axios from \"axios\";\r\nimport Profile from \"../Components/account/profile\";\r\nimport { UserContext } from \"../utils/userContext\";\r\nimport { useNavigate, useLocation, useParams } from \"react-router-dom\";\r\nimport ResetPasswordForm from \"../Components/profilePopup/resetPassowrd\";\r\nimport ShippingInfoPopup from \"../Components/profilePopup/Shipping\";\r\nimport WishlistPage from \"../Components/account/wishlist\";\r\nimport Footer from \"../Components/Footer\";\r\nimport TrackOrder from \"./TrackOrder\";\r\nimport LoadingScreen from \"./loadingScreen\";\r\nimport BillingInfo from \"../Components/profilePopup/billingInfo\";\r\nimport TrackQuotation from \"../Components/TrackQuotation\";\r\nimport ConfirmationDialog from \"../Components/confirmationMsg\";\r\nimport TrackViewInStore from \"../Components/trackViewInStore\";\r\n\r\n// import BillingInfo from \"../Components/profilePopup/billingInfo\";\r\nconst MyAccount = () => {\r\n  const { section } = useParams();\r\n  const [selectedSection, setSelectedSection] = useState(section || \"profile\");\r\n  const { userSession, logout } = useContext(UserContext); // Access user session and logout function from context\r\n  const [userData, setUserData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    email: \"\",\r\n    address1: \"\",\r\n    phoneNumber: \"\",\r\n    gender: \"\",\r\n  });\r\n  const location = useLocation();\r\n  const [loading, setLoading] = useState(true);\r\n  const navigate = useNavigate();\r\n  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);\r\n\r\n  // When the URL param changes, update selectedSection\r\n  useEffect(() => {\r\n    setSelectedSection(section || \"profile\");\r\n  }, [section]);\r\n  // If location.state.section is set (e.g., from navigation), update the URL\r\n  useEffect(() => {\r\n    if (location.state?.section) {\r\n      navigate(`/myaccount/${location.state.section}`);\r\n    }\r\n  }, [location, navigate]);\r\n\r\n  useEffect(() => {\r\n    // Redirect to login if user is not logged in\r\n    if (!userSession) {\r\n      navigate(\"/login\"); // Redirect to login page if no session\r\n      return;\r\n    }\r\n\r\n    // Fetch user data if logged in\r\n    const fetchData = async () => {\r\n      console.log(\"id in MyAccount:\", userSession.id);\r\n      try {\r\n        const response = await axios.get(\r\n          `https://api.thedesigngrit.com/api/getUserById/${userSession.id}`,\r\n          {\r\n            withCredentials: true,\r\n          }\r\n        );\r\n        console.log(\"userSession in MyAccount:\", userSession);\r\n        setUserData(response.data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching user data:\", error.response || error);\r\n      } finally {\r\n        setTimeout(() => {\r\n          setLoading(false);\r\n        }, 5000);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [userSession, navigate]); // Add `navigate` to the dependencies\r\n\r\n  const handleLogoutClick = () => {\r\n    setLogoutConfirmOpen(true);\r\n  };\r\n\r\n  const handleLogoutConfirm = () => {\r\n    logout(); // Call logout from context\r\n    navigate(\"/home\"); // Redirect to home or login page\r\n    setLogoutConfirmOpen(false);\r\n  };\r\n\r\n  const handleLogoutCancel = () => {\r\n    setLogoutConfirmOpen(false);\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingScreen onComplete={() => setLoading(false)} />;\r\n  }\r\n  const sections = {\r\n    profile: <Profile userData={userData} />, // Pass userData as a prop\r\n    orders: <TrackOrder />,\r\n    quotation: <TrackQuotation />,\r\n    trackviewinstore: <TrackViewInStore />,\r\n    Password: <ResetPasswordForm />,\r\n    // Billing: <BillingInfo />,\r\n    shipping: <ShippingInfoPopup />,\r\n    billing: <BillingInfo />,\r\n    wishlist: <WishlistPage />,\r\n    // Render logout as a clickable word that performs the logout directly\r\n    logout: (\r\n      <span style={{ cursor: \"pointer\" }} onClick={handleLogoutClick}>\r\n        Logout\r\n      </span>\r\n    ),\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Header />\r\n      <Box>\r\n        <div className=\"hero-job-container\">\r\n          <div className=\"hero-text\">\r\n            <h1 className=\"hero-title\">\r\n              Hey,{\" \"}\r\n              {userData.firstName.charAt(0).toUpperCase() +\r\n                userData.firstName.slice(1)}\r\n            </h1>\r\n          </div>\r\n        </div>\r\n        <div className=\"terms-container\">\r\n          {/* Sidebar */}\r\n          <div className=\"sidebar\">\r\n            {Object.keys(sections).map((section) =>\r\n              section !== \"logout\" ? (\r\n                <button\r\n                  key={section}\r\n                  className={`sidebar-item ${\r\n                    selectedSection === section.toLowerCase() ? \"active\" : \"\"\r\n                  }`}\r\n                  onClick={() => {\r\n                    navigate(`/myaccount/${section.toLowerCase()}`);\r\n                  }}\r\n                >\r\n                  {section === \"trackviewinstore\" ? \"View In Store\" : section}\r\n                </button>\r\n              ) : (\r\n                <div key={section} className=\"sidebar-item\">\r\n                  {sections[section]}\r\n                </div>\r\n              )\r\n            )}\r\n          </div>\r\n          <div className=\"divider\"></div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"content\">{sections[selectedSection]}</div>\r\n        </div>\r\n      </Box>\r\n      <Footer />\r\n      <ConfirmationDialog\r\n        open={logoutConfirmOpen}\r\n        title=\"Confirm Logout\"\r\n        content=\"Are you sure you want to logout?\"\r\n        onConfirm={handleLogoutConfirm}\r\n        onCancel={handleLogoutCancel}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default MyAccount;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,+BAA+B;AACnD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,OAAOC,gBAAgB,MAAM,gCAAgC;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAQ,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC/B,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAACyB,OAAO,IAAI,SAAS,CAAC;EAC5E,MAAM;IAAEG,WAAW;IAAEC;EAAO,CAAC,GAAG3B,UAAU,CAACK,WAAW,CAAC,CAAC,CAAC;EACzD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC;IACvCgC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMyC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACAC,SAAS,CAAC,MAAM;IACd0B,kBAAkB,CAACF,OAAO,IAAI,SAAS,CAAC;EAC1C,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb;EACAxB,SAAS,CAAC,MAAM;IAAA,IAAA2C,eAAA;IACd,KAAAA,eAAA,GAAIN,QAAQ,CAACO,KAAK,cAAAD,eAAA,eAAdA,eAAA,CAAgBnB,OAAO,EAAE;MAC3BgB,QAAQ,CAAC,cAAcH,QAAQ,CAACO,KAAK,CAACpB,OAAO,EAAE,CAAC;IAClD;EACF,CAAC,EAAE,CAACa,QAAQ,EAAEG,QAAQ,CAAC,CAAC;EAExBxC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC2B,WAAW,EAAE;MAChBa,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;MACpB;IACF;;IAEA;IACA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEpB,WAAW,CAACqB,EAAE,CAAC;MAC/C,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM7C,KAAK,CAAC8C,GAAG,CAC9B,iDAAiDvB,WAAW,CAACqB,EAAE,EAAE,EACjE;UACEG,eAAe,EAAE;QACnB,CACF,CAAC;QACDL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEpB,WAAW,CAAC;QACrDG,WAAW,CAACmB,QAAQ,CAACG,IAAI,CAAC;MAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACJ,QAAQ,IAAII,KAAK,CAAC;MACrE,CAAC,SAAS;QACRC,UAAU,CAAC,MAAM;UACff,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IAEDM,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAClB,WAAW,EAAEa,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE7B,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bb,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChC5B,MAAM,CAAC,CAAC,CAAC,CAAC;IACVY,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACnBE,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bf,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,IAAIJ,OAAO,EAAE;IACX,oBAAOjB,OAAA,CAACN,aAAa;MAAC2C,UAAU,EAAEA,CAAA,KAAMnB,UAAU,CAAC,KAAK;IAAE;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/D;EACA,MAAMC,QAAQ,GAAG;IACfC,OAAO,eAAE3C,OAAA,CAAChB,OAAO;MAACwB,QAAQ,EAAEA;IAAS;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE;IAC1CG,MAAM,eAAE5C,OAAA,CAACP,UAAU;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBI,SAAS,eAAE7C,OAAA,CAACJ,cAAc;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BK,gBAAgB,eAAE9C,OAAA,CAACF,gBAAgB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCM,QAAQ,eAAE/C,OAAA,CAACX,iBAAiB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/B;IACAO,QAAQ,eAAEhD,OAAA,CAACV,iBAAiB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/BQ,OAAO,eAAEjD,OAAA,CAACL,WAAW;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBS,QAAQ,eAAElD,OAAA,CAACT,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1B;IACAlC,MAAM,eACJP,OAAA;MAAMmD,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAU,CAAE;MAACC,OAAO,EAAEnB,iBAAkB;MAAAoB,QAAA,EAAC;IAEhE;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEV,CAAC;EAED,oBACEzC,OAAA,CAACnB,GAAG;IAAAyE,QAAA,gBACFtD,OAAA,CAAClB,MAAM;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVzC,OAAA,CAACnB,GAAG;MAAAyE,QAAA,gBACFtD,OAAA;QAAKuD,SAAS,EAAC,oBAAoB;QAAAD,QAAA,eACjCtD,OAAA;UAAKuD,SAAS,EAAC,WAAW;UAAAD,QAAA,eACxBtD,OAAA;YAAIuD,SAAS,EAAC,YAAY;YAAAD,QAAA,GAAC,MACrB,EAAC,GAAG,EACP9C,QAAQ,CAACE,SAAS,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACzCjD,QAAQ,CAACE,SAAS,CAACgD,KAAK,CAAC,CAAC,CAAC;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNzC,OAAA;QAAKuD,SAAS,EAAC,iBAAiB;QAAAD,QAAA,gBAE9BtD,OAAA;UAAKuD,SAAS,EAAC,SAAS;UAAAD,QAAA,EACrBK,MAAM,CAACC,IAAI,CAAClB,QAAQ,CAAC,CAACmB,GAAG,CAAE1D,OAAO,IACjCA,OAAO,KAAK,QAAQ,gBAClBH,OAAA;YAEEuD,SAAS,EAAE,gBACTnD,eAAe,KAAKD,OAAO,CAAC2D,WAAW,CAAC,CAAC,GAAG,QAAQ,GAAG,EAAE,EACxD;YACHT,OAAO,EAAEA,CAAA,KAAM;cACblC,QAAQ,CAAC,cAAchB,OAAO,CAAC2D,WAAW,CAAC,CAAC,EAAE,CAAC;YACjD,CAAE;YAAAR,QAAA,EAEDnD,OAAO,KAAK,kBAAkB,GAAG,eAAe,GAAGA;UAAO,GARtDA,OAAO;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASN,CAAC,gBAETzC,OAAA;YAAmBuD,SAAS,EAAC,cAAc;YAAAD,QAAA,EACxCZ,QAAQ,CAACvC,OAAO;UAAC,GADVA,OAAO;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEZ,CAET;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzC,OAAA;UAAKuD,SAAS,EAAC;QAAS;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG/BzC,OAAA;UAAKuD,SAAS,EAAC,SAAS;UAAAD,QAAA,EAAEZ,QAAQ,CAACtC,eAAe;QAAC;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNzC,OAAA,CAACR,MAAM;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVzC,OAAA,CAACH,kBAAkB;MACjBkE,IAAI,EAAE3C,iBAAkB;MACxB4C,KAAK,EAAC,gBAAgB;MACtBC,OAAO,EAAC,kCAAkC;MAC1CC,SAAS,EAAE/B,mBAAoB;MAC/BgC,QAAQ,EAAE/B;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvC,EAAA,CAlJID,SAAS;EAAA,QACOb,SAAS,EAWZD,WAAW,EAEXD,WAAW;AAAA;AAAAkF,EAAA,GAdxBnE,SAAS;AAoJf,eAAeA,SAAS;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}