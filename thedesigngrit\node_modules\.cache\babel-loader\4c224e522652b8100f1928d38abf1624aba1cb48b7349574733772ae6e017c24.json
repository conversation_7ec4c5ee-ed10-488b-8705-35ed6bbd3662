{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Checkout\\\\Shippingform.jsx\",\n  _s = $RefreshSig$();\nimport { Box } from \"@mui/material\";\nimport React, { useState, useContext, useEffect, useCallback } from \"react\";\nimport { Checkbox, FormControlLabel } from \"@mui/material\";\nimport { styled } from \"@mui/system\";\nimport { UserContext } from \"../../utils/userContext\";\nimport AddressSelectionPopup from \"./AddressSelectionPopup\";\nimport axios from \"axios\";\n\n// Styled circular checkbox\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CircularCheckbox = styled(Checkbox)(({\n  theme\n}) => ({\n  padding: 0,\n  marginLeft: \"12px\",\n  marginRight: \"12px\",\n  \"& .MuiSvgIcon-root\": {\n    fontSize: \"0\" // Hide the default square checkmark\n  },\n  width: 20,\n  height: 20,\n  borderRadius: \"50%\",\n  border: \"2px solid #000\",\n  backgroundColor: \"transparent\",\n  position: \"relative\",\n  \"&:before\": {\n    content: '\"\"',\n    position: \"absolute\",\n    top: \"50%\",\n    left: \"50%\",\n    width: \"12px\",\n    height: \"12px\",\n    borderRadius: \"50%\",\n    transform: \"translate(-50%, -50%)\",\n    backgroundColor: \"transparent\",\n    transition: \"background-color 0.2s ease-in-out\"\n  },\n  \"&.Mui-checked:before\": {\n    backgroundColor: \"#000\" // Change this to your desired color\n  }\n}));\n_c = CircularCheckbox;\nfunction ShippingForm({\n  shippingData,\n  onChange,\n  errors = {},\n  validateOnChange = false\n}) {\n  _s();\n  const [selectedOption, setSelectedOption] = useState(\"new\");\n  const {\n    userSession\n  } = useContext(UserContext);\n  const [showAddressPopup, setShowAddressPopup] = useState(false);\n  const [hasAddresses, setHasAddresses] = useState(false);\n  const checkForAddresses = useCallback(async () => {\n    try {\n      const response = await axios.get(`https://api.thedesigngrit.com/api/getUserById/${userSession.id}`, {\n        withCredentials: true\n      });\n      const hasShipmentAddresses = response.data && response.data.shipmentAddress && response.data.shipmentAddress.length > 0;\n      setHasAddresses(hasShipmentAddresses);\n    } catch (error) {\n      console.error(\"Error checking for addresses:\", error);\n      setHasAddresses(false);\n    }\n  }, [userSession === null || userSession === void 0 ? void 0 : userSession.id]);\n  useEffect(() => {\n    if (userSession && userSession.id) {\n      checkForAddresses();\n    }\n  }, [userSession, checkForAddresses]);\n  const handleCheckboxChange = async option => {\n    if (option === \"existing\") {\n      setSelectedOption(\"existing\");\n      if (hasAddresses) {\n        try {\n          var _response$data;\n          const response = await axios.get(`https://api.thedesigngrit.com/api/getUserById/${userSession.id}`, {\n            withCredentials: true\n          });\n          const addresses = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.shipmentAddress) || [];\n          const defaultAddress = addresses.find(addr => addr.isDefault);\n          if (defaultAddress) {\n            handleAddressSelect(defaultAddress); // auto-fill\n            setShowAddressPopup(false);\n          } else {\n            setShowAddressPopup(true);\n          }\n        } catch (err) {\n          console.error(\"Error loading default address:\", err);\n          setShowAddressPopup(true);\n        }\n      } else {\n        setShowAddressPopup(true);\n      }\n    } else {\n      setSelectedOption(\"new\");\n      onChange({\n        firstName: \"\",\n        lastName: \"\",\n        address: \"\",\n        label: \"\",\n        apartment: \"\",\n        floor: \"\",\n        country: \"\",\n        city: \"\",\n        zipCode: \"\"\n      });\n    }\n  };\n  const handleAddressSelect = address => {\n    setSelectedOption(\"existing\");\n\n    // Map the selected address to the shipping form format\n    onChange({\n      firstName: userSession.firstName || \"\",\n      lastName: userSession.lastName || \"\",\n      address: address.address1 || \"\",\n      label: address.label || \"Home\",\n      apartment: address.apartment || address.address2 || \"\",\n      floor: address.floor || \"\",\n      country: address.country || \"\",\n      city: address.city || \"\",\n      zipCode: address.postalCode || \"\"\n    });\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    const updatedData = {\n      ...shippingData,\n      [name]: value\n    };\n    onChange(updatedData);\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    console.log(\"Shipping Data:\", shippingData);\n    onChange(shippingData);\n  };\n\n  // Add CSS for error styling\n  const errorStyle = {\n    border: \"1px solid red\",\n    backgroundColor: \"rgba(255, 0, 0, 0.05)\"\n  };\n  const errorMessageStyle = {\n    color: \"red\",\n    fontSize: \"12px\",\n    marginTop: \"4px\",\n    textAlign: \"left\"\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"Billinginfo_container\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: \"Billinginfo_checkbox\",\n      children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(CircularCheckbox, {\n          checked: selectedOption === \"existing\",\n          onChange: () => handleCheckboxChange(\"existing\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this),\n        label: \"Use the Default Shipping Information\",\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          columnGap: \"16px\",\n          paddingLeft: \"20px\",\n          \"& .MuiFormControlLabel-label\": {\n            fontFamily: \"Montserrat, san-sarif\",\n            fontSize: \"13px\",\n            color: \"#333\"\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        style: {\n          width: \"100%\",\n          height: \"1px\",\n          backgroundColor: \"#ccc\",\n          margin: \"8px 0\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(CircularCheckbox, {\n          checked: selectedOption === \"new\",\n          onChange: () => handleCheckboxChange(\"new\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this),\n        label: \"Enter a new Shipping Information\",\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          columnGap: \"16px\",\n          paddingLeft: \"20px\",\n          \"& .MuiFormControlLabel-label\": {\n            fontFamily: \"Montserrat, san-sarif\",\n            fontSize: \"13px\",\n            color: \"#333\"\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddressSelectionPopup, {\n      open: showAddressPopup,\n      onClose: () => setShowAddressPopup(false),\n      onAddressSelect: handleAddressSelect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"shipping-form-container\",\n      sx: {\n        width: \"85%\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"shipping-form\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"shippingform-form-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shippingform-form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"firstName\",\n                name: \"firstName\",\n                placeholder: \"First Name *\",\n                value: shippingData.firstName,\n                onChange: handleChange,\n                required: true,\n                style: errors.firstName ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), errors.firstName && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.firstName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"lastName\",\n                name: \"lastName\",\n                placeholder: \"Last Name *\",\n                value: shippingData.lastName,\n                onChange: handleChange,\n                required: true,\n                style: errors.lastName ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), errors.lastName && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.lastName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shippingform-form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"address\",\n                name: \"address\",\n                placeholder: \"Address *\",\n                value: shippingData.address,\n                onChange: handleChange,\n                required: true,\n                style: errors.address ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), errors.address && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.address\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"label\",\n                name: \"label\",\n                placeholder: \"Label\",\n                value: shippingData.label,\n                onChange: handleChange,\n                style: errors.label ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), errors.label && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shippingform-form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"apartment\",\n                name: \"apartment\",\n                placeholder: \"Apartment\",\n                value: shippingData.apartment,\n                onChange: handleChange,\n                style: errors.apartment ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), errors.apartment && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.apartment\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"floor\",\n                name: \"floor\",\n                placeholder: \"Floor\",\n                value: shippingData.floor,\n                onChange: handleChange,\n                style: errors.floor ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), errors.floor && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.floor\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shippingform-form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"country\",\n                name: \"country\",\n                placeholder: \"Country\",\n                value: shippingData.country,\n                onChange: handleChange,\n                style: errors.country ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), errors.country && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.country\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"city\",\n                name: \"city\",\n                placeholder: \"City *\",\n                value: shippingData.city,\n                onChange: handleChange,\n                required: true,\n                style: errors.city ? errorStyle : {}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), errors.city && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.city\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"shippingform-form-row\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"zipCode\",\n                name: \"zipCode\",\n                placeholder: \"Zip Code *\",\n                value: shippingData.zipCode,\n                onChange: handleChange,\n                required: true,\n                style: {\n                  ...(errors.zipCode ? errorStyle : {}),\n                  width: window.innerWidth < 768 ? \"100%\" : \"42.6%\"\n                },\n                className: \"zip-code-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), errors.zipCode && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: errorMessageStyle,\n                children: errors.zipCode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            style: {\n              display: \"none\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n}\n_s(ShippingForm, \"BKnl4gbcNsNDQP4wJG0QaQg4964=\");\n_c2 = ShippingForm;\nexport default ShippingForm;\nvar _c, _c2;\n$RefreshReg$(_c, \"CircularCheckbox\");\n$RefreshReg$(_c2, \"ShippingForm\");", "map": {"version": 3, "names": ["Box", "React", "useState", "useContext", "useEffect", "useCallback", "Checkbox", "FormControlLabel", "styled", "UserContext", "AddressSelectionPopup", "axios", "jsxDEV", "_jsxDEV", "CircularCheckbox", "theme", "padding", "marginLeft", "marginRight", "fontSize", "width", "height", "borderRadius", "border", "backgroundColor", "position", "content", "top", "left", "transform", "transition", "_c", "ShippingForm", "shippingData", "onChange", "errors", "validateOnChange", "_s", "selectedOption", "setSelectedOption", "userSession", "showAddressPopup", "setShowAddressPopup", "has<PERSON><PERSON><PERSON>", "setHasAddresses", "checkForAddresses", "response", "get", "id", "withCredentials", "hasShipmentAddresses", "data", "shipmentAddress", "length", "error", "console", "handleCheckboxChange", "option", "_response$data", "addresses", "defaultAddress", "find", "addr", "isDefault", "handleAddressSelect", "err", "firstName", "lastName", "address", "label", "apartment", "floor", "country", "city", "zipCode", "address1", "address2", "postalCode", "handleChange", "e", "name", "value", "target", "updatedData", "handleSubmit", "preventDefault", "log", "errorStyle", "errorMessageStyle", "color", "marginTop", "textAlign", "className", "children", "control", "checked", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "alignItems", "columnGap", "paddingLeft", "fontFamily", "style", "margin", "open", "onClose", "onAddressSelect", "onSubmit", "type", "placeholder", "required", "window", "innerWidth", "_c2", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Checkout/Shippingform.jsx"], "sourcesContent": ["import { Box } from \"@mui/material\";\r\nimport React, { useState, useContext, useEffect, useCallback } from \"react\";\r\nimport { Checkbox, FormControlLabel } from \"@mui/material\";\r\nimport { styled } from \"@mui/system\";\r\nimport { UserContext } from \"../../utils/userContext\";\r\nimport AddressSelectionPopup from \"./AddressSelectionPopup\";\r\nimport axios from \"axios\";\r\n\r\n// Styled circular checkbox\r\nconst CircularCheckbox = styled(Checkbox)(({ theme }) => ({\r\n  padding: 0,\r\n  marginLeft: \"12px\",\r\n  marginRight: \"12px\",\r\n  \"& .MuiSvgIcon-root\": {\r\n    fontSize: \"0\", // Hide the default square checkmark\r\n  },\r\n  width: 20,\r\n  height: 20,\r\n  borderRadius: \"50%\",\r\n  border: \"2px solid #000\",\r\n  backgroundColor: \"transparent\",\r\n  position: \"relative\",\r\n  \"&:before\": {\r\n    content: '\"\"',\r\n    position: \"absolute\",\r\n    top: \"50%\",\r\n    left: \"50%\",\r\n    width: \"12px\",\r\n    height: \"12px\",\r\n    borderRadius: \"50%\",\r\n    transform: \"translate(-50%, -50%)\",\r\n    backgroundColor: \"transparent\",\r\n    transition: \"background-color 0.2s ease-in-out\",\r\n  },\r\n  \"&.<PERSON>i-checked:before\": {\r\n    backgroundColor: \"#000\", // Change this to your desired color\r\n  },\r\n}));\r\n\r\nfunction ShippingForm({\r\n  shippingData,\r\n  onChange,\r\n  errors = {},\r\n  validateOnChange = false,\r\n}) {\r\n  const [selectedOption, setSelectedOption] = useState(\"new\");\r\n  const { userSession } = useContext(UserContext);\r\n  const [showAddressPopup, setShowAddressPopup] = useState(false);\r\n  const [hasAddresses, setHasAddresses] = useState(false);\r\n\r\n  const checkForAddresses = useCallback(async () => {\r\n    try {\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/getUserById/${userSession.id}`,\r\n        { withCredentials: true }\r\n      );\r\n\r\n      const hasShipmentAddresses =\r\n        response.data &&\r\n        response.data.shipmentAddress &&\r\n        response.data.shipmentAddress.length > 0;\r\n\r\n      setHasAddresses(hasShipmentAddresses);\r\n    } catch (error) {\r\n      console.error(\"Error checking for addresses:\", error);\r\n      setHasAddresses(false);\r\n    }\r\n  }, [userSession?.id]);\r\n  useEffect(() => {\r\n    if (userSession && userSession.id) {\r\n      checkForAddresses();\r\n    }\r\n  }, [userSession, checkForAddresses]);\r\n\r\n  const handleCheckboxChange = async (option) => {\r\n    if (option === \"existing\") {\r\n      setSelectedOption(\"existing\");\r\n\r\n      if (hasAddresses) {\r\n        try {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/getUserById/${userSession.id}`,\r\n            { withCredentials: true }\r\n          );\r\n\r\n          const addresses = response.data?.shipmentAddress || [];\r\n          const defaultAddress = addresses.find((addr) => addr.isDefault);\r\n\r\n          if (defaultAddress) {\r\n            handleAddressSelect(defaultAddress); // auto-fill\r\n            setShowAddressPopup(false);\r\n          } else {\r\n            setShowAddressPopup(true);\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Error loading default address:\", err);\r\n          setShowAddressPopup(true);\r\n        }\r\n      } else {\r\n        setShowAddressPopup(true);\r\n      }\r\n    } else {\r\n      setSelectedOption(\"new\");\r\n      onChange({\r\n        firstName: \"\",\r\n        lastName: \"\",\r\n        address: \"\",\r\n        label: \"\",\r\n        apartment: \"\",\r\n        floor: \"\",\r\n        country: \"\",\r\n        city: \"\",\r\n        zipCode: \"\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleAddressSelect = (address) => {\r\n    setSelectedOption(\"existing\");\r\n\r\n    // Map the selected address to the shipping form format\r\n    onChange({\r\n      firstName: userSession.firstName || \"\",\r\n      lastName: userSession.lastName || \"\",\r\n      address: address.address1 || \"\",\r\n      label: address.label || \"Home\",\r\n      apartment: address.apartment || address.address2 || \"\",\r\n      floor: address.floor || \"\",\r\n      country: address.country || \"\",\r\n      city: address.city || \"\",\r\n      zipCode: address.postalCode || \"\",\r\n    });\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    const updatedData = { ...shippingData, [name]: value };\r\n    onChange(updatedData);\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    console.log(\"Shipping Data:\", shippingData);\r\n    onChange(shippingData);\r\n  };\r\n\r\n  // Add CSS for error styling\r\n  const errorStyle = {\r\n    border: \"1px solid red\",\r\n    backgroundColor: \"rgba(255, 0, 0, 0.05)\",\r\n  };\r\n\r\n  const errorMessageStyle = {\r\n    color: \"red\",\r\n    fontSize: \"12px\",\r\n    marginTop: \"4px\",\r\n    textAlign: \"left\",\r\n  };\r\n\r\n  return (\r\n    <Box className=\"Billinginfo_container\">\r\n      <Box className=\"Billinginfo_checkbox\">\r\n        <FormControlLabel\r\n          control={\r\n            <CircularCheckbox\r\n              checked={selectedOption === \"existing\"}\r\n              onChange={() => handleCheckboxChange(\"existing\")}\r\n            />\r\n          }\r\n          label=\"Use the Default Shipping Information\"\r\n          sx={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            columnGap: \"16px\",\r\n            paddingLeft: \"20px\",\r\n            \"& .MuiFormControlLabel-label\": {\r\n              fontFamily: \"Montserrat, san-sarif\",\r\n              fontSize: \"13px\",\r\n              color: \"#333\",\r\n            },\r\n          }}\r\n        />\r\n        <Box\r\n          style={{\r\n            width: \"100%\",\r\n            height: \"1px\",\r\n            backgroundColor: \"#ccc\",\r\n            margin: \"8px 0\",\r\n          }}\r\n        />\r\n        <FormControlLabel\r\n          control={\r\n            <CircularCheckbox\r\n              checked={selectedOption === \"new\"}\r\n              onChange={() => handleCheckboxChange(\"new\")}\r\n            />\r\n          }\r\n          label=\"Enter a new Shipping Information\"\r\n          sx={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            columnGap: \"16px\",\r\n            paddingLeft: \"20px\",\r\n            \"& .MuiFormControlLabel-label\": {\r\n              fontFamily: \"Montserrat, san-sarif\",\r\n              fontSize: \"13px\",\r\n              color: \"#333\",\r\n            },\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      {/* Address Selection Popup */}\r\n      <AddressSelectionPopup\r\n        open={showAddressPopup}\r\n        onClose={() => setShowAddressPopup(false)}\r\n        onAddressSelect={handleAddressSelect}\r\n      />\r\n\r\n      <Box className=\"shipping-form-container\" sx={{ width: \"85%\" }}>\r\n        <Box className=\"shipping-form\">\r\n          <form onSubmit={handleSubmit} className=\"shippingform-form-container\">\r\n            {/* Row 1 */}\r\n            <div className=\"shippingform-form-row\">\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"firstName\"\r\n                  name=\"firstName\"\r\n                  placeholder=\"First Name *\"\r\n                  value={shippingData.firstName}\r\n                  onChange={handleChange}\r\n                  required\r\n                  style={errors.firstName ? errorStyle : {}}\r\n                />\r\n                {errors.firstName && (\r\n                  <div style={errorMessageStyle}>{errors.firstName}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"lastName\"\r\n                  name=\"lastName\"\r\n                  placeholder=\"Last Name *\"\r\n                  value={shippingData.lastName}\r\n                  onChange={handleChange}\r\n                  required\r\n                  style={errors.lastName ? errorStyle : {}}\r\n                />\r\n                {errors.lastName && (\r\n                  <div style={errorMessageStyle}>{errors.lastName}</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Row 2 */}\r\n            <div className=\"shippingform-form-row\">\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"address\"\r\n                  name=\"address\"\r\n                  placeholder=\"Address *\"\r\n                  value={shippingData.address}\r\n                  onChange={handleChange}\r\n                  required\r\n                  style={errors.address ? errorStyle : {}}\r\n                />\r\n                {errors.address && (\r\n                  <div style={errorMessageStyle}>{errors.address}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"label\"\r\n                  name=\"label\"\r\n                  placeholder=\"Label\"\r\n                  value={shippingData.label}\r\n                  onChange={handleChange}\r\n                  style={errors.label ? errorStyle : {}}\r\n                />\r\n                {errors.label && (\r\n                  <div style={errorMessageStyle}>{errors.label}</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Row 3 */}\r\n            <div className=\"shippingform-form-row\">\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"apartment\"\r\n                  name=\"apartment\"\r\n                  placeholder=\"Apartment\"\r\n                  value={shippingData.apartment}\r\n                  onChange={handleChange}\r\n                  style={errors.apartment ? errorStyle : {}}\r\n                />\r\n                {errors.apartment && (\r\n                  <div style={errorMessageStyle}>{errors.apartment}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"floor\"\r\n                  name=\"floor\"\r\n                  placeholder=\"Floor\"\r\n                  value={shippingData.floor}\r\n                  onChange={handleChange}\r\n                  style={errors.floor ? errorStyle : {}}\r\n                />\r\n                {errors.floor && (\r\n                  <div style={errorMessageStyle}>{errors.floor}</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Row 4 */}\r\n            <div className=\"shippingform-form-row\">\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"country\"\r\n                  name=\"country\"\r\n                  placeholder=\"Country\"\r\n                  value={shippingData.country}\r\n                  onChange={handleChange}\r\n                  style={errors.country ? errorStyle : {}}\r\n                />\r\n                {errors.country && (\r\n                  <div style={errorMessageStyle}>{errors.country}</div>\r\n                )}\r\n              </div>\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"city\"\r\n                  name=\"city\"\r\n                  placeholder=\"City *\"\r\n                  value={shippingData.city}\r\n                  onChange={handleChange}\r\n                  required\r\n                  style={errors.city ? errorStyle : {}}\r\n                />\r\n                {errors.city && (\r\n                  <div style={errorMessageStyle}>{errors.city}</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Row 5 */}\r\n            <div className=\"shippingform-form-row\">\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"zipCode\"\r\n                  name=\"zipCode\"\r\n                  placeholder=\"Zip Code *\"\r\n                  value={shippingData.zipCode}\r\n                  onChange={handleChange}\r\n                  required\r\n                  style={{\r\n                    ...(errors.zipCode ? errorStyle : {}),\r\n                    width: window.innerWidth < 768 ? \"100%\" : \"42.6%\",\r\n                  }}\r\n                  className=\"zip-code-input\"\r\n                />\r\n                {errors.zipCode && (\r\n                  <div style={errorMessageStyle}>{errors.zipCode}</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <button type=\"submit\" style={{ display: \"none\" }}></button>\r\n          </form>\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default ShippingForm;\r\n"], "mappings": ";;AAAA,SAASA,GAAG,QAAQ,eAAe;AACnC,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC3E,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC1D,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAGN,MAAM,CAACF,QAAQ,CAAC,CAAC,CAAC;EAAES;AAAM,CAAC,MAAM;EACxDC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,MAAM;EAClBC,WAAW,EAAE,MAAM;EACnB,oBAAoB,EAAE;IACpBC,QAAQ,EAAE,GAAG,CAAE;EACjB,CAAC;EACDC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,KAAK;EACnBC,MAAM,EAAE,gBAAgB;EACxBC,eAAe,EAAE,aAAa;EAC9BC,QAAQ,EAAE,UAAU;EACpB,UAAU,EAAE;IACVC,OAAO,EAAE,IAAI;IACbD,QAAQ,EAAE,UAAU;IACpBE,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACXR,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBO,SAAS,EAAE,uBAAuB;IAClCL,eAAe,EAAE,aAAa;IAC9BM,UAAU,EAAE;EACd,CAAC;EACD,sBAAsB,EAAE;IACtBN,eAAe,EAAE,MAAM,CAAE;EAC3B;AACF,CAAC,CAAC,CAAC;AAACO,EAAA,GA5BEjB,gBAAgB;AA8BtB,SAASkB,YAAYA,CAAC;EACpBC,YAAY;EACZC,QAAQ;EACRC,MAAM,GAAG,CAAC,CAAC;EACXC,gBAAgB,GAAG;AACrB,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAEsC;EAAY,CAAC,GAAGrC,UAAU,CAACM,WAAW,CAAC;EAC/C,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM2C,iBAAiB,GAAGxC,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMyC,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAC9B,iDAAiDP,WAAW,CAACQ,EAAE,EAAE,EACjE;QAAEC,eAAe,EAAE;MAAK,CAC1B,CAAC;MAED,MAAMC,oBAAoB,GACxBJ,QAAQ,CAACK,IAAI,IACbL,QAAQ,CAACK,IAAI,CAACC,eAAe,IAC7BN,QAAQ,CAACK,IAAI,CAACC,eAAe,CAACC,MAAM,GAAG,CAAC;MAE1CT,eAAe,CAACM,oBAAoB,CAAC;IACvC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDV,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,EAAE,CAAC,CAAC;EACrB5C,SAAS,CAAC,MAAM;IACd,IAAIoC,WAAW,IAAIA,WAAW,CAACQ,EAAE,EAAE;MACjCH,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,WAAW,EAAEK,iBAAiB,CAAC,CAAC;EAEpC,MAAMW,oBAAoB,GAAG,MAAOC,MAAM,IAAK;IAC7C,IAAIA,MAAM,KAAK,UAAU,EAAE;MACzBlB,iBAAiB,CAAC,UAAU,CAAC;MAE7B,IAAII,YAAY,EAAE;QAChB,IAAI;UAAA,IAAAe,cAAA;UACF,MAAMZ,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAC9B,iDAAiDP,WAAW,CAACQ,EAAE,EAAE,EACjE;YAAEC,eAAe,EAAE;UAAK,CAC1B,CAAC;UAED,MAAMU,SAAS,GAAG,EAAAD,cAAA,GAAAZ,QAAQ,CAACK,IAAI,cAAAO,cAAA,uBAAbA,cAAA,CAAeN,eAAe,KAAI,EAAE;UACtD,MAAMQ,cAAc,GAAGD,SAAS,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,SAAS,CAAC;UAE/D,IAAIH,cAAc,EAAE;YAClBI,mBAAmB,CAACJ,cAAc,CAAC,CAAC,CAAC;YACrClB,mBAAmB,CAAC,KAAK,CAAC;UAC5B,CAAC,MAAM;YACLA,mBAAmB,CAAC,IAAI,CAAC;UAC3B;QACF,CAAC,CAAC,OAAOuB,GAAG,EAAE;UACZV,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEW,GAAG,CAAC;UACpDvB,mBAAmB,CAAC,IAAI,CAAC;QAC3B;MACF,CAAC,MAAM;QACLA,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF,CAAC,MAAM;MACLH,iBAAiB,CAAC,KAAK,CAAC;MACxBL,QAAQ,CAAC;QACPgC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMV,mBAAmB,GAAII,OAAO,IAAK;IACvC7B,iBAAiB,CAAC,UAAU,CAAC;;IAE7B;IACAL,QAAQ,CAAC;MACPgC,SAAS,EAAE1B,WAAW,CAAC0B,SAAS,IAAI,EAAE;MACtCC,QAAQ,EAAE3B,WAAW,CAAC2B,QAAQ,IAAI,EAAE;MACpCC,OAAO,EAAEA,OAAO,CAACO,QAAQ,IAAI,EAAE;MAC/BN,KAAK,EAAED,OAAO,CAACC,KAAK,IAAI,MAAM;MAC9BC,SAAS,EAAEF,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACQ,QAAQ,IAAI,EAAE;MACtDL,KAAK,EAAEH,OAAO,CAACG,KAAK,IAAI,EAAE;MAC1BC,OAAO,EAAEJ,OAAO,CAACI,OAAO,IAAI,EAAE;MAC9BC,IAAI,EAAEL,OAAO,CAACK,IAAI,IAAI,EAAE;MACxBC,OAAO,EAAEN,OAAO,CAACS,UAAU,IAAI;IACjC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC,MAAMC,WAAW,GAAG;MAAE,GAAGlD,YAAY;MAAE,CAAC+C,IAAI,GAAGC;IAAM,CAAC;IACtD/C,QAAQ,CAACiD,WAAW,CAAC;EACvB,CAAC;EAED,MAAMC,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB9B,OAAO,CAAC+B,GAAG,CAAC,gBAAgB,EAAErD,YAAY,CAAC;IAC3CC,QAAQ,CAACD,YAAY,CAAC;EACxB,CAAC;;EAED;EACA,MAAMsD,UAAU,GAAG;IACjBhE,MAAM,EAAE,eAAe;IACvBC,eAAe,EAAE;EACnB,CAAC;EAED,MAAMgE,iBAAiB,GAAG;IACxBC,KAAK,EAAE,KAAK;IACZtE,QAAQ,EAAE,MAAM;IAChBuE,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE;EACb,CAAC;EAED,oBACE9E,OAAA,CAACb,GAAG;IAAC4F,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpChF,OAAA,CAACb,GAAG;MAAC4F,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnChF,OAAA,CAACN,gBAAgB;QACfuF,OAAO,eACLjF,OAAA,CAACC,gBAAgB;UACfiF,OAAO,EAAEzD,cAAc,KAAK,UAAW;UACvCJ,QAAQ,EAAEA,CAAA,KAAMsB,oBAAoB,CAAC,UAAU;QAAE;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CACF;QACD9B,KAAK,EAAC,sCAAsC;QAC5C+B,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,MAAM;UACnB,8BAA8B,EAAE;YAC9BC,UAAU,EAAE,uBAAuB;YACnCtF,QAAQ,EAAE,MAAM;YAChBsE,KAAK,EAAE;UACT;QACF;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFtF,OAAA,CAACb,GAAG;QACF0G,KAAK,EAAE;UACLtF,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,KAAK;UACbG,eAAe,EAAE,MAAM;UACvBmF,MAAM,EAAE;QACV;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFtF,OAAA,CAACN,gBAAgB;QACfuF,OAAO,eACLjF,OAAA,CAACC,gBAAgB;UACfiF,OAAO,EAAEzD,cAAc,KAAK,KAAM;UAClCJ,QAAQ,EAAEA,CAAA,KAAMsB,oBAAoB,CAAC,KAAK;QAAE;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACF;QACD9B,KAAK,EAAC,kCAAkC;QACxC+B,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,MAAM;UACjBC,WAAW,EAAE,MAAM;UACnB,8BAA8B,EAAE;YAC9BC,UAAU,EAAE,uBAAuB;YACnCtF,QAAQ,EAAE,MAAM;YAChBsE,KAAK,EAAE;UACT;QACF;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtF,OAAA,CAACH,qBAAqB;MACpBkG,IAAI,EAAEnE,gBAAiB;MACvBoE,OAAO,EAAEA,CAAA,KAAMnE,mBAAmB,CAAC,KAAK,CAAE;MAC1CoE,eAAe,EAAE9C;IAAoB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAEFtF,OAAA,CAACb,GAAG;MAAC4F,SAAS,EAAC,yBAAyB;MAACQ,EAAE,EAAE;QAAEhF,KAAK,EAAE;MAAM,CAAE;MAAAyE,QAAA,eAC5DhF,OAAA,CAACb,GAAG;QAAC4F,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BhF,OAAA;UAAMkG,QAAQ,EAAE3B,YAAa;UAACQ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAEnEhF,OAAA;YAAK+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpChF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,WAAW;gBACdgC,IAAI,EAAC,WAAW;gBAChBiC,WAAW,EAAC,cAAc;gBAC1BhC,KAAK,EAAEhD,YAAY,CAACiC,SAAU;gBAC9BhC,QAAQ,EAAE4C,YAAa;gBACvBoC,QAAQ;gBACRR,KAAK,EAAEvE,MAAM,CAAC+B,SAAS,GAAGqB,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACDhE,MAAM,CAAC+B,SAAS,iBACfrD,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAAC+B;cAAS;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,UAAU;gBACbgC,IAAI,EAAC,UAAU;gBACfiC,WAAW,EAAC,aAAa;gBACzBhC,KAAK,EAAEhD,YAAY,CAACkC,QAAS;gBAC7BjC,QAAQ,EAAE4C,YAAa;gBACvBoC,QAAQ;gBACRR,KAAK,EAAEvE,MAAM,CAACgC,QAAQ,GAAGoB,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,EACDhE,MAAM,CAACgC,QAAQ,iBACdtD,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACgC;cAAQ;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAK+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpChF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,SAAS;gBACZgC,IAAI,EAAC,SAAS;gBACdiC,WAAW,EAAC,WAAW;gBACvBhC,KAAK,EAAEhD,YAAY,CAACmC,OAAQ;gBAC5BlC,QAAQ,EAAE4C,YAAa;gBACvBoC,QAAQ;gBACRR,KAAK,EAAEvE,MAAM,CAACiC,OAAO,GAAGmB,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,EACDhE,MAAM,CAACiC,OAAO,iBACbvD,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACiC;cAAO;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,OAAO;gBACVgC,IAAI,EAAC,OAAO;gBACZiC,WAAW,EAAC,OAAO;gBACnBhC,KAAK,EAAEhD,YAAY,CAACoC,KAAM;gBAC1BnC,QAAQ,EAAE4C,YAAa;gBACvB4B,KAAK,EAAEvE,MAAM,CAACkC,KAAK,GAAGkB,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,EACDhE,MAAM,CAACkC,KAAK,iBACXxD,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACkC;cAAK;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAK+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpChF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,WAAW;gBACdgC,IAAI,EAAC,WAAW;gBAChBiC,WAAW,EAAC,WAAW;gBACvBhC,KAAK,EAAEhD,YAAY,CAACqC,SAAU;gBAC9BpC,QAAQ,EAAE4C,YAAa;gBACvB4B,KAAK,EAAEvE,MAAM,CAACmC,SAAS,GAAGiB,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACDhE,MAAM,CAACmC,SAAS,iBACfzD,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACmC;cAAS;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,OAAO;gBACVgC,IAAI,EAAC,OAAO;gBACZiC,WAAW,EAAC,OAAO;gBACnBhC,KAAK,EAAEhD,YAAY,CAACsC,KAAM;gBAC1BrC,QAAQ,EAAE4C,YAAa;gBACvB4B,KAAK,EAAEvE,MAAM,CAACoC,KAAK,GAAGgB,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,EACDhE,MAAM,CAACoC,KAAK,iBACX1D,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACoC;cAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAK+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpChF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,SAAS;gBACZgC,IAAI,EAAC,SAAS;gBACdiC,WAAW,EAAC,SAAS;gBACrBhC,KAAK,EAAEhD,YAAY,CAACuC,OAAQ;gBAC5BtC,QAAQ,EAAE4C,YAAa;gBACvB4B,KAAK,EAAEvE,MAAM,CAACqC,OAAO,GAAGe,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,EACDhE,MAAM,CAACqC,OAAO,iBACb3D,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACqC;cAAO;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,MAAM;gBACTgC,IAAI,EAAC,MAAM;gBACXiC,WAAW,EAAC,QAAQ;gBACpBhC,KAAK,EAAEhD,YAAY,CAACwC,IAAK;gBACzBvC,QAAQ,EAAE4C,YAAa;gBACvBoC,QAAQ;gBACRR,KAAK,EAAEvE,MAAM,CAACsC,IAAI,GAAGc,UAAU,GAAG,CAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACDhE,MAAM,CAACsC,IAAI,iBACV5D,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACsC;cAAI;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtF,OAAA;YAAK+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpChF,OAAA;cAAK+E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhF,OAAA;gBACEmG,IAAI,EAAC,MAAM;gBACXhE,EAAE,EAAC,SAAS;gBACZgC,IAAI,EAAC,SAAS;gBACdiC,WAAW,EAAC,YAAY;gBACxBhC,KAAK,EAAEhD,YAAY,CAACyC,OAAQ;gBAC5BxC,QAAQ,EAAE4C,YAAa;gBACvBoC,QAAQ;gBACRR,KAAK,EAAE;kBACL,IAAIvE,MAAM,CAACuC,OAAO,GAAGa,UAAU,GAAG,CAAC,CAAC,CAAC;kBACrCnE,KAAK,EAAE+F,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG;gBAC5C,CAAE;gBACFxB,SAAS,EAAC;cAAgB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EACDhE,MAAM,CAACuC,OAAO,iBACb7D,OAAA;gBAAK6F,KAAK,EAAElB,iBAAkB;gBAAAK,QAAA,EAAE1D,MAAM,CAACuC;cAAO;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtF,OAAA;YAAQmG,IAAI,EAAC,QAAQ;YAACN,KAAK,EAAE;cAAEL,OAAO,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9D,EAAA,CAxVQL,YAAY;AAAAqF,GAAA,GAAZrF,YAAY;AA0VrB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAsF,GAAA;AAAAC,YAAA,CAAAvF,EAAA;AAAAuF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}