{"version": 3, "file": "Cropper.d.ts", "sourceRoot": "", "sources": ["src/Cropper.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAA;AAE9B,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAA;AAehE,MAAM,MAAM,YAAY,GAAG;IACzB,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,KAAK,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,CAAA;IAC3B,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,IAAI,EAAE,KAAK,CAAA;IACX,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,MAAM,CAAA;IAChB,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,MAAM,GAAG,OAAO,CAAA;IAC3B,QAAQ,CAAC,EAAE,IAAI,CAAA;IACf,SAAS,CAAC,EAAE,SAAS,GAAG,OAAO,GAAG,kBAAkB,GAAG,gBAAgB,CAAA;IACvE,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,SAAS,EAAE,MAAM,CAAA;IACjB,cAAc,CAAC,EAAE,OAAO,CAAA;IACxB,YAAY,EAAE,CAAC,QAAQ,EAAE,KAAK,KAAK,IAAI,CAAA;IACvC,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAA;IACrC,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAA;IAC7C,cAAc,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,KAAK,IAAI,CAAA;IACrE,gBAAgB,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,KAAK,IAAI,CAAA;IACvE,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,KAAK,IAAI,CAAA;IAC3C,kBAAkB,CAAC,EAAE,MAAM,IAAI,CAAA;IAC/B,gBAAgB,CAAC,EAAE,MAAM,IAAI,CAAA;IAC7B,aAAa,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,KAAK,IAAI,CAAA;IAC9C,KAAK,EAAE;QACL,cAAc,CAAC,EAAE,KAAK,CAAC,aAAa,CAAA;QACpC,UAAU,CAAC,EAAE,KAAK,CAAC,aAAa,CAAA;QAChC,aAAa,CAAC,EAAE,KAAK,CAAC,aAAa,CAAA;KACpC,CAAA;IACD,OAAO,EAAE;QACP,kBAAkB,CAAC,EAAE,MAAM,CAAA;QAC3B,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,iBAAiB,CAAC,EAAE,MAAM,CAAA;KAC3B,CAAA;IACD,gBAAgB,EAAE,OAAO,CAAA;IACzB,UAAU,EAAE,KAAK,CAAC,iBAAiB,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA;IACzF,YAAY,EAAE,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;IAClD,+BAA+B,CAAC,EAAE,OAAO,CAAA;IACzC,wBAAwB,CAAC,EAAE,IAAI,CAAA;IAC/B,6BAA6B,CAAC,EAAE,IAAI,CAAA;IACpC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,OAAO,CAAA;IACjE,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,KAAK,OAAO,CAAA;IAC3C,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,IAAI,CAAA;IAC9D,WAAW,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAA;IAC9D,WAAW,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAA;IAC9D,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,IAAI,CAAA;IACxC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAA;IAClC,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,YAAY,EAAE,MAAM,CAAA;CACrB,CAAA;AAED,KAAK,KAAK,GAAG;IACX,QAAQ,EAAE,IAAI,GAAG,IAAI,CAAA;IACrB,mBAAmB,EAAE,OAAO,CAAA;IAC5B,cAAc,EAAE,MAAM,GAAG,SAAS,CAAA;CACnC,CAAA;AAMD,KAAK,YAAY,GAAG,OAAO,GAAG;IAC5B,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;CAChB,CAAA;AAED,cAAM,OAAQ,SAAQ,KAAK,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC;IACxD,MAAM,CAAC,YAAY;;;;;;;;;;;;;;;;;MAiBlB;IAED,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,CAAoB;IAC/D,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAoB;IAC/D,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAoB;IAC/D,iBAAiB,EAAE,KAAK,CAAiB;IACzC,YAAY,EAAE,cAAc,GAAG,IAAI,CAAO;IAC1C,QAAQ,EAAE,gBAAgB,GAAG,IAAI,CAAO;IACxC,aAAa,EAAE,OAAO,GAAG,IAAI,CAAO;IACpC,SAAS,EAAE,SAAS,CAA6D;IACjF,iBAAiB,EAAE,KAAK,CAAiB;IACzC,aAAa,EAAE,KAAK,CAAiB;IACrC,gBAAgB,SAAI;IACpB,oBAAoB,SAAI;IACxB,UAAU,UAAQ;IAClB,iBAAiB,SAAI;IACrB,iBAAiB,SAAI;IACrB,cAAc,EAAE,MAAM,GAAG,IAAI,CAAO;IACpC,eAAe,EAAE,MAAM,GAAG,IAAI,CAAO;IACrC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAO;IAChC,UAAU,EAAE,QAAQ,GAAG,IAAI,CAAoD;IAC/E,aAAa,EAAE,MAAM,GAAG,IAAI,CAAgD;IAC5E,cAAc,EAAE,cAAc,GAAG,IAAI,CAAO;IAE5C,KAAK,EAAE,KAAK,CAIX;IAED,iBAAiB;IAmDjB,oBAAoB;IAkBpB,kBAAkB,CAAC,SAAS,EAAE,YAAY;IAoC1C,kBAAkB,aAajB;IAGD,iBAAiB,MAAO,KAAK,UAAuB;IAEpD,WAAW,aASV;IAED,gBAAgB,aAKf;IAED,WAAW,aAWV;IAED,cAAc,aAAc,IAAI,UA0B/B;IAED,SAAS;IAQT,YAAY;IAqBZ,YAAY,yBA8FX;IAED,qBAAqB,aAKpB;IAED,MAAM,CAAC,aAAa,MAAO,UAAU,GAAG,gBAAgB,GAAG,YAAY;;;MAGrE;IAEF,MAAM,CAAC,aAAa,UAAW,KAAK,GAAG,WAAW;;;MAGhD;IAEF,WAAW,MAAO,gBAAgB,CAAC,cAAc,EAAE,UAAU,CAAC,UAO7D;IAED,WAAW,MAAO,UAAU,UAA0C;IAEtE,QAAQ,MAAO,KAAK,UAInB;IAED,YAAY,MAAO,gBAAgB,CAAC,cAAc,CAAC,UAiBlD;IAED,WAAW,MAAO,UAAU,UAQ3B;IAED,cAAc,MAAO,YAAY,UAOhC;IAED,eAAe,MAAO,YAAY,UAcjC;IAED,YAAY,MAAO,YAAY,UAE9B;IAED,WAAW,aAAc,KAAK,UAI7B;IAED,MAAM,aAAc,KAAK,UAyBxB;IAED,aAAa,aAKZ;IAED,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC;IAQhD,WAAW,CAAC,CAAC,EAAE,UAAU;IAqBzB,OAAO,MAAO,UAAU,UAuBvB;IAED,mBAAmB,aAAc,KAAK,oBAAoB,KAAK,KAAG,KAAK,CAQtE;IAED,eAAe,aAAc,KAAK;;;MAMjC;IAED,UAAU,SAAU,MAAM,SAAS,KAAK;;eA0BvC;IAED,WAAW;;;aAwBV;IAED,YAAY,aAYX;IAED,kBAAkB,aAQjB;IAED,qBAAqB,aAepB;IAED,SAAS,UAAW,mBAAmB,CAAC,cAAc,CAAC,UA2CtD;IAED,OAAO,UAAW,mBAAmB,CAAC,cAAc,CAAC,UAapD;IAED,MAAM;CAqGP;AAED,eAAe,OAAO,CAAA"}