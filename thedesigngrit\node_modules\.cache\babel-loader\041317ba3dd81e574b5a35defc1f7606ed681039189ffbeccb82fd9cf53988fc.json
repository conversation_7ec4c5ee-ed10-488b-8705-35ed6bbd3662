{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\signUpForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { Checkbox, Box, Popper, Paper, Typography, ClickAwayListener, useMediaQuery } from \"@mui/material\";\nimport { AiOutlineEye, AiOutlineEyeInvisible } from \"react-icons/ai\";\nimport axios from \"axios\";\nimport { useNavigate } from \"react-router-dom\";\nimport AccountSentPopup from \"./successMsgs/successfullyRegistered\";\nimport { useForm } from \"react-hook-form\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport * as yup from \"yup\";\nimport { useUser } from \"../utils/userContext\";\nimport AccountExistsPopup from \"./successMsgs/accountExists\";\nimport { GoogleLogin } from \"@react-oauth/google\";\nimport { FcGoogle } from \"react-icons/fc\";\n\n// Validation Schema\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst schema = yup.object().shape({\n  email: yup.string().email(\"Invalid email format\").required(\"Email is required\"),\n  firstName: yup.string().required(\"First Name is required\"),\n  lastName: yup.string().required(\"Last Name is required\"),\n  password: yup.string().min(8, \"Password must be at least 8 characters\").matches(/[A-Z]/, \"Must contain at least one uppercase letter\").matches(/\\d/, \"Must contain at least one number\").matches(/[\\W_]/, \"Must contain at least one special character\").required(\"Password is required\"),\n  confirmPassword: yup.string().oneOf([yup.ref(\"password\"), null], \"Passwords must match\").required(\"Confirm Password is required\"),\n  terms: yup.boolean().oneOf([true], \"You must accept the Terms & Privacy Policy\")\n});\nconst SignUpForm = () => {\n  _s();\n  var _errors$email, _errors$password, _errors$confirmPasswo, _errors$firstName, _errors$lastName, _errors$terms;\n  const navigate = useNavigate();\n  const {\n    setUserSession\n  } = useUser();\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isPopupVisible, setIsPopupVisible] = useState(false);\n  const [password, setPassword] = useState(\"\");\n  const [strength, setStrength] = useState(0);\n  const [showRequirements, setShowRequirements] = useState(false);\n  const [emailExistsPopup, setEmailExistsPopup] = useState(false);\n  const passwordFieldRef = useRef(null);\n\n  // Add media query for medium-sized laptops\n  const isMediumLaptop = useMediaQuery(\"(min-width: 1024px) and (max-width: 1440px)\");\n\n  // Add media query for mobile devices\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\n\n  // Password requirements state\n  const [requirements, setRequirements] = useState({\n    length: false,\n    uppercase: false,\n    number: false,\n    special: false\n  });\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    setValue,\n    watch,\n    setError // <== ADD THIS\n  } = useForm({\n    resolver: yupResolver(schema)\n  });\n\n  // Watch the password field to update strength and requirements\n  const watchPassword = watch(\"password\", \"\");\n\n  // Update password strength and requirements whenever password changes\n  useEffect(() => {\n    if (watchPassword) {\n      calculateStrength(watchPassword);\n      checkRequirements(watchPassword);\n\n      // Check if all requirements are met to auto-close the popper\n      setShowRequirements(true); // Always show on change\n    }\n  }, [watchPassword, strength]);\n  const checkRequirements = password => {\n    const newRequirements = {\n      length: password.length >= 8,\n      uppercase: /[A-Z]/.test(password),\n      number: /\\d/.test(password),\n      special: /[\\W_]/.test(password)\n    };\n    setRequirements(newRequirements);\n    return newRequirements; // Return the requirements for immediate use\n  };\n  const calculateStrength = password => {\n    let score = 0;\n    if (password.length >= 8) score += 25;\n    if (/[A-Z]/.test(password)) score += 25;\n    if (/\\d/.test(password)) score += 25;\n    if (/[\\W_]/.test(password)) score += 25;\n    setStrength(score);\n  };\n  const onSubmit = async data => {\n    try {\n      // Register the user\n      const response = await axios.post(\"https://api.thedesigngrit.com/api/signup\", data);\n\n      // If registration is successful, automatically sign in\n      if (response.data.success || response.status === 200 || response.status === 201) {\n        try {\n          // Sign in with the same credentials\n          const signInResponse = await axios.post(\"https://api.thedesigngrit.com/api/signin\", {\n            email: data.email,\n            password: data.password\n          }, {\n            withCredentials: true\n          });\n\n          // Set user session\n          setUserSession(signInResponse.data.user);\n\n          // Show success popup briefly\n          setIsPopupVisible(true);\n\n          // Navigate to home page after a short delay\n          setTimeout(() => {\n            navigate(\"/\");\n          }, 2000);\n        } catch (signInError) {\n          console.error(\"Auto sign-in error:\", signInError);\n          // If auto sign-in fails, still show success message and redirect to login\n          setIsPopupVisible(true);\n          setTimeout(() => {\n            navigate(\"/login\");\n          }, 3000);\n        }\n      } else {\n        console.error(\"Registration failed:\", response.data.message);\n      }\n    } catch (error) {\n      console.error(\"Sign-up error:\", error);\n\n      // If backend returns email in use error\n      if (error.response && error.response.data && error.response.data.message && error.response.data.message.toLowerCase().includes(\"email\")) {\n        setEmailExistsPopup(true);\n        setError(\"email\", {\n          type: \"manual\",\n          message: \"This email is already exists.\"\n        });\n      }\n    }\n  };\n  const closePopup = () => {\n    setIsPopupVisible(false);\n    navigate(\"/login\");\n  };\n  const handlePasswordChange = e => {\n    const newPassword = e.target.value;\n    setPassword(newPassword);\n    setValue(\"password\", newPassword);\n    setShowRequirements(true); // Always show requirements when changing\n  };\n  const handlePasswordFocus = () => {\n    setShowRequirements(true);\n  };\n  const handleClickAway = () => {\n    setShowRequirements(false);\n  };\n\n  // Google Signup handler\n  const handleGoogleSignupSuccess = async credentialResponse => {\n    try {\n      const googleCredential = credentialResponse.credential;\n      const response = await axios.post(\"https://api.thedesigngrit.com/api/signup\", {\n        googleCredential\n      }, {\n        withCredentials: true\n      });\n      // Set user session\n      setUserSession(response.data.user);\n      setIsPopupVisible(true);\n      setTimeout(() => {\n        navigate(\"/\");\n      }, 2000);\n    } catch (error) {\n      // If backend returns email in use error\n      if (error.response && error.response.data && error.response.data.message && error.response.data.message.toLowerCase().includes(\"email\")) {\n        setEmailExistsPopup(true);\n        setError(\"email\", {\n          type: \"manual\",\n          message: \"This email is already exists.\"\n        });\n      }\n    }\n  };\n  const handleGoogleSignupError = () => {\n    setError(\"email\", {\n      type: \"manual\",\n      message: \"Google signup failed. Please try again.\"\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"form-title-signup\",\n      style: isMediumLaptop ? {\n        fontSize: \"1.5rem\",\n        marginBottom: \"10px\"\n      } : {},\n      children: \"Register\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"social-btns-section\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(GoogleLogin, {\n        onSuccess: handleGoogleSignupSuccess,\n        onError: handleGoogleSignupError,\n        useOneTap: false,\n        render: renderProps => /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn social-btn google-btn\",\n          onClick: renderProps.onClick,\n          disabled: renderProps.disabled,\n          style: {\n            width: \"100%\",\n            height: \"40px\",\n            fontFamily: \"Montserrat\",\n            fontSize: \"14px\",\n            fontWeight: \"500\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            gap: \"8px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(FcGoogle, {\n            style: {\n              fontSize: \"20px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this), \"Sign up with Google\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"signup-form\",\n      noValidate: true,\n      style: {\n        maxWidth: isMediumLaptop ? \"450px\" : \"initial\",\n        margin: isMediumLaptop ? \"0 auto\" : \"initial\"\n      },\n      children: [(errors.email || errors.password || errors.confirmPassword || errors.firstName || errors.lastName || errors.terms) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-error-message\",\n        children: [((_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: errors.email.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 39\n        }, this), ((_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: errors.password.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 42\n        }, this), ((_errors$confirmPasswo = errors.confirmPassword) === null || _errors$confirmPasswo === void 0 ? void 0 : _errors$confirmPasswo.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: errors.confirmPassword.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this), ((_errors$firstName = errors.firstName) === null || _errors$firstName === void 0 ? void 0 : _errors$firstName.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: errors.firstName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 43\n        }, this), ((_errors$lastName = errors.lastName) === null || _errors$lastName === void 0 ? void 0 : _errors$lastName.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: errors.lastName.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 42\n        }, this), ((_errors$terms = errors.terms) === null || _errors$terms === void 0 ? void 0 : _errors$terms.message) && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: errors.terms.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 39\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"email\",\n        placeholder: \"E-mail\",\n        className: \"input-field\",\n        ...register(\"email\", {\n          required: \"Email is required\",\n          pattern: {\n            value: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n            message: \"Enter a valid email address\"\n          }\n        }),\n        style: isMediumLaptop ? {\n          marginBottom: \"10px\",\n          padding: \"8px 12px\"\n        } : {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"First Name\",\n        className: \"input-field\",\n        ...register(\"firstName\"),\n        style: isMediumLaptop ? {\n          marginBottom: \"10px\",\n          padding: \"8px 12px\"\n        } : {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Last Name\",\n        className: \"input-field\",\n        ...register(\"lastName\"),\n        style: isMediumLaptop ? {\n          marginBottom: \"10px\",\n          padding: \"8px 12px\"\n        } : {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ClickAwayListener, {\n        onClickAway: handleClickAway,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ref: passwordFieldRef,\n            type: showPassword ? \"text\" : \"password\",\n            placeholder: \"Password\",\n            className: \"input-field\",\n            value: password,\n            onChange: handlePasswordChange,\n            onFocus: handlePasswordFocus,\n            style: isMediumLaptop ? {\n              marginBottom: \"10px\",\n              padding: \"8px 12px\"\n            } : {}\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            onClick: () => setShowPassword(prev => !prev),\n            style: {\n              position: \"absolute\",\n              right: \"10px\",\n              top: \"50%\",\n              transform: \"translate(-50%,-50%)\",\n              cursor: \"pointer\"\n            },\n            children: showPassword ? /*#__PURE__*/_jsxDEV(AiOutlineEyeInvisible, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(AiOutlineEye, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Popper, {\n            open: showRequirements,\n            anchorEl: passwordFieldRef.current,\n            placement: isMobile ? \"bottom-start\" : \"right-start\",\n            style: {\n              zIndex: 1000\n            },\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 3,\n              sx: {\n                p: 2,\n                ...(isMobile ? {\n                  mt: 1,\n                  position: \"relative\",\n                  \"&::before\": {\n                    content: '\"\"',\n                    position: \"absolute\",\n                    top: -10,\n                    left: 20,\n                    borderWidth: \"0 10px 10px 10px\",\n                    borderStyle: \"solid\",\n                    borderColor: \"transparent transparent #fff transparent\"\n                  }\n                } : {\n                  ml: 1,\n                  position: \"relative\",\n                  \"&::before\": {\n                    content: '\"\"',\n                    position: \"absolute\",\n                    top: 20,\n                    left: -10,\n                    borderWidth: \"10px 10px 10px 0\",\n                    borderStyle: \"solid\",\n                    borderColor: \"transparent #fff transparent transparent\"\n                  }\n                })\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: \"bold\",\n                  mb: 1\n                },\n                children: \"Password Requirements:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  margin: 0,\n                  paddingLeft: 20\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    color: requirements.length ? \"green\" : \"red\",\n                    marginBottom: \"4px\"\n                  },\n                  children: \"At least 8 characters\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    color: requirements.uppercase ? \"green\" : \"red\",\n                    marginBottom: \"4px\"\n                  },\n                  children: \"At least one uppercase letter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    color: requirements.number ? \"green\" : \"red\",\n                    marginBottom: \"4px\"\n                  },\n                  children: \"At least one number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    color: requirements.special ? \"green\" : \"red\"\n                  },\n                  children: \"At least one special character\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"relative\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: showConfirmPassword ? \"text\" : \"password\",\n          placeholder: \"Confirm Password\",\n          className: \"input-field\",\n          ...register(\"confirmPassword\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => setShowConfirmPassword(prev => !prev),\n          style: {\n            position: \"absolute\",\n            right: \"10px\",\n            top: \"50%\",\n            transform: \"translate(-50%,-50%)\",\n            cursor: \"pointer\"\n          },\n          children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(AiOutlineEyeInvisible, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(AiOutlineEye, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register-policy-container\",\n        children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n          ...register(\"terms\"),\n          sx: {\n            color: \"#efebe8\",\n            \"&.Mui-checked\": {\n              color: \"#efebe8\"\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"register-policy\",\n          children: [\"I have read and accept the\", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/policy\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"Terms of use\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), \" \", \"and\", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/policy\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"btn signin-btn\",\n        children: \"Sign Up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"register-link\",\n        children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/Login\",\n          children: \"Log In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 36\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AccountSentPopup, {\n      show: isPopupVisible,\n      closePopup: closePopup\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AccountExistsPopup, {\n      show: emailExistsPopup,\n      closePopup: () => {\n        setEmailExistsPopup(false);\n        navigate(\"/signup\");\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n_s(SignUpForm, \"c3l5IJ4VbnZyzBemRA7tmwj6YBY=\", false, function () {\n  return [useNavigate, useUser, useMediaQuery, useMediaQuery, useForm];\n});\n_c = SignUpForm;\nexport default SignUpForm;\nvar _c;\n$RefreshReg$(_c, \"SignUpForm\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Checkbox", "Box", "<PERSON><PERSON>", "Paper", "Typography", "ClickAwayListener", "useMediaQuery", "AiOutlineEye", "AiOutlineEyeInvisible", "axios", "useNavigate", "AccountSentPopup", "useForm", "yupResolver", "yup", "useUser", "AccountExistsPopup", "GoogleLogin", "FcGoogle", "jsxDEV", "_jsxDEV", "schema", "object", "shape", "email", "string", "required", "firstName", "lastName", "password", "min", "matches", "confirmPassword", "oneOf", "ref", "terms", "boolean", "SignUpForm", "_s", "_errors$email", "_errors$password", "_errors$confirmPasswo", "_errors$firstName", "_errors$lastName", "_errors$terms", "navigate", "setUserSession", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "isPopupVisible", "setIsPopupVisible", "setPassword", "strength", "setStrength", "showRequirements", "setShowRequirements", "emailExistsPopup", "setEmailExistsPopup", "passwordFieldRef", "isMediumLaptop", "isMobile", "requirements", "setRequirements", "length", "uppercase", "number", "special", "register", "handleSubmit", "formState", "errors", "setValue", "watch", "setError", "resolver", "watchPassword", "calculateStrength", "checkRequirements", "newRequirements", "test", "score", "onSubmit", "data", "response", "post", "success", "status", "signInResponse", "withCredentials", "user", "setTimeout", "signInError", "console", "error", "message", "toLowerCase", "includes", "type", "closePopup", "handlePasswordChange", "e", "newPassword", "target", "value", "handlePasswordFocus", "handleClickAway", "handleGoogleSignupSuccess", "credentialResponse", "googleCredential", "credential", "handleGoogleSignupError", "children", "className", "style", "fontSize", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSuccess", "onError", "useOneTap", "render", "renderProps", "onClick", "disabled", "width", "height", "fontFamily", "fontWeight", "display", "alignItems", "justifyContent", "gap", "noValidate", "max<PERSON><PERSON><PERSON>", "margin", "placeholder", "pattern", "padding", "onClickAway", "position", "onChange", "onFocus", "prev", "right", "top", "transform", "cursor", "open", "anchorEl", "current", "placement", "zIndex", "elevation", "sx", "p", "mt", "content", "left", "borderWidth", "borderStyle", "borderColor", "ml", "variant", "mb", "paddingLeft", "color", "href", "rel", "show", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/signUpForm.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport {\r\n  Checkbox,\r\n  Box,\r\n  Popper,\r\n  Paper,\r\n  Typography,\r\n  ClickAwayListener,\r\n  useMediaQuery,\r\n} from \"@mui/material\";\r\nimport { AiOutlineEye, AiOutlineEyeInvisible } from \"react-icons/ai\";\r\nimport axios from \"axios\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AccountSentPopup from \"./successMsgs/successfullyRegistered\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { yupResolver } from \"@hookform/resolvers/yup\";\r\nimport * as yup from \"yup\";\r\nimport { useUser } from \"../utils/userContext\";\r\nimport AccountExistsPopup from \"./successMsgs/accountExists\";\r\nimport { GoogleLogin } from \"@react-oauth/google\";\r\nimport { FcGoogle } from \"react-icons/fc\";\r\n\r\n// Validation Schema\r\nconst schema = yup.object().shape({\r\n  email: yup\r\n    .string()\r\n    .email(\"Invalid email format\")\r\n    .required(\"Email is required\"),\r\n  firstName: yup.string().required(\"First Name is required\"),\r\n  lastName: yup.string().required(\"Last Name is required\"),\r\n  password: yup\r\n    .string()\r\n    .min(8, \"Password must be at least 8 characters\")\r\n    .matches(/[A-Z]/, \"Must contain at least one uppercase letter\")\r\n    .matches(/\\d/, \"Must contain at least one number\")\r\n    .matches(/[\\W_]/, \"Must contain at least one special character\")\r\n    .required(\"Password is required\"),\r\n  confirmPassword: yup\r\n    .string()\r\n    .oneOf([yup.ref(\"password\"), null], \"Passwords must match\")\r\n    .required(\"Confirm Password is required\"),\r\n  terms: yup\r\n    .boolean()\r\n    .oneOf([true], \"You must accept the Terms & Privacy Policy\"),\r\n});\r\n\r\nconst SignUpForm = () => {\r\n  const navigate = useNavigate();\r\n  const { setUserSession } = useUser();\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [isPopupVisible, setIsPopupVisible] = useState(false);\r\n  const [password, setPassword] = useState(\"\");\r\n  const [strength, setStrength] = useState(0);\r\n  const [showRequirements, setShowRequirements] = useState(false);\r\n  const [emailExistsPopup, setEmailExistsPopup] = useState(false);\r\n\r\n  const passwordFieldRef = useRef(null);\r\n\r\n  // Add media query for medium-sized laptops\r\n  const isMediumLaptop = useMediaQuery(\r\n    \"(min-width: 1024px) and (max-width: 1440px)\"\r\n  );\r\n\r\n  // Add media query for mobile devices\r\n  const isMobile = useMediaQuery(\"(max-width:768px)\");\r\n\r\n  // Password requirements state\r\n  const [requirements, setRequirements] = useState({\r\n    length: false,\r\n    uppercase: false,\r\n    number: false,\r\n    special: false,\r\n  });\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    setValue,\r\n    watch,\r\n    setError, // <== ADD THIS\r\n  } = useForm({\r\n    resolver: yupResolver(schema),\r\n  });\r\n\r\n  // Watch the password field to update strength and requirements\r\n  const watchPassword = watch(\"password\", \"\");\r\n\r\n  // Update password strength and requirements whenever password changes\r\n  useEffect(() => {\r\n    if (watchPassword) {\r\n      calculateStrength(watchPassword);\r\n      checkRequirements(watchPassword);\r\n\r\n      // Check if all requirements are met to auto-close the popper\r\n      setShowRequirements(true); // Always show on change\r\n    }\r\n  }, [watchPassword, strength]);\r\n\r\n  const checkRequirements = (password) => {\r\n    const newRequirements = {\r\n      length: password.length >= 8,\r\n      uppercase: /[A-Z]/.test(password),\r\n      number: /\\d/.test(password),\r\n      special: /[\\W_]/.test(password),\r\n    };\r\n\r\n    setRequirements(newRequirements);\r\n    return newRequirements; // Return the requirements for immediate use\r\n  };\r\n\r\n  const calculateStrength = (password) => {\r\n    let score = 0;\r\n    if (password.length >= 8) score += 25;\r\n    if (/[A-Z]/.test(password)) score += 25;\r\n    if (/\\d/.test(password)) score += 25;\r\n    if (/[\\W_]/.test(password)) score += 25;\r\n    setStrength(score);\r\n  };\r\n  const onSubmit = async (data) => {\r\n    try {\r\n      // Register the user\r\n      const response = await axios.post(\r\n        \"https://api.thedesigngrit.com/api/signup\",\r\n        data\r\n      );\r\n\r\n      // If registration is successful, automatically sign in\r\n      if (\r\n        response.data.success ||\r\n        response.status === 200 ||\r\n        response.status === 201\r\n      ) {\r\n        try {\r\n          // Sign in with the same credentials\r\n          const signInResponse = await axios.post(\r\n            \"https://api.thedesigngrit.com/api/signin\",\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n            },\r\n            { withCredentials: true }\r\n          );\r\n\r\n          // Set user session\r\n          setUserSession(signInResponse.data.user);\r\n\r\n          // Show success popup briefly\r\n          setIsPopupVisible(true);\r\n\r\n          // Navigate to home page after a short delay\r\n          setTimeout(() => {\r\n            navigate(\"/\");\r\n          }, 2000);\r\n        } catch (signInError) {\r\n          console.error(\"Auto sign-in error:\", signInError);\r\n          // If auto sign-in fails, still show success message and redirect to login\r\n          setIsPopupVisible(true);\r\n          setTimeout(() => {\r\n            navigate(\"/login\");\r\n          }, 3000);\r\n        }\r\n      } else {\r\n        console.error(\"Registration failed:\", response.data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Sign-up error:\", error);\r\n\r\n      // If backend returns email in use error\r\n      if (\r\n        error.response &&\r\n        error.response.data &&\r\n        error.response.data.message &&\r\n        error.response.data.message.toLowerCase().includes(\"email\")\r\n      ) {\r\n        setEmailExistsPopup(true);\r\n\r\n        setError(\"email\", {\r\n          type: \"manual\",\r\n          message: \"This email is already exists.\",\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const closePopup = () => {\r\n    setIsPopupVisible(false);\r\n    navigate(\"/login\");\r\n  };\r\n\r\n  const handlePasswordChange = (e) => {\r\n    const newPassword = e.target.value;\r\n    setPassword(newPassword);\r\n    setValue(\"password\", newPassword);\r\n    setShowRequirements(true); // Always show requirements when changing\r\n  };\r\n\r\n  const handlePasswordFocus = () => {\r\n    setShowRequirements(true);\r\n  };\r\n\r\n  const handleClickAway = () => {\r\n    setShowRequirements(false);\r\n  };\r\n\r\n  // Google Signup handler\r\n  const handleGoogleSignupSuccess = async (credentialResponse) => {\r\n    try {\r\n      const googleCredential = credentialResponse.credential;\r\n      const response = await axios.post(\r\n        \"https://api.thedesigngrit.com/api/signup\",\r\n        { googleCredential },\r\n        { withCredentials: true }\r\n      );\r\n      // Set user session\r\n      setUserSession(response.data.user);\r\n      setIsPopupVisible(true);\r\n      setTimeout(() => {\r\n        navigate(\"/\");\r\n      }, 2000);\r\n    } catch (error) {\r\n      // If backend returns email in use error\r\n      if (\r\n        error.response &&\r\n        error.response.data &&\r\n        error.response.data.message &&\r\n        error.response.data.message.toLowerCase().includes(\"email\")\r\n      ) {\r\n        setEmailExistsPopup(true);\r\n        setError(\"email\", {\r\n          type: \"manual\",\r\n          message: \"This email is already exists.\",\r\n        });\r\n      }\r\n    }\r\n  };\r\n  const handleGoogleSignupError = () => {\r\n    setError(\"email\", {\r\n      type: \"manual\",\r\n      message: \"Google signup failed. Please try again.\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <h1\r\n        className=\"form-title-signup\"\r\n        style={\r\n          isMediumLaptop ? { fontSize: \"1.5rem\", marginBottom: \"10px\" } : {}\r\n        }\r\n      >\r\n        Register\r\n      </h1>\r\n      {/* Google Signup Button */}\r\n      <div className=\"social-btns-section\" style={{ marginBottom: 16 }}>\r\n        <GoogleLogin\r\n          onSuccess={handleGoogleSignupSuccess}\r\n          onError={handleGoogleSignupError}\r\n          useOneTap={false}\r\n          render={(renderProps) => (\r\n            <button\r\n              type=\"button\"\r\n              className=\"btn social-btn google-btn\"\r\n              onClick={renderProps.onClick}\r\n              disabled={renderProps.disabled}\r\n              style={{\r\n                width: \"100%\",\r\n                height: \"40px\",\r\n                fontFamily: \"Montserrat\",\r\n                fontSize: \"14px\",\r\n                fontWeight: \"500\",\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\",\r\n                gap: \"8px\",\r\n              }}\r\n            >\r\n              <FcGoogle style={{ fontSize: \"20px\" }} />\r\n              Sign up with Google\r\n            </button>\r\n          )}\r\n        />\r\n      </div>\r\n      <form\r\n        onSubmit={handleSubmit(onSubmit)}\r\n        className=\"signup-form\"\r\n        noValidate\r\n        style={{\r\n          maxWidth: isMediumLaptop ? \"450px\" : \"initial\",\r\n          margin: isMediumLaptop ? \"0 auto\" : \"initial\",\r\n        }}\r\n      >\r\n        {(errors.email ||\r\n          errors.password ||\r\n          errors.confirmPassword ||\r\n          errors.firstName ||\r\n          errors.lastName ||\r\n          errors.terms) && (\r\n          <div className=\"login-error-message\">\r\n            {errors.email?.message && <div>{errors.email.message}</div>}\r\n            {errors.password?.message && <div>{errors.password.message}</div>}\r\n            {errors.confirmPassword?.message && (\r\n              <div>{errors.confirmPassword.message}</div>\r\n            )}\r\n            {errors.firstName?.message && <div>{errors.firstName.message}</div>}\r\n            {errors.lastName?.message && <div>{errors.lastName.message}</div>}\r\n            {errors.terms?.message && <div>{errors.terms.message}</div>}\r\n          </div>\r\n        )}\r\n        <input\r\n          type=\"email\"\r\n          placeholder=\"E-mail\"\r\n          className=\"input-field\"\r\n          {...register(\"email\", {\r\n            required: \"Email is required\",\r\n            pattern: {\r\n              value: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n              message: \"Enter a valid email address\",\r\n            },\r\n          })}\r\n          style={\r\n            isMediumLaptop ? { marginBottom: \"10px\", padding: \"8px 12px\" } : {}\r\n          }\r\n        />\r\n        {/* {errors.email && (\r\n          <p\r\n            className=\"error-message\"\r\n            style={\r\n              isMediumLaptop\r\n                ? { marginTop: \"-8px\", marginBottom: \"8px\", fontSize: \"0.7rem\" }\r\n                : {}\r\n            }\r\n          >\r\n            {errors.email.message}\r\n          </p>\r\n        )} */}\r\n\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"First Name\"\r\n          className=\"input-field\"\r\n          {...register(\"firstName\")}\r\n          style={\r\n            isMediumLaptop ? { marginBottom: \"10px\", padding: \"8px 12px\" } : {}\r\n          }\r\n        />\r\n        {/* {errors.firstName && (\r\n          <p\r\n            className=\"error-message\"\r\n            style={\r\n              isMediumLaptop\r\n                ? { marginTop: \"-8px\", marginBottom: \"8px\", fontSize: \"0.7rem\" }\r\n                : {}\r\n            }\r\n          >\r\n            {errors.firstName.message}\r\n          </p>\r\n        )} */}\r\n\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Last Name\"\r\n          className=\"input-field\"\r\n          {...register(\"lastName\")}\r\n          style={\r\n            isMediumLaptop ? { marginBottom: \"10px\", padding: \"8px 12px\" } : {}\r\n          }\r\n        />\r\n        {/* {errors.lastName && (\r\n          <p\r\n            className=\"error-message\"\r\n            style={\r\n              isMediumLaptop\r\n                ? { marginTop: \"-8px\", marginBottom: \"8px\", fontSize: \"0.7rem\" }\r\n                : {}\r\n            }\r\n          >\r\n            {errors.lastName.message}\r\n          </p>\r\n        )} */}\r\n\r\n        {/* Password Field */}\r\n        <ClickAwayListener onClickAway={handleClickAway}>\r\n          <div style={{ position: \"relative\" }}>\r\n            <input\r\n              ref={passwordFieldRef}\r\n              type={showPassword ? \"text\" : \"password\"}\r\n              placeholder=\"Password\"\r\n              className=\"input-field\"\r\n              value={password}\r\n              onChange={handlePasswordChange}\r\n              onFocus={handlePasswordFocus}\r\n              style={\r\n                isMediumLaptop\r\n                  ? { marginBottom: \"10px\", padding: \"8px 12px\" }\r\n                  : {}\r\n              }\r\n            />\r\n            <span\r\n              onClick={() => setShowPassword((prev) => !prev)}\r\n              style={{\r\n                position: \"absolute\",\r\n                right: \"10px\",\r\n                top: \"50%\",\r\n                transform: \"translate(-50%,-50%)\",\r\n                cursor: \"pointer\",\r\n              }}\r\n            >\r\n              {showPassword ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}\r\n            </span>\r\n\r\n            {/* Password requirements popup */}\r\n            <Popper\r\n              open={showRequirements}\r\n              anchorEl={passwordFieldRef.current}\r\n              placement={isMobile ? \"bottom-start\" : \"right-start\"}\r\n              style={{ zIndex: 1000 }}\r\n            >\r\n              <Paper\r\n                elevation={3}\r\n                sx={{\r\n                  p: 2,\r\n                  ...(isMobile\r\n                    ? {\r\n                        mt: 1,\r\n                        position: \"relative\",\r\n                        \"&::before\": {\r\n                          content: '\"\"',\r\n                          position: \"absolute\",\r\n                          top: -10,\r\n                          left: 20,\r\n                          borderWidth: \"0 10px 10px 10px\",\r\n                          borderStyle: \"solid\",\r\n                          borderColor:\r\n                            \"transparent transparent #fff transparent\",\r\n                        },\r\n                      }\r\n                    : {\r\n                        ml: 1,\r\n                        position: \"relative\",\r\n                        \"&::before\": {\r\n                          content: '\"\"',\r\n                          position: \"absolute\",\r\n                          top: 20,\r\n                          left: -10,\r\n                          borderWidth: \"10px 10px 10px 0\",\r\n                          borderStyle: \"solid\",\r\n                          borderColor:\r\n                            \"transparent #fff transparent transparent\",\r\n                        },\r\n                      }),\r\n                }}\r\n              >\r\n                <Typography\r\n                  variant=\"subtitle2\"\r\n                  sx={{ fontWeight: \"bold\", mb: 1 }}\r\n                >\r\n                  Password Requirements:\r\n                </Typography>\r\n                <ul style={{ margin: 0, paddingLeft: 20 }}>\r\n                  <li\r\n                    style={{\r\n                      color: requirements.length ? \"green\" : \"red\",\r\n                      marginBottom: \"4px\",\r\n                    }}\r\n                  >\r\n                    At least 8 characters\r\n                  </li>\r\n                  <li\r\n                    style={{\r\n                      color: requirements.uppercase ? \"green\" : \"red\",\r\n                      marginBottom: \"4px\",\r\n                    }}\r\n                  >\r\n                    At least one uppercase letter\r\n                  </li>\r\n                  <li\r\n                    style={{\r\n                      color: requirements.number ? \"green\" : \"red\",\r\n                      marginBottom: \"4px\",\r\n                    }}\r\n                  >\r\n                    At least one number\r\n                  </li>\r\n                  <li style={{ color: requirements.special ? \"green\" : \"red\" }}>\r\n                    At least one special character\r\n                  </li>\r\n                </ul>\r\n              </Paper>\r\n            </Popper>\r\n          </div>\r\n        </ClickAwayListener>\r\n\r\n        {/* {errors.password && (\r\n          <p className=\"error-message\">{errors.password.message}</p>\r\n        )} */}\r\n\r\n        {/* Confirm Password Field */}\r\n        <div style={{ position: \"relative\" }}>\r\n          <input\r\n            type={showConfirmPassword ? \"text\" : \"password\"}\r\n            placeholder=\"Confirm Password\"\r\n            className=\"input-field\"\r\n            {...register(\"confirmPassword\")}\r\n          />\r\n          <span\r\n            onClick={() => setShowConfirmPassword((prev) => !prev)}\r\n            style={{\r\n              position: \"absolute\",\r\n              right: \"10px\",\r\n              top: \"50%\",\r\n              transform: \"translate(-50%,-50%)\",\r\n              cursor: \"pointer\",\r\n            }}\r\n          >\r\n            {showConfirmPassword ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}\r\n          </span>\r\n        </div>\r\n        {/* {errors.confirmPassword && (\r\n          <p className=\"error-message\">{errors.confirmPassword.message}</p>\r\n        )} */}\r\n\r\n        <div className=\"register-policy-container\">\r\n          <Checkbox\r\n            {...register(\"terms\")}\r\n            sx={{ color: \"#efebe8\", \"&.Mui-checked\": { color: \"#efebe8\" } }}\r\n          />\r\n          <p className=\"register-policy\">\r\n            I have read and accept the{\" \"}\r\n            <a href=\"/policy\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n              Terms of use\r\n            </a>{\" \"}\r\n            and{\" \"}\r\n            <a href=\"/policy\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n              Privacy Policy\r\n            </a>\r\n            .\r\n          </p>\r\n        </div>\r\n        {/* {errors.terms && (\r\n          <p className=\"error-message\">{errors.terms.message}</p>\r\n        )} */}\r\n\r\n        <button type=\"submit\" className=\"btn signin-btn\">\r\n          Sign Up\r\n        </button>\r\n        <p className=\"register-link\">\r\n          Already have an account? <a href=\"/Login\">Log In</a>\r\n        </p>\r\n      </form>\r\n      <AccountSentPopup show={isPopupVisible} closePopup={closePopup} />\r\n      <AccountExistsPopup\r\n        show={emailExistsPopup}\r\n        closePopup={() => {\r\n          setEmailExistsPopup(false);\r\n          navigate(\"/signup\");\r\n        }}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default SignUpForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,iBAAiB,EACjBC,aAAa,QACR,eAAe;AACtB,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,gBAAgB;AACpE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,QAAQ,QAAQ,gBAAgB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAGP,GAAG,CAACQ,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAChCC,KAAK,EAAEV,GAAG,CACPW,MAAM,CAAC,CAAC,CACRD,KAAK,CAAC,sBAAsB,CAAC,CAC7BE,QAAQ,CAAC,mBAAmB,CAAC;EAChCC,SAAS,EAAEb,GAAG,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,wBAAwB,CAAC;EAC1DE,QAAQ,EAAEd,GAAG,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,uBAAuB,CAAC;EACxDG,QAAQ,EAAEf,GAAG,CACVW,MAAM,CAAC,CAAC,CACRK,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,OAAO,CAAC,OAAO,EAAE,4CAA4C,CAAC,CAC9DA,OAAO,CAAC,IAAI,EAAE,kCAAkC,CAAC,CACjDA,OAAO,CAAC,OAAO,EAAE,6CAA6C,CAAC,CAC/DL,QAAQ,CAAC,sBAAsB,CAAC;EACnCM,eAAe,EAAElB,GAAG,CACjBW,MAAM,CAAC,CAAC,CACRQ,KAAK,CAAC,CAACnB,GAAG,CAACoB,GAAG,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,CAC1DR,QAAQ,CAAC,8BAA8B,CAAC;EAC3CS,KAAK,EAAErB,GAAG,CACPsB,OAAO,CAAC,CAAC,CACTH,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,4CAA4C;AAC/D,CAAC,CAAC;AAEF,MAAMI,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,aAAA;EACvB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC;EAAe,CAAC,GAAG/B,OAAO,CAAC,CAAC;EACpC,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgC,QAAQ,EAAEwB,WAAW,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM+D,gBAAgB,GAAG9D,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM+D,cAAc,GAAGvD,aAAa,CAClC,6CACF,CAAC;;EAED;EACA,MAAMwD,QAAQ,GAAGxD,aAAa,CAAC,mBAAmB,CAAC;;EAEnD;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC;IAC/CoE,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM;IACJC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC,QAAQ;IACRC,KAAK;IACLC,QAAQ,CAAE;EACZ,CAAC,GAAG/D,OAAO,CAAC;IACVgE,QAAQ,EAAE/D,WAAW,CAACQ,MAAM;EAC9B,CAAC,CAAC;;EAEF;EACA,MAAMwD,aAAa,GAAGH,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC;;EAE3C;EACA3E,SAAS,CAAC,MAAM;IACd,IAAI8E,aAAa,EAAE;MACjBC,iBAAiB,CAACD,aAAa,CAAC;MAChCE,iBAAiB,CAACF,aAAa,CAAC;;MAEhC;MACApB,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACoB,aAAa,EAAEvB,QAAQ,CAAC,CAAC;EAE7B,MAAMyB,iBAAiB,GAAIlD,QAAQ,IAAK;IACtC,MAAMmD,eAAe,GAAG;MACtBf,MAAM,EAAEpC,QAAQ,CAACoC,MAAM,IAAI,CAAC;MAC5BC,SAAS,EAAE,OAAO,CAACe,IAAI,CAACpD,QAAQ,CAAC;MACjCsC,MAAM,EAAE,IAAI,CAACc,IAAI,CAACpD,QAAQ,CAAC;MAC3BuC,OAAO,EAAE,OAAO,CAACa,IAAI,CAACpD,QAAQ;IAChC,CAAC;IAEDmC,eAAe,CAACgB,eAAe,CAAC;IAChC,OAAOA,eAAe,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMF,iBAAiB,GAAIjD,QAAQ,IAAK;IACtC,IAAIqD,KAAK,GAAG,CAAC;IACb,IAAIrD,QAAQ,CAACoC,MAAM,IAAI,CAAC,EAAEiB,KAAK,IAAI,EAAE;IACrC,IAAI,OAAO,CAACD,IAAI,CAACpD,QAAQ,CAAC,EAAEqD,KAAK,IAAI,EAAE;IACvC,IAAI,IAAI,CAACD,IAAI,CAACpD,QAAQ,CAAC,EAAEqD,KAAK,IAAI,EAAE;IACpC,IAAI,OAAO,CAACD,IAAI,CAACpD,QAAQ,CAAC,EAAEqD,KAAK,IAAI,EAAE;IACvC3B,WAAW,CAAC2B,KAAK,CAAC;EACpB,CAAC;EACD,MAAMC,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAM5E,KAAK,CAAC6E,IAAI,CAC/B,0CAA0C,EAC1CF,IACF,CAAC;;MAED;MACA,IACEC,QAAQ,CAACD,IAAI,CAACG,OAAO,IACrBF,QAAQ,CAACG,MAAM,KAAK,GAAG,IACvBH,QAAQ,CAACG,MAAM,KAAK,GAAG,EACvB;QACA,IAAI;UACF;UACA,MAAMC,cAAc,GAAG,MAAMhF,KAAK,CAAC6E,IAAI,CACrC,0CAA0C,EAC1C;YACE9D,KAAK,EAAE4D,IAAI,CAAC5D,KAAK;YACjBK,QAAQ,EAAEuD,IAAI,CAACvD;UACjB,CAAC,EACD;YAAE6D,eAAe,EAAE;UAAK,CAC1B,CAAC;;UAED;UACA5C,cAAc,CAAC2C,cAAc,CAACL,IAAI,CAACO,IAAI,CAAC;;UAExC;UACAvC,iBAAiB,CAAC,IAAI,CAAC;;UAEvB;UACAwC,UAAU,CAAC,MAAM;YACf/C,QAAQ,CAAC,GAAG,CAAC;UACf,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,CAAC,OAAOgD,WAAW,EAAE;UACpBC,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEF,WAAW,CAAC;UACjD;UACAzC,iBAAiB,CAAC,IAAI,CAAC;UACvBwC,UAAU,CAAC,MAAM;YACf/C,QAAQ,CAAC,QAAQ,CAAC;UACpB,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,MAAM;QACLiD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEV,QAAQ,CAACD,IAAI,CAACY,OAAO,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;;MAEtC;MACA,IACEA,KAAK,CAACV,QAAQ,IACdU,KAAK,CAACV,QAAQ,CAACD,IAAI,IACnBW,KAAK,CAACV,QAAQ,CAACD,IAAI,CAACY,OAAO,IAC3BD,KAAK,CAACV,QAAQ,CAACD,IAAI,CAACY,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAC3D;QACAvC,mBAAmB,CAAC,IAAI,CAAC;QAEzBgB,QAAQ,CAAC,OAAO,EAAE;UAChBwB,IAAI,EAAE,QAAQ;UACdH,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACvBhD,iBAAiB,CAAC,KAAK,CAAC;IACxBP,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMwD,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,WAAW,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IAClCpD,WAAW,CAACkD,WAAW,CAAC;IACxB9B,QAAQ,CAAC,UAAU,EAAE8B,WAAW,CAAC;IACjC9C,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC7B,CAAC;EAED,MAAMiD,mBAAmB,GAAGA,CAAA,KAAM;IAChCjD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5BlD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmD,yBAAyB,GAAG,MAAOC,kBAAkB,IAAK;IAC9D,IAAI;MACF,MAAMC,gBAAgB,GAAGD,kBAAkB,CAACE,UAAU;MACtD,MAAM1B,QAAQ,GAAG,MAAM5E,KAAK,CAAC6E,IAAI,CAC/B,0CAA0C,EAC1C;QAAEwB;MAAiB,CAAC,EACpB;QAAEpB,eAAe,EAAE;MAAK,CAC1B,CAAC;MACD;MACA5C,cAAc,CAACuC,QAAQ,CAACD,IAAI,CAACO,IAAI,CAAC;MAClCvC,iBAAiB,CAAC,IAAI,CAAC;MACvBwC,UAAU,CAAC,MAAM;QACf/C,QAAQ,CAAC,GAAG,CAAC;MACf,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACd;MACA,IACEA,KAAK,CAACV,QAAQ,IACdU,KAAK,CAACV,QAAQ,CAACD,IAAI,IACnBW,KAAK,CAACV,QAAQ,CAACD,IAAI,CAACY,OAAO,IAC3BD,KAAK,CAACV,QAAQ,CAACD,IAAI,CAACY,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAC3D;QACAvC,mBAAmB,CAAC,IAAI,CAAC;QACzBgB,QAAQ,CAAC,OAAO,EAAE;UAChBwB,IAAI,EAAE,QAAQ;UACdH,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD,MAAMgB,uBAAuB,GAAGA,CAAA,KAAM;IACpCrC,QAAQ,CAAC,OAAO,EAAE;MAChBwB,IAAI,EAAE,QAAQ;MACdH,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,oBACE5E,OAAA,CAACnB,GAAG;IAAAgH,QAAA,gBACF7F,OAAA;MACE8F,SAAS,EAAC,mBAAmB;MAC7BC,KAAK,EACHtD,cAAc,GAAG;QAAEuD,QAAQ,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAC,GAAG,CAAC,CAClE;MAAAJ,QAAA,EACF;IAED;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELrG,OAAA;MAAK8F,SAAS,EAAC,qBAAqB;MAACC,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAG,CAAE;MAAAJ,QAAA,eAC/D7F,OAAA,CAACH,WAAW;QACVyG,SAAS,EAAEd,yBAA0B;QACrCe,OAAO,EAAEX,uBAAwB;QACjCY,SAAS,EAAE,KAAM;QACjBC,MAAM,EAAGC,WAAW,iBAClB1G,OAAA;UACE+E,IAAI,EAAC,QAAQ;UACbe,SAAS,EAAC,2BAA2B;UACrCa,OAAO,EAAED,WAAW,CAACC,OAAQ;UAC7BC,QAAQ,EAAEF,WAAW,CAACE,QAAS;UAC/Bb,KAAK,EAAE;YACLc,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,YAAY;YACxBf,QAAQ,EAAE,MAAM;YAChBgB,UAAU,EAAE,KAAK;YACjBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,GAAG,EAAE;UACP,CAAE;UAAAvB,QAAA,gBAEF7F,OAAA,CAACF,QAAQ;YAACiG,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNrG,OAAA;MACE+D,QAAQ,EAAEb,YAAY,CAACa,QAAQ,CAAE;MACjC+B,SAAS,EAAC,aAAa;MACvBuB,UAAU;MACVtB,KAAK,EAAE;QACLuB,QAAQ,EAAE7E,cAAc,GAAG,OAAO,GAAG,SAAS;QAC9C8E,MAAM,EAAE9E,cAAc,GAAG,QAAQ,GAAG;MACtC,CAAE;MAAAoD,QAAA,GAED,CAACzC,MAAM,CAAChD,KAAK,IACZgD,MAAM,CAAC3C,QAAQ,IACf2C,MAAM,CAACxC,eAAe,IACtBwC,MAAM,CAAC7C,SAAS,IAChB6C,MAAM,CAAC5C,QAAQ,IACf4C,MAAM,CAACrC,KAAK,kBACZf,OAAA;QAAK8F,SAAS,EAAC,qBAAqB;QAAAD,QAAA,GACjC,EAAA1E,aAAA,GAAAiC,MAAM,CAAChD,KAAK,cAAAe,aAAA,uBAAZA,aAAA,CAAcyD,OAAO,kBAAI5E,OAAA;UAAA6F,QAAA,EAAMzC,MAAM,CAAChD,KAAK,CAACwE;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC1D,EAAAjF,gBAAA,GAAAgC,MAAM,CAAC3C,QAAQ,cAAAW,gBAAA,uBAAfA,gBAAA,CAAiBwD,OAAO,kBAAI5E,OAAA;UAAA6F,QAAA,EAAMzC,MAAM,CAAC3C,QAAQ,CAACmE;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAChE,EAAAhF,qBAAA,GAAA+B,MAAM,CAACxC,eAAe,cAAAS,qBAAA,uBAAtBA,qBAAA,CAAwBuD,OAAO,kBAC9B5E,OAAA;UAAA6F,QAAA,EAAMzC,MAAM,CAACxC,eAAe,CAACgE;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC3C,EACA,EAAA/E,iBAAA,GAAA8B,MAAM,CAAC7C,SAAS,cAAAe,iBAAA,uBAAhBA,iBAAA,CAAkBsD,OAAO,kBAAI5E,OAAA;UAAA6F,QAAA,EAAMzC,MAAM,CAAC7C,SAAS,CAACqE;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAClE,EAAA9E,gBAAA,GAAA6B,MAAM,CAAC5C,QAAQ,cAAAe,gBAAA,uBAAfA,gBAAA,CAAiBqD,OAAO,kBAAI5E,OAAA;UAAA6F,QAAA,EAAMzC,MAAM,CAAC5C,QAAQ,CAACoE;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAChE,EAAA7E,aAAA,GAAA4B,MAAM,CAACrC,KAAK,cAAAS,aAAA,uBAAZA,aAAA,CAAcoD,OAAO,kBAAI5E,OAAA;UAAA6F,QAAA,EAAMzC,MAAM,CAACrC,KAAK,CAAC6D;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACN,eACDrG,OAAA;QACE+E,IAAI,EAAC,OAAO;QACZyC,WAAW,EAAC,QAAQ;QACpB1B,SAAS,EAAC,aAAa;QAAA,GACnB7C,QAAQ,CAAC,OAAO,EAAE;UACpB3C,QAAQ,EAAE,mBAAmB;UAC7BmH,OAAO,EAAE;YACPpC,KAAK,EAAE,4BAA4B;YACnCT,OAAO,EAAE;UACX;QACF,CAAC,CAAC;QACFmB,KAAK,EACHtD,cAAc,GAAG;UAAEwD,YAAY,EAAE,MAAM;UAAEyB,OAAO,EAAE;QAAW,CAAC,GAAG,CAAC;MACnE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAcFrG,OAAA;QACE+E,IAAI,EAAC,MAAM;QACXyC,WAAW,EAAC,YAAY;QACxB1B,SAAS,EAAC,aAAa;QAAA,GACnB7C,QAAQ,CAAC,WAAW,CAAC;QACzB8C,KAAK,EACHtD,cAAc,GAAG;UAAEwD,YAAY,EAAE,MAAM;UAAEyB,OAAO,EAAE;QAAW,CAAC,GAAG,CAAC;MACnE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAcFrG,OAAA;QACE+E,IAAI,EAAC,MAAM;QACXyC,WAAW,EAAC,WAAW;QACvB1B,SAAS,EAAC,aAAa;QAAA,GACnB7C,QAAQ,CAAC,UAAU,CAAC;QACxB8C,KAAK,EACHtD,cAAc,GAAG;UAAEwD,YAAY,EAAE,MAAM;UAAEyB,OAAO,EAAE;QAAW,CAAC,GAAG,CAAC;MACnE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAeFrG,OAAA,CAACf,iBAAiB;QAAC0I,WAAW,EAAEpC,eAAgB;QAAAM,QAAA,eAC9C7F,OAAA;UAAK+F,KAAK,EAAE;YAAE6B,QAAQ,EAAE;UAAW,CAAE;UAAA/B,QAAA,gBACnC7F,OAAA;YACEc,GAAG,EAAE0B,gBAAiB;YACtBuC,IAAI,EAAEpD,YAAY,GAAG,MAAM,GAAG,UAAW;YACzC6F,WAAW,EAAC,UAAU;YACtB1B,SAAS,EAAC,aAAa;YACvBT,KAAK,EAAE5E,QAAS;YAChBoH,QAAQ,EAAE5C,oBAAqB;YAC/B6C,OAAO,EAAExC,mBAAoB;YAC7BS,KAAK,EACHtD,cAAc,GACV;cAAEwD,YAAY,EAAE,MAAM;cAAEyB,OAAO,EAAE;YAAW,CAAC,GAC7C,CAAC;UACN;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFrG,OAAA;YACE2G,OAAO,EAAEA,CAAA,KAAM/E,eAAe,CAAEmG,IAAI,IAAK,CAACA,IAAI,CAAE;YAChDhC,KAAK,EAAE;cACL6B,QAAQ,EAAE,UAAU;cACpBI,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE,KAAK;cACVC,SAAS,EAAE,sBAAsB;cACjCC,MAAM,EAAE;YACV,CAAE;YAAAtC,QAAA,EAEDlE,YAAY,gBAAG3B,OAAA,CAACZ,qBAAqB;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrG,OAAA,CAACb,YAAY;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAGPrG,OAAA,CAAClB,MAAM;YACLsJ,IAAI,EAAEhG,gBAAiB;YACvBiG,QAAQ,EAAE7F,gBAAgB,CAAC8F,OAAQ;YACnCC,SAAS,EAAE7F,QAAQ,GAAG,cAAc,GAAG,aAAc;YACrDqD,KAAK,EAAE;cAAEyC,MAAM,EAAE;YAAK,CAAE;YAAA3C,QAAA,eAExB7F,OAAA,CAACjB,KAAK;cACJ0J,SAAS,EAAE,CAAE;cACbC,EAAE,EAAE;gBACFC,CAAC,EAAE,CAAC;gBACJ,IAAIjG,QAAQ,GACR;kBACEkG,EAAE,EAAE,CAAC;kBACLhB,QAAQ,EAAE,UAAU;kBACpB,WAAW,EAAE;oBACXiB,OAAO,EAAE,IAAI;oBACbjB,QAAQ,EAAE,UAAU;oBACpBK,GAAG,EAAE,CAAC,EAAE;oBACRa,IAAI,EAAE,EAAE;oBACRC,WAAW,EAAE,kBAAkB;oBAC/BC,WAAW,EAAE,OAAO;oBACpBC,WAAW,EACT;kBACJ;gBACF,CAAC,GACD;kBACEC,EAAE,EAAE,CAAC;kBACLtB,QAAQ,EAAE,UAAU;kBACpB,WAAW,EAAE;oBACXiB,OAAO,EAAE,IAAI;oBACbjB,QAAQ,EAAE,UAAU;oBACpBK,GAAG,EAAE,EAAE;oBACPa,IAAI,EAAE,CAAC,EAAE;oBACTC,WAAW,EAAE,kBAAkB;oBAC/BC,WAAW,EAAE,OAAO;oBACpBC,WAAW,EACT;kBACJ;gBACF,CAAC;cACP,CAAE;cAAApD,QAAA,gBAEF7F,OAAA,CAAChB,UAAU;gBACTmK,OAAO,EAAC,WAAW;gBACnBT,EAAE,EAAE;kBAAE1B,UAAU,EAAE,MAAM;kBAAEoC,EAAE,EAAE;gBAAE,CAAE;gBAAAvD,QAAA,EACnC;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA;gBAAI+F,KAAK,EAAE;kBAAEwB,MAAM,EAAE,CAAC;kBAAE8B,WAAW,EAAE;gBAAG,CAAE;gBAAAxD,QAAA,gBACxC7F,OAAA;kBACE+F,KAAK,EAAE;oBACLuD,KAAK,EAAE3G,YAAY,CAACE,MAAM,GAAG,OAAO,GAAG,KAAK;oBAC5CoD,YAAY,EAAE;kBAChB,CAAE;kBAAAJ,QAAA,EACH;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrG,OAAA;kBACE+F,KAAK,EAAE;oBACLuD,KAAK,EAAE3G,YAAY,CAACG,SAAS,GAAG,OAAO,GAAG,KAAK;oBAC/CmD,YAAY,EAAE;kBAChB,CAAE;kBAAAJ,QAAA,EACH;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrG,OAAA;kBACE+F,KAAK,EAAE;oBACLuD,KAAK,EAAE3G,YAAY,CAACI,MAAM,GAAG,OAAO,GAAG,KAAK;oBAC5CkD,YAAY,EAAE;kBAChB,CAAE;kBAAAJ,QAAA,EACH;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrG,OAAA;kBAAI+F,KAAK,EAAE;oBAAEuD,KAAK,EAAE3G,YAAY,CAACK,OAAO,GAAG,OAAO,GAAG;kBAAM,CAAE;kBAAA6C,QAAA,EAAC;gBAE9D;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAOpBrG,OAAA;QAAK+F,KAAK,EAAE;UAAE6B,QAAQ,EAAE;QAAW,CAAE;QAAA/B,QAAA,gBACnC7F,OAAA;UACE+E,IAAI,EAAElD,mBAAmB,GAAG,MAAM,GAAG,UAAW;UAChD2F,WAAW,EAAC,kBAAkB;UAC9B1B,SAAS,EAAC,aAAa;UAAA,GACnB7C,QAAQ,CAAC,iBAAiB;QAAC;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACFrG,OAAA;UACE2G,OAAO,EAAEA,CAAA,KAAM7E,sBAAsB,CAAEiG,IAAI,IAAK,CAACA,IAAI,CAAE;UACvDhC,KAAK,EAAE;YACL6B,QAAQ,EAAE,UAAU;YACpBI,KAAK,EAAE,MAAM;YACbC,GAAG,EAAE,KAAK;YACVC,SAAS,EAAE,sBAAsB;YACjCC,MAAM,EAAE;UACV,CAAE;UAAAtC,QAAA,EAEDhE,mBAAmB,gBAAG7B,OAAA,CAACZ,qBAAqB;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGrG,OAAA,CAACb,YAAY;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAKNrG,OAAA;QAAK8F,SAAS,EAAC,2BAA2B;QAAAD,QAAA,gBACxC7F,OAAA,CAACpB,QAAQ;UAAA,GACHqE,QAAQ,CAAC,OAAO,CAAC;UACrByF,EAAE,EAAE;YAAEY,KAAK,EAAE,SAAS;YAAE,eAAe,EAAE;cAAEA,KAAK,EAAE;YAAU;UAAE;QAAE;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACFrG,OAAA;UAAG8F,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAAC,4BACH,EAAC,GAAG,eAC9B7F,OAAA;YAAGuJ,IAAI,EAAC,SAAS;YAACnE,MAAM,EAAC,QAAQ;YAACoE,GAAG,EAAC,qBAAqB;YAAA3D,QAAA,EAAC;UAE5D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAAC,GAAG,EAAC,KACN,EAAC,GAAG,eACPrG,OAAA;YAAGuJ,IAAI,EAAC,SAAS;YAACnE,MAAM,EAAC,QAAQ;YAACoE,GAAG,EAAC,qBAAqB;YAAA3D,QAAA,EAAC;UAE5D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,KAEN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAKNrG,OAAA;QAAQ+E,IAAI,EAAC,QAAQ;QAACe,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAAC;MAEjD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrG,OAAA;QAAG8F,SAAS,EAAC,eAAe;QAAAD,QAAA,GAAC,2BACF,eAAA7F,OAAA;UAAGuJ,IAAI,EAAC,QAAQ;UAAA1D,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrG,OAAA,CAACT,gBAAgB;MAACkK,IAAI,EAAE1H,cAAe;MAACiD,UAAU,EAAEA;IAAW;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClErG,OAAA,CAACJ,kBAAkB;MACjB6J,IAAI,EAAEnH,gBAAiB;MACvB0C,UAAU,EAAEA,CAAA,KAAM;QAChBzC,mBAAmB,CAAC,KAAK,CAAC;QAC1Bd,QAAQ,CAAC,SAAS,CAAC;MACrB;IAAE;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnF,EAAA,CAngBID,UAAU;EAAA,QACG3B,WAAW,EACDK,OAAO,EAYXT,aAAa,EAKnBA,aAAa,EAiB1BM,OAAO;AAAA;AAAAkK,EAAA,GApCPzI,UAAU;AAqgBhB,eAAeA,UAAU;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}