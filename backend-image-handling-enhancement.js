// Enhanced backend function to handle optimized image updates
// This is a suggested enhancement for your existing updateProduct function

exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = { ...req.body };

    // Fetch the existing product
    const existingProduct = await Product.findById(id);
    if (!existingProduct) {
      return res.status(404).json({ message: "Product not found" });
    }

    // Handle image metadata for efficient updates
    let imageMetadata = null;
    if (req.body.imageMetadata) {
      try {
        imageMetadata = JSON.parse(req.body.imageMetadata);
      } catch (error) {
        console.error("Error parsing image metadata:", error);
      }
    }

    // Reconstruct arrays for colors and sizes (FIXED)
    if (req.body.colors) {
      updates.colors = Array.isArray(req.body.colors)
        ? req.body.colors
        : Object.keys(req.body)
            .filter((key) => key.startsWith("colors["))
            .map((key) => req.body[key]);
    }

    if (req.body.sizes) {
      updates.sizes = Array.isArray(req.body.sizes)
        ? req.body.sizes
        : Object.keys(req.body)
            .filter((key) => key.startsWith("sizes["))
            .map((key) => req.body[key]);
    }

    // Reconstruct tags array
    if (req.body.tags) {
      updates.tags = Array.isArray(req.body.tags)
        ? req.body.tags
        : Object.keys(req.body)
            .filter((key) => key.startsWith("tags["))
            .map((key) => req.body[key]);
    }

    // Ensure reviews is an array of objects
    if (updates.reviews && typeof updates.reviews === "string") {
      updates.reviews = JSON.parse(updates.reviews);
    }

    // Parse technical dimensions
    if (
      updates.technicalDimensions &&
      typeof updates.technicalDimensions === "string"
    ) {
      updates.technicalDimensions = JSON.parse(updates.technicalDimensions);
    }

    // Ensure warrantyInfo is an object
    if (updates.warrantyInfo && typeof updates.warrantyInfo === "string") {
      updates.warrantyInfo = JSON.parse(updates.warrantyInfo);
    }

    // Handle readyToShip boolean conversion
    if (updates.readyToShip !== undefined) {
      updates.readyToShip =
        updates.readyToShip === "true" || updates.readyToShip === true;
    }

    // Enhanced image handling with metadata
    if (imageMetadata && imageMetadata.operation === "update") {
      // Get existing images that should be kept
      const existingImages = (existingProduct.images || [])
        .map((img) =>
          typeof img === "string" ? img.replace(/^\/uploads\//, "") : null
        )
        .filter((img) => img && img !== "undefined" && img !== "null");

      // Process new image uploads
      let newImages = [];
      if (req.files && req.files.images && req.files.images.length > 0) {
        newImages = req.files.images
          .map((file) => file.filename)
          .filter(
            (filename) =>
              filename && filename !== "undefined" && filename !== "null"
          );
      }

      // Combine images based on metadata
      const finalImages = [];
      let newImageIndex = 0;

      imageMetadata.currentImages.forEach((imgPath) => {
        if (imgPath === "NEW_FILE") {
          // Add new uploaded image
          if (newImageIndex < newImages.length) {
            finalImages.push(newImages[newImageIndex]);
            newImageIndex++;
          }
        } else {
          // Keep existing image if it's in the current list
          const cleanPath = imgPath.replace(/^.*\//, "");
          if (existingImages.includes(cleanPath)) {
            finalImages.push(cleanPath);
          }
        }
      });

      updates.images = finalImages;
    } else if (req.files && req.files.images && req.files.images.length > 0) {
      // Fallback: if no metadata, append new images to existing ones
      const existingImages = (existingProduct.images || [])
        .map((img) =>
          typeof img === "string" ? img.replace(/^\/uploads\//, "") : null
        )
        .filter((img) => img && img !== "undefined" && img !== "null");

      const newImages = req.files.images
        .map((file) => file.filename)
        .filter(
          (filename) =>
            filename && filename !== "undefined" && filename !== "null"
        );

      updates.images = [...existingImages, ...newImages];
    }

    // Handle CAD file upload
    if (req.files && req.files.cadFile && req.files.cadFile.length > 0) {
      const cadFile = req.files.cadFile[0];
      updates.cadFile = cadFile.filename;
    }

    // Update mainImage if provided, else keep the current one
    updates.mainImage = req.body.mainImage || existingProduct.mainImage;

    // Store updates in pendingUpdates and set updateStatus to 'pending'
    existingProduct.pendingUpdates = updates;
    existingProduct.updateStatus = "pending";
    await existingProduct.save();

    // Compare current product data with pendingUpdates to find changed fields
    const changedFields = [];
    for (let key in updates) {
      if (
        Object.prototype.hasOwnProperty.call(existingProduct._doc, key) &&
        typeof updates[key] !== "object" &&
        existingProduct[key] !== updates[key]
      ) {
        changedFields.push({
          field: key,
          oldValue: existingProduct[key],
          newValue: updates[key],
        });
      }
    }

    // Build a description for the notification
    let description = `Product '${existingProduct.name}' submitted changes for approval: `;
    if (changedFields.length > 0) {
      description += changedFields
        .map((f) => {
          const oldVal =
            typeof f.oldValue === "object"
              ? JSON.stringify(f.oldValue)
              : f.oldValue;
          const newVal =
            typeof f.newValue === "object"
              ? JSON.stringify(f.newValue)
              : f.newValue;
          return `${f.field}: "${oldVal}" → "${newVal}"`;
        })
        .join(", ");
    } else {
      description += "No fields changed.";
    }

    // Add image change information to description
    if (imageMetadata && imageMetadata.operation === "update") {
      const removedImages = imageMetadata.existingImages.filter(
        (img) => !imageMetadata.currentImages.includes(img.replace(/^.*\//, ""))
      );
      const addedImages = imageMetadata.currentImages.filter(
        (img) => img === "NEW_FILE"
      ).length;

      if (removedImages.length > 0 || addedImages > 0) {
        description += ` | Images: ${addedImages} added, ${removedImages.length} removed`;
      }
    }

    // Create admin notification
    const adminNotification = new AdminNotification({
      type: "Product Update",
      description: description,
      read: false,
    });
    await adminNotification.save();

    res.status(200).json({
      message: "Product update submitted for admin approval.",
      product: existingProduct,
      changedFields: Object.keys(updates),
    });
  } catch (error) {
    console.error("Error updating product:", error);
    res
      .status(500)
      .json({ message: "Error updating product", error: error.message });
  }
};
