{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Vendor-Profile\\\\CategoryCard.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport { motion } from \"framer-motion\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VendorCategoryCard = ({\n  id,\n  name,\n  description,\n  image\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleClick = () => {\n    const typeName = encodeURIComponent(name.toLowerCase().replace(/\\s+/g, \"-\"));\n    navigate(`/products/${id}/${typeName}`);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: motion.div,\n    whileHover: {\n      y: -5,\n      transition: {\n        duration: 0.3\n      }\n    },\n    onClick: handleClick,\n    sx: {\n      position: \"relative\",\n      width: \"100%\",\n      height: \"220px\",\n      // Reduced height for slider\n      borderRadius: \"12px\",\n      overflow: \"hidden\",\n      boxShadow: \"0 5px 15px rgba(0,0,0,0.1)\",\n      cursor: \"pointer\",\n      \"&::before\": {\n        content: '\"\"',\n        position: \"absolute\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: \"linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 30%, rgba(0,0,0,0) 60%)\",\n        zIndex: 1,\n        transition: \"opacity 0.3s ease\",\n        opacity: 0.7\n      },\n      \"&:hover::before\": {\n        opacity: 0.9\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      component: motion.img,\n      whileHover: {\n        scale: 1.05\n      },\n      transition: {\n        duration: 0.5\n      },\n      src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}`,\n      alt: name,\n      sx: {\n        width: \"100%\",\n        height: \"100%\",\n        objectFit: \"cover\",\n        transition: \"transform 0.5s ease\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: motion.div,\n      initial: {\n        opacity: 0.9,\n        y: 0\n      },\n      whileHover: {\n        opacity: 1,\n        y: -3,\n        transition: {\n          duration: 0.3\n        }\n      },\n      sx: {\n        position: \"absolute\",\n        bottom: 0,\n        left: 0,\n        right: 0,\n        padding: \"16px\",\n        zIndex: 2,\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"flex-start\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\" // Smaller heading for compact cards\n        ,\n        component: motion.h6,\n        initial: {\n          y: 0\n        },\n        whileHover: {\n          y: -2\n        },\n        sx: {\n          color: \"white\",\n          fontFamily: \"Horizon\",\n          fontWeight: \"bold\",\n          marginBottom: \"4px\",\n          fontSize: \"16px\",\n          // Smaller font size\n          textShadow: \"0 2px 4px rgba(0,0,0,0.3)\",\n          position: \"relative\",\n          \"&::after\": {\n            content: '\"\"',\n            position: \"absolute\",\n            bottom: -5,\n            left: 0,\n            width: \"30px\",\n            height: \"2px\",\n            backgroundColor: \"#6b7b58\",\n            transition: \"width 0.3s ease\"\n          },\n          \"&:hover::after\": {\n            width: \"100%\"\n          }\n        },\n        children: name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), description && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        component: motion.p,\n        initial: {\n          opacity: 0\n        },\n        whileHover: {\n          opacity: 1\n        },\n        sx: {\n          color: \"rgba(255,255,255,0.8)\",\n          fontFamily: \"Montserrat\",\n          fontSize: \"12px\",\n          // Smaller font size\n          maxWidth: \"90%\",\n          overflow: \"hidden\",\n          textOverflow: \"ellipsis\",\n          display: \"-webkit-box\",\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: \"vertical\"\n        },\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: motion.div,\n        initial: {\n          width: 0\n        },\n        whileHover: {\n          width: \"100%\"\n        },\n        sx: {\n          height: \"2px\",\n          backgroundColor: \"white\",\n          marginTop: \"auto\",\n          transition: \"width 0.3s ease\",\n          alignSelf: \"flex-start\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(VendorCategoryCard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = VendorCategoryCard;\nexport default VendorCategoryCard;\nvar _c;\n$RefreshReg$(_c, \"VendorCategoryCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "motion", "useNavigate", "jsxDEV", "_jsxDEV", "VendorCategoryCard", "id", "name", "description", "image", "_s", "navigate", "handleClick", "typeName", "encodeURIComponent", "toLowerCase", "replace", "component", "div", "whileHover", "y", "transition", "duration", "onClick", "sx", "position", "width", "height", "borderRadius", "overflow", "boxShadow", "cursor", "content", "top", "left", "right", "bottom", "background", "zIndex", "opacity", "children", "img", "scale", "src", "alt", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "padding", "display", "flexDirection", "alignItems", "variant", "h6", "color", "fontFamily", "fontWeight", "marginBottom", "fontSize", "textShadow", "backgroundColor", "p", "max<PERSON><PERSON><PERSON>", "textOverflow", "WebkitLineClamp", "WebkitBoxOrient", "marginTop", "alignSelf", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Vendor-Profile/CategoryCard.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Box, Typography } from \"@mui/material\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nconst VendorCategoryCard = ({ id, name, description, image }) => {\r\n  const navigate = useNavigate();\r\n  const handleClick = () => {\r\n    const typeName = encodeURIComponent(\r\n      name.toLowerCase().replace(/\\s+/g, \"-\")\r\n    );\r\n    navigate(`/products/${id}/${typeName}`);\r\n  };\r\n  return (\r\n    <Box\r\n      component={motion.div}\r\n      whileHover={{\r\n        y: -5,\r\n        transition: { duration: 0.3 },\r\n      }}\r\n      onClick={handleClick}\r\n      sx={{\r\n        position: \"relative\",\r\n        width: \"100%\",\r\n        height: \"220px\", // Reduced height for slider\r\n        borderRadius: \"12px\",\r\n        overflow: \"hidden\",\r\n        boxShadow: \"0 5px 15px rgba(0,0,0,0.1)\",\r\n        cursor: \"pointer\",\r\n        \"&::before\": {\r\n          content: '\"\"',\r\n          position: \"absolute\",\r\n          top: 0,\r\n          left: 0,\r\n          right: 0,\r\n          bottom: 0,\r\n          background:\r\n            \"linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 30%, rgba(0,0,0,0) 60%)\",\r\n          zIndex: 1,\r\n          transition: \"opacity 0.3s ease\",\r\n          opacity: 0.7,\r\n        },\r\n        \"&:hover::before\": {\r\n          opacity: 0.9,\r\n        },\r\n      }}\r\n    >\r\n      <Box\r\n        component={motion.img}\r\n        whileHover={{ scale: 1.05 }}\r\n        transition={{ duration: 0.5 }}\r\n        src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${image}`}\r\n        alt={name}\r\n        sx={{\r\n          width: \"100%\",\r\n          height: \"100%\",\r\n          objectFit: \"cover\",\r\n          transition: \"transform 0.5s ease\",\r\n        }}\r\n      />\r\n\r\n      <Box\r\n        component={motion.div}\r\n        initial={{ opacity: 0.9, y: 0 }}\r\n        whileHover={{\r\n          opacity: 1,\r\n          y: -3,\r\n          transition: { duration: 0.3 },\r\n        }}\r\n        sx={{\r\n          position: \"absolute\",\r\n          bottom: 0,\r\n          left: 0,\r\n          right: 0,\r\n          padding: \"16px\",\r\n          zIndex: 2,\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n          alignItems: \"flex-start\",\r\n        }}\r\n      >\r\n        <Typography\r\n          variant=\"h6\" // Smaller heading for compact cards\r\n          component={motion.h6}\r\n          initial={{ y: 0 }}\r\n          whileHover={{ y: -2 }}\r\n          sx={{\r\n            color: \"white\",\r\n            fontFamily: \"Horizon\",\r\n            fontWeight: \"bold\",\r\n            marginBottom: \"4px\",\r\n            fontSize: \"16px\", // Smaller font size\r\n            textShadow: \"0 2px 4px rgba(0,0,0,0.3)\",\r\n            position: \"relative\",\r\n            \"&::after\": {\r\n              content: '\"\"',\r\n              position: \"absolute\",\r\n              bottom: -5,\r\n              left: 0,\r\n              width: \"30px\",\r\n              height: \"2px\",\r\n              backgroundColor: \"#6b7b58\",\r\n              transition: \"width 0.3s ease\",\r\n            },\r\n            \"&:hover::after\": {\r\n              width: \"100%\",\r\n            },\r\n          }}\r\n        >\r\n          {name}\r\n        </Typography>\r\n\r\n        {description && (\r\n          <Typography\r\n            variant=\"body2\"\r\n            component={motion.p}\r\n            initial={{ opacity: 0 }}\r\n            whileHover={{ opacity: 1 }}\r\n            sx={{\r\n              color: \"rgba(255,255,255,0.8)\",\r\n              fontFamily: \"Montserrat\",\r\n              fontSize: \"12px\", // Smaller font size\r\n              maxWidth: \"90%\",\r\n              overflow: \"hidden\",\r\n              textOverflow: \"ellipsis\",\r\n              display: \"-webkit-box\",\r\n              WebkitLineClamp: 2,\r\n              WebkitBoxOrient: \"vertical\",\r\n            }}\r\n          >\r\n            {description}\r\n          </Typography>\r\n        )}\r\n\r\n        <Box\r\n          component={motion.div}\r\n          initial={{ width: 0 }}\r\n          whileHover={{ width: \"100%\" }}\r\n          sx={{\r\n            height: \"2px\",\r\n            backgroundColor: \"white\",\r\n            marginTop: \"auto\",\r\n            transition: \"width 0.3s ease\",\r\n            alignSelf: \"flex-start\",\r\n          }}\r\n        />\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default VendorCategoryCard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAC/C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,EAAE;EAAEC,IAAI;EAAEC,WAAW;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAGC,kBAAkB,CACjCP,IAAI,CAACQ,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CACxC,CAAC;IACDL,QAAQ,CAAC,aAAaL,EAAE,IAAIO,QAAQ,EAAE,CAAC;EACzC,CAAC;EACD,oBACET,OAAA,CAACL,GAAG;IACFkB,SAAS,EAAEhB,MAAM,CAACiB,GAAI;IACtBC,UAAU,EAAE;MACVC,CAAC,EAAE,CAAC,CAAC;MACLC,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B,CAAE;IACFC,OAAO,EAAEX,WAAY;IACrBY,EAAE,EAAE;MACFC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,OAAO;MAAE;MACjBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,4BAA4B;MACvCC,MAAM,EAAE,SAAS;MACjB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbP,QAAQ,EAAE,UAAU;QACpBQ,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,UAAU,EACR,qFAAqF;QACvFC,MAAM,EAAE,CAAC;QACTjB,UAAU,EAAE,mBAAmB;QAC/BkB,OAAO,EAAE;MACX,CAAC;MACD,iBAAiB,EAAE;QACjBA,OAAO,EAAE;MACX;IACF,CAAE;IAAAC,QAAA,gBAEFpC,OAAA,CAACL,GAAG;MACFkB,SAAS,EAAEhB,MAAM,CAACwC,GAAI;MACtBtB,UAAU,EAAE;QAAEuB,KAAK,EAAE;MAAK,CAAE;MAC5BrB,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BqB,GAAG,EAAE,uDAAuDlC,KAAK,EAAG;MACpEmC,GAAG,EAAErC,IAAK;MACViB,EAAE,EAAE;QACFE,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdkB,SAAS,EAAE,OAAO;QAClBxB,UAAU,EAAE;MACd;IAAE;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF7C,OAAA,CAACL,GAAG;MACFkB,SAAS,EAAEhB,MAAM,CAACiB,GAAI;MACtBgC,OAAO,EAAE;QAAEX,OAAO,EAAE,GAAG;QAAEnB,CAAC,EAAE;MAAE,CAAE;MAChCD,UAAU,EAAE;QACVoB,OAAO,EAAE,CAAC;QACVnB,CAAC,EAAE,CAAC,CAAC;QACLC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI;MAC9B,CAAE;MACFE,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE,CAAC;QACTF,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRgB,OAAO,EAAE,MAAM;QACfb,MAAM,EAAE,CAAC;QACTc,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE;MACd,CAAE;MAAAd,QAAA,gBAEFpC,OAAA,CAACJ,UAAU;QACTuD,OAAO,EAAC,IAAI,CAAC;QAAA;QACbtC,SAAS,EAAEhB,MAAM,CAACuD,EAAG;QACrBN,OAAO,EAAE;UAAE9B,CAAC,EAAE;QAAE,CAAE;QAClBD,UAAU,EAAE;UAAEC,CAAC,EAAE,CAAC;QAAE,CAAE;QACtBI,EAAE,EAAE;UACFiC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,SAAS;UACrBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,MAAM;UAAE;UAClBC,UAAU,EAAE,2BAA2B;UACvCrC,QAAQ,EAAE,UAAU;UACpB,UAAU,EAAE;YACVO,OAAO,EAAE,IAAI;YACbP,QAAQ,EAAE,UAAU;YACpBW,MAAM,EAAE,CAAC,CAAC;YACVF,IAAI,EAAE,CAAC;YACPR,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACboC,eAAe,EAAE,SAAS;YAC1B1C,UAAU,EAAE;UACd,CAAC;UACD,gBAAgB,EAAE;YAChBK,KAAK,EAAE;UACT;QACF,CAAE;QAAAc,QAAA,EAEDjC;MAAI;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAEZzC,WAAW,iBACVJ,OAAA,CAACJ,UAAU;QACTuD,OAAO,EAAC,OAAO;QACftC,SAAS,EAAEhB,MAAM,CAAC+D,CAAE;QACpBd,OAAO,EAAE;UAAEX,OAAO,EAAE;QAAE,CAAE;QACxBpB,UAAU,EAAE;UAAEoB,OAAO,EAAE;QAAE,CAAE;QAC3Bf,EAAE,EAAE;UACFiC,KAAK,EAAE,uBAAuB;UAC9BC,UAAU,EAAE,YAAY;UACxBG,QAAQ,EAAE,MAAM;UAAE;UAClBI,QAAQ,EAAE,KAAK;UACfpC,QAAQ,EAAE,QAAQ;UAClBqC,YAAY,EAAE,UAAU;UACxBd,OAAO,EAAE,aAAa;UACtBe,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE;QACnB,CAAE;QAAA5B,QAAA,EAEDhC;MAAW;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACb,eAED7C,OAAA,CAACL,GAAG;QACFkB,SAAS,EAAEhB,MAAM,CAACiB,GAAI;QACtBgC,OAAO,EAAE;UAAExB,KAAK,EAAE;QAAE,CAAE;QACtBP,UAAU,EAAE;UAAEO,KAAK,EAAE;QAAO,CAAE;QAC9BF,EAAE,EAAE;UACFG,MAAM,EAAE,KAAK;UACboC,eAAe,EAAE,OAAO;UACxBM,SAAS,EAAE,MAAM;UACjBhD,UAAU,EAAE,iBAAiB;UAC7BiD,SAAS,EAAE;QACb;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAhJIL,kBAAkB;EAAA,QACLH,WAAW;AAAA;AAAAqE,EAAA,GADxBlE,kBAAkB;AAkJxB,eAAeA,kBAAkB;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}