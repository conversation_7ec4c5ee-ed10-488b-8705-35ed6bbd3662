{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\Checkout\\\\Paymentmethod.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, FormControl, RadioGroup, Radio, FormControlLabel, Button, CircularProgress, Typography, Dialog, DialogTitle, IconButton } from \"@mui/material\";\nimport BillSummary from \"./billingSummary\";\nimport paymobService from \"../../services/paymobService\";\nimport { useUser } from \"../../utils/userContext\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { useLocation } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PaymentForm({\n  onSubmit,\n  onSuccess,\n  onFailed,\n  resetCart,\n  paymentData,\n  onChange,\n  billData,\n  errors = {}\n}) {\n  _s();\n  const [paymentMethod, setPaymentMethod] = useState(paymentData.paymentMethod);\n  const [showCOD, setShowCOD] = useState(true);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [paymentError, setPaymentError] = useState(null);\n  const [iframeUrl, setIframeUrl] = useState(null);\n  const {\n    userSession\n  } = useUser();\n  const [iframeModalOpen, setIframeModalOpen] = useState(false);\n  const location = useLocation();\n\n  // Check URL for payment success\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const orderId = searchParams.get(\"order\");\n    const status = searchParams.get(\"status\");\n    if (orderId && status === \"success\") {\n      onSuccess(); // ✅ Trigger success popup in parent\n      // Clear the URL parameters without refreshing the page\n      window.history.replaceState({}, document.title, window.location.pathname);\n    }\n  }, [location, onSuccess]);\n  useEffect(() => {\n    if (billData && billData.total) {\n      setShowCOD(billData.total <= 10000);\n\n      // If COD is selected but no longer available, switch to card payment\n      if (paymentMethod === \"cod\" && billData.total > 10000) {\n        setPaymentMethod(\"card\");\n        onChange({\n          ...paymentData,\n          paymentMethod: \"card\"\n        });\n      }\n    }\n  }, [billData, paymentData, onChange, paymentMethod]);\n  useEffect(() => {\n    const handleMessage = event => {\n      if (event.origin !== \"https://accept.paymob.com\") return;\n      const {\n        success,\n        error_occured\n      } = event.data;\n      if (success) {\n        onSubmit();\n        onSuccess(); // ✅ Trigger success popup in parent\n        resetCart(); // Reset cart after successful payment\n      } else if (error_occured) {\n        setPaymentError(\"Payment failed. Please try again.\");\n      }\n    };\n    window.addEventListener(\"message\", handleMessage);\n    return () => window.removeEventListener(\"message\", handleMessage);\n  }, [onSubmit, onSuccess, resetCart]);\n  const handlePaymentMethodChange = e => {\n    const method = e.target.value;\n    setPaymentMethod(method);\n    onChange({\n      ...paymentData,\n      paymentMethod: method\n    });\n    setPaymentError(null); // Clear any previous errors when changing payment method\n  };\n  const handlePayNow = async () => {\n    try {\n      setIsProcessing(true);\n      setPaymentError(null);\n      if (paymentMethod === \"card\") {\n        if (!(billData !== null && billData !== void 0 && billData.billingDetails)) {\n          throw new Error(\"Billing information is missing. Please complete the billing form first.\");\n        }\n        const billingDetails = billData.billingDetails;\n        const shippingDetails = billData.shippingDetails;\n        const requiredFields = {\n          first_name: \"First Name\",\n          last_name: \"Last Name\",\n          email: \"Email\",\n          phone_number: \"Phone Number\",\n          street: \"Street Address\",\n          city: \"City\",\n          country: \"Country\"\n        };\n        const missingFields = Object.entries(requiredFields).filter(([key]) => !billingDetails[key]).map(([_, label]) => label);\n        if (missingFields.length > 0) {\n          throw new Error(`Please complete the following billing information: ${missingFields.join(\", \")}`);\n        }\n\n        // Check if cart items have the required properties\n        if (!billData.cartItems || billData.cartItems.length === 0) {\n          throw new Error(\"Your cart is empty. Please add items to your cart.\");\n        }\n\n        // Validate cart items have the necessary properties\n        const invalidItems = billData.cartItems.filter(item => !item.name || typeof item.unitPrice === \"undefined\");\n        if (invalidItems.length > 0) {\n          console.error(\"Invalid cart items:\", invalidItems);\n          throw new Error(\"Some items in your cart are invalid. Please try again or contact support.\");\n        }\n        // Ensure each cart item has productId and brandId\n        const enhancedCartItems = billData.cartItems.map(item => {\n          // Extract the correct productId\n          const productId = item.productId || item.id;\n\n          // Extract the correct brandId\n          let brandId;\n          if (typeof item.brandId === \"object\" && item.brandId !== null) {\n            brandId = item.brandId._id || item.brandId.id;\n          } else {\n            brandId = item.brandId;\n          }\n          return {\n            ...item,\n            productId,\n            brandId\n          };\n        });\n        console.log(\"Enhanced cart items with proper IDs:\", enhancedCartItems);\n        console.log(\"Billing details in paymentMethod:\", billingDetails);\n        console.log(\"Shipping details in paymentMethod:\", shippingDetails);\n        const subtotal = billData.subtotal || 0;\n        const shippingFee = billData.shippingFee || 0;\n        const taxAmount = +(subtotal * 0.14).toFixed(2);\n        const finalTotal = +(subtotal + taxAmount + shippingFee).toFixed(2);\n        const paymentData = {\n          total: finalTotal,\n          billingDetails: billingDetails,\n          cartItems: enhancedCartItems || [],\n          shippingDetails: shippingDetails || {},\n          subtotal,\n          taxAmount,\n          shippingFee\n        };\n        console.log(\"Sending payment data:\", paymentData);\n        try {\n          const result = await paymobService.initializePayment(paymentData, userSession);\n          console.log(\"Result from initializePayment:\", result);\n\n          // Check if we got a valid iframe URL\n          if (!result || !result.iframeUrl) {\n            throw new Error(\"Payment gateway URL not received. Please try again later.\");\n          }\n\n          // Set the iframe URL\n          setIframeUrl(result.iframeUrl);\n          setIframeModalOpen(true);\n          onSuccess(); // ✅ Trigger success popup in parent\n          resetCart();\n          console.log(\"Setting iframe URL to:\", result.iframeUrl);\n        } catch (paymentError) {\n          console.error(\"Payment initialization failed:\", paymentError);\n          throw new Error(paymentError.message || \"Failed to connect to payment gateway. Please try again later.\");\n        }\n      } else if (paymentMethod === \"cod\") {\n        // Handle Cash on Delivery\n        console.log(\"COD selected, calling onSubmit()\");\n        await onSubmit();\n        console.log(\"onSubmit finished, setting showSuccessPopup\");\n        onSuccess(); // ✅ Trigger success popup in parent\n      }\n    } catch (error) {\n      setPaymentError(error.message || \"Payment initialization failed. Please try again.\");\n      if (onFailed) onFailed(); // <-- show the failed popup\n\n      console.error(\"Payment error details:\", {\n        error: error,\n        message: error.message,\n        stack: error.stack,\n        billData: billData\n      });\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  // const handleClosePopup = () => {\n  //   setShowSuccessPopup(false);\n  //   navigate(\"/\");\n  // };\n\n  const handleCloseIframeModal = () => {\n    setIframeModalOpen(false);\n    setIframeUrl(null); // This will unmount the iframe\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"paymentmethod-container\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: \"paymentmethod-firstrow-firstcolumn\",\n      children: /*#__PURE__*/_jsxDEV(FormControl, {\n        fullWidth: true,\n        children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n          value: paymentMethod,\n          onChange: handlePaymentMethodChange,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"card\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 24\n            }, this),\n            label: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"horizon-font\",\n              children: \"Pay with Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 22\n            }, this),\n            sx: {\n              borderBottom: \"1px solid black\",\n              pb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), paymentError && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              mb: 2,\n              bgcolor: \"rgba(244, 67, 54, 0.1)\",\n              color: \"#f44336\",\n              borderRadius: \"8px\",\n              borderLeft: \"4px solid #f44336\",\n              animation: \"fadeIn 0.3s ease\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: paymentError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mt: 1,\n                fontSize: \"0.85rem\"\n              },\n              children: \"If this problem persists, please contact our support team.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), paymentMethod === \"card\" && iframeUrl && /*#__PURE__*/_jsxDEV(Box, {\n            className: \"payment-iframe-container\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                p: 1,\n                bgcolor: \"#f5f5f5\"\n              },\n              children: \"Payment Gateway\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"iframe\", {\n              src: iframeUrl,\n              style: {\n                width: \"100%\",\n                height: \"600px\",\n                border: \"none\",\n                display: \"block\"\n              },\n              allow: \"camera; microphone; accelerometer; gyroscope; payment\",\n              allowFullScreen: true,\n              title: \"Paymob Payment\",\n              id: \"paymob-iframe\",\n              onLoad: () => console.log(\"Iframe loaded\"),\n              onError: e => console.error(\"Iframe error:\", e),\n              referrerPolicy: \"origin\",\n              sandbox: \"allow-forms allow-scripts allow-same-origin allow-top-navigation allow-popups\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), paymentMethod === \"card\" && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              textAlign: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              className: \"montserrat-font\",\n              onClick: handlePayNow,\n              disabled: isProcessing || iframeUrl,\n              sx: {\n                mt: 2,\n                backgroundColor: \"#6B7B58\",\n                \"&:hover\": {\n                  backgroundColor: \"#5a6a47\"\n                },\n                display: iframeUrl ? \"none\" : \"inline-flex\"\n              },\n              children: isProcessing ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this) : \"Pay Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), isProcessing && !iframeUrl && /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                mt: 2\n              },\n              children: \"Preparing payment gateway...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), paymentMethod === \"cod\" && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              textAlign: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              className: \"montserrat-font\",\n              onClick: handlePayNow,\n              sx: {\n                mt: 2,\n                backgroundColor: \"#6B7B58\",\n                \"&:hover\": {\n                  backgroundColor: \"#5a6a47\"\n                }\n              },\n              children: \"Complete Order\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), showCOD ? /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"cod\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 26\n            }, this),\n            label: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"montserrat-font\",\n              children: \"Cash on Delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 19\n            }, this),\n            sx: {\n              pt: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              padding: \"8px 0\",\n              color: \"#888\",\n              fontStyle: \"italic\",\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"8px\",\n              pt: 1\n            },\n            className: \"montserrat-font\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: \"24px\",\n                height: \"24px\",\n                display: \"inline-block\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), \"Cash on Delivery unavailable for orders over 10,000 EGP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BillSummary, {\n      cartItems: billData.cartItems,\n      subtotal: billData.subtotal,\n      shippingFee: billData.shippingFee,\n      total: billData.total\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: iframeModalOpen,\n      onClose: handleCloseIframeModal,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 4,\n          background: \"#fff\",\n          boxShadow: \"0 8px 32px rgba(0,0,0,0.18)\",\n          p: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\"\n        },\n        children: [\"Complete Payment\", /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleCloseIframeModal,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n          src: iframeUrl,\n          style: {\n            width: \"100%\",\n            height: \"600px\",\n            border: \"none\",\n            display: \"block\"\n          },\n          allow: \"camera; microphone; accelerometer; gyroscope; payment\",\n          allowFullScreen: true,\n          title: \"Paymob Payment\",\n          id: \"paymob-iframe\",\n          referrerPolicy: \"origin\",\n          sandbox: \"allow-forms allow-scripts allow-same-origin allow-top-navigation allow-popups\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n}\n_s(PaymentForm, \"anAFYLmhv69bFh0/VxB8Q3Ai7yc=\", false, function () {\n  return [useUser, useLocation];\n});\n_c = PaymentForm;\nexport default PaymentForm;\nvar _c;\n$RefreshReg$(_c, \"PaymentForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "FormControl", "RadioGroup", "Radio", "FormControlLabel", "<PERSON><PERSON>", "CircularProgress", "Typography", "Dialog", "DialogTitle", "IconButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymobService", "useUser", "CloseIcon", "useLocation", "jsxDEV", "_jsxDEV", "PaymentForm", "onSubmit", "onSuccess", "onFailed", "resetCart", "paymentData", "onChange", "billData", "errors", "_s", "paymentMethod", "setPaymentMethod", "showCOD", "setShowCOD", "isProcessing", "setIsProcessing", "paymentError", "setPaymentError", "iframeUrl", "setIframeUrl", "userSession", "iframeModalOpen", "setIframeModalOpen", "location", "searchParams", "URLSearchParams", "search", "orderId", "get", "status", "window", "history", "replaceState", "document", "title", "pathname", "total", "handleMessage", "event", "origin", "success", "error_occured", "data", "addEventListener", "removeEventListener", "handlePaymentMethodChange", "e", "method", "target", "value", "handlePayNow", "billingDetails", "Error", "shippingDetails", "requiredFields", "first_name", "last_name", "email", "phone_number", "street", "city", "country", "missingFields", "Object", "entries", "filter", "key", "map", "_", "label", "length", "join", "cartItems", "invalidItems", "item", "name", "unitPrice", "console", "error", "enhancedCartItems", "productId", "id", "brandId", "_id", "log", "subtotal", "shippingFee", "taxAmount", "toFixed", "finalTotal", "result", "initializePayment", "message", "stack", "handleCloseIframeModal", "className", "children", "fullWidth", "control", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "borderBottom", "pb", "p", "mb", "bgcolor", "color", "borderRadius", "borderLeft", "animation", "variant", "mt", "fontSize", "src", "style", "width", "height", "border", "display", "allow", "allowFullScreen", "onLoad", "onError", "referrerPolicy", "sandbox", "textAlign", "onClick", "disabled", "backgroundColor", "size", "pt", "padding", "fontStyle", "alignItems", "gap", "open", "onClose", "max<PERSON><PERSON><PERSON>", "PaperProps", "background", "boxShadow", "justifyContent", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/Checkout/Paymentmethod.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  FormControl,\r\n  RadioGroup,\r\n  Radio,\r\n  FormControlLabel,\r\n  Button,\r\n  CircularProgress,\r\n  Typography,\r\n  Dialog,\r\n  DialogTitle,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport BillSummary from \"./billingSummary\";\r\nimport paymobService from \"../../services/paymobService\";\r\nimport { useUser } from \"../../utils/userContext\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useLocation } from \"react-router-dom\";\r\n\r\nfunction PaymentForm({\r\n  onSubmit,\r\n  onSuccess,\r\n  onFailed,\r\n  resetCart,\r\n  paymentData,\r\n  onChange,\r\n  billData,\r\n  errors = {},\r\n}) {\r\n  const [paymentMethod, setPaymentMethod] = useState(paymentData.paymentMethod);\r\n  const [showCOD, setShowCOD] = useState(true);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [paymentError, setPaymentError] = useState(null);\r\n  const [iframeUrl, setIframeUrl] = useState(null);\r\n  const { userSession } = useUser();\r\n  const [iframeModalOpen, setIframeModalOpen] = useState(false);\r\n  const location = useLocation();\r\n\r\n  // Check URL for payment success\r\n  useEffect(() => {\r\n    const searchParams = new URLSearchParams(location.search);\r\n    const orderId = searchParams.get(\"order\");\r\n    const status = searchParams.get(\"status\");\r\n\r\n    if (orderId && status === \"success\") {\r\n      onSuccess(); // ✅ Trigger success popup in parent\r\n      // Clear the URL parameters without refreshing the page\r\n      window.history.replaceState({}, document.title, window.location.pathname);\r\n    }\r\n  }, [location, onSuccess]);\r\n  useEffect(() => {\r\n    if (billData && billData.total) {\r\n      setShowCOD(billData.total <= 10000);\r\n\r\n      // If COD is selected but no longer available, switch to card payment\r\n      if (paymentMethod === \"cod\" && billData.total > 10000) {\r\n        setPaymentMethod(\"card\");\r\n        onChange({ ...paymentData, paymentMethod: \"card\" });\r\n      }\r\n    }\r\n  }, [billData, paymentData, onChange, paymentMethod]);\r\n\r\n  useEffect(() => {\r\n    const handleMessage = (event) => {\r\n      if (event.origin !== \"https://accept.paymob.com\") return;\r\n      const { success, error_occured } = event.data;\r\n      if (success) {\r\n        onSubmit();\r\n        onSuccess(); // ✅ Trigger success popup in parent\r\n        resetCart(); // Reset cart after successful payment\r\n      } else if (error_occured) {\r\n        setPaymentError(\"Payment failed. Please try again.\");\r\n      }\r\n    };\r\n    window.addEventListener(\"message\", handleMessage);\r\n    return () => window.removeEventListener(\"message\", handleMessage);\r\n  }, [onSubmit, onSuccess, resetCart]);\r\n\r\n  const handlePaymentMethodChange = (e) => {\r\n    const method = e.target.value;\r\n    setPaymentMethod(method);\r\n    onChange({ ...paymentData, paymentMethod: method });\r\n    setPaymentError(null); // Clear any previous errors when changing payment method\r\n  };\r\n\r\n  const handlePayNow = async () => {\r\n    try {\r\n      setIsProcessing(true);\r\n      setPaymentError(null);\r\n\r\n      if (paymentMethod === \"card\") {\r\n        if (!billData?.billingDetails) {\r\n          throw new Error(\r\n            \"Billing information is missing. Please complete the billing form first.\"\r\n          );\r\n        }\r\n\r\n        const billingDetails = billData.billingDetails;\r\n        const shippingDetails = billData.shippingDetails;\r\n        const requiredFields = {\r\n          first_name: \"First Name\",\r\n          last_name: \"Last Name\",\r\n          email: \"Email\",\r\n          phone_number: \"Phone Number\",\r\n          street: \"Street Address\",\r\n          city: \"City\",\r\n          country: \"Country\",\r\n        };\r\n\r\n        const missingFields = Object.entries(requiredFields)\r\n          .filter(([key]) => !billingDetails[key])\r\n          .map(([_, label]) => label);\r\n\r\n        if (missingFields.length > 0) {\r\n          throw new Error(\r\n            `Please complete the following billing information: ${missingFields.join(\r\n              \", \"\r\n            )}`\r\n          );\r\n        }\r\n\r\n        // Check if cart items have the required properties\r\n        if (!billData.cartItems || billData.cartItems.length === 0) {\r\n          throw new Error(\"Your cart is empty. Please add items to your cart.\");\r\n        }\r\n\r\n        // Validate cart items have the necessary properties\r\n        const invalidItems = billData.cartItems.filter(\r\n          (item) => !item.name || typeof item.unitPrice === \"undefined\"\r\n        );\r\n\r\n        if (invalidItems.length > 0) {\r\n          console.error(\"Invalid cart items:\", invalidItems);\r\n          throw new Error(\r\n            \"Some items in your cart are invalid. Please try again or contact support.\"\r\n          );\r\n        }\r\n        // Ensure each cart item has productId and brandId\r\n        const enhancedCartItems = billData.cartItems.map((item) => {\r\n          // Extract the correct productId\r\n          const productId = item.productId || item.id;\r\n\r\n          // Extract the correct brandId\r\n          let brandId;\r\n          if (typeof item.brandId === \"object\" && item.brandId !== null) {\r\n            brandId = item.brandId._id || item.brandId.id;\r\n          } else {\r\n            brandId = item.brandId;\r\n          }\r\n\r\n          return {\r\n            ...item,\r\n            productId,\r\n            brandId,\r\n          };\r\n        });\r\n\r\n        console.log(\"Enhanced cart items with proper IDs:\", enhancedCartItems);\r\n        console.log(\"Billing details in paymentMethod:\", billingDetails);\r\n        console.log(\"Shipping details in paymentMethod:\", shippingDetails);\r\n        const subtotal = billData.subtotal || 0;\r\n        const shippingFee = billData.shippingFee || 0;\r\n        const taxAmount = +(subtotal * 0.14).toFixed(2);\r\n        const finalTotal = +(subtotal + taxAmount + shippingFee).toFixed(2);\r\n        const paymentData = {\r\n          total: finalTotal,\r\n          billingDetails: billingDetails,\r\n          cartItems: enhancedCartItems || [],\r\n          shippingDetails: shippingDetails || {},\r\n          subtotal,\r\n          taxAmount,\r\n          shippingFee,\r\n        };\r\n\r\n        console.log(\"Sending payment data:\", paymentData);\r\n\r\n        try {\r\n          const result = await paymobService.initializePayment(\r\n            paymentData,\r\n            userSession\r\n          );\r\n          console.log(\"Result from initializePayment:\", result);\r\n\r\n          // Check if we got a valid iframe URL\r\n          if (!result || !result.iframeUrl) {\r\n            throw new Error(\r\n              \"Payment gateway URL not received. Please try again later.\"\r\n            );\r\n          }\r\n\r\n          // Set the iframe URL\r\n          setIframeUrl(result.iframeUrl);\r\n          setIframeModalOpen(true);\r\n          onSuccess(); // ✅ Trigger success popup in parent\r\n          resetCart();\r\n          console.log(\"Setting iframe URL to:\", result.iframeUrl);\r\n        } catch (paymentError) {\r\n          console.error(\"Payment initialization failed:\", paymentError);\r\n          throw new Error(\r\n            paymentError.message ||\r\n              \"Failed to connect to payment gateway. Please try again later.\"\r\n          );\r\n        }\r\n      } else if (paymentMethod === \"cod\") {\r\n        // Handle Cash on Delivery\r\n        console.log(\"COD selected, calling onSubmit()\");\r\n        await onSubmit();\r\n        console.log(\"onSubmit finished, setting showSuccessPopup\");\r\n        onSuccess(); // ✅ Trigger success popup in parent\r\n      }\r\n    } catch (error) {\r\n      setPaymentError(\r\n        error.message || \"Payment initialization failed. Please try again.\"\r\n      );\r\n      if (onFailed) onFailed(); // <-- show the failed popup\r\n\r\n      console.error(\"Payment error details:\", {\r\n        error: error,\r\n        message: error.message,\r\n        stack: error.stack,\r\n        billData: billData,\r\n      });\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // const handleClosePopup = () => {\r\n  //   setShowSuccessPopup(false);\r\n  //   navigate(\"/\");\r\n  // };\r\n\r\n  const handleCloseIframeModal = () => {\r\n    setIframeModalOpen(false);\r\n    setIframeUrl(null); // This will unmount the iframe\r\n  };\r\n  return (\r\n    <Box className=\"paymentmethod-container\">\r\n      <Box className=\"paymentmethod-firstrow-firstcolumn\">\r\n        <FormControl fullWidth>\r\n          <RadioGroup\r\n            value={paymentMethod}\r\n            onChange={handlePaymentMethodChange}\r\n          >\r\n            {/* Card Payment Option */}\r\n            <FormControlLabel\r\n              value=\"card\"\r\n              control={<Radio />}\r\n              label={<span className=\"horizon-font\">Pay with Card</span>}\r\n              sx={{ borderBottom: \"1px solid black\", pb: 1 }}\r\n            />\r\n            {/* Display payment errors prominently */}\r\n            {paymentError && (\r\n              <Box\r\n                sx={{\r\n                  p: 2,\r\n                  mb: 2,\r\n                  bgcolor: \"rgba(244, 67, 54, 0.1)\",\r\n                  color: \"#f44336\",\r\n                  borderRadius: \"8px\",\r\n                  borderLeft: \"4px solid #f44336\",\r\n                  animation: \"fadeIn 0.3s ease\",\r\n                }}\r\n              >\r\n                <Typography variant=\"body1\">{paymentError}</Typography>\r\n                <Typography variant=\"body2\" sx={{ mt: 1, fontSize: \"0.85rem\" }}>\r\n                  If this problem persists, please contact our support team.\r\n                </Typography>\r\n              </Box>\r\n            )}\r\n\r\n            {/* Paymob iframe for card payments */}\r\n            {paymentMethod === \"card\" && iframeUrl && (\r\n              <Box className=\"payment-iframe-container\">\r\n                <Typography\r\n                  variant=\"subtitle2\"\r\n                  sx={{ p: 1, bgcolor: \"#f5f5f5\" }}\r\n                >\r\n                  Payment Gateway\r\n                </Typography>\r\n                <iframe\r\n                  src={iframeUrl}\r\n                  style={{\r\n                    width: \"100%\",\r\n                    height: \"600px\",\r\n                    border: \"none\",\r\n                    display: \"block\",\r\n                  }}\r\n                  allow=\"camera; microphone; accelerometer; gyroscope; payment\"\r\n                  allowFullScreen\r\n                  title=\"Paymob Payment\"\r\n                  id=\"paymob-iframe\"\r\n                  onLoad={() => console.log(\"Iframe loaded\")}\r\n                  onError={(e) => console.error(\"Iframe error:\", e)}\r\n                  referrerPolicy=\"origin\"\r\n                  sandbox=\"allow-forms allow-scripts allow-same-origin allow-top-navigation allow-popups\"\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            {/* Payment buttons */}\r\n            {paymentMethod === \"card\" && (\r\n              <Box sx={{ mt: 3, textAlign: \"center\" }}>\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  className=\"montserrat-font\"\r\n                  onClick={handlePayNow}\r\n                  disabled={isProcessing || iframeUrl}\r\n                  sx={{\r\n                    mt: 2,\r\n                    backgroundColor: \"#6B7B58\",\r\n                    \"&:hover\": { backgroundColor: \"#5a6a47\" },\r\n                    display: iframeUrl ? \"none\" : \"inline-flex\",\r\n                  }}\r\n                >\r\n                  {isProcessing ? (\r\n                    <CircularProgress size={24} color=\"inherit\" />\r\n                  ) : (\r\n                    \"Pay Now\"\r\n                  )}\r\n                </Button>\r\n                {isProcessing && !iframeUrl && (\r\n                  <Typography sx={{ mt: 2 }}>\r\n                    Preparing payment gateway...\r\n                  </Typography>\r\n                )}\r\n              </Box>\r\n            )}\r\n\r\n            {/* COD button */}\r\n            {paymentMethod === \"cod\" && (\r\n              <Box sx={{ mt: 3, textAlign: \"center\" }}>\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  className=\"montserrat-font\"\r\n                  onClick={handlePayNow}\r\n                  sx={{\r\n                    mt: 2,\r\n                    backgroundColor: \"#6B7B58\",\r\n                    \"&:hover\": { backgroundColor: \"#5a6a47\" },\r\n                  }}\r\n                >\r\n                  Complete Order\r\n                </Button>\r\n              </Box>\r\n            )}\r\n            {/* Cash on Delivery Option */}\r\n            {showCOD ? (\r\n              <FormControlLabel\r\n                value=\"cod\"\r\n                control={<Radio />}\r\n                label={\r\n                  <span className=\"montserrat-font\">Cash on Delivery</span>\r\n                }\r\n                sx={{ pt: 1 }}\r\n              />\r\n            ) : (\r\n              <Box\r\n                sx={{\r\n                  padding: \"8px 0\",\r\n                  color: \"#888\",\r\n                  fontStyle: \"italic\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"8px\",\r\n                  pt: 1,\r\n                }}\r\n                className=\"montserrat-font\"\r\n              >\r\n                <span\r\n                  style={{\r\n                    width: \"24px\",\r\n                    height: \"24px\",\r\n                    display: \"inline-block\",\r\n                  }}\r\n                ></span>\r\n                Cash on Delivery unavailable for orders over 10,000 EGP\r\n              </Box>\r\n            )}\r\n          </RadioGroup>\r\n        </FormControl>\r\n      </Box>\r\n\r\n      <BillSummary\r\n        cartItems={billData.cartItems}\r\n        subtotal={billData.subtotal}\r\n        shippingFee={billData.shippingFee}\r\n        total={billData.total}\r\n      />\r\n      <Dialog\r\n        open={iframeModalOpen}\r\n        onClose={handleCloseIframeModal}\r\n        maxWidth=\"md\"\r\n        fullWidth\r\n        PaperProps={{\r\n          sx: {\r\n            borderRadius: 4,\r\n            background: \"#fff\",\r\n            boxShadow: \"0 8px 32px rgba(0,0,0,0.18)\",\r\n            p: 2,\r\n          },\r\n        }}\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          Complete Payment\r\n          <IconButton onClick={handleCloseIframeModal}>\r\n            <CloseIcon />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <Box sx={{ p: 2 }}>\r\n          <iframe\r\n            src={iframeUrl}\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"600px\",\r\n              border: \"none\",\r\n              display: \"block\",\r\n            }}\r\n            allow=\"camera; microphone; accelerometer; gyroscope; payment\"\r\n            allowFullScreen\r\n            title=\"Paymob Payment\"\r\n            id=\"paymob-iframe\"\r\n            referrerPolicy=\"origin\"\r\n            sandbox=\"allow-forms allow-scripts allow-same-origin allow-top-navigation allow-popups\"\r\n          />\r\n        </Box>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default PaymentForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,EAChBC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,WAAWA,CAAC;EACnBC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,SAAS;EACTC,WAAW;EACXC,QAAQ;EACRC,QAAQ;EACRC,MAAM,GAAG,CAAC;AACZ,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAACyB,WAAW,CAACK,aAAa,CAAC;EAC7E,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEwC;EAAY,CAAC,GAAGzB,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM2C,QAAQ,GAAG1B,WAAW,CAAC,CAAC;;EAE9B;EACAhB,SAAS,CAAC,MAAM;IACd,MAAM2C,YAAY,GAAG,IAAIC,eAAe,CAACF,QAAQ,CAACG,MAAM,CAAC;IACzD,MAAMC,OAAO,GAAGH,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IACzC,MAAMC,MAAM,GAAGL,YAAY,CAACI,GAAG,CAAC,QAAQ,CAAC;IAEzC,IAAID,OAAO,IAAIE,MAAM,KAAK,SAAS,EAAE;MACnC3B,SAAS,CAAC,CAAC,CAAC,CAAC;MACb;MACA4B,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAEJ,MAAM,CAACP,QAAQ,CAACY,QAAQ,CAAC;IAC3E;EACF,CAAC,EAAE,CAACZ,QAAQ,EAAErB,SAAS,CAAC,CAAC;EACzBrB,SAAS,CAAC,MAAM;IACd,IAAI0B,QAAQ,IAAIA,QAAQ,CAAC6B,KAAK,EAAE;MAC9BvB,UAAU,CAACN,QAAQ,CAAC6B,KAAK,IAAI,KAAK,CAAC;;MAEnC;MACA,IAAI1B,aAAa,KAAK,KAAK,IAAIH,QAAQ,CAAC6B,KAAK,GAAG,KAAK,EAAE;QACrDzB,gBAAgB,CAAC,MAAM,CAAC;QACxBL,QAAQ,CAAC;UAAE,GAAGD,WAAW;UAAEK,aAAa,EAAE;QAAO,CAAC,CAAC;MACrD;IACF;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEF,WAAW,EAAEC,QAAQ,EAAEI,aAAa,CAAC,CAAC;EAEpD7B,SAAS,CAAC,MAAM;IACd,MAAMwD,aAAa,GAAIC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,MAAM,KAAK,2BAA2B,EAAE;MAClD,MAAM;QAAEC,OAAO;QAAEC;MAAc,CAAC,GAAGH,KAAK,CAACI,IAAI;MAC7C,IAAIF,OAAO,EAAE;QACXvC,QAAQ,CAAC,CAAC;QACVC,SAAS,CAAC,CAAC,CAAC,CAAC;QACbE,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,MAAM,IAAIqC,aAAa,EAAE;QACxBxB,eAAe,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC;IACDa,MAAM,CAACa,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACjD,OAAO,MAAMP,MAAM,CAACc,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;EACnE,CAAC,EAAE,CAACpC,QAAQ,EAAEC,SAAS,EAAEE,SAAS,CAAC,CAAC;EAEpC,MAAMyC,yBAAyB,GAAIC,CAAC,IAAK;IACvC,MAAMC,MAAM,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IAC7BtC,gBAAgB,CAACoC,MAAM,CAAC;IACxBzC,QAAQ,CAAC;MAAE,GAAGD,WAAW;MAAEK,aAAa,EAAEqC;IAAO,CAAC,CAAC;IACnD9B,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFnC,eAAe,CAAC,IAAI,CAAC;MACrBE,eAAe,CAAC,IAAI,CAAC;MAErB,IAAIP,aAAa,KAAK,MAAM,EAAE;QAC5B,IAAI,EAACH,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE4C,cAAc,GAAE;UAC7B,MAAM,IAAIC,KAAK,CACb,yEACF,CAAC;QACH;QAEA,MAAMD,cAAc,GAAG5C,QAAQ,CAAC4C,cAAc;QAC9C,MAAME,eAAe,GAAG9C,QAAQ,CAAC8C,eAAe;QAChD,MAAMC,cAAc,GAAG;UACrBC,UAAU,EAAE,YAAY;UACxBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,OAAO;UACdC,YAAY,EAAE,cAAc;UAC5BC,MAAM,EAAE,gBAAgB;UACxBC,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE;QACX,CAAC;QAED,MAAMC,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACV,cAAc,CAAC,CACjDW,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,KAAK,CAACf,cAAc,CAACe,GAAG,CAAC,CAAC,CACvCC,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKA,KAAK,CAAC;QAE7B,IAAIP,aAAa,CAACQ,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM,IAAIlB,KAAK,CACb,sDAAsDU,aAAa,CAACS,IAAI,CACtE,IACF,CAAC,EACH,CAAC;QACH;;QAEA;QACA,IAAI,CAAChE,QAAQ,CAACiE,SAAS,IAAIjE,QAAQ,CAACiE,SAAS,CAACF,MAAM,KAAK,CAAC,EAAE;UAC1D,MAAM,IAAIlB,KAAK,CAAC,oDAAoD,CAAC;QACvE;;QAEA;QACA,MAAMqB,YAAY,GAAGlE,QAAQ,CAACiE,SAAS,CAACP,MAAM,CAC3CS,IAAI,IAAK,CAACA,IAAI,CAACC,IAAI,IAAI,OAAOD,IAAI,CAACE,SAAS,KAAK,WACpD,CAAC;QAED,IAAIH,YAAY,CAACH,MAAM,GAAG,CAAC,EAAE;UAC3BO,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEL,YAAY,CAAC;UAClD,MAAM,IAAIrB,KAAK,CACb,2EACF,CAAC;QACH;QACA;QACA,MAAM2B,iBAAiB,GAAGxE,QAAQ,CAACiE,SAAS,CAACL,GAAG,CAAEO,IAAI,IAAK;UACzD;UACA,MAAMM,SAAS,GAAGN,IAAI,CAACM,SAAS,IAAIN,IAAI,CAACO,EAAE;;UAE3C;UACA,IAAIC,OAAO;UACX,IAAI,OAAOR,IAAI,CAACQ,OAAO,KAAK,QAAQ,IAAIR,IAAI,CAACQ,OAAO,KAAK,IAAI,EAAE;YAC7DA,OAAO,GAAGR,IAAI,CAACQ,OAAO,CAACC,GAAG,IAAIT,IAAI,CAACQ,OAAO,CAACD,EAAE;UAC/C,CAAC,MAAM;YACLC,OAAO,GAAGR,IAAI,CAACQ,OAAO;UACxB;UAEA,OAAO;YACL,GAAGR,IAAI;YACPM,SAAS;YACTE;UACF,CAAC;QACH,CAAC,CAAC;QAEFL,OAAO,CAACO,GAAG,CAAC,sCAAsC,EAAEL,iBAAiB,CAAC;QACtEF,OAAO,CAACO,GAAG,CAAC,mCAAmC,EAAEjC,cAAc,CAAC;QAChE0B,OAAO,CAACO,GAAG,CAAC,oCAAoC,EAAE/B,eAAe,CAAC;QAClE,MAAMgC,QAAQ,GAAG9E,QAAQ,CAAC8E,QAAQ,IAAI,CAAC;QACvC,MAAMC,WAAW,GAAG/E,QAAQ,CAAC+E,WAAW,IAAI,CAAC;QAC7C,MAAMC,SAAS,GAAG,CAAC,CAACF,QAAQ,GAAG,IAAI,EAAEG,OAAO,CAAC,CAAC,CAAC;QAC/C,MAAMC,UAAU,GAAG,CAAC,CAACJ,QAAQ,GAAGE,SAAS,GAAGD,WAAW,EAAEE,OAAO,CAAC,CAAC,CAAC;QACnE,MAAMnF,WAAW,GAAG;UAClB+B,KAAK,EAAEqD,UAAU;UACjBtC,cAAc,EAAEA,cAAc;UAC9BqB,SAAS,EAAEO,iBAAiB,IAAI,EAAE;UAClC1B,eAAe,EAAEA,eAAe,IAAI,CAAC,CAAC;UACtCgC,QAAQ;UACRE,SAAS;UACTD;QACF,CAAC;QAEDT,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAE/E,WAAW,CAAC;QAEjD,IAAI;UACF,MAAMqF,MAAM,GAAG,MAAMhG,aAAa,CAACiG,iBAAiB,CAClDtF,WAAW,EACXe,WACF,CAAC;UACDyD,OAAO,CAACO,GAAG,CAAC,gCAAgC,EAAEM,MAAM,CAAC;;UAErD;UACA,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACxE,SAAS,EAAE;YAChC,MAAM,IAAIkC,KAAK,CACb,2DACF,CAAC;UACH;;UAEA;UACAjC,YAAY,CAACuE,MAAM,CAACxE,SAAS,CAAC;UAC9BI,kBAAkB,CAAC,IAAI,CAAC;UACxBpB,SAAS,CAAC,CAAC,CAAC,CAAC;UACbE,SAAS,CAAC,CAAC;UACXyE,OAAO,CAACO,GAAG,CAAC,wBAAwB,EAAEM,MAAM,CAACxE,SAAS,CAAC;QACzD,CAAC,CAAC,OAAOF,YAAY,EAAE;UACrB6D,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAE9D,YAAY,CAAC;UAC7D,MAAM,IAAIoC,KAAK,CACbpC,YAAY,CAAC4E,OAAO,IAClB,+DACJ,CAAC;QACH;MACF,CAAC,MAAM,IAAIlF,aAAa,KAAK,KAAK,EAAE;QAClC;QACAmE,OAAO,CAACO,GAAG,CAAC,kCAAkC,CAAC;QAC/C,MAAMnF,QAAQ,CAAC,CAAC;QAChB4E,OAAO,CAACO,GAAG,CAAC,6CAA6C,CAAC;QAC1DlF,SAAS,CAAC,CAAC,CAAC,CAAC;MACf;IACF,CAAC,CAAC,OAAO4E,KAAK,EAAE;MACd7D,eAAe,CACb6D,KAAK,CAACc,OAAO,IAAI,kDACnB,CAAC;MACD,IAAIzF,QAAQ,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAE1B0E,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAE;QACtCA,KAAK,EAAEA,KAAK;QACZc,OAAO,EAAEd,KAAK,CAACc,OAAO;QACtBC,KAAK,EAAEf,KAAK,CAACe,KAAK;QAClBtF,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRQ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA;EACA;EACA;;EAEA,MAAM+E,sBAAsB,GAAGA,CAAA,KAAM;IACnCxE,kBAAkB,CAAC,KAAK,CAAC;IACzBH,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;EACtB,CAAC;EACD,oBACEpB,OAAA,CAACjB,GAAG;IAACiH,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCjG,OAAA,CAACjB,GAAG;MAACiH,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDjG,OAAA,CAAChB,WAAW;QAACkH,SAAS;QAAAD,QAAA,eACpBjG,OAAA,CAACf,UAAU;UACTiE,KAAK,EAAEvC,aAAc;UACrBJ,QAAQ,EAAEuC,yBAA0B;UAAAmD,QAAA,gBAGpCjG,OAAA,CAACb,gBAAgB;YACf+D,KAAK,EAAC,MAAM;YACZiD,OAAO,eAAEnG,OAAA,CAACd,KAAK;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBjC,KAAK,eAAEtE,OAAA;cAAMgG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAC3DC,EAAE,EAAE;cAAEC,YAAY,EAAE,iBAAiB;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,EAEDtF,YAAY,iBACXjB,OAAA,CAACjB,GAAG;YACFyH,EAAE,EAAE;cACFG,CAAC,EAAE,CAAC;cACJC,EAAE,EAAE,CAAC;cACLC,OAAO,EAAE,wBAAwB;cACjCC,KAAK,EAAE,SAAS;cAChBC,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE,mBAAmB;cAC/BC,SAAS,EAAE;YACb,CAAE;YAAAhB,QAAA,gBAEFjG,OAAA,CAACV,UAAU;cAAC4H,OAAO,EAAC,OAAO;cAAAjB,QAAA,EAAEhF;YAAY;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACvDvG,OAAA,CAACV,UAAU;cAAC4H,OAAO,EAAC,OAAO;cAACV,EAAE,EAAE;gBAAEW,EAAE,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAEhE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,EAGA5F,aAAa,KAAK,MAAM,IAAIQ,SAAS,iBACpCnB,OAAA,CAACjB,GAAG;YAACiH,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCjG,OAAA,CAACV,UAAU;cACT4H,OAAO,EAAC,WAAW;cACnBV,EAAE,EAAE;gBAAEG,CAAC,EAAE,CAAC;gBAAEE,OAAO,EAAE;cAAU,CAAE;cAAAZ,QAAA,EAClC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvG,OAAA;cACEqH,GAAG,EAAElG,SAAU;cACfmG,KAAK,EAAE;gBACLC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,OAAO;gBACfC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE;cACX,CAAE;cACFC,KAAK,EAAC,uDAAuD;cAC7DC,eAAe;cACfzF,KAAK,EAAC,gBAAgB;cACtB+C,EAAE,EAAC,eAAe;cAClB2C,MAAM,EAAEA,CAAA,KAAM/C,OAAO,CAACO,GAAG,CAAC,eAAe,CAAE;cAC3CyC,OAAO,EAAG/E,CAAC,IAAK+B,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEhC,CAAC,CAAE;cAClDgF,cAAc,EAAC,QAAQ;cACvBC,OAAO,EAAC;YAA+E;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGA5F,aAAa,KAAK,MAAM,iBACvBX,OAAA,CAACjB,GAAG;YAACyH,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEc,SAAS,EAAE;YAAS,CAAE;YAAAhC,QAAA,gBACtCjG,OAAA,CAACZ,MAAM;cACL8H,OAAO,EAAC,WAAW;cACnBJ,KAAK,EAAC,SAAS;cACfd,SAAS,EAAC,iBAAiB;cAC3BkC,OAAO,EAAE/E,YAAa;cACtBgF,QAAQ,EAAEpH,YAAY,IAAII,SAAU;cACpCqF,EAAE,EAAE;gBACFW,EAAE,EAAE,CAAC;gBACLiB,eAAe,EAAE,SAAS;gBAC1B,SAAS,EAAE;kBAAEA,eAAe,EAAE;gBAAU,CAAC;gBACzCV,OAAO,EAAEvG,SAAS,GAAG,MAAM,GAAG;cAChC,CAAE;cAAA8E,QAAA,EAEDlF,YAAY,gBACXf,OAAA,CAACX,gBAAgB;gBAACgJ,IAAI,EAAE,EAAG;gBAACvB,KAAK,EAAC;cAAS;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAE9C;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EACRxF,YAAY,IAAI,CAACI,SAAS,iBACzBnB,OAAA,CAACV,UAAU;cAACkH,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE,CAAE;cAAAlB,QAAA,EAAC;YAE3B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA5F,aAAa,KAAK,KAAK,iBACtBX,OAAA,CAACjB,GAAG;YAACyH,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEc,SAAS,EAAE;YAAS,CAAE;YAAAhC,QAAA,eACtCjG,OAAA,CAACZ,MAAM;cACL8H,OAAO,EAAC,WAAW;cACnBJ,KAAK,EAAC,SAAS;cACfd,SAAS,EAAC,iBAAiB;cAC3BkC,OAAO,EAAE/E,YAAa;cACtBqD,EAAE,EAAE;gBACFW,EAAE,EAAE,CAAC;gBACLiB,eAAe,EAAE,SAAS;gBAC1B,SAAS,EAAE;kBAAEA,eAAe,EAAE;gBAAU;cAC1C,CAAE;cAAAnC,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEA1F,OAAO,gBACNb,OAAA,CAACb,gBAAgB;YACf+D,KAAK,EAAC,KAAK;YACXiD,OAAO,eAAEnG,OAAA,CAACd,KAAK;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBjC,KAAK,eACHtE,OAAA;cAAMgG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACzD;YACDC,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,gBAEFvG,OAAA,CAACjB,GAAG;YACFyH,EAAE,EAAE;cACF+B,OAAO,EAAE,OAAO;cAChBzB,KAAK,EAAE,MAAM;cACb0B,SAAS,EAAE,QAAQ;cACnBd,OAAO,EAAE,MAAM;cACfe,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,KAAK;cACVJ,EAAE,EAAE;YACN,CAAE;YACFtC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAE3BjG,OAAA;cACEsH,KAAK,EAAE;gBACLC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,OAAO,EAAE;cACX;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,2DAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAENvG,OAAA,CAACN,WAAW;MACV+E,SAAS,EAAEjE,QAAQ,CAACiE,SAAU;MAC9Ba,QAAQ,EAAE9E,QAAQ,CAAC8E,QAAS;MAC5BC,WAAW,EAAE/E,QAAQ,CAAC+E,WAAY;MAClClD,KAAK,EAAE7B,QAAQ,CAAC6B;IAAM;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eACFvG,OAAA,CAACT,MAAM;MACLoJ,IAAI,EAAErH,eAAgB;MACtBsH,OAAO,EAAE7C,sBAAuB;MAChC8C,QAAQ,EAAC,IAAI;MACb3C,SAAS;MACT4C,UAAU,EAAE;QACVtC,EAAE,EAAE;UACFO,YAAY,EAAE,CAAC;UACfgC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,6BAA6B;UACxCrC,CAAC,EAAE;QACL;MACF,CAAE;MAAAV,QAAA,gBAEFjG,OAAA,CAACR,WAAW;QACVgH,EAAE,EAAE;UACFkB,OAAO,EAAE,MAAM;UACfuB,cAAc,EAAE,eAAe;UAC/BR,UAAU,EAAE;QACd,CAAE;QAAAxC,QAAA,GACH,kBAEC,eAAAjG,OAAA,CAACP,UAAU;UAACyI,OAAO,EAAEnC,sBAAuB;UAAAE,QAAA,eAC1CjG,OAAA,CAACH,SAAS;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdvG,OAAA,CAACjB,GAAG;QAACyH,EAAE,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAAAV,QAAA,eAChBjG,OAAA;UACEqH,GAAG,EAAElG,SAAU;UACfmG,KAAK,EAAE;YACLC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACfC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;UACX,CAAE;UACFC,KAAK,EAAC,uDAAuD;UAC7DC,eAAe;UACfzF,KAAK,EAAC,gBAAgB;UACtB+C,EAAE,EAAC,eAAe;UAClB6C,cAAc,EAAC,QAAQ;UACvBC,OAAO,EAAC;QAA+E;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAC7F,EAAA,CAlaQT,WAAW;EAAA,QAeML,OAAO,EAEdE,WAAW;AAAA;AAAAoJ,EAAA,GAjBrBjJ,WAAW;AAoapB,eAAeA,WAAW;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}