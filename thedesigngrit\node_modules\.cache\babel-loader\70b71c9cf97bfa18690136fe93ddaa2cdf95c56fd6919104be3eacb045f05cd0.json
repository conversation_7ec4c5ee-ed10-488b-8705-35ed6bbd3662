{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\home.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useRef, useState, useEffect, lazy, Suspense } from \"react\";\nimport { Box } from \"@mui/material\";\nimport Header from \"../Components/navBar\";\nimport OrderSentPopup from \"../Components/successMsgs/orderSubmit\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShopByCategory = /*#__PURE__*/lazy(_c = () => import(\"../Components/home/<USER>\"));\n_c2 = ShopByCategory;\nconst ExploreConcepts = /*#__PURE__*/lazy(_c3 = () => import(\"../Components/home/<USER>\"));\n_c4 = ExploreConcepts;\nconst SustainabilitySection = /*#__PURE__*/lazy(_c5 = () => import(\"../Components/home/<USER>\"));\n_c6 = SustainabilitySection;\nconst PartnersSection = /*#__PURE__*/lazy(_c7 = () => import(\"../Components/home/<USER>\"));\n_c8 = PartnersSection;\nconst ProductSlider = /*#__PURE__*/lazy(_c9 = () => import(\"../Components/home/<USER>\"));\n_c10 = ProductSlider;\nconst Footer = /*#__PURE__*/lazy(_c11 = () => import(\"../Components/Footer\"));\n_c12 = Footer;\nconst ScrollAnimation = /*#__PURE__*/lazy(_c13 = () => import(\"../Context/scrollingAnimation\"));\n_c14 = ScrollAnimation;\nconst videos = [{\n  webm: \"/Assets/Video-hero/herovideo2.webm\",\n  mp4: \"/Assets/Video-hero/herovideo2.mp4\"\n}, {\n  webm: \"/Assets/Video-hero/herovideo5.webm\",\n  mp4: \"/Assets/Video-hero/herovideo5.mp4\"\n}, {\n  webm: \"/Assets/Video-hero/herovideo4.webm\",\n  mp4: \"/Assets/Video-hero/herovideo4.mp4\"\n}];\nconst isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\nconst posterImages = [isSafari ? \"/Assets/Video-hero/poster.jpg\" : \"/Assets/Video-hero/poster.avif\", isSafari ? \"/Assets/Video-hero/poster5.jpg\" : \"/Assets/Video-hero/poster5.avif\", isSafari ? \"/Assets/Video-hero/poster4.jpg\" : \"/Assets/Video-hero/poster4.avif\"];\nconst useIsMobile = () => {\n  _s();\n  const [isMobile, setIsMobile] = useState(false);\n  useEffect(() => {\n    const check = () => setIsMobile(window.innerWidth <= 768);\n    check();\n    window.addEventListener(\"resize\", check);\n    return () => window.removeEventListener(\"resize\", check);\n  }, []);\n  return isMobile;\n};\n_s(useIsMobile, \"0VTTNJATKABQPGLm9RVT0tKGUgU=\");\nfunction Home() {\n  _s2();\n  const fgVideoRef = useRef(null);\n  const heroSectionRef = useRef(null);\n  const isMobile = useIsMobile();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [progress, setProgress] = useState(0);\n  const [showVideo] = useState(true);\n  const [isHeroVisible, setIsHeroVisible] = useState(true); // 👈 track visibility\n  useEffect(() => {\n    const observer = new IntersectionObserver(([entry]) => {\n      setIsHeroVisible(entry.isIntersecting);\n    }, {\n      threshold: 0.3\n    } // 30% of hero must be visible to be \"active\"\n    );\n    const currentHeroRef = heroSectionRef.current;\n    if (currentHeroRef) {\n      observer.observe(currentHeroRef);\n    }\n    return () => {\n      // Use the stored variable in cleanup\n      if (currentHeroRef) {\n        observer.unobserve(currentHeroRef);\n      }\n    };\n  }, []);\n  useEffect(() => {\n    if (!isHeroVisible) return;\n    const interval = setInterval(() => {\n      setCurrentVideoIndex(prevIndex => (prevIndex + 1) % videos.length);\n    }, 8000);\n    return () => clearInterval(interval);\n  }, [isHeroVisible]);\n  // 👇 Handle playing/pausing video based on visibility\n  useEffect(() => {\n    const video = fgVideoRef.current;\n    if (video) {\n      video.load();\n      if (isHeroVisible) {\n        video.play().catch(err => console.error(\"Autoplay failed:\", err));\n      } else {\n        video.pause();\n      }\n    }\n  }, [currentVideoIndex, isHeroVisible]);\n  const handleTimeUpdate = () => {\n    const video = fgVideoRef.current;\n    if (video && video.duration) {\n      setProgress(video.currentTime / video.duration * 100);\n    }\n  };\n  const handleDotClick = index => {\n    setCurrentVideoIndex(index);\n    setProgress(0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"background-layer\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: posterImages[currentVideoIndex],\n        alt: `Hero background ${currentVideoIndex}`,\n        className: \"hero-video-element\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-home-section\",\n      ref: heroSectionRef,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-video\",\n        children: [showVideo ? isMobile ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: posterImages[currentVideoIndex],\n          alt: `Hero background ${currentVideoIndex}`,\n          className: \"hero-video-element\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"video\", {\n          ref: fgVideoRef,\n          className: \"hero-video-element\",\n          poster: posterImages[currentVideoIndex],\n          autoPlay: true,\n          muted: true,\n          playsInline: true,\n          preload: \"auto\",\n          onTimeUpdate: handleTimeUpdate,\n          onEnded: () => {\n            setCurrentVideoIndex(prevIndex => (prevIndex + 1) % videos.length);\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"source\", {\n            src: videos[currentVideoIndex].mp4,\n            type: \"video/mp4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"source\", {\n            src: videos[currentVideoIndex].webm,\n            type: \"video/webm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this)]\n        }, currentVideoIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n          src: posterImages[currentVideoIndex],\n          alt: \"Hero placeholder\",\n          className: \"hero-video-element\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-progress-container\",\n          children: videos.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `video-progress-dot ${currentVideoIndex === index ? \"active\" : \"circle\"}`,\n            onClick: () => handleDotClick(index),\n            children: currentVideoIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-progress-bar\",\n              style: {\n                width: `${progress}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: null,\n      children: [/*#__PURE__*/_jsxDEV(ScrollAnimation, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"concept-title\",\n          children: /*#__PURE__*/_jsxDEV(ExploreConcepts, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollAnimation, {\n        children: /*#__PURE__*/_jsxDEV(ShopByCategory, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollAnimation, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: \"100%\"\n          },\n          children: /*#__PURE__*/_jsxDEV(ProductSlider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollAnimation, {\n        children: /*#__PURE__*/_jsxDEV(SustainabilitySection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollAnimation, {\n        children: /*#__PURE__*/_jsxDEV(PartnersSection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OrderSentPopup, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n}\n_s2(Home, \"RJW63/pfCS3xRXNZk5AM/Gr8A6o=\", false, function () {\n  return [useIsMobile];\n});\n_c15 = Home;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"ShopByCategory$lazy\");\n$RefreshReg$(_c2, \"ShopByCategory\");\n$RefreshReg$(_c3, \"ExploreConcepts$lazy\");\n$RefreshReg$(_c4, \"ExploreConcepts\");\n$RefreshReg$(_c5, \"SustainabilitySection$lazy\");\n$RefreshReg$(_c6, \"SustainabilitySection\");\n$RefreshReg$(_c7, \"PartnersSection$lazy\");\n$RefreshReg$(_c8, \"PartnersSection\");\n$RefreshReg$(_c9, \"ProductSlider$lazy\");\n$RefreshReg$(_c10, \"ProductSlider\");\n$RefreshReg$(_c11, \"Footer$lazy\");\n$RefreshReg$(_c12, \"Footer\");\n$RefreshReg$(_c13, \"ScrollAnimation$lazy\");\n$RefreshReg$(_c14, \"ScrollAnimation\");\n$RefreshReg$(_c15, \"Home\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "lazy", "Suspense", "Box", "Header", "OrderSentPopup", "jsxDEV", "_jsxDEV", "ShopByCategory", "_c", "_c2", "ExploreConcepts", "_c3", "_c4", "SustainabilitySection", "_c5", "_c6", "PartnersSection", "_c7", "_c8", "ProductSlider", "_c9", "_c10", "Footer", "_c11", "_c12", "ScrollAnimation", "_c13", "_c14", "videos", "webm", "mp4", "<PERSON><PERSON><PERSON><PERSON>", "test", "navigator", "userAgent", "posterImages", "useIsMobile", "_s", "isMobile", "setIsMobile", "check", "window", "innerWidth", "addEventListener", "removeEventListener", "Home", "_s2", "fgVideoRef", "heroSectionRef", "currentVideoIndex", "setCurrentVideoIndex", "progress", "setProgress", "showVideo", "isHeroVisible", "setIsHeroVisible", "observer", "IntersectionObserver", "entry", "isIntersecting", "threshold", "currentHeroRef", "current", "observe", "unobserve", "interval", "setInterval", "prevIndex", "length", "clearInterval", "video", "load", "play", "catch", "err", "console", "error", "pause", "handleTimeUpdate", "duration", "currentTime", "handleDotClick", "index", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "poster", "autoPlay", "muted", "playsInline", "preload", "onTimeUpdate", "onEnded", "type", "map", "_", "onClick", "style", "width", "fallback", "sx", "_c15", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/home.jsx"], "sourcesContent": ["import React, { useRef, useState, useEffect, lazy, Suspense } from \"react\";\r\nimport { Box } from \"@mui/material\";\r\nimport Header from \"../Components/navBar\";\r\nimport OrderSentPopup from \"../Components/successMsgs/orderSubmit\";\r\nconst ShopByCategory = lazy(() => import(\"../Components/home/<USER>\"));\r\nconst ExploreConcepts = lazy(() => import(\"../Components/home/<USER>\"));\r\nconst SustainabilitySection = lazy(() =>\r\n  import(\"../Components/home/<USER>\")\r\n);\r\nconst PartnersSection = lazy(() => import(\"../Components/home/<USER>\"));\r\nconst ProductSlider = lazy(() => import(\"../Components/home/<USER>\"));\r\nconst Footer = lazy(() => import(\"../Components/Footer\"));\r\nconst ScrollAnimation = lazy(() => import(\"../Context/scrollingAnimation\"));\r\n\r\nconst videos = [\r\n  {\r\n    webm: \"/Assets/Video-hero/herovideo2.webm\",\r\n    mp4: \"/Assets/Video-hero/herovideo2.mp4\",\r\n  },\r\n  {\r\n    webm: \"/Assets/Video-hero/herovideo5.webm\",\r\n    mp4: \"/Assets/Video-hero/herovideo5.mp4\",\r\n  },\r\n  {\r\n    webm: \"/Assets/Video-hero/herovideo4.webm\",\r\n    mp4: \"/Assets/Video-hero/herovideo4.mp4\",\r\n  },\r\n];\r\n\r\nconst isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\r\nconst posterImages = [\r\n  isSafari ? \"/Assets/Video-hero/poster.jpg\" : \"/Assets/Video-hero/poster.avif\",\r\n  isSafari\r\n    ? \"/Assets/Video-hero/poster5.jpg\"\r\n    : \"/Assets/Video-hero/poster5.avif\",\r\n  isSafari\r\n    ? \"/Assets/Video-hero/poster4.jpg\"\r\n    : \"/Assets/Video-hero/poster4.avif\",\r\n];\r\n\r\nconst useIsMobile = () => {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  useEffect(() => {\r\n    const check = () => setIsMobile(window.innerWidth <= 768);\r\n    check();\r\n    window.addEventListener(\"resize\", check);\r\n    return () => window.removeEventListener(\"resize\", check);\r\n  }, []);\r\n  return isMobile;\r\n};\r\n\r\nfunction Home() {\r\n  const fgVideoRef = useRef(null);\r\n  const heroSectionRef = useRef(null);\r\n  const isMobile = useIsMobile();\r\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\r\n  const [progress, setProgress] = useState(0);\r\n  const [showVideo] = useState(true);\r\n  const [isHeroVisible, setIsHeroVisible] = useState(true); // 👈 track visibility\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        setIsHeroVisible(entry.isIntersecting);\r\n      },\r\n      { threshold: 0.3 } // 30% of hero must be visible to be \"active\"\r\n    );\r\n    const currentHeroRef = heroSectionRef.current;\r\n\r\n    if (currentHeroRef) {\r\n      observer.observe(currentHeroRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored variable in cleanup\r\n      if (currentHeroRef) {\r\n        observer.unobserve(currentHeroRef);\r\n      }\r\n    };\r\n  }, []);\r\n  useEffect(() => {\r\n    if (!isHeroVisible) return;\r\n\r\n    const interval = setInterval(() => {\r\n      setCurrentVideoIndex((prevIndex) => (prevIndex + 1) % videos.length);\r\n    }, 8000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isHeroVisible]);\r\n  // 👇 Handle playing/pausing video based on visibility\r\n  useEffect(() => {\r\n    const video = fgVideoRef.current;\r\n    if (video) {\r\n      video.load();\r\n      if (isHeroVisible) {\r\n        video.play().catch((err) => console.error(\"Autoplay failed:\", err));\r\n      } else {\r\n        video.pause();\r\n      }\r\n    }\r\n  }, [currentVideoIndex, isHeroVisible]);\r\n\r\n  const handleTimeUpdate = () => {\r\n    const video = fgVideoRef.current;\r\n    if (video && video.duration) {\r\n      setProgress((video.currentTime / video.duration) * 100);\r\n    }\r\n  };\r\n\r\n  const handleDotClick = (index) => {\r\n    setCurrentVideoIndex(index);\r\n    setProgress(0);\r\n  };\r\n  return (\r\n    <div className=\"home\">\r\n      <div className=\"background-layer\">\r\n        <img\r\n          src={posterImages[currentVideoIndex]}\r\n          alt={`Hero background ${currentVideoIndex}`}\r\n          className=\"hero-video-element\"\r\n        />\r\n      </div>\r\n      <Header />\r\n\r\n      <div className=\"hero-home-section\" ref={heroSectionRef}>\r\n        <div className=\"hero-video\">\r\n          {showVideo ? (\r\n            isMobile ? (\r\n              <img\r\n                src={posterImages[currentVideoIndex]}\r\n                alt={`Hero background ${currentVideoIndex}`}\r\n                className=\"hero-video-element\"\r\n              />\r\n            ) : (\r\n              <video\r\n                key={currentVideoIndex}\r\n                ref={fgVideoRef}\r\n                className=\"hero-video-element\"\r\n                poster={posterImages[currentVideoIndex]}\r\n                autoPlay\r\n                muted\r\n                playsInline\r\n                preload=\"auto\"\r\n                onTimeUpdate={handleTimeUpdate}\r\n                onEnded={() => {\r\n                  setCurrentVideoIndex(\r\n                    (prevIndex) => (prevIndex + 1) % videos.length\r\n                  );\r\n                }}\r\n              >\r\n                <source src={videos[currentVideoIndex].mp4} type=\"video/mp4\" />\r\n                <source\r\n                  src={videos[currentVideoIndex].webm}\r\n                  type=\"video/webm\"\r\n                />\r\n              </video>\r\n            )\r\n          ) : (\r\n            <img\r\n              src={posterImages[currentVideoIndex]}\r\n              alt=\"Hero placeholder\"\r\n              className=\"hero-video-element\"\r\n            />\r\n          )}\r\n\r\n          <div className=\"video-progress-container\">\r\n            {videos.map((_, index) => (\r\n              <div\r\n                key={index}\r\n                className={`video-progress-dot ${\r\n                  currentVideoIndex === index ? \"active\" : \"circle\"\r\n                }`}\r\n                onClick={() => handleDotClick(index)}\r\n              >\r\n                {currentVideoIndex === index && (\r\n                  <div\r\n                    className=\"video-progress-bar\"\r\n                    style={{ width: `${progress}%` }}\r\n                  ></div>\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <Suspense fallback={null}>\r\n        <ScrollAnimation>\r\n          <Box className=\"concept-title\">\r\n            <ExploreConcepts />\r\n          </Box>\r\n        </ScrollAnimation>\r\n\r\n        <ScrollAnimation>\r\n          <ShopByCategory />\r\n        </ScrollAnimation>\r\n\r\n        <ScrollAnimation>\r\n          <Box sx={{ width: \"100%\" }}>\r\n            <ProductSlider />\r\n          </Box>\r\n        </ScrollAnimation>\r\n\r\n        <ScrollAnimation>\r\n          <SustainabilitySection />\r\n        </ScrollAnimation>\r\n\r\n        <ScrollAnimation>\r\n          <PartnersSection />\r\n        </ScrollAnimation>\r\n        <Footer />\r\n      </Suspense>\r\n      <OrderSentPopup />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AAC1E,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,cAAc,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACnE,MAAMC,cAAc,gBAAGP,IAAI,CAAAQ,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAACC,GAAA,GAAnEF,cAAc;AACpB,MAAMG,eAAe,gBAAGV,IAAI,CAAAW,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAAnEF,eAAe;AACrB,MAAMG,qBAAqB,gBAAGb,IAAI,CAAAc,GAAA,GAACA,CAAA,KACjC,MAAM,CAAC,mCAAmC,CAC5C,CAAC;AAACC,GAAA,GAFIF,qBAAqB;AAG3B,MAAMG,eAAe,gBAAGhB,IAAI,CAAAiB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAACC,GAAA,GAApEF,eAAe;AACrB,MAAMG,aAAa,gBAAGnB,IAAI,CAAAoB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,IAAA,GAApEF,aAAa;AACnB,MAAMG,MAAM,gBAAGtB,IAAI,CAAAuB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAApDF,MAAM;AACZ,MAAMG,eAAe,gBAAGzB,IAAI,CAAA0B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,IAAA,GAAtEF,eAAe;AAErB,MAAMG,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,oCAAoC;EAC1CC,GAAG,EAAE;AACP,CAAC,EACD;EACED,IAAI,EAAE,oCAAoC;EAC1CC,GAAG,EAAE;AACP,CAAC,EACD;EACED,IAAI,EAAE,oCAAoC;EAC1CC,GAAG,EAAE;AACP,CAAC,CACF;AAED,MAAMC,QAAQ,GAAG,gCAAgC,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;AAC3E,MAAMC,YAAY,GAAG,CACnBJ,QAAQ,GAAG,+BAA+B,GAAG,gCAAgC,EAC7EA,QAAQ,GACJ,gCAAgC,GAChC,iCAAiC,EACrCA,QAAQ,GACJ,gCAAgC,GAChC,iCAAiC,CACtC;AAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC/CC,SAAS,CAAC,MAAM;IACd,MAAMyC,KAAK,GAAGA,CAAA,KAAMD,WAAW,CAACE,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACzDF,KAAK,CAAC,CAAC;IACPC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,KAAK,CAAC;IACxC,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,KAAK,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,QAAQ;AACjB,CAAC;AAACD,EAAA,CATID,WAAW;AAWjB,SAASS,IAAIA,CAAA,EAAG;EAAAC,GAAA;EACd,MAAMC,UAAU,GAAGlD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmD,cAAc,GAAGnD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyC,QAAQ,GAAGF,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuD,SAAS,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAClC,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1DC,SAAS,CAAC,MAAM;IACd,MAAMyD,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACXH,gBAAgB,CAACG,KAAK,CAACC,cAAc,CAAC;IACxC,CAAC,EACD;MAAEC,SAAS,EAAE;IAAI,CAAC,CAAC;IACrB,CAAC;IACD,MAAMC,cAAc,GAAGb,cAAc,CAACc,OAAO;IAE7C,IAAID,cAAc,EAAE;MAClBL,QAAQ,CAACO,OAAO,CAACF,cAAc,CAAC;IAClC;IAEA,OAAO,MAAM;MACX;MACA,IAAIA,cAAc,EAAE;QAClBL,QAAQ,CAACQ,SAAS,CAACH,cAAc,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN9D,SAAS,CAAC,MAAM;IACd,IAAI,CAACuD,aAAa,EAAE;IAEpB,MAAMW,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjChB,oBAAoB,CAAEiB,SAAS,IAAK,CAACA,SAAS,GAAG,CAAC,IAAIvC,MAAM,CAACwC,MAAM,CAAC;IACtE,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACX,aAAa,CAAC,CAAC;EACnB;EACAvD,SAAS,CAAC,MAAM;IACd,MAAMuE,KAAK,GAAGvB,UAAU,CAACe,OAAO;IAChC,IAAIQ,KAAK,EAAE;MACTA,KAAK,CAACC,IAAI,CAAC,CAAC;MACZ,IAAIjB,aAAa,EAAE;QACjBgB,KAAK,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEF,GAAG,CAAC,CAAC;MACrE,CAAC,MAAM;QACLJ,KAAK,CAACO,KAAK,CAAC,CAAC;MACf;IACF;EACF,CAAC,EAAE,CAAC5B,iBAAiB,EAAEK,aAAa,CAAC,CAAC;EAEtC,MAAMwB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMR,KAAK,GAAGvB,UAAU,CAACe,OAAO;IAChC,IAAIQ,KAAK,IAAIA,KAAK,CAACS,QAAQ,EAAE;MAC3B3B,WAAW,CAAEkB,KAAK,CAACU,WAAW,GAAGV,KAAK,CAACS,QAAQ,GAAI,GAAG,CAAC;IACzD;EACF,CAAC;EAED,MAAME,cAAc,GAAIC,KAAK,IAAK;IAChChC,oBAAoB,CAACgC,KAAK,CAAC;IAC3B9B,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EACD,oBACE9C,OAAA;IAAK6E,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnB9E,OAAA;MAAK6E,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9E,OAAA;QACE+E,GAAG,EAAElD,YAAY,CAACc,iBAAiB,CAAE;QACrCqC,GAAG,EAAE,mBAAmBrC,iBAAiB,EAAG;QAC5CkC,SAAS,EAAC;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNpF,OAAA,CAACH,MAAM;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVpF,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAACQ,GAAG,EAAE3C,cAAe;MAAAoC,QAAA,eACrD9E,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxB/B,SAAS,GACRf,QAAQ,gBACNhC,OAAA;UACE+E,GAAG,EAAElD,YAAY,CAACc,iBAAiB,CAAE;UACrCqC,GAAG,EAAE,mBAAmBrC,iBAAiB,EAAG;UAC5CkC,SAAS,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,gBAEFpF,OAAA;UAEEqF,GAAG,EAAE5C,UAAW;UAChBoC,SAAS,EAAC,oBAAoB;UAC9BS,MAAM,EAAEzD,YAAY,CAACc,iBAAiB,CAAE;UACxC4C,QAAQ;UACRC,KAAK;UACLC,WAAW;UACXC,OAAO,EAAC,MAAM;UACdC,YAAY,EAAEnB,gBAAiB;UAC/BoB,OAAO,EAAEA,CAAA,KAAM;YACbhD,oBAAoB,CACjBiB,SAAS,IAAK,CAACA,SAAS,GAAG,CAAC,IAAIvC,MAAM,CAACwC,MAC1C,CAAC;UACH,CAAE;UAAAgB,QAAA,gBAEF9E,OAAA;YAAQ+E,GAAG,EAAEzD,MAAM,CAACqB,iBAAiB,CAAC,CAACnB,GAAI;YAACqE,IAAI,EAAC;UAAW;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DpF,OAAA;YACE+E,GAAG,EAAEzD,MAAM,CAACqB,iBAAiB,CAAC,CAACpB,IAAK;YACpCsE,IAAI,EAAC;UAAY;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA,GAnBGzC,iBAAiB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBjB,CACR,gBAEDpF,OAAA;UACE+E,GAAG,EAAElD,YAAY,CAACc,iBAAiB,CAAE;UACrCqC,GAAG,EAAC,kBAAkB;UACtBH,SAAS,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACF,eAEDpF,OAAA;UAAK6E,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCxD,MAAM,CAACwE,GAAG,CAAC,CAACC,CAAC,EAAEnB,KAAK,kBACnB5E,OAAA;YAEE6E,SAAS,EAAE,sBACTlC,iBAAiB,KAAKiC,KAAK,GAAG,QAAQ,GAAG,QAAQ,EAChD;YACHoB,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAACC,KAAK,CAAE;YAAAE,QAAA,EAEpCnC,iBAAiB,KAAKiC,KAAK,iBAC1B5E,OAAA;cACE6E,SAAS,EAAC,oBAAoB;cAC9BoB,KAAK,EAAE;gBAAEC,KAAK,EAAE,GAAGrD,QAAQ;cAAI;YAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UACP,GAXIR,KAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpF,OAAA,CAACL,QAAQ;MAACwG,QAAQ,EAAE,IAAK;MAAArB,QAAA,gBACvB9E,OAAA,CAACmB,eAAe;QAAA2D,QAAA,eACd9E,OAAA,CAACJ,GAAG;UAACiF,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9E,OAAA,CAACI,eAAe;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAElBpF,OAAA,CAACmB,eAAe;QAAA2D,QAAA,eACd9E,OAAA,CAACC,cAAc;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAElBpF,OAAA,CAACmB,eAAe;QAAA2D,QAAA,eACd9E,OAAA,CAACJ,GAAG;UAACwG,EAAE,EAAE;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAApB,QAAA,eACzB9E,OAAA,CAACa,aAAa;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAElBpF,OAAA,CAACmB,eAAe;QAAA2D,QAAA,eACd9E,OAAA,CAACO,qBAAqB;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAElBpF,OAAA,CAACmB,eAAe;QAAA2D,QAAA,eACd9E,OAAA,CAACU,eAAe;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAClBpF,OAAA,CAACgB,MAAM;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACXpF,OAAA,CAACF,cAAc;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV;AAAC5C,GAAA,CAnKQD,IAAI;EAAA,QAGMT,WAAW;AAAA;AAAAuE,IAAA,GAHrB9D,IAAI;AAqKb,eAAeA,IAAI;AAAC,IAAArC,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAgF,IAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}