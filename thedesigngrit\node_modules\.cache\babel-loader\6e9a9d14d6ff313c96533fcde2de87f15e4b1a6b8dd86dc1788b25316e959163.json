{"ast": null, "code": "import axios from \"axios\";\n\n// Create axios instance with default headers\nconst paymobAxios = axios.create({\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// Token management\nlet authToken = null;\nlet tokenExpiry = null;\nconst TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before actual expiry\n\nconst paymobService = {\n  // Get or refresh authentication token\n  async getAuthToken() {\n    try {\n      // Check if we have a valid token\n      if (authToken && tokenExpiry && Date.now() < tokenExpiry - TOKEN_EXPIRY_BUFFER) {\n        console.log(\"Using cached auth token\");\n        return authToken;\n      }\n      console.log(\"Getting new auth token\");\n      // Get Paymob configuration from backend\n      const configResponse = await axios.get(\"https://api.thedesigngrit.com/api/paymob/config\");\n      const {\n        apiKey\n      } = configResponse.data;\n      const response = await paymobAxios.post(\"https://accept.paymob.com/api/auth/tokens\", {\n        api_key: apiKey\n      });\n\n      // Store the new token and set expiry (Paymob tokens typically last 1 hour)\n      authToken = response.data.token;\n      tokenExpiry = Date.now() + 60 * 60 * 1000; // 1 hour from now\n\n      return authToken;\n    } catch (error) {\n      console.error(\"Error getting auth token:\", error);\n      // Clear token on error\n      authToken = null;\n      tokenExpiry = null;\n      throw error;\n    }\n  },\n  // Initialize payment\n  async initializePayment(paymentData, userSession) {\n    try {\n      console.log(\"Initializing payment with data:\", paymentData);\n\n      // Extract billing details from the payment data\n      const {\n        billingDetails,\n        subtotal = 0,\n        taxAmount = 0,\n        shippingFee = 0,\n        total = 0,\n        cartItems = [],\n        shippingDetails\n      } = paymentData;\n\n      // Fix: Ensure amount_cents is properly calculated for each item\n      const items = cartItems.map(item => {\n        const base = {\n          name: item.name,\n          amount_cents: Math.round((item.unitPrice || 0) * 100),\n          description: item.description || \"\",\n          quantity: item.quantity,\n          productId: item.productId || item.id,\n          brandId: item.brandId,\n          variantId: item.variantId || null\n        };\n        if (item.fromQuotation && item.quotationId) {\n          base.fromQuotation = true;\n          base.quotationId = item.quotationId;\n          if (item.color) base.color = item.color;\n          if (item.size) base.size = item.size;\n          if (item.material) base.material = item.material;\n          if (item.customization) base.customization = item.customization;\n        }\n        return base;\n      });\n      console.log(\"Prepared items for payment:\", items);\n      console.log(\"usersession: \", userSession.id || userSession._id);\n      const finalAmountCents = Math.round((subtotal + taxAmount + shippingFee) * 100);\n\n      // Prepare the order data for the backend\n      const orderData = {\n        orderData: {\n          customerId: userSession.id || userSession._id,\n          // Ensure this is taken from the authenticated user\n          total: finalAmountCents / 100,\n          // Store total in EGP\n          subtotal,\n          taxAmount,\n          shippingFee,\n          billingDetails: {\n            apartment: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.apartment) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.apartment) || \"N/A\",\n            email: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.email) || \"N/A\",\n            floor: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.floor) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.floor) || \"N/A\",\n            label: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.label) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.label) || \"N/A\",\n            first_name: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.first_name) || \"N/A\",\n            last_name: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.last_name) || \"N/A\",\n            building: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.apartment) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.apartment) || \"N/A\",\n            street: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.street) || (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.address) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.street) || \"NA\",\n            address: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.address) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.address) || (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.street) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.street) || \"N/A\",\n            phone_number: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.phone_number) || \"N/A\",\n            shipping_method: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.shipping_method) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.shipping_method) || \"NA\",\n            postal_code: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.zipCode) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.zipCode) || \"NA\",\n            city: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.city) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.city) || \"N/A\",\n            country: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.country) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.country) || \"N/A\",\n            state: (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.state) || (billingDetails === null || billingDetails === void 0 ? void 0 : billingDetails.city) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.state) || (shippingDetails === null || shippingDetails === void 0 ? void 0 : shippingDetails.city) || \"N/A\"\n          },\n          items: items\n        }\n      };\n\n      // Log the exact data being sent\n      console.log(\"Sending order data to backend:\", JSON.stringify(orderData, null, 2));\n\n      // Send the order data to your backend\n      const response = await axios.post(`https://api.thedesigngrit.com/api/paymob/create-payment`, orderData, {\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      });\n      console.log(\"Payment initialization response:\", response.data);\n\n      // Check if the response contains the iframe URL\n      if (response.data && response.data.success) {\n        // Ensure we're getting the iframe URL correctly\n        const iframeUrl = response.data.iframe_url;\n        console.log(\"Received iframe URL:\", iframeUrl);\n        if (!iframeUrl) {\n          console.error(\"Backend response is missing iframe_url property:\", response.data);\n          throw new Error(\"Payment gateway URL not received from backend\");\n        }\n        return {\n          iframeUrl: iframeUrl,\n          orderId: response.data.orderId || null\n        };\n      } else {\n        var _response$data;\n        console.error(\"Backend response indicates failure:\", response.data);\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Failed to initialize payment\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Payment initialization error:\", error);\n\n      // Log the full error response if available\n      if (error.response) {\n        console.error(\"Error response data:\", error.response.data);\n        console.error(\"Error response status:\", error.response.status);\n        console.error(\"Error response headers:\", error.response.headers);\n      }\n      throw new Error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || \"Failed to initialize payment\");\n    }\n  },\n  // Create order\n  async createOrder(authToken, amount) {\n    try {\n      const response = await paymobAxios.post(\"https://accept.paymob.com/api/ecommerce/orders\", {\n        auth_token: authToken,\n        delivery_needed: false,\n        amount_cents: Math.round(amount * 100),\n        currency: \"EGP\",\n        items: []\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error creating order:\", error);\n      throw error;\n    }\n  },\n  // Get payment key\n  async getPaymentKey(authToken, orderId, billingData) {\n    try {\n      // Get Paymob configuration from backend\n      const configResponse = await axios.get(\"/api/paymob/config\");\n      const {\n        integrationId\n      } = configResponse.data;\n      const response = await paymobAxios.post(\"https://accept.paymob.com/api/acceptance/payment_keys\", {\n        auth_token: authToken,\n        amount_cents: Math.round(billingData.amount * 100),\n        expiration: 3600,\n        order_id: orderId,\n        billing_data: {\n          apartment: billingData.apartment || \"NA\",\n          email: billingData.email,\n          floor: billingData.floor || \"NA\",\n          first_name: billingData.first_name,\n          street: billingData.street,\n          building: billingData.building,\n          phone_number: billingData.phone_number,\n          shipping_method: billingData.shipping_method || \"NA\",\n          postal_code: billingData.postal_code,\n          city: billingData.city,\n          country: billingData.country,\n          last_name: billingData.last_name,\n          state: billingData.state || billingData.city\n        },\n        currency: \"EGP\",\n        integration_id: integrationId\n      });\n      return response.data;\n    } catch (error) {\n      console.error(\"Error getting payment key:\", error);\n      throw error;\n    }\n  },\n  // Clear cached token (useful for testing or when token becomes invalid)\n  clearAuthToken() {\n    authToken = null;\n    tokenExpiry = null;\n  }\n};\nexport default paymobService;", "map": {"version": 3, "names": ["axios", "paymobAxios", "create", "headers", "authToken", "tokenExpiry", "TOKEN_EXPIRY_BUFFER", "paymobService", "getAuthToken", "Date", "now", "console", "log", "configResponse", "get", "<PERSON><PERSON><PERSON><PERSON>", "data", "response", "post", "api_key", "token", "error", "initializePayment", "paymentData", "userSession", "billingDetails", "subtotal", "taxAmount", "shippingFee", "total", "cartItems", "shippingDetails", "items", "map", "item", "base", "name", "amount_cents", "Math", "round", "unitPrice", "description", "quantity", "productId", "id", "brandId", "variantId", "fromQuotation", "quotationId", "color", "size", "material", "customization", "_id", "finalAmountCents", "orderData", "customerId", "apartment", "email", "floor", "label", "first_name", "last_name", "building", "street", "address", "phone_number", "shipping_method", "postal_code", "zipCode", "city", "country", "state", "JSON", "stringify", "success", "iframeUrl", "iframe_url", "Error", "orderId", "_response$data", "message", "_error$response", "_error$response$data", "status", "createOrder", "amount", "auth_token", "delivery_needed", "currency", "getPaymentKey", "billingData", "integrationId", "expiration", "order_id", "billing_data", "integration_id", "clearAuthToken"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/services/paymobService.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create axios instance with default headers\r\nconst paymobAxios = axios.create({\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n});\r\n\r\n// Token management\r\nlet authToken = null;\r\nlet tokenExpiry = null;\r\nconst TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before actual expiry\r\n\r\nconst paymobService = {\r\n  // Get or refresh authentication token\r\n  async getAuthToken() {\r\n    try {\r\n      // Check if we have a valid token\r\n      if (\r\n        authToken &&\r\n        tokenExpiry &&\r\n        Date.now() < tokenExpiry - TOKEN_EXPIRY_BUFFER\r\n      ) {\r\n        console.log(\"Using cached auth token\");\r\n        return authToken;\r\n      }\r\n\r\n      console.log(\"Getting new auth token\");\r\n      // Get Paymob configuration from backend\r\n      const configResponse = await axios.get(\r\n        \"https://api.thedesigngrit.com/api/paymob/config\"\r\n      );\r\n      const { apiKey } = configResponse.data;\r\n\r\n      const response = await paymobAxios.post(\r\n        \"https://accept.paymob.com/api/auth/tokens\",\r\n        {\r\n          api_key: apiKey,\r\n        }\r\n      );\r\n\r\n      // Store the new token and set expiry (Paymob tokens typically last 1 hour)\r\n      authToken = response.data.token;\r\n      tokenExpiry = Date.now() + 60 * 60 * 1000; // 1 hour from now\r\n\r\n      return authToken;\r\n    } catch (error) {\r\n      console.error(\"Error getting auth token:\", error);\r\n      // Clear token on error\r\n      authToken = null;\r\n      tokenExpiry = null;\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Initialize payment\r\n  async initializePayment(paymentData, userSession) {\r\n    try {\r\n      console.log(\"Initializing payment with data:\", paymentData);\r\n\r\n      // Extract billing details from the payment data\r\n      const {\r\n        billingDetails,\r\n        subtotal = 0,\r\n        taxAmount = 0,\r\n        shippingFee = 0,\r\n        total = 0,\r\n        cartItems = [],\r\n        shippingDetails,\r\n      } = paymentData;\r\n\r\n      // Fix: Ensure amount_cents is properly calculated for each item\r\n      const items = cartItems.map((item) => {\r\n        const base = {\r\n          name: item.name,\r\n          amount_cents: Math.round((item.unitPrice || 0) * 100),\r\n          description: item.description || \"\",\r\n          quantity: item.quantity,\r\n          productId: item.productId || item.id,\r\n          brandId: item.brandId,\r\n          variantId: item.variantId || null,\r\n        };\r\n        if (item.fromQuotation && item.quotationId) {\r\n          base.fromQuotation = true;\r\n          base.quotationId = item.quotationId;\r\n          if (item.color) base.color = item.color;\r\n          if (item.size) base.size = item.size;\r\n          if (item.material) base.material = item.material;\r\n          if (item.customization) base.customization = item.customization;\r\n        }\r\n        return base;\r\n      });\r\n\r\n      console.log(\"Prepared items for payment:\", items);\r\n      console.log(\"usersession: \", userSession.id || userSession._id);\r\n      const finalAmountCents = Math.round(\r\n        (subtotal + taxAmount + shippingFee) * 100\r\n      );\r\n\r\n      // Prepare the order data for the backend\r\n      const orderData = {\r\n        orderData: {\r\n          customerId: userSession.id || userSession._id, // Ensure this is taken from the authenticated user\r\n          total: finalAmountCents / 100, // Store total in EGP\r\n          subtotal,\r\n          taxAmount,\r\n          shippingFee,\r\n          billingDetails: {\r\n            apartment:\r\n              billingDetails?.apartment || shippingDetails?.apartment || \"N/A\",\r\n            email: billingDetails?.email || \"N/A\",\r\n            floor: billingDetails?.floor || shippingDetails?.floor || \"N/A\",\r\n            label: billingDetails?.label || shippingDetails?.label || \"N/A\",\r\n            first_name: billingDetails?.first_name || \"N/A\",\r\n            last_name: billingDetails?.last_name || \"N/A\",\r\n            building:\r\n              billingDetails?.apartment || shippingDetails?.apartment || \"N/A\",\r\n            street:\r\n              billingDetails?.street ||\r\n              billingDetails?.address ||\r\n              shippingDetails?.street ||\r\n              \"NA\",\r\n            address:\r\n              billingDetails?.address ||\r\n              shippingDetails?.address ||\r\n              billingDetails?.street ||\r\n              shippingDetails?.street ||\r\n              \"N/A\",\r\n            phone_number: billingDetails?.phone_number || \"N/A\",\r\n            shipping_method:\r\n              billingDetails?.shipping_method ||\r\n              shippingDetails?.shipping_method ||\r\n              \"NA\",\r\n            postal_code:\r\n              billingDetails?.zipCode || shippingDetails?.zipCode || \"NA\",\r\n            city: billingDetails?.city || shippingDetails?.city || \"N/A\",\r\n            country:\r\n              billingDetails?.country || shippingDetails?.country || \"N/A\",\r\n            state:\r\n              billingDetails?.state ||\r\n              billingDetails?.city ||\r\n              shippingDetails?.state ||\r\n              shippingDetails?.city ||\r\n              \"N/A\",\r\n          },\r\n\r\n          items: items,\r\n        },\r\n      };\r\n\r\n      // Log the exact data being sent\r\n      console.log(\r\n        \"Sending order data to backend:\",\r\n        JSON.stringify(orderData, null, 2)\r\n      );\r\n\r\n      // Send the order data to your backend\r\n      const response = await axios.post(\r\n        `https://api.thedesigngrit.com/api/paymob/create-payment`,\r\n        orderData,\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n        }\r\n      );\r\n\r\n      console.log(\"Payment initialization response:\", response.data);\r\n\r\n      // Check if the response contains the iframe URL\r\n      if (response.data && response.data.success) {\r\n        // Ensure we're getting the iframe URL correctly\r\n        const iframeUrl = response.data.iframe_url;\r\n        console.log(\"Received iframe URL:\", iframeUrl);\r\n\r\n        if (!iframeUrl) {\r\n          console.error(\r\n            \"Backend response is missing iframe_url property:\",\r\n            response.data\r\n          );\r\n          throw new Error(\"Payment gateway URL not received from backend\");\r\n        }\r\n\r\n        return {\r\n          iframeUrl: iframeUrl,\r\n          orderId: response.data.orderId || null,\r\n        };\r\n      } else {\r\n        console.error(\"Backend response indicates failure:\", response.data);\r\n        throw new Error(\r\n          response.data?.message || \"Failed to initialize payment\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Payment initialization error:\", error);\r\n\r\n      // Log the full error response if available\r\n      if (error.response) {\r\n        console.error(\"Error response data:\", error.response.data);\r\n        console.error(\"Error response status:\", error.response.status);\r\n        console.error(\"Error response headers:\", error.response.headers);\r\n      }\r\n\r\n      throw new Error(\r\n        error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to initialize payment\"\r\n      );\r\n    }\r\n  },\r\n\r\n  // Create order\r\n  async createOrder(authToken, amount) {\r\n    try {\r\n      const response = await paymobAxios.post(\r\n        \"https://accept.paymob.com/api/ecommerce/orders\",\r\n        {\r\n          auth_token: authToken,\r\n          delivery_needed: false,\r\n          amount_cents: Math.round(amount * 100),\r\n          currency: \"EGP\",\r\n          items: [],\r\n        }\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating order:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get payment key\r\n  async getPaymentKey(authToken, orderId, billingData) {\r\n    try {\r\n      // Get Paymob configuration from backend\r\n      const configResponse = await axios.get(\"/api/paymob/config\");\r\n      const { integrationId } = configResponse.data;\r\n\r\n      const response = await paymobAxios.post(\r\n        \"https://accept.paymob.com/api/acceptance/payment_keys\",\r\n        {\r\n          auth_token: authToken,\r\n          amount_cents: Math.round(billingData.amount * 100),\r\n          expiration: 3600,\r\n          order_id: orderId,\r\n          billing_data: {\r\n            apartment: billingData.apartment || \"NA\",\r\n            email: billingData.email,\r\n            floor: billingData.floor || \"NA\",\r\n            first_name: billingData.first_name,\r\n            street: billingData.street,\r\n            building: billingData.building,\r\n            phone_number: billingData.phone_number,\r\n            shipping_method: billingData.shipping_method || \"NA\",\r\n            postal_code: billingData.postal_code,\r\n            city: billingData.city,\r\n            country: billingData.country,\r\n            last_name: billingData.last_name,\r\n            state: billingData.state || billingData.city,\r\n          },\r\n          currency: \"EGP\",\r\n          integration_id: integrationId,\r\n        }\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting payment key:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Clear cached token (useful for testing or when token becomes invalid)\r\n  clearAuthToken() {\r\n    authToken = null;\r\n    tokenExpiry = null;\r\n  },\r\n};\r\n\r\nexport default paymobService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,WAAW,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC/BC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,SAAS,GAAG,IAAI;AACpB,IAAIC,WAAW,GAAG,IAAI;AACtB,MAAMC,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;AAE3C,MAAMC,aAAa,GAAG;EACpB;EACA,MAAMC,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF;MACA,IACEJ,SAAS,IACTC,WAAW,IACXI,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,WAAW,GAAGC,mBAAmB,EAC9C;QACAK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtC,OAAOR,SAAS;MAClB;MAEAO,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACA,MAAMC,cAAc,GAAG,MAAMb,KAAK,CAACc,GAAG,CACpC,iDACF,CAAC;MACD,MAAM;QAAEC;MAAO,CAAC,GAAGF,cAAc,CAACG,IAAI;MAEtC,MAAMC,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,IAAI,CACrC,2CAA2C,EAC3C;QACEC,OAAO,EAAEJ;MACX,CACF,CAAC;;MAED;MACAX,SAAS,GAAGa,QAAQ,CAACD,IAAI,CAACI,KAAK;MAC/Bf,WAAW,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;MAE3C,OAAON,SAAS;IAClB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACAjB,SAAS,GAAG,IAAI;MAChBC,WAAW,GAAG,IAAI;MAClB,MAAMgB,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMC,iBAAiBA,CAACC,WAAW,EAAEC,WAAW,EAAE;IAChD,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,WAAW,CAAC;;MAE3D;MACA,MAAM;QACJE,cAAc;QACdC,QAAQ,GAAG,CAAC;QACZC,SAAS,GAAG,CAAC;QACbC,WAAW,GAAG,CAAC;QACfC,KAAK,GAAG,CAAC;QACTC,SAAS,GAAG,EAAE;QACdC;MACF,CAAC,GAAGR,WAAW;;MAEf;MACA,MAAMS,KAAK,GAAGF,SAAS,CAACG,GAAG,CAAEC,IAAI,IAAK;QACpC,MAAMC,IAAI,GAAG;UACXC,IAAI,EAAEF,IAAI,CAACE,IAAI;UACfC,YAAY,EAAEC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,CAACM,SAAS,IAAI,CAAC,IAAI,GAAG,CAAC;UACrDC,WAAW,EAAEP,IAAI,CAACO,WAAW,IAAI,EAAE;UACnCC,QAAQ,EAAER,IAAI,CAACQ,QAAQ;UACvBC,SAAS,EAAET,IAAI,CAACS,SAAS,IAAIT,IAAI,CAACU,EAAE;UACpCC,OAAO,EAAEX,IAAI,CAACW,OAAO;UACrBC,SAAS,EAAEZ,IAAI,CAACY,SAAS,IAAI;QAC/B,CAAC;QACD,IAAIZ,IAAI,CAACa,aAAa,IAAIb,IAAI,CAACc,WAAW,EAAE;UAC1Cb,IAAI,CAACY,aAAa,GAAG,IAAI;UACzBZ,IAAI,CAACa,WAAW,GAAGd,IAAI,CAACc,WAAW;UACnC,IAAId,IAAI,CAACe,KAAK,EAAEd,IAAI,CAACc,KAAK,GAAGf,IAAI,CAACe,KAAK;UACvC,IAAIf,IAAI,CAACgB,IAAI,EAAEf,IAAI,CAACe,IAAI,GAAGhB,IAAI,CAACgB,IAAI;UACpC,IAAIhB,IAAI,CAACiB,QAAQ,EAAEhB,IAAI,CAACgB,QAAQ,GAAGjB,IAAI,CAACiB,QAAQ;UAChD,IAAIjB,IAAI,CAACkB,aAAa,EAAEjB,IAAI,CAACiB,aAAa,GAAGlB,IAAI,CAACkB,aAAa;QACjE;QACA,OAAOjB,IAAI;MACb,CAAC,CAAC;MAEFxB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoB,KAAK,CAAC;MACjDrB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEY,WAAW,CAACoB,EAAE,IAAIpB,WAAW,CAAC6B,GAAG,CAAC;MAC/D,MAAMC,gBAAgB,GAAGhB,IAAI,CAACC,KAAK,CACjC,CAACb,QAAQ,GAAGC,SAAS,GAAGC,WAAW,IAAI,GACzC,CAAC;;MAED;MACA,MAAM2B,SAAS,GAAG;QAChBA,SAAS,EAAE;UACTC,UAAU,EAAEhC,WAAW,CAACoB,EAAE,IAAIpB,WAAW,CAAC6B,GAAG;UAAE;UAC/CxB,KAAK,EAAEyB,gBAAgB,GAAG,GAAG;UAAE;UAC/B5B,QAAQ;UACRC,SAAS;UACTC,WAAW;UACXH,cAAc,EAAE;YACdgC,SAAS,EACP,CAAAhC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgC,SAAS,MAAI1B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0B,SAAS,KAAI,KAAK;YAClEC,KAAK,EAAE,CAAAjC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiC,KAAK,KAAI,KAAK;YACrCC,KAAK,EAAE,CAAAlC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkC,KAAK,MAAI5B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4B,KAAK,KAAI,KAAK;YAC/DC,KAAK,EAAE,CAAAnC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmC,KAAK,MAAI7B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6B,KAAK,KAAI,KAAK;YAC/DC,UAAU,EAAE,CAAApC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEoC,UAAU,KAAI,KAAK;YAC/CC,SAAS,EAAE,CAAArC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEqC,SAAS,KAAI,KAAK;YAC7CC,QAAQ,EACN,CAAAtC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgC,SAAS,MAAI1B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0B,SAAS,KAAI,KAAK;YAClEO,MAAM,EACJ,CAAAvC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuC,MAAM,MACtBvC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwC,OAAO,MACvBlC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiC,MAAM,KACvB,IAAI;YACNC,OAAO,EACL,CAAAxC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwC,OAAO,MACvBlC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,OAAO,MACxBxC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuC,MAAM,MACtBjC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiC,MAAM,KACvB,KAAK;YACPE,YAAY,EAAE,CAAAzC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyC,YAAY,KAAI,KAAK;YACnDC,eAAe,EACb,CAAA1C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0C,eAAe,MAC/BpC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoC,eAAe,KAChC,IAAI;YACNC,WAAW,EACT,CAAA3C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE4C,OAAO,MAAItC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsC,OAAO,KAAI,IAAI;YAC7DC,IAAI,EAAE,CAAA7C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,IAAI,MAAIvC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuC,IAAI,KAAI,KAAK;YAC5DC,OAAO,EACL,CAAA9C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8C,OAAO,MAAIxC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwC,OAAO,KAAI,KAAK;YAC9DC,KAAK,EACH,CAAA/C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE+C,KAAK,MACrB/C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,IAAI,MACpBvC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyC,KAAK,MACtBzC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuC,IAAI,KACrB;UACJ,CAAC;UAEDtC,KAAK,EAAEA;QACT;MACF,CAAC;;MAED;MACArB,OAAO,CAACC,GAAG,CACT,gCAAgC,EAChC6D,IAAI,CAACC,SAAS,CAACnB,SAAS,EAAE,IAAI,EAAE,CAAC,CACnC,CAAC;;MAED;MACA,MAAMtC,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,IAAI,CAC/B,yDAAyD,EACzDqC,SAAS,EACT;QACEpD,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAEDQ,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEK,QAAQ,CAACD,IAAI,CAAC;;MAE9D;MACA,IAAIC,QAAQ,CAACD,IAAI,IAAIC,QAAQ,CAACD,IAAI,CAAC2D,OAAO,EAAE;QAC1C;QACA,MAAMC,SAAS,GAAG3D,QAAQ,CAACD,IAAI,CAAC6D,UAAU;QAC1ClE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgE,SAAS,CAAC;QAE9C,IAAI,CAACA,SAAS,EAAE;UACdjE,OAAO,CAACU,KAAK,CACX,kDAAkD,EAClDJ,QAAQ,CAACD,IACX,CAAC;UACD,MAAM,IAAI8D,KAAK,CAAC,+CAA+C,CAAC;QAClE;QAEA,OAAO;UACLF,SAAS,EAAEA,SAAS;UACpBG,OAAO,EAAE9D,QAAQ,CAACD,IAAI,CAAC+D,OAAO,IAAI;QACpC,CAAC;MACH,CAAC,MAAM;QAAA,IAAAC,cAAA;QACLrE,OAAO,CAACU,KAAK,CAAC,qCAAqC,EAAEJ,QAAQ,CAACD,IAAI,CAAC;QACnE,MAAM,IAAI8D,KAAK,CACb,EAAAE,cAAA,GAAA/D,QAAQ,CAACD,IAAI,cAAAgE,cAAA,uBAAbA,cAAA,CAAeC,OAAO,KAAI,8BAC5B,CAAC;MACH;IACF,CAAC,CAAC,OAAO5D,KAAK,EAAE;MAAA,IAAA6D,eAAA,EAAAC,oBAAA;MACdxE,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAIA,KAAK,CAACJ,QAAQ,EAAE;QAClBN,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACJ,QAAQ,CAACD,IAAI,CAAC;QAC1DL,OAAO,CAACU,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACJ,QAAQ,CAACmE,MAAM,CAAC;QAC9DzE,OAAO,CAACU,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAACJ,QAAQ,CAACd,OAAO,CAAC;MAClE;MAEA,MAAM,IAAI2E,KAAK,CACb,EAAAI,eAAA,GAAA7D,KAAK,CAACJ,QAAQ,cAAAiE,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlE,IAAI,cAAAmE,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAC3B5D,KAAK,CAAC4D,OAAO,IACb,8BACJ,CAAC;IACH;EACF,CAAC;EAED;EACA,MAAMI,WAAWA,CAACjF,SAAS,EAAEkF,MAAM,EAAE;IACnC,IAAI;MACF,MAAMrE,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,IAAI,CACrC,gDAAgD,EAChD;QACEqE,UAAU,EAAEnF,SAAS;QACrBoF,eAAe,EAAE,KAAK;QACtBnD,YAAY,EAAEC,IAAI,CAACC,KAAK,CAAC+C,MAAM,GAAG,GAAG,CAAC;QACtCG,QAAQ,EAAE,KAAK;QACfzD,KAAK,EAAE;MACT,CACF,CAAC;MACD,OAAOf,QAAQ,CAACD,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMqE,aAAaA,CAACtF,SAAS,EAAE2E,OAAO,EAAEY,WAAW,EAAE;IACnD,IAAI;MACF;MACA,MAAM9E,cAAc,GAAG,MAAMb,KAAK,CAACc,GAAG,CAAC,oBAAoB,CAAC;MAC5D,MAAM;QAAE8E;MAAc,CAAC,GAAG/E,cAAc,CAACG,IAAI;MAE7C,MAAMC,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,IAAI,CACrC,uDAAuD,EACvD;QACEqE,UAAU,EAAEnF,SAAS;QACrBiC,YAAY,EAAEC,IAAI,CAACC,KAAK,CAACoD,WAAW,CAACL,MAAM,GAAG,GAAG,CAAC;QAClDO,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAEf,OAAO;QACjBgB,YAAY,EAAE;UACZtC,SAAS,EAAEkC,WAAW,CAAClC,SAAS,IAAI,IAAI;UACxCC,KAAK,EAAEiC,WAAW,CAACjC,KAAK;UACxBC,KAAK,EAAEgC,WAAW,CAAChC,KAAK,IAAI,IAAI;UAChCE,UAAU,EAAE8B,WAAW,CAAC9B,UAAU;UAClCG,MAAM,EAAE2B,WAAW,CAAC3B,MAAM;UAC1BD,QAAQ,EAAE4B,WAAW,CAAC5B,QAAQ;UAC9BG,YAAY,EAAEyB,WAAW,CAACzB,YAAY;UACtCC,eAAe,EAAEwB,WAAW,CAACxB,eAAe,IAAI,IAAI;UACpDC,WAAW,EAAEuB,WAAW,CAACvB,WAAW;UACpCE,IAAI,EAAEqB,WAAW,CAACrB,IAAI;UACtBC,OAAO,EAAEoB,WAAW,CAACpB,OAAO;UAC5BT,SAAS,EAAE6B,WAAW,CAAC7B,SAAS;UAChCU,KAAK,EAAEmB,WAAW,CAACnB,KAAK,IAAImB,WAAW,CAACrB;QAC1C,CAAC;QACDmB,QAAQ,EAAE,KAAK;QACfO,cAAc,EAAEJ;MAClB,CACF,CAAC;MACD,OAAO3E,QAAQ,CAACD,IAAI;IACtB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA4E,cAAcA,CAAA,EAAG;IACf7F,SAAS,GAAG,IAAI;IAChBC,WAAW,GAAG,IAAI;EACpB;AACF,CAAC;AAED,eAAeE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}