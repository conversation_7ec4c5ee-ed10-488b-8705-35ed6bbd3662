{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\PendingBrandUpdates.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Box, Card, CardContent, CardMedia, Typography, Grid, Dialog, DialogTitle, DialogContent, DialogActions, Button, Snackbar, Alert, CircularProgress, Divider, IconButton } from \"@mui/material\";\nimport { Close as CloseIcon } from \"@mui/icons-material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PendingBrandUpdates = () => {\n  _s();\n  const [pendingBrands, setPendingBrands] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedBrand, setSelectedBrand] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [actionLoading, setActionLoading] = useState(false);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: \"\",\n    severity: \"success\"\n  });\n  useEffect(() => {\n    fetchPendingBrands();\n  }, []);\n  const fetchPendingBrands = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(\"https://api.thedesigngrit.com/api/brand/admin/brands/pending\");\n      setPendingBrands(response.data || []);\n    } catch (error) {\n      setSnackbar({\n        open: true,\n        message: \"Failed to fetch pending brands\",\n        severity: \"error\"\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCardClick = brand => {\n    setSelectedBrand(brand);\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBrand(null);\n  };\n  const handleApprove = async () => {\n    if (!selectedBrand) return;\n    setActionLoading(true);\n    try {\n      await axios.put(`https://api.thedesigngrit.com/api/brand/admin/brands/${selectedBrand._id}/approve`);\n      setSnackbar({\n        open: true,\n        message: \"Brand update approved\",\n        severity: \"success\"\n      });\n      fetchPendingBrands();\n      handleCloseDialog();\n    } catch (error) {\n      setSnackbar({\n        open: true,\n        message: \"Failed to approve update\",\n        severity: \"error\"\n      });\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleReject = async () => {\n    if (!selectedBrand) return;\n    setActionLoading(true);\n    try {\n      await axios.put(`https://api.thedesigngrit.com/api/brand/admin/brands/${selectedBrand._id}/reject`);\n      setSnackbar({\n        open: true,\n        message: \"Brand update rejected\",\n        severity: \"success\"\n      });\n      fetchPendingBrands();\n      handleCloseDialog();\n    } catch (error) {\n      setSnackbar({\n        open: true,\n        message: \"Failed to reject update\",\n        severity: \"error\"\n      });\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Helper to safely render any value (stringify objects)\n  const renderValue = val => {\n    if (val === null || val === undefined) return \"N/A\";\n    if (typeof val === \"object\") return JSON.stringify(val, null, 2);\n    return val;\n  };\n\n  // Helper to check if a field is an image (by key or value)\n  const isImageField = (key, value) => {\n    const imageFields = [\"brandlogo\", \"coverPhoto\", \"logo\", \"image\", \"photo\"];\n    const imageExtensions = [\".png\", \".jpg\", \".jpeg\", \".webp\", \".avif\"];\n    if (imageFields.includes(key)) return true;\n    if (typeof value === \"string\") {\n      return imageExtensions.some(ext => value.toLowerCase().endsWith(ext));\n    }\n    return false;\n  };\n\n  // Helper to render an image if value is a filename\n  const renderImage = (filename, alt) => {\n    if (!filename) return \"N/A\";\n    return /*#__PURE__*/_jsxDEV(\"img\", {\n      src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${filename}`,\n      alt: alt,\n      style: {\n        maxWidth: 120,\n        maxHeight: 80,\n        display: \"block\",\n        margin: \"8px 0\",\n        borderRadius: 6,\n        border: \"1px solid #eee\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFieldDiff = (field, current, pending) => {\n    if (isImageField(field, current) || isImageField(field, pending)) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Current:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), renderImage(current, \"Current\"), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"primary\",\n          children: \"Pending:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), renderImage(pending, \"Pending\")]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this);\n    }\n    if (JSON.stringify(current) === JSON.stringify(pending)) {\n      return /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: renderValue(current)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: [\"Current: \", renderValue(current)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"primary\",\n        children: [\"Pending: \", renderValue(pending)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  };\n  const renderBrandCards = brandsList => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: brandsList.map(brand => {\n      var _brand$brandDescripti;\n      return /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            cursor: \"pointer\",\n            transition: \"transform 0.3s, box-shadow 0.3s\",\n            \"&:hover\": {\n              transform: \"translateY(-5px)\",\n              boxShadow: \"0 8px 16px rgba(0,0,0,0.2)\"\n            }\n          },\n          onClick: () => handleCardClick(brand),\n          children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n            component: \"img\",\n            height: \"140\",\n            image: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${brand.brandlogo}?t=${Date.now()}`,\n            alt: brand.brandName,\n            sx: {\n              objectFit: \"contain\",\n              padding: 2,\n              backgroundColor: \"#f5f5f5\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              gutterBottom: true,\n              variant: \"h5\",\n              component: \"div\",\n              children: brand.brandName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [(_brand$brandDescripti = brand.brandDescription) === null || _brand$brandDescripti === void 0 ? void 0 : _brand$brandDescripti.substring(0, 100), \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Address:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), \" \", brand.companyAddress]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Phone:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), \" \", brand.phoneNumber]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), \" \", brand.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, brand._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n  const renderBrandDetails = () => {\n    if (!selectedBrand) return null;\n    const {\n      pendingUpdates = {},\n      ...currentBrand\n    } = selectedBrand;\n    // Show all fields in pendingUpdates\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: Object.keys(pendingUpdates).length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"No pending updates for this brand.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this) : Object.entries(pendingUpdates).map(([key, pendingValue]) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            color: \"text.secondary\",\n            sx: {\n              textTransform: \"capitalize\"\n            },\n            children: key.replace(/([A-Z])/g, \" $1\").trim()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this), renderFieldDiff(key, currentBrand[key], pendingValue)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Pending Brand Updates\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        p: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        thickness: 4,\n        sx: {\n          color: \"#6b7b58\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this) : pendingBrands.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: \"center\",\n        mt: 6\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"No brand updates pending approval.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: renderBrandCards(pendingBrands)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\"\n        },\n        children: [selectedBrand === null || selectedBrand === void 0 ? void 0 : selectedBrand.brandName, /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleCloseDialog,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: renderBrandDetails()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleReject,\n          color: \"error\",\n          disabled: actionLoading,\n          children: actionLoading ? \"Rejecting...\" : \"Reject\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleApprove,\n          color: \"primary\",\n          disabled: actionLoading,\n          children: actionLoading ? \"Approving...\" : \"Approve\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"right\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        variant: \"filled\",\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n};\n_s(PendingBrandUpdates, \"rM3HsNTbOYzkfLuW6fkUzgLVqsU=\");\n_c = PendingBrandUpdates;\nexport default PendingBrandUpdates;\nvar _c;\n$RefreshReg$(_c, \"PendingBrandUpdates\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Typography", "Grid", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "Close", "CloseIcon", "jsxDEV", "_jsxDEV", "PendingBrandUpdates", "_s", "pendingBrands", "setPendingBrands", "loading", "setLoading", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>rand", "openDialog", "setOpenDialog", "actionLoading", "setActionLoading", "snackbar", "setSnackbar", "open", "message", "severity", "fetchPendingBrands", "response", "get", "data", "error", "handleCardClick", "brand", "handleCloseDialog", "handleApprove", "put", "_id", "handleReject", "handleCloseSnackbar", "renderValue", "val", "undefined", "JSON", "stringify", "isImageField", "key", "value", "imageFields", "imageExtensions", "includes", "some", "ext", "toLowerCase", "endsWith", "renderImage", "filename", "alt", "src", "style", "max<PERSON><PERSON><PERSON>", "maxHeight", "display", "margin", "borderRadius", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderFieldDiff", "field", "current", "pending", "children", "variant", "color", "renderBrandCards", "brandsList", "container", "spacing", "map", "_brand$brandDescripti", "item", "xs", "sm", "md", "sx", "height", "flexDirection", "cursor", "transition", "transform", "boxShadow", "onClick", "component", "image", "brandlogo", "Date", "now", "brandName", "objectFit", "padding", "backgroundColor", "flexGrow", "gutterBottom", "mb", "brandDescription", "substring", "my", "companyAddress", "phoneNumber", "email", "renderBrandDetails", "pendingUpdates", "current<PERSON><PERSON>", "p", "Object", "keys", "length", "entries", "pendingValue", "textTransform", "replace", "trim", "justifyContent", "size", "thickness", "textAlign", "mt", "onClose", "fullWidth", "alignItems", "dividers", "disabled", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/PendingBrandUpdates.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  CardContent,\r\n  CardMedia,\r\n  Typography,\r\n  Grid,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Button,\r\n  Snackbar,\r\n  Alert,\r\n  CircularProgress,\r\n  Divider,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport { Close as CloseIcon } from \"@mui/icons-material\";\r\n\r\nconst PendingBrandUpdates = () => {\r\n  const [pendingBrands, setPendingBrands] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [selectedBrand, setSelectedBrand] = useState(null);\r\n  const [openDialog, setOpenDialog] = useState(false);\r\n  const [actionLoading, setActionLoading] = useState(false);\r\n  const [snackbar, setSnackbar] = useState({\r\n    open: false,\r\n    message: \"\",\r\n    severity: \"success\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetchPendingBrands();\r\n  }, []);\r\n\r\n  const fetchPendingBrands = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get(\r\n        \"https://api.thedesigngrit.com/api/brand/admin/brands/pending\"\r\n      );\r\n      setPendingBrands(response.data || []);\r\n    } catch (error) {\r\n      setSnackbar({\r\n        open: true,\r\n        message: \"Failed to fetch pending brands\",\r\n        severity: \"error\",\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCardClick = (brand) => {\r\n    setSelectedBrand(brand);\r\n    setOpenDialog(true);\r\n  };\r\n\r\n  const handleCloseDialog = () => {\r\n    setOpenDialog(false);\r\n    setSelectedBrand(null);\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    if (!selectedBrand) return;\r\n    setActionLoading(true);\r\n    try {\r\n      await axios.put(\r\n        `https://api.thedesigngrit.com/api/brand/admin/brands/${selectedBrand._id}/approve`\r\n      );\r\n      setSnackbar({\r\n        open: true,\r\n        message: \"Brand update approved\",\r\n        severity: \"success\",\r\n      });\r\n      fetchPendingBrands();\r\n      handleCloseDialog();\r\n    } catch (error) {\r\n      setSnackbar({\r\n        open: true,\r\n        message: \"Failed to approve update\",\r\n        severity: \"error\",\r\n      });\r\n    } finally {\r\n      setActionLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleReject = async () => {\r\n    if (!selectedBrand) return;\r\n    setActionLoading(true);\r\n    try {\r\n      await axios.put(\r\n        `https://api.thedesigngrit.com/api/brand/admin/brands/${selectedBrand._id}/reject`\r\n      );\r\n      setSnackbar({\r\n        open: true,\r\n        message: \"Brand update rejected\",\r\n        severity: \"success\",\r\n      });\r\n      fetchPendingBrands();\r\n      handleCloseDialog();\r\n    } catch (error) {\r\n      setSnackbar({\r\n        open: true,\r\n        message: \"Failed to reject update\",\r\n        severity: \"error\",\r\n      });\r\n    } finally {\r\n      setActionLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseSnackbar = () => {\r\n    setSnackbar({ ...snackbar, open: false });\r\n  };\r\n\r\n  // Helper to safely render any value (stringify objects)\r\n  const renderValue = (val) => {\r\n    if (val === null || val === undefined) return \"N/A\";\r\n    if (typeof val === \"object\") return JSON.stringify(val, null, 2);\r\n    return val;\r\n  };\r\n\r\n  // Helper to check if a field is an image (by key or value)\r\n  const isImageField = (key, value) => {\r\n    const imageFields = [\"brandlogo\", \"coverPhoto\", \"logo\", \"image\", \"photo\"];\r\n    const imageExtensions = [\".png\", \".jpg\", \".jpeg\", \".webp\", \".avif\"];\r\n    if (imageFields.includes(key)) return true;\r\n    if (typeof value === \"string\") {\r\n      return imageExtensions.some((ext) => value.toLowerCase().endsWith(ext));\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Helper to render an image if value is a filename\r\n  const renderImage = (filename, alt) => {\r\n    if (!filename) return \"N/A\";\r\n    return (\r\n      <img\r\n        src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${filename}`}\r\n        alt={alt}\r\n        style={{\r\n          maxWidth: 120,\r\n          maxHeight: 80,\r\n          display: \"block\",\r\n          margin: \"8px 0\",\r\n          borderRadius: 6,\r\n          border: \"1px solid #eee\",\r\n        }}\r\n      />\r\n    );\r\n  };\r\n\r\n  const renderFieldDiff = (field, current, pending) => {\r\n    if (isImageField(field, current) || isImageField(field, pending)) {\r\n      return (\r\n        <Box>\r\n          <Typography variant=\"body2\" color=\"text.secondary\">\r\n            Current:\r\n          </Typography>\r\n          {renderImage(current, \"Current\")}\r\n          <Typography variant=\"body2\" color=\"primary\">\r\n            Pending:\r\n          </Typography>\r\n          {renderImage(pending, \"Pending\")}\r\n        </Box>\r\n      );\r\n    }\r\n    if (JSON.stringify(current) === JSON.stringify(pending)) {\r\n      return <Typography variant=\"body1\">{renderValue(current)}</Typography>;\r\n    }\r\n    return (\r\n      <Box>\r\n        <Typography variant=\"body2\" color=\"text.secondary\">\r\n          Current: {renderValue(current)}\r\n        </Typography>\r\n        <Typography variant=\"body2\" color=\"primary\">\r\n          Pending: {renderValue(pending)}\r\n        </Typography>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderBrandCards = (brandsList) => (\r\n    <Grid container spacing={3}>\r\n      {brandsList.map((brand) => (\r\n        <Grid item xs={12} sm={6} md={4} key={brand._id}>\r\n          <Card\r\n            sx={{\r\n              height: \"100%\",\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              cursor: \"pointer\",\r\n              transition: \"transform 0.3s, box-shadow 0.3s\",\r\n              \"&:hover\": {\r\n                transform: \"translateY(-5px)\",\r\n                boxShadow: \"0 8px 16px rgba(0,0,0,0.2)\",\r\n              },\r\n            }}\r\n            onClick={() => handleCardClick(brand)}\r\n          >\r\n            <CardMedia\r\n              component=\"img\"\r\n              height=\"140\"\r\n              image={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${\r\n                brand.brandlogo\r\n              }?t=${Date.now()}`}\r\n              alt={brand.brandName}\r\n              sx={{\r\n                objectFit: \"contain\",\r\n                padding: 2,\r\n                backgroundColor: \"#f5f5f5\",\r\n              }}\r\n            />\r\n            <CardContent sx={{ flexGrow: 1 }}>\r\n              <Typography gutterBottom variant=\"h5\" component=\"div\">\r\n                {brand.brandName}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\r\n                {brand.brandDescription?.substring(0, 100)}...\r\n              </Typography>\r\n              <Divider sx={{ my: 1 }} />\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                <strong>Address:</strong> {brand.companyAddress}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                <strong>Phone:</strong> {brand.phoneNumber}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                <strong>Email:</strong> {brand.email}\r\n              </Typography>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      ))}\r\n    </Grid>\r\n  );\r\n\r\n  const renderBrandDetails = () => {\r\n    if (!selectedBrand) return null;\r\n    const { pendingUpdates = {}, ...currentBrand } = selectedBrand;\r\n    // Show all fields in pendingUpdates\r\n    return (\r\n      <Box sx={{ p: 2 }}>\r\n        <Grid container spacing={2}>\r\n          {Object.keys(pendingUpdates).length === 0 ? (\r\n            <Typography>No pending updates for this brand.</Typography>\r\n          ) : (\r\n            Object.entries(pendingUpdates).map(([key, pendingValue]) => (\r\n              <Grid item xs={12} sm={6} key={key}>\r\n                <Typography\r\n                  variant=\"subtitle2\"\r\n                  color=\"text.secondary\"\r\n                  sx={{ textTransform: \"capitalize\" }}\r\n                >\r\n                  {key.replace(/([A-Z])/g, \" $1\").trim()}\r\n                </Typography>\r\n                {renderFieldDiff(key, currentBrand[key], pendingValue)}\r\n              </Grid>\r\n            ))\r\n          )}\r\n        </Grid>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ p: 3 }}>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        Pending Brand Updates\r\n      </Typography>\r\n      {loading ? (\r\n        <Box sx={{ display: \"flex\", justifyContent: \"center\", p: 4 }}>\r\n          <CircularProgress size={60} thickness={4} sx={{ color: \"#6b7b58\" }} />\r\n        </Box>\r\n      ) : pendingBrands.length === 0 ? (\r\n        <Box sx={{ textAlign: \"center\", mt: 6 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            No brand updates pending approval.\r\n          </Typography>\r\n        </Box>\r\n      ) : (\r\n        <Box>{renderBrandCards(pendingBrands)}</Box>\r\n      )}\r\n      {/* Brand Details Dialog */}\r\n      <Dialog\r\n        open={openDialog}\r\n        onClose={handleCloseDialog}\r\n        maxWidth=\"md\"\r\n        fullWidth\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          {selectedBrand?.brandName}\r\n          <IconButton onClick={handleCloseDialog}>\r\n            <CloseIcon />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent dividers>{renderBrandDetails()}</DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleReject} color=\"error\" disabled={actionLoading}>\r\n            {actionLoading ? \"Rejecting...\" : \"Reject\"}\r\n          </Button>\r\n          <Button\r\n            onClick={handleApprove}\r\n            color=\"primary\"\r\n            disabled={actionLoading}\r\n          >\r\n            {actionLoading ? \"Approving...\" : \"Approve\"}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n      {/* Snackbar for notifications */}\r\n      <Snackbar\r\n        open={snackbar.open}\r\n        autoHideDuration={6000}\r\n        onClose={handleCloseSnackbar}\r\n        anchorOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseSnackbar}\r\n          severity={snackbar.severity}\r\n          variant=\"filled\"\r\n        >\r\n          {snackbar.message}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default PendingBrandUpdates;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,QACL,eAAe;AACtB,SAASC,KAAK,IAAIC,SAAS,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC;IACvCqC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFtC,SAAS,CAAC,MAAM;IACduC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAC9B,8DACF,CAAC;MACDhB,gBAAgB,CAACe,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdR,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,gCAAgC;QACzCC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,eAAe,GAAIC,KAAK,IAAK;IACjChB,gBAAgB,CAACgB,KAAK,CAAC;IACvBd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bf,aAAa,CAAC,KAAK,CAAC;IACpBF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACnB,aAAa,EAAE;IACpBK,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMhC,KAAK,CAAC+C,GAAG,CACb,wDAAwDpB,aAAa,CAACqB,GAAG,UAC3E,CAAC;MACDd,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,uBAAuB;QAChCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFC,kBAAkB,CAAC,CAAC;MACpBO,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdR,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,0BAA0B;QACnCC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRL,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACtB,aAAa,EAAE;IACpBK,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF,MAAMhC,KAAK,CAAC+C,GAAG,CACb,wDAAwDpB,aAAa,CAACqB,GAAG,SAC3E,CAAC;MACDd,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,uBAAuB;QAChCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFC,kBAAkB,CAAC,CAAC;MACpBO,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdR,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,yBAAyB;QAClCC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRL,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMkB,mBAAmB,GAAGA,CAAA,KAAM;IAChChB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMgB,WAAW,GAAIC,GAAG,IAAK;IAC3B,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,EAAE,OAAO,KAAK;IACnD,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE,OAAOE,IAAI,CAACC,SAAS,CAACH,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;IAChE,OAAOA,GAAG;EACZ,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACnC,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;IACzE,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;IACnE,IAAID,WAAW,CAACE,QAAQ,CAACJ,GAAG,CAAC,EAAE,OAAO,IAAI;IAC1C,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOE,eAAe,CAACE,IAAI,CAAEC,GAAG,IAAKL,KAAK,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAC;IACzE;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAACC,QAAQ,EAAEC,GAAG,KAAK;IACrC,IAAI,CAACD,QAAQ,EAAE,OAAO,KAAK;IAC3B,oBACE/C,OAAA;MACEiD,GAAG,EAAE,uDAAuDF,QAAQ,EAAG;MACvEC,GAAG,EAAEA,GAAI;MACTE,KAAK,EAAE;QACLC,QAAQ,EAAE,GAAG;QACbC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE,OAAO;QAChBC,MAAM,EAAE,OAAO;QACfC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEN,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,KAAK;IACnD,IAAI5B,YAAY,CAAC0B,KAAK,EAAEC,OAAO,CAAC,IAAI3B,YAAY,CAAC0B,KAAK,EAAEE,OAAO,CAAC,EAAE;MAChE,oBACEhE,OAAA,CAACnB,GAAG;QAAAoF,QAAA,gBACFjE,OAAA,CAACf,UAAU;UAACiF,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAEnD;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZd,WAAW,CAACiB,OAAO,EAAE,SAAS,CAAC,eAChC/D,OAAA,CAACf,UAAU;UAACiF,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,SAAS;UAAAF,QAAA,EAAC;QAE5C;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZd,WAAW,CAACkB,OAAO,EAAE,SAAS,CAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAEV;IACA,IAAI1B,IAAI,CAACC,SAAS,CAAC4B,OAAO,CAAC,KAAK7B,IAAI,CAACC,SAAS,CAAC6B,OAAO,CAAC,EAAE;MACvD,oBAAOhE,OAAA,CAACf,UAAU;QAACiF,OAAO,EAAC,OAAO;QAAAD,QAAA,EAAElC,WAAW,CAACgC,OAAO;MAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IACxE;IACA,oBACE5D,OAAA,CAACnB,GAAG;MAAAoF,QAAA,gBACFjE,OAAA,CAACf,UAAU;QAACiF,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,GAAC,WACxC,EAAClC,WAAW,CAACgC,OAAO,CAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACb5D,OAAA,CAACf,UAAU;QAACiF,OAAO,EAAC,OAAO;QAACC,KAAK,EAAC,SAAS;QAAAF,QAAA,GAAC,WACjC,EAAClC,WAAW,CAACiC,OAAO,CAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV,CAAC;EAED,MAAMQ,gBAAgB,GAAIC,UAAU,iBAClCrE,OAAA,CAACd,IAAI;IAACoF,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAN,QAAA,EACxBI,UAAU,CAACG,GAAG,CAAEhD,KAAK;MAAA,IAAAiD,qBAAA;MAAA,oBACpBzE,OAAA,CAACd,IAAI;QAACwF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BjE,OAAA,CAAClB,IAAI;UACHgG,EAAE,EAAE;YACFC,MAAM,EAAE,MAAM;YACd1B,OAAO,EAAE,MAAM;YACf2B,aAAa,EAAE,QAAQ;YACvBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UACFC,OAAO,EAAEA,CAAA,KAAM9D,eAAe,CAACC,KAAK,CAAE;UAAAyC,QAAA,gBAEtCjE,OAAA,CAAChB,SAAS;YACRsG,SAAS,EAAC,KAAK;YACfP,MAAM,EAAC,KAAK;YACZQ,KAAK,EAAE,uDACL/D,KAAK,CAACgE,SAAS,MACXC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;YACnB1C,GAAG,EAAExB,KAAK,CAACmE,SAAU;YACrBb,EAAE,EAAE;cACFc,SAAS,EAAE,SAAS;cACpBC,OAAO,EAAE,CAAC;cACVC,eAAe,EAAE;YACnB;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF5D,OAAA,CAACjB,WAAW;YAAC+F,EAAE,EAAE;cAAEiB,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/BjE,OAAA,CAACf,UAAU;cAAC+G,YAAY;cAAC9B,OAAO,EAAC,IAAI;cAACoB,SAAS,EAAC,KAAK;cAAArB,QAAA,EAClDzC,KAAK,CAACmE;YAAS;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACb5D,OAAA,CAACf,UAAU;cAACiF,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAACW,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,IAAAQ,qBAAA,GAC9DjD,KAAK,CAAC0E,gBAAgB,cAAAzB,qBAAA,uBAAtBA,qBAAA,CAAwB0B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAC7C;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5D,OAAA,CAACL,OAAO;cAACmF,EAAE,EAAE;gBAAEsB,EAAE,EAAE;cAAE;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1B5D,OAAA,CAACf,UAAU;cAACiF,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAAAF,QAAA,gBAChDjE,OAAA;gBAAAiE,QAAA,EAAQ;cAAQ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpC,KAAK,CAAC6E,cAAc;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACb5D,OAAA,CAACf,UAAU;cAACiF,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAAAF,QAAA,gBAChDjE,OAAA;gBAAAiE,QAAA,EAAQ;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpC,KAAK,CAAC8E,WAAW;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACb5D,OAAA,CAACf,UAAU;cAACiF,OAAO,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAAAF,QAAA,gBAChDjE,OAAA;gBAAAiE,QAAA,EAAQ;cAAM;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpC,KAAK,CAAC+E,KAAK;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA9C6BpC,KAAK,CAACI,GAAG;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+CzC,CAAC;IAAA,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;EAED,MAAM4C,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACjG,aAAa,EAAE,OAAO,IAAI;IAC/B,MAAM;MAAEkG,cAAc,GAAG,CAAC,CAAC;MAAE,GAAGC;IAAa,CAAC,GAAGnG,aAAa;IAC9D;IACA,oBACEP,OAAA,CAACnB,GAAG;MAACiG,EAAE,EAAE;QAAE6B,CAAC,EAAE;MAAE,CAAE;MAAA1C,QAAA,eAChBjE,OAAA,CAACd,IAAI;QAACoF,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAN,QAAA,EACxB2C,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAACK,MAAM,KAAK,CAAC,gBACvC9G,OAAA,CAACf,UAAU;UAAAgF,QAAA,EAAC;QAAkC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GAE3DgD,MAAM,CAACG,OAAO,CAACN,cAAc,CAAC,CAACjC,GAAG,CAAC,CAAC,CAACnC,GAAG,EAAE2E,YAAY,CAAC,kBACrDhH,OAAA,CAACd,IAAI;UAACwF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,gBACvBjE,OAAA,CAACf,UAAU;YACTiF,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,gBAAgB;YACtBW,EAAE,EAAE;cAAEmC,aAAa,EAAE;YAAa,CAAE;YAAAhD,QAAA,EAEnC5B,GAAG,CAAC6E,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;UAAC;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,EACZC,eAAe,CAACxB,GAAG,EAAEqE,YAAY,CAACrE,GAAG,CAAC,EAAE2E,YAAY,CAAC;QAAA,GARzB3E,GAAG;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAS5B,CACP;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV,CAAC;EAED,oBACE5D,OAAA,CAACnB,GAAG;IAACiG,EAAE,EAAE;MAAE6B,CAAC,EAAE;IAAE,CAAE;IAAA1C,QAAA,gBAChBjE,OAAA,CAACf,UAAU;MAACiF,OAAO,EAAC,IAAI;MAAC8B,YAAY;MAAA/B,QAAA,EAAC;IAEtC;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EACZvD,OAAO,gBACNL,OAAA,CAACnB,GAAG;MAACiG,EAAE,EAAE;QAAEzB,OAAO,EAAE,MAAM;QAAE+D,cAAc,EAAE,QAAQ;QAAET,CAAC,EAAE;MAAE,CAAE;MAAA1C,QAAA,eAC3DjE,OAAA,CAACN,gBAAgB;QAAC2H,IAAI,EAAE,EAAG;QAACC,SAAS,EAAE,CAAE;QAACxC,EAAE,EAAE;UAAEX,KAAK,EAAE;QAAU;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,GACJzD,aAAa,CAAC2G,MAAM,KAAK,CAAC,gBAC5B9G,OAAA,CAACnB,GAAG;MAACiG,EAAE,EAAE;QAAEyC,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAvD,QAAA,eACtCjE,OAAA,CAACf,UAAU;QAACiF,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAEhD;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEN5D,OAAA,CAACnB,GAAG;MAAAoF,QAAA,EAAEG,gBAAgB,CAACjE,aAAa;IAAC;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC5C,eAED5D,OAAA,CAACb,MAAM;MACL4B,IAAI,EAAEN,UAAW;MACjBgH,OAAO,EAAEhG,iBAAkB;MAC3B0B,QAAQ,EAAC,IAAI;MACbuE,SAAS;MAAAzD,QAAA,gBAETjE,OAAA,CAACZ,WAAW;QACV0F,EAAE,EAAE;UACFzB,OAAO,EAAE,MAAM;UACf+D,cAAc,EAAE,eAAe;UAC/BO,UAAU,EAAE;QACd,CAAE;QAAA1D,QAAA,GAED1D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoF,SAAS,eACzB3F,OAAA,CAACJ,UAAU;UAACyF,OAAO,EAAE5D,iBAAkB;UAAAwC,QAAA,eACrCjE,OAAA,CAACF,SAAS;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd5D,OAAA,CAACX,aAAa;QAACuI,QAAQ;QAAA3D,QAAA,EAAEuC,kBAAkB,CAAC;MAAC;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,eAC9D5D,OAAA,CAACV,aAAa;QAAA2E,QAAA,gBACZjE,OAAA,CAACT,MAAM;UAAC8F,OAAO,EAAExD,YAAa;UAACsC,KAAK,EAAC,OAAO;UAAC0D,QAAQ,EAAElH,aAAc;UAAAsD,QAAA,EAClEtD,aAAa,GAAG,cAAc,GAAG;QAAQ;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACT5D,OAAA,CAACT,MAAM;UACL8F,OAAO,EAAE3D,aAAc;UACvByC,KAAK,EAAC,SAAS;UACf0D,QAAQ,EAAElH,aAAc;UAAAsD,QAAA,EAEvBtD,aAAa,GAAG,cAAc,GAAG;QAAS;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET5D,OAAA,CAACR,QAAQ;MACPuB,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB+G,gBAAgB,EAAE,IAAK;MACvBL,OAAO,EAAE3F,mBAAoB;MAC7BiG,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAhE,QAAA,eAEvDjE,OAAA,CAACP,KAAK;QACJgI,OAAO,EAAE3F,mBAAoB;QAC7Bb,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAC5BiD,OAAO,EAAC,QAAQ;QAAAD,QAAA,EAEfpD,QAAQ,CAACG;MAAO;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC1D,EAAA,CA5TID,mBAAmB;AAAAiI,EAAA,GAAnBjI,mBAAmB;AA8TzB,eAAeA,mBAAmB;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}