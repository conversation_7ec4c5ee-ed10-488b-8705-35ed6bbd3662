{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\adminSide\\\\tags.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Box, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, Button, Select, MenuItem, TextField } from \"@mui/material\";\nimport { CiCirclePlus } from \"react-icons/ci\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TAG_CATEGORIES = [\"Color\", \"Style\", \"Material\", \"Finish\", \"Size\", \"Shape\", \"Functionality\"];\nconst TagsTable = () => {\n  _s();\n  const [tags, setTags] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedTag, setSelectedTag] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [openAddDialog, setOpenAddDialog] = useState(false);\n  const [tagName, setTagName] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState(\"\");\n  useEffect(() => {\n    fetchTags();\n  }, []);\n  const fetchTags = async () => {\n    try {\n      const response = await axios.get(\"https://api.thedesigngrit.com/api/tags\");\n      setTags(response.data);\n    } catch (error) {\n      console.error(\"Error fetching tags:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEditClick = tag => {\n    setSelectedTag(tag);\n    setSelectedCategory(tag.category);\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedTag(null);\n  };\n  const handleCloseAddDialog = () => {\n    setOpenAddDialog(false);\n    setTagName(\"\");\n    setSelectedCategory(\"\");\n  };\n  const handleSaveTag = async () => {\n    if (!tagName || !selectedCategory) {\n      alert(\"Please enter a tag name and select a category.\");\n      return;\n    }\n    try {\n      await axios.post(\"https://api.thedesigngrit.com/api/tags\", {\n        name: tagName,\n        category: selectedCategory\n      });\n      fetchTags();\n      handleCloseAddDialog();\n    } catch (error) {\n      console.error(\"Error adding tag:\", error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Tags\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Home > Tags\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-date-vendor\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setOpenAddDialog(true),\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"5px\",\n            backgroundColor: \"#2d2d2d\",\n            color: \"white\",\n            padding: \"15px 15px\",\n            borderRadius: \"8px\",\n            border: \"none\",\n            cursor: \"pointer\",\n            fontSize: \"14px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(CiCirclePlus, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), \" Add Tag\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"dashboard-lists-vendor\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recent-orders-vendor\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              color: \"#2d2d2d\",\n              textAlign: \"left\"\n            },\n            children: \"Tags List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: {\n                backgroundColor: \"#f2f2f2\",\n                color: \"#2d2d2d\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Id\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Tag Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), loading ? /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: 3,\n                  style: {\n                    textAlign: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    style: {\n                      color: \"#6b7b58\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: tags.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: 3,\n                  style: {\n                    textAlign: \"center\"\n                  },\n                  children: \"No tags found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 21\n              }, this) : tags.map(tag => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: tag.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: tag.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: tag.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    display: \"flex\",\n                    gap: \"10px\",\n                    flexDirection: \"row\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEditClick(tag),\n                    style: {\n                      color: \"#e3e3e3\",\n                      backgroundColor: \"#6a8452\"\n                    },\n                    children: \"Edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEditClick(tag),\n                    style: {\n                      color: \"#e3e3e3\",\n                      backgroundColor: \"#6a8452\"\n                    },\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 25\n                }, this)]\n              }, tag._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openAddDialog,\n      onClose: handleCloseAddDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Add New Tag\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Tag Name\",\n          fullWidth: true,\n          value: tagName,\n          onChange: e => setTagName(e.target.value),\n          margin: \"dense\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginTop: \"10px\"\n          },\n          children: \"Category:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: selectedCategory,\n          onChange: e => setSelectedCategory(e.target.value),\n          fullWidth: true,\n          children: TAG_CATEGORIES.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: category,\n            children: category\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAddDialog,\n          color: \"secondary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveTag,\n          color: \"primary\",\n          children: [\"Add\", \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Edit Tag\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\"\n          },\n          children: \"Tag Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Tag Name\",\n          fullWidth: true,\n          value: tagName,\n          onChange: e => setTagName(e.target.value),\n          margin: \"dense\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: \"block\",\n            marginTop: \"10px\"\n          },\n          children: \"Category:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: selectedCategory,\n          onChange: e => setSelectedCategory(e.target.value),\n          fullWidth: true,\n          children: TAG_CATEGORIES.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: category,\n            children: category\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          color: \"secondary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"primary\",\n          children: \"Update\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(TagsTable, \"hLW7mHZ32A3sHGrxklqOpb8NFHw=\");\n_c = TagsTable;\nexport default TagsTable;\nvar _c;\n$RefreshReg$(_c, \"TagsTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Select", "MenuItem", "TextField", "CiCirclePlus", "jsxDEV", "_jsxDEV", "TAG_CATEGORIES", "TagsTable", "_s", "tags", "setTags", "loading", "setLoading", "selectedTag", "setSelectedTag", "openDialog", "setOpenDialog", "openAddDialog", "setOpenAddDialog", "tagName", "setTagName", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "fetchTags", "response", "get", "data", "error", "console", "handleEditClick", "tag", "category", "handleCloseDialog", "handleCloseAddDialog", "handleSaveTag", "alert", "post", "name", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "display", "alignItems", "gap", "backgroundColor", "color", "padding", "borderRadius", "border", "cursor", "fontSize", "sx", "flexDirection", "textAlign", "colSpan", "length", "map", "id", "_id", "open", "onClose", "label", "fullWidth", "value", "onChange", "e", "target", "margin", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/adminSide/tags.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Box,\r\n  CircularProgress,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Button,\r\n  Select,\r\n  MenuItem,\r\n  TextField,\r\n} from \"@mui/material\";\r\nimport { CiCirclePlus } from \"react-icons/ci\";\r\n\r\nconst TAG_CATEGORIES = [\r\n  \"Color\",\r\n  \"Style\",\r\n  \"Material\",\r\n  \"Finish\",\r\n  \"Size\",\r\n  \"Shape\",\r\n  \"Functionality\",\r\n];\r\n\r\nconst TagsTable = () => {\r\n  const [tags, setTags] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [selectedTag, setSelectedTag] = useState(null);\r\n  const [openDialog, setOpenDialog] = useState(false);\r\n  const [openAddDialog, setOpenAddDialog] = useState(false);\r\n  const [tagName, setTagName] = useState(\"\");\r\n  const [selectedCategory, setSelectedCategory] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    fetchTags();\r\n  }, []);\r\n\r\n  const fetchTags = async () => {\r\n    try {\r\n      const response = await axios.get(\r\n        \"https://api.thedesigngrit.com/api/tags\"\r\n      );\r\n      setTags(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching tags:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleEditClick = (tag) => {\r\n    setSelectedTag(tag);\r\n    setSelectedCategory(tag.category);\r\n    setOpenDialog(true);\r\n  };\r\n\r\n  const handleCloseDialog = () => {\r\n    setOpenDialog(false);\r\n    setSelectedTag(null);\r\n  };\r\n\r\n  const handleCloseAddDialog = () => {\r\n    setOpenAddDialog(false);\r\n    setTagName(\"\");\r\n    setSelectedCategory(\"\");\r\n  };\r\n\r\n  const handleSaveTag = async () => {\r\n    if (!tagName || !selectedCategory) {\r\n      alert(\"Please enter a tag name and select a category.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await axios.post(\"https://api.thedesigngrit.com/api/tags\", {\r\n        name: tagName,\r\n        category: selectedCategory,\r\n      });\r\n      fetchTags();\r\n      handleCloseAddDialog();\r\n    } catch (error) {\r\n      console.error(\"Error adding tag:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <h2>Tags</h2>\r\n          <p>Home &gt; Tags</p>\r\n        </div>\r\n        <div className=\"dashboard-date-vendor\">\r\n          <button\r\n            onClick={() => setOpenAddDialog(true)}\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n              gap: \"5px\",\r\n              backgroundColor: \"#2d2d2d\",\r\n              color: \"white\",\r\n              padding: \"15px 15px\",\r\n              borderRadius: \"8px\",\r\n              border: \"none\",\r\n              cursor: \"pointer\",\r\n              fontSize: \"14px\",\r\n            }}\r\n          >\r\n            <CiCirclePlus /> Add Tag\r\n          </button>\r\n        </div>\r\n      </header>\r\n\r\n      <section className=\"dashboard-lists-vendor\">\r\n        <div className=\"recent-orders-vendor\">\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <h2 style={{ color: \"#2d2d2d\", textAlign: \"left\" }}>Tags List</h2>\r\n            <table>\r\n              <thead style={{ backgroundColor: \"#f2f2f2\", color: \"#2d2d2d\" }}>\r\n                <tr>\r\n                  <th>Id</th>\r\n                  <th>Tag Name</th>\r\n                  <th>Category</th>\r\n                  <th>Actions</th>\r\n                </tr>\r\n              </thead>\r\n              {loading ? (\r\n                <tbody>\r\n                  <tr>\r\n                    <td colSpan={3} style={{ textAlign: \"center\" }}>\r\n                      <CircularProgress style={{ color: \"#6b7b58\" }} />\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              ) : (\r\n                <tbody>\r\n                  {tags.length === 0 ? (\r\n                    <tr>\r\n                      <td colSpan={3} style={{ textAlign: \"center\" }}>\r\n                        No tags found\r\n                      </td>\r\n                    </tr>\r\n                  ) : (\r\n                    tags.map((tag) => (\r\n                      <tr key={tag._id}>\r\n                        <td>{tag.id}</td>\r\n                        <td>{tag.name}</td>\r\n                        <td>{tag.category}</td>\r\n                        <td\r\n                          style={{\r\n                            display: \"flex\",\r\n                            gap: \"10px\",\r\n                            flexDirection: \"row\",\r\n                          }}\r\n                        >\r\n                          <button\r\n                            onClick={() => handleEditClick(tag)}\r\n                            style={{\r\n                              color: \"#e3e3e3\",\r\n                              backgroundColor: \"#6a8452\",\r\n                            }}\r\n                          >\r\n                            Edit\r\n                          </button>\r\n                          <button\r\n                            onClick={() => handleEditClick(tag)}\r\n                            style={{\r\n                              color: \"#e3e3e3\",\r\n                              backgroundColor: \"#6a8452\",\r\n                            }}\r\n                          >\r\n                            Delete\r\n                          </button>\r\n                        </td>\r\n                      </tr>\r\n                    ))\r\n                  )}\r\n                </tbody>\r\n              )}\r\n            </table>\r\n          </Box>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Add Tag Dialog */}\r\n      <Dialog open={openAddDialog} onClose={handleCloseAddDialog}>\r\n        <DialogTitle>Add New Tag</DialogTitle>\r\n        <DialogContent>\r\n          <TextField\r\n            label=\"Tag Name\"\r\n            fullWidth\r\n            value={tagName}\r\n            onChange={(e) => setTagName(e.target.value)}\r\n            margin=\"dense\"\r\n          />\r\n          <label style={{ display: \"block\", marginTop: \"10px\" }}>\r\n            Category:\r\n          </label>\r\n          <Select\r\n            value={selectedCategory}\r\n            onChange={(e) => setSelectedCategory(e.target.value)}\r\n            fullWidth\r\n          >\r\n            {TAG_CATEGORIES.map((category) => (\r\n              <MenuItem key={category} value={category}>\r\n                {category}\r\n              </MenuItem>\r\n            ))}\r\n          </Select>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseAddDialog} color=\"secondary\">\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleSaveTag} color=\"primary\">\r\n            Add{\" \"}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Edit Tag Dialog */}\r\n      <Dialog open={openDialog} onClose={handleCloseDialog}>\r\n        <DialogTitle>Edit Tag</DialogTitle>\r\n        <DialogContent>\r\n          <label style={{ display: \"block\" }}>Tag Name:</label>\r\n          <TextField\r\n            label=\"Tag Name\"\r\n            fullWidth\r\n            value={tagName}\r\n            onChange={(e) => setTagName(e.target.value)}\r\n            margin=\"dense\"\r\n          />\r\n          <label style={{ display: \"block\", marginTop: \"10px\" }}>\r\n            Category:\r\n          </label>\r\n          <Select\r\n            value={selectedCategory}\r\n            onChange={(e) => setSelectedCategory(e.target.value)}\r\n            fullWidth\r\n          >\r\n            {TAG_CATEGORIES.map((category) => (\r\n              <MenuItem key={category} value={category}>\r\n                {category}\r\n              </MenuItem>\r\n            ))}\r\n          </Select>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseDialog} color=\"secondary\">\r\n            Cancel\r\n          </Button>\r\n          <Button color=\"primary\">Update</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TagsTable;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,SAAS,QACJ,eAAe;AACtB,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,cAAc,GAAG,CACrB,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,MAAM,EACN,OAAO,EACP,eAAe,CAChB;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACdgC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAC9B,wCACF,CAAC;MACDf,OAAO,CAACc,QAAQ,CAACE,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,eAAe,GAAIC,GAAG,IAAK;IAC/BhB,cAAc,CAACgB,GAAG,CAAC;IACnBR,mBAAmB,CAACQ,GAAG,CAACC,QAAQ,CAAC;IACjCf,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhB,aAAa,CAAC,KAAK,CAAC;IACpBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMmB,oBAAoB,GAAGA,CAAA,KAAM;IACjCf,gBAAgB,CAAC,KAAK,CAAC;IACvBE,UAAU,CAAC,EAAE,CAAC;IACdE,mBAAmB,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMY,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACf,OAAO,IAAI,CAACE,gBAAgB,EAAE;MACjCc,KAAK,CAAC,gDAAgD,CAAC;MACvD;IACF;IAEA,IAAI;MACF,MAAM3C,KAAK,CAAC4C,IAAI,CAAC,wCAAwC,EAAE;QACzDC,IAAI,EAAElB,OAAO;QACbY,QAAQ,EAAEV;MACZ,CAAC,CAAC;MACFE,SAAS,CAAC,CAAC;MACXU,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;EACF,CAAC;EAED,oBACEtB,OAAA;IAAAiC,QAAA,gBACEjC,OAAA;MAAQkC,SAAS,EAAC,yBAAyB;MAAAD,QAAA,gBACzCjC,OAAA;QAAKkC,SAAS,EAAC,wBAAwB;QAAAD,QAAA,gBACrCjC,OAAA;UAAAiC,QAAA,EAAI;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACbtC,OAAA;UAAAiC,QAAA,EAAG;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACNtC,OAAA;QAAKkC,SAAS,EAAC,uBAAuB;QAAAD,QAAA,eACpCjC,OAAA;UACEuC,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC,IAAI,CAAE;UACtC2B,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,KAAK;YACVC,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,WAAW;YACpBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,SAAS;YACjBC,QAAQ,EAAE;UACZ,CAAE;UAAAjB,QAAA,gBAEFjC,OAAA,CAACF,YAAY;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETtC,OAAA;MAASkC,SAAS,EAAC,wBAAwB;MAAAD,QAAA,eACzCjC,OAAA;QAAKkC,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACnCjC,OAAA,CAACZ,GAAG;UACF+D,EAAE,EAAE;YACFV,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBV,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,gBAEFjC,OAAA;YAAIwC,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEQ,SAAS,EAAE;YAAO,CAAE;YAAApB,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEtC,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAOwC,KAAK,EAAE;gBAAEI,eAAe,EAAE,SAAS;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAZ,QAAA,eAC7DjC,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAAiC,QAAA,EAAI;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACXtC,OAAA;kBAAAiC,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBtC,OAAA;kBAAAiC,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBtC,OAAA;kBAAAiC,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EACPhC,OAAO,gBACNN,OAAA;cAAAiC,QAAA,eACEjC,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAIsD,OAAO,EAAE,CAAE;kBAACd,KAAK,EAAE;oBAAEa,SAAS,EAAE;kBAAS,CAAE;kBAAApB,QAAA,eAC7CjC,OAAA,CAACX,gBAAgB;oBAACmD,KAAK,EAAE;sBAAEK,KAAK,EAAE;oBAAU;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAERtC,OAAA;cAAAiC,QAAA,EACG7B,IAAI,CAACmD,MAAM,KAAK,CAAC,gBAChBvD,OAAA;gBAAAiC,QAAA,eACEjC,OAAA;kBAAIsD,OAAO,EAAE,CAAE;kBAACd,KAAK,EAAE;oBAAEa,SAAS,EAAE;kBAAS,CAAE;kBAAApB,QAAA,EAAC;gBAEhD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,GAELlC,IAAI,CAACoD,GAAG,CAAE/B,GAAG,iBACXzB,OAAA;gBAAAiC,QAAA,gBACEjC,OAAA;kBAAAiC,QAAA,EAAKR,GAAG,CAACgC;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjBtC,OAAA;kBAAAiC,QAAA,EAAKR,GAAG,CAACO;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBtC,OAAA;kBAAAiC,QAAA,EAAKR,GAAG,CAACC;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBtC,OAAA;kBACEwC,KAAK,EAAE;oBACLC,OAAO,EAAE,MAAM;oBACfE,GAAG,EAAE,MAAM;oBACXS,aAAa,EAAE;kBACjB,CAAE;kBAAAnB,QAAA,gBAEFjC,OAAA;oBACEuC,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAACC,GAAG,CAAE;oBACpCe,KAAK,EAAE;sBACLK,KAAK,EAAE,SAAS;sBAChBD,eAAe,EAAE;oBACnB,CAAE;oBAAAX,QAAA,EACH;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTtC,OAAA;oBACEuC,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAACC,GAAG,CAAE;oBACpCe,KAAK,EAAE;sBACLK,KAAK,EAAE,SAAS;sBAChBD,eAAe,EAAE;oBACnB,CAAE;oBAAAX,QAAA,EACH;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GA7BEb,GAAG,CAACiC,GAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8BZ,CACL;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVtC,OAAA,CAACV,MAAM;MAACqE,IAAI,EAAE/C,aAAc;MAACgD,OAAO,EAAEhC,oBAAqB;MAAAK,QAAA,gBACzDjC,OAAA,CAACT,WAAW;QAAA0C,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtCtC,OAAA,CAACR,aAAa;QAAAyC,QAAA,gBACZjC,OAAA,CAACH,SAAS;UACRgE,KAAK,EAAC,UAAU;UAChBC,SAAS;UACTC,KAAK,EAAEjD,OAAQ;UACfkD,QAAQ,EAAGC,CAAC,IAAKlD,UAAU,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC5CI,MAAM,EAAC;QAAO;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFtC,OAAA;UAAOwC,KAAK,EAAE;YAAEC,OAAO,EAAE,OAAO;YAAE2B,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA,CAACL,MAAM;UACLoE,KAAK,EAAE/C,gBAAiB;UACxBgD,QAAQ,EAAGC,CAAC,IAAKhD,mBAAmB,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACrDD,SAAS;UAAA7B,QAAA,EAERhC,cAAc,CAACuD,GAAG,CAAE9B,QAAQ,iBAC3B1B,OAAA,CAACJ,QAAQ;YAAgBmE,KAAK,EAAErC,QAAS;YAAAO,QAAA,EACtCP;UAAQ,GADIA,QAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAChBtC,OAAA,CAACP,aAAa;QAAAwC,QAAA,gBACZjC,OAAA,CAACN,MAAM;UAAC6C,OAAO,EAAEX,oBAAqB;UAACiB,KAAK,EAAC,WAAW;UAAAZ,QAAA,EAAC;QAEzD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtC,OAAA,CAACN,MAAM;UAAC6C,OAAO,EAAEV,aAAc;UAACgB,KAAK,EAAC,SAAS;UAAAZ,QAAA,GAAC,KAC3C,EAAC,GAAG;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtC,OAAA,CAACV,MAAM;MAACqE,IAAI,EAAEjD,UAAW;MAACkD,OAAO,EAAEjC,iBAAkB;MAAAM,QAAA,gBACnDjC,OAAA,CAACT,WAAW;QAAA0C,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACnCtC,OAAA,CAACR,aAAa;QAAAyC,QAAA,gBACZjC,OAAA;UAAOwC,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAQ,CAAE;UAAAR,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrDtC,OAAA,CAACH,SAAS;UACRgE,KAAK,EAAC,UAAU;UAChBC,SAAS;UACTC,KAAK,EAAEjD,OAAQ;UACfkD,QAAQ,EAAGC,CAAC,IAAKlD,UAAU,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC5CI,MAAM,EAAC;QAAO;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACFtC,OAAA;UAAOwC,KAAK,EAAE;YAAEC,OAAO,EAAE,OAAO;YAAE2B,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtC,OAAA,CAACL,MAAM;UACLoE,KAAK,EAAE/C,gBAAiB;UACxBgD,QAAQ,EAAGC,CAAC,IAAKhD,mBAAmB,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACrDD,SAAS;UAAA7B,QAAA,EAERhC,cAAc,CAACuD,GAAG,CAAE9B,QAAQ,iBAC3B1B,OAAA,CAACJ,QAAQ;YAAgBmE,KAAK,EAAErC,QAAS;YAAAO,QAAA,EACtCP;UAAQ,GADIA,QAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAChBtC,OAAA,CAACP,aAAa;QAAAwC,QAAA,gBACZjC,OAAA,CAACN,MAAM;UAAC6C,OAAO,EAAEZ,iBAAkB;UAACkB,KAAK,EAAC,WAAW;UAAAZ,QAAA,EAAC;QAEtD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtC,OAAA,CAACN,MAAM;UAACmD,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnC,EAAA,CA9OID,SAAS;AAAAmE,EAAA,GAATnE,SAAS;AAgPf,eAAeA,SAAS;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}