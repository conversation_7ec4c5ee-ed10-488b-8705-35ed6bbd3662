{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\brandCursol.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { FaWhatsapp } from \"react-icons/fa\";\nimport { IoNewspaperOutline } from \"react-icons/io5\";\nimport { LuPhone } from \"react-icons/lu\";\nimport { HiOutlineChevronRight, HiOutlineChevronLeft } from \"react-icons/hi\";\nimport { Box } from \"@mui/material\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function BrandCursol({\n  brandId,\n  onRequestQuote,\n  mainProductId\n}) {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  useEffect(() => {\n    if (!brandId || !brandId._id) {\n      console.warn(\"Invalid brandId:\", brandId);\n      return;\n    }\n    const fetchProducts = async () => {\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId._id}`);\n        if (!response.ok) {\n          throw new Error(`HTTP error! Status: ${response.status}`);\n        }\n        const data = await response.json();\n        setProducts(data.slice(0, 3)); // Limit to 3 products\n      } catch (error) {\n        console.error(\"Error fetching products:\", error);\n      }\n    };\n    fetchProducts();\n  }, [brandId]);\n  const prevSlide = () => {\n    setCurrentIndex(prev => prev === 0 ? products.length - 1 : prev - 1);\n  };\n  const nextSlide = () => {\n    setCurrentIndex(prev => prev === products.length - 1 ? 0 : prev + 1);\n  };\n  const handleRequestQuote = () => {\n    // Always use the main product ID from the product page\n    if (mainProductId) {\n      // Create a product object with the main product ID and brand data\n      const productData = {\n        _id: mainProductId,\n        brandId: brandId\n      };\n      onRequestQuote(productData);\n    } else {\n      // Fallback to using the carousel product if main product ID is not available\n      if (products.length > 0 && products[currentIndex]) {\n        const currentProduct = products[currentIndex];\n        const productWithBrand = {\n          ...currentProduct,\n          brandId: currentProduct.brandId || brandId\n        };\n        onRequestQuote(productWithBrand);\n      } else {\n        // If no products, just use the brandId\n        onRequestQuote(brandId);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"carousel-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"carousel-contact-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRequestQuote,\n        className: \"contact-link\",\n        style: {\n          cursor: \"pointer\",\n          background: \"none\",\n          border: \"none\",\n          padding: 0,\n          fontFamily: \"montserrat\",\n          fontSize: \"14px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(IoNewspaperOutline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), \" Request a quotation\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"carousel-divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `tel:${brandId.phoneNumber}`,\n        className: \"contact-link\",\n        children: [/*#__PURE__*/_jsxDEV(LuPhone, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), \" Call us at \", brandId.phoneNumber]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"carousel-divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `whatsapp://send?text=Hello, I would like to order a product from your brand. Please contact me at ${brandId.phoneNumber}&phone=+393664455454`,\n        className: \"contact-link\",\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: [/*#__PURE__*/_jsxDEV(FaWhatsapp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), \" Write to us or order on Whatsapp\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), Array.isArray(products) && products.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"carousel-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"carousel-3d\",\n        children: products.filter(product => product && product.mainImage && product.name).map((product, index) => {\n          const position = (index - currentIndex + products.length) % products.length;\n          const rotation = position * 120; // 360 degrees / 3 items = 120 degrees per item\n          const isCenter = position === 0;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"carousel-item\",\n            style: {\n              transform: `rotateY(${rotation}deg) translateZ(${window.innerWidth <= 767 ? 153 : 200}px)`,\n              opacity: isCenter ? 1 : 0.3,\n              filter: isCenter ? \"blur(0)\" : \"blur(4px)\",\n              zIndex: isCenter ? 3 : 1,\n              scale: isCenter ? \"1\" : \"0.8\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n              alt: product.name || \"Product\",\n              className: \"carousel-product-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"carousel-product-name\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"carousel-product-price\",\n              children: product.price ? `${product.price.toLocaleString()} E£` : \"Price Unavailable\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 21\n            }, this)]\n          }, product._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 19\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this), products.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"carousel-button left\",\n          onClick: prevSlide,\n          children: /*#__PURE__*/_jsxDEV(HiOutlineChevronLeft, {\n            style: {\n              position: \"relative\",\n              left: \"50%\",\n              transform: \"translate(-50%, 0%)\",\n              top: \"calc(50% - 10px)\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"carousel-button right\",\n          onClick: nextSlide,\n          children: /*#__PURE__*/_jsxDEV(HiOutlineChevronRight, {\n            style: {\n              position: \"relative\",\n              left: \"50%\",\n              transform: \"translate(-50%, 0%)\",\n              top: \"calc(50% - 10px)\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"carousel-no-products\",\n      children: \"No products available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n}\n_s(BrandCursol, \"WnBo70yQekvGu6Q3ThmIbTEkS9U=\");\n_c = BrandCursol;\nvar _c;\n$RefreshReg$(_c, \"BrandCursol\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaWhatsapp", "IoNewspaperOutline", "LuPhone", "HiOutlineChevronRight", "HiOutlineChevronLeft", "Box", "jsxDEV", "_jsxDEV", "BrandCursol", "brandId", "onRequestQuote", "mainProductId", "_s", "products", "setProducts", "currentIndex", "setCurrentIndex", "_id", "console", "warn", "fetchProducts", "response", "fetch", "ok", "Error", "status", "data", "json", "slice", "error", "prevSlide", "prev", "length", "nextSlide", "handleRequestQuote", "productData", "currentProduct", "productWithBrand", "className", "children", "onClick", "style", "cursor", "background", "border", "padding", "fontFamily", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "phoneNumber", "target", "rel", "Array", "isArray", "filter", "product", "mainImage", "name", "map", "index", "position", "rotation", "isCenter", "transform", "window", "innerWidth", "opacity", "zIndex", "scale", "src", "alt", "price", "toLocaleString", "sx", "display", "justifyContent", "left", "top", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/brandCursol.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { FaWhatsapp } from \"react-icons/fa\";\r\nimport { IoNewspaperOutline } from \"react-icons/io5\";\r\nimport { LuPhone } from \"react-icons/lu\";\r\nimport { HiOutlineChevronRight, HiOutlineChevronLeft } from \"react-icons/hi\";\r\nimport { Box } from \"@mui/material\";\r\n\r\nexport default function BrandCursol({\r\n  brandId,\r\n  onRequestQuote,\r\n  mainProductId,\r\n}) {\r\n  const [products, setProducts] = useState([]);\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (!brandId || !brandId._id) {\r\n      console.warn(\"Invalid brandId:\", brandId);\r\n      return;\r\n    }\r\n\r\n    const fetchProducts = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/products/getproducts/brand/${brandId._id}`\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! Status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        setProducts(data.slice(0, 3)); // Limit to 3 products\r\n      } catch (error) {\r\n        console.error(\"Error fetching products:\", error);\r\n      }\r\n    };\r\n\r\n    fetchProducts();\r\n  }, [brandId]);\r\n\r\n  const prevSlide = () => {\r\n    setCurrentIndex((prev) => (prev === 0 ? products.length - 1 : prev - 1));\r\n  };\r\n\r\n  const nextSlide = () => {\r\n    setCurrentIndex((prev) => (prev === products.length - 1 ? 0 : prev + 1));\r\n  };\r\n\r\n  const handleRequestQuote = () => {\r\n    // Always use the main product ID from the product page\r\n    if (mainProductId) {\r\n      // Create a product object with the main product ID and brand data\r\n      const productData = {\r\n        _id: mainProductId,\r\n        brandId: brandId,\r\n      };\r\n      onRequestQuote(productData);\r\n    } else {\r\n      // Fallback to using the carousel product if main product ID is not available\r\n      if (products.length > 0 && products[currentIndex]) {\r\n        const currentProduct = products[currentIndex];\r\n        const productWithBrand = {\r\n          ...currentProduct,\r\n          brandId: currentProduct.brandId || brandId,\r\n        };\r\n        onRequestQuote(productWithBrand);\r\n      } else {\r\n        // If no products, just use the brandId\r\n        onRequestQuote(brandId);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"carousel-container\">\r\n      {/* Contact Section */}\r\n      <div className=\"carousel-contact-section\">\r\n        <button\r\n          onClick={handleRequestQuote}\r\n          className=\"contact-link\"\r\n          style={{\r\n            cursor: \"pointer\",\r\n            background: \"none\",\r\n            border: \"none\",\r\n            padding: 0,\r\n            fontFamily: \"montserrat\",\r\n            fontSize: \"14px\",\r\n          }}\r\n        >\r\n          <IoNewspaperOutline /> Request a quotation\r\n        </button>\r\n\r\n        <hr className=\"carousel-divider\" />\r\n        <a href={`tel:${brandId.phoneNumber}`} className=\"contact-link\">\r\n          <LuPhone /> Call us at {brandId.phoneNumber}\r\n        </a>\r\n        <hr className=\"carousel-divider\" />\r\n        <a\r\n          href={`whatsapp://send?text=Hello, I would like to order a product from your brand. Please contact me at ${brandId.phoneNumber}&phone=+393664455454`}\r\n          className=\"contact-link\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          <FaWhatsapp /> Write to us or order on Whatsapp\r\n        </a>\r\n      </div>\r\n\r\n      {/* Carousel */}\r\n      {Array.isArray(products) && products.length > 0 ? (\r\n        <div className=\"carousel-wrapper\">\r\n          <div className=\"carousel-3d\">\r\n            {products\r\n              .filter((product) => product && product.mainImage && product.name)\r\n              .map((product, index) => {\r\n                const position =\r\n                  (index - currentIndex + products.length) % products.length;\r\n                const rotation = position * 120; // 360 degrees / 3 items = 120 degrees per item\r\n                const isCenter = position === 0;\r\n\r\n                return (\r\n                  <div\r\n                    key={product._id}\r\n                    className=\"carousel-item\"\r\n                    style={{\r\n                      transform: `rotateY(${rotation}deg) translateZ(${\r\n                        window.innerWidth <= 767 ? 153 : 200\r\n                      }px)`,\r\n                      opacity: isCenter ? 1 : 0.3,\r\n                      filter: isCenter ? \"blur(0)\" : \"blur(4px)\",\r\n                      zIndex: isCenter ? 3 : 1,\r\n                      scale: isCenter ? \"1\" : \"0.8\",\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                      alt={product.name || \"Product\"}\r\n                      className=\"carousel-product-image\"\r\n                    />\r\n                    <h3 className=\"carousel-product-name\">{product.name}</h3>\r\n                    <p className=\"carousel-product-price\">\r\n                      {product.price\r\n                        ? `${product.price.toLocaleString()} E£`\r\n                        : \"Price Unavailable\"}\r\n                    </p>\r\n                  </div>\r\n                );\r\n              })}\r\n          </div>\r\n          {products.length > 1 && (\r\n            <Box sx={{ display: \"flex\", justifyContent: \"center\" }}>\r\n              <button className=\"carousel-button left\" onClick={prevSlide}>\r\n                <HiOutlineChevronLeft\r\n                  style={{\r\n                    position: \"relative\",\r\n                    left: \"50%\",\r\n                    transform: \"translate(-50%, 0%)\",\r\n                    top: \"calc(50% - 10px)\",\r\n                  }}\r\n                />\r\n              </button>\r\n              <button className=\"carousel-button right\" onClick={nextSlide}>\r\n                <HiOutlineChevronRight\r\n                  style={{\r\n                    position: \"relative\",\r\n                    left: \"50%\",\r\n                    transform: \"translate(-50%, 0%)\",\r\n                    top: \"calc(50% - 10px)\",\r\n                  }}\r\n                />\r\n              </button>\r\n            </Box>\r\n          )}\r\n        </div>\r\n      ) : (\r\n        <p className=\"carousel-no-products\">No products available</p>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC5E,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,eAAe,SAASC,WAAWA,CAAC;EAClCC,OAAO;EACPC,cAAc;EACdC;AACF,CAAC,EAAE;EAAAC,EAAA;EACD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,OAAO,IAAI,CAACA,OAAO,CAACQ,GAAG,EAAE;MAC5BC,OAAO,CAACC,IAAI,CAAC,kBAAkB,EAAEV,OAAO,CAAC;MACzC;IACF;IAEA,MAAMW,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,gEAAgEb,OAAO,CAACQ,GAAG,EAC7E,CAAC;QAED,IAAI,CAACI,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCb,WAAW,CAACY,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC;IAEDT,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;EAEb,MAAMqB,SAAS,GAAGA,CAAA,KAAM;IACtBd,eAAe,CAAEe,IAAI,IAAMA,IAAI,KAAK,CAAC,GAAGlB,QAAQ,CAACmB,MAAM,GAAG,CAAC,GAAGD,IAAI,GAAG,CAAE,CAAC;EAC1E,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtBjB,eAAe,CAAEe,IAAI,IAAMA,IAAI,KAAKlB,QAAQ,CAACmB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGD,IAAI,GAAG,CAAE,CAAC;EAC1E,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,IAAIvB,aAAa,EAAE;MACjB;MACA,MAAMwB,WAAW,GAAG;QAClBlB,GAAG,EAAEN,aAAa;QAClBF,OAAO,EAAEA;MACX,CAAC;MACDC,cAAc,CAACyB,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACA,IAAItB,QAAQ,CAACmB,MAAM,GAAG,CAAC,IAAInB,QAAQ,CAACE,YAAY,CAAC,EAAE;QACjD,MAAMqB,cAAc,GAAGvB,QAAQ,CAACE,YAAY,CAAC;QAC7C,MAAMsB,gBAAgB,GAAG;UACvB,GAAGD,cAAc;UACjB3B,OAAO,EAAE2B,cAAc,CAAC3B,OAAO,IAAIA;QACrC,CAAC;QACDC,cAAc,CAAC2B,gBAAgB,CAAC;MAClC,CAAC,MAAM;QACL;QACA3B,cAAc,CAACD,OAAO,CAAC;MACzB;IACF;EACF,CAAC;EAED,oBACEF,OAAA;IAAK+B,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjChC,OAAA;MAAK+B,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACvChC,OAAA;QACEiC,OAAO,EAAEN,kBAAmB;QAC5BI,SAAS,EAAC,cAAc;QACxBG,KAAK,EAAE;UACLC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,MAAM;UAClBC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE,CAAC;UACVC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAE;QAAAR,QAAA,gBAEFhC,OAAA,CAACN,kBAAkB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wBACxB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5C,OAAA;QAAI+B,SAAS,EAAC;MAAkB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnC5C,OAAA;QAAG6C,IAAI,EAAE,OAAO3C,OAAO,CAAC4C,WAAW,EAAG;QAACf,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC7DhC,OAAA,CAACL,OAAO;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAY,EAAC1C,OAAO,CAAC4C,WAAW;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACJ5C,OAAA;QAAI+B,SAAS,EAAC;MAAkB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnC5C,OAAA;QACE6C,IAAI,EAAE,qGAAqG3C,OAAO,CAAC4C,WAAW,sBAAuB;QACrJf,SAAS,EAAC,cAAc;QACxBgB,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,qBAAqB;QAAAhB,QAAA,gBAEzBhC,OAAA,CAACP,UAAU;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qCAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGLK,KAAK,CAACC,OAAO,CAAC5C,QAAQ,CAAC,IAAIA,QAAQ,CAACmB,MAAM,GAAG,CAAC,gBAC7CzB,OAAA;MAAK+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhC,OAAA;QAAK+B,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB1B,QAAQ,CACN6C,MAAM,CAAEC,OAAO,IAAKA,OAAO,IAAIA,OAAO,CAACC,SAAS,IAAID,OAAO,CAACE,IAAI,CAAC,CACjEC,GAAG,CAAC,CAACH,OAAO,EAAEI,KAAK,KAAK;UACvB,MAAMC,QAAQ,GACZ,CAACD,KAAK,GAAGhD,YAAY,GAAGF,QAAQ,CAACmB,MAAM,IAAInB,QAAQ,CAACmB,MAAM;UAC5D,MAAMiC,QAAQ,GAAGD,QAAQ,GAAG,GAAG,CAAC,CAAC;UACjC,MAAME,QAAQ,GAAGF,QAAQ,KAAK,CAAC;UAE/B,oBACEzD,OAAA;YAEE+B,SAAS,EAAC,eAAe;YACzBG,KAAK,EAAE;cACL0B,SAAS,EAAE,WAAWF,QAAQ,mBAC5BG,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,KACjC;cACLC,OAAO,EAAEJ,QAAQ,GAAG,CAAC,GAAG,GAAG;cAC3BR,MAAM,EAAEQ,QAAQ,GAAG,SAAS,GAAG,WAAW;cAC1CK,MAAM,EAAEL,QAAQ,GAAG,CAAC,GAAG,CAAC;cACxBM,KAAK,EAAEN,QAAQ,GAAG,GAAG,GAAG;YAC1B,CAAE;YAAA3B,QAAA,gBAEFhC,OAAA;cACEkE,GAAG,EAAE,uDAAuDd,OAAO,CAACC,SAAS,EAAG;cAChFc,GAAG,EAAEf,OAAO,CAACE,IAAI,IAAI,SAAU;cAC/BvB,SAAS,EAAC;YAAwB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACF5C,OAAA;cAAI+B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEoB,OAAO,CAACE;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzD5C,OAAA;cAAG+B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAClCoB,OAAO,CAACgB,KAAK,GACV,GAAGhB,OAAO,CAACgB,KAAK,CAACC,cAAc,CAAC,CAAC,KAAK,GACtC;YAAmB;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA,GAtBCQ,OAAO,CAAC1C,GAAG;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBb,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EACLtC,QAAQ,CAACmB,MAAM,GAAG,CAAC,iBAClBzB,OAAA,CAACF,GAAG;QAACwE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAxC,QAAA,gBACrDhC,OAAA;UAAQ+B,SAAS,EAAC,sBAAsB;UAACE,OAAO,EAAEV,SAAU;UAAAS,QAAA,eAC1DhC,OAAA,CAACH,oBAAoB;YACnBqC,KAAK,EAAE;cACLuB,QAAQ,EAAE,UAAU;cACpBgB,IAAI,EAAE,KAAK;cACXb,SAAS,EAAE,qBAAqB;cAChCc,GAAG,EAAE;YACP;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT5C,OAAA;UAAQ+B,SAAS,EAAC,uBAAuB;UAACE,OAAO,EAAEP,SAAU;UAAAM,QAAA,eAC3DhC,OAAA,CAACJ,qBAAqB;YACpBsC,KAAK,EAAE;cACLuB,QAAQ,EAAE,UAAU;cACpBgB,IAAI,EAAE,KAAK;cACXb,SAAS,EAAE,qBAAqB;cAChCc,GAAG,EAAE;YACP;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAEN5C,OAAA;MAAG+B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAAC;IAAqB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAC7D;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvC,EAAA,CA5KuBJ,WAAW;AAAA0E,EAAA,GAAX1E,WAAW;AAAA,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}