{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\TrackQuotation.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useContext } from \"react\";\nimport { Box, Typography, Select, MenuItem, FormControl } from \"@mui/material\";\nimport { UserContext } from \"../utils/userContext\";\nimport LoadingScreen from \"../Pages/loadingScreen\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport QuotationDealSuccess from \"./quotationDealSuccess\";\nimport Dialog from \"@mui/material/Dialog\";\nimport DialogTitle from \"@mui/material/DialogTitle\";\nimport IconButton from \"@mui/material/IconButton\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport paymobService from \"../services/paymobService\";\nimport { useCart } from \"../Context/cartcontext\";\nimport InfoOutlinedIcon from \"@mui/icons-material/InfoOutlined\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction TrackQuotation() {\n  _s();\n  var _selectedQuotation$pr5, _selectedQuotation$pr6, _selectedQuotation$pa;\n  const {\n    userSession\n  } = useContext(UserContext);\n  const [quotations, setQuotations] = useState([]);\n  const [selectedQuotation, setSelectedQuotation] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [showDealSuccess, setShowDealSuccess] = useState(false);\n  const [payError, setPayError] = useState(\"\");\n  const [payLoading, setPayLoading] = useState(false);\n  const [iframeUrl, setIframeUrl] = useState(null);\n  const [iframeModalOpen, setIframeModalOpen] = useState(false);\n  const {\n    addToCart,\n    cartItems\n  } = useCart();\n  const navigate = useNavigate();\n  const [isPaying, setIsPaying] = useState(false);\n  useEffect(() => {\n    const fetchQuotations = async () => {\n      if (!(userSession !== null && userSession !== void 0 && userSession.id)) return;\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/quotation/customer/${userSession.id}`);\n        const data = await response.json();\n        if (Array.isArray(data)) {\n          setQuotations(data);\n          setSelectedQuotation(data[0] || null);\n        } else {\n          console.error(\"Unexpected response structure:\", data);\n        }\n      } catch (err) {\n        console.error(\"Error fetching quotations:\", err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchQuotations();\n  }, [userSession]);\n  const handleDealDecision = async decision => {\n    if (!(selectedQuotation !== null && selectedQuotation !== void 0 && selectedQuotation._id)) return;\n    try {\n      const res = await fetch(`https://api.thedesigngrit.com/api/quotation/quotation/${selectedQuotation._id}/client-approval`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          approved: decision\n        })\n      });\n      const data = await res.json();\n      if (res.ok) {\n        // Update the quotation locally\n        setQuotations(prev => prev.map(q => q._id === data.quotation._id ? data.quotation : q));\n        setSelectedQuotation(data.quotation);\n        if (decision === true) {\n          setIsPaying(true); // Start payment flow\n          handlePayNow();\n        }\n      } else {\n        console.error(\"Error updating approval:\", data.message);\n      }\n    } catch (err) {\n      console.error(\"Request failed:\", err);\n    }\n  };\n  const handleSelectChange = e => {\n    const selectedId = e.target.value;\n    const quotation = quotations.find(q => q._id === selectedId);\n    setSelectedQuotation(quotation);\n  };\n  const handlePayNow = () => {\n    setPayError(\"\");\n    setPayLoading(true);\n    try {\n      var _selectedQuotation$pr, _selectedQuotation$pr2, _selectedQuotation$pr3, _selectedQuotation$pr4;\n      if (!selectedQuotation) {\n        setPayError(\"No quotation selected.\");\n        setPayLoading(false);\n        return;\n      }\n      // Prevent duplicate quotation in cart\n      const alreadyInCart = cartItems.some(item => item.fromQuotation && item.quotationId === selectedQuotation._id);\n      if (alreadyInCart) {\n        setPayError(\"This quotation is already in your cart.\");\n        setPayLoading(false);\n        navigate(\"/checkout\");\n        return;\n      }\n      // Generate a unique id for the cart item\n      let uniqueId;\n      try {\n        // Try to use nanoid if available\n        // eslint-disable-next-line global-require\n        uniqueId = require(\"nanoid\").nanoid();\n      } catch {\n        uniqueId = Date.now().toString();\n      }\n      const cartItem = {\n        id: uniqueId,\n        productId: (_selectedQuotation$pr = selectedQuotation.productId) === null || _selectedQuotation$pr === void 0 ? void 0 : _selectedQuotation$pr._id,\n        name: ((_selectedQuotation$pr2 = selectedQuotation.productId) === null || _selectedQuotation$pr2 === void 0 ? void 0 : _selectedQuotation$pr2.name) || \"Quoted Product\",\n        unitPrice: selectedQuotation.quotePrice,\n        mainImage: ((_selectedQuotation$pr3 = selectedQuotation.productId) === null || _selectedQuotation$pr3 === void 0 ? void 0 : _selectedQuotation$pr3.mainImage) || \"\",\n        quantity: 1,\n        description: selectedQuotation.note || \"\",\n        brandId: (_selectedQuotation$pr4 = selectedQuotation.productId) === null || _selectedQuotation$pr4 === void 0 ? void 0 : _selectedQuotation$pr4.brandId,\n        variantId: selectedQuotation.variantId || null,\n        fromQuotation: true,\n        quotationId: selectedQuotation._id,\n        color: selectedQuotation.color || undefined,\n        size: selectedQuotation.size || undefined,\n        material: selectedQuotation.material || undefined,\n        customization: selectedQuotation.customization || undefined\n      };\n      console.log(\"Adding to cart:\", cartItem);\n      addToCart(cartItem);\n      setPayLoading(false);\n      navigate(\"/checkout\");\n    } catch (err) {\n      setPayError(err.message || \"Failed to add quotation to cart.\");\n      setPayLoading(false);\n    }\n  };\n  const handleCloseIframeModal = () => {\n    setIframeModalOpen(false);\n    setIframeUrl(null);\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(LoadingScreen, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      fontFamily: \"Montserrat\",\n      paddingBottom: \"10rem\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Track Your Quotation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      sx: {\n        marginBottom: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Select, {\n        value: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation._id) || \"\",\n        onChange: handleSelectChange,\n        children: quotations.map(quotation => {\n          var _quotation$productId;\n          return /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: quotation._id,\n            children: [\"Quotation \", quotation._id.slice(-6), \" \\u2013 \", (_quotation$productId = quotation.productId) === null || _quotation$productId === void 0 ? void 0 : _quotation$productId.name]\n          }, quotation._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), selectedQuotation && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderRadius: \"8px\",\n        padding: \"16px\",\n        backgroundColor: \"#fff\",\n        position: \"relative\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        boxShadow: \"0 4px 6px 6px rgba(0, 0, 0, 0.1)\"\n      },\n      children: [selectedQuotation.productId && /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `https://thedesigngrit.com/product/${selectedQuotation.productId._id}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          position: \"absolute\",\n          top: 16,\n          right: 16,\n          color: \"#2d2d2d\",\n          textDecoration: \"none\",\n          zIndex: 2\n        },\n        title: \"View Product Page\",\n        children: /*#__PURE__*/_jsxDEV(InfoOutlinedIcon, {\n          fontSize: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: \"bold\",\n        gutterBottom: true,\n        children: \"Quotation Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Product:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), \" \", ((_selectedQuotation$pr5 = selectedQuotation.productId) === null || _selectedQuotation$pr5 === void 0 ? void 0 : _selectedQuotation$pr5.name) || \"N/A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), ((_selectedQuotation$pr6 = selectedQuotation.productId) === null || _selectedQuotation$pr6 === void 0 ? void 0 : _selectedQuotation$pr6.mainImage) && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${selectedQuotation.productId.mainImage}`,\n        alt: selectedQuotation.productId.name,\n        style: {\n          width: \"200px\",\n          borderRadius: \"6px\",\n          marginTop: \"10px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: [\"Customization Lead Time:\", \" \", selectedQuotation.productId.Estimatedtimeleadforcustomization || \"N/A\", \"Business Days\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Color:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), \" \", selectedQuotation.color || \"N/A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Size:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), \" \", selectedQuotation.size || \"N/A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Material:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), \" \", selectedQuotation.material || \"N/A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Customization Ref:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), \" \", selectedQuotation.customization || \"N/A\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Quoted Price:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), \" \", selectedQuotation.quotePrice ? `${selectedQuotation.quotePrice.toLocaleString()} E£` : \"Not yet provided\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Vendor Note:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), \" \", selectedQuotation.note || \"No note provided by vendor.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this), selectedQuotation.quotationInvoice && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          mt: 2,\n          cursor: \"pointer\"\n        },\n        onClick: () => {\n          const link = document.createElement(\"a\");\n          link.href = `https://pub-64ea2c5c4ba5460991425897a370f20c.r2.dev/${selectedQuotation.quotationInvoice}`;\n          link.setAttribute(\"download\", selectedQuotation.quotationInvoice);\n          link.setAttribute(\"target\", \"_blank\");\n          link.style.display = \"none\";\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Quotation Invoice:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 15\n        }, this), \" \", /*#__PURE__*/_jsxDEV(\"u\", {\n          children: selectedQuotation.quotationInvoice\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: \"gray\",\n          mt: 2\n        },\n        children: [\"Requested On:\", \" \", new Date(selectedQuotation.createdAt).toLocaleDateString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), \"Quoted On:\", \" \", selectedQuotation.dateOfQuotePrice ? new Date(selectedQuotation.dateOfQuotePrice).toLocaleDateString() : \"Pending\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: \"flex\",\n          gap: 2,\n          justifyContent: \"end\",\n          flexDirection: \"row-reverse\",\n          alignItems: \"baseline\"\n        },\n        children: [(selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) === true && (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.quotePrice) && ((selectedQuotation === null || selectedQuotation === void 0 ? void 0 : (_selectedQuotation$pa = selectedQuotation.paymentDetails) === null || _selectedQuotation$pa === void 0 ? void 0 : _selectedQuotation$pa.paid) === true ? /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontWeight: \"bold\",\n            color: \"#6b7b58\",\n            mr: 2\n          },\n          children: \"Paid\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n        // <button\n        //   onClick={handlePayNow}\n        //   className=\"submit-btn\"\n        //   disabled={payLoading}\n        //   style={{ marginRight: 8 }}\n        // >\n        //   {payLoading ? \"Processing...\" : \"Pay Now\"}\n        // </button>\n        ), isPaying ? /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontWeight: \"bold\",\n            color: \"#6b7b58\"\n          },\n          children: \"Redirecting to payment...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 15\n        }, this) : (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"approved\" ? /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontWeight: \"bold\"\n          },\n          children: \"Deal Sealed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 15\n        }, this) : (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"rejected\" ? /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: \"#2d2d2d\"\n          },\n          children: [\"You have rejected the offer. Please make a new quotation.\", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products/readytoship\",\n            style: {\n              color: \"#6b7b58\",\n              textDecoration: \"none\"\n            },\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 17\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 15\n        }, this) : selectedQuotation !== null && selectedQuotation !== void 0 && selectedQuotation.quotePrice && !((selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) === true && (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"pending\") ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDealDecision(true),\n            className: \"submit-btn\",\n            disabled: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) || (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"rejected\" || (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"approved\",\n            children: \"Pay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDealDecision(false),\n            className: \"cancel-btn\",\n            disabled: (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) || (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"rejected\" || (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"approved\",\n            children: \"Reject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : selectedQuotation !== null && selectedQuotation !== void 0 && selectedQuotation.quotePrice && !((selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.ClientApproval) === false && (selectedQuotation === null || selectedQuotation === void 0 ? void 0 : selectedQuotation.status) === \"rejected\") ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            color: \"#2d2d2d\",\n            fontWeight: 500,\n            marginTop: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: \"center\",\n            color: \"#2d2d2d\",\n            fontWeight: 500,\n            marginTop: 16\n          },\n          children: \"Waiting for the brand Offer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this), payError && /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"error\",\n        sx: {\n          mt: 2\n        },\n        children: payError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(QuotationDealSuccess, {\n      show: showDealSuccess,\n      closePopup: () => setShowDealSuccess(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: iframeModalOpen,\n      onClose: handleCloseIframeModal,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 4,\n          background: \"#fff\",\n          boxShadow: \"0 8px 32px rgba(0,0,0,0.18)\",\n          p: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\"\n        },\n        children: [\"Complete Payment\", /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleCloseIframeModal,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: iframeUrl && /*#__PURE__*/_jsxDEV(\"iframe\", {\n          src: iframeUrl,\n          style: {\n            width: \"100%\",\n            height: \"600px\",\n            border: \"none\",\n            display: \"block\"\n          },\n          allow: \"camera; microphone; accelerometer; gyroscope; payment\",\n          allowFullScreen: true,\n          title: \"Paymob Payment\",\n          id: \"paymob-iframe\",\n          referrerPolicy: \"origin\",\n          sandbox: \"allow-forms allow-scripts allow-same-origin allow-top-navigation allow-popups\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n}\n_s(TrackQuotation, \"t5P5/WFnQuRfj9f4HkXGVbnet4M=\", false, function () {\n  return [useCart, useNavigate];\n});\n_c = TrackQuotation;\nexport default TrackQuotation;\nvar _c;\n$RefreshReg$(_c, \"TrackQuotation\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useContext", "Box", "Typography", "Select", "MenuItem", "FormControl", "UserContext", "LoadingScreen", "Link", "useNavigate", "QuotationDealSuccess", "Dialog", "DialogTitle", "IconButton", "CloseIcon", "paymobService", "useCart", "InfoOutlinedIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrackQuotation", "_s", "_selectedQuotation$pr5", "_selectedQuotation$pr6", "_selectedQuotation$pa", "userSession", "quotations", "setQuotations", "selectedQuotation", "setSelectedQuotation", "loading", "setLoading", "showDealSuccess", "setShowDealSuccess", "payError", "setPayError", "payLoading", "setPayLoading", "iframeUrl", "setIframeUrl", "iframeModalOpen", "setIframeModalOpen", "addToCart", "cartItems", "navigate", "isPaying", "setIsPaying", "fetchQuotations", "id", "response", "fetch", "data", "json", "Array", "isArray", "console", "error", "err", "handleDealDecision", "decision", "_id", "res", "method", "headers", "body", "JSON", "stringify", "approved", "ok", "prev", "map", "q", "quotation", "handlePayNow", "message", "handleSelectChange", "e", "selectedId", "target", "value", "find", "_selectedQuotation$pr", "_selectedQuotation$pr2", "_selectedQuotation$pr3", "_selectedQuotation$pr4", "alreadyInCart", "some", "item", "fromQuotation", "quotationId", "uniqueId", "require", "nanoid", "Date", "now", "toString", "cartItem", "productId", "name", "unitPrice", "quotePrice", "mainImage", "quantity", "description", "note", "brandId", "variantId", "color", "undefined", "size", "material", "customization", "log", "handleCloseIframeModal", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "fontFamily", "paddingBottom", "children", "variant", "gutterBottom", "fullWidth", "marginBottom", "onChange", "_quotation$productId", "slice", "borderRadius", "padding", "backgroundColor", "position", "display", "flexDirection", "boxShadow", "href", "rel", "style", "top", "right", "textDecoration", "zIndex", "title", "fontSize", "fontWeight", "src", "alt", "width", "marginTop", "mt", "Estimatedtimeleadforcustomization", "toLocaleString", "quotationInvoice", "cursor", "onClick", "link", "document", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "createdAt", "toLocaleDateString", "dateOfQuotePrice", "gap", "justifyContent", "alignItems", "ClientApproval", "paymentDetails", "paid", "mr", "status", "to", "className", "disabled", "textAlign", "show", "closePopup", "open", "onClose", "max<PERSON><PERSON><PERSON>", "PaperProps", "background", "p", "height", "border", "allow", "allowFullScreen", "referrerPolicy", "sandbox", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/TrackQuotation.jsx"], "sourcesContent": ["import React, { useEffect, useState, useContext } from \"react\";\r\nimport { Box, Typography, Select, MenuItem, FormControl } from \"@mui/material\";\r\nimport { UserContext } from \"../utils/userContext\";\r\nimport LoadingScreen from \"../Pages/loadingScreen\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport QuotationDealSuccess from \"./quotationDealSuccess\";\r\nimport Dialog from \"@mui/material/Dialog\";\r\nimport DialogTitle from \"@mui/material/DialogTitle\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport paymobService from \"../services/paymobService\";\r\nimport { useCart } from \"../Context/cartcontext\";\r\nimport InfoOutlinedIcon from \"@mui/icons-material/InfoOutlined\";\r\n\r\nfunction TrackQuotation() {\r\n  const { userSession } = useContext(UserContext);\r\n  const [quotations, setQuotations] = useState([]);\r\n  const [selectedQuotation, setSelectedQuotation] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [showDealSuccess, setShowDealSuccess] = useState(false);\r\n  const [payError, setPayError] = useState(\"\");\r\n  const [payLoading, setPayLoading] = useState(false);\r\n  const [iframeUrl, setIframeUrl] = useState(null);\r\n  const [iframeModalOpen, setIframeModalOpen] = useState(false);\r\n  const { addToCart, cartItems } = useCart();\r\n  const navigate = useNavigate();\r\n  const [isPaying, setIsPaying] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchQuotations = async () => {\r\n      if (!userSession?.id) return;\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/quotation/customer/${userSession.id}`\r\n        );\r\n        const data = await response.json();\r\n\r\n        if (Array.isArray(data)) {\r\n          setQuotations(data);\r\n          setSelectedQuotation(data[0] || null);\r\n        } else {\r\n          console.error(\"Unexpected response structure:\", data);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching quotations:\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchQuotations();\r\n  }, [userSession]);\r\n  const handleDealDecision = async (decision) => {\r\n    if (!selectedQuotation?._id) return;\r\n\r\n    try {\r\n      const res = await fetch(\r\n        `https://api.thedesigngrit.com/api/quotation/quotation/${selectedQuotation._id}/client-approval`,\r\n        {\r\n          method: \"PATCH\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ approved: decision }),\r\n        }\r\n      );\r\n      const data = await res.json();\r\n\r\n      if (res.ok) {\r\n        // Update the quotation locally\r\n        setQuotations((prev) =>\r\n          prev.map((q) => (q._id === data.quotation._id ? data.quotation : q))\r\n        );\r\n        setSelectedQuotation(data.quotation);\r\n        if (decision === true) {\r\n          setIsPaying(true); // Start payment flow\r\n          handlePayNow();\r\n        }\r\n      } else {\r\n        console.error(\"Error updating approval:\", data.message);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Request failed:\", err);\r\n    }\r\n  };\r\n\r\n  const handleSelectChange = (e) => {\r\n    const selectedId = e.target.value;\r\n    const quotation = quotations.find((q) => q._id === selectedId);\r\n    setSelectedQuotation(quotation);\r\n  };\r\n\r\n  const handlePayNow = () => {\r\n    setPayError(\"\");\r\n    setPayLoading(true);\r\n    try {\r\n      if (!selectedQuotation) {\r\n        setPayError(\"No quotation selected.\");\r\n        setPayLoading(false);\r\n        return;\r\n      }\r\n      // Prevent duplicate quotation in cart\r\n      const alreadyInCart = cartItems.some(\r\n        (item) =>\r\n          item.fromQuotation && item.quotationId === selectedQuotation._id\r\n      );\r\n      if (alreadyInCart) {\r\n        setPayError(\"This quotation is already in your cart.\");\r\n        setPayLoading(false);\r\n        navigate(\"/checkout\");\r\n        return;\r\n      }\r\n      // Generate a unique id for the cart item\r\n      let uniqueId;\r\n      try {\r\n        // Try to use nanoid if available\r\n        // eslint-disable-next-line global-require\r\n        uniqueId = require(\"nanoid\").nanoid();\r\n      } catch {\r\n        uniqueId = Date.now().toString();\r\n      }\r\n      const cartItem = {\r\n        id: uniqueId,\r\n        productId: selectedQuotation.productId?._id,\r\n        name: selectedQuotation.productId?.name || \"Quoted Product\",\r\n        unitPrice: selectedQuotation.quotePrice,\r\n        mainImage: selectedQuotation.productId?.mainImage || \"\",\r\n        quantity: 1,\r\n        description: selectedQuotation.note || \"\",\r\n        brandId: selectedQuotation.productId?.brandId,\r\n        variantId: selectedQuotation.variantId || null,\r\n        fromQuotation: true,\r\n        quotationId: selectedQuotation._id,\r\n        color: selectedQuotation.color || undefined,\r\n        size: selectedQuotation.size || undefined,\r\n        material: selectedQuotation.material || undefined,\r\n        customization: selectedQuotation.customization || undefined,\r\n      };\r\n      console.log(\"Adding to cart:\", cartItem);\r\n      addToCart(cartItem);\r\n      setPayLoading(false);\r\n      navigate(\"/checkout\");\r\n    } catch (err) {\r\n      setPayError(err.message || \"Failed to add quotation to cart.\");\r\n      setPayLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseIframeModal = () => {\r\n    setIframeModalOpen(false);\r\n    setIframeUrl(null);\r\n  };\r\n\r\n  if (loading) return <LoadingScreen />;\r\n\r\n  return (\r\n    <Box sx={{ fontFamily: \"Montserrat\", paddingBottom: \"10rem\" }}>\r\n      <Typography variant=\"h6\" gutterBottom>\r\n        Track Your Quotation\r\n      </Typography>\r\n\r\n      <FormControl fullWidth sx={{ marginBottom: 4 }}>\r\n        <Select\r\n          value={selectedQuotation?._id || \"\"}\r\n          onChange={handleSelectChange}\r\n        >\r\n          {quotations.map((quotation) => (\r\n            <MenuItem key={quotation._id} value={quotation._id}>\r\n              Quotation {quotation._id.slice(-6)} – {quotation.productId?.name}\r\n            </MenuItem>\r\n          ))}\r\n        </Select>\r\n      </FormControl>\r\n\r\n      {selectedQuotation && (\r\n        <Box\r\n          sx={{\r\n            borderRadius: \"8px\",\r\n            padding: \"16px\",\r\n            backgroundColor: \"#fff\",\r\n            position: \"relative\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            boxShadow: \"0 4px 6px 6px rgba(0, 0, 0, 0.1)\",\r\n          }}\r\n        >\r\n          {/* Info Icon - top right, opposite to image */}\r\n          {selectedQuotation.productId && (\r\n            <a\r\n              href={`https://thedesigngrit.com/product/${selectedQuotation.productId._id}`}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              style={{\r\n                position: \"absolute\",\r\n                top: 16,\r\n                right: 16,\r\n                color: \"#2d2d2d\",\r\n                textDecoration: \"none\",\r\n                zIndex: 2,\r\n              }}\r\n              title=\"View Product Page\"\r\n            >\r\n              <InfoOutlinedIcon fontSize=\"large\" />\r\n            </a>\r\n          )}\r\n          <Typography variant=\"h6\" fontWeight=\"bold\" gutterBottom>\r\n            Quotation Summary\r\n          </Typography>\r\n\r\n          <Typography>\r\n            <strong>Product:</strong>{\" \"}\r\n            {selectedQuotation.productId?.name || \"N/A\"}\r\n          </Typography>\r\n\r\n          {selectedQuotation.productId?.mainImage && (\r\n            <img\r\n              src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${selectedQuotation.productId.mainImage}`}\r\n              alt={selectedQuotation.productId.name}\r\n              style={{ width: \"200px\", borderRadius: \"6px\", marginTop: \"10px\" }}\r\n            />\r\n          )}\r\n          {/* Customization Lead Time */}\r\n          <Typography sx={{ mt: 2 }}>\r\n            Customization Lead Time:{\" \"}\r\n            {selectedQuotation.productId.Estimatedtimeleadforcustomization ||\r\n              \"N/A\"}\r\n            Business Days\r\n          </Typography>\r\n\r\n          <Typography sx={{ mt: 2 }}>\r\n            <strong>Color:</strong> {selectedQuotation.color || \"N/A\"}\r\n          </Typography>\r\n          <Typography>\r\n            <strong>Size:</strong> {selectedQuotation.size || \"N/A\"}\r\n          </Typography>\r\n          <Typography>\r\n            <strong>Material:</strong> {selectedQuotation.material || \"N/A\"}\r\n          </Typography>\r\n          <Typography>\r\n            <strong>Customization Ref:</strong>{\" \"}\r\n            {selectedQuotation.customization || \"N/A\"}\r\n          </Typography>\r\n\r\n          <Typography sx={{ mt: 2 }}>\r\n            <strong>Quoted Price:</strong>{\" \"}\r\n            {selectedQuotation.quotePrice\r\n              ? `${selectedQuotation.quotePrice.toLocaleString()} E£`\r\n              : \"Not yet provided\"}\r\n          </Typography>\r\n\r\n          <Typography>\r\n            <strong>Vendor Note:</strong>{\" \"}\r\n            {selectedQuotation.note || \"No note provided by vendor.\"}\r\n          </Typography>\r\n          {selectedQuotation.quotationInvoice && (\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{ mt: 2, cursor: \"pointer\" }}\r\n              onClick={() => {\r\n                const link = document.createElement(\"a\");\r\n                link.href = `https://pub-64ea2c5c4ba5460991425897a370f20c.r2.dev/${selectedQuotation.quotationInvoice}`;\r\n                link.setAttribute(\r\n                  \"download\",\r\n                  selectedQuotation.quotationInvoice\r\n                );\r\n                link.setAttribute(\"target\", \"_blank\");\r\n                link.style.display = \"none\";\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n              }}\r\n            >\r\n              <strong>Quotation Invoice:</strong>{\" \"}\r\n              <u>{selectedQuotation.quotationInvoice}</u>\r\n            </Typography>\r\n          )}\r\n\r\n          <Typography sx={{ color: \"gray\", mt: 2 }}>\r\n            Requested On:{\" \"}\r\n            {new Date(selectedQuotation.createdAt).toLocaleDateString()}\r\n            <br />\r\n            Quoted On:{\" \"}\r\n            {selectedQuotation.dateOfQuotePrice\r\n              ? new Date(\r\n                  selectedQuotation.dateOfQuotePrice\r\n                ).toLocaleDateString()\r\n              : \"Pending\"}\r\n          </Typography>\r\n\r\n          <Box\r\n            sx={{\r\n              mt: 3,\r\n              display: \"flex\",\r\n              gap: 2,\r\n              justifyContent: \"end\",\r\n              flexDirection: \"row-reverse\",\r\n              alignItems: \"baseline\",\r\n            }}\r\n          >\r\n            {selectedQuotation?.ClientApproval === true &&\r\n              selectedQuotation?.quotePrice &&\r\n              (selectedQuotation?.paymentDetails?.paid === true ? (\r\n                <Typography\r\n                  sx={{ fontWeight: \"bold\", color: \"#6b7b58\", mr: 2 }}\r\n                >\r\n                  Paid\r\n                </Typography>\r\n              ) : (\r\n                <></>\r\n                // <button\r\n                //   onClick={handlePayNow}\r\n                //   className=\"submit-btn\"\r\n                //   disabled={payLoading}\r\n                //   style={{ marginRight: 8 }}\r\n                // >\r\n                //   {payLoading ? \"Processing...\" : \"Pay Now\"}\r\n                // </button>\r\n              ))}\r\n            {isPaying ? (\r\n              <Typography sx={{ fontWeight: \"bold\", color: \"#6b7b58\" }}>\r\n                Redirecting to payment...\r\n              </Typography>\r\n            ) : selectedQuotation?.status === \"approved\" ? (\r\n              <Typography sx={{ fontWeight: \"bold\" }}>Deal Sealed</Typography>\r\n            ) : selectedQuotation?.status === \"rejected\" ? (\r\n              <Typography sx={{ color: \"#2d2d2d\" }}>\r\n                You have rejected the offer. Please make a new quotation.\r\n                <Link\r\n                  to=\"/products/readytoship\"\r\n                  style={{ color: \"#6b7b58\", textDecoration: \"none\" }}\r\n                >\r\n                  Products\r\n                </Link>\r\n                .\r\n              </Typography>\r\n            ) : selectedQuotation?.quotePrice &&\r\n              !(\r\n                selectedQuotation?.ClientApproval === true &&\r\n                selectedQuotation?.status === \"pending\"\r\n              ) ? (\r\n              <>\r\n                <button\r\n                  onClick={() => handleDealDecision(true)}\r\n                  className=\"submit-btn\"\r\n                  disabled={\r\n                    selectedQuotation?.ClientApproval ||\r\n                    selectedQuotation?.status === \"rejected\" ||\r\n                    selectedQuotation?.status === \"approved\"\r\n                  }\r\n                >\r\n                  Pay\r\n                </button>\r\n                <button\r\n                  onClick={() => handleDealDecision(false)}\r\n                  className=\"cancel-btn\"\r\n                  disabled={\r\n                    selectedQuotation?.ClientApproval ||\r\n                    selectedQuotation?.status === \"rejected\" ||\r\n                    selectedQuotation?.status === \"approved\"\r\n                  }\r\n                >\r\n                  Reject\r\n                </button>\r\n              </>\r\n            ) : selectedQuotation?.quotePrice &&\r\n              !(\r\n                selectedQuotation?.ClientApproval === false &&\r\n                selectedQuotation?.status === \"rejected\"\r\n              ) ? (\r\n              <div\r\n                style={{\r\n                  textAlign: \"center\",\r\n                  color: \"#2d2d2d\",\r\n                  fontWeight: 500,\r\n                  marginTop: 16,\r\n                }}\r\n              ></div>\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  textAlign: \"center\",\r\n                  color: \"#2d2d2d\",\r\n                  fontWeight: 500,\r\n                  marginTop: 16,\r\n                }}\r\n              >\r\n                Waiting for the brand Offer\r\n              </div>\r\n            )}\r\n          </Box>\r\n          {payError && (\r\n            <Typography color=\"error\" sx={{ mt: 2 }}>\r\n              {payError}\r\n            </Typography>\r\n          )}\r\n        </Box>\r\n      )}\r\n      <QuotationDealSuccess\r\n        show={showDealSuccess}\r\n        closePopup={() => setShowDealSuccess(false)}\r\n      />\r\n      {/* Paymob Iframe Modal */}\r\n      <Dialog\r\n        open={iframeModalOpen}\r\n        onClose={handleCloseIframeModal}\r\n        maxWidth=\"md\"\r\n        fullWidth\r\n        PaperProps={{\r\n          sx: {\r\n            borderRadius: 4,\r\n            background: \"#fff\",\r\n            boxShadow: \"0 8px 32px rgba(0,0,0,0.18)\",\r\n            p: 2,\r\n          },\r\n        }}\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          Complete Payment\r\n          <IconButton onClick={handleCloseIframeModal}>\r\n            <CloseIcon />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <Box sx={{ p: 2 }}>\r\n          {iframeUrl && (\r\n            <iframe\r\n              src={iframeUrl}\r\n              style={{\r\n                width: \"100%\",\r\n                height: \"600px\",\r\n                border: \"none\",\r\n                display: \"block\",\r\n              }}\r\n              allow=\"camera; microphone; accelerometer; gyroscope; payment\"\r\n              allowFullScreen\r\n              title=\"Paymob Payment\"\r\n              id=\"paymob-iframe\"\r\n              referrerPolicy=\"origin\"\r\n              sandbox=\"allow-forms allow-scripts allow-same-origin allow-top-navigation allow-popups\"\r\n            />\r\n          )}\r\n        </Box>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default TrackQuotation;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,eAAe;AAC9E,SAASC,WAAW,QAAQ,sBAAsB;AAClD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,gBAAgB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhE,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACxB,MAAM;IAAEC;EAAY,CAAC,GAAG3B,UAAU,CAACM,WAAW,CAAC;EAC/C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM;IAAE6C,SAAS;IAAEC;EAAU,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAC1C,MAAM8B,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACd,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI,EAACtB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEuB,EAAE,GAAE;MAEtB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,wDAAwDzB,WAAW,CAACuB,EAAE,EACxE,CAAC;QACD,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACvBxB,aAAa,CAACwB,IAAI,CAAC;UACnBtB,oBAAoB,CAACsB,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACvC,CAAC,MAAM;UACLI,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEL,IAAI,CAAC;QACvD;MACF,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZF,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;MAClD,CAAC,SAAS;QACR1B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACtB,WAAW,CAAC,CAAC;EACjB,MAAMiC,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI,EAAC/B,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEgC,GAAG,GAAE;IAE7B,IAAI;MACF,MAAMC,GAAG,GAAG,MAAMX,KAAK,CACrB,yDAAyDtB,iBAAiB,CAACgC,GAAG,kBAAkB,EAChG;QACEE,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,QAAQ,EAAER;QAAS,CAAC;MAC7C,CACF,CAAC;MACD,MAAMR,IAAI,GAAG,MAAMU,GAAG,CAACT,IAAI,CAAC,CAAC;MAE7B,IAAIS,GAAG,CAACO,EAAE,EAAE;QACV;QACAzC,aAAa,CAAE0C,IAAI,IACjBA,IAAI,CAACC,GAAG,CAAEC,CAAC,IAAMA,CAAC,CAACX,GAAG,KAAKT,IAAI,CAACqB,SAAS,CAACZ,GAAG,GAAGT,IAAI,CAACqB,SAAS,GAAGD,CAAE,CACrE,CAAC;QACD1C,oBAAoB,CAACsB,IAAI,CAACqB,SAAS,CAAC;QACpC,IAAIb,QAAQ,KAAK,IAAI,EAAE;UACrBb,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;UACnB2B,YAAY,CAAC,CAAC;QAChB;MACF,CAAC,MAAM;QACLlB,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEL,IAAI,CAACuB,OAAO,CAAC;MACzD;IACF,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZF,OAAO,CAACC,KAAK,CAAC,iBAAiB,EAAEC,GAAG,CAAC;IACvC;EACF,CAAC;EAED,MAAMkB,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMC,UAAU,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACjC,MAAMP,SAAS,GAAG9C,UAAU,CAACsD,IAAI,CAAET,CAAC,IAAKA,CAAC,CAACX,GAAG,KAAKiB,UAAU,CAAC;IAC9DhD,oBAAoB,CAAC2C,SAAS,CAAC;EACjC,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBtC,WAAW,CAAC,EAAE,CAAC;IACfE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MAAA,IAAA4C,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,IAAI,CAACxD,iBAAiB,EAAE;QACtBO,WAAW,CAAC,wBAAwB,CAAC;QACrCE,aAAa,CAAC,KAAK,CAAC;QACpB;MACF;MACA;MACA,MAAMgD,aAAa,GAAG1C,SAAS,CAAC2C,IAAI,CACjCC,IAAI,IACHA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,WAAW,KAAK7D,iBAAiB,CAACgC,GACjE,CAAC;MACD,IAAIyB,aAAa,EAAE;QACjBlD,WAAW,CAAC,yCAAyC,CAAC;QACtDE,aAAa,CAAC,KAAK,CAAC;QACpBO,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF;MACA;MACA,IAAI8C,QAAQ;MACZ,IAAI;QACF;QACA;QACAA,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC;MACvC,CAAC,CAAC,MAAM;QACNF,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAClC;MACA,MAAMC,QAAQ,GAAG;QACfhD,EAAE,EAAE0C,QAAQ;QACZO,SAAS,GAAAhB,qBAAA,GAAErD,iBAAiB,CAACqE,SAAS,cAAAhB,qBAAA,uBAA3BA,qBAAA,CAA6BrB,GAAG;QAC3CsC,IAAI,EAAE,EAAAhB,sBAAA,GAAAtD,iBAAiB,CAACqE,SAAS,cAAAf,sBAAA,uBAA3BA,sBAAA,CAA6BgB,IAAI,KAAI,gBAAgB;QAC3DC,SAAS,EAAEvE,iBAAiB,CAACwE,UAAU;QACvCC,SAAS,EAAE,EAAAlB,sBAAA,GAAAvD,iBAAiB,CAACqE,SAAS,cAAAd,sBAAA,uBAA3BA,sBAAA,CAA6BkB,SAAS,KAAI,EAAE;QACvDC,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE3E,iBAAiB,CAAC4E,IAAI,IAAI,EAAE;QACzCC,OAAO,GAAArB,sBAAA,GAAExD,iBAAiB,CAACqE,SAAS,cAAAb,sBAAA,uBAA3BA,sBAAA,CAA6BqB,OAAO;QAC7CC,SAAS,EAAE9E,iBAAiB,CAAC8E,SAAS,IAAI,IAAI;QAC9ClB,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE7D,iBAAiB,CAACgC,GAAG;QAClC+C,KAAK,EAAE/E,iBAAiB,CAAC+E,KAAK,IAAIC,SAAS;QAC3CC,IAAI,EAAEjF,iBAAiB,CAACiF,IAAI,IAAID,SAAS;QACzCE,QAAQ,EAAElF,iBAAiB,CAACkF,QAAQ,IAAIF,SAAS;QACjDG,aAAa,EAAEnF,iBAAiB,CAACmF,aAAa,IAAIH;MACpD,CAAC;MACDrD,OAAO,CAACyD,GAAG,CAAC,iBAAiB,EAAEhB,QAAQ,CAAC;MACxCtD,SAAS,CAACsD,QAAQ,CAAC;MACnB3D,aAAa,CAAC,KAAK,CAAC;MACpBO,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,CAAC,OAAOa,GAAG,EAAE;MACZtB,WAAW,CAACsB,GAAG,CAACiB,OAAO,IAAI,kCAAkC,CAAC;MAC9DrC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAM4E,sBAAsB,GAAGA,CAAA,KAAM;IACnCxE,kBAAkB,CAAC,KAAK,CAAC;IACzBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,IAAIT,OAAO,EAAE,oBAAOb,OAAA,CAACZ,aAAa;IAAA6G,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAErC,oBACEpG,OAAA,CAAClB,GAAG;IAACuH,EAAE,EAAE;MAAEC,UAAU,EAAE,YAAY;MAAEC,aAAa,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC5DxG,OAAA,CAACjB,UAAU;MAAC0H,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbpG,OAAA,CAACd,WAAW;MAACyH,SAAS;MAACN,EAAE,EAAE;QAAEO,YAAY,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC7CxG,OAAA,CAAChB,MAAM;QACL8E,KAAK,EAAE,CAAAnD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEgC,GAAG,KAAI,EAAG;QACpCkE,QAAQ,EAAEnD,kBAAmB;QAAA8C,QAAA,EAE5B/F,UAAU,CAAC4C,GAAG,CAAEE,SAAS;UAAA,IAAAuD,oBAAA;UAAA,oBACxB9G,OAAA,CAACf,QAAQ;YAAqB6E,KAAK,EAAEP,SAAS,CAACZ,GAAI;YAAA6D,QAAA,GAAC,YACxC,EAACjD,SAAS,CAACZ,GAAG,CAACoE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,UAAG,GAAAD,oBAAA,GAACvD,SAAS,CAACyB,SAAS,cAAA8B,oBAAA,uBAAnBA,oBAAA,CAAqB7B,IAAI;UAAA,GADnD1B,SAAS,CAACZ,GAAG;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElB,CAAC;QAAA,CACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEbzF,iBAAiB,iBAChBX,OAAA,CAAClB,GAAG;MACFuH,EAAE,EAAE;QACFW,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,eAAe,EAAE,MAAM;QACvBC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,SAAS,EAAE;MACb,CAAE;MAAAd,QAAA,GAGD7F,iBAAiB,CAACqE,SAAS,iBAC1BhF,OAAA;QACEuH,IAAI,EAAE,qCAAqC5G,iBAAiB,CAACqE,SAAS,CAACrC,GAAG,EAAG;QAC7EkB,MAAM,EAAC,QAAQ;QACf2D,GAAG,EAAC,qBAAqB;QACzBC,KAAK,EAAE;UACLN,QAAQ,EAAE,UAAU;UACpBO,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE,EAAE;UACTjC,KAAK,EAAE,SAAS;UAChBkC,cAAc,EAAE,MAAM;UACtBC,MAAM,EAAE;QACV,CAAE;QACFC,KAAK,EAAC,mBAAmB;QAAAtB,QAAA,eAEzBxG,OAAA,CAACF,gBAAgB;UAACiI,QAAQ,EAAC;QAAO;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACJ,eACDpG,OAAA,CAACjB,UAAU;QAAC0H,OAAO,EAAC,IAAI;QAACuB,UAAU,EAAC,MAAM;QAACtB,YAAY;QAAAF,QAAA,EAAC;MAExD;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbpG,OAAA,CAACjB,UAAU;QAAAyH,QAAA,gBACTxG,OAAA;UAAAwG,QAAA,EAAQ;QAAQ;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAAC,GAAG,EAC5B,EAAA/F,sBAAA,GAAAM,iBAAiB,CAACqE,SAAS,cAAA3E,sBAAA,uBAA3BA,sBAAA,CAA6B4E,IAAI,KAAI,KAAK;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAEZ,EAAA9F,sBAAA,GAAAK,iBAAiB,CAACqE,SAAS,cAAA1E,sBAAA,uBAA3BA,sBAAA,CAA6B8E,SAAS,kBACrCpF,OAAA;QACEiI,GAAG,EAAE,uDAAuDtH,iBAAiB,CAACqE,SAAS,CAACI,SAAS,EAAG;QACpG8C,GAAG,EAAEvH,iBAAiB,CAACqE,SAAS,CAACC,IAAK;QACtCwC,KAAK,EAAE;UAAEU,KAAK,EAAE,OAAO;UAAEnB,YAAY,EAAE,KAAK;UAAEoB,SAAS,EAAE;QAAO;MAAE;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACF,eAEDpG,OAAA,CAACjB,UAAU;QAACsH,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,GAAC,0BACD,EAAC,GAAG,EAC3B7F,iBAAiB,CAACqE,SAAS,CAACsD,iCAAiC,IAC5D,KAAK,EAAC,eAEV;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbpG,OAAA,CAACjB,UAAU;QAACsH,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACxBxG,OAAA;UAAAwG,QAAA,EAAQ;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACzF,iBAAiB,CAAC+E,KAAK,IAAI,KAAK;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACbpG,OAAA,CAACjB,UAAU;QAAAyH,QAAA,gBACTxG,OAAA;UAAAwG,QAAA,EAAQ;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACzF,iBAAiB,CAACiF,IAAI,IAAI,KAAK;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACbpG,OAAA,CAACjB,UAAU;QAAAyH,QAAA,gBACTxG,OAAA;UAAAwG,QAAA,EAAQ;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACzF,iBAAiB,CAACkF,QAAQ,IAAI,KAAK;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACbpG,OAAA,CAACjB,UAAU;QAAAyH,QAAA,gBACTxG,OAAA;UAAAwG,QAAA,EAAQ;QAAkB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAAC,GAAG,EACtCzF,iBAAiB,CAACmF,aAAa,IAAI,KAAK;MAAA;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEbpG,OAAA,CAACjB,UAAU;QAACsH,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACxBxG,OAAA;UAAAwG,QAAA,EAAQ;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAAC,GAAG,EACjCzF,iBAAiB,CAACwE,UAAU,GACzB,GAAGxE,iBAAiB,CAACwE,UAAU,CAACoD,cAAc,CAAC,CAAC,KAAK,GACrD,kBAAkB;MAAA;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEbpG,OAAA,CAACjB,UAAU;QAAAyH,QAAA,gBACTxG,OAAA;UAAAwG,QAAA,EAAQ;QAAY;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAAC,GAAG,EAChCzF,iBAAiB,CAAC4E,IAAI,IAAI,6BAA6B;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,EACZzF,iBAAiB,CAAC6H,gBAAgB,iBACjCxI,OAAA,CAACjB,UAAU;QACT0H,OAAO,EAAC,OAAO;QACfJ,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAEI,MAAM,EAAE;QAAU,CAAE;QACjCC,OAAO,EAAEA,CAAA,KAAM;UACb,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACpB,IAAI,GAAG,uDAAuD5G,iBAAiB,CAAC6H,gBAAgB,EAAE;UACvGG,IAAI,CAACG,YAAY,CACf,UAAU,EACVnI,iBAAiB,CAAC6H,gBACpB,CAAC;UACDG,IAAI,CAACG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;UACrCH,IAAI,CAAClB,KAAK,CAACL,OAAO,GAAG,MAAM;UAC3BwB,QAAQ,CAAC7F,IAAI,CAACgG,WAAW,CAACJ,IAAI,CAAC;UAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;UACZJ,QAAQ,CAAC7F,IAAI,CAACkG,WAAW,CAACN,IAAI,CAAC;QACjC,CAAE;QAAAnC,QAAA,gBAEFxG,OAAA;UAAAwG,QAAA,EAAQ;QAAkB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAAC,GAAG,eACvCpG,OAAA;UAAAwG,QAAA,EAAI7F,iBAAiB,CAAC6H;QAAgB;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACb,eAEDpG,OAAA,CAACjB,UAAU;QAACsH,EAAE,EAAE;UAAEX,KAAK,EAAE,MAAM;UAAE2C,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,GAAC,eAC3B,EAAC,GAAG,EAChB,IAAI5B,IAAI,CAACjE,iBAAiB,CAACuI,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC,eAC3DnJ,OAAA;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,cACI,EAAC,GAAG,EACbzF,iBAAiB,CAACyI,gBAAgB,GAC/B,IAAIxE,IAAI,CACNjE,iBAAiB,CAACyI,gBACpB,CAAC,CAACD,kBAAkB,CAAC,CAAC,GACtB,SAAS;MAAA;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEbpG,OAAA,CAAClB,GAAG;QACFuH,EAAE,EAAE;UACFgC,EAAE,EAAE,CAAC;UACLjB,OAAO,EAAE,MAAM;UACfiC,GAAG,EAAE,CAAC;UACNC,cAAc,EAAE,KAAK;UACrBjC,aAAa,EAAE,aAAa;UAC5BkC,UAAU,EAAE;QACd,CAAE;QAAA/C,QAAA,GAED,CAAA7F,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6I,cAAc,MAAK,IAAI,KACzC7I,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEwE,UAAU,MAC5B,CAAAxE,iBAAiB,aAAjBA,iBAAiB,wBAAAJ,qBAAA,GAAjBI,iBAAiB,CAAE8I,cAAc,cAAAlJ,qBAAA,uBAAjCA,qBAAA,CAAmCmJ,IAAI,MAAK,IAAI,gBAC/C1J,OAAA,CAACjB,UAAU;UACTsH,EAAE,EAAE;YAAE2B,UAAU,EAAE,MAAM;YAAEtC,KAAK,EAAE,SAAS;YAAEiE,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,EACrD;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEbpG,OAAA,CAAAE,SAAA,mBAAI;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACD,CAAC,EACH0B,QAAQ,gBACP5B,OAAA,CAACjB,UAAU;UAACsH,EAAE,EAAE;YAAE2B,UAAU,EAAE,MAAM;YAAEtC,KAAK,EAAE;UAAU,CAAE;UAAAc,QAAA,EAAC;QAE1D;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GACX,CAAAzF,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAAU,gBAC1C5J,OAAA,CAACjB,UAAU;UAACsH,EAAE,EAAE;YAAE2B,UAAU,EAAE;UAAO,CAAE;UAAAxB,QAAA,EAAC;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GAC9D,CAAAzF,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAAU,gBAC1C5J,OAAA,CAACjB,UAAU;UAACsH,EAAE,EAAE;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAc,QAAA,GAAC,2DAEpC,eAAAxG,OAAA,CAACX,IAAI;YACHwK,EAAE,EAAC,uBAAuB;YAC1BpC,KAAK,EAAE;cAAE/B,KAAK,EAAE,SAAS;cAAEkC,cAAc,EAAE;YAAO,CAAE;YAAApB,QAAA,EACrD;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAET;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GACXzF,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEwE,UAAU,IAC/B,EACE,CAAAxE,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6I,cAAc,MAAK,IAAI,IAC1C,CAAA7I,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,SAAS,CACxC,gBACD5J,OAAA,CAAAE,SAAA;UAAAsG,QAAA,gBACExG,OAAA;YACE0I,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAAC,IAAI,CAAE;YACxCqH,SAAS,EAAC,YAAY;YACtBC,QAAQ,EACN,CAAApJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6I,cAAc,KACjC,CAAA7I,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAAU,IACxC,CAAAjJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAC/B;YAAApD,QAAA,EACF;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpG,OAAA;YACE0I,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAAC,KAAK,CAAE;YACzCqH,SAAS,EAAC,YAAY;YACtBC,QAAQ,EACN,CAAApJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6I,cAAc,KACjC,CAAA7I,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAAU,IACxC,CAAAjJ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAC/B;YAAApD,QAAA,EACF;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,GACDzF,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEwE,UAAU,IAC/B,EACE,CAAAxE,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6I,cAAc,MAAK,KAAK,IAC3C,CAAA7I,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiJ,MAAM,MAAK,UAAU,CACzC,gBACD5J,OAAA;UACEyH,KAAK,EAAE;YACLuC,SAAS,EAAE,QAAQ;YACnBtE,KAAK,EAAE,SAAS;YAChBsC,UAAU,EAAE,GAAG;YACfI,SAAS,EAAE;UACb;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEPpG,OAAA;UACEyH,KAAK,EAAE;YACLuC,SAAS,EAAE,QAAQ;YACnBtE,KAAK,EAAE,SAAS;YAChBsC,UAAU,EAAE,GAAG;YACfI,SAAS,EAAE;UACb,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACLnF,QAAQ,iBACPjB,OAAA,CAACjB,UAAU;QAAC2G,KAAK,EAAC,OAAO;QAACW,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,EACrCvF;MAAQ;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eACDpG,OAAA,CAACT,oBAAoB;MACnB0K,IAAI,EAAElJ,eAAgB;MACtBmJ,UAAU,EAAEA,CAAA,KAAMlJ,kBAAkB,CAAC,KAAK;IAAE;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,eAEFpG,OAAA,CAACR,MAAM;MACL2K,IAAI,EAAE5I,eAAgB;MACtB6I,OAAO,EAAEpE,sBAAuB;MAChCqE,QAAQ,EAAC,IAAI;MACb1D,SAAS;MACT2D,UAAU,EAAE;QACVjE,EAAE,EAAE;UACFW,YAAY,EAAE,CAAC;UACfuD,UAAU,EAAE,MAAM;UAClBjD,SAAS,EAAE,6BAA6B;UACxCkD,CAAC,EAAE;QACL;MACF,CAAE;MAAAhE,QAAA,gBAEFxG,OAAA,CAACP,WAAW;QACV4G,EAAE,EAAE;UACFe,OAAO,EAAE,MAAM;UACfkC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE;QACd,CAAE;QAAA/C,QAAA,GACH,kBAEC,eAAAxG,OAAA,CAACN,UAAU;UAACgJ,OAAO,EAAE1C,sBAAuB;UAAAQ,QAAA,eAC1CxG,OAAA,CAACL,SAAS;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdpG,OAAA,CAAClB,GAAG;QAACuH,EAAE,EAAE;UAAEmE,CAAC,EAAE;QAAE,CAAE;QAAAhE,QAAA,EACfnF,SAAS,iBACRrB,OAAA;UACEiI,GAAG,EAAE5G,SAAU;UACfoG,KAAK,EAAE;YACLU,KAAK,EAAE,MAAM;YACbsC,MAAM,EAAE,OAAO;YACfC,MAAM,EAAE,MAAM;YACdtD,OAAO,EAAE;UACX,CAAE;UACFuD,KAAK,EAAC,uDAAuD;UAC7DC,eAAe;UACf9C,KAAK,EAAC,gBAAgB;UACtB/F,EAAE,EAAC,eAAe;UAClB8I,cAAc,EAAC,QAAQ;UACvBC,OAAO,EAAC;QAA+E;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAChG,EAAA,CAnbQD,cAAc;EAAA,QAUYN,OAAO,EACvBP,WAAW;AAAA;AAAAyL,EAAA,GAXrB5K,cAAc;AAqbvB,eAAeA,cAAc;AAAC,IAAA4K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}