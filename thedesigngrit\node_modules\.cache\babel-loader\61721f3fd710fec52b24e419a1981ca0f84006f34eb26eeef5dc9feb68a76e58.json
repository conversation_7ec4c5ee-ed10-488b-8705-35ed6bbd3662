{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\postProduct.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { TiDeleteOutline } from \"react-icons/ti\"; // Import the delete icon\nimport { useVendor } from \"../../utils/vendorContext\";\n// import { Link } from \"react-router-dom\";\nimport { Box } from \"@mui/material\";\nimport ConfirmationDialog from \"../confirmationMsg\";\nimport Cropper from \"react-easy-crop\";\nimport CircularProgress from \"@mui/material/CircularProgress\";\nimport Dialog from \"@mui/material/Dialog\";\nimport DialogActions from \"@mui/material/DialogActions\";\nimport DialogContent from \"@mui/material/DialogContent\";\nimport DialogContentText from \"@mui/material/DialogContentText\";\nimport DialogTitle from \"@mui/material/DialogTitle\";\nimport Button from \"@mui/material/Button\";\nimport UpdateProduct from \"./UpdateProduct\"; // Import UpdateProduct component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getCroppedImg = (imageSrc, croppedAreaPixels) => {\n  return new Promise(resolve => {\n    const image = new Image();\n    image.src = imageSrc;\n    image.onload = () => {\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = croppedAreaPixels.width;\n      canvas.height = croppedAreaPixels.height;\n      const ctx = canvas.getContext(\"2d\");\n      ctx.drawImage(image, croppedAreaPixels.x, croppedAreaPixels.y, croppedAreaPixels.width, croppedAreaPixels.height, 0, 0, croppedAreaPixels.width, croppedAreaPixels.height);\n      canvas.toBlob(blob => resolve(blob), \"image/jpeg\");\n    };\n  });\n};\nconst AddProduct = ({\n  existingProduct,\n  onBack\n}) => {\n  _s();\n  const {\n    vendor\n  } = useVendor(); // Access vendor data from context\n\n  // State variables\n  const [brandName, setBrandName] = useState(\"\"); // Store fetched brand name\n  const [categories, setCategories] = useState([]); // Categories from API\n  const [subCategories, setSubCategories] = useState([]); // Subcategories from API\n  const [types, setTypes] = useState([]); // Types from API\n  const [selectedCategory, setSelectedCategory] = useState(\"\"); // Selected category\n  const [selectedSubCategory, setSelectedSubCategory] = useState(\"\"); // Selected subcategory\n  const [tags, setTags] = useState([]); // Tags array\n  const [tagOptions, setTagOptions] = useState({}); // Store tags per category\n  const [isDialogOpen, setDialogOpen] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showSuccessDialog, setShowSuccessDialog] = useState(false);\n  const [validationErrors, setValidationErrors] = useState([]);\n\n  // Edit functionality states\n  const [showUpdate, setShowUpdate] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n\n  // Check if we're in edit mode\n  const isEditMode = existingProduct && onBack;\n  // Form data state\n  const [formData, setFormData] = useState({\n    name: \"\",\n    price: \"\",\n    salePrice: null,\n    category: \"\",\n    subcategory: \"\",\n    collection: \"\",\n    type: \"\",\n    manufactureYear: \"\",\n    tags: [],\n    // Tags array in formData\n    reviews: [],\n    // Initialize reviews as an empty array\n    colors: [],\n    sizes: [],\n    images: [],\n    mainImage: \"\",\n    description: \"\",\n    technicalDimensions: {\n      length: \"\",\n      width: \"\",\n      height: \"\",\n      weight: \"\"\n    },\n    brandId: \"\",\n    brandName: \"\",\n    leadTime: \"\",\n    stock: \"\",\n    sku: \"\",\n    readyToShip: false,\n    // Add readyToShip field with default value false\n    warrantyInfo: {\n      warrantyYears: \"\",\n      warrantyCoverage: []\n    },\n    materialCareInstructions: \"\",\n    productSpecificRecommendations: \"\",\n    Estimatedtimeleadforcustomization: \"\",\n    cadFile: null // Add CAD field\n    // claimProcess: \"\",\n  });\n\n  // Fetch categories on mount\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await axios.get(\"https://api.thedesigngrit.com/api/categories/categories\");\n        setCategories(response.data); // Assuming the response contains categories\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n      }\n    };\n    fetchCategories();\n  }, []);\n  // Fetch brand details using brandId from the vendor session\n  useEffect(() => {\n    if (vendor !== null && vendor !== void 0 && vendor.brandId) {\n      const fetchBrandName = async () => {\n        try {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/brand/${vendor.brandId}`);\n          setBrandName(response.data.brandName); // Set the brand name in state\n          setFormData(prevData => ({\n            ...prevData,\n            brandName: response.data.brandName // Update formData with brand name\n          }));\n        } catch (error) {\n          console.error(\"Error fetching brand name:\", error);\n        }\n      };\n      fetchBrandName();\n    }\n  }, [vendor === null || vendor === void 0 ? void 0 : vendor.brandId]);\n  // Set brandId from vendor context\n  useEffect(() => {\n    if (vendor) {\n      setFormData(prevFormData => ({\n        ...prevFormData,\n        brandId: vendor.brandId || \"\" // Ensure the brandId exists in the vendor data\n      }));\n    }\n  }, [vendor]);\n  console.log(\"Brand Name:\", brandName); // Handle category change\n  const handleCategoryChange = async e => {\n    const selectedCategoryId = e.target.value;\n    setSelectedCategory(selectedCategoryId);\n    setFormData({\n      ...formData,\n      category: selectedCategoryId // Update formData.category\n    });\n    setSubCategories([]); // Reset subcategories\n    setSelectedSubCategory(\"\"); // Reset subcategory\n    console.log(selectedCategory);\n    try {\n      // Fetch subcategories for the selected category\n      const response = await axios.get(`https://api.thedesigngrit.com/api/subcategories/byCategory/${selectedCategoryId}`);\n      setSubCategories(response.data);\n    } catch (error) {\n      console.error(\"Error fetching subcategories:\", error);\n    }\n  };\n\n  // Handle subcategory change\n  const handleSubCategoryChange = async e => {\n    const selectedSubCategoryId = e.target.value;\n    setSelectedSubCategory(selectedSubCategoryId);\n    setFormData({\n      ...formData,\n      subcategory: selectedSubCategoryId // Update formData.subcategory\n    });\n    console.log(selectedSubCategory);\n    setTypes([]); // Reset types\n\n    try {\n      // Fetch types that are associated with the selected subcategory\n      const response = await axios.get(`https://api.thedesigngrit.com/api/subcategories/bySubcategory/${selectedSubCategoryId}`);\n      setTypes(response.data); // Set types based on the fetched data\n    } catch (error) {\n      console.error(\"Error fetching types:\", error);\n    }\n  };\n  // Handle input change for basic fields\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Special handling for leadTime and Estimatedtimeleadforcustomization to ensure they're valid ranges\n    if (name === \"leadTime\" || name === \"Estimatedtimeleadforcustomization\") {\n      // Allow numbers and a single hyphen\n      const validValue = value.replace(/[^\\d-]/g, \"\");\n      // Ensure only one hyphen\n      const parts = validValue.split(\"-\");\n      if (parts.length > 2) {\n        // If more than one hyphen, keep only the first one\n        const firstPart = parts[0];\n        const secondPart = parts[1];\n        setFormData({\n          ...formData,\n          [name]: `${firstPart}-${secondPart}`\n        });\n      } else {\n        setFormData({\n          ...formData,\n          [name]: validValue\n        });\n      }\n      return;\n    }\n\n    // Handle bullet points for specific fields\n    if (name === \"materialCareInstructions\") {\n      // For material care instructions, just allow normal line breaks\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    } else if (name === \"productSpecificRecommendations\") {\n      // For product recommendations, add bullet points\n      const formattedValue = value.split(\"\\n\").map(line => line.startsWith(\"•\") ? line : `• ${line.trim()}`).join(\"\\n\");\n      setFormData({\n        ...formData,\n        [name]: formattedValue\n      });\n    } else {\n      // For all other fields, just update normally\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === \"Enter\") {\n      e.preventDefault();\n      const {\n        name\n      } = e.target;\n      if (name === \"productSpecificRecommendations\") {\n        // Only add bullet points for product recommendations\n        setFormData(prev => ({\n          ...prev,\n          productSpecificRecommendations: (prev.productSpecificRecommendations ? prev.productSpecificRecommendations + \"\\n• \" : \"• \") + e.target.value.slice(e.target.selectionStart)\n        }));\n      } else if (name === \"materialCareInstructions\") {\n        // For material care, just add a new line\n        setFormData(prev => ({\n          ...prev,\n          materialCareInstructions: (prev.materialCareInstructions ? prev.materialCareInstructions + \"\\n\" : \"\") + e.target.value.slice(e.target.selectionStart)\n        }));\n      }\n    }\n  };\n  // Handle nested input change\n  const handleNestedChange = (e, parentField) => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [parentField]: {\n        ...formData[parentField],\n        [name]: value\n      }\n    });\n  };\n\n  // Handle array fields (tags, colors, sizes, warrantyCoverage)\n  const handleArrayChange = (e, field, parentField = null) => {\n    const {\n      value\n    } = e.target;\n\n    // Split the input value by commas and trim each item\n    const arrayValues = value.split(\",\").map(item => item.trim());\n    if (parentField) {\n      // Handle nested fields (e.g., warrantyInfo.warrantyCoverage)\n      setFormData({\n        ...formData,\n        [parentField]: {\n          ...formData[parentField],\n          [field]: arrayValues // Ensure this is an array\n        }\n      });\n    } else {\n      // Handle top-level fields (e.g., tags, colors, sizes)\n      setFormData({\n        ...formData,\n        [field]: arrayValues // Ensure this is an array\n      });\n    }\n  };\n\n  // Handles adding a tag from the input field (Press Enter)\n  useEffect(() => {\n    const fetchTags = async () => {\n      try {\n        const categories = [\"Color\", \"Shape\", \"Size\", \"Material\", \"Style\", \"Finish\", \"Functionality\"];\n        const fetchedTags = {};\n        for (const category of categories) {\n          const response = await axios.get(`https://api.thedesigngrit.com/api/tags/tags/${category}`);\n          fetchedTags[category] = response.data.map(tag => tag.name);\n        }\n        setTagOptions(fetchedTags);\n      } catch (error) {\n        console.error(\"Error fetching tags:\", error);\n      }\n    };\n    fetchTags();\n  }, []);\n  const handleAddTag = e => {\n    if (e.key === \"Enter\" && e.target.value.trim() !== \"\") {\n      const newTag = e.target.value.trim();\n      if (!tags.includes(newTag)) {\n        setTags([...tags, newTag]); // Update local state\n        setFormData({\n          ...formData,\n          tags: [...formData.tags, newTag]\n        }); // Update formData\n      }\n      e.target.value = \"\"; // Clear input\n    }\n  };\n\n  // Handles adding a tag from a dropdown\n  const handleSelectTag = (category, value) => {\n    if (value && !tags.includes(value)) {\n      setTags([...tags, value]); // Update local state\n      setFormData({\n        ...formData,\n        tags: [...formData.tags, value]\n      }); // Update formData\n    }\n  };\n\n  // Function to remove a tag by index\n  const handleRemoveTag = index => {\n    const newTags = tags.filter((_, i) => i !== index);\n    setTags(newTags); // Update local state\n    setFormData({\n      ...formData,\n      tags: newTags\n    }); // Update formData\n  };\n  const [imagePreviews, setImagePreviews] = useState([]); // Preview URLs\n  const [mainImagePreview, setMainImagePreview] = useState(null); // Main image preview\n  const [images, setImages] = useState([]); // Uploaded images\n  const [mainImage, setMainImage] = useState(null); // Main image file\n  const [showCropModal, setShowCropModal] = useState(false);\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\n  const [pendingFile, setPendingFile] = useState(null);\n  const [crop, setCrop] = useState({\n    x: 0,\n    y: 0\n  });\n  const [zoom, setZoom] = useState(1);\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\n\n  // Handle image upload\n  const handleImageUpload = e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    const img = new Image();\n    img.onload = () => {\n      if (img.width < 400 || img.height < 300) {\n        alert(\"Image too small. Minimum size: 400x300px for 4:3 ratio.\");\n        return;\n      }\n      const previewUrl = URL.createObjectURL(file);\n      setSelectedImageSrc(previewUrl);\n      setPendingFile(file);\n      setShowCropModal(true);\n    };\n    img.src = URL.createObjectURL(file);\n  };\n\n  // const handleCropComplete = (croppedBlob, croppedUrl) => {\n  //   const croppedFile = new File([croppedBlob], \"cropped.jpg\", {\n  //     type: \"image/jpeg\",\n  //   });\n\n  //   setImages((prev) => [...prev, croppedFile]);\n  //   setImagePreviews((prev) => [...prev, croppedUrl]);\n\n  //   if (!mainImage) {\n  //     setMainImage(croppedFile);\n  //     setMainImagePreview(croppedUrl);\n  //   }\n\n  //   setFormData((prevData) => ({\n  //     ...prevData,\n  //     images: [...prevData.images, croppedFile],\n  //     mainImage: prevData.mainImage || croppedFile,\n  //   }));\n\n  //   setShowCropModal(false);\n  // };\n\n  // Handle setting the main image\n  const handleSetMainImage = index => {\n    setMainImage(images[index]); // Set main image file\n    setMainImagePreview(imagePreviews[index]); // Set preview\n\n    setFormData(prevData => ({\n      ...prevData,\n      mainImage: images[index] // Update formData with the selected main image\n    }));\n  };\n\n  // Handle removing an image\n  const handleRemoveImage = index => {\n    const updatedImages = images.filter((_, i) => i !== index);\n    const updatedPreviews = imagePreviews.filter((_, i) => i !== index);\n    setImages(updatedImages);\n    setImagePreviews(updatedPreviews);\n\n    // If the removed image was the main image, update the main image\n    if (images[index] === mainImage) {\n      setMainImage(updatedImages[0] || null);\n      setMainImagePreview(updatedPreviews[0] || null);\n      setFormData(prevData => ({\n        ...prevData,\n        mainImage: updatedImages[0] || \"\" // Update mainImage in formData\n      }));\n    }\n\n    // Update formData images list\n    setFormData(prevData => ({\n      ...prevData,\n      images: updatedImages\n    }));\n  };\n  // Open confirmation dialog\n  const handleOpenDialog = () => {\n    setDialogOpen(true);\n  };\n\n  // Close confirmation dialog\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n  };\n\n  // Add CAD file handling functions\n  const handleCADUpload = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Check file type\n      const allowedTypes = [\".dwg\", \".dxf\", \".stp\", \".step\", \".igs\", \".iges\"];\n      const fileExtension = \".\" + file.name.split(\".\").pop().toLowerCase();\n      if (!allowedTypes.includes(fileExtension)) {\n        alert(\"Please upload a valid CAD file (DWG, DXF, STP, STEP, IGS, or IGES format)\");\n        return;\n      }\n      setFormData(prevData => ({\n        ...prevData,\n        cadFile: file // Changed from 'cad' to 'cadFile' to match backend\n      }));\n    }\n  };\n\n  // Add handleCheckboxChange function\n  const handleCheckboxChange = e => {\n    const {\n      name,\n      checked\n    } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: checked\n    }));\n  };\n\n  // Update handleSubmit to include readyToShip\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setDialogOpen(false);\n    setIsSubmitting(true);\n    const data = new FormData();\n\n    // Append basic fields\n    data.append(\"name\", formData.name);\n    data.append(\"price\", formData.price);\n    data.append(\"salePrice\", formData.salePrice || null || \"\");\n    data.append(\"category\", formData.category);\n    data.append(\"subcategory\", formData.subcategory);\n    data.append(\"collection\", formData.collection || \"\");\n    data.append(\"type\", formData.type || \"\");\n    data.append(\"manufactureYear\", formData.manufactureYear || \"\");\n    data.append(\"description\", formData.description || \"\");\n    data.append(\"brandId\", formData.brandId || \"\");\n    data.append(\"brandName\", formData.brandName || \"\");\n    data.append(\"leadTime\", formData.leadTime || \"\");\n    data.append(\"stock\", formData.stock || \"\");\n    data.append(\"sku\", formData.sku || \"\");\n    data.append(\"readyToShip\", formData.readyToShip); // Add readyToShip to FormData\n    data.append(\"materialCareInstructions\", formData.materialCareInstructions || \"\");\n    data.append(\"productSpecificRecommendations\", formData.productSpecificRecommendations || \"\");\n    data.append(\"Estimatedtimeleadforcustomization\", formData.Estimatedtimeleadforcustomization || \"\");\n    // Append array fields\n    formData.tags.forEach((tag, index) => data.append(`tags[${index}]`, tag));\n    formData.colors.forEach((color, index) => data.append(`colors[${index}]`, color));\n    formData.sizes.forEach((size, index) => data.append(`sizes[${index}]`, size));\n    // Append nested objects\n    data.append(\"technicalDimensions\", JSON.stringify(formData.technicalDimensions));\n    data.append(\"warrantyInfo\", JSON.stringify(formData.warrantyInfo));\n\n    // Append images\n    formData.images.forEach(file => {\n      data.append(\"images\", file);\n    });\n\n    // Append CAD file if exists\n    if (formData.cadFile) {\n      // Changed from 'cad' to 'cadFile'\n      data.append(\"cadFile\", formData.cadFile);\n    }\n\n    // Log FormData for debugging\n    for (let [key, value] of data.entries()) {\n      console.log(`${key}:`, value);\n    }\n    try {\n      const response = await axios.post(\"https://api.thedesigngrit.com/api/products/addproduct\", data, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n      console.log(\"Product created successfully:\", response.data);\n      setShowSuccessDialog(true);\n    } catch (error) {\n      var _error$response;\n      console.error(\"Error creating product:\", ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error);\n      alert(\"Failed to add product. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Add this new function near your other handlers\n  const handleWarrantyCoverageChange = coverage => {\n    setFormData(prevState => {\n      const currentCoverage = prevState.warrantyInfo.warrantyCoverage;\n      let newCoverage;\n      if (currentCoverage.includes(coverage)) {\n        // Remove the coverage if it's already selected\n        newCoverage = currentCoverage.filter(item => item !== coverage);\n      } else {\n        // Add the coverage if it's not selected\n        newCoverage = [...currentCoverage, coverage];\n      }\n      return {\n        ...prevState,\n        warrantyInfo: {\n          ...prevState.warrantyInfo,\n          warrantyCoverage: newCoverage\n        }\n      };\n    });\n  };\n\n  // Add success dialog\n  const handleCloseSuccessDialog = () => {\n    setShowSuccessDialog(false);\n    window.location.reload();\n  };\n  const validateForm = () => {\n    const errors = [];\n    if (!formData.name.trim()) errors.push(\"Product Name\");\n    if (!formData.price || isNaN(formData.price)) errors.push(\"Product Price\");\n    if (!formData.sku.trim()) errors.push(\"SKU\");\n    if (!formData.collection.trim()) errors.push(\"Collection\");\n    if (!formData.stock || isNaN(formData.stock)) errors.push(\"Stock\");\n    if (!formData.leadTime.trim()) errors.push(\"Lead Time\");\n    if (!formData.description.trim()) errors.push(\"Description\");\n    if (!formData.images || formData.images.length === 0) errors.push(\"At least one image\");\n    return errors;\n  };\n\n  // Handle edit functionality\n  const handleEdit = product => {\n    setSelectedProduct(product);\n    setShowUpdate(true);\n  };\n\n  // If we're in edit mode and have a product to edit, show UpdateProduct\n  if (isEditMode) {\n    return /*#__PURE__*/_jsxDEV(UpdateProduct, {\n      existingProduct: existingProduct,\n      onBack: onBack\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If showUpdate is true, show UpdateProduct component\n  if (showUpdate) {\n    return /*#__PURE__*/_jsxDEV(UpdateProduct, {\n      existingProduct: selectedProduct,\n      onBack: () => setShowUpdate(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header-vendor\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"All Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Home > Add Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: e => e.preventDefault(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                placeholder: \"Ex: L-shaped Sofa \",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \" Product Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"price\",\n                value: formData.price,\n                onChange: handleChange,\n                placeholder: \"Ex: 10,000.00\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Category:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"category\",\n                value: formData.category // Link to formData.category\n                ,\n                onChange: handleCategoryChange,\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Select Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category._id,\n                  children: category.name\n                }, category._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Subcategory:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"subcategory\",\n                value: formData.subcategory // Link to formData.subcategory\n                ,\n                onChange: handleSubCategoryChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Select Subcategory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 19\n                }, this), subCategories.map(subCategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subCategory._id,\n                  children: subCategory.name\n                }, subCategory._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"type\",\n                value: formData.type,\n                onChange: handleChange,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Select Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), types.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type._id,\n                  children: type.name\n                }, type._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Tag\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dropdown-container\",\n                children: Object.entries(tagOptions).map(([category, options]) => /*#__PURE__*/_jsxDEV(\"select\", {\n                  onChange: e => {\n                    handleSelectTag(category, e.target.value);\n                    e.target.value = \"\"; // Reset dropdown after selection\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: `Select ${category}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 23\n                  }, this), options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, option, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this))]\n                }, category, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"tags\",\n                placeholder: \"Add tag and press Enter  Ex: Sofa, Living Room\",\n                onKeyDown: handleAddTag,\n                className: \"tag-input\",\n                style: {\n                  margin: \"10px 0px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tags\",\n                children: tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"tag\",\n                  children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => handleRemoveTag(index),\n                    className: \"remove-tag-btn\",\n                    children: /*#__PURE__*/_jsxDEV(TiDeleteOutline, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 799,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Product Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Collection:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"collection\",\n                value: formData.collection,\n                onChange: handleChange,\n                placeholder: \"Ex: Living Room\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Manufacture Year:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"manufactureYear\",\n                value: formData.manufactureYear,\n                onChange: handleChange,\n                placeholder: \"Ex: 2023\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Colors (comma separated):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"grey\",\n                  margin: \"5px\"\n                },\n                children: \"Enter the colors of the product which be vaild for variants.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"colors\",\n                value: formData.colors.join(\",\"),\n                onChange: e => handleArrayChange(e, \"colors\"),\n                placeholder: \"Ex: Red, Blue, Green\",\n                style: {\n                  marginTop: \"10px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 965,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Sizes (comma separated):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: \"grey\",\n                  margin: \"5px\"\n                },\n                children: \"Enter the sizes of the product which be vaild for variants.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"sizes\",\n                value: formData.sizes.join(\",\"),\n                onChange: e => handleArrayChange(e, \"sizes\"),\n                placeholder: \"Ex: Small, Medium, Large\",\n                style: {\n                  marginTop: \"10px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Description:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"description\",\n                value: formData.description,\n                onChange: e => {\n                  const words = e.target.value.trim().split(/\\s+/).filter(word => word.length > 0);\n                  if (words.length <= 10) {\n                    handleChange(e);\n                  }\n                },\n                placeholder: \"Provide a brief product description (max 10 words). Include key features and benefits.\",\n                maxLength: \"2000\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: \"12px\",\n                  color: \"#666\",\n                  marginTop: \"5px\"\n                },\n                children: [\"Word count:\", \" \", formData.description.trim().split(/\\s+/).filter(word => word.length > 0).length, \"/10 words\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                textAlign: \"left\",\n                marginBottom: \"10px\",\n                marginTop: \"20px\"\n              },\n              children: \"Technical Dimensions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                gap: \"10px\",\n                flexDirection: \"row\",\n                justifyContent: \"space-between\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Length:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"length\",\n                  value: formData.technicalDimensions.length,\n                  placeholder: \"CM\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Width:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"width\",\n                  value: formData.technicalDimensions.width,\n                  placeholder: \"CM\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Height:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"height\",\n                  value: formData.technicalDimensions.height,\n                  placeholder: \"CM\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Weight:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1093,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"weight\",\n                  value: formData.technicalDimensions.weight,\n                  placeholder: \"Kg\",\n                  onChange: e => handleNestedChange(e, \"technicalDimensions\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Brand Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Brand ID:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"brandId\",\n                value: formData.brandId,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1113,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Brand Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"brandName\",\n                value: formData.brandName,\n                onChange: handleChange,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  name: \"readyToShip\",\n                  checked: formData.readyToShip,\n                  onChange: handleCheckboxChange,\n                  style: {\n                    width: \"auto\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1134,\n                  columnNumber: 19\n                }, this), \"Ready to Ship\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: \"normal\"\n                  },\n                  children: \"(That The Product is Ready to Ship )\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1142,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Lead Time:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"leadTime\",\n                value: formData.leadTime,\n                onChange: handleChange,\n                placeholder: \"Enter lead time range (e.g., 5-7 days)\",\n                required: true,\n                pattern: \"\\\\d+-\\\\d+\",\n                title: \"Please enter a valid range (e.g., 5-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Stock:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"stock\",\n                value: formData.stock,\n                onChange: handleChange,\n                placeholder: \"Enter the stock quantity  Ex:100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"SKU:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"sku\",\n                value: formData.sku,\n                onChange: handleChange,\n                placeholder: \"Enter the Stock Keeping Unit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Warranty Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Warranty Years:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"warrantyYears\",\n                value: formData.warrantyInfo.warrantyYears,\n                onChange: e => handleNestedChange(e, \"warrantyInfo\"),\n                placeholder: \"Enter the number of years of warranty\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Warranty Coverage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  flexDirection: \"row\",\n                  gap: \"10px\",\n                  marginTop: \"10px\"\n                },\n                children: [\"Manufacturer Defects\", \"Wear and Tear\", \"Damage During Shipping\"].map(coverage => /*#__PURE__*/_jsxDEV(\"label\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"8px\",\n                    cursor: \"pointer\",\n                    fontSize: \"14px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: formData.warrantyInfo.warrantyCoverage.includes(coverage),\n                    onChange: () => handleWarrantyCoverageChange(coverage),\n                    style: {\n                      cursor: \"pointer\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 23\n                  }, this), coverage]\n                }, coverage, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"column\",\n              width: \"100%\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Material Care Instructions:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"materialCareInstructions\",\n                value: formData.materialCareInstructions,\n                onChange: handleChange,\n                onKeyDown: handleKeyDown,\n                placeholder: \"Enter the material care instructions\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Product Specific Recommendations:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"productSpecificRecommendations\",\n                value: formData.productSpecificRecommendations,\n                onChange: handleChange,\n                onKeyDown: handleKeyDown,\n                placeholder: \"Enter the product specific recommendations\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Estimated Time Lead for Customization:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"Estimatedtimeleadforcustomization\",\n                value: formData.Estimatedtimeleadforcustomization,\n                onChange: handleChange,\n                placeholder: \"Enter time range (e.g., 5-7 days)\",\n                pattern: \"\\\\d+-\\\\d+\",\n                title: \"Please enter a valid range (e.g., 5-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-placeholder\",\n            children: mainImagePreview ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: mainImagePreview // Use preview URL instead of file object\n              ,\n              alt: \"Main Preview\",\n              className: \"main-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1288,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                border: \"1px solid #8A9A5B\",\n                borderRadius: \"10px\",\n                color: \"#71797E\",\n                margin: \" auto\",\n                width: \"30%\",\n                textAlign: \"center\",\n                padding: \"10px\",\n                marginTop: \"80px\"\n              },\n              children: \"Image Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1294,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-gallery\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Product Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"drop-zone\",\n              onDragOver: e => e.preventDefault() // Prevent default to allow drop\n              ,\n              onDrop: e => {\n                e.preventDefault(); // Prevent default behavior\n                const files = Array.from(e.dataTransfer.files); // Access dropped files\n                handleImageUpload({\n                  target: {\n                    files\n                  }\n                }); // Pass to handleImageUpload\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"(Upload high-quality images with a minimum resolution of 1080x1080px. Use .jpeg or .png format. Ensure clear visibility of the product from multiple angles. White background)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                multiple: true,\n                accept: \"image/jpeg, image/png, image/webp\",\n                onChange: handleImageUpload,\n                className: \"file-input\",\n                style: {\n                  display: \"none\"\n                } // Hide the input visually\n                ,\n                id: \"fileInput\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"fileInput\",\n                className: \"drop-zone-label\",\n                children: [\"Drop your image here, or browse\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 19\n                }, this), \"Jpeg, png are allowed\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => document.getElementById(\"fileInput\").click(),\n                className: \"upload-btn\",\n                children: \"Upload Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"thumbnail-list\",\n              children: imagePreviews.map((preview, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `thumbnail ${preview === mainImagePreview ? \"main-thumbnail\" : \"\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    gap: \"10px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: preview,\n                    alt: `Thumbnail ${index}`,\n                    className: \"image-thumbnail\",\n                    onClick: () => handleSetMainImage(index)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1370,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Product thumbnail.png\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1376,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkmark\",\n                    children: preview === mainImagePreview ? \"✔ Main\" : \"✔\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1377,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    gap: \"10px\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"remove-thumbnail\",\n                    onClick: () => handleRemoveImage(index),\n                    children: \"\\u2716\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1356,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cad-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"CAD File Upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cad-drop-zone\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".dwg,.dxf,.stp,.step,.igs,.iges\",\n                onChange: handleCADUpload,\n                className: \"cad-file-input\",\n                style: {\n                  display: \"none\"\n                },\n                id: \"cadFileInput\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"cadFileInput\",\n                className: \"cad-drop-zone-label\",\n                children: [\"Drop your CAD file here, or browse\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1415,\n                  columnNumber: 19\n                }, this), \"Supported formats: DWG, DXF, STP, STEP, IGS, IGES\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => document.getElementById(\"cadFileInput\").click(),\n                className: \"upload-btn\",\n                children: \"Upload CAD File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1418,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1404,\n              columnNumber: 15\n            }, this), formData.cadFile &&\n            /*#__PURE__*/\n            // Changed from 'cad' to 'cadFile'\n            _jsxDEV(\"div\", {\n              className: \"cad-file-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Selected file: \", formData.cadFile.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setFormData(prev => ({\n                  ...prev,\n                  cadFile: null\n                })),\n                className: \"remove-cad-btn\",\n                children: \"\\u2716\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1431,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1429,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1285,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-actions\",\n        children: [validationErrors.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: \"red\",\n            marginBottom: \"10px\"\n          },\n          children: [\"Please fill the following required fields:\", \" \", validationErrors.join(\", \")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1447,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn update\",\n          type: \"button\",\n          onClick: () => {\n            const errors = validateForm();\n            if (errors.length > 0) {\n              setValidationErrors(errors);\n              return;\n            }\n            setValidationErrors([]);\n            handleOpenDialog();\n          },\n          disabled: isSubmitting,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1466,\n            columnNumber: 29\n          }, this) : \"ADD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn cancel\",\n          children: \"CANCEL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1468,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1445,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(ConfirmationDialog, {\n        open: isDialogOpen,\n        title: \"Confirm Product Addition\",\n        content: \"Are you sure you want to add this product?\",\n        onConfirm: handleSubmit,\n        onCancel: handleCloseDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: showSuccessDialog,\n        onClose: handleCloseSuccessDialog,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n            children: \"Product added successfully!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1481,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1480,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseSuccessDialog,\n            color: \"primary\",\n            children: \"Done\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1484,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1478,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this), showCropModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay-uploadimage\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content-uploadimage\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cropper-container-uploadimage\",\n          children: /*#__PURE__*/_jsxDEV(Cropper, {\n            image: selectedImageSrc,\n            crop: crop,\n            zoom: zoom,\n            aspect: 4 / 3,\n            onCropChange: setCrop,\n            onZoomChange: setZoom,\n            onCropComplete: (_, area) => setCroppedAreaPixels(area)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1494,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1493,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cropper-buttons-uploadimage\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: async () => {\n              const blob = await getCroppedImg(selectedImageSrc, croppedAreaPixels);\n              const url = URL.createObjectURL(blob);\n              const croppedFile = new File([blob], pendingFile.name, {\n                type: \"image/jpeg\"\n              });\n              setImages(prev => [...prev, croppedFile]);\n              setImagePreviews(prev => [...prev, url]);\n              if (!mainImage) {\n                setMainImage(croppedFile);\n                setMainImagePreview(url);\n              }\n              setFormData(prevData => ({\n                ...prevData,\n                images: [...prevData.images, croppedFile],\n                mainImage: prevData.mainImage || croppedFile\n              }));\n              setShowCropModal(false);\n              setPendingFile(null);\n              setSelectedImageSrc(null);\n            },\n            children: \"Crop Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCropModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1537,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1504,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1492,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1491,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AddProduct, \"4CVA8J0aRcVBU1hn4XWmfu1G7To=\", false, function () {\n  return [useVendor];\n});\n_c = AddProduct;\nexport default AddProduct;\nvar _c;\n$RefreshReg$(_c, \"AddProduct\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "TiDeleteOutline", "useVendor", "Box", "ConfirmationDialog", "C<PERSON>per", "CircularProgress", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "UpdateProduct", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getCroppedImg", "imageSrc", "croppedAreaPixels", "Promise", "resolve", "image", "Image", "src", "onload", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "drawImage", "x", "y", "toBlob", "blob", "AddProduct", "existingProduct", "onBack", "_s", "vendor", "brandName", "setBrandName", "categories", "setCategories", "subCategories", "setSubCategories", "types", "setTypes", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedSubCategory", "setSelectedSubCategory", "tags", "setTags", "tagOptions", "setTagOptions", "isDialogOpen", "setDialogOpen", "isSubmitting", "setIsSubmitting", "showSuccessDialog", "setShowSuccessDialog", "validationErrors", "setValidationErrors", "showUpdate", "setShowUpdate", "selectedProduct", "setSelectedProduct", "isEditMode", "formData", "setFormData", "name", "price", "salePrice", "category", "subcategory", "collection", "type", "manufactureYear", "reviews", "colors", "sizes", "images", "mainImage", "description", "technicalDimensions", "length", "weight", "brandId", "leadTime", "stock", "sku", "readyToShip", "warrantyInfo", "warrantyYears", "warrantyCoverage", "materialCareInstructions", "productSpecificRecommendations", "Estimatedtimeleadforcustomization", "cadFile", "fetchCategories", "response", "get", "data", "error", "console", "fetchBrandName", "prevData", "prevFormData", "log", "handleCategoryChange", "e", "selectedCategoryId", "target", "value", "handleSubCategoryChange", "selectedSubCategoryId", "handleChange", "validValue", "replace", "parts", "split", "firstPart", "second<PERSON><PERSON>", "formattedValue", "map", "line", "startsWith", "trim", "join", "handleKeyDown", "key", "preventDefault", "prev", "slice", "selectionStart", "handleNestedChange", "parentField", "handleArrayChange", "field", "arrayValues", "item", "fetchTags", "fetchedTags", "tag", "handleAddTag", "newTag", "includes", "handleSelectTag", "handleRemoveTag", "index", "newTags", "filter", "_", "i", "imagePreviews", "setImagePreviews", "mainImagePreview", "setMainImagePreview", "setImages", "setMainImage", "showCropModal", "setShowCropModal", "selectedImageSrc", "setSelectedImageSrc", "pendingFile", "setPendingFile", "crop", "setCrop", "zoom", "setZoom", "setCroppedAreaPixels", "handleImageUpload", "file", "files", "img", "alert", "previewUrl", "URL", "createObjectURL", "handleSetMainImage", "handleRemoveImage", "updatedImages", "updatedPreviews", "handleOpenDialog", "handleCloseDialog", "handleCADUpload", "allowedTypes", "fileExtension", "pop", "toLowerCase", "handleCheckboxChange", "checked", "prevState", "handleSubmit", "FormData", "append", "for<PERSON>ach", "color", "size", "JSON", "stringify", "entries", "post", "headers", "_error$response", "handleWarrantyCoverageChange", "coverage", "currentCoverage", "newCoverage", "handleCloseSuccessDialog", "window", "location", "reload", "validateForm", "errors", "push", "isNaN", "handleEdit", "product", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "onSubmit", "sx", "display", "flexDirection", "onChange", "placeholder", "required", "disabled", "_id", "subCategory", "Object", "options", "option", "onKeyDown", "style", "margin", "onClick", "marginTop", "words", "word", "max<PERSON><PERSON><PERSON>", "fontSize", "textAlign", "marginBottom", "gap", "justifyContent", "readOnly", "alignItems", "fontWeight", "pattern", "title", "cursor", "alt", "border", "borderRadius", "padding", "onDragOver", "onDrop", "Array", "from", "dataTransfer", "multiple", "accept", "id", "htmlFor", "getElementById", "click", "preview", "open", "content", "onConfirm", "onCancel", "onClose", "aspect", "onCropChange", "onZoomChange", "onCropComplete", "area", "url", "croppedFile", "File", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/postProduct.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport { TiDeleteOutline } from \"react-icons/ti\"; // Import the delete icon\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\n// import { Link } from \"react-router-dom\";\r\nimport { Box } from \"@mui/material\";\r\nimport ConfirmationDialog from \"../confirmationMsg\";\r\nimport Cropper from \"react-easy-crop\";\r\nimport CircularProgress from \"@mui/material/CircularProgress\";\r\nimport Dialog from \"@mui/material/Dialog\";\r\nimport DialogActions from \"@mui/material/DialogActions\";\r\nimport DialogContent from \"@mui/material/DialogContent\";\r\nimport DialogContentText from \"@mui/material/DialogContentText\";\r\nimport DialogTitle from \"@mui/material/DialogTitle\";\r\nimport Button from \"@mui/material/Button\";\r\nimport UpdateProduct from \"./UpdateProduct\"; // Import UpdateProduct component\r\nconst getCroppedImg = (imageSrc, croppedAreaPixels) => {\r\n  return new Promise((resolve) => {\r\n    const image = new Image();\r\n    image.src = imageSrc;\r\n    image.onload = () => {\r\n      const canvas = document.createElement(\"canvas\");\r\n      canvas.width = croppedAreaPixels.width;\r\n      canvas.height = croppedAreaPixels.height;\r\n      const ctx = canvas.getContext(\"2d\");\r\n\r\n      ctx.drawImage(\r\n        image,\r\n        croppedAreaPixels.x,\r\n        croppedAreaPixels.y,\r\n        croppedAreaPixels.width,\r\n        croppedAreaPixels.height,\r\n        0,\r\n        0,\r\n        croppedAreaPixels.width,\r\n        croppedAreaPixels.height\r\n      );\r\n\r\n      canvas.toBlob((blob) => resolve(blob), \"image/jpeg\");\r\n    };\r\n  });\r\n};\r\n\r\nconst AddProduct = ({ existingProduct, onBack }) => {\r\n  const { vendor } = useVendor(); // Access vendor data from context\r\n\r\n  // State variables\r\n  const [brandName, setBrandName] = useState(\"\"); // Store fetched brand name\r\n  const [categories, setCategories] = useState([]); // Categories from API\r\n  const [subCategories, setSubCategories] = useState([]); // Subcategories from API\r\n  const [types, setTypes] = useState([]); // Types from API\r\n  const [selectedCategory, setSelectedCategory] = useState(\"\"); // Selected category\r\n  const [selectedSubCategory, setSelectedSubCategory] = useState(\"\"); // Selected subcategory\r\n  const [tags, setTags] = useState([]); // Tags array\r\n  const [tagOptions, setTagOptions] = useState({}); // Store tags per category\r\n  const [isDialogOpen, setDialogOpen] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [showSuccessDialog, setShowSuccessDialog] = useState(false);\r\n  const [validationErrors, setValidationErrors] = useState([]);\r\n\r\n  // Edit functionality states\r\n  const [showUpdate, setShowUpdate] = useState(false);\r\n  const [selectedProduct, setSelectedProduct] = useState(null);\r\n\r\n  // Check if we're in edit mode\r\n  const isEditMode = existingProduct && onBack;\r\n  // Form data state\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    price: \"\",\r\n    salePrice: null,\r\n    category: \"\",\r\n    subcategory: \"\",\r\n    collection: \"\",\r\n    type: \"\",\r\n    manufactureYear: \"\",\r\n    tags: [], // Tags array in formData\r\n    reviews: [], // Initialize reviews as an empty array\r\n    colors: [],\r\n    sizes: [],\r\n    images: [],\r\n    mainImage: \"\",\r\n    description: \"\",\r\n    technicalDimensions: {\r\n      length: \"\",\r\n      width: \"\",\r\n      height: \"\",\r\n      weight: \"\",\r\n    },\r\n    brandId: \"\",\r\n    brandName: \"\",\r\n    leadTime: \"\",\r\n    stock: \"\",\r\n    sku: \"\",\r\n    readyToShip: false, // Add readyToShip field with default value false\r\n    warrantyInfo: {\r\n      warrantyYears: \"\",\r\n      warrantyCoverage: [],\r\n    },\r\n    materialCareInstructions: \"\",\r\n    productSpecificRecommendations: \"\",\r\n    Estimatedtimeleadforcustomization: \"\",\r\n\r\n    cadFile: null, // Add CAD field\r\n    // claimProcess: \"\",\r\n  });\r\n\r\n  // Fetch categories on mount\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        const response = await axios.get(\r\n          \"https://api.thedesigngrit.com/api/categories/categories\"\r\n        );\r\n        setCategories(response.data); // Assuming the response contains categories\r\n      } catch (error) {\r\n        console.error(\"Error fetching categories:\", error);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n  // Fetch brand details using brandId from the vendor session\r\n  useEffect(() => {\r\n    if (vendor?.brandId) {\r\n      const fetchBrandName = async () => {\r\n        try {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/brand/${vendor.brandId}`\r\n          );\r\n          setBrandName(response.data.brandName); // Set the brand name in state\r\n          setFormData((prevData) => ({\r\n            ...prevData,\r\n            brandName: response.data.brandName, // Update formData with brand name\r\n          }));\r\n        } catch (error) {\r\n          console.error(\"Error fetching brand name:\", error);\r\n        }\r\n      };\r\n      fetchBrandName();\r\n    }\r\n  }, [vendor?.brandId]);\r\n  // Set brandId from vendor context\r\n  useEffect(() => {\r\n    if (vendor) {\r\n      setFormData((prevFormData) => ({\r\n        ...prevFormData,\r\n        brandId: vendor.brandId || \"\", // Ensure the brandId exists in the vendor data\r\n      }));\r\n    }\r\n  }, [vendor]);\r\n  console.log(\"Brand Name:\", brandName); // Handle category change\r\n  const handleCategoryChange = async (e) => {\r\n    const selectedCategoryId = e.target.value;\r\n    setSelectedCategory(selectedCategoryId);\r\n    setFormData({\r\n      ...formData,\r\n      category: selectedCategoryId, // Update formData.category\r\n    });\r\n\r\n    setSubCategories([]); // Reset subcategories\r\n    setSelectedSubCategory(\"\"); // Reset subcategory\r\n    console.log(selectedCategory);\r\n    try {\r\n      // Fetch subcategories for the selected category\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/subcategories/byCategory/${selectedCategoryId}`\r\n      );\r\n      setSubCategories(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching subcategories:\", error);\r\n    }\r\n  };\r\n\r\n  // Handle subcategory change\r\n  const handleSubCategoryChange = async (e) => {\r\n    const selectedSubCategoryId = e.target.value;\r\n    setSelectedSubCategory(selectedSubCategoryId);\r\n\r\n    setFormData({\r\n      ...formData,\r\n      subcategory: selectedSubCategoryId, // Update formData.subcategory\r\n    });\r\n    console.log(selectedSubCategory);\r\n    setTypes([]); // Reset types\r\n\r\n    try {\r\n      // Fetch types that are associated with the selected subcategory\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/subcategories/bySubcategory/${selectedSubCategoryId}`\r\n      );\r\n      setTypes(response.data); // Set types based on the fetched data\r\n    } catch (error) {\r\n      console.error(\"Error fetching types:\", error);\r\n    }\r\n  };\r\n  // Handle input change for basic fields\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Special handling for leadTime and Estimatedtimeleadforcustomization to ensure they're valid ranges\r\n    if (name === \"leadTime\" || name === \"Estimatedtimeleadforcustomization\") {\r\n      // Allow numbers and a single hyphen\r\n      const validValue = value.replace(/[^\\d-]/g, \"\");\r\n      // Ensure only one hyphen\r\n      const parts = validValue.split(\"-\");\r\n      if (parts.length > 2) {\r\n        // If more than one hyphen, keep only the first one\r\n        const firstPart = parts[0];\r\n        const secondPart = parts[1];\r\n        setFormData({\r\n          ...formData,\r\n          [name]: `${firstPart}-${secondPart}`,\r\n        });\r\n      } else {\r\n        setFormData({\r\n          ...formData,\r\n          [name]: validValue,\r\n        });\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Handle bullet points for specific fields\r\n    if (name === \"materialCareInstructions\") {\r\n      // For material care instructions, just allow normal line breaks\r\n      setFormData({\r\n        ...formData,\r\n        [name]: value,\r\n      });\r\n    } else if (name === \"productSpecificRecommendations\") {\r\n      // For product recommendations, add bullet points\r\n      const formattedValue = value\r\n        .split(\"\\n\")\r\n        .map((line) => (line.startsWith(\"•\") ? line : `• ${line.trim()}`))\r\n        .join(\"\\n\");\r\n\r\n      setFormData({\r\n        ...formData,\r\n        [name]: formattedValue,\r\n      });\r\n    } else {\r\n      // For all other fields, just update normally\r\n      setFormData({\r\n        ...formData,\r\n        [name]: value,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === \"Enter\") {\r\n      e.preventDefault();\r\n      const { name } = e.target;\r\n\r\n      if (name === \"productSpecificRecommendations\") {\r\n        // Only add bullet points for product recommendations\r\n        setFormData((prev) => ({\r\n          ...prev,\r\n          productSpecificRecommendations:\r\n            (prev.productSpecificRecommendations\r\n              ? prev.productSpecificRecommendations + \"\\n• \"\r\n              : \"• \") + e.target.value.slice(e.target.selectionStart),\r\n        }));\r\n      } else if (name === \"materialCareInstructions\") {\r\n        // For material care, just add a new line\r\n        setFormData((prev) => ({\r\n          ...prev,\r\n          materialCareInstructions:\r\n            (prev.materialCareInstructions\r\n              ? prev.materialCareInstructions + \"\\n\"\r\n              : \"\") + e.target.value.slice(e.target.selectionStart),\r\n        }));\r\n      }\r\n    }\r\n  };\r\n  // Handle nested input change\r\n  const handleNestedChange = (e, parentField) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [parentField]: {\r\n        ...formData[parentField],\r\n        [name]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Handle array fields (tags, colors, sizes, warrantyCoverage)\r\n  const handleArrayChange = (e, field, parentField = null) => {\r\n    const { value } = e.target;\r\n\r\n    // Split the input value by commas and trim each item\r\n    const arrayValues = value.split(\",\").map((item) => item.trim());\r\n\r\n    if (parentField) {\r\n      // Handle nested fields (e.g., warrantyInfo.warrantyCoverage)\r\n      setFormData({\r\n        ...formData,\r\n        [parentField]: {\r\n          ...formData[parentField],\r\n          [field]: arrayValues, // Ensure this is an array\r\n        },\r\n      });\r\n    } else {\r\n      // Handle top-level fields (e.g., tags, colors, sizes)\r\n      setFormData({\r\n        ...formData,\r\n        [field]: arrayValues, // Ensure this is an array\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handles adding a tag from the input field (Press Enter)\r\n  useEffect(() => {\r\n    const fetchTags = async () => {\r\n      try {\r\n        const categories = [\r\n          \"Color\",\r\n          \"Shape\",\r\n          \"Size\",\r\n          \"Material\",\r\n          \"Style\",\r\n          \"Finish\",\r\n          \"Functionality\",\r\n        ];\r\n\r\n        const fetchedTags = {};\r\n        for (const category of categories) {\r\n          const response = await axios.get(\r\n            `https://api.thedesigngrit.com/api/tags/tags/${category}`\r\n          );\r\n          fetchedTags[category] = response.data.map((tag) => tag.name);\r\n        }\r\n\r\n        setTagOptions(fetchedTags);\r\n      } catch (error) {\r\n        console.error(\"Error fetching tags:\", error);\r\n      }\r\n    };\r\n\r\n    fetchTags();\r\n  }, []);\r\n  const handleAddTag = (e) => {\r\n    if (e.key === \"Enter\" && e.target.value.trim() !== \"\") {\r\n      const newTag = e.target.value.trim();\r\n      if (!tags.includes(newTag)) {\r\n        setTags([...tags, newTag]); // Update local state\r\n        setFormData({ ...formData, tags: [...formData.tags, newTag] }); // Update formData\r\n      }\r\n      e.target.value = \"\"; // Clear input\r\n    }\r\n  };\r\n\r\n  // Handles adding a tag from a dropdown\r\n  const handleSelectTag = (category, value) => {\r\n    if (value && !tags.includes(value)) {\r\n      setTags([...tags, value]); // Update local state\r\n      setFormData({ ...formData, tags: [...formData.tags, value] }); // Update formData\r\n    }\r\n  };\r\n\r\n  // Function to remove a tag by index\r\n  const handleRemoveTag = (index) => {\r\n    const newTags = tags.filter((_, i) => i !== index);\r\n    setTags(newTags); // Update local state\r\n    setFormData({ ...formData, tags: newTags }); // Update formData\r\n  };\r\n  const [imagePreviews, setImagePreviews] = useState([]); // Preview URLs\r\n  const [mainImagePreview, setMainImagePreview] = useState(null); // Main image preview\r\n  const [images, setImages] = useState([]); // Uploaded images\r\n  const [mainImage, setMainImage] = useState(null); // Main image file\r\n  const [showCropModal, setShowCropModal] = useState(false);\r\n  const [selectedImageSrc, setSelectedImageSrc] = useState(null);\r\n  const [pendingFile, setPendingFile] = useState(null);\r\n  const [crop, setCrop] = useState({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);\r\n\r\n  // Handle image upload\r\n  const handleImageUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      if (img.width < 400 || img.height < 300) {\r\n        alert(\"Image too small. Minimum size: 400x300px for 4:3 ratio.\");\r\n        return;\r\n      }\r\n\r\n      const previewUrl = URL.createObjectURL(file);\r\n      setSelectedImageSrc(previewUrl);\r\n      setPendingFile(file);\r\n      setShowCropModal(true);\r\n    };\r\n    img.src = URL.createObjectURL(file);\r\n  };\r\n\r\n  // const handleCropComplete = (croppedBlob, croppedUrl) => {\r\n  //   const croppedFile = new File([croppedBlob], \"cropped.jpg\", {\r\n  //     type: \"image/jpeg\",\r\n  //   });\r\n\r\n  //   setImages((prev) => [...prev, croppedFile]);\r\n  //   setImagePreviews((prev) => [...prev, croppedUrl]);\r\n\r\n  //   if (!mainImage) {\r\n  //     setMainImage(croppedFile);\r\n  //     setMainImagePreview(croppedUrl);\r\n  //   }\r\n\r\n  //   setFormData((prevData) => ({\r\n  //     ...prevData,\r\n  //     images: [...prevData.images, croppedFile],\r\n  //     mainImage: prevData.mainImage || croppedFile,\r\n  //   }));\r\n\r\n  //   setShowCropModal(false);\r\n  // };\r\n\r\n  // Handle setting the main image\r\n  const handleSetMainImage = (index) => {\r\n    setMainImage(images[index]); // Set main image file\r\n    setMainImagePreview(imagePreviews[index]); // Set preview\r\n\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      mainImage: images[index], // Update formData with the selected main image\r\n    }));\r\n  };\r\n\r\n  // Handle removing an image\r\n  const handleRemoveImage = (index) => {\r\n    const updatedImages = images.filter((_, i) => i !== index);\r\n    const updatedPreviews = imagePreviews.filter((_, i) => i !== index);\r\n\r\n    setImages(updatedImages);\r\n    setImagePreviews(updatedPreviews);\r\n\r\n    // If the removed image was the main image, update the main image\r\n    if (images[index] === mainImage) {\r\n      setMainImage(updatedImages[0] || null);\r\n      setMainImagePreview(updatedPreviews[0] || null);\r\n\r\n      setFormData((prevData) => ({\r\n        ...prevData,\r\n        mainImage: updatedImages[0] || \"\", // Update mainImage in formData\r\n      }));\r\n    }\r\n\r\n    // Update formData images list\r\n    setFormData((prevData) => ({\r\n      ...prevData,\r\n      images: updatedImages,\r\n    }));\r\n  };\r\n  // Open confirmation dialog\r\n  const handleOpenDialog = () => {\r\n    setDialogOpen(true);\r\n  };\r\n\r\n  // Close confirmation dialog\r\n  const handleCloseDialog = () => {\r\n    setDialogOpen(false);\r\n  };\r\n\r\n  // Add CAD file handling functions\r\n  const handleCADUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Check file type\r\n      const allowedTypes = [\".dwg\", \".dxf\", \".stp\", \".step\", \".igs\", \".iges\"];\r\n      const fileExtension = \".\" + file.name.split(\".\").pop().toLowerCase();\r\n\r\n      if (!allowedTypes.includes(fileExtension)) {\r\n        alert(\r\n          \"Please upload a valid CAD file (DWG, DXF, STP, STEP, IGS, or IGES format)\"\r\n        );\r\n        return;\r\n      }\r\n\r\n      setFormData((prevData) => ({\r\n        ...prevData,\r\n        cadFile: file, // Changed from 'cad' to 'cadFile' to match backend\r\n      }));\r\n    }\r\n  };\r\n\r\n  // Add handleCheckboxChange function\r\n  const handleCheckboxChange = (e) => {\r\n    const { name, checked } = e.target;\r\n    setFormData((prevState) => ({\r\n      ...prevState,\r\n      [name]: checked,\r\n    }));\r\n  };\r\n\r\n  // Update handleSubmit to include readyToShip\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setDialogOpen(false);\r\n    setIsSubmitting(true);\r\n    const data = new FormData();\r\n\r\n    // Append basic fields\r\n    data.append(\"name\", formData.name);\r\n    data.append(\"price\", formData.price);\r\n    data.append(\"salePrice\", formData.salePrice || null || \"\");\r\n    data.append(\"category\", formData.category);\r\n    data.append(\"subcategory\", formData.subcategory);\r\n    data.append(\"collection\", formData.collection || \"\");\r\n    data.append(\"type\", formData.type || \"\");\r\n    data.append(\"manufactureYear\", formData.manufactureYear || \"\");\r\n    data.append(\"description\", formData.description || \"\");\r\n    data.append(\"brandId\", formData.brandId || \"\");\r\n    data.append(\"brandName\", formData.brandName || \"\");\r\n    data.append(\"leadTime\", formData.leadTime || \"\");\r\n    data.append(\"stock\", formData.stock || \"\");\r\n    data.append(\"sku\", formData.sku || \"\");\r\n    data.append(\"readyToShip\", formData.readyToShip); // Add readyToShip to FormData\r\n    data.append(\r\n      \"materialCareInstructions\",\r\n      formData.materialCareInstructions || \"\"\r\n    );\r\n    data.append(\r\n      \"productSpecificRecommendations\",\r\n      formData.productSpecificRecommendations || \"\"\r\n    );\r\n    data.append(\r\n      \"Estimatedtimeleadforcustomization\",\r\n      formData.Estimatedtimeleadforcustomization || \"\"\r\n    );\r\n    // Append array fields\r\n    formData.tags.forEach((tag, index) => data.append(`tags[${index}]`, tag));\r\n    formData.colors.forEach((color, index) =>\r\n      data.append(`colors[${index}]`, color)\r\n    );\r\n    formData.sizes.forEach((size, index) =>\r\n      data.append(`sizes[${index}]`, size)\r\n    );\r\n    // Append nested objects\r\n    data.append(\r\n      \"technicalDimensions\",\r\n      JSON.stringify(formData.technicalDimensions)\r\n    );\r\n    data.append(\"warrantyInfo\", JSON.stringify(formData.warrantyInfo));\r\n\r\n    // Append images\r\n    formData.images.forEach((file) => {\r\n      data.append(\"images\", file);\r\n    });\r\n\r\n    // Append CAD file if exists\r\n    if (formData.cadFile) {\r\n      // Changed from 'cad' to 'cadFile'\r\n      data.append(\"cadFile\", formData.cadFile);\r\n    }\r\n\r\n    // Log FormData for debugging\r\n    for (let [key, value] of data.entries()) {\r\n      console.log(`${key}:`, value);\r\n    }\r\n\r\n    try {\r\n      const response = await axios.post(\r\n        \"https://api.thedesigngrit.com/api/products/addproduct\",\r\n        data,\r\n        { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n      );\r\n      console.log(\"Product created successfully:\", response.data);\r\n      setShowSuccessDialog(true);\r\n    } catch (error) {\r\n      console.error(\"Error creating product:\", error.response?.data || error);\r\n      alert(\"Failed to add product. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Add this new function near your other handlers\r\n  const handleWarrantyCoverageChange = (coverage) => {\r\n    setFormData((prevState) => {\r\n      const currentCoverage = prevState.warrantyInfo.warrantyCoverage;\r\n      let newCoverage;\r\n\r\n      if (currentCoverage.includes(coverage)) {\r\n        // Remove the coverage if it's already selected\r\n        newCoverage = currentCoverage.filter((item) => item !== coverage);\r\n      } else {\r\n        // Add the coverage if it's not selected\r\n        newCoverage = [...currentCoverage, coverage];\r\n      }\r\n\r\n      return {\r\n        ...prevState,\r\n        warrantyInfo: {\r\n          ...prevState.warrantyInfo,\r\n          warrantyCoverage: newCoverage,\r\n        },\r\n      };\r\n    });\r\n  };\r\n\r\n  // Add success dialog\r\n  const handleCloseSuccessDialog = () => {\r\n    setShowSuccessDialog(false);\r\n    window.location.reload();\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const errors = [];\r\n    if (!formData.name.trim()) errors.push(\"Product Name\");\r\n    if (!formData.price || isNaN(formData.price)) errors.push(\"Product Price\");\r\n    if (!formData.sku.trim()) errors.push(\"SKU\");\r\n    if (!formData.collection.trim()) errors.push(\"Collection\");\r\n    if (!formData.stock || isNaN(formData.stock)) errors.push(\"Stock\");\r\n    if (!formData.leadTime.trim()) errors.push(\"Lead Time\");\r\n    if (!formData.description.trim()) errors.push(\"Description\");\r\n    if (!formData.images || formData.images.length === 0)\r\n      errors.push(\"At least one image\");\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit functionality\r\n  const handleEdit = (product) => {\r\n    setSelectedProduct(product);\r\n    setShowUpdate(true);\r\n  };\r\n\r\n  // If we're in edit mode and have a product to edit, show UpdateProduct\r\n  if (isEditMode) {\r\n    return <UpdateProduct existingProduct={existingProduct} onBack={onBack} />;\r\n  }\r\n\r\n  // If showUpdate is true, show UpdateProduct component\r\n  if (showUpdate) {\r\n    return (\r\n      <UpdateProduct\r\n        existingProduct={selectedProduct}\r\n        onBack={() => setShowUpdate(false)}\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <header className=\"dashboard-header-vendor\">\r\n        <div className=\"dashboard-header-title\">\r\n          <h2>All Products</h2>\r\n          <p>\r\n            {/* <Link to={`/vendor-dashboard/${formData.brandId}`}></Link> */}\r\n            Home &gt; Add Products\r\n          </p>\r\n        </div>\r\n      </header>\r\n      <form onSubmit={(e) => e.preventDefault()}>\r\n        <div className=\"product-form\">\r\n          <div className=\"form-left\">\r\n            <h1>Add Product</h1>\r\n\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Basic Information */}\r\n              <div className=\"form-group\">\r\n                <label>Product Name:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: L-shaped Sofa \"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label> Product Price:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"price\"\r\n                  value={formData.price}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: 10,000.00\"\r\n                  required\r\n                />\r\n              </div>\r\n              {/* <div className=\"form-group\">\r\n                <label>Sale Price:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"salePrice\"\r\n                  value={formData.salePrice}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: 1000.00\"\r\n                />\r\n              </div> */}\r\n              {/* Category Dropdown */}\r\n              <div className=\"form-group\">\r\n                <label>Category:</label>\r\n                <select\r\n                  name=\"category\"\r\n                  value={formData.category} // Link to formData.category\r\n                  onChange={handleCategoryChange}\r\n                  required\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Select Category\r\n                  </option>\r\n                  {categories.map((category) => (\r\n                    <option key={category._id} value={category._id}>\r\n                      {category.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                {/* Subcategory Dropdown */}\r\n                <label>Subcategory:</label>\r\n                <select\r\n                  name=\"subcategory\"\r\n                  value={formData.subcategory} // Link to formData.subcategory\r\n                  onChange={handleSubCategoryChange}\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Select Subcategory\r\n                  </option>\r\n                  {subCategories.map((subCategory) => (\r\n                    <option key={subCategory._id} value={subCategory._id}>\r\n                      {subCategory.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                {/* Type Dropdown */}\r\n                <label>Type:</label>\r\n                <select\r\n                  name=\"type\"\r\n                  value={formData.type}\r\n                  onChange={handleChange}\r\n                >\r\n                  <option value=\"\" disabled>\r\n                    Select Type\r\n                  </option>\r\n                  {types.map((type) => (\r\n                    <option key={type._id} value={type._id}>\r\n                      {type.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              {/* Tags Input */}\r\n              <div className=\"form-group\">\r\n                <label>Tag</label>\r\n\r\n                {/* Dropdowns for predefined tag categories */}\r\n                <div className=\"dropdown-container\">\r\n                  {Object.entries(tagOptions).map(([category, options]) => (\r\n                    <select\r\n                      key={category}\r\n                      onChange={(e) => {\r\n                        handleSelectTag(category, e.target.value);\r\n                        e.target.value = \"\"; // Reset dropdown after selection\r\n                      }}\r\n                    >\r\n                      <option value=\"\">{`Select ${category}`}</option>\r\n                      {options.map((option) => (\r\n                        <option key={option} value={option}>\r\n                          {option}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* Input field for custom tags */}\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"tags\"\r\n                  placeholder=\"Add tag and press Enter  Ex: Sofa, Living Room\"\r\n                  onKeyDown={handleAddTag}\r\n                  className=\"tag-input\"\r\n                  style={{ margin: \"10px 0px\" }}\r\n                />\r\n\r\n                {/* Display Selected Tags */}\r\n                <br />\r\n                <div className=\"tags\">\r\n                  {tags.map((tag, index) => (\r\n                    <span key={index} className=\"tag\">\r\n                      {tag}\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleRemoveTag(index)}\r\n                        className=\"remove-tag-btn\"\r\n                      >\r\n                        <TiDeleteOutline size={18} />\r\n                      </button>\r\n                    </span>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </Box>\r\n            {/* <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              // Customization Options \r\n              <div className=\"form-group\">\r\n                <label>Customization Types (Select all that apply):</label>\r\n                <div\r\n                  style={{\r\n                    marginTop: \"10px\",\r\n                    display: \"inline\",\r\n\r\n                    alignItems: \"flex-start\",\r\n                  }}\r\n                >\r\n                  {[\r\n                    \"Color Options\",\r\n                    \"Size Options\",\r\n                    \"Fabric/Material Options\",\r\n                    \"Finish Options\",\r\n                    \"Engraving/Personalization\",\r\n                    \"Design Modifications\",\r\n                    \"Other\",\r\n                  ].map((option) => (\r\n                    <div\r\n                      key={option}\r\n                      style={{\r\n                        display: \"flex\",\r\n                        flexDirection: \"row\",\r\n                        alignItems: \"center\",\r\n                        marginBottom: \"8px\",\r\n                      }}\r\n                    >\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        value={option}\r\n                        checked={customizationOptions.includes(option)}\r\n                        onChange={handleCustomizationChange}\r\n                        style={{ marginRight: \"8px\" }}\r\n                      />\r\n                      <label style={{ width: \"100%\", marginBottom: \"1px\" }}>\r\n                        {option}\r\n                      </label>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n                {customizationOptions.includes(\"Other\") && (\r\n                  <div style={{ marginTop: \"10px\" }}>\r\n                    <label>Other (Please specify):</label>\r\n                    <textarea\r\n                      name=\"otherCustomization\"\r\n                      value={otherCustomization}\r\n                      onChange={handleOtherCustomizationChange}\r\n                      placeholder=\"Specify other customization options\"\r\n                    />\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              //Additional Costs for Customization \r\n              <div className=\"form-group\">\r\n                <label>Additional Costs for Customization (Select one):</label>\r\n                <div style={{ marginTop: \"10px\" }}>\r\n                  <label>\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"additionalCosts\"\r\n                      value=\"Yes, additional cost applies\"\r\n                      checked={\r\n                        additionalCosts === \"Yes, additional cost applies\"\r\n                      }\r\n                      onChange={handleAdditionalCostsChange}\r\n                    />\r\n                    Yes, additional cost applies\r\n                  </label>\r\n                  <br />\r\n                  <label>\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"additionalCosts\"\r\n                      value=\"No additional cost\"\r\n                      checked={additionalCosts === \"No additional cost\"}\r\n                      onChange={handleAdditionalCostsChange}\r\n                    />\r\n                    No additional cost\r\n                  </label>\r\n                  <br />\r\n                  <label>\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"additionalCosts\"\r\n                      value=\"Variable based on customization type\"\r\n                      checked={\r\n                        additionalCosts ===\r\n                        \"Variable based on customization type\"\r\n                      }\r\n                      onChange={handleAdditionalCostsChange}\r\n                    />\r\n                    Variable based on customization type (please specify\r\n                    breakdown):\r\n                  </label>\r\n                </div>\r\n                {additionalCosts === \"Variable based on customization type\" && (\r\n                  <div style={{ marginTop: \"10px\" }}>\r\n                    <textarea\r\n                      name=\"costBreakdown\"\r\n                      value={costBreakdown}\r\n                      onChange={handleCostBreakdownChange}\r\n                      placeholder=\"Specify cost breakdown\"\r\n                    />\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Box> */}\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              <h2>Product Details</h2>\r\n              {/* <div className=\"form-group\">\r\n                \r\n                <label>Manufacturer:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"manufacturer\"\r\n                  value={formData.manufacturer}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: Home Essentials\"\r\n                />\r\n              </div> */}\r\n              <div className=\"form-group\">\r\n                <label>Collection:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"collection\"\r\n                  value={formData.collection}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: Living Room\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Manufacture Year:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"manufactureYear\"\r\n                  value={formData.manufactureYear}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Ex: 2023\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Colors (comma separated):</label>\r\n                <span\r\n                  style={{\r\n                    color: \"grey\",\r\n                    margin: \"5px\",\r\n                  }}\r\n                >\r\n                  Enter the colors of the product which be vaild for variants.\r\n                </span>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"colors\"\r\n                  value={formData.colors.join(\",\")}\r\n                  onChange={(e) => handleArrayChange(e, \"colors\")}\r\n                  placeholder=\"Ex: Red, Blue, Green\"\r\n                  style={{ marginTop: \"10px\" }}\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Sizes (comma separated):</label>\r\n                <span\r\n                  style={{\r\n                    color: \"grey\",\r\n                    margin: \"5px\",\r\n                  }}\r\n                >\r\n                  Enter the sizes of the product which be vaild for variants.\r\n                </span>\r\n\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"sizes\"\r\n                  value={formData.sizes.join(\",\")}\r\n                  onChange={(e) => handleArrayChange(e, \"sizes\")}\r\n                  placeholder=\"Ex: Small, Medium, Large\"\r\n                  style={{ marginTop: \"10px\" }}\r\n                />\r\n              </div>\r\n              {/* <div className=\"form-group\">\r\n                <label>Main Image URL:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"mainImage\"\r\n                  value={formData.mainImage}\r\n                  onChange={handleChange}\r\n                  readOnly\r\n                />\r\n              </div> */}\r\n              <div className=\"form-group\">\r\n                <label>Description:</label>\r\n                <textarea\r\n                  name=\"description\"\r\n                  value={formData.description}\r\n                  onChange={(e) => {\r\n                    const words = e.target.value\r\n                      .trim()\r\n                      .split(/\\s+/)\r\n                      .filter((word) => word.length > 0);\r\n                    if (words.length <= 10) {\r\n                      handleChange(e);\r\n                    }\r\n                  }}\r\n                  placeholder=\"Provide a brief product description (max 10 words). Include key features and benefits.\"\r\n                  maxLength=\"2000\"\r\n                />\r\n                <div\r\n                  style={{ fontSize: \"12px\", color: \"#666\", marginTop: \"5px\" }}\r\n                >\r\n                  Word count:{\" \"}\r\n                  {\r\n                    formData.description\r\n                      .trim()\r\n                      .split(/\\s+/)\r\n                      .filter((word) => word.length > 0).length\r\n                  }\r\n                  /10 words\r\n                </div>\r\n              </div>\r\n            </Box>\r\n\r\n            {/* Technical Dimensions */}\r\n            <div className=\"form-group\">\r\n              <h2\r\n                style={{\r\n                  textAlign: \"left\",\r\n                  marginBottom: \"10px\",\r\n                  marginTop: \"20px\",\r\n                }}\r\n              >\r\n                Technical Dimensions\r\n              </h2>\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  gap: \"10px\",\r\n                  flexDirection: \"row\",\r\n                  justifyContent: \"space-between\",\r\n                  width: \"100%\",\r\n                }}\r\n              >\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Length:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"length\"\r\n                    value={formData.technicalDimensions.length}\r\n                    placeholder=\"CM\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Width:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"width\"\r\n                    value={formData.technicalDimensions.width}\r\n                    placeholder=\"CM\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Height:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"height\"\r\n                    value={formData.technicalDimensions.height}\r\n                    placeholder=\"CM\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n                <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n                  <label>Weight:</label>\r\n                  <input\r\n                    type=\"number\"\r\n                    name=\"weight\"\r\n                    value={formData.technicalDimensions.weight}\r\n                    placeholder=\"Kg\"\r\n                    onChange={(e) =>\r\n                      handleNestedChange(e, \"technicalDimensions\")\r\n                    }\r\n                  />\r\n                </Box>\r\n              </Box>\r\n            </div>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Brand Information */}\r\n              <h2>Brand Information</h2>\r\n              <div className=\"form-group\">\r\n                <label>Brand ID:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"brandId\"\r\n                  value={formData.brandId}\r\n                  readOnly\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Brand Name:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"brandName\"\r\n                  value={formData.brandName}\r\n                  onChange={handleChange}\r\n                  readOnly\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label\r\n                  style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    name=\"readyToShip\"\r\n                    checked={formData.readyToShip}\r\n                    onChange={handleCheckboxChange}\r\n                    style={{ width: \"auto\" }}\r\n                  />\r\n                  Ready to Ship\r\n                  <span style={{ fontWeight: \"normal\" }}>\r\n                    (That The Product is Ready to Ship )\r\n                  </span>\r\n                </label>\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Lead Time:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"leadTime\"\r\n                  value={formData.leadTime}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter lead time range (e.g., 5-7 days)\"\r\n                  required\r\n                  pattern=\"\\d+-\\d+\"\r\n                  title=\"Please enter a valid range (e.g., 5-7)\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Stock:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"stock\"\r\n                  value={formData.stock}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter the stock quantity  Ex:100\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>SKU:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"sku\"\r\n                  value={formData.sku}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter the Stock Keeping Unit\"\r\n                />\r\n              </div>\r\n            </Box>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Warranty Information */}\r\n              <h2>Warranty Information</h2>\r\n              <div className=\"form-group\">\r\n                <label>Warranty Years:</label>\r\n                <input\r\n                  type=\"number\"\r\n                  name=\"warrantyYears\"\r\n                  value={formData.warrantyInfo.warrantyYears}\r\n                  onChange={(e) => handleNestedChange(e, \"warrantyInfo\")}\r\n                  placeholder=\"Enter the number of years of warranty\"\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Warranty Coverage:</label>\r\n                <div\r\n                  style={{\r\n                    display: \"flex\",\r\n                    flexDirection: \"row\",\r\n                    gap: \"10px\",\r\n                    marginTop: \"10px\",\r\n                  }}\r\n                >\r\n                  {[\r\n                    \"Manufacturer Defects\",\r\n                    \"Wear and Tear\",\r\n                    \"Damage During Shipping\",\r\n                  ].map((coverage) => (\r\n                    <label\r\n                      key={coverage}\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: \"8px\",\r\n                        cursor: \"pointer\",\r\n                        fontSize: \"14px\",\r\n                      }}\r\n                    >\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={formData.warrantyInfo.warrantyCoverage.includes(\r\n                          coverage\r\n                        )}\r\n                        onChange={() => handleWarrantyCoverageChange(coverage)}\r\n                        style={{ cursor: \"pointer\" }}\r\n                      />\r\n                      {coverage}\r\n                    </label>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </Box>\r\n            <Box\r\n              sx={{ display: \"flex\", flexDirection: \"column\", width: \"100%\" }}\r\n            >\r\n              {/* Additional Information */}\r\n              <h2>Additional Information</h2>\r\n              <div className=\"form-group\">\r\n                <label>Material Care Instructions:</label>\r\n                <textarea\r\n                  name=\"materialCareInstructions\"\r\n                  value={formData.materialCareInstructions}\r\n                  onChange={handleChange}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"Enter the material care instructions\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Product Specific Recommendations:</label>\r\n                <textarea\r\n                  name=\"productSpecificRecommendations\"\r\n                  value={formData.productSpecificRecommendations}\r\n                  onChange={handleChange}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"Enter the product specific recommendations\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"form-group\">\r\n                <label>Estimated Time Lead for Customization:</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"Estimatedtimeleadforcustomization\"\r\n                  value={formData.Estimatedtimeleadforcustomization}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Enter time range (e.g., 5-7 days)\"\r\n                  pattern=\"\\d+-\\d+\"\r\n                  title=\"Please enter a valid range (e.g., 5-7)\"\r\n                />\r\n              </div>\r\n\r\n              {/* <div className=\"form-group\">\r\n                <label>Claim Process:</label>\r\n                <textarea\r\n                  name=\"claimProcess\"\r\n                  value={formData.claimProcess}\r\n                  onChange={handleChange}\r\n                />\r\n              </div> */}\r\n            </Box>\r\n          </div>\r\n          <div className=\"form-right\">\r\n            <div className=\"image-placeholder\">\r\n              {mainImagePreview ? (\r\n                <img\r\n                  src={mainImagePreview} // Use preview URL instead of file object\r\n                  alt=\"Main Preview\"\r\n                  className=\"main-image\"\r\n                />\r\n              ) : (\r\n                <p\r\n                  style={{\r\n                    border: \"1px solid #8A9A5B\",\r\n                    borderRadius: \"10px\",\r\n                    color: \"#71797E\",\r\n                    margin: \" auto\",\r\n                    width: \"30%\",\r\n                    textAlign: \"center\",\r\n                    padding: \"10px\",\r\n                    marginTop: \"80px\",\r\n                  }}\r\n                >\r\n                  Image Preview\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"product-gallery\">\r\n              <label>Product Gallery</label>\r\n\r\n              <div\r\n                className=\"drop-zone\"\r\n                onDragOver={(e) => e.preventDefault()} // Prevent default to allow drop\r\n                onDrop={(e) => {\r\n                  e.preventDefault(); // Prevent default behavior\r\n                  const files = Array.from(e.dataTransfer.files); // Access dropped files\r\n                  handleImageUpload({ target: { files } }); // Pass to handleImageUpload\r\n                }}\r\n              >\r\n                <span>\r\n                  <strong>\r\n                    (Upload high-quality images with a minimum resolution of\r\n                    1080x1080px. Use .jpeg or .png format. Ensure clear\r\n                    visibility of the product from multiple angles. White\r\n                    background)\r\n                  </strong>\r\n                </span>\r\n                <input\r\n                  type=\"file\"\r\n                  multiple\r\n                  accept=\"image/jpeg, image/png, image/webp\"\r\n                  onChange={handleImageUpload}\r\n                  className=\"file-input\"\r\n                  style={{ display: \"none\" }} // Hide the input visually\r\n                  id=\"fileInput\"\r\n                />\r\n                <label htmlFor=\"fileInput\" className=\"drop-zone-label\">\r\n                  Drop your image here, or browse\r\n                  <br />\r\n                  Jpeg, png are allowed\r\n                </label>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => document.getElementById(\"fileInput\").click()}\r\n                  className=\"upload-btn\"\r\n                >\r\n                  Upload Images\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"thumbnail-list\">\r\n                {imagePreviews.map((preview, index) => (\r\n                  <div\r\n                    className={`thumbnail ${\r\n                      preview === mainImagePreview ? \"main-thumbnail\" : \"\"\r\n                    }`}\r\n                    key={index}\r\n                  >\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        justifyContent: \"space-between\",\r\n                        gap: \"10px\",\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={preview}\r\n                        alt={`Thumbnail ${index}`}\r\n                        className=\"image-thumbnail\"\r\n                        onClick={() => handleSetMainImage(index)}\r\n                      />\r\n                      <span>Product thumbnail.png</span>\r\n                      <span className=\"checkmark\">\r\n                        {preview === mainImagePreview ? \"✔ Main\" : \"✔\"}\r\n                      </span>\r\n                    </div>\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        justifyContent: \"space-between\",\r\n                        gap: \"10px\",\r\n                      }}\r\n                    >\r\n                      <span\r\n                        className=\"remove-thumbnail\"\r\n                        onClick={() => handleRemoveImage(index)}\r\n                      >\r\n                        ✖\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Update CAD Upload Section */}\r\n            <div className=\"cad-upload-section\">\r\n              <label>CAD File Upload</label>\r\n              <div className=\"cad-drop-zone\">\r\n                <input\r\n                  type=\"file\"\r\n                  accept=\".dwg,.dxf,.stp,.step,.igs,.iges\"\r\n                  onChange={handleCADUpload}\r\n                  className=\"cad-file-input\"\r\n                  style={{ display: \"none\" }}\r\n                  id=\"cadFileInput\"\r\n                />\r\n                <label htmlFor=\"cadFileInput\" className=\"cad-drop-zone-label\">\r\n                  Drop your CAD file here, or browse\r\n                  <br />\r\n                  Supported formats: DWG, DXF, STP, STEP, IGS, IGES\r\n                </label>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() =>\r\n                    document.getElementById(\"cadFileInput\").click()\r\n                  }\r\n                  className=\"upload-btn\"\r\n                >\r\n                  Upload CAD File\r\n                </button>\r\n              </div>\r\n              {formData.cadFile && ( // Changed from 'cad' to 'cadFile'\r\n                <div className=\"cad-file-info\">\r\n                  <span>Selected file: {formData.cadFile.name}</span>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() =>\r\n                      setFormData((prev) => ({ ...prev, cadFile: null }))\r\n                    }\r\n                    className=\"remove-cad-btn\"\r\n                  >\r\n                    ✖\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"form-actions\">\r\n          {validationErrors.length > 0 && (\r\n            <div style={{ color: \"red\", marginBottom: \"10px\" }}>\r\n              Please fill the following required fields:{\" \"}\r\n              {validationErrors.join(\", \")}\r\n            </div>\r\n          )}\r\n          <button\r\n            className=\"btn update\"\r\n            type=\"button\"\r\n            onClick={() => {\r\n              const errors = validateForm();\r\n              if (errors.length > 0) {\r\n                setValidationErrors(errors);\r\n                return;\r\n              }\r\n              setValidationErrors([]);\r\n              handleOpenDialog();\r\n            }}\r\n            disabled={isSubmitting}\r\n          >\r\n            {isSubmitting ? <CircularProgress size={24} /> : \"ADD\"}\r\n          </button>\r\n          <button className=\"btn cancel\">CANCEL</button>\r\n        </div>{\" \"}\r\n        {/* Confirmation Dialog */}\r\n        <ConfirmationDialog\r\n          open={isDialogOpen}\r\n          title=\"Confirm Product Addition\"\r\n          content=\"Are you sure you want to add this product?\"\r\n          onConfirm={handleSubmit}\r\n          onCancel={handleCloseDialog}\r\n        />\r\n        <Dialog open={showSuccessDialog} onClose={handleCloseSuccessDialog}>\r\n          <DialogTitle>Success</DialogTitle>\r\n          <DialogContent>\r\n            <DialogContentText>Product added successfully!</DialogContentText>\r\n          </DialogContent>\r\n          <DialogActions>\r\n            <Button onClick={handleCloseSuccessDialog} color=\"primary\">\r\n              Done\r\n            </Button>\r\n          </DialogActions>\r\n        </Dialog>\r\n      </form>\r\n      {showCropModal && (\r\n        <div className=\"modal-overlay-uploadimage\">\r\n          <div className=\"modal-content-uploadimage\">\r\n            <div className=\"cropper-container-uploadimage\">\r\n              <Cropper\r\n                image={selectedImageSrc}\r\n                crop={crop}\r\n                zoom={zoom}\r\n                aspect={4 / 3}\r\n                onCropChange={setCrop}\r\n                onZoomChange={setZoom}\r\n                onCropComplete={(_, area) => setCroppedAreaPixels(area)}\r\n              />\r\n            </div>\r\n            <div className=\"cropper-buttons-uploadimage\">\r\n              <button\r\n                onClick={async () => {\r\n                  const blob = await getCroppedImg(\r\n                    selectedImageSrc,\r\n                    croppedAreaPixels\r\n                  );\r\n                  const url = URL.createObjectURL(blob);\r\n                  const croppedFile = new File([blob], pendingFile.name, {\r\n                    type: \"image/jpeg\",\r\n                  });\r\n\r\n                  setImages((prev) => [...prev, croppedFile]);\r\n                  setImagePreviews((prev) => [...prev, url]);\r\n\r\n                  if (!mainImage) {\r\n                    setMainImage(croppedFile);\r\n                    setMainImagePreview(url);\r\n                  }\r\n\r\n                  setFormData((prevData) => ({\r\n                    ...prevData,\r\n                    images: [...prevData.images, croppedFile],\r\n                    mainImage: prevData.mainImage || croppedFile,\r\n                  }));\r\n\r\n                  setShowCropModal(false);\r\n                  setPendingFile(null);\r\n                  setSelectedImageSrc(null);\r\n                }}\r\n              >\r\n                Crop Image\r\n              </button>\r\n              <button onClick={() => setShowCropModal(false)}>Cancel</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddProduct;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,gBAAgB,CAAC,CAAC;AAClD,SAASC,SAAS,QAAQ,2BAA2B;AACrD;AACA,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,iBAAiB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC7C,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEC,iBAAiB,KAAK;EACrD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAACE,GAAG,GAAGN,QAAQ;IACpBI,KAAK,CAACG,MAAM,GAAG,MAAM;MACnB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,KAAK,GAAGV,iBAAiB,CAACU,KAAK;MACtCH,MAAM,CAACI,MAAM,GAAGX,iBAAiB,CAACW,MAAM;MACxC,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MAEnCD,GAAG,CAACE,SAAS,CACXX,KAAK,EACLH,iBAAiB,CAACe,CAAC,EACnBf,iBAAiB,CAACgB,CAAC,EACnBhB,iBAAiB,CAACU,KAAK,EACvBV,iBAAiB,CAACW,MAAM,EACxB,CAAC,EACD,CAAC,EACDX,iBAAiB,CAACU,KAAK,EACvBV,iBAAiB,CAACW,MACpB,CAAC;MAEDJ,MAAM,CAACU,MAAM,CAAEC,IAAI,IAAKhB,OAAO,CAACgB,IAAI,CAAC,EAAE,YAAY,CAAC;IACtD,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAMC,UAAU,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM;IAAEC;EAAO,CAAC,GAAGzC,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEhC;EACA,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACwD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC8D,YAAY,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM0E,UAAU,GAAGhC,eAAe,IAAIC,MAAM;EAC5C;EACA,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnB1B,IAAI,EAAE,EAAE;IAAE;IACV2B,OAAO,EAAE,EAAE;IAAE;IACbC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,mBAAmB,EAAE;MACnBC,MAAM,EAAE,EAAE;MACV5D,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACV4D,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE,EAAE;IACXhD,SAAS,EAAE,EAAE;IACbiD,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,WAAW,EAAE,KAAK;IAAE;IACpBC,YAAY,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE;IACpB,CAAC;IACDC,wBAAwB,EAAE,EAAE;IAC5BC,8BAA8B,EAAE,EAAE;IAClCC,iCAAiC,EAAE,EAAE;IAErCC,OAAO,EAAE,IAAI,CAAE;IACf;EACF,CAAC,CAAC;;EAEF;EACAxG,SAAS,CAAC,MAAM;IACd,MAAMyG,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAC9B,yDACF,CAAC;QACD3D,aAAa,CAAC0D,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;IACF,CAAC;IAEDJ,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EACN;EACAzG,SAAS,CAAC,MAAM;IACd,IAAI4C,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEiD,OAAO,EAAE;MACnB,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;QACjC,IAAI;UACF,MAAML,QAAQ,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAC9B,2CAA2C/D,MAAM,CAACiD,OAAO,EAC3D,CAAC;UACD/C,YAAY,CAAC4D,QAAQ,CAACE,IAAI,CAAC/D,SAAS,CAAC,CAAC,CAAC;UACvC8B,WAAW,CAAEqC,QAAQ,KAAM;YACzB,GAAGA,QAAQ;YACXnE,SAAS,EAAE6D,QAAQ,CAACE,IAAI,CAAC/D,SAAS,CAAE;UACtC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,OAAOgE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC;MACDE,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACnE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiD,OAAO,CAAC,CAAC;EACrB;EACA7F,SAAS,CAAC,MAAM;IACd,IAAI4C,MAAM,EAAE;MACV+B,WAAW,CAAEsC,YAAY,KAAM;QAC7B,GAAGA,YAAY;QACfpB,OAAO,EAAEjD,MAAM,CAACiD,OAAO,IAAI,EAAE,CAAE;MACjC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACjD,MAAM,CAAC,CAAC;EACZkE,OAAO,CAACI,GAAG,CAAC,aAAa,EAAErE,SAAS,CAAC,CAAC,CAAC;EACvC,MAAMsE,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACxC,MAAMC,kBAAkB,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACzCjE,mBAAmB,CAAC+D,kBAAkB,CAAC;IACvC1C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXK,QAAQ,EAAEsC,kBAAkB,CAAE;IAChC,CAAC,CAAC;IAEFnE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBM,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5BsD,OAAO,CAACI,GAAG,CAAC7D,gBAAgB,CAAC;IAC7B,IAAI;MACF;MACA,MAAMqD,QAAQ,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAC9B,8DAA8DU,kBAAkB,EAClF,CAAC;MACDnE,gBAAgB,CAACwD,QAAQ,CAACE,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMW,uBAAuB,GAAG,MAAOJ,CAAC,IAAK;IAC3C,MAAMK,qBAAqB,GAAGL,CAAC,CAACE,MAAM,CAACC,KAAK;IAC5C/D,sBAAsB,CAACiE,qBAAqB,CAAC;IAE7C9C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXM,WAAW,EAAEyC,qBAAqB,CAAE;IACtC,CAAC,CAAC;IACFX,OAAO,CAACI,GAAG,CAAC3D,mBAAmB,CAAC;IAChCH,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAI;MACF;MACA,MAAMsD,QAAQ,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAC9B,iEAAiEc,qBAAqB,EACxF,CAAC;MACDrE,QAAQ,CAACsD,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EACD;EACA,MAAMa,YAAY,GAAIN,CAAC,IAAK;IAC1B,MAAM;MAAExC,IAAI;MAAE2C;IAAM,CAAC,GAAGH,CAAC,CAACE,MAAM;;IAEhC;IACA,IAAI1C,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,mCAAmC,EAAE;MACvE;MACA,MAAM+C,UAAU,GAAGJ,KAAK,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAC/C;MACA,MAAMC,KAAK,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC;MACnC,IAAID,KAAK,CAAClC,MAAM,GAAG,CAAC,EAAE;QACpB;QACA,MAAMoC,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMG,UAAU,GAAGH,KAAK,CAAC,CAAC,CAAC;QAC3BlD,WAAW,CAAC;UACV,GAAGD,QAAQ;UACX,CAACE,IAAI,GAAG,GAAGmD,SAAS,IAAIC,UAAU;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLrD,WAAW,CAAC;UACV,GAAGD,QAAQ;UACX,CAACE,IAAI,GAAG+C;QACV,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAI/C,IAAI,KAAK,0BAA0B,EAAE;MACvC;MACAD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAG2C;MACV,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI3C,IAAI,KAAK,gCAAgC,EAAE;MACpD;MACA,MAAMqD,cAAc,GAAGV,KAAK,CACzBO,KAAK,CAAC,IAAI,CAAC,CACXI,GAAG,CAAEC,IAAI,IAAMA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGD,IAAI,GAAG,KAAKA,IAAI,CAACE,IAAI,CAAC,CAAC,EAAG,CAAC,CACjEC,IAAI,CAAC,IAAI,CAAC;MAEb3D,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGqD;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAtD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAG2C;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMgB,aAAa,GAAInB,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACoB,GAAG,KAAK,OAAO,EAAE;MACrBpB,CAAC,CAACqB,cAAc,CAAC,CAAC;MAClB,MAAM;QAAE7D;MAAK,CAAC,GAAGwC,CAAC,CAACE,MAAM;MAEzB,IAAI1C,IAAI,KAAK,gCAAgC,EAAE;QAC7C;QACAD,WAAW,CAAE+D,IAAI,KAAM;UACrB,GAAGA,IAAI;UACPpC,8BAA8B,EAC5B,CAACoC,IAAI,CAACpC,8BAA8B,GAChCoC,IAAI,CAACpC,8BAA8B,GAAG,MAAM,GAC5C,IAAI,IAAIc,CAAC,CAACE,MAAM,CAACC,KAAK,CAACoB,KAAK,CAACvB,CAAC,CAACE,MAAM,CAACsB,cAAc;QAC5D,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAIhE,IAAI,KAAK,0BAA0B,EAAE;QAC9C;QACAD,WAAW,CAAE+D,IAAI,KAAM;UACrB,GAAGA,IAAI;UACPrC,wBAAwB,EACtB,CAACqC,IAAI,CAACrC,wBAAwB,GAC1BqC,IAAI,CAACrC,wBAAwB,GAAG,IAAI,GACpC,EAAE,IAAIe,CAAC,CAACE,MAAM,CAACC,KAAK,CAACoB,KAAK,CAACvB,CAAC,CAACE,MAAM,CAACsB,cAAc;QAC1D,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;EACD;EACA,MAAMC,kBAAkB,GAAGA,CAACzB,CAAC,EAAE0B,WAAW,KAAK;IAC7C,MAAM;MAAElE,IAAI;MAAE2C;IAAM,CAAC,GAAGH,CAAC,CAACE,MAAM;IAChC3C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoE,WAAW,GAAG;QACb,GAAGpE,QAAQ,CAACoE,WAAW,CAAC;QACxB,CAAClE,IAAI,GAAG2C;MACV;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAGA,CAAC3B,CAAC,EAAE4B,KAAK,EAAEF,WAAW,GAAG,IAAI,KAAK;IAC1D,MAAM;MAAEvB;IAAM,CAAC,GAAGH,CAAC,CAACE,MAAM;;IAE1B;IACA,MAAM2B,WAAW,GAAG1B,KAAK,CAACO,KAAK,CAAC,GAAG,CAAC,CAACI,GAAG,CAAEgB,IAAI,IAAKA,IAAI,CAACb,IAAI,CAAC,CAAC,CAAC;IAE/D,IAAIS,WAAW,EAAE;MACf;MACAnE,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACoE,WAAW,GAAG;UACb,GAAGpE,QAAQ,CAACoE,WAAW,CAAC;UACxB,CAACE,KAAK,GAAGC,WAAW,CAAE;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAtE,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACsE,KAAK,GAAGC,WAAW,CAAE;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAjJ,SAAS,CAAC,MAAM;IACd,MAAMmJ,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMpG,UAAU,GAAG,CACjB,OAAO,EACP,OAAO,EACP,MAAM,EACN,UAAU,EACV,OAAO,EACP,QAAQ,EACR,eAAe,CAChB;QAED,MAAMqG,WAAW,GAAG,CAAC,CAAC;QACtB,KAAK,MAAMrE,QAAQ,IAAIhC,UAAU,EAAE;UACjC,MAAM2D,QAAQ,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAC9B,+CAA+C5B,QAAQ,EACzD,CAAC;UACDqE,WAAW,CAACrE,QAAQ,CAAC,GAAG2B,QAAQ,CAACE,IAAI,CAACsB,GAAG,CAAEmB,GAAG,IAAKA,GAAG,CAACzE,IAAI,CAAC;QAC9D;QAEAhB,aAAa,CAACwF,WAAW,CAAC;MAC5B,CAAC,CAAC,OAAOvC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC;IAEDsC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,YAAY,GAAIlC,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACoB,GAAG,KAAK,OAAO,IAAIpB,CAAC,CAACE,MAAM,CAACC,KAAK,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrD,MAAMkB,MAAM,GAAGnC,CAAC,CAACE,MAAM,CAACC,KAAK,CAACc,IAAI,CAAC,CAAC;MACpC,IAAI,CAAC5E,IAAI,CAAC+F,QAAQ,CAACD,MAAM,CAAC,EAAE;QAC1B7F,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE8F,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5B5E,WAAW,CAAC;UAAE,GAAGD,QAAQ;UAAEjB,IAAI,EAAE,CAAC,GAAGiB,QAAQ,CAACjB,IAAI,EAAE8F,MAAM;QAAE,CAAC,CAAC,CAAC,CAAC;MAClE;MACAnC,CAAC,CAACE,MAAM,CAACC,KAAK,GAAG,EAAE,CAAC,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAC1E,QAAQ,EAAEwC,KAAK,KAAK;IAC3C,IAAIA,KAAK,IAAI,CAAC9D,IAAI,CAAC+F,QAAQ,CAACjC,KAAK,CAAC,EAAE;MAClC7D,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE8D,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3B5C,WAAW,CAAC;QAAE,GAAGD,QAAQ;QAAEjB,IAAI,EAAE,CAAC,GAAGiB,QAAQ,CAACjB,IAAI,EAAE8D,KAAK;MAAE,CAAC,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMmC,eAAe,GAAIC,KAAK,IAAK;IACjC,MAAMC,OAAO,GAAGnG,IAAI,CAACoG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAClDjG,OAAO,CAACkG,OAAO,CAAC,CAAC,CAAC;IAClBjF,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEjB,IAAI,EAAEmG;IAAQ,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACD,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACmK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACwF,MAAM,EAAE6E,SAAS,CAAC,GAAGrK,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACyF,SAAS,EAAE6E,YAAY,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM,CAACuK,aAAa,EAAEC,gBAAgB,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1K,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2K,WAAW,EAAEC,cAAc,CAAC,GAAG5K,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6K,IAAI,EAAEC,OAAO,CAAC,GAAG9K,QAAQ,CAAC;IAAEqC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChD,MAAM,CAACyI,IAAI,EAAEC,OAAO,CAAC,GAAGhL,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACsB,iBAAiB,EAAE2J,oBAAoB,CAAC,GAAGjL,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAMkL,iBAAiB,GAAI7D,CAAC,IAAK;IAC/B,MAAM8D,IAAI,GAAG9D,CAAC,CAACE,MAAM,CAAC6D,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACD,IAAI,EAAE;IAEX,MAAME,GAAG,GAAG,IAAI3J,KAAK,CAAC,CAAC;IACvB2J,GAAG,CAACzJ,MAAM,GAAG,MAAM;MACjB,IAAIyJ,GAAG,CAACrJ,KAAK,GAAG,GAAG,IAAIqJ,GAAG,CAACpJ,MAAM,GAAG,GAAG,EAAE;QACvCqJ,KAAK,CAAC,yDAAyD,CAAC;QAChE;MACF;MAEA,MAAMC,UAAU,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MAC5CT,mBAAmB,CAACa,UAAU,CAAC;MAC/BX,cAAc,CAACO,IAAI,CAAC;MACpBX,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC;IACDa,GAAG,CAAC1J,GAAG,GAAG6J,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;EACrC,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA,MAAMO,kBAAkB,GAAI9B,KAAK,IAAK;IACpCU,YAAY,CAAC9E,MAAM,CAACoE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7BQ,mBAAmB,CAACH,aAAa,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3ChF,WAAW,CAAEqC,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACXxB,SAAS,EAAED,MAAM,CAACoE,KAAK,CAAC,CAAE;IAC5B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM+B,iBAAiB,GAAI/B,KAAK,IAAK;IACnC,MAAMgC,aAAa,GAAGpG,MAAM,CAACsE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAC1D,MAAMiC,eAAe,GAAG5B,aAAa,CAACH,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAEnES,SAAS,CAACuB,aAAa,CAAC;IACxB1B,gBAAgB,CAAC2B,eAAe,CAAC;;IAEjC;IACA,IAAIrG,MAAM,CAACoE,KAAK,CAAC,KAAKnE,SAAS,EAAE;MAC/B6E,YAAY,CAACsB,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MACtCxB,mBAAmB,CAACyB,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MAE/CjH,WAAW,CAAEqC,QAAQ,KAAM;QACzB,GAAGA,QAAQ;QACXxB,SAAS,EAAEmG,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAE;MACrC,CAAC,CAAC,CAAC;IACL;;IAEA;IACAhH,WAAW,CAAEqC,QAAQ,KAAM;MACzB,GAAGA,QAAQ;MACXzB,MAAM,EAAEoG;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EACD;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/H,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgI,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhI,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMiI,eAAe,GAAI3E,CAAC,IAAK;IAC7B,MAAM8D,IAAI,GAAG9D,CAAC,CAACE,MAAM,CAAC6D,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,MAAMc,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;MACvE,MAAMC,aAAa,GAAG,GAAG,GAAGf,IAAI,CAACtG,IAAI,CAACkD,KAAK,CAAC,GAAG,CAAC,CAACoE,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAEpE,IAAI,CAACH,YAAY,CAACxC,QAAQ,CAACyC,aAAa,CAAC,EAAE;QACzCZ,KAAK,CACH,2EACF,CAAC;QACD;MACF;MAEA1G,WAAW,CAAEqC,QAAQ,KAAM;QACzB,GAAGA,QAAQ;QACXR,OAAO,EAAE0E,IAAI,CAAE;MACjB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMkB,oBAAoB,GAAIhF,CAAC,IAAK;IAClC,MAAM;MAAExC,IAAI;MAAEyH;IAAQ,CAAC,GAAGjF,CAAC,CAACE,MAAM;IAClC3C,WAAW,CAAE2H,SAAS,KAAM;MAC1B,GAAGA,SAAS;MACZ,CAAC1H,IAAI,GAAGyH;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAOnF,CAAC,IAAK;IAChCA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAClB3E,aAAa,CAAC,KAAK,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM4C,IAAI,GAAG,IAAI4F,QAAQ,CAAC,CAAC;;IAE3B;IACA5F,IAAI,CAAC6F,MAAM,CAAC,MAAM,EAAE/H,QAAQ,CAACE,IAAI,CAAC;IAClCgC,IAAI,CAAC6F,MAAM,CAAC,OAAO,EAAE/H,QAAQ,CAACG,KAAK,CAAC;IACpC+B,IAAI,CAAC6F,MAAM,CAAC,WAAW,EAAE/H,QAAQ,CAACI,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;IAC1D8B,IAAI,CAAC6F,MAAM,CAAC,UAAU,EAAE/H,QAAQ,CAACK,QAAQ,CAAC;IAC1C6B,IAAI,CAAC6F,MAAM,CAAC,aAAa,EAAE/H,QAAQ,CAACM,WAAW,CAAC;IAChD4B,IAAI,CAAC6F,MAAM,CAAC,YAAY,EAAE/H,QAAQ,CAACO,UAAU,IAAI,EAAE,CAAC;IACpD2B,IAAI,CAAC6F,MAAM,CAAC,MAAM,EAAE/H,QAAQ,CAACQ,IAAI,IAAI,EAAE,CAAC;IACxC0B,IAAI,CAAC6F,MAAM,CAAC,iBAAiB,EAAE/H,QAAQ,CAACS,eAAe,IAAI,EAAE,CAAC;IAC9DyB,IAAI,CAAC6F,MAAM,CAAC,aAAa,EAAE/H,QAAQ,CAACe,WAAW,IAAI,EAAE,CAAC;IACtDmB,IAAI,CAAC6F,MAAM,CAAC,SAAS,EAAE/H,QAAQ,CAACmB,OAAO,IAAI,EAAE,CAAC;IAC9Ce,IAAI,CAAC6F,MAAM,CAAC,WAAW,EAAE/H,QAAQ,CAAC7B,SAAS,IAAI,EAAE,CAAC;IAClD+D,IAAI,CAAC6F,MAAM,CAAC,UAAU,EAAE/H,QAAQ,CAACoB,QAAQ,IAAI,EAAE,CAAC;IAChDc,IAAI,CAAC6F,MAAM,CAAC,OAAO,EAAE/H,QAAQ,CAACqB,KAAK,IAAI,EAAE,CAAC;IAC1Ca,IAAI,CAAC6F,MAAM,CAAC,KAAK,EAAE/H,QAAQ,CAACsB,GAAG,IAAI,EAAE,CAAC;IACtCY,IAAI,CAAC6F,MAAM,CAAC,aAAa,EAAE/H,QAAQ,CAACuB,WAAW,CAAC,CAAC,CAAC;IAClDW,IAAI,CAAC6F,MAAM,CACT,0BAA0B,EAC1B/H,QAAQ,CAAC2B,wBAAwB,IAAI,EACvC,CAAC;IACDO,IAAI,CAAC6F,MAAM,CACT,gCAAgC,EAChC/H,QAAQ,CAAC4B,8BAA8B,IAAI,EAC7C,CAAC;IACDM,IAAI,CAAC6F,MAAM,CACT,mCAAmC,EACnC/H,QAAQ,CAAC6B,iCAAiC,IAAI,EAChD,CAAC;IACD;IACA7B,QAAQ,CAACjB,IAAI,CAACiJ,OAAO,CAAC,CAACrD,GAAG,EAAEM,KAAK,KAAK/C,IAAI,CAAC6F,MAAM,CAAC,QAAQ9C,KAAK,GAAG,EAAEN,GAAG,CAAC,CAAC;IACzE3E,QAAQ,CAACW,MAAM,CAACqH,OAAO,CAAC,CAACC,KAAK,EAAEhD,KAAK,KACnC/C,IAAI,CAAC6F,MAAM,CAAC,UAAU9C,KAAK,GAAG,EAAEgD,KAAK,CACvC,CAAC;IACDjI,QAAQ,CAACY,KAAK,CAACoH,OAAO,CAAC,CAACE,IAAI,EAAEjD,KAAK,KACjC/C,IAAI,CAAC6F,MAAM,CAAC,SAAS9C,KAAK,GAAG,EAAEiD,IAAI,CACrC,CAAC;IACD;IACAhG,IAAI,CAAC6F,MAAM,CACT,qBAAqB,EACrBI,IAAI,CAACC,SAAS,CAACpI,QAAQ,CAACgB,mBAAmB,CAC7C,CAAC;IACDkB,IAAI,CAAC6F,MAAM,CAAC,cAAc,EAAEI,IAAI,CAACC,SAAS,CAACpI,QAAQ,CAACwB,YAAY,CAAC,CAAC;;IAElE;IACAxB,QAAQ,CAACa,MAAM,CAACmH,OAAO,CAAExB,IAAI,IAAK;MAChCtE,IAAI,CAAC6F,MAAM,CAAC,QAAQ,EAAEvB,IAAI,CAAC;IAC7B,CAAC,CAAC;;IAEF;IACA,IAAIxG,QAAQ,CAAC8B,OAAO,EAAE;MACpB;MACAI,IAAI,CAAC6F,MAAM,CAAC,SAAS,EAAE/H,QAAQ,CAAC8B,OAAO,CAAC;IAC1C;;IAEA;IACA,KAAK,IAAI,CAACgC,GAAG,EAAEjB,KAAK,CAAC,IAAIX,IAAI,CAACmG,OAAO,CAAC,CAAC,EAAE;MACvCjG,OAAO,CAACI,GAAG,CAAC,GAAGsB,GAAG,GAAG,EAAEjB,KAAK,CAAC;IAC/B;IAEA,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMzG,KAAK,CAAC+M,IAAI,CAC/B,uDAAuD,EACvDpG,IAAI,EACJ;QAAEqG,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MAAE,CACvD,CAAC;MACDnG,OAAO,CAACI,GAAG,CAAC,+BAA+B,EAAER,QAAQ,CAACE,IAAI,CAAC;MAC3D1C,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAO2C,KAAK,EAAE;MAAA,IAAAqG,eAAA;MACdpG,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE,EAAAqG,eAAA,GAAArG,KAAK,CAACH,QAAQ,cAAAwG,eAAA,uBAAdA,eAAA,CAAgBtG,IAAI,KAAIC,KAAK,CAAC;MACvEwE,KAAK,CAAC,0CAA0C,CAAC;IACnD,CAAC,SAAS;MACRrH,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMmJ,4BAA4B,GAAIC,QAAQ,IAAK;IACjDzI,WAAW,CAAE2H,SAAS,IAAK;MACzB,MAAMe,eAAe,GAAGf,SAAS,CAACpG,YAAY,CAACE,gBAAgB;MAC/D,IAAIkH,WAAW;MAEf,IAAID,eAAe,CAAC7D,QAAQ,CAAC4D,QAAQ,CAAC,EAAE;QACtC;QACAE,WAAW,GAAGD,eAAe,CAACxD,MAAM,CAAEX,IAAI,IAAKA,IAAI,KAAKkE,QAAQ,CAAC;MACnE,CAAC,MAAM;QACL;QACAE,WAAW,GAAG,CAAC,GAAGD,eAAe,EAAED,QAAQ,CAAC;MAC9C;MAEA,OAAO;QACL,GAAGd,SAAS;QACZpG,YAAY,EAAE;UACZ,GAAGoG,SAAS,CAACpG,YAAY;UACzBE,gBAAgB,EAAEkH;QACpB;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCrJ,oBAAoB,CAAC,KAAK,CAAC;IAC3BsJ,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,EAAE;IACjB,IAAI,CAAClJ,QAAQ,CAACE,IAAI,CAACyD,IAAI,CAAC,CAAC,EAAEuF,MAAM,CAACC,IAAI,CAAC,cAAc,CAAC;IACtD,IAAI,CAACnJ,QAAQ,CAACG,KAAK,IAAIiJ,KAAK,CAACpJ,QAAQ,CAACG,KAAK,CAAC,EAAE+I,MAAM,CAACC,IAAI,CAAC,eAAe,CAAC;IAC1E,IAAI,CAACnJ,QAAQ,CAACsB,GAAG,CAACqC,IAAI,CAAC,CAAC,EAAEuF,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC;IAC5C,IAAI,CAACnJ,QAAQ,CAACO,UAAU,CAACoD,IAAI,CAAC,CAAC,EAAEuF,MAAM,CAACC,IAAI,CAAC,YAAY,CAAC;IAC1D,IAAI,CAACnJ,QAAQ,CAACqB,KAAK,IAAI+H,KAAK,CAACpJ,QAAQ,CAACqB,KAAK,CAAC,EAAE6H,MAAM,CAACC,IAAI,CAAC,OAAO,CAAC;IAClE,IAAI,CAACnJ,QAAQ,CAACoB,QAAQ,CAACuC,IAAI,CAAC,CAAC,EAAEuF,MAAM,CAACC,IAAI,CAAC,WAAW,CAAC;IACvD,IAAI,CAACnJ,QAAQ,CAACe,WAAW,CAAC4C,IAAI,CAAC,CAAC,EAAEuF,MAAM,CAACC,IAAI,CAAC,aAAa,CAAC;IAC5D,IAAI,CAACnJ,QAAQ,CAACa,MAAM,IAAIb,QAAQ,CAACa,MAAM,CAACI,MAAM,KAAK,CAAC,EAClDiI,MAAM,CAACC,IAAI,CAAC,oBAAoB,CAAC;IACnC,OAAOD,MAAM;EACf,CAAC;;EAED;EACA,MAAMG,UAAU,GAAIC,OAAO,IAAK;IAC9BxJ,kBAAkB,CAACwJ,OAAO,CAAC;IAC3B1J,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,IAAIG,UAAU,EAAE;IACd,oBAAOzD,OAAA,CAACF,aAAa;MAAC2B,eAAe,EAAEA,eAAgB;MAACC,MAAM,EAAEA;IAAO;MAAAuL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5E;;EAEA;EACA,IAAI/J,UAAU,EAAE;IACd,oBACErD,OAAA,CAACF,aAAa;MACZ2B,eAAe,EAAE8B,eAAgB;MACjC7B,MAAM,EAAEA,CAAA,KAAM4B,aAAa,CAAC,KAAK;IAAE;MAAA2J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEN;EAEA,oBACEpN,OAAA,CAAAE,SAAA;IAAAmN,QAAA,gBACErN,OAAA;MAAQsN,SAAS,EAAC,yBAAyB;MAAAD,QAAA,eACzCrN,OAAA;QAAKsN,SAAS,EAAC,wBAAwB;QAAAD,QAAA,gBACrCrN,OAAA;UAAAqN,QAAA,EAAI;QAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBpN,OAAA;UAAAqN,QAAA,EACoE;QAEpE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACTpN,OAAA;MAAMuN,QAAQ,EAAGnH,CAAC,IAAKA,CAAC,CAACqB,cAAc,CAAC,CAAE;MAAA4F,QAAA,gBACxCrN,OAAA;QAAKsN,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BrN,OAAA;UAAKsN,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBrN,OAAA;YAAAqN,QAAA,EAAI;UAAW;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEpBpN,OAAA,CAACZ,GAAG;YACFoO,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAE3M,KAAK,EAAE;YAAO,CAAE;YAAAsM,QAAA,gBAGhErN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAa;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5BpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,MAAM;gBACX2C,KAAK,EAAE7C,QAAQ,CAACE,IAAK;gBACrB+J,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC,oBAAoB;gBAChCC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAe;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,OAAO;gBACZ2C,KAAK,EAAE7C,QAAQ,CAACG,KAAM;gBACtB8J,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC,eAAe;gBAC3BC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAYNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBpN,OAAA;gBACE4D,IAAI,EAAC,UAAU;gBACf2C,KAAK,EAAE7C,QAAQ,CAACK,QAAS,CAAC;gBAAA;gBAC1B4J,QAAQ,EAAExH,oBAAqB;gBAC/B0H,QAAQ;gBAAAR,QAAA,gBAERrN,OAAA;kBAAQuG,KAAK,EAAC,EAAE;kBAACuH,QAAQ;kBAAAT,QAAA,EAAC;gBAE1B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRrL,UAAU,CAACmF,GAAG,CAAEnD,QAAQ,iBACvB/D,OAAA;kBAA2BuG,KAAK,EAAExC,QAAQ,CAACgK,GAAI;kBAAAV,QAAA,EAC5CtJ,QAAQ,CAACH;gBAAI,GADHG,QAAQ,CAACgK,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBAEzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAY;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BpN,OAAA;gBACE4D,IAAI,EAAC,aAAa;gBAClB2C,KAAK,EAAE7C,QAAQ,CAACM,WAAY,CAAC;gBAAA;gBAC7B2J,QAAQ,EAAEnH,uBAAwB;gBAAA6G,QAAA,gBAElCrN,OAAA;kBAAQuG,KAAK,EAAC,EAAE;kBAACuH,QAAQ;kBAAAT,QAAA,EAAC;gBAE1B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRnL,aAAa,CAACiF,GAAG,CAAE8G,WAAW,iBAC7BhO,OAAA;kBAA8BuG,KAAK,EAAEyH,WAAW,CAACD,GAAI;kBAAAV,QAAA,EAClDW,WAAW,CAACpK;gBAAI,GADNoK,WAAW,CAACD,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEpB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBAEzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpBpN,OAAA;gBACE4D,IAAI,EAAC,MAAM;gBACX2C,KAAK,EAAE7C,QAAQ,CAACQ,IAAK;gBACrByJ,QAAQ,EAAEjH,YAAa;gBAAA2G,QAAA,gBAEvBrN,OAAA;kBAAQuG,KAAK,EAAC,EAAE;kBAACuH,QAAQ;kBAAAT,QAAA,EAAC;gBAE1B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACRjL,KAAK,CAAC+E,GAAG,CAAEhD,IAAI,iBACdlE,OAAA;kBAAuBuG,KAAK,EAAErC,IAAI,CAAC6J,GAAI;kBAAAV,QAAA,EACpCnJ,IAAI,CAACN;gBAAI,GADCM,IAAI,CAAC6J,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAGlBpN,OAAA;gBAAKsN,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,EAChCY,MAAM,CAAClC,OAAO,CAACpJ,UAAU,CAAC,CAACuE,GAAG,CAAC,CAAC,CAACnD,QAAQ,EAAEmK,OAAO,CAAC,kBAClDlO,OAAA;kBAEE2N,QAAQ,EAAGvH,CAAC,IAAK;oBACfqC,eAAe,CAAC1E,QAAQ,EAAEqC,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;oBACzCH,CAAC,CAACE,MAAM,CAACC,KAAK,GAAG,EAAE,CAAC,CAAC;kBACvB,CAAE;kBAAA8G,QAAA,gBAEFrN,OAAA;oBAAQuG,KAAK,EAAC,EAAE;oBAAA8G,QAAA,EAAE,UAAUtJ,QAAQ;kBAAE;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,EAC/Cc,OAAO,CAAChH,GAAG,CAAEiH,MAAM,iBAClBnO,OAAA;oBAAqBuG,KAAK,EAAE4H,MAAO;oBAAAd,QAAA,EAChCc;kBAAM,GADIA,MAAM;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACT,CAAC;gBAAA,GAXGrJ,QAAQ;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYP,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,MAAM;gBACXgK,WAAW,EAAC,gDAAgD;gBAC5DQ,SAAS,EAAE9F,YAAa;gBACxBgF,SAAS,EAAC,WAAW;gBACrBe,KAAK,EAAE;kBAAEC,MAAM,EAAE;gBAAW;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAGFpN,OAAA;gBAAAiN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpN,OAAA;gBAAKsN,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAClB5K,IAAI,CAACyE,GAAG,CAAC,CAACmB,GAAG,EAAEM,KAAK,kBACnB3I,OAAA;kBAAkBsN,SAAS,EAAC,KAAK;kBAAAD,QAAA,GAC9BhF,GAAG,eACJrI,OAAA;oBACEkE,IAAI,EAAC,QAAQ;oBACbqK,OAAO,EAAEA,CAAA,KAAM7F,eAAe,CAACC,KAAK,CAAE;oBACtC2E,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,eAE1BrN,OAAA,CAACd,eAAe;sBAAC0M,IAAI,EAAE;oBAAG;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA,GARAzE,KAAK;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAkHNpN,OAAA,CAACZ,GAAG;YACFoO,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAE3M,KAAK,EAAE;YAAO,CAAE;YAAAsM,QAAA,gBAEhErN,OAAA;cAAAqN,QAAA,EAAI;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAYxBpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,YAAY;gBACjB2C,KAAK,EAAE7C,QAAQ,CAACO,UAAW;gBAC3B0J,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC;cAAiB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAiB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,iBAAiB;gBACtB2C,KAAK,EAAE7C,QAAQ,CAACS,eAAgB;gBAChCwJ,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC,UAAU;gBACtBC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAyB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxCpN,OAAA;gBACEqO,KAAK,EAAE;kBACL1C,KAAK,EAAE,MAAM;kBACb2C,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,QAAQ;gBACb2C,KAAK,EAAE7C,QAAQ,CAACW,MAAM,CAACiD,IAAI,CAAC,GAAG,CAAE;gBACjCqG,QAAQ,EAAGvH,CAAC,IAAK2B,iBAAiB,CAAC3B,CAAC,EAAE,QAAQ,CAAE;gBAChDwH,WAAW,EAAC,sBAAsB;gBAClCS,KAAK,EAAE;kBAAEG,SAAS,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAwB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvCpN,OAAA;gBACEqO,KAAK,EAAE;kBACL1C,KAAK,EAAE,MAAM;kBACb2C,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAEPpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,OAAO;gBACZ2C,KAAK,EAAE7C,QAAQ,CAACY,KAAK,CAACgD,IAAI,CAAC,GAAG,CAAE;gBAChCqG,QAAQ,EAAGvH,CAAC,IAAK2B,iBAAiB,CAAC3B,CAAC,EAAE,OAAO,CAAE;gBAC/CwH,WAAW,EAAC,0BAA0B;gBACtCS,KAAK,EAAE;kBAAEG,SAAS,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAWNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAY;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BpN,OAAA;gBACE4D,IAAI,EAAC,aAAa;gBAClB2C,KAAK,EAAE7C,QAAQ,CAACe,WAAY;gBAC5BkJ,QAAQ,EAAGvH,CAAC,IAAK;kBACf,MAAMqI,KAAK,GAAGrI,CAAC,CAACE,MAAM,CAACC,KAAK,CACzBc,IAAI,CAAC,CAAC,CACNP,KAAK,CAAC,KAAK,CAAC,CACZ+B,MAAM,CAAE6F,IAAI,IAAKA,IAAI,CAAC/J,MAAM,GAAG,CAAC,CAAC;kBACpC,IAAI8J,KAAK,CAAC9J,MAAM,IAAI,EAAE,EAAE;oBACtB+B,YAAY,CAACN,CAAC,CAAC;kBACjB;gBACF,CAAE;gBACFwH,WAAW,EAAC,wFAAwF;gBACpGe,SAAS,EAAC;cAAM;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACFpN,OAAA;gBACEqO,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEjD,KAAK,EAAE,MAAM;kBAAE6C,SAAS,EAAE;gBAAM,CAAE;gBAAAnB,QAAA,GAC9D,aACY,EAAC,GAAG,EAEb3J,QAAQ,CAACe,WAAW,CACjB4C,IAAI,CAAC,CAAC,CACNP,KAAK,CAAC,KAAK,CAAC,CACZ+B,MAAM,CAAE6F,IAAI,IAAKA,IAAI,CAAC/J,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,EAC5C,WAEH;cAAA;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpN,OAAA;YAAKsN,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACzBrN,OAAA;cACEqO,KAAK,EAAE;gBACLQ,SAAS,EAAE,MAAM;gBACjBC,YAAY,EAAE,MAAM;gBACpBN,SAAS,EAAE;cACb,CAAE;cAAAnB,QAAA,EACH;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpN,OAAA,CAACZ,GAAG;cACFoO,EAAE,EAAE;gBACFC,OAAO,EAAE,MAAM;gBACfsB,GAAG,EAAE,MAAM;gBACXrB,aAAa,EAAE,KAAK;gBACpBsB,cAAc,EAAE,eAAe;gBAC/BjO,KAAK,EAAE;cACT,CAAE;cAAAsM,QAAA,gBAEFrN,OAAA,CAACZ,GAAG;gBAACoO,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpDrN,OAAA;kBAAAqN,QAAA,EAAO;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBpN,OAAA;kBACEkE,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,QAAQ;kBACb2C,KAAK,EAAE7C,QAAQ,CAACgB,mBAAmB,CAACC,MAAO;kBAC3CiJ,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAGvH,CAAC,IACVyB,kBAAkB,CAACzB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpN,OAAA,CAACZ,GAAG;gBAACoO,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpDrN,OAAA;kBAAAqN,QAAA,EAAO;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBpN,OAAA;kBACEkE,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,OAAO;kBACZ2C,KAAK,EAAE7C,QAAQ,CAACgB,mBAAmB,CAAC3D,KAAM;kBAC1C6M,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAGvH,CAAC,IACVyB,kBAAkB,CAACzB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpN,OAAA,CAACZ,GAAG;gBAACoO,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpDrN,OAAA;kBAAAqN,QAAA,EAAO;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBpN,OAAA;kBACEkE,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,QAAQ;kBACb2C,KAAK,EAAE7C,QAAQ,CAACgB,mBAAmB,CAAC1D,MAAO;kBAC3C4M,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAGvH,CAAC,IACVyB,kBAAkB,CAACzB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpN,OAAA,CAACZ,GAAG;gBAACoO,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,aAAa,EAAE;gBAAS,CAAE;gBAAAL,QAAA,gBACpDrN,OAAA;kBAAAqN,QAAA,EAAO;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtBpN,OAAA;kBACEkE,IAAI,EAAC,QAAQ;kBACbN,IAAI,EAAC,QAAQ;kBACb2C,KAAK,EAAE7C,QAAQ,CAACgB,mBAAmB,CAACE,MAAO;kBAC3CgJ,WAAW,EAAC,IAAI;kBAChBD,QAAQ,EAAGvH,CAAC,IACVyB,kBAAkB,CAACzB,CAAC,EAAE,qBAAqB;gBAC5C;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpN,OAAA,CAACZ,GAAG;YACFoO,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAE3M,KAAK,EAAE;YAAO,CAAE;YAAAsM,QAAA,gBAGhErN,OAAA;cAAAqN,QAAA,EAAI;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAS;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxBpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,SAAS;gBACd2C,KAAK,EAAE7C,QAAQ,CAACmB,OAAQ;gBACxBoK,QAAQ;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,WAAW;gBAChB2C,KAAK,EAAE7C,QAAQ,CAAC7B,SAAU;gBAC1B8L,QAAQ,EAAEjH,YAAa;gBACvBuI,QAAQ;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,eACzBrN,OAAA;gBACEqO,KAAK,EAAE;kBAAEZ,OAAO,EAAE,MAAM;kBAAEyB,UAAU,EAAE,QAAQ;kBAAEH,GAAG,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBAE9DrN,OAAA;kBACEkE,IAAI,EAAC,UAAU;kBACfN,IAAI,EAAC,aAAa;kBAClByH,OAAO,EAAE3H,QAAQ,CAACuB,WAAY;kBAC9B0I,QAAQ,EAAEvC,oBAAqB;kBAC/BiD,KAAK,EAAE;oBAAEtN,KAAK,EAAE;kBAAO;gBAAE;kBAAAkM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,iBAEF,eAAApN,OAAA;kBAAMqO,KAAK,EAAE;oBAAEc,UAAU,EAAE;kBAAS,CAAE;kBAAA9B,QAAA,EAAC;gBAEvC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAU;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzBpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,UAAU;gBACf2C,KAAK,EAAE7C,QAAQ,CAACoB,QAAS;gBACzB6I,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC,wCAAwC;gBACpDC,QAAQ;gBACRuB,OAAO,EAAC,WAAS;gBACjBC,KAAK,EAAC;cAAwC;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,OAAO;gBACZ2C,KAAK,EAAE7C,QAAQ,CAACqB,KAAM;gBACtB4I,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC;cAAkC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnBpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,KAAK;gBACV2C,KAAK,EAAE7C,QAAQ,CAACsB,GAAI;gBACpB2I,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC;cAA8B;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpN,OAAA,CAACZ,GAAG;YACFoO,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAE3M,KAAK,EAAE;YAAO,CAAE;YAAAsM,QAAA,gBAGhErN,OAAA;cAAAqN,QAAA,EAAI;YAAoB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAe;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbN,IAAI,EAAC,eAAe;gBACpB2C,KAAK,EAAE7C,QAAQ,CAACwB,YAAY,CAACC,aAAc;gBAC3CwI,QAAQ,EAAGvH,CAAC,IAAKyB,kBAAkB,CAACzB,CAAC,EAAE,cAAc,CAAE;gBACvDwH,WAAW,EAAC;cAAuC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAkB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCpN,OAAA;gBACEqO,KAAK,EAAE;kBACLZ,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,KAAK;kBACpBqB,GAAG,EAAE,MAAM;kBACXP,SAAS,EAAE;gBACb,CAAE;gBAAAnB,QAAA,EAED,CACC,sBAAsB,EACtB,eAAe,EACf,wBAAwB,CACzB,CAACnG,GAAG,CAAEkF,QAAQ,iBACbpM,OAAA;kBAEEqO,KAAK,EAAE;oBACLZ,OAAO,EAAE,MAAM;oBACfyB,UAAU,EAAE,QAAQ;oBACpBH,GAAG,EAAE,KAAK;oBACVO,MAAM,EAAE,SAAS;oBACjBV,QAAQ,EAAE;kBACZ,CAAE;kBAAAvB,QAAA,gBAEFrN,OAAA;oBACEkE,IAAI,EAAC,UAAU;oBACfmH,OAAO,EAAE3H,QAAQ,CAACwB,YAAY,CAACE,gBAAgB,CAACoD,QAAQ,CACtD4D,QACF,CAAE;oBACFuB,QAAQ,EAAEA,CAAA,KAAMxB,4BAA4B,CAACC,QAAQ,CAAE;oBACvDiC,KAAK,EAAE;sBAAEiB,MAAM,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,EACDhB,QAAQ;gBAAA,GAjBJA,QAAQ;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBR,CACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpN,OAAA,CAACZ,GAAG;YACFoO,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAE3M,KAAK,EAAE;YAAO,CAAE;YAAAsM,QAAA,gBAGhErN,OAAA;cAAAqN,QAAA,EAAI;YAAsB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAA2B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CpN,OAAA;gBACE4D,IAAI,EAAC,0BAA0B;gBAC/B2C,KAAK,EAAE7C,QAAQ,CAAC2B,wBAAyB;gBACzCsI,QAAQ,EAAEjH,YAAa;gBACvB0H,SAAS,EAAE7G,aAAc;gBACzBqG,WAAW,EAAC,sCAAsC;gBAClDC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAiC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChDpN,OAAA;gBACE4D,IAAI,EAAC,gCAAgC;gBACrC2C,KAAK,EAAE7C,QAAQ,CAAC4B,8BAA+B;gBAC/CqI,QAAQ,EAAEjH,YAAa;gBACvB0H,SAAS,EAAE7G,aAAc;gBACzBqG,WAAW,EAAC,4CAA4C;gBACxDC,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpN,OAAA;cAAKsN,SAAS,EAAC,YAAY;cAAAD,QAAA,gBACzBrN,OAAA;gBAAAqN,QAAA,EAAO;cAAsC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACXN,IAAI,EAAC,mCAAmC;gBACxC2C,KAAK,EAAE7C,QAAQ,CAAC6B,iCAAkC;gBAClDoI,QAAQ,EAAEjH,YAAa;gBACvBkH,WAAW,EAAC,mCAAmC;gBAC/CwB,OAAO,EAAC,WAAS;gBACjBC,KAAK,EAAC;cAAwC;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpN,OAAA;UAAKsN,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACzBrN,OAAA;YAAKsN,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAC/BnE,gBAAgB,gBACflJ,OAAA;cACEU,GAAG,EAAEwI,gBAAiB,CAAC;cAAA;cACvBqG,GAAG,EAAC,cAAc;cAClBjC,SAAS,EAAC;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,gBAEFpN,OAAA;cACEqO,KAAK,EAAE;gBACLmB,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,MAAM;gBACpB9D,KAAK,EAAE,SAAS;gBAChB2C,MAAM,EAAE,OAAO;gBACfvN,KAAK,EAAE,KAAK;gBACZ8N,SAAS,EAAE,QAAQ;gBACnBa,OAAO,EAAE,MAAM;gBACflB,SAAS,EAAE;cACb,CAAE;cAAAnB,QAAA,EACH;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENpN,OAAA;YAAKsN,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BrN,OAAA;cAAAqN,QAAA,EAAO;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAE9BpN,OAAA;cACEsN,SAAS,EAAC,WAAW;cACrBqC,UAAU,EAAGvJ,CAAC,IAAKA,CAAC,CAACqB,cAAc,CAAC,CAAE,CAAC;cAAA;cACvCmI,MAAM,EAAGxJ,CAAC,IAAK;gBACbA,CAAC,CAACqB,cAAc,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM0C,KAAK,GAAG0F,KAAK,CAACC,IAAI,CAAC1J,CAAC,CAAC2J,YAAY,CAAC5F,KAAK,CAAC,CAAC,CAAC;gBAChDF,iBAAiB,CAAC;kBAAE3D,MAAM,EAAE;oBAAE6D;kBAAM;gBAAE,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAE;cAAAkD,QAAA,gBAEFrN,OAAA;gBAAAqN,QAAA,eACErN,OAAA;kBAAAqN,QAAA,EAAQ;gBAKR;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACPpN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX8L,QAAQ;gBACRC,MAAM,EAAC,mCAAmC;gBAC1CtC,QAAQ,EAAE1D,iBAAkB;gBAC5BqD,SAAS,EAAC,YAAY;gBACtBe,KAAK,EAAE;kBAAEZ,OAAO,EAAE;gBAAO,CAAE,CAAC;gBAAA;gBAC5ByC,EAAE,EAAC;cAAW;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACFpN,OAAA;gBAAOmQ,OAAO,EAAC,WAAW;gBAAC7C,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,GAAC,iCAErD,eAAArN,OAAA;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,yBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbqK,OAAO,EAAEA,CAAA,KAAM1N,QAAQ,CAACuP,cAAc,CAAC,WAAW,CAAC,CAACC,KAAK,CAAC,CAAE;gBAC5D/C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EACvB;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENpN,OAAA;cAAKsN,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAC5BrE,aAAa,CAAC9B,GAAG,CAAC,CAACoJ,OAAO,EAAE3H,KAAK,kBAChC3I,OAAA;gBACEsN,SAAS,EAAE,aACTgD,OAAO,KAAKpH,gBAAgB,GAAG,gBAAgB,GAAG,EAAE,EACnD;gBAAAmE,QAAA,gBAGHrN,OAAA;kBACEqO,KAAK,EAAE;oBACLZ,OAAO,EAAE,MAAM;oBACfyB,UAAU,EAAE,QAAQ;oBACpBF,cAAc,EAAE,eAAe;oBAC/BD,GAAG,EAAE;kBACP,CAAE;kBAAA1B,QAAA,gBAEFrN,OAAA;oBACEU,GAAG,EAAE4P,OAAQ;oBACbf,GAAG,EAAE,aAAa5G,KAAK,EAAG;oBAC1B2E,SAAS,EAAC,iBAAiB;oBAC3BiB,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC9B,KAAK;kBAAE;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACFpN,OAAA;oBAAAqN,QAAA,EAAM;kBAAqB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCpN,OAAA;oBAAMsN,SAAS,EAAC,WAAW;oBAAAD,QAAA,EACxBiD,OAAO,KAAKpH,gBAAgB,GAAG,QAAQ,GAAG;kBAAG;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNpN,OAAA;kBACEqO,KAAK,EAAE;oBACLZ,OAAO,EAAE,MAAM;oBACfyB,UAAU,EAAE,QAAQ;oBACpBF,cAAc,EAAE,eAAe;oBAC/BD,GAAG,EAAE;kBACP,CAAE;kBAAA1B,QAAA,eAEFrN,OAAA;oBACEsN,SAAS,EAAC,kBAAkB;oBAC5BiB,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAAC/B,KAAK,CAAE;oBAAA0E,QAAA,EACzC;kBAED;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAnCDzE,KAAK;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoCP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpN,OAAA;YAAKsN,SAAS,EAAC,oBAAoB;YAAAD,QAAA,gBACjCrN,OAAA;cAAAqN,QAAA,EAAO;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9BpN,OAAA;cAAKsN,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5BrN,OAAA;gBACEkE,IAAI,EAAC,MAAM;gBACX+L,MAAM,EAAC,iCAAiC;gBACxCtC,QAAQ,EAAE5C,eAAgB;gBAC1BuC,SAAS,EAAC,gBAAgB;gBAC1Be,KAAK,EAAE;kBAAEZ,OAAO,EAAE;gBAAO,CAAE;gBAC3ByC,EAAE,EAAC;cAAc;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACFpN,OAAA;gBAAOmQ,OAAO,EAAC,cAAc;gBAAC7C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,GAAC,oCAE5D,eAAArN,OAAA;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,qDAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbqK,OAAO,EAAEA,CAAA,KACP1N,QAAQ,CAACuP,cAAc,CAAC,cAAc,CAAC,CAACC,KAAK,CAAC,CAC/C;gBACD/C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EACvB;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL1J,QAAQ,CAAC8B,OAAO;YAAA;YAAM;YACrBxF,OAAA;cAAKsN,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5BrN,OAAA;gBAAAqN,QAAA,GAAM,iBAAe,EAAC3J,QAAQ,CAAC8B,OAAO,CAAC5B,IAAI;cAAA;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDpN,OAAA;gBACEkE,IAAI,EAAC,QAAQ;gBACbqK,OAAO,EAAEA,CAAA,KACP5K,WAAW,CAAE+D,IAAI,KAAM;kBAAE,GAAGA,IAAI;kBAAElC,OAAO,EAAE;gBAAK,CAAC,CAAC,CACnD;gBACD8H,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAC3B;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpN,OAAA;QAAKsN,SAAS,EAAC,cAAc;QAAAD,QAAA,GAC1BlK,gBAAgB,CAACwB,MAAM,GAAG,CAAC,iBAC1B3E,OAAA;UAAKqO,KAAK,EAAE;YAAE1C,KAAK,EAAE,KAAK;YAAEmD,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,GAAC,4CACR,EAAC,GAAG,EAC7ClK,gBAAgB,CAACmE,IAAI,CAAC,IAAI,CAAC;QAAA;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACN,eACDpN,OAAA;UACEsN,SAAS,EAAC,YAAY;UACtBpJ,IAAI,EAAC,QAAQ;UACbqK,OAAO,EAAEA,CAAA,KAAM;YACb,MAAM3B,MAAM,GAAGD,YAAY,CAAC,CAAC;YAC7B,IAAIC,MAAM,CAACjI,MAAM,GAAG,CAAC,EAAE;cACrBvB,mBAAmB,CAACwJ,MAAM,CAAC;cAC3B;YACF;YACAxJ,mBAAmB,CAAC,EAAE,CAAC;YACvByH,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFiD,QAAQ,EAAE/K,YAAa;UAAAsK,QAAA,EAEtBtK,YAAY,gBAAG/C,OAAA,CAACT,gBAAgB;YAACqM,IAAI,EAAE;UAAG;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACTpN,OAAA;UAAQsN,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,EAAC,GAAG,eAEVpN,OAAA,CAACX,kBAAkB;QACjBkR,IAAI,EAAE1N,YAAa;QACnBwM,KAAK,EAAC,0BAA0B;QAChCmB,OAAO,EAAC,4CAA4C;QACpDC,SAAS,EAAElF,YAAa;QACxBmF,QAAQ,EAAE5F;MAAkB;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFpN,OAAA,CAACR,MAAM;QAAC+Q,IAAI,EAAEtN,iBAAkB;QAAC0N,OAAO,EAAEpE,wBAAyB;QAAAc,QAAA,gBACjErN,OAAA,CAACJ,WAAW;UAAAyN,QAAA,EAAC;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClCpN,OAAA,CAACN,aAAa;UAAA2N,QAAA,eACZrN,OAAA,CAACL,iBAAiB;YAAA0N,QAAA,EAAC;UAA2B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAChBpN,OAAA,CAACP,aAAa;UAAA4N,QAAA,eACZrN,OAAA,CAACH,MAAM;YAAC0O,OAAO,EAAEhC,wBAAyB;YAACZ,KAAK,EAAC,SAAS;YAAA0B,QAAA,EAAC;UAE3D;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACN9D,aAAa,iBACZtJ,OAAA;MAAKsN,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACxCrN,OAAA;QAAKsN,SAAS,EAAC,2BAA2B;QAAAD,QAAA,gBACxCrN,OAAA;UAAKsN,SAAS,EAAC,+BAA+B;UAAAD,QAAA,eAC5CrN,OAAA,CAACV,OAAO;YACNkB,KAAK,EAAEgJ,gBAAiB;YACxBI,IAAI,EAAEA,IAAK;YACXE,IAAI,EAAEA,IAAK;YACX8G,MAAM,EAAE,CAAC,GAAG,CAAE;YACdC,YAAY,EAAEhH,OAAQ;YACtBiH,YAAY,EAAE/G,OAAQ;YACtBgH,cAAc,EAAEA,CAACjI,CAAC,EAAEkI,IAAI,KAAKhH,oBAAoB,CAACgH,IAAI;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpN,OAAA;UAAKsN,SAAS,EAAC,6BAA6B;UAAAD,QAAA,gBAC1CrN,OAAA;YACEuO,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB,MAAMhN,IAAI,GAAG,MAAMpB,aAAa,CAC9BqJ,gBAAgB,EAChBnJ,iBACF,CAAC;cACD,MAAM4Q,GAAG,GAAG1G,GAAG,CAACC,eAAe,CAACjJ,IAAI,CAAC;cACrC,MAAM2P,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC5P,IAAI,CAAC,EAAEmI,WAAW,CAAC9F,IAAI,EAAE;gBACrDM,IAAI,EAAE;cACR,CAAC,CAAC;cAEFkF,SAAS,CAAE1B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEwJ,WAAW,CAAC,CAAC;cAC3CjI,gBAAgB,CAAEvB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEuJ,GAAG,CAAC,CAAC;cAE1C,IAAI,CAACzM,SAAS,EAAE;gBACd6E,YAAY,CAAC6H,WAAW,CAAC;gBACzB/H,mBAAmB,CAAC8H,GAAG,CAAC;cAC1B;cAEAtN,WAAW,CAAEqC,QAAQ,KAAM;gBACzB,GAAGA,QAAQ;gBACXzB,MAAM,EAAE,CAAC,GAAGyB,QAAQ,CAACzB,MAAM,EAAE2M,WAAW,CAAC;gBACzC1M,SAAS,EAAEwB,QAAQ,CAACxB,SAAS,IAAI0M;cACnC,CAAC,CAAC,CAAC;cAEH3H,gBAAgB,CAAC,KAAK,CAAC;cACvBI,cAAc,CAAC,IAAI,CAAC;cACpBF,mBAAmB,CAAC,IAAI,CAAC;YAC3B,CAAE;YAAA4D,QAAA,EACH;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpN,OAAA;YAAQuO,OAAO,EAAEA,CAAA,KAAMhF,gBAAgB,CAAC,KAAK,CAAE;YAAA8D,QAAA,EAAC;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACzL,EAAA,CA59CIH,UAAU;EAAA,QACKrC,SAAS;AAAA;AAAAiS,EAAA,GADxB5P,UAAU;AA89ChB,eAAeA,UAAU;AAAC,IAAA4P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}