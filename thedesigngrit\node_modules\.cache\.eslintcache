[{"D:\\TDGweb\\TDG\\thedesigngrit\\src\\index.js": "1", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\reportWebVitals.js": "2", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\App.js": "3", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\userContext.js": "4", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\vendorContext.js": "5", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Context\\cartcontext.js": "6", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Context\\scrollToTop.jsx": "7", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\JobDescription.jsx": "8", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\loadingScreen.jsx": "9", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Policy.jsx": "10", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\subcategories.jsx": "11", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Partners.jsx": "12", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\types.jsx": "13", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\userss.jsx": "14", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ReadyToship.jsx": "15", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ProductsPage.jsx": "16", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\myAccount.jsx": "17", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ProductPage.jsx": "18", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\FAQs.jsx": "19", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\TrackOrder.jsx": "20", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\careers.jsx": "21", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Checkout.jsx": "22", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ContactUs.jsx": "23", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ShoppingCart.jsx": "24", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\VendorProfile.jsx": "25", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\login.jsx": "26", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\aboutUs.jsx": "27", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Vendorspage.jsx": "28", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\signup.jsx": "29", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\home.jsx": "30", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\vendorSide\\VendorHome.jsx": "31", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\vendorSide\\AdminHome.jsx": "32", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\addbrand.jsx": "33", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\notificationPage.jsx": "34", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\SignupVendor.jsx": "35", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\editEmployee.jsx": "36", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\UpdateProduct.jsx": "37", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\orderDetails.jsx": "38", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\signinVendor.jsx": "39", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\VerifyPartners.jsx": "40", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\partnerApplication.jsx": "41", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Footer.jsx": "42", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Topheader.jsx": "43", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\brandCursol.jsx": "44", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\reviewBox.jsx": "45", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\toast.jsx": "46", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\rating.jsx": "47", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\relatedProducts.jsx": "48", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\invoiceOrderCustomer.jsx": "49", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\navBar.jsx": "50", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\paymentsIcons.jsx": "51", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\JobDesc\\jobForm.jsx": "52", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\account\\profile.jsx": "53", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\Productsgrid.jsx": "54", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\filters.jsx": "55", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\Shipping.jsx": "56", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\resetPassowrd.jsx": "57", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\TopFilters.jsx": "58", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\billingInfo.jsx": "59", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\JobDesc\\applicationSentPopUp.jsx": "60", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\account\\wishlist.jsx": "61", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\About\\heroAbout.jsx": "62", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Context\\scrollingAnimation.js": "63", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\careers\\jobBorder.jsx": "64", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\careers\\valueCards.jsx": "65", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Checkout.jsx": "66", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\product\\optionPopUp.jsx": "67", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\signInForm.jsx": "68", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\About\\ourTeam.jsx": "69", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\signUpForm.jsx": "70", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\profileheader.jsx": "71", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Categories.jsx": "72", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Hero.jsx": "73", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\navbarVendor.jsx": "74", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Catalogs.jsx": "75", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Products.jsx": "76", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\DashboardVendor.jsx": "77", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\orderListVendor.jsx": "78", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\sideBarVendor.jsx": "79", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\productsPageVendor.jsx": "80", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\brandingPage.jsx": "81", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\brandSignup.jsx": "82", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\employeePage.jsx": "83", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\postProduct.jsx": "84", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Addemployee.jsx": "85", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\viewInStoreVendor.jsx": "86", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\quotationsList.jsx": "87", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\VendorsGrid.jsx": "88", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\TopButtons.jsx": "89", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\Filters.jsx": "90", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminNav.jsx": "91", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\dashboardAdmin.jsx": "92", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\ProductsAdmin.jsx": "93", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminSideBar.jsx": "94", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\createCategory.jsx": "95", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\tags.jsx": "96", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\categoriesList.jsx": "97", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\Requests.jsx": "98", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\VendorLayout.jsx": "99", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\orderListAdmin.jsx": "100", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminLayout.jsx": "101", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\invoice.jsx": "102", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\Sustainability.jsx": "103", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\partners.jsx": "104", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\bestSeller.jsx": "105", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\Category.jsx": "106", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\concept.jsx": "107", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\favoriteOverlay.jsx": "108", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopUp.jsx": "109", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\confirmationMsg.jsx": "110", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\productcard.jsx": "111", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\forgetPassword.jsx": "112", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\BillingInfoPop.jsx": "113", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\greeting.jsx": "114", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\menuhover\\Menudrop.jsx": "115", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Popups\\CartOverlay.jsx": "116", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\ordersummary.jsx": "117", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Billingform.jsx": "118", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Shippingform.jsx": "119", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Paymentmethod.jsx": "120", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\product\\viewInStore.jsx": "121", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\product\\RequestInfo.jsx": "122", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\successfullyRegistered.jsx": "123", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\CategoryCard.jsx": "124", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Productscard.jsx": "125", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\CatalogCard.jsx": "126", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\Vendorcard.jsx": "127", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\notificationOverlay.jsx": "128", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\promotionProduct.jsx": "129", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\editcategories.jsx": "130", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\EditProfile.jsx": "131", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\ordersPopup.jsx": "132", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\billingSummary.jsx": "133", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\PromotionPage.jsx": "134", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\EditPromotionModal.jsx": "135", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\CreatePromotionDialog.jsx": "136", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\PosPage.jsx": "137", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\concepts.jsx": "138", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\PrivateRouteAdmin.js": "139", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\AdminLogin.jsx": "140", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\adminContext.js": "141", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\onSale.jsx": "142", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\orderSubmit.jsx": "143", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting.jsx": "144", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\Chart.jsx": "145", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\PayoutHistoryTable.jsx": "146", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\RefundTable.jsx": "147", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\PaymentMethod.jsx": "148", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\DateRangePicker.jsx": "149", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\VariantDialog.jsx": "150", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\reviewPopup.jsx": "151", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\allEmployees.jsx": "152", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\brandsAdmin.jsx": "153", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminNotifications.jsx": "154", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\promotionsAdmin.jsx": "155", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\orderDetailsAdmin.jsx": "156", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\contactUsSuccess.jsx": "157", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\TrackQuotation.jsx": "158", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\quotationDealSuccess.jsx": "159", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\AddressSelectionPopup.jsx": "160", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\services\\paymobService.js": "161", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\trackViewInStore.jsx": "162", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\PendingProductUpdates.jsx": "163", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\ourEmployees.jsx": "164", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\PendingBrandUpdates.jsx": "165", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\contactusRequests.jsx": "166", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\accountingAdmin.jsx": "167", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\orderFailed.jsx": "168", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\accountExists.jsx": "169", "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\ProfileCardVendor.jsx": "170"}, {"size": 642, "mtime": *************, "results": "171", "hashOfConfig": "172"}, {"size": 375, "mtime": *************, "results": "173", "hashOfConfig": "172"}, {"size": 14051, "mtime": *************, "results": "174", "hashOfConfig": "172"}, {"size": 1165, "mtime": *************, "results": "175", "hashOfConfig": "172"}, {"size": 1089, "mtime": *************, "results": "176", "hashOfConfig": "172"}, {"size": 5163, "mtime": *************, "results": "177", "hashOfConfig": "172"}, {"size": 436, "mtime": *************, "results": "178", "hashOfConfig": "172"}, {"size": 3991, "mtime": *************, "results": "179", "hashOfConfig": "172"}, {"size": 5430, "mtime": *************, "results": "180", "hashOfConfig": "172"}, {"size": 3075, "mtime": *************, "results": "181", "hashOfConfig": "172"}, {"size": 10145, "mtime": *************, "results": "182", "hashOfConfig": "172"}, {"size": 2453, "mtime": 1745246263146, "results": "183", "hashOfConfig": "172"}, {"size": 14461, "mtime": 1750583904290, "results": "184", "hashOfConfig": "172"}, {"size": 476, "mtime": 1738492989398, "results": "185", "hashOfConfig": "172"}, {"size": 9577, "mtime": 1750781378394, "results": "186", "hashOfConfig": "172"}, {"size": 8164, "mtime": 1750781378390, "results": "187", "hashOfConfig": "172"}, {"size": 5672, "mtime": 1751879532892, "results": "188", "hashOfConfig": "172"}, {"size": 54998, "mtime": 1751879532881, "results": "189", "hashOfConfig": "172"}, {"size": 4327, "mtime": 1750781378378, "results": "190", "hashOfConfig": "172"}, {"size": 18926, "mtime": 1751879532888, "results": "191", "hashOfConfig": "172"}, {"size": 1349, "mtime": 1739441157321, "results": "192", "hashOfConfig": "172"}, {"size": 395, "mtime": 1748242361538, "results": "193", "hashOfConfig": "172"}, {"size": 5689, "mtime": 1748242361538, "results": "194", "hashOfConfig": "172"}, {"size": 5882, "mtime": 1748945138710, "results": "195", "hashOfConfig": "172"}, {"size": 2490, "mtime": 1750583904246, "results": "196", "hashOfConfig": "172"}, {"size": 668, "mtime": 1740410739748, "results": "197", "hashOfConfig": "172"}, {"size": 5620, "mtime": 1750781378429, "results": "198", "hashOfConfig": "172"}, {"size": 2358, "mtime": 1750781378429, "results": "199", "hashOfConfig": "172"}, {"size": 665, "mtime": 1740410758925, "results": "200", "hashOfConfig": "172"}, {"size": 6699, "mtime": 1751879532889, "results": "201", "hashOfConfig": "172"}, {"size": 7652, "mtime": 1747670349185, "results": "202", "hashOfConfig": "172"}, {"size": 3454, "mtime": 1751879532894, "results": "203", "hashOfConfig": "172"}, {"size": 10255, "mtime": 1745756318348, "results": "204", "hashOfConfig": "172"}, {"size": 8330, "mtime": 1746358968032, "results": "205", "hashOfConfig": "172"}, {"size": 32773, "mtime": 1748945138663, "results": "206", "hashOfConfig": "172"}, {"size": 2763, "mtime": 1745756355695, "results": "207", "hashOfConfig": "172"}, {"size": 58924, "mtime": 1751894560360, "results": "208", "hashOfConfig": "172"}, {"size": 37697, "mtime": 1751879532852, "results": "209", "hashOfConfig": "172"}, {"size": 4407, "mtime": 1748245074390, "results": "210", "hashOfConfig": "172"}, {"size": 3581, "mtime": 1745756676217, "results": "211", "hashOfConfig": "172"}, {"size": 8253, "mtime": 1738773514530, "results": "212", "hashOfConfig": "172"}, {"size": 17240, "mtime": 1747857773555, "results": "213", "hashOfConfig": "172"}, {"size": 2653, "mtime": 1740045914776, "results": "214", "hashOfConfig": "172"}, {"size": 6326, "mtime": 1751879532830, "results": "215", "hashOfConfig": "172"}, {"size": 3519, "mtime": 1745164340759, "results": "216", "hashOfConfig": "172"}, {"size": 621, "mtime": 1743003823907, "results": "217", "hashOfConfig": "172"}, {"size": 1130, "mtime": 1733762788732, "results": "218", "hashOfConfig": "172"}, {"size": 6324, "mtime": 1750583904200, "results": "219", "hashOfConfig": "172"}, {"size": 1924, "mtime": 1748945138574, "results": "220", "hashOfConfig": "172"}, {"size": 34324, "mtime": 1750583904198, "results": "221", "hashOfConfig": "172"}, {"size": 617, "mtime": 1739441157257, "results": "222", "hashOfConfig": "172"}, {"size": 7588, "mtime": 1745756769083, "results": "223", "hashOfConfig": "172"}, {"size": 7970, "mtime": 1748242361445, "results": "224", "hashOfConfig": "172"}, {"size": 2748, "mtime": 1750583904124, "results": "225", "hashOfConfig": "172"}, {"size": 8215, "mtime": 1750583904127, "results": "226", "hashOfConfig": "172"}, {"size": 18206, "mtime": 1748945138620, "results": "227", "hashOfConfig": "172"}, {"size": 10211, "mtime": 1745756261206, "results": "228", "hashOfConfig": "172"}, {"size": 6499, "mtime": 1750583904124, "results": "229", "hashOfConfig": "172"}, {"size": 6467, "mtime": 1747859196819, "results": "230", "hashOfConfig": "172"}, {"size": 1102, "mtime": 1732809805482, "results": "231", "hashOfConfig": "172"}, {"size": 2471, "mtime": 1745756721769, "results": "232", "hashOfConfig": "172"}, {"size": 432, "mtime": 1732461607834, "results": "233", "hashOfConfig": "172"}, {"size": 599, "mtime": 1744989053201, "results": "234", "hashOfConfig": "172"}, {"size": 2755, "mtime": 1745756670016, "results": "235", "hashOfConfig": "172"}, {"size": 1882, "mtime": 1733701104286, "results": "236", "hashOfConfig": "172"}, {"size": 14991, "mtime": 1750781378174, "results": "237", "hashOfConfig": "172"}, {"size": 4615, "mtime": 1751879532835, "results": "238", "hashOfConfig": "172"}, {"size": 8748, "mtime": 1750781378297, "results": "239", "hashOfConfig": "172"}, {"size": 2826, "mtime": 1739875502029, "results": "240", "hashOfConfig": "172"}, {"size": 18152, "mtime": 1750781378303, "results": "241", "hashOfConfig": "172"}, {"size": 4586, "mtime": 1750583904173, "results": "242", "hashOfConfig": "172"}, {"size": 5047, "mtime": 1750583904138, "results": "243", "hashOfConfig": "172"}, {"size": 1256, "mtime": 1750583904156, "results": "244", "hashOfConfig": "172"}, {"size": 4787, "mtime": 1750583904226, "results": "245", "hashOfConfig": "172"}, {"size": 6585, "mtime": 1750583904136, "results": "246", "hashOfConfig": "172"}, {"size": 2936, "mtime": 1750583904158, "results": "247", "hashOfConfig": "172"}, {"size": 22297, "mtime": 1751879532847, "results": "248", "hashOfConfig": "172"}, {"size": 8486, "mtime": 1748945138665, "results": "249", "hashOfConfig": "172"}, {"size": 14557, "mtime": 1751879532857, "results": "250", "hashOfConfig": "172"}, {"size": 23936, "mtime": 1751879532855, "results": "251", "hashOfConfig": "172"}, {"size": 22650, "mtime": 1750583904221, "results": "252", "hashOfConfig": "172"}, {"size": 19249, "mtime": 1751879532850, "results": "253", "hashOfConfig": "172"}, {"size": 12822, "mtime": 1751879532851, "results": "254", "hashOfConfig": "172"}, {"size": 54388, "mtime": 1751897751782, "results": "255", "hashOfConfig": "172"}, {"size": 10279, "mtime": 1751879532845, "results": "256", "hashOfConfig": "172"}, {"size": 13730, "mtime": 1751879532878, "results": "257", "hashOfConfig": "172"}, {"size": 18173, "mtime": 1751879532856, "results": "258", "hashOfConfig": "172"}, {"size": 4735, "mtime": 1748256693504, "results": "259", "hashOfConfig": "172"}, {"size": 1357, "mtime": 1747558244288, "results": "260", "hashOfConfig": "172"}, {"size": 3480, "mtime": 1747558956120, "results": "261", "hashOfConfig": "172"}, {"size": 5536, "mtime": 1750781378188, "results": "262", "hashOfConfig": "172"}, {"size": 10509, "mtime": 1751879532826, "results": "263", "hashOfConfig": "172"}, {"size": 27853, "mtime": 1751896976302, "results": "264", "hashOfConfig": "172"}, {"size": 3031, "mtime": 1751879532824, "results": "265", "hashOfConfig": "172"}, {"size": 10627, "mtime": 1745756704976, "results": "266", "hashOfConfig": "172"}, {"size": 7928, "mtime": 1751879532829, "results": "267", "hashOfConfig": "172"}, {"size": 5066, "mtime": 1747588012006, "results": "268", "hashOfConfig": "172"}, {"size": 7059, "mtime": 1747588011975, "results": "269", "hashOfConfig": "172"}, {"size": 470, "mtime": 1736953517473, "results": "270", "hashOfConfig": "172"}, {"size": 10205, "mtime": 1750781378262, "results": "271", "hashOfConfig": "172"}, {"size": 407, "mtime": 1736953517423, "results": "272", "hashOfConfig": "172"}, {"size": 3750, "mtime": 1739267845916, "results": "273", "hashOfConfig": "172"}, {"size": 3470, "mtime": 1750583904193, "results": "274", "hashOfConfig": "172"}, {"size": 2473, "mtime": 1747561888096, "results": "275", "hashOfConfig": "172"}, {"size": 10563, "mtime": 1751879532832, "results": "276", "hashOfConfig": "172"}, {"size": 1908, "mtime": 1746287905619, "results": "277", "hashOfConfig": "172"}, {"size": 8604, "mtime": 1750583904196, "results": "278", "hashOfConfig": "172"}, {"size": 8510, "mtime": 1751879532831, "results": "279", "hashOfConfig": "172"}, {"size": 4354, "mtime": 1739441157288, "results": "280", "hashOfConfig": "172"}, {"size": 1867, "mtime": 1739794753830, "results": "281", "hashOfConfig": "172"}, {"size": 9639, "mtime": 1751879532782, "results": "282", "hashOfConfig": "172"}, {"size": 8234, "mtime": 1747857773581, "results": "283", "hashOfConfig": "172"}, {"size": 7349, "mtime": 1748242361523, "results": "284", "hashOfConfig": "172"}, {"size": 927, "mtime": 1742126853937, "results": "285", "hashOfConfig": "172"}, {"size": 3533, "mtime": 1745250026779, "results": "286", "hashOfConfig": "172"}, {"size": 13226, "mtime": 1751879532780, "results": "287", "hashOfConfig": "172"}, {"size": 16297, "mtime": 1751879532779, "results": "288", "hashOfConfig": "172"}, {"size": 10175, "mtime": 1748945138381, "results": "289", "hashOfConfig": "172"}, {"size": 12310, "mtime": 1750583904120, "results": "290", "hashOfConfig": "172"}, {"size": 14974, "mtime": 1751879532777, "results": "291", "hashOfConfig": "172"}, {"size": 6399, "mtime": 1751879532836, "results": "292", "hashOfConfig": "172"}, {"size": 15374, "mtime": 1751879532834, "results": "293", "hashOfConfig": "172"}, {"size": 1016, "mtime": 1747857773597, "results": "294", "hashOfConfig": "172"}, {"size": 4255, "mtime": 1750583904153, "results": "295", "hashOfConfig": "172"}, {"size": 6073, "mtime": 1750583904160, "results": "296", "hashOfConfig": "172"}, {"size": 2807, "mtime": 1750583904129, "results": "297", "hashOfConfig": "172"}, {"size": 5136, "mtime": 1747561392218, "results": "298", "hashOfConfig": "172"}, {"size": 2823, "mtime": 1745927880834, "results": "299", "hashOfConfig": "172"}, {"size": 5861, "mtime": 1750583904241, "results": "300", "hashOfConfig": "172"}, {"size": 9463, "mtime": 1745756695171, "results": "301", "hashOfConfig": "172"}, {"size": 6308, "mtime": 1748242361523, "results": "302", "hashOfConfig": "172"}, {"size": 3821, "mtime": 1745756256387, "results": "303", "hashOfConfig": "172"}, {"size": 4398, "mtime": 1751879532777, "results": "304", "hashOfConfig": "172"}, {"size": 16867, "mtime": 1750583904211, "results": "305", "hashOfConfig": "172"}, {"size": 6183, "mtime": 1745756359150, "results": "306", "hashOfConfig": "172"}, {"size": 7315, "mtime": 1747857773601, "results": "307", "hashOfConfig": "172"}, {"size": 3509, "mtime": 1743957366130, "results": "308", "hashOfConfig": "172"}, {"size": 13152, "mtime": 1745756697925, "results": "309", "hashOfConfig": "172"}, {"size": 309, "mtime": 1744991139097, "results": "310", "hashOfConfig": "172"}, {"size": 5212, "mtime": 1745756717639, "results": "311", "hashOfConfig": "172"}, {"size": 1050, "mtime": 1744991535690, "results": "312", "hashOfConfig": "172"}, {"size": 9414, "mtime": 1750781378443, "results": "313", "hashOfConfig": "172"}, {"size": 1908, "mtime": 1750583904207, "results": "314", "hashOfConfig": "172"}, {"size": 18694, "mtime": 1746531108678, "results": "315", "hashOfConfig": "172"}, {"size": 815, "mtime": 1746022982637, "results": "316", "hashOfConfig": "172"}, {"size": 620, "mtime": 1746022982637, "results": "317", "hashOfConfig": "172"}, {"size": 606, "mtime": 1746022982637, "results": "318", "hashOfConfig": "172"}, {"size": 929, "mtime": 1746022982637, "results": "319", "hashOfConfig": "172"}, {"size": 843, "mtime": 1746022982637, "results": "320", "hashOfConfig": "172"}, {"size": 29923, "mtime": 1750781378374, "results": "321", "hashOfConfig": "172"}, {"size": 5582, "mtime": 1747857773568, "results": "322", "hashOfConfig": "172"}, {"size": 14914, "mtime": 1750781378239, "results": "323", "hashOfConfig": "172"}, {"size": 25908, "mtime": 1747858444177, "results": "324", "hashOfConfig": "172"}, {"size": 27407, "mtime": 1751879532824, "results": "325", "hashOfConfig": "172"}, {"size": 23128, "mtime": 1747588012007, "results": "326", "hashOfConfig": "172"}, {"size": 19317, "mtime": 1751879532828, "results": "327", "hashOfConfig": "172"}, {"size": 1007, "mtime": 1748242361471, "results": "328", "hashOfConfig": "172"}, {"size": 15767, "mtime": 1751879532783, "results": "329", "hashOfConfig": "172"}, {"size": 1049, "mtime": 1748945138622, "results": "330", "hashOfConfig": "172"}, {"size": 9350, "mtime": 1748945138379, "results": "331", "hashOfConfig": "172"}, {"size": 9235, "mtime": 1751879532897, "results": "332", "hashOfConfig": "172"}, {"size": 10193, "mtime": 1751879532844, "results": "333", "hashOfConfig": "172"}, {"size": 9615, "mtime": 1751894666021, "results": "334", "hashOfConfig": "172"}, {"size": 8245, "mtime": 1750781378262, "results": "335", "hashOfConfig": "172"}, {"size": 10271, "mtime": 1751879532785, "results": "336", "hashOfConfig": "172"}, {"size": 9025, "mtime": 1750781378244, "results": "337", "hashOfConfig": "172"}, {"size": 32598, "mtime": 1751879532822, "results": "338", "hashOfConfig": "172"}, {"size": 1907, "mtime": 1750781378327, "results": "339", "hashOfConfig": "172"}, {"size": 1116, "mtime": 1750583904205, "results": "340", "hashOfConfig": "172"}, {"size": 1178, "mtime": 1750781378343, "results": "341", "hashOfConfig": "172"}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zm27oo", {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\TDGweb\\TDG\\thedesigngrit\\src\\index.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\reportWebVitals.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\App.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\userContext.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\vendorContext.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Context\\cartcontext.js", ["852"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Context\\scrollToTop.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\JobDescription.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\loadingScreen.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Policy.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\subcategories.jsx", ["853", "854"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Partners.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\types.jsx", ["855"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\userss.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ReadyToship.jsx", ["856"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ProductsPage.jsx", ["857"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\myAccount.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ProductPage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\FAQs.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\TrackOrder.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\careers.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Checkout.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ContactUs.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\ShoppingCart.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\VendorProfile.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\login.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\aboutUs.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\Vendorspage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\signup.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\home.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\vendorSide\\VendorHome.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\vendorSide\\AdminHome.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\addbrand.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\notificationPage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\SignupVendor.jsx", ["858"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\editEmployee.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\UpdateProduct.jsx", ["859", "860", "861"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\orderDetails.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\signinVendor.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\VerifyPartners.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\partnerApplication.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Footer.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Topheader.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\brandCursol.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\reviewBox.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\toast.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\rating.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\relatedProducts.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\invoiceOrderCustomer.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\navBar.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\paymentsIcons.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\JobDesc\\jobForm.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\account\\profile.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\Productsgrid.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\filters.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\Shipping.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\resetPassowrd.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\TopFilters.jsx", ["862"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\billingInfo.jsx", ["863"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\JobDesc\\applicationSentPopUp.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\account\\wishlist.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\About\\heroAbout.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Context\\scrollingAnimation.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\careers\\jobBorder.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\careers\\valueCards.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Checkout.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\product\\optionPopUp.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\signInForm.jsx", ["864"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\About\\ourTeam.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\signUpForm.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\profileheader.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Categories.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Hero.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\navbarVendor.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Catalogs.jsx", ["865"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Products.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\DashboardVendor.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\orderListVendor.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\sideBarVendor.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\productsPageVendor.jsx", ["866", "867", "868"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\brandingPage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\brandSignup.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\employeePage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\postProduct.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Addemployee.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\viewInStoreVendor.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\quotationsList.jsx", ["869"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\VendorsGrid.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\TopButtons.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\Filters.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminNav.jsx", ["870", "871"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\dashboardAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\ProductsAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminSideBar.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\createCategory.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\tags.jsx", ["872"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\categoriesList.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\Requests.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\VendorLayout.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\orderListAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminLayout.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\invoice.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\Sustainability.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\partners.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\bestSeller.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\Category.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\home\\concept.jsx", ["873", "874"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\favoriteOverlay.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopUp.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\confirmationMsg.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Products\\productcard.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\forgetPassword.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\BillingInfoPop.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\greeting.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\menuhover\\Menudrop.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Popups\\CartOverlay.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\ordersummary.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Billingform.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Shippingform.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\Paymentmethod.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\product\\viewInStore.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\product\\RequestInfo.jsx", ["875"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\successfullyRegistered.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\CategoryCard.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\Productscard.jsx", ["876", "877"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Vendor-Profile\\CatalogCard.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\VendorsPage\\Vendorcard.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\notificationOverlay.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\promotionProduct.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\editcategories.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\EditProfile.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\profilePopup\\ordersPopup.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\billingSummary.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\PromotionPage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\EditPromotionModal.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\CreatePromotionDialog.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\PosPage.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\concepts.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\PrivateRouteAdmin.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\AdminLogin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\utils\\adminContext.js", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Pages\\onSale.jsx", ["878"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\orderSubmit.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting.jsx", ["879", "880", "881", "882", "883", "884", "885"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\Chart.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\PayoutHistoryTable.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\RefundTable.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\PaymentMethod.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\Accounting\\DateRangePicker.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\VariantDialog.jsx", ["886", "887", "888", "889", "890", "891"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\reviewPopup.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\allEmployees.jsx", ["892"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\brandsAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\adminNotifications.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\promotionsAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\orderDetailsAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\contactUsSuccess.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\TrackQuotation.jsx", ["893", "894"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\quotationDealSuccess.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\Checkout\\AddressSelectionPopup.jsx", ["895"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\services\\paymobService.js", ["896"], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\trackViewInStore.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\PendingProductUpdates.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\ourEmployees.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\PendingBrandUpdates.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\contactusRequests.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\adminSide\\accountingAdmin.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\orderFailed.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\successMsgs\\accountExists.jsx", [], [], "D:\\TDGweb\\TDG\\thedesigngrit\\src\\Components\\vendorSide\\ProfileCardVendor.jsx", [], [], {"ruleId": "897", "severity": 1, "message": "898", "line": 62, "column": 6, "nodeType": "899", "endLine": 62, "endColumn": 17, "suggestions": "900"}, {"ruleId": "901", "severity": 1, "message": "902", "line": 26, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 26, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "905", "line": 120, "column": 21, "nodeType": "903", "messageId": "904", "endLine": 120, "endColumn": 39}, {"ruleId": "901", "severity": 1, "message": "905", "line": 213, "column": 21, "nodeType": "903", "messageId": "904", "endLine": 213, "endColumn": 39}, {"ruleId": "901", "severity": 1, "message": "902", "line": 29, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 29, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "902", "line": 31, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 31, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "906", "line": 179, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 179, "endColumn": 28}, {"ruleId": "901", "severity": 1, "message": "907", "line": 46, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 46, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "908", "line": 50, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 50, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "909", "line": 51, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 51, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "910", "line": 1, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 25}, {"ruleId": "897", "severity": 1, "message": "911", "line": 40, "column": 6, "nodeType": "899", "endLine": 40, "endColumn": 14, "suggestions": "912"}, {"ruleId": "901", "severity": 1, "message": "913", "line": 2, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "914", "line": 14, "column": 24, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 39}, {"ruleId": "901", "severity": 1, "message": "915", "line": 29, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 29, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "916", "line": 34, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 18}, {"ruleId": "901", "severity": 1, "message": "917", "line": 165, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 165, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "918", "line": 45, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 45, "endColumn": 28}, {"ruleId": "901", "severity": 1, "message": "910", "line": 1, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "919", "line": 1, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 36}, {"ruleId": "901", "severity": 1, "message": "920", "line": 30, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 30, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "921", "line": 7, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 7, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "922", "line": 12, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "923", "line": 8, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "924", "line": 1, "column": 38, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 44}, {"ruleId": "901", "severity": 1, "message": "925", "line": 11, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 24}, {"ruleId": "901", "severity": 1, "message": "902", "line": 28, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 28, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "926", "line": 32, "column": 25, "nodeType": "903", "messageId": "904", "endLine": 32, "endColumn": 41}, {"ruleId": "901", "severity": 1, "message": "927", "line": 33, "column": 25, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 41}, {"ruleId": "901", "severity": 1, "message": "928", "line": 34, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 24}, {"ruleId": "901", "severity": 1, "message": "929", "line": 34, "column": 26, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 43}, {"ruleId": "901", "severity": 1, "message": "930", "line": 39, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 39, "endColumn": 17}, {"ruleId": "897", "severity": 1, "message": "931", "line": 133, "column": 6, "nodeType": "899", "endLine": 133, "endColumn": 8, "suggestions": "932"}, {"ruleId": "901", "severity": 1, "message": "933", "line": 173, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 173, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "934", "line": 20, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 20, "endColumn": 24}, {"ruleId": "897", "severity": 1, "message": "935", "line": 73, "column": 6, "nodeType": "899", "endLine": 73, "endColumn": 12, "suggestions": "936"}, {"ruleId": "897", "severity": 1, "message": "937", "line": 152, "column": 6, "nodeType": "899", "endLine": 152, "endColumn": 53, "suggestions": "938"}, {"ruleId": "897", "severity": 1, "message": "939", "line": 152, "column": 23, "nodeType": "940", "endLine": 152, "endColumn": 52}, {"ruleId": "897", "severity": 1, "message": "941", "line": 159, "column": 6, "nodeType": "899", "endLine": 159, "endColumn": 17, "suggestions": "942"}, {"ruleId": "901", "severity": 1, "message": "943", "line": 260, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 260, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "944", "line": 97, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 97, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "945", "line": 11, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "946", "line": 22, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 22, "endColumn": 20}, {"ruleId": "897", "severity": 1, "message": "947", "line": 69, "column": 6, "nodeType": "899", "endLine": 69, "endColumn": 28, "suggestions": "948"}, {"ruleId": "901", "severity": 1, "message": "949", "line": 68, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 68, "endColumn": 14}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'cartTimeoutId', 'userSession.email', 'userSession.firstName', and 'userSession.lastName'. Either include them or remove the dependency array.", "ArrayExpression", ["950"], "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "'displayDescription' is assigned a value but never used.", "'newRequirements' is assigned a value but never used.", "'brandName' is assigned a value but never used.", "'selectedCategory' is assigned a value but never used.", "'selectedSubCategory' is assigned a value but never used.", "'useState' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCards'. Either include it or remove the dependency array.", ["951"], "'FaFacebook' is defined but never used.", "'setCurrentIndex' is assigned a value but never used.", "'error' is assigned a value but never used.", "'variants' is assigned a value but never used.", "'success' is assigned a value but never used.", "'handleVendorConfirm' is assigned a value but never used.", "'useEffect' is defined but never used.", "'selectedTag' is assigned a value but never used.", "'CardContent' is defined but never used.", "'ShoppingBagIcon' is defined but never used.", "'Close' is defined but never used.", "'useRef' is defined but never used.", "'swiperInstance' is assigned a value but never used.", "'setPayoutHistory' is assigned a value but never used.", "'setRefundRecords' is assigned a value but never used.", "'paymentMethods' is assigned a value but never used.", "'setPaymentMethods' is assigned a value but never used.", "'summary' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'vendor.brandId'. Either include it or remove the dependency array.", ["952"], "'formatDataForDisplay' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSkus'. Either include it or remove the dependency array.", ["953"], "React Hook useEffect has a missing dependency: 'variants'. Either include it or remove the dependency array.", ["954"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "ChainExpression", "React Hook useEffect has a missing dependency: 'fetchProductDetails'. Either include it or remove the dependency array.", ["955"], "'handleArrayChange' is assigned a value but never used.", "'response' is assigned a value but never used.", "'paymobService' is defined but never used.", "'payLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAddresses'. Either include it or remove the dependency array.", ["956"], "'total' is assigned a value but never used.", {"desc": "957", "fix": "958"}, {"desc": "959", "fix": "960"}, {"desc": "961", "fix": "962"}, {"desc": "963", "fix": "964"}, {"desc": "965", "fix": "966"}, {"desc": "967", "fix": "968"}, {"desc": "969", "fix": "970"}, "Update the dependencies array to be: [cartItems, cartTimeoutId, userSession.email, userSession.firstName, userSession.lastName]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [fetchCards, userId]", {"range": "973", "text": "974"}, "Update the dependencies array to be: [vendor.brandId]", {"range": "975", "text": "976"}, "Update the dependencies array to be: [fetchSkus, open]", {"range": "977", "text": "978"}, "Update the dependencies array to be: [currentVariant, variants]", {"range": "979", "text": "980"}, "Update the dependencies array to be: [fetchProductDetails, productId]", {"range": "981", "text": "982"}, "Update the dependencies array to be: [fetchAddresses, open, userSession.id]", {"range": "983", "text": "984"}, [2227, 2238], "[cartItems, cartTimeoutId, userSession.email, userSession.firstName, userSession.lastName]", [1525, 1533], "[fetchCards, userId]", [4352, 4354], "[vendor.brandId]", [2005, 2011], "[fetchSkus, open]", [4431, 4478], "[currentVariant, variants]", [4648, 4659], "[fetchProductDetails, productId]", [1997, 2019], "[fetchAddresses, open, userSession.id]"]