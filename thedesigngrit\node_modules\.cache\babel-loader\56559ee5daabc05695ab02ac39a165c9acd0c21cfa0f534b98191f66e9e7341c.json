{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\vendorSide\\\\brandSignup.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useVendor } from \"../../utils/vendorContext\";\nimport { Box, Select, MenuItem, Chip, TextField, Button, Typography, Paper, Grid, Container, CircularProgress, Alert } from \"@mui/material\";\nimport axios from \"axios\";\nimport { FaInstagram, FaFacebook, FaLinkedin, FaTiktok, FaGlobe, FaEdit, FaSave, FaTimes } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BrandSignup = () => {\n  _s();\n  const {\n    vendor\n  } = useVendor();\n  const [formData, setFormData] = useState({});\n  const [isEditing, setIsEditing] = useState(false);\n  const [originalData, setOriginalData] = useState({});\n  const [types, setTypes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [saveLoading, setSaveLoading] = useState(false);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n  const [saveError, setSaveError] = useState(null);\n  const platformIcons = {\n    instagramURL: /*#__PURE__*/_jsxDEV(FaInstagram, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 19\n    }, this),\n    facebookURL: /*#__PURE__*/_jsxDEV(FaFacebook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 18\n    }, this),\n    linkedinURL: /*#__PURE__*/_jsxDEV(FaLinkedin, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 18\n    }, this),\n    tiktokURL: /*#__PURE__*/_jsxDEV(FaTiktok, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 16\n    }, this),\n    websiteURL: /*#__PURE__*/_jsxDEV(FaGlobe, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }, this)\n  };\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        await fetchTypes();\n        if (vendor !== null && vendor !== void 0 && vendor.brandId) {\n          await fetchBrandData(vendor.brandId);\n        }\n      } catch (err) {\n        console.error(\"Error loading data:\", err);\n        setError(err.message || \"Failed to load data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [vendor]);\n  const fetchTypes = async () => {\n    try {\n      const {\n        data\n      } = await axios.get(\"https://api.thedesigngrit.com/api/types/getAll\");\n      setTypes(data || []);\n    } catch (error) {\n      console.error(\"Error fetching types:\", error);\n      throw error;\n    }\n  };\n  const fetchBrandData = async brandId => {\n    try {\n      const response = await axios.get(`https://api.thedesigngrit.com/api/brand/${brandId}`);\n      setFormData(response.data);\n      setOriginalData(response.data);\n    } catch (error) {\n      console.error(\"Error fetching brand data:\", error);\n      throw error;\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleTypeChange = e => {\n    const {\n      value\n    } = e.target;\n    // Limit to maximum 3 types\n    if (value.length <= 3) {\n      setFormData(prev => ({\n        ...prev,\n        selectedTypes: value\n      }));\n    }\n  };\n  const handleEdit = () => {\n    var _formData$types;\n    // Create a selectedTypes array from the types objects\n    const selectedTypeIds = ((_formData$types = formData.types) === null || _formData$types === void 0 ? void 0 : _formData$types.map(type => type._id)) || [];\n    setFormData(prev => ({\n      ...prev,\n      selectedTypes: selectedTypeIds\n    }));\n    setIsEditing(true);\n  };\n  const handleCancel = () => {\n    setFormData(originalData);\n    setIsEditing(false);\n    setSaveError(null);\n    setSaveSuccess(false);\n  };\n  const handleSave = async () => {\n    try {\n      setSaveLoading(true);\n      setSaveError(null);\n      setSaveSuccess(false);\n      const dataToSend = new FormData();\n\n      // Add all regular fields\n      Object.keys(formData).forEach(key => {\n        if (key !== \"types\" && key !== \"selectedTypes\" && key !== \"_id\" && key !== \"createdAt\" && key !== \"updatedAt\" && key !== \"__v\" && key !== \"brandlogo\" && key !== \"coverPhoto\" && key !== \"digitalCopiesLogo\" && key !== \"catalogues\" && key !== \"documents\" && formData[key] !== null && formData[key] !== undefined) {\n          dataToSend.append(key, formData[key]);\n        }\n      });\n\n      // Add selected types\n      if (formData.selectedTypes && Array.isArray(formData.selectedTypes)) {\n        formData.selectedTypes.forEach(typeId => {\n          if (typeId) dataToSend.append(\"types\", typeId);\n        });\n      }\n      const response = await axios.put(`https://api.thedesigngrit.com/api/brand/${vendor.brandId}`, dataToSend, {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\"\n        }\n      });\n\n      // Update the data with the response\n      setFormData(response.data);\n      setOriginalData(response.data);\n      setSaveSuccess(true);\n      setIsEditing(false);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error updating brand data:\", error);\n      setSaveError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Failed to update brand\");\n    } finally {\n      setSaveLoading(false);\n    }\n  };\n  const getStatusStyle = status => {\n    switch (status) {\n      case \"pending\":\n        return {\n          backgroundColor: \"#FAD5A5\",\n          color: \"#FF5F1F\"\n        };\n      case \"active\":\n        return {\n          backgroundColor: \"#def9bf\",\n          color: \"#6b7b58\"\n        };\n      case \"deactivated\":\n        return {\n          backgroundColor: \"#ffcccc\",\n          color: \"#cc0000\"\n        };\n      default:\n        return {\n          backgroundColor: \"#e0e0e0\",\n          color: \"#666666\"\n        };\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        p: 5\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            gutterBottom: true,\n            children: formData.brandName || \"Brand Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"inline-block\",\n              px: 2,\n              py: 0.5,\n              borderRadius: 1,\n              ...getStatusStyle(formData.status)\n            },\n            children: formData.status ? `${formData.status.charAt(0).toUpperCase()}${formData.status.slice(1)}` : \"Status not set\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: \"center\"\n          },\n          children: formData.brandlogo ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${formData.brandlogo}`,\n            alt: \"Brand Logo\",\n            style: {\n              width: \"120px\",\n              height: \"120px\",\n              objectFit: \"contain\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 120,\n              height: 120,\n              bgcolor: \"#f0f0f0\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"No logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), saveSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 3\n        },\n        children: \"Brand information updated successfully Waiting for approvals from the admin!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), saveError && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: saveError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            sx: {\n              borderBottom: \"1px solid #eee\",\n              pb: 1\n            },\n            children: \"Basic Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [\"brandName\", \"commercialRegisterNo\", \"taxNumber\", \"companyAddress\", \"phoneNumber\", \"email\", \"bankAccountNumber\"].map(field => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: field.replace(/([A-Z])/g, \" $1\").replace(/^./, str => str.toUpperCase())\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  name: field,\n                  value: formData[field] || \"\",\n                  onChange: handleInputChange,\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formData[field] || \"Not provided\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this)\n            }, field, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            sx: {\n              borderBottom: \"1px solid #eee\",\n              pb: 1\n            },\n            children: \"Online Presence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [\"websiteURL\", \"instagramURL\", \"facebookURL\", \"tiktokURL\", \"linkedinURL\"].map(field => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: field.replace(/URL$/, \"\").replace(/([A-Z])/g, \" $1\").replace(/^./, str => str.toUpperCase())\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  name: field,\n                  value: formData[field] || \"\",\n                  onChange: handleInputChange,\n                  size: \"small\",\n                  variant: \"outlined\",\n                  InputProps: {\n                    startAdornment: platformIcons[field]\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formData[field] ? /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: formData[field].startsWith(\"http\") ? formData[field] : `https://${formData[field]}`,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      color: \"#1976d2\",\n                      textDecoration: \"none\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mr: 1\n                      },\n                      children: platformIcons[field]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 29\n                    }, this), formData[field]]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 27\n                  }, this) : \"Not provided\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)\n            }, field, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            sx: {\n              borderBottom: \"1px solid #eee\",\n              pb: 1,\n              mt: 2\n            },\n            children: \"Additional Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Shipping Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  name: \"shippingPolicy\",\n                  value: formData.shippingPolicy || \"\",\n                  onChange: handleInputChange,\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formData.shippingPolicy || \"Not provided\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Brand Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  multiline: true,\n                  rows: 3,\n                  name: \"brandDescription\",\n                  value: formData.brandDescription || \"\",\n                  onChange: handleInputChange,\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formData.brandDescription || \"Not provided\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), isEditing ? /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  name: \"fees\",\n                  type: \"number\",\n                  value: formData.fees || \"\",\n                  onChange: handleInputChange,\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: formData.fees || \"Not provided\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this), isEditing ? /*#__PURE__*/_jsxDEV(Select, {\n                  multiple: true,\n                  fullWidth: true,\n                  size: \"small\",\n                  value: formData.selectedTypes || [],\n                  onChange: handleTypeChange,\n                  renderValue: selected => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: \"flex\",\n                      flexWrap: \"wrap\",\n                      gap: 0.5\n                    },\n                    children: selected.map(value => {\n                      const type = types.find(t => t._id === value);\n                      return /*#__PURE__*/_jsxDEV(Chip, {\n                        label: type ? type.name : value,\n                        size: \"small\"\n                      }, value, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this),\n                  children: types.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: type._id,\n                    children: type.name\n                  }, type._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: \"flex\",\n                    flexWrap: \"wrap\",\n                    gap: 0.5\n                  },\n                  children: formData.types && formData.types.length > 0 ? formData.types.map((type, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: type.name,\n                    size: \"small\",\n                    sx: {\n                      bgcolor: \"#f0f0f0\"\n                    }\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 27\n                  }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: \"No types selected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4,\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          gap: 2\n        },\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            onClick: handleCancel,\n            startIcon: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 28\n            }, this),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSave,\n            disabled: saveLoading,\n            startIcon: saveLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 66\n            }, this),\n            children: saveLoading ? \"Saving...\" : \"Save Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleEdit,\n          startIcon: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 26\n          }, this),\n          children: \"Edit Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(BrandSignup, \"vqPcF1g6fjJOvF3oB9rvU9SxsK0=\", false, function () {\n  return [useVendor];\n});\n_c = BrandSignup;\nexport default BrandSignup;\nvar _c;\n$RefreshReg$(_c, \"BrandSignup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useVendor", "Box", "Select", "MenuItem", "Chip", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "Grid", "Container", "CircularProgress", "<PERSON><PERSON>", "axios", "FaInstagram", "FaFacebook", "FaLinkedin", "FaTiktok", "FaGlobe", "FaEdit", "FaSave", "FaTimes", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BrandSignup", "_s", "vendor", "formData", "setFormData", "isEditing", "setIsEditing", "originalData", "setOriginalData", "types", "setTypes", "loading", "setLoading", "error", "setError", "saveLoading", "setSaveLoading", "saveSuccess", "setSaveSuccess", "saveError", "setSaveError", "platformIcons", "instagramURL", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "facebookURL", "linkedinURL", "tiktokURL", "websiteURL", "fetchData", "fetchTypes", "brandId", "fetchBrandData", "err", "console", "message", "data", "get", "response", "handleInputChange", "e", "name", "value", "target", "prev", "handleTypeChange", "length", "selectedTypes", "handleEdit", "_formData$types", "selectedTypeIds", "map", "type", "_id", "handleCancel", "handleSave", "dataToSend", "FormData", "Object", "keys", "for<PERSON>ach", "key", "undefined", "append", "Array", "isArray", "typeId", "put", "headers", "_error$response", "_error$response$data", "getStatusStyle", "status", "backgroundColor", "color", "sx", "display", "justifyContent", "p", "children", "severity", "max<PERSON><PERSON><PERSON>", "py", "elevation", "mb", "alignItems", "variant", "component", "gutterBottom", "brandName", "px", "borderRadius", "char<PERSON>t", "toUpperCase", "slice", "textAlign", "brandlogo", "src", "alt", "style", "width", "height", "objectFit", "bgcolor", "container", "spacing", "item", "xs", "md", "borderBottom", "pb", "field", "replace", "str", "fullWidth", "onChange", "size", "InputProps", "startAdornment", "href", "startsWith", "rel", "textDecoration", "mr", "mt", "shippingPolicy", "multiline", "rows", "brandDescription", "fees", "multiple", "renderValue", "selected", "flexWrap", "gap", "find", "t", "label", "index", "onClick", "startIcon", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/vendorSide/brandSignup.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { useVendor } from \"../../utils/vendorContext\";\r\nimport {\r\n  Box,\r\n  Select,\r\n  MenuItem,\r\n  Chip,\r\n  TextField,\r\n  Button,\r\n  Typography,\r\n  Paper,\r\n  Grid,\r\n  Container,\r\n  CircularProgress,\r\n  Alert,\r\n} from \"@mui/material\";\r\nimport axios from \"axios\";\r\nimport {\r\n  FaInstagram,\r\n  FaFacebook,\r\n  FaLinkedin,\r\n  FaTiktok,\r\n  FaGlobe,\r\n  FaEdit,\r\n  FaSave,\r\n  FaTimes,\r\n} from \"react-icons/fa\";\r\n\r\nconst BrandSignup = () => {\r\n  const { vendor } = useVendor();\r\n  const [formData, setFormData] = useState({});\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [originalData, setOriginalData] = useState({});\r\n  const [types, setTypes] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [saveLoading, setSaveLoading] = useState(false);\r\n  const [saveSuccess, setSaveSuccess] = useState(false);\r\n  const [saveError, setSaveError] = useState(null);\r\n\r\n  const platformIcons = {\r\n    instagramURL: <FaInstagram />,\r\n    facebookURL: <FaFacebook />,\r\n    linkedinURL: <FaLinkedin />,\r\n    tiktokURL: <FaTiktok />,\r\n    websiteURL: <FaGlobe />,\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        await fetchTypes();\r\n        if (vendor?.brandId) {\r\n          await fetchBrandData(vendor.brandId);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error loading data:\", err);\r\n        setError(err.message || \"Failed to load data\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [vendor]);\r\n\r\n  const fetchTypes = async () => {\r\n    try {\r\n      const { data } = await axios.get(\r\n        \"https://api.thedesigngrit.com/api/types/getAll\"\r\n      );\r\n      setTypes(data || []);\r\n    } catch (error) {\r\n      console.error(\"Error fetching types:\", error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const fetchBrandData = async (brandId) => {\r\n    try {\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/brand/${brandId}`\r\n      );\r\n      setFormData(response.data);\r\n      setOriginalData(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching brand data:\", error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleTypeChange = (e) => {\r\n    const { value } = e.target;\r\n    // Limit to maximum 3 types\r\n    if (value.length <= 3) {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        selectedTypes: value,\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleEdit = () => {\r\n    // Create a selectedTypes array from the types objects\r\n    const selectedTypeIds = formData.types?.map((type) => type._id) || [];\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      selectedTypes: selectedTypeIds,\r\n    }));\r\n    setIsEditing(true);\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setFormData(originalData);\r\n    setIsEditing(false);\r\n    setSaveError(null);\r\n    setSaveSuccess(false);\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    try {\r\n      setSaveLoading(true);\r\n      setSaveError(null);\r\n      setSaveSuccess(false);\r\n\r\n      const dataToSend = new FormData();\r\n\r\n      // Add all regular fields\r\n      Object.keys(formData).forEach((key) => {\r\n        if (\r\n          key !== \"types\" &&\r\n          key !== \"selectedTypes\" &&\r\n          key !== \"_id\" &&\r\n          key !== \"createdAt\" &&\r\n          key !== \"updatedAt\" &&\r\n          key !== \"__v\" &&\r\n          key !== \"brandlogo\" &&\r\n          key !== \"coverPhoto\" &&\r\n          key !== \"digitalCopiesLogo\" &&\r\n          key !== \"catalogues\" &&\r\n          key !== \"documents\" &&\r\n          formData[key] !== null &&\r\n          formData[key] !== undefined\r\n        ) {\r\n          dataToSend.append(key, formData[key]);\r\n        }\r\n      });\r\n\r\n      // Add selected types\r\n      if (formData.selectedTypes && Array.isArray(formData.selectedTypes)) {\r\n        formData.selectedTypes.forEach((typeId) => {\r\n          if (typeId) dataToSend.append(\"types\", typeId);\r\n        });\r\n      }\r\n\r\n      const response = await axios.put(\r\n        `https://api.thedesigngrit.com/api/brand/${vendor.brandId}`,\r\n        dataToSend,\r\n        { headers: { \"Content-Type\": \"multipart/form-data\" } }\r\n      );\r\n\r\n      // Update the data with the response\r\n      setFormData(response.data);\r\n      setOriginalData(response.data);\r\n      setSaveSuccess(true);\r\n      setIsEditing(false);\r\n    } catch (error) {\r\n      console.error(\"Error updating brand data:\", error);\r\n      setSaveError(error.response?.data?.message || \"Failed to update brand\");\r\n    } finally {\r\n      setSaveLoading(false);\r\n    }\r\n  };\r\n\r\n  const getStatusStyle = (status) => {\r\n    switch (status) {\r\n      case \"pending\":\r\n        return { backgroundColor: \"#FAD5A5\", color: \"#FF5F1F\" };\r\n      case \"active\":\r\n        return { backgroundColor: \"#def9bf\", color: \"#6b7b58\" };\r\n      case \"deactivated\":\r\n        return { backgroundColor: \"#ffcccc\", color: \"#cc0000\" };\r\n      default:\r\n        return { backgroundColor: \"#e0e0e0\", color: \"#666666\" };\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Box sx={{ display: \"flex\", justifyContent: \"center\", p: 5 }}>\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Box sx={{ p: 3 }}>\r\n        <Alert severity=\"error\">{error}</Alert>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>\r\n        {/* Header with status and brand info */}\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n            mb: 3,\r\n          }}\r\n        >\r\n          <Box>\r\n            <Typography variant=\"h4\" component=\"h1\" gutterBottom>\r\n              {formData.brandName || \"Brand Information\"}\r\n            </Typography>\r\n\r\n            <Box\r\n              sx={{\r\n                display: \"inline-block\",\r\n                px: 2,\r\n                py: 0.5,\r\n                borderRadius: 1,\r\n                ...getStatusStyle(formData.status),\r\n              }}\r\n            >\r\n              {formData.status\r\n                ? `${formData.status\r\n                    .charAt(0)\r\n                    .toUpperCase()}${formData.status.slice(1)}`\r\n                : \"Status not set\"}\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box sx={{ textAlign: \"center\" }}>\r\n            {formData.brandlogo ? (\r\n              <img\r\n                src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${formData.brandlogo}`}\r\n                alt=\"Brand Logo\"\r\n                style={{\r\n                  width: \"120px\",\r\n                  height: \"120px\",\r\n                  objectFit: \"contain\",\r\n                }}\r\n              />\r\n            ) : (\r\n              <Box\r\n                sx={{\r\n                  width: 120,\r\n                  height: 120,\r\n                  bgcolor: \"#f0f0f0\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                }}\r\n              >\r\n                <Typography variant=\"body2\" color=\"text.secondary\">\r\n                  No logo\r\n                </Typography>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        </Box>\r\n\r\n        {saveSuccess && (\r\n          <Alert severity=\"success\" sx={{ mb: 3 }}>\r\n            Brand information updated successfully Waiting for approvals from\r\n            the admin!\r\n          </Alert>\r\n        )}\r\n\r\n        {saveError && (\r\n          <Alert severity=\"error\" sx={{ mb: 3 }}>\r\n            {saveError}\r\n          </Alert>\r\n        )}\r\n\r\n        {/* Brand Information */}\r\n        <Grid container spacing={3}>\r\n          {/* Basic Information */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography\r\n              variant=\"h6\"\r\n              gutterBottom\r\n              sx={{ borderBottom: \"1px solid #eee\", pb: 1 }}\r\n            >\r\n              Basic Information\r\n            </Typography>\r\n\r\n            <Grid container spacing={2}>\r\n              {[\r\n                \"brandName\",\r\n                \"commercialRegisterNo\",\r\n                \"taxNumber\",\r\n                \"companyAddress\",\r\n                \"phoneNumber\",\r\n                \"email\",\r\n                \"bankAccountNumber\",\r\n              ].map((field) => (\r\n                <Grid item xs={12} key={field}>\r\n                  <Box sx={{ mb: 2 }}>\r\n                    <Typography\r\n                      variant=\"subtitle2\"\r\n                      color=\"text.secondary\"\r\n                      gutterBottom\r\n                    >\r\n                      {field\r\n                        .replace(/([A-Z])/g, \" $1\")\r\n                        .replace(/^./, (str) => str.toUpperCase())}\r\n                    </Typography>\r\n\r\n                    {isEditing ? (\r\n                      <TextField\r\n                        fullWidth\r\n                        name={field}\r\n                        value={formData[field] || \"\"}\r\n                        onChange={handleInputChange}\r\n                        size=\"small\"\r\n                        variant=\"outlined\"\r\n                      />\r\n                    ) : (\r\n                      <Typography variant=\"body1\">\r\n                        {formData[field] || \"Not provided\"}\r\n                      </Typography>\r\n                    )}\r\n                  </Box>\r\n                </Grid>\r\n              ))}\r\n            </Grid>\r\n          </Grid>\r\n\r\n          {/* Online Presence */}\r\n          <Grid item xs={12} md={6}>\r\n            <Typography\r\n              variant=\"h6\"\r\n              gutterBottom\r\n              sx={{ borderBottom: \"1px solid #eee\", pb: 1 }}\r\n            >\r\n              Online Presence\r\n            </Typography>\r\n\r\n            <Grid container spacing={2}>\r\n              {[\r\n                \"websiteURL\",\r\n                \"instagramURL\",\r\n                \"facebookURL\",\r\n                \"tiktokURL\",\r\n                \"linkedinURL\",\r\n              ].map((field) => (\r\n                <Grid item xs={12} key={field}>\r\n                  <Box sx={{ mb: 2 }}>\r\n                    <Typography\r\n                      variant=\"subtitle2\"\r\n                      color=\"text.secondary\"\r\n                      gutterBottom\r\n                    >\r\n                      {field\r\n                        .replace(/URL$/, \"\")\r\n                        .replace(/([A-Z])/g, \" $1\")\r\n                        .replace(/^./, (str) => str.toUpperCase())}\r\n                    </Typography>\r\n\r\n                    {isEditing ? (\r\n                      <TextField\r\n                        fullWidth\r\n                        name={field}\r\n                        value={formData[field] || \"\"}\r\n                        onChange={handleInputChange}\r\n                        size=\"small\"\r\n                        variant=\"outlined\"\r\n                        InputProps={{\r\n                          startAdornment: platformIcons[field],\r\n                        }}\r\n                      />\r\n                    ) : (\r\n                      <Typography variant=\"body1\">\r\n                        {formData[field] ? (\r\n                          <a\r\n                            href={\r\n                              formData[field].startsWith(\"http\")\r\n                                ? formData[field]\r\n                                : `https://${formData[field]}`\r\n                            }\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            style={{\r\n                              display: \"flex\",\r\n                              alignItems: \"center\",\r\n                              color: \"#1976d2\",\r\n                              textDecoration: \"none\",\r\n                            }}\r\n                          >\r\n                            <Box sx={{ mr: 1 }}>{platformIcons[field]}</Box>\r\n                            {formData[field]}\r\n                          </a>\r\n                        ) : (\r\n                          \"Not provided\"\r\n                        )}\r\n                      </Typography>\r\n                    )}\r\n                  </Box>\r\n                </Grid>\r\n              ))}\r\n            </Grid>\r\n          </Grid>\r\n\r\n          {/* Additional Information */}\r\n          <Grid item xs={12}>\r\n            <Typography\r\n              variant=\"h6\"\r\n              gutterBottom\r\n              sx={{ borderBottom: \"1px solid #eee\", pb: 1, mt: 2 }}\r\n            >\r\n              Additional Information\r\n            </Typography>\r\n\r\n            <Grid container spacing={3}>\r\n              <Grid item xs={12} md={6}>\r\n                <Box sx={{ mb: 2 }}>\r\n                  <Typography\r\n                    variant=\"subtitle2\"\r\n                    color=\"text.secondary\"\r\n                    gutterBottom\r\n                  >\r\n                    Shipping Policy\r\n                  </Typography>\r\n\r\n                  {isEditing ? (\r\n                    <TextField\r\n                      fullWidth\r\n                      name=\"shippingPolicy\"\r\n                      value={formData.shippingPolicy || \"\"}\r\n                      onChange={handleInputChange}\r\n                      size=\"small\"\r\n                      variant=\"outlined\"\r\n                    />\r\n                  ) : (\r\n                    <Typography variant=\"body1\">\r\n                      {formData.shippingPolicy || \"Not provided\"}\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6}>\r\n                <Box sx={{ mb: 2 }}>\r\n                  <Typography\r\n                    variant=\"subtitle2\"\r\n                    color=\"text.secondary\"\r\n                    gutterBottom\r\n                  >\r\n                    Brand Description\r\n                  </Typography>\r\n\r\n                  {isEditing ? (\r\n                    <TextField\r\n                      fullWidth\r\n                      multiline\r\n                      rows={3}\r\n                      name=\"brandDescription\"\r\n                      value={formData.brandDescription || \"\"}\r\n                      onChange={handleInputChange}\r\n                      size=\"small\"\r\n                      variant=\"outlined\"\r\n                    />\r\n                  ) : (\r\n                    <Typography variant=\"body1\">\r\n                      {formData.brandDescription || \"Not provided\"}\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6}>\r\n                <Box sx={{ mb: 2 }}>\r\n                  <Typography\r\n                    variant=\"subtitle2\"\r\n                    color=\"text.secondary\"\r\n                    gutterBottom\r\n                  >\r\n                    Fees\r\n                  </Typography>\r\n\r\n                  {isEditing ? (\r\n                    <TextField\r\n                      fullWidth\r\n                      name=\"fees\"\r\n                      type=\"number\"\r\n                      value={formData.fees || \"\"}\r\n                      onChange={handleInputChange}\r\n                      size=\"small\"\r\n                      variant=\"outlined\"\r\n                    />\r\n                  ) : (\r\n                    <Typography variant=\"body1\">\r\n                      {formData.fees || \"Not provided\"}\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6}>\r\n                <Box sx={{ mb: 2 }}>\r\n                  <Typography\r\n                    variant=\"subtitle2\"\r\n                    color=\"text.secondary\"\r\n                    gutterBottom\r\n                  >\r\n                    Types\r\n                  </Typography>\r\n\r\n                  {isEditing ? (\r\n                    <Select\r\n                      multiple\r\n                      fullWidth\r\n                      size=\"small\"\r\n                      value={formData.selectedTypes || []}\r\n                      onChange={handleTypeChange}\r\n                      renderValue={(selected) => (\r\n                        <Box\r\n                          sx={{ display: \"flex\", flexWrap: \"wrap\", gap: 0.5 }}\r\n                        >\r\n                          {selected.map((value) => {\r\n                            const type = types.find((t) => t._id === value);\r\n                            return (\r\n                              <Chip\r\n                                key={value}\r\n                                label={type ? type.name : value}\r\n                                size=\"small\"\r\n                              />\r\n                            );\r\n                          })}\r\n                        </Box>\r\n                      )}\r\n                    >\r\n                      {types.map((type) => (\r\n                        <MenuItem key={type._id} value={type._id}>\r\n                          {type.name}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                  ) : (\r\n                    <Box sx={{ display: \"flex\", flexWrap: \"wrap\", gap: 0.5 }}>\r\n                      {formData.types && formData.types.length > 0 ? (\r\n                        formData.types.map((type, index) => (\r\n                          <Chip\r\n                            key={index}\r\n                            label={type.name}\r\n                            size=\"small\"\r\n                            sx={{ bgcolor: \"#f0f0f0\" }}\r\n                          />\r\n                        ))\r\n                      ) : (\r\n                        <Typography variant=\"body1\">\r\n                          No types selected\r\n                        </Typography>\r\n                      )}\r\n                    </Box>\r\n                  )}\r\n                </Box>\r\n              </Grid>\r\n            </Grid>\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* Action Buttons */}\r\n        <Box\r\n          sx={{ mt: 4, display: \"flex\", justifyContent: \"flex-end\", gap: 2 }}\r\n        >\r\n          {isEditing ? (\r\n            <>\r\n              <Button\r\n                variant=\"outlined\"\r\n                color=\"error\"\r\n                onClick={handleCancel}\r\n                startIcon={<FaTimes />}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                onClick={handleSave}\r\n                disabled={saveLoading}\r\n                startIcon={\r\n                  saveLoading ? <CircularProgress size={20} /> : <FaSave />\r\n                }\r\n              >\r\n                {saveLoading ? \"Saving...\" : \"Save Changes\"}\r\n              </Button>\r\n            </>\r\n          ) : (\r\n            <Button\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              onClick={handleEdit}\r\n              startIcon={<FaEdit />}\r\n            >\r\n              Edit Information\r\n            </Button>\r\n          )}\r\n        </Box>\r\n      </Paper>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default BrandSignup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SACEC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAO,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC9B,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMiD,aAAa,GAAG;IACpBC,YAAY,eAAEzB,OAAA,CAACT,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,WAAW,eAAE9B,OAAA,CAACR,UAAU;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BE,WAAW,eAAE/B,OAAA,CAACP,UAAU;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BG,SAAS,eAAEhC,OAAA,CAACN,QAAQ;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBI,UAAU,eAAEjC,OAAA,CAACL,OAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACxB,CAAC;EAEDrD,SAAS,CAAC,MAAM;IACd,MAAM0D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFnB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMoB,UAAU,CAAC,CAAC;QAClB,IAAI9B,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE+B,OAAO,EAAE;UACnB,MAAMC,cAAc,CAAChC,MAAM,CAAC+B,OAAO,CAAC;QACtC;MACF,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACvB,KAAK,CAAC,qBAAqB,EAAEsB,GAAG,CAAC;QACzCrB,QAAQ,CAACqB,GAAG,CAACE,OAAO,IAAI,qBAAqB,CAAC;MAChD,CAAC,SAAS;QACRzB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDmB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,MAAM,CAAC,CAAC;EAEZ,MAAM8B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAM;QAAEM;MAAK,CAAC,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAC9B,gDACF,CAAC;MACD7B,QAAQ,CAAC4B,IAAI,IAAI,EAAE,CAAC;IACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMqB,cAAc,GAAG,MAAOD,OAAO,IAAK;IACxC,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMrD,KAAK,CAACoD,GAAG,CAC9B,2CAA2CN,OAAO,EACpD,CAAC;MACD7B,WAAW,CAACoC,QAAQ,CAACF,IAAI,CAAC;MAC1B9B,eAAe,CAACgC,QAAQ,CAACF,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM4B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzC,WAAW,CAAE0C,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,gBAAgB,GAAIL,CAAC,IAAK;IAC9B,MAAM;MAAEE;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC1B;IACA,IAAID,KAAK,CAACI,MAAM,IAAI,CAAC,EAAE;MACrB5C,WAAW,CAAE0C,IAAI,KAAM;QACrB,GAAGA,IAAI;QACPG,aAAa,EAAEL;MACjB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,eAAA;IACvB;IACA,MAAMC,eAAe,GAAG,EAAAD,eAAA,GAAAhD,QAAQ,CAACM,KAAK,cAAA0C,eAAA,uBAAdA,eAAA,CAAgBE,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,GAAG,CAAC,KAAI,EAAE;IACrEnD,WAAW,CAAE0C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPG,aAAa,EAAEG;IACjB,CAAC,CAAC,CAAC;IACH9C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkD,YAAY,GAAGA,CAAA,KAAM;IACzBpD,WAAW,CAACG,YAAY,CAAC;IACzBD,YAAY,CAAC,KAAK,CAAC;IACnBc,YAAY,CAAC,IAAI,CAAC;IAClBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMuC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzC,cAAc,CAAC,IAAI,CAAC;MACpBI,YAAY,CAAC,IAAI,CAAC;MAClBF,cAAc,CAAC,KAAK,CAAC;MAErB,MAAMwC,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAEjC;MACAC,MAAM,CAACC,IAAI,CAAC1D,QAAQ,CAAC,CAAC2D,OAAO,CAAEC,GAAG,IAAK;QACrC,IACEA,GAAG,KAAK,OAAO,IACfA,GAAG,KAAK,eAAe,IACvBA,GAAG,KAAK,KAAK,IACbA,GAAG,KAAK,WAAW,IACnBA,GAAG,KAAK,WAAW,IACnBA,GAAG,KAAK,KAAK,IACbA,GAAG,KAAK,WAAW,IACnBA,GAAG,KAAK,YAAY,IACpBA,GAAG,KAAK,mBAAmB,IAC3BA,GAAG,KAAK,YAAY,IACpBA,GAAG,KAAK,WAAW,IACnB5D,QAAQ,CAAC4D,GAAG,CAAC,KAAK,IAAI,IACtB5D,QAAQ,CAAC4D,GAAG,CAAC,KAAKC,SAAS,EAC3B;UACAN,UAAU,CAACO,MAAM,CAACF,GAAG,EAAE5D,QAAQ,CAAC4D,GAAG,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;;MAEF;MACA,IAAI5D,QAAQ,CAAC8C,aAAa,IAAIiB,KAAK,CAACC,OAAO,CAAChE,QAAQ,CAAC8C,aAAa,CAAC,EAAE;QACnE9C,QAAQ,CAAC8C,aAAa,CAACa,OAAO,CAAEM,MAAM,IAAK;UACzC,IAAIA,MAAM,EAAEV,UAAU,CAACO,MAAM,CAAC,OAAO,EAAEG,MAAM,CAAC;QAChD,CAAC,CAAC;MACJ;MAEA,MAAM5B,QAAQ,GAAG,MAAMrD,KAAK,CAACkF,GAAG,CAC9B,2CAA2CnE,MAAM,CAAC+B,OAAO,EAAE,EAC3DyB,UAAU,EACV;QAAEY,OAAO,EAAE;UAAE,cAAc,EAAE;QAAsB;MAAE,CACvD,CAAC;;MAED;MACAlE,WAAW,CAACoC,QAAQ,CAACF,IAAI,CAAC;MAC1B9B,eAAe,CAACgC,QAAQ,CAACF,IAAI,CAAC;MAC9BpB,cAAc,CAAC,IAAI,CAAC;MACpBZ,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAA0D,eAAA,EAAAC,oBAAA;MACdpC,OAAO,CAACvB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDO,YAAY,CAAC,EAAAmD,eAAA,GAAA1D,KAAK,CAAC2B,QAAQ,cAAA+B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBjC,IAAI,cAAAkC,oBAAA,uBAApBA,oBAAA,CAAsBnC,OAAO,KAAI,wBAAwB,CAAC;IACzE,CAAC,SAAS;MACRrB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyD,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO;UAAEC,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAU,CAAC;MACzD,KAAK,QAAQ;QACX,OAAO;UAAED,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAU,CAAC;MACzD,KAAK,aAAa;QAChB,OAAO;UAAED,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAU,CAAC;MACzD;QACE,OAAO;UAAED,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAU,CAAC;IAC3D;EACF,CAAC;EAED,IAAIjE,OAAO,EAAE;IACX,oBACEd,OAAA,CAACtB,GAAG;MAACsG,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC3DpF,OAAA,CAACZ,gBAAgB;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,IAAIb,KAAK,EAAE;IACT,oBACEhB,OAAA,CAACtB,GAAG;MAACsG,EAAE,EAAE;QAAEG,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAChBpF,OAAA,CAACX,KAAK;QAACgG,QAAQ,EAAC,OAAO;QAAAD,QAAA,EAAEpE;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,oBACE7B,OAAA,CAACb,SAAS;IAACmG,QAAQ,EAAC,IAAI;IAACN,EAAE,EAAE;MAAEO,EAAE,EAAE;IAAE,CAAE;IAAAH,QAAA,eACrCpF,OAAA,CAACf,KAAK;MAACuG,SAAS,EAAE,CAAE;MAACR,EAAE,EAAE;QAAEG,CAAC,EAAE,CAAC;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBAEvCpF,OAAA,CAACtB,GAAG;QACFsG,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BQ,UAAU,EAAE,QAAQ;UACpBD,EAAE,EAAE;QACN,CAAE;QAAAL,QAAA,gBAEFpF,OAAA,CAACtB,GAAG;UAAA0G,QAAA,gBACFpF,OAAA,CAAChB,UAAU;YAAC2G,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,YAAY;YAAAT,QAAA,EACjD9E,QAAQ,CAACwF,SAAS,IAAI;UAAmB;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEb7B,OAAA,CAACtB,GAAG;YACFsG,EAAE,EAAE;cACFC,OAAO,EAAE,cAAc;cACvBc,EAAE,EAAE,CAAC;cACLR,EAAE,EAAE,GAAG;cACPS,YAAY,EAAE,CAAC;cACf,GAAGpB,cAAc,CAACtE,QAAQ,CAACuE,MAAM;YACnC,CAAE;YAAAO,QAAA,EAED9E,QAAQ,CAACuE,MAAM,GACZ,GAAGvE,QAAQ,CAACuE,MAAM,CACfoB,MAAM,CAAC,CAAC,CAAC,CACTC,WAAW,CAAC,CAAC,GAAG5F,QAAQ,CAACuE,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAC,EAAE,GAC7C;UAAgB;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA,CAACtB,GAAG;UAACsG,EAAE,EAAE;YAAEoB,SAAS,EAAE;UAAS,CAAE;UAAAhB,QAAA,EAC9B9E,QAAQ,CAAC+F,SAAS,gBACjBrG,OAAA;YACEsG,GAAG,EAAE,uDAAuDhG,QAAQ,CAAC+F,SAAS,EAAG;YACjFE,GAAG,EAAC,YAAY;YAChBC,KAAK,EAAE;cACLC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,OAAO;cACfC,SAAS,EAAE;YACb;UAAE;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF7B,OAAA,CAACtB,GAAG;YACFsG,EAAE,EAAE;cACFyB,KAAK,EAAE,GAAG;cACVC,MAAM,EAAE,GAAG;cACXE,OAAO,EAAE,SAAS;cAClB3B,OAAO,EAAE,MAAM;cACfS,UAAU,EAAE,QAAQ;cACpBR,cAAc,EAAE;YAClB,CAAE;YAAAE,QAAA,eAEFpF,OAAA,CAAChB,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACZ,KAAK,EAAC,gBAAgB;cAAAK,QAAA,EAAC;YAEnD;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELT,WAAW,iBACVpB,OAAA,CAACX,KAAK;QAACgG,QAAQ,EAAC,SAAS;QAACL,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EAAC;MAGzC;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEAP,SAAS,iBACRtB,OAAA,CAACX,KAAK;QAACgG,QAAQ,EAAC,OAAO;QAACL,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EACnC9D;MAAS;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGD7B,OAAA,CAACd,IAAI;QAAC2H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1B,QAAA,gBAEzBpF,OAAA,CAACd,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,gBACvBpF,OAAA,CAAChB,UAAU;YACT2G,OAAO,EAAC,IAAI;YACZE,YAAY;YACZb,EAAE,EAAE;cAAEkC,YAAY,EAAE,gBAAgB;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAC/C;UAED;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb7B,OAAA,CAACd,IAAI;YAAC2H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,EACxB,CACC,WAAW,EACX,sBAAsB,EACtB,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,mBAAmB,CACpB,CAAC5B,GAAG,CAAE4D,KAAK,iBACVpH,OAAA,CAACd,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA5B,QAAA,eAChBpF,OAAA,CAACtB,GAAG;gBAACsG,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACjBpF,OAAA,CAAChB,UAAU;kBACT2G,OAAO,EAAC,WAAW;kBACnBZ,KAAK,EAAC,gBAAgB;kBACtBc,YAAY;kBAAAT,QAAA,EAEXgC,KAAK,CACHC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAC1BA,OAAO,CAAC,IAAI,EAAGC,GAAG,IAAKA,GAAG,CAACpB,WAAW,CAAC,CAAC;gBAAC;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EAEZrB,SAAS,gBACRR,OAAA,CAAClB,SAAS;kBACRyI,SAAS;kBACTzE,IAAI,EAAEsE,KAAM;kBACZrE,KAAK,EAAEzC,QAAQ,CAAC8G,KAAK,CAAC,IAAI,EAAG;kBAC7BI,QAAQ,EAAE5E,iBAAkB;kBAC5B6E,IAAI,EAAC,OAAO;kBACZ9B,OAAO,EAAC;gBAAU;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,gBAEF7B,OAAA,CAAChB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAP,QAAA,EACxB9E,QAAQ,CAAC8G,KAAK,CAAC,IAAI;gBAAc;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GA1BgBuF,KAAK;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BvB,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP7B,OAAA,CAACd,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,gBACvBpF,OAAA,CAAChB,UAAU;YACT2G,OAAO,EAAC,IAAI;YACZE,YAAY;YACZb,EAAE,EAAE;cAAEkC,YAAY,EAAE,gBAAgB;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAC/C;UAED;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb7B,OAAA,CAACd,IAAI;YAAC2H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,EACxB,CACC,YAAY,EACZ,cAAc,EACd,aAAa,EACb,WAAW,EACX,aAAa,CACd,CAAC5B,GAAG,CAAE4D,KAAK,iBACVpH,OAAA,CAACd,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA5B,QAAA,eAChBpF,OAAA,CAACtB,GAAG;gBAACsG,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACjBpF,OAAA,CAAChB,UAAU;kBACT2G,OAAO,EAAC,WAAW;kBACnBZ,KAAK,EAAC,gBAAgB;kBACtBc,YAAY;kBAAAT,QAAA,EAEXgC,KAAK,CACHC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CACnBA,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAC1BA,OAAO,CAAC,IAAI,EAAGC,GAAG,IAAKA,GAAG,CAACpB,WAAW,CAAC,CAAC;gBAAC;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EAEZrB,SAAS,gBACRR,OAAA,CAAClB,SAAS;kBACRyI,SAAS;kBACTzE,IAAI,EAAEsE,KAAM;kBACZrE,KAAK,EAAEzC,QAAQ,CAAC8G,KAAK,CAAC,IAAI,EAAG;kBAC7BI,QAAQ,EAAE5E,iBAAkB;kBAC5B6E,IAAI,EAAC,OAAO;kBACZ9B,OAAO,EAAC,UAAU;kBAClB+B,UAAU,EAAE;oBACVC,cAAc,EAAEnG,aAAa,CAAC4F,KAAK;kBACrC;gBAAE;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEF7B,OAAA,CAAChB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAP,QAAA,EACxB9E,QAAQ,CAAC8G,KAAK,CAAC,gBACdpH,OAAA;oBACE4H,IAAI,EACFtH,QAAQ,CAAC8G,KAAK,CAAC,CAACS,UAAU,CAAC,MAAM,CAAC,GAC9BvH,QAAQ,CAAC8G,KAAK,CAAC,GACf,WAAW9G,QAAQ,CAAC8G,KAAK,CAAC,EAC/B;oBACDpE,MAAM,EAAC,QAAQ;oBACf8E,GAAG,EAAC,qBAAqB;oBACzBtB,KAAK,EAAE;sBACLvB,OAAO,EAAE,MAAM;sBACfS,UAAU,EAAE,QAAQ;sBACpBX,KAAK,EAAE,SAAS;sBAChBgD,cAAc,EAAE;oBAClB,CAAE;oBAAA3C,QAAA,gBAEFpF,OAAA,CAACtB,GAAG;sBAACsG,EAAE,EAAE;wBAAEgD,EAAE,EAAE;sBAAE,CAAE;sBAAA5C,QAAA,EAAE5D,aAAa,CAAC4F,KAAK;oBAAC;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC/CvB,QAAQ,CAAC8G,KAAK,CAAC;kBAAA;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GAEJ;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAnDgBuF,KAAK;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoDvB,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP7B,OAAA,CAACd,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA5B,QAAA,gBAChBpF,OAAA,CAAChB,UAAU;YACT2G,OAAO,EAAC,IAAI;YACZE,YAAY;YACZb,EAAE,EAAE;cAAEkC,YAAY,EAAE,gBAAgB;cAAEC,EAAE,EAAE,CAAC;cAAEc,EAAE,EAAE;YAAE,CAAE;YAAA7C,QAAA,EACtD;UAED;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb7B,OAAA,CAACd,IAAI;YAAC2H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,gBACzBpF,OAAA,CAACd,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBpF,OAAA,CAACtB,GAAG;gBAACsG,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACjBpF,OAAA,CAAChB,UAAU;kBACT2G,OAAO,EAAC,WAAW;kBACnBZ,KAAK,EAAC,gBAAgB;kBACtBc,YAAY;kBAAAT,QAAA,EACb;gBAED;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZrB,SAAS,gBACRR,OAAA,CAAClB,SAAS;kBACRyI,SAAS;kBACTzE,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEzC,QAAQ,CAAC4H,cAAc,IAAI,EAAG;kBACrCV,QAAQ,EAAE5E,iBAAkB;kBAC5B6E,IAAI,EAAC,OAAO;kBACZ9B,OAAO,EAAC;gBAAU;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,gBAEF7B,OAAA,CAAChB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAP,QAAA,EACxB9E,QAAQ,CAAC4H,cAAc,IAAI;gBAAc;kBAAAxG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEP7B,OAAA,CAACd,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBpF,OAAA,CAACtB,GAAG;gBAACsG,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACjBpF,OAAA,CAAChB,UAAU;kBACT2G,OAAO,EAAC,WAAW;kBACnBZ,KAAK,EAAC,gBAAgB;kBACtBc,YAAY;kBAAAT,QAAA,EACb;gBAED;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZrB,SAAS,gBACRR,OAAA,CAAClB,SAAS;kBACRyI,SAAS;kBACTY,SAAS;kBACTC,IAAI,EAAE,CAAE;kBACRtF,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAEzC,QAAQ,CAAC+H,gBAAgB,IAAI,EAAG;kBACvCb,QAAQ,EAAE5E,iBAAkB;kBAC5B6E,IAAI,EAAC,OAAO;kBACZ9B,OAAO,EAAC;gBAAU;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,gBAEF7B,OAAA,CAAChB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAP,QAAA,EACxB9E,QAAQ,CAAC+H,gBAAgB,IAAI;gBAAc;kBAAA3G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEP7B,OAAA,CAACd,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBpF,OAAA,CAACtB,GAAG;gBAACsG,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACjBpF,OAAA,CAAChB,UAAU;kBACT2G,OAAO,EAAC,WAAW;kBACnBZ,KAAK,EAAC,gBAAgB;kBACtBc,YAAY;kBAAAT,QAAA,EACb;gBAED;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZrB,SAAS,gBACRR,OAAA,CAAClB,SAAS;kBACRyI,SAAS;kBACTzE,IAAI,EAAC,MAAM;kBACXW,IAAI,EAAC,QAAQ;kBACbV,KAAK,EAAEzC,QAAQ,CAACgI,IAAI,IAAI,EAAG;kBAC3Bd,QAAQ,EAAE5E,iBAAkB;kBAC5B6E,IAAI,EAAC,OAAO;kBACZ9B,OAAO,EAAC;gBAAU;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,gBAEF7B,OAAA,CAAChB,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAP,QAAA,EACxB9E,QAAQ,CAACgI,IAAI,IAAI;gBAAc;kBAAA5G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEP7B,OAAA,CAACd,IAAI;cAAC6H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBpF,OAAA,CAACtB,GAAG;gBAACsG,EAAE,EAAE;kBAAES,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACjBpF,OAAA,CAAChB,UAAU;kBACT2G,OAAO,EAAC,WAAW;kBACnBZ,KAAK,EAAC,gBAAgB;kBACtBc,YAAY;kBAAAT,QAAA,EACb;gBAED;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZrB,SAAS,gBACRR,OAAA,CAACrB,MAAM;kBACL4J,QAAQ;kBACRhB,SAAS;kBACTE,IAAI,EAAC,OAAO;kBACZ1E,KAAK,EAAEzC,QAAQ,CAAC8C,aAAa,IAAI,EAAG;kBACpCoE,QAAQ,EAAEtE,gBAAiB;kBAC3BsF,WAAW,EAAGC,QAAQ,iBACpBzI,OAAA,CAACtB,GAAG;oBACFsG,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEyD,QAAQ,EAAE,MAAM;sBAAEC,GAAG,EAAE;oBAAI,CAAE;oBAAAvD,QAAA,EAEnDqD,QAAQ,CAACjF,GAAG,CAAET,KAAK,IAAK;sBACvB,MAAMU,IAAI,GAAG7C,KAAK,CAACgI,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACnF,GAAG,KAAKX,KAAK,CAAC;sBAC/C,oBACE/C,OAAA,CAACnB,IAAI;wBAEHiK,KAAK,EAAErF,IAAI,GAAGA,IAAI,CAACX,IAAI,GAAGC,KAAM;wBAChC0E,IAAI,EAAC;sBAAO,GAFP1E,KAAK;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGX,CAAC;oBAEN,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACL;kBAAAuD,QAAA,EAEDxE,KAAK,CAAC4C,GAAG,CAAEC,IAAI,iBACdzD,OAAA,CAACpB,QAAQ;oBAAgBmE,KAAK,EAAEU,IAAI,CAACC,GAAI;oBAAA0B,QAAA,EACtC3B,IAAI,CAACX;kBAAI,GADGW,IAAI,CAACC,GAAG;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,gBAET7B,OAAA,CAACtB,GAAG;kBAACsG,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEyD,QAAQ,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAI,CAAE;kBAAAvD,QAAA,EACtD9E,QAAQ,CAACM,KAAK,IAAIN,QAAQ,CAACM,KAAK,CAACuC,MAAM,GAAG,CAAC,GAC1C7C,QAAQ,CAACM,KAAK,CAAC4C,GAAG,CAAC,CAACC,IAAI,EAAEsF,KAAK,kBAC7B/I,OAAA,CAACnB,IAAI;oBAEHiK,KAAK,EAAErF,IAAI,CAACX,IAAK;oBACjB2E,IAAI,EAAC,OAAO;oBACZzC,EAAE,EAAE;sBAAE4B,OAAO,EAAE;oBAAU;kBAAE,GAHtBmC,KAAK;oBAAArH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIX,CACF,CAAC,gBAEF7B,OAAA,CAAChB,UAAU;oBAAC2G,OAAO,EAAC,OAAO;oBAAAP,QAAA,EAAC;kBAE5B;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP7B,OAAA,CAACtB,GAAG;QACFsG,EAAE,EAAE;UAAEiD,EAAE,EAAE,CAAC;UAAEhD,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEyD,GAAG,EAAE;QAAE,CAAE;QAAAvD,QAAA,EAElE5E,SAAS,gBACRR,OAAA,CAAAE,SAAA;UAAAkF,QAAA,gBACEpF,OAAA,CAACjB,MAAM;YACL4G,OAAO,EAAC,UAAU;YAClBZ,KAAK,EAAC,OAAO;YACbiE,OAAO,EAAErF,YAAa;YACtBsF,SAAS,eAAEjJ,OAAA,CAACF,OAAO;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAuD,QAAA,EACxB;UAED;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7B,OAAA,CAACjB,MAAM;YACL4G,OAAO,EAAC,WAAW;YACnBZ,KAAK,EAAC,SAAS;YACfiE,OAAO,EAAEpF,UAAW;YACpBsF,QAAQ,EAAEhI,WAAY;YACtB+H,SAAS,EACP/H,WAAW,gBAAGlB,OAAA,CAACZ,gBAAgB;cAACqI,IAAI,EAAE;YAAG;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACH,MAAM;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACzD;YAAAuD,QAAA,EAEAlE,WAAW,GAAG,WAAW,GAAG;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA,eACT,CAAC,gBAEH7B,OAAA,CAACjB,MAAM;UACL4G,OAAO,EAAC,WAAW;UACnBZ,KAAK,EAAC,SAAS;UACfiE,OAAO,EAAE3F,UAAW;UACpB4F,SAAS,eAAEjJ,OAAA,CAACJ,MAAM;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAuD,QAAA,EACvB;QAED;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACzB,EAAA,CA1kBID,WAAW;EAAA,QACI1B,SAAS;AAAA;AAAA0K,EAAA,GADxBhJ,WAAW;AA4kBjB,eAAeA,WAAW;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}