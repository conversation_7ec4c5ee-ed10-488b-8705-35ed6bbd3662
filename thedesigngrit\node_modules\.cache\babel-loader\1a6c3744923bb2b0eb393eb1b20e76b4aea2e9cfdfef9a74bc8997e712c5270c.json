{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Pages\\\\Vendorspage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box } from \"@mui/material\";\nimport Header from \"../Components/navBar\";\nimport PageDescription from \"../Components/Topheader\";\nimport FilterVSection from \"../Components/VendorsPage/Filters\";\nimport VendorsGrid from \"../Components/VendorsPage/VendorsGrid\";\nimport TopVButtons from \"../Components/VendorsPage/TopButtons\";\nimport Footer from \"../Components/Footer\";\nimport { Helmet } from \"react-helmet-async\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Vendorspage() {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState(\"\"); // Manage category filter\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: \"Brands & Local Designers | The Design Grit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: \"Explore our curated list of Egyptian brands and designers featured on The Design Grit. Discover quality craftsmanship and unique designs from our vendor partners.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"link\", {\n        rel: \"canonical\",\n        href: \"https://thedesigngrit.com/vendors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:title\",\n        content: \"Meet Our Brands | The Design Grit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:description\",\n        content: \"Discover the heart of TDG\\u2014our brands. Explore original Egyptian craftsmanship by trusted local vendors.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        property: \"og:url\",\n        content: \"https://thedesigngrit.com/vendors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageDescription, {\n      name: \"Brands\",\n      description: \"Explore the heart of TheDesignGrit—our brands. They are the foundation of our mission, each bringing unique artistry and unmatched quality to the platform. By showcasing these creators, we’re building a collective that empowers local talent and delivers authentic Egyptian craftsmanship to your home.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"flex-end\",\n        marginTop: \"20px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(TopVButtons, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        flexDirection: \"column\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(FilterVSection, {\n        selectedCategory: selectedCategory,\n        setSelectedCategory: setSelectedCategory\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(VendorsGrid, {\n        selectedCategory: selectedCategory\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_s(Vendorspage, \"vHEs05KRzRen6wbE7HarMc5vD2c=\");\n_c = Vendorspage;\nexport default Vendorspage;\nvar _c;\n$RefreshReg$(_c, \"Vendorspage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Header", "PageDescription", "FilterVSection", "VendorsGrid", "TopVButtons", "Footer", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Vendorspage", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "rel", "href", "property", "description", "sx", "display", "justifyContent", "marginTop", "flexDirection", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Pages/Vendorspage.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box } from \"@mui/material\";\r\nimport Header from \"../Components/navBar\";\r\nimport PageDescription from \"../Components/Topheader\";\r\nimport FilterVSection from \"../Components/VendorsPage/Filters\";\r\nimport VendorsGrid from \"../Components/VendorsPage/VendorsGrid\";\r\nimport TopVButtons from \"../Components/VendorsPage/TopButtons\";\r\nimport Footer from \"../Components/Footer\";\r\nimport { Helmet } from \"react-helmet-async\";\r\nfunction Vendorspage() {\r\n  const [selectedCategory, setSelectedCategory] = useState(\"\"); // Manage category filter\r\n\r\n  return (\r\n    <Box>\r\n      <Helmet>\r\n        <title>Brands & Local Designers | The Design Grit</title>\r\n        <meta\r\n          name=\"description\"\r\n          content=\"Explore our curated list of Egyptian brands and designers featured on The Design Grit. Discover quality craftsmanship and unique designs from our vendor partners.\"\r\n        />\r\n        <link rel=\"canonical\" href=\"https://thedesigngrit.com/vendors\" />\r\n        <meta property=\"og:title\" content=\"Meet Our Brands | The Design Grit\" />\r\n        <meta\r\n          property=\"og:description\"\r\n          content=\"Discover the heart of TDG—our brands. Explore original Egyptian craftsmanship by trusted local vendors.\"\r\n        />\r\n        <meta property=\"og:url\" content=\"https://thedesigngrit.com/vendors\" />\r\n      </Helmet>\r\n      <Header />\r\n      <PageDescription\r\n        name={\"Brands\"}\r\n        description={\r\n          \"Explore the heart of TheDesignGrit—our brands. They are the foundation of our mission, each bringing unique artistry and unmatched quality to the platform. By showcasing these creators, we’re building a collective that empowers local talent and delivers authentic Egyptian craftsmanship to your home.\"\r\n        }\r\n      />\r\n      <Box\r\n        sx={{ display: \"flex\", justifyContent: \"flex-end\", marginTop: \"20px\" }}\r\n      >\r\n        <TopVButtons />\r\n      </Box>\r\n      <Box sx={{ display: \"flex\", flexDirection: \"column\" }}>\r\n        {/* Pass the selected category and setter function */}\r\n        <FilterVSection\r\n          selectedCategory={selectedCategory}\r\n          setSelectedCategory={setSelectedCategory}\r\n        />\r\n        <VendorsGrid selectedCategory={selectedCategory} />\r\n      </Box>\r\n\r\n      <Footer />\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Vendorspage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC5C,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE9D,oBACEU,OAAA,CAACT,GAAG;IAAAc,QAAA,gBACFL,OAAA,CAACF,MAAM;MAAAO,QAAA,gBACLL,OAAA;QAAAK,QAAA,EAAO;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzDT,OAAA;QACEU,IAAI,EAAC,aAAa;QAClBC,OAAO,EAAC;MAAoK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7K,CAAC,eACFT,OAAA;QAAMY,GAAG,EAAC,WAAW;QAACC,IAAI,EAAC;MAAmC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjET,OAAA;QAAMc,QAAQ,EAAC,UAAU;QAACH,OAAO,EAAC;MAAmC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxET,OAAA;QACEc,QAAQ,EAAC,gBAAgB;QACzBH,OAAO,EAAC;MAAyG;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClH,CAAC,eACFT,OAAA;QAAMc,QAAQ,EAAC,QAAQ;QAACH,OAAO,EAAC;MAAmC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eACTT,OAAA,CAACR,MAAM;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVT,OAAA,CAACP,eAAe;MACdiB,IAAI,EAAE,QAAS;MACfK,WAAW,EACT;IACD;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFT,OAAA,CAACT,GAAG;MACFyB,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAd,QAAA,eAEvEL,OAAA,CAACJ,WAAW;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eACNT,OAAA,CAACT,GAAG;MAACyB,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEG,aAAa,EAAE;MAAS,CAAE;MAAAf,QAAA,gBAEpDL,OAAA,CAACN,cAAc;QACbS,gBAAgB,EAAEA,gBAAiB;QACnCC,mBAAmB,EAAEA;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACFT,OAAA,CAACL,WAAW;QAACQ,gBAAgB,EAAEA;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAENT,OAAA,CAACH,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACP,EAAA,CA3CQD,WAAW;AAAAoB,EAAA,GAAXpB,WAAW;AA6CpB,eAAeA,WAAW;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}