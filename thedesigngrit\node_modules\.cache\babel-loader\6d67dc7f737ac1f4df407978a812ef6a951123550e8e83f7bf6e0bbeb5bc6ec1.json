{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\relatedProducts.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Link } from \"react-router-dom\"; // ✅ For navigation\nimport axios from \"axios\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Navigation } from \"swiper/modules\";\nimport \"swiper/css\";\nimport \"swiper/css/navigation\";\nimport { BsExclamationOctagon } from \"react-icons/bs\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RelatedProducts = ({\n  productId\n}) => {\n  _s();\n  const [relatedProducts, setRelatedProducts] = useState([]);\n  // const [categories, setCategories] = useState([]);\n  const [windowWidth, setWindowWidth] = useState(window.innerWidth);\n  const [categories, setCategories] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  // Update window width on resize\n  useEffect(() => {\n    const handleResize = () => {\n      setWindowWidth(window.innerWidth);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const isMobile = windowWidth < 768;\n  useEffect(() => {\n    const fetchRelatedProducts = async () => {\n      try {\n        const response = await axios.get(`https://api.thedesigngrit.com/api/related-products/related/${productId}\n`);\n        setRelatedProducts(response.data);\n      } catch (error) {\n        console.error(\"Error fetching related products:\", error);\n      }\n    };\n    if (productId) fetchRelatedProducts();\n  }, [productId]);\n  // Fetch categories\n  useEffect(() => {\n    const fetchCategories = async () => {\n      setIsLoading(true);\n      try {\n        const response = await fetch(\"https://api.thedesigngrit.com/api/categories/categories/\");\n        const data = await response.json();\n        setCategories(data);\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchCategories();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"related-products-container\",\n    children: relatedProducts.length > 0 ? /*#__PURE__*/_jsxDEV(Swiper, {\n      modules: [Navigation],\n      spaceBetween: isMobile ? 10 : 20,\n      slidesPerView: isMobile ? 1 : undefined,\n      navigation: !isMobile,\n      loop: true,\n      breakpoints: {\n        // When window width is >= 0px\n        0: {\n          slidesPerView: 1,\n          spaceBetween: 10\n        },\n        // When window width is >= 768px (tablet and above)\n        768: {\n          slidesPerView: 2,\n          spaceBetween: 15\n        },\n        // When window width is >= 1024px (desktop)\n        1024: {\n          slidesPerView: 3,\n          spaceBetween: 20\n        }\n      },\n      className: \"related-swiper\",\n      children: relatedProducts.map(product => {\n        const categoryId = typeof product.category === \"object\" ? product.category._id : product.category;\n        const category = categories.find(cat => cat._id === categoryId);\n        const categoryName = isLoading ? \"Loading...\" : (category === null || category === void 0 ? void 0 : category.name) || \"Unknown Category\";\n        return /*#__PURE__*/_jsxDEV(SwiperSlide, {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: `/product/${product._id}`,\n            className: \"related-product-card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"related-product-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"related-product-image-container\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`,\n                  alt: product.name,\n                  className: \"related-img\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"related-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"related-category\",\n                  children: categoryName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"related-name\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 23\n                }, this), product.salePrice && product.promotionApproved === true ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"related-price\",\n                    style: {\n                      textDecoration: \"line-through\",\n                      color: \"#999\"\n                    },\n                    children: [product.price.toLocaleString(\"en-US\", {\n                      minimumFractionDigits: 2,\n                      maximumFractionDigits: 2\n                    }), \" \", \"E\\xA3\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"related-price\",\n                    style: {\n                      color: \"#e74c3c\",\n                      fontWeight: \"bold\"\n                    },\n                    children: [product.salePrice.toLocaleString(\"en-US\", {\n                      minimumFractionDigits: 2,\n                      maximumFractionDigits: 2\n                    }), \" \", \"E\\xA3\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"related-price\",\n                  children: [product.price.toLocaleString(\"en-US\", {\n                    minimumFractionDigits: 2,\n                    maximumFractionDigits: 2\n                  }), \" \", \"E\\xA3\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this)\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        marginTop: \"20px\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        gap: \"16px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(BsExclamationOctagon, {\n        size: 50,\n        color: \"#ccc\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"no-reviews\",\n        children: \"No products yet \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(RelatedProducts, \"CPvzl13U1WhAqJTTsmEEeCQ997o=\");\n_c = RelatedProducts;\nexport default RelatedProducts;\nvar _c;\n$RefreshReg$(_c, \"RelatedProducts\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "axios", "Swiper", "SwiperSlide", "Navigation", "BsExclamationOctagon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RelatedProducts", "productId", "_s", "relatedProducts", "setRelatedProducts", "windowWidth", "setW<PERSON>owWidth", "window", "innerWidth", "categories", "setCategories", "isLoading", "setIsLoading", "handleResize", "addEventListener", "removeEventListener", "isMobile", "fetchRelatedProducts", "response", "get", "data", "error", "console", "fetchCategories", "fetch", "json", "className", "children", "length", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "undefined", "navigation", "loop", "breakpoints", "map", "product", "categoryId", "category", "_id", "find", "cat", "categoryName", "name", "to", "src", "mainImage", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "salePrice", "promotionApproved", "style", "textDecoration", "color", "price", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "fontWeight", "textAlign", "marginTop", "display", "flexDirection", "alignItems", "gap", "size", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/relatedProducts.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Link } from \"react-router-dom\"; // ✅ For navigation\r\nimport axios from \"axios\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport { Navigation } from \"swiper/modules\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/navigation\";\r\nimport { BsExclamationOctagon } from \"react-icons/bs\";\r\n\r\nconst RelatedProducts = ({ productId }) => {\r\n  const [relatedProducts, setRelatedProducts] = useState([]);\r\n  // const [categories, setCategories] = useState([]);\r\n  const [windowWidth, setWindowWidth] = useState(window.innerWidth);\r\n  const [categories, setCategories] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  // Update window width on resize\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setWindowWidth(window.innerWidth);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const isMobile = windowWidth < 768;\r\n  useEffect(() => {\r\n    const fetchRelatedProducts = async () => {\r\n      try {\r\n        const response =\r\n          await axios.get(`https://api.thedesigngrit.com/api/related-products/related/${productId}\r\n`);\r\n        setRelatedProducts(response.data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching related products:\", error);\r\n      }\r\n    };\r\n    if (productId) fetchRelatedProducts();\r\n  }, [productId]);\r\n  // Fetch categories\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      setIsLoading(true);\r\n      try {\r\n        const response = await fetch(\r\n          \"https://api.thedesigngrit.com/api/categories/categories/\"\r\n        );\r\n        const data = await response.json();\r\n        setCategories(data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching categories:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"related-products-container\">\r\n      {relatedProducts.length > 0 ? (\r\n        <Swiper\r\n          modules={[Navigation]}\r\n          spaceBetween={isMobile ? 10 : 20}\r\n          slidesPerView={isMobile ? 1 : undefined}\r\n          navigation={!isMobile}\r\n          loop={true}\r\n          breakpoints={{\r\n            // When window width is >= 0px\r\n            0: {\r\n              slidesPerView: 1,\r\n              spaceBetween: 10,\r\n            },\r\n            // When window width is >= 768px (tablet and above)\r\n            768: {\r\n              slidesPerView: 2,\r\n              spaceBetween: 15,\r\n            },\r\n            // When window width is >= 1024px (desktop)\r\n            1024: {\r\n              slidesPerView: 3,\r\n              spaceBetween: 20,\r\n            },\r\n          }}\r\n          className=\"related-swiper\"\r\n        >\r\n          {relatedProducts.map((product) => {\r\n            const categoryId =\r\n              typeof product.category === \"object\"\r\n                ? product.category._id\r\n                : product.category;\r\n\r\n            const category = categories.find((cat) => cat._id === categoryId);\r\n            const categoryName = isLoading\r\n              ? \"Loading...\"\r\n              : category?.name || \"Unknown Category\";\r\n            return (\r\n              <SwiperSlide key={product._id}>\r\n                <Link\r\n                  to={`/product/${product._id}`}\r\n                  className=\"related-product-card\"\r\n                >\r\n                  <div className=\"related-product-card\">\r\n                    <div className=\"related-product-image-container\">\r\n                      <img\r\n                        src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product.mainImage}`}\r\n                        alt={product.name}\r\n                        className=\"related-img\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"related-info\">\r\n                      <p className=\"related-category\">{categoryName}</p>\r\n                      <h3 className=\"related-name\">{product.name}</h3>\r\n                      {product.salePrice &&\r\n                      product.promotionApproved === true ? (\r\n                        <>\r\n                          <p\r\n                            className=\"related-price\"\r\n                            style={{\r\n                              textDecoration: \"line-through\",\r\n                              color: \"#999\",\r\n                            }}\r\n                          >\r\n                            {product.price.toLocaleString(\"en-US\", {\r\n                              minimumFractionDigits: 2,\r\n                              maximumFractionDigits: 2,\r\n                            })}{\" \"}\r\n                            E£\r\n                          </p>\r\n                          <p\r\n                            className=\"related-price\"\r\n                            style={{ color: \"#e74c3c\", fontWeight: \"bold\" }}\r\n                          >\r\n                            {product.salePrice.toLocaleString(\"en-US\", {\r\n                              minimumFractionDigits: 2,\r\n                              maximumFractionDigits: 2,\r\n                            })}{\" \"}\r\n                            E£\r\n                          </p>\r\n                        </>\r\n                      ) : (\r\n                        <p className=\"related-price\">\r\n                          {product.price.toLocaleString(\"en-US\", {\r\n                            minimumFractionDigits: 2,\r\n                            maximumFractionDigits: 2,\r\n                          })}{\" \"}\r\n                          E£\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              </SwiperSlide>\r\n            );\r\n          })}\r\n        </Swiper>\r\n      ) : (\r\n        <div\r\n          style={{\r\n            textAlign: \"center\",\r\n            marginTop: \"20px\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            alignItems: \"center\",\r\n            gap: \"16px\",\r\n          }}\r\n        >\r\n          <BsExclamationOctagon size={50} color=\"#ccc\" />\r\n\r\n          <p className=\"no-reviews\">No products yet </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RelatedProducts;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB,CAAC,CAAC;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,SAASC,oBAAoB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAACkB,MAAM,CAACC,UAAU,CAAC;EACjE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAChD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMyB,YAAY,GAAGA,CAAA,KAAM;MACzBP,cAAc,CAACC,MAAM,CAACC,UAAU,CAAC;IACnC,CAAC;IAEDD,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAM;MACXN,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,QAAQ,GAAGX,WAAW,GAAG,GAAG;EAClCjB,SAAS,CAAC,MAAM;IACd,MAAM6B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF,MAAMC,QAAQ,GACZ,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,8DAA8DlB,SAAS;AACjG,CAAC,CAAC;QACMG,kBAAkB,CAACc,QAAQ,CAACE,IAAI,CAAC;MACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IACD,IAAIpB,SAAS,EAAEgB,oBAAoB,CAAC,CAAC;EACvC,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EACf;EACAb,SAAS,CAAC,MAAM;IACd,MAAMmC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClCX,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMM,KAAK,CAC1B,0DACF,CAAC;QACD,MAAMJ,IAAI,GAAG,MAAMF,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCf,aAAa,CAACU,IAAI,CAAC;MACrB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,SAAS;QACRT,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDW,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE1B,OAAA;IAAK6B,SAAS,EAAC,4BAA4B;IAAAC,QAAA,EACxCxB,eAAe,CAACyB,MAAM,GAAG,CAAC,gBACzB/B,OAAA,CAACL,MAAM;MACLqC,OAAO,EAAE,CAACnC,UAAU,CAAE;MACtBoC,YAAY,EAAEd,QAAQ,GAAG,EAAE,GAAG,EAAG;MACjCe,aAAa,EAAEf,QAAQ,GAAG,CAAC,GAAGgB,SAAU;MACxCC,UAAU,EAAE,CAACjB,QAAS;MACtBkB,IAAI,EAAE,IAAK;MACXC,WAAW,EAAE;QACX;QACA,CAAC,EAAE;UACDJ,aAAa,EAAE,CAAC;UAChBD,YAAY,EAAE;QAChB,CAAC;QACD;QACA,GAAG,EAAE;UACHC,aAAa,EAAE,CAAC;UAChBD,YAAY,EAAE;QAChB,CAAC;QACD;QACA,IAAI,EAAE;UACJC,aAAa,EAAE,CAAC;UAChBD,YAAY,EAAE;QAChB;MACF,CAAE;MACFJ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAEzBxB,eAAe,CAACiC,GAAG,CAAEC,OAAO,IAAK;QAChC,MAAMC,UAAU,GACd,OAAOD,OAAO,CAACE,QAAQ,KAAK,QAAQ,GAChCF,OAAO,CAACE,QAAQ,CAACC,GAAG,GACpBH,OAAO,CAACE,QAAQ;QAEtB,MAAMA,QAAQ,GAAG9B,UAAU,CAACgC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACF,GAAG,KAAKF,UAAU,CAAC;QACjE,MAAMK,YAAY,GAAGhC,SAAS,GAC1B,YAAY,GACZ,CAAA4B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEK,IAAI,KAAI,kBAAkB;QACxC,oBACE/C,OAAA,CAACJ,WAAW;UAAAkC,QAAA,eACV9B,OAAA,CAACP,IAAI;YACHuD,EAAE,EAAE,YAAYR,OAAO,CAACG,GAAG,EAAG;YAC9Bd,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC9B,OAAA;cAAK6B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC9B,OAAA;gBAAK6B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,eAC9C9B,OAAA;kBACEiD,GAAG,EAAE,uDAAuDT,OAAO,CAACU,SAAS,EAAG;kBAChFC,GAAG,EAAEX,OAAO,CAACO,IAAK;kBAClBlB,SAAS,EAAC;gBAAa;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAK6B,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B9B,OAAA;kBAAG6B,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEgB;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClDvD,OAAA;kBAAI6B,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEU,OAAO,CAACO;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC/Cf,OAAO,CAACgB,SAAS,IAClBhB,OAAO,CAACiB,iBAAiB,KAAK,IAAI,gBAChCzD,OAAA,CAAAE,SAAA;kBAAA4B,QAAA,gBACE9B,OAAA;oBACE6B,SAAS,EAAC,eAAe;oBACzB6B,KAAK,EAAE;sBACLC,cAAc,EAAE,cAAc;sBAC9BC,KAAK,EAAE;oBACT,CAAE;oBAAA9B,QAAA,GAEDU,OAAO,CAACqB,KAAK,CAACC,cAAc,CAAC,OAAO,EAAE;sBACrCC,qBAAqB,EAAE,CAAC;sBACxBC,qBAAqB,EAAE;oBACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJvD,OAAA;oBACE6B,SAAS,EAAC,eAAe;oBACzB6B,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEK,UAAU,EAAE;oBAAO,CAAE;oBAAAnC,QAAA,GAE/CU,OAAO,CAACgB,SAAS,CAACM,cAAc,CAAC,OAAO,EAAE;sBACzCC,qBAAqB,EAAE,CAAC;sBACxBC,qBAAqB,EAAE;oBACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA,eACJ,CAAC,gBAEHvD,OAAA;kBAAG6B,SAAS,EAAC,eAAe;kBAAAC,QAAA,GACzBU,OAAO,CAACqB,KAAK,CAACC,cAAc,CAAC,OAAO,EAAE;oBACrCC,qBAAqB,EAAE,CAAC;oBACxBC,qBAAqB,EAAE;kBACzB,CAAC,CAAC,EAAE,GAAG,EAAC,OAEV;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GAtDSf,OAAO,CAACG,GAAG;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuDhB,CAAC;MAElB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,gBAETvD,OAAA;MACE0D,KAAK,EAAE;QACLQ,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAzC,QAAA,gBAEF9B,OAAA,CAACF,oBAAoB;QAAC0E,IAAI,EAAE,EAAG;QAACZ,KAAK,EAAC;MAAM;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE/CvD,OAAA;QAAG6B,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAgB;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClD,EAAA,CAxKIF,eAAe;AAAAsE,EAAA,GAAftE,eAAe;AA0KrB,eAAeA,eAAe;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}