{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Context\\\\cartcontext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useEffect, useContext } from \"react\";\nimport axios from \"axios\";\nimport { useUser } from \"../utils/userContext\"; // Import user context to access user data\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\nexport const CartProvider = ({\n  children\n}) => {\n  _s();\n  const [cartItems, setCartItems] = useState([]);\n  const [lastAddedItem, setLastAddedItem] = useState(null); // Track last added item for highlighting\n  const [cartTimeoutId, setCartTimeoutId] = useState(null); // Abandoned cart timer\n  const {\n    userSession\n  } = useUser(); // Access user data from user context\n  // Load cart items from localStorage if available\n  useEffect(() => {\n    const storedCartItems = localStorage.getItem(\"cartItems\");\n    if (storedCartItems) {\n      setCartItems(JSON.parse(storedCartItems));\n    }\n  }, []);\n\n  // Update localStorage whenever cartItems change\n  useEffect(() => {\n    localStorage.setItem(\"cartItems\", JSON.stringify(cartItems));\n    console.log(\"Cart items updated:\", cartItems); // Debug log\n  }, [cartItems]);\n  // ⏰ Setup abandoned cart timer\n  useEffect(() => {\n    console.log(\"🪵 Cart changed. Current items:\", cartItems);\n    console.log(\"🪵 User info:\", userSession === null || userSession === void 0 ? void 0 : userSession.email);\n    if (cartTimeoutId) clearTimeout(cartTimeoutId);\n    if (cartItems.length && userSession !== null && userSession !== void 0 && userSession.email) {\n      console.log(`🕒 Starting abandoned cart timer for ${userSession.email} (30 minutes)`);\n      const timeout = setTimeout(() => {\n        axios.post(\"https://api.thedesigngrit.com/api/mailchimp/abandoned-cart\", {\n          email: userSession.email,\n          firstName: userSession.firstName || \"Valued Customer\",\n          lastName: userSession.lastName || \"\"\n        });\n        console.log(\"📧 Abandoned cart email trigger sent for:\", userSession.email, userSession.firstName, userSession.lastName);\n      }, 30 * 60 * 1000);\n      setCartTimeoutId(timeout);\n    }\n    return () => {\n      if (cartTimeoutId) {\n        console.log(\"🛑 Clearing previous abandoned cart timer\");\n        clearTimeout(cartTimeoutId);\n      }\n    };\n  }, [cartItems]);\n  const addToCart = product => {\n    console.log(\"Adding product to cart:\", product); // Debug log\n    console.log(\"Current unit Price:\", product.unitPrice); // Debug log\n\n    // Store the last added item for highlighting in cart\n    setLastAddedItem(product);\n    setCartItems(prev => {\n      // Check if this exact product/variant combination exists in cart\n      const existingProduct = prev.find(item => product.variantId ? item.id === product.id && item.variantId === product.variantId : item.id === product.id);\n      if (existingProduct) {\n        // Update quantity if product already exists\n        return prev.map(item => (product.variantId ? item.id === product.id && item.variantId === product.variantId : item.id === product.id) ? {\n          ...item,\n          quantity: item.quantity + 1\n        } : item);\n      } else {\n        // Add new product with all necessary fields\n        return [...prev, {\n          ...product,\n          brandId: product.brandId || 1,\n          quantity: 1,\n          unitPrice: product.unitPrice || product.salePrice || product.price || 0,\n          shippingFee: product.brandId.fees || 0,\n          // Add variant information if present\n          variantId: product.variantId || null,\n          productId: product.productId || product._id,\n          // Use productId from variant if available\n          // Ensure mainImage is always set\n          mainImage: product.mainImage || product.productId && product.productId.mainImage || \"\"\n        }];\n      }\n    });\n  };\n\n  // Add updateQuantity function\n  const updateQuantity = (itemId, newQuantity) => {\n    if (newQuantity < 1) return; // Prevent quantities less than 1\n\n    setCartItems(prev => prev.map(item => item.id === itemId ? {\n      ...item,\n      quantity: newQuantity,\n      // Make sure totalPrice is updated correctly\n      totalPrice: item.unitPrice * newQuantity\n    } : item));\n  };\n  const removeFromCart = id => {\n    setCartItems(prev => prev.filter(item => item.id !== id));\n  };\n  const resetCart = () => {\n    setCartItems([]); // Clear cartItems state\n    localStorage.removeItem(\"cartItems\"); // Clear localStorage\n    setLastAddedItem(null); // Reset last added item\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: {\n      cartItems,\n      addToCart,\n      removeFromCart,\n      resetCart,\n      updateQuantity,\n      lastAddedItem\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(CartProvider, \"au+IASbpjiJ5HIswA+LxL28Mu1s=\", false, function () {\n  return [useUser];\n});\n_c = CartProvider;\nexport const useCart = () => {\n  _s2();\n  return useContext(CartContext);\n};\n_s2(useCart, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useContext", "axios", "useUser", "jsxDEV", "_jsxDEV", "CartContext", "CartProvider", "children", "_s", "cartItems", "setCartItems", "lastAddedItem", "setLastAddedItem", "cartTimeoutId", "setCartTimeoutId", "userSession", "storedCartItems", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "console", "log", "email", "clearTimeout", "length", "timeout", "setTimeout", "post", "firstName", "lastName", "addToCart", "product", "unitPrice", "prev", "existingProduct", "find", "item", "variantId", "id", "map", "quantity", "brandId", "salePrice", "price", "shippingFee", "fees", "productId", "_id", "mainImage", "updateQuantity", "itemId", "newQuantity", "totalPrice", "removeFromCart", "filter", "resetCart", "removeItem", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useCart", "_s2", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Context/cartcontext.js"], "sourcesContent": ["import React, { createContext, useState, useEffect, useContext } from \"react\";\r\nimport axios from \"axios\";\r\nimport { useUser } from \"../utils/userContext\"; // Import user context to access user data\r\nconst CartContext = createContext();\r\n\r\nexport const CartProvider = ({ children }) => {\r\n  const [cartItems, setCartItems] = useState([]);\r\n  const [lastAddedItem, setLastAddedItem] = useState(null); // Track last added item for highlighting\r\n  const [cartTimeoutId, setCartTimeoutId] = useState(null); // Abandoned cart timer\r\n  const { userSession } = useUser(); // Access user data from user context\r\n  // Load cart items from localStorage if available\r\n  useEffect(() => {\r\n    const storedCartItems = localStorage.getItem(\"cartItems\");\r\n    if (storedCartItems) {\r\n      setCartItems(JSON.parse(storedCartItems));\r\n    }\r\n  }, []);\r\n\r\n  // Update localStorage whenever cartItems change\r\n  useEffect(() => {\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(cartItems));\r\n    console.log(\"Cart items updated:\", cartItems); // Debug log\r\n  }, [cartItems]);\r\n  // ⏰ Setup abandoned cart timer\r\n  useEffect(() => {\r\n    console.log(\"🪵 Cart changed. Current items:\", cartItems);\r\n    console.log(\"🪵 User info:\", userSession?.email);\r\n\r\n    if (cartTimeoutId) clearTimeout(cartTimeoutId);\r\n\r\n    if (cartItems.length && userSession?.email) {\r\n      console.log(\r\n        `🕒 Starting abandoned cart timer for ${userSession.email} (30 minutes)`\r\n      );\r\n\r\n      const timeout = setTimeout(() => {\r\n        axios.post(\r\n          \"https://api.thedesigngrit.com/api/mailchimp/abandoned-cart\",\r\n          {\r\n            email: userSession.email,\r\n            firstName: userSession.firstName || \"Valued Customer\",\r\n            lastName: userSession.lastName || \"\",\r\n          }\r\n        );\r\n        console.log(\r\n          \"📧 Abandoned cart email trigger sent for:\",\r\n          userSession.email,\r\n          userSession.firstName,\r\n          userSession.lastName\r\n        );\r\n      }, 30 * 60 * 1000);\r\n\r\n      setCartTimeoutId(timeout);\r\n    }\r\n\r\n    return () => {\r\n      if (cartTimeoutId) {\r\n        console.log(\"🛑 Clearing previous abandoned cart timer\");\r\n        clearTimeout(cartTimeoutId);\r\n      }\r\n    };\r\n  }, [cartItems]);\r\n\r\n  const addToCart = (product) => {\r\n    console.log(\"Adding product to cart:\", product); // Debug log\r\n    console.log(\"Current unit Price:\", product.unitPrice); // Debug log\r\n\r\n    // Store the last added item for highlighting in cart\r\n    setLastAddedItem(product);\r\n\r\n    setCartItems((prev) => {\r\n      // Check if this exact product/variant combination exists in cart\r\n      const existingProduct = prev.find((item) =>\r\n        product.variantId\r\n          ? item.id === product.id && item.variantId === product.variantId\r\n          : item.id === product.id\r\n      );\r\n\r\n      if (existingProduct) {\r\n        // Update quantity if product already exists\r\n        return prev.map((item) =>\r\n          (\r\n            product.variantId\r\n              ? item.id === product.id && item.variantId === product.variantId\r\n              : item.id === product.id\r\n          )\r\n            ? { ...item, quantity: item.quantity + 1 }\r\n            : item\r\n        );\r\n      } else {\r\n        // Add new product with all necessary fields\r\n        return [\r\n          ...prev,\r\n          {\r\n            ...product,\r\n            brandId: product.brandId || 1,\r\n            quantity: 1,\r\n            unitPrice:\r\n              product.unitPrice || product.salePrice || product.price || 0,\r\n            shippingFee: product.brandId.fees || 0,\r\n            // Add variant information if present\r\n            variantId: product.variantId || null,\r\n            productId: product.productId || product._id, // Use productId from variant if available\r\n            // Ensure mainImage is always set\r\n            mainImage:\r\n              product.mainImage ||\r\n              (product.productId && product.productId.mainImage) ||\r\n              \"\",\r\n          },\r\n        ];\r\n      }\r\n    });\r\n  };\r\n\r\n  // Add updateQuantity function\r\n  const updateQuantity = (itemId, newQuantity) => {\r\n    if (newQuantity < 1) return; // Prevent quantities less than 1\r\n\r\n    setCartItems((prev) =>\r\n      prev.map((item) =>\r\n        item.id === itemId\r\n          ? {\r\n              ...item,\r\n              quantity: newQuantity,\r\n              // Make sure totalPrice is updated correctly\r\n              totalPrice: item.unitPrice * newQuantity,\r\n            }\r\n          : item\r\n      )\r\n    );\r\n  };\r\n\r\n  const removeFromCart = (id) => {\r\n    setCartItems((prev) => prev.filter((item) => item.id !== id));\r\n  };\r\n\r\n  const resetCart = () => {\r\n    setCartItems([]); // Clear cartItems state\r\n    localStorage.removeItem(\"cartItems\"); // Clear localStorage\r\n    setLastAddedItem(null); // Reset last added item\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider\r\n      value={{\r\n        cartItems,\r\n        addToCart,\r\n        removeFromCart,\r\n        resetCart,\r\n        updateQuantity,\r\n        lastAddedItem,\r\n      }}\r\n    >\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useCart = () => useContext(CartContext);\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,sBAAsB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAChD,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM;IAAEiB;EAAY,CAAC,GAAGb,OAAO,CAAC,CAAC,CAAC,CAAC;EACnC;EACAH,SAAS,CAAC,MAAM;IACd,MAAMiB,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACzD,IAAIF,eAAe,EAAE;MACnBN,YAAY,CAACS,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjB,SAAS,CAAC,MAAM;IACdkB,YAAY,CAACI,OAAO,CAAC,WAAW,EAAEF,IAAI,CAACG,SAAS,CAACb,SAAS,CAAC,CAAC;IAC5Dc,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEf,SAAS,CAAC,CAAC,CAAC;EACjD,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACf;EACAV,SAAS,CAAC,MAAM;IACdwB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEf,SAAS,CAAC;IACzDc,OAAO,CAACC,GAAG,CAAC,eAAe,EAAET,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,KAAK,CAAC;IAEhD,IAAIZ,aAAa,EAAEa,YAAY,CAACb,aAAa,CAAC;IAE9C,IAAIJ,SAAS,CAACkB,MAAM,IAAIZ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEU,KAAK,EAAE;MAC1CF,OAAO,CAACC,GAAG,CACT,wCAAwCT,WAAW,CAACU,KAAK,eAC3D,CAAC;MAED,MAAMG,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC/B5B,KAAK,CAAC6B,IAAI,CACR,4DAA4D,EAC5D;UACEL,KAAK,EAAEV,WAAW,CAACU,KAAK;UACxBM,SAAS,EAAEhB,WAAW,CAACgB,SAAS,IAAI,iBAAiB;UACrDC,QAAQ,EAAEjB,WAAW,CAACiB,QAAQ,IAAI;QACpC,CACF,CAAC;QACDT,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CT,WAAW,CAACU,KAAK,EACjBV,WAAW,CAACgB,SAAS,EACrBhB,WAAW,CAACiB,QACd,CAAC;MACH,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAElBlB,gBAAgB,CAACc,OAAO,CAAC;IAC3B;IAEA,OAAO,MAAM;MACX,IAAIf,aAAa,EAAE;QACjBU,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QACxDE,YAAY,CAACb,aAAa,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACJ,SAAS,CAAC,CAAC;EAEf,MAAMwB,SAAS,GAAIC,OAAO,IAAK;IAC7BX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,OAAO,CAAC,CAAC,CAAC;IACjDX,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEU,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC;;IAEvD;IACAvB,gBAAgB,CAACsB,OAAO,CAAC;IAEzBxB,YAAY,CAAE0B,IAAI,IAAK;MACrB;MACA,MAAMC,eAAe,GAAGD,IAAI,CAACE,IAAI,CAAEC,IAAI,IACrCL,OAAO,CAACM,SAAS,GACbD,IAAI,CAACE,EAAE,KAAKP,OAAO,CAACO,EAAE,IAAIF,IAAI,CAACC,SAAS,KAAKN,OAAO,CAACM,SAAS,GAC9DD,IAAI,CAACE,EAAE,KAAKP,OAAO,CAACO,EAC1B,CAAC;MAED,IAAIJ,eAAe,EAAE;QACnB;QACA,OAAOD,IAAI,CAACM,GAAG,CAAEH,IAAI,IACnB,CACEL,OAAO,CAACM,SAAS,GACbD,IAAI,CAACE,EAAE,KAAKP,OAAO,CAACO,EAAE,IAAIF,IAAI,CAACC,SAAS,KAAKN,OAAO,CAACM,SAAS,GAC9DD,IAAI,CAACE,EAAE,KAAKP,OAAO,CAACO,EAAE,IAExB;UAAE,GAAGF,IAAI;UAAEI,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,GAAG;QAAE,CAAC,GACxCJ,IACN,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO,CACL,GAAGH,IAAI,EACP;UACE,GAAGF,OAAO;UACVU,OAAO,EAAEV,OAAO,CAACU,OAAO,IAAI,CAAC;UAC7BD,QAAQ,EAAE,CAAC;UACXR,SAAS,EACPD,OAAO,CAACC,SAAS,IAAID,OAAO,CAACW,SAAS,IAAIX,OAAO,CAACY,KAAK,IAAI,CAAC;UAC9DC,WAAW,EAAEb,OAAO,CAACU,OAAO,CAACI,IAAI,IAAI,CAAC;UACtC;UACAR,SAAS,EAAEN,OAAO,CAACM,SAAS,IAAI,IAAI;UACpCS,SAAS,EAAEf,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACgB,GAAG;UAAE;UAC7C;UACAC,SAAS,EACPjB,OAAO,CAACiB,SAAS,IAChBjB,OAAO,CAACe,SAAS,IAAIf,OAAO,CAACe,SAAS,CAACE,SAAU,IAClD;QACJ,CAAC,CACF;MACH;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;IAC9C,IAAIA,WAAW,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE7B5C,YAAY,CAAE0B,IAAI,IAChBA,IAAI,CAACM,GAAG,CAAEH,IAAI,IACZA,IAAI,CAACE,EAAE,KAAKY,MAAM,GACd;MACE,GAAGd,IAAI;MACPI,QAAQ,EAAEW,WAAW;MACrB;MACAC,UAAU,EAAEhB,IAAI,CAACJ,SAAS,GAAGmB;IAC/B,CAAC,GACDf,IACN,CACF,CAAC;EACH,CAAC;EAED,MAAMiB,cAAc,GAAIf,EAAE,IAAK;IAC7B/B,YAAY,CAAE0B,IAAI,IAAKA,IAAI,CAACqB,MAAM,CAAElB,IAAI,IAAKA,IAAI,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMiB,SAAS,GAAGA,CAAA,KAAM;IACtBhD,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IAClBO,YAAY,CAAC0C,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;IACtC/C,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1B,CAAC;EAED,oBACER,OAAA,CAACC,WAAW,CAACuD,QAAQ;IACnBC,KAAK,EAAE;MACLpD,SAAS;MACTwB,SAAS;MACTuB,cAAc;MACdE,SAAS;MACTN,cAAc;MACdzC;IACF,CAAE;IAAAJ,QAAA,EAEDA;EAAQ;IAAAuD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzD,EAAA,CAvJWF,YAAY;EAAA,QAICJ,OAAO;AAAA;AAAAgE,EAAA,GAJpB5D,YAAY;AAyJzB,OAAO,MAAM6D,OAAO,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAMpE,UAAU,CAACK,WAAW,CAAC;AAAA;AAAC+D,GAAA,CAAxCD,OAAO;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}