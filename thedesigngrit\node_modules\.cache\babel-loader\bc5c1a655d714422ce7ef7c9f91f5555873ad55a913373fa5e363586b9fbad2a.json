{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\favoriteOverlay.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useContext, createContext } from \"react\";\nimport { FaTimes } from \"react-icons/fa\";\nimport { UserContext } from \"../../src/utils/userContext\";\nimport { useNavigate } from \"react-router-dom\";\n\n// Create a context for favorite updates\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const FavoritesContext = /*#__PURE__*/createContext({\n  favoritesUpdated: false,\n  updateFavorites: () => {},\n  favoriteCount: 0,\n  setFavoriteCount: () => {}\n});\nexport const FavoritesProvider = ({\n  children\n}) => {\n  _s();\n  const [favoritesUpdated, setFavoritesUpdated] = useState(false);\n  const [favoriteCount, setFavoriteCount] = useState(0);\n  const {\n    userSession\n  } = useContext(UserContext);\n  const updateFavorites = () => {\n    setFavoritesUpdated(prev => !prev); // Toggle to trigger useEffect\n  };\n\n  // Fetch favorite count whenever favorites are updated\n  useEffect(() => {\n    const fetchFavoriteCount = async () => {\n      if (!userSession) {\n        setFavoriteCount(0);\n        return;\n      }\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/favorites/${userSession.id}`);\n        if (response.ok) {\n          const favoritesData = await response.json();\n          setFavoriteCount(favoritesData.length);\n        }\n      } catch (error) {\n        console.error(\"Error fetching favorite count:\", error);\n      }\n    };\n    fetchFavoriteCount();\n  }, [userSession, favoritesUpdated]);\n  return /*#__PURE__*/_jsxDEV(FavoritesContext.Provider, {\n    value: {\n      favoritesUpdated,\n      updateFavorites,\n      favoriteCount,\n      setFavoriteCount\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(FavoritesProvider, \"ZQZ1UfLOZmmIN7O3daTtadmOJfk=\");\n_c = FavoritesProvider;\nconst FavoritesOverlay = ({\n  open,\n  onClose\n}) => {\n  _s2();\n  const [favoriteProducts, setFavoriteProducts] = useState([]);\n  const {\n    userSession\n  } = useContext(UserContext);\n  const navigate = useNavigate();\n  const {\n    favoritesUpdated,\n    updateFavorites,\n    setFavoriteCount\n  } = useContext(FavoritesContext);\n\n  // Fetch favorite products when the component is mounted or when userSession or favoritesUpdated changes\n  useEffect(() => {\n    const fetchFavorites = async () => {\n      if (!userSession) return;\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/favorites/${userSession.id}`);\n        if (response.ok) {\n          const favoritesData = await response.json();\n          setFavoriteProducts(favoritesData);\n          // Update the count in the context\n          setFavoriteCount(favoritesData.length);\n        } else {\n          console.error(\"Failed to fetch favorites\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching favorites:\", error);\n      }\n    };\n    if (userSession) {\n      fetchFavorites();\n    }\n  }, [userSession, favoritesUpdated, open, setFavoriteCount]);\n\n  // Function to remove item from favorites\n  const removeFavorite = async productId => {\n    if (!userSession) return;\n    try {\n      const response = await fetch(`https://api.thedesigngrit.com/api/favorites/remove`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userSession,\n          productId\n        })\n      });\n      if (response.ok) {\n        // Update the local state to remove the product\n        setFavoriteProducts(prevProducts => prevProducts.filter(product => product._id !== productId));\n\n        // Update the count in the context\n        setFavoriteCount(prev => Math.max(0, prev - 1));\n\n        // Trigger update to refresh other components\n        updateFavorites();\n      } else {\n        console.error(\"Failed to remove favorite\");\n      }\n    } catch (error) {\n      console.error(\"Error removing favorite:\", error);\n    }\n  };\n  if (!open) return null; // Do not render if not open\n\n  const navigateToWishlistPage = () => {\n    navigate(\"/myaccount\", {\n      state: {\n        section: \"wishlist\"\n      }\n    });\n    onClose(); // Close the overlay when navigating\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"overlay-container-vendor\",\n    style: {\n      right: \"30px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overlay-header-vendor\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontFamily: \"Horizon\",\n          fontWeight: \"bold\"\n        },\n        children: [\"Wishlist (\", favoriteProducts.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close-button-vendor\",\n        onClick: onClose,\n        style: {\n          color: \"#2d2d2d\",\n          backgroundColor: \"transparent\",\n          border: \"none\",\n          cursor: \"pointer\",\n          \":hover\": {\n            backgroundColor: \"transparent\",\n            color: \"#2d2d2d\"\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overlay-body-vendor\",\n      children: favoriteProducts.length > 0 ? favoriteProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notification-item-vendor\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notification-image-vendor\",\n          onClick: () => navigate(`/product/${product._id}`),\n          style: {\n            cursor: \"pointer\"\n          },\n          children: product && product.mainImage && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product === null || product === void 0 ? void 0 : product.mainImage}`,\n            alt: (product === null || product === void 0 ? void 0 : product.name) || \"Product\",\n            className: \"notification-image-vendor-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notification-details-vendor\",\n          onClick: () => navigate(`/product/${product._id}`),\n          style: {\n            cursor: \"pointer\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this), product.salePrice ? /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              textDecoration: \"line-through\",\n              marginRight: \"8px\",\n              color: \"#ccc\"\n            },\n            children: [product.price.toLocaleString(), \" E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\" \", product.price.toLocaleString(), \" E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 19\n          }, this), product.salePrice && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: \"red\"\n            },\n            children: [product.salePrice.toLocaleString(), \" E\\xA3\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: product.brandId.brandName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notification-actions-vendor\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-status-vendor\",\n            style: {\n              backgroundColor: product.stock === 0 ? \"#f44336\" : product.stock > 0 && product.stock < 5 ? \"#ff9800\" : \"#6c7c59\"\n            },\n            children: product.stock === 0 ? \"Unavailable\" : product.stock > 0 && product.stock < 5 ? \"Hurry! Only \" + product.stock + \" left\" : \"Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"remove-favorite-button\",\n            onClick: () => removeFavorite(product._id),\n            \"aria-label\": \"Remove from favorites\",\n            children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 15\n        }, this)]\n      }, product._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          padding: \"16px\",\n          textAlign: \"center\",\n          color: \"#888\"\n        },\n        children: \"No favorites added yet.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overlay-footer-vendor\",\n      children: !userSession ? /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"view-all-vendor\",\n        onClick: () => navigate(\"/login\"),\n        children: \"VIEW WISHLIST\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"view-all-vendor\",\n        onClick: navigateToWishlistPage,\n        children: \"VIEW WISHLIST\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s2(FavoritesOverlay, \"YMvN0nvIWe8W4WvX1sIqB7euvoA=\", false, function () {\n  return [useNavigate];\n});\n_c2 = FavoritesOverlay;\nexport default FavoritesOverlay;\nvar _c, _c2;\n$RefreshReg$(_c, \"FavoritesProvider\");\n$RefreshReg$(_c2, \"FavoritesOverlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "createContext", "FaTimes", "UserContext", "useNavigate", "jsxDEV", "_jsxDEV", "FavoritesContext", "favoritesUpdated", "updateFavorites", "favoriteCount", "setFavoriteCount", "FavoritesProvider", "children", "_s", "setFavoritesUpdated", "userSession", "prev", "fetchFavoriteCount", "response", "fetch", "id", "ok", "favoritesData", "json", "length", "error", "console", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "FavoritesOverlay", "open", "onClose", "_s2", "favoriteProducts", "setFavoriteProducts", "navigate", "fetchFavorites", "removeFavorite", "productId", "method", "headers", "body", "JSON", "stringify", "prevProducts", "filter", "product", "_id", "Math", "max", "navigateToWishlistPage", "state", "section", "className", "style", "right", "fontFamily", "fontWeight", "onClick", "color", "backgroundColor", "border", "cursor", "map", "mainImage", "src", "alt", "name", "salePrice", "textDecoration", "marginRight", "price", "toLocaleString", "brandId", "brandName", "stock", "padding", "textAlign", "_c2", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/favoriteOverlay.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext, createContext } from \"react\";\r\nimport { FaTimes } from \"react-icons/fa\";\r\nimport { UserContext } from \"../../src/utils/userContext\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\n// Create a context for favorite updates\r\nexport const FavoritesContext = createContext({\r\n  favoritesUpdated: false,\r\n  updateFavorites: () => {},\r\n  favoriteCount: 0,\r\n  setFavoriteCount: () => {},\r\n});\r\n\r\nexport const FavoritesProvider = ({ children }) => {\r\n  const [favoritesUpdated, setFavoritesUpdated] = useState(false);\r\n  const [favoriteCount, setFavoriteCount] = useState(0);\r\n  const { userSession } = useContext(UserContext);\r\n\r\n  const updateFavorites = () => {\r\n    setFavoritesUpdated((prev) => !prev); // Toggle to trigger useEffect\r\n  };\r\n\r\n  // Fetch favorite count whenever favorites are updated\r\n  useEffect(() => {\r\n    const fetchFavoriteCount = async () => {\r\n      if (!userSession) {\r\n        setFavoriteCount(0);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/favorites/${userSession.id}`\r\n        );\r\n        if (response.ok) {\r\n          const favoritesData = await response.json();\r\n          setFavoriteCount(favoritesData.length);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching favorite count:\", error);\r\n      }\r\n    };\r\n\r\n    fetchFavoriteCount();\r\n  }, [userSession, favoritesUpdated]);\r\n\r\n  return (\r\n    <FavoritesContext.Provider\r\n      value={{\r\n        favoritesUpdated,\r\n        updateFavorites,\r\n        favoriteCount,\r\n        setFavoriteCount,\r\n      }}\r\n    >\r\n      {children}\r\n    </FavoritesContext.Provider>\r\n  );\r\n};\r\n\r\nconst FavoritesOverlay = ({ open, onClose }) => {\r\n  const [favoriteProducts, setFavoriteProducts] = useState([]);\r\n  const { userSession } = useContext(UserContext);\r\n  const navigate = useNavigate();\r\n  const { favoritesUpdated, updateFavorites, setFavoriteCount } =\r\n    useContext(FavoritesContext);\r\n\r\n  // Fetch favorite products when the component is mounted or when userSession or favoritesUpdated changes\r\n  useEffect(() => {\r\n    const fetchFavorites = async () => {\r\n      if (!userSession) return;\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/favorites/${userSession.id}`\r\n        );\r\n        if (response.ok) {\r\n          const favoritesData = await response.json();\r\n          setFavoriteProducts(favoritesData);\r\n          // Update the count in the context\r\n          setFavoriteCount(favoritesData.length);\r\n        } else {\r\n          console.error(\"Failed to fetch favorites\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching favorites:\", error);\r\n      }\r\n    };\r\n\r\n    if (userSession) {\r\n      fetchFavorites();\r\n    }\r\n  }, [userSession, favoritesUpdated, open, setFavoriteCount]);\r\n\r\n  // Function to remove item from favorites\r\n  const removeFavorite = async (productId) => {\r\n    if (!userSession) return;\r\n\r\n    try {\r\n      const response = await fetch(\r\n        `https://api.thedesigngrit.com/api/favorites/remove`,\r\n        {\r\n          method: \"POST\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n          },\r\n          body: JSON.stringify({\r\n            userSession,\r\n            productId,\r\n          }),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        // Update the local state to remove the product\r\n        setFavoriteProducts((prevProducts) =>\r\n          prevProducts.filter((product) => product._id !== productId)\r\n        );\r\n\r\n        // Update the count in the context\r\n        setFavoriteCount((prev) => Math.max(0, prev - 1));\r\n\r\n        // Trigger update to refresh other components\r\n        updateFavorites();\r\n      } else {\r\n        console.error(\"Failed to remove favorite\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error removing favorite:\", error);\r\n    }\r\n  };\r\n\r\n  if (!open) return null; // Do not render if not open\r\n\r\n  const navigateToWishlistPage = () => {\r\n    navigate(\"/myaccount\", { state: { section: \"wishlist\" } });\r\n    onClose(); // Close the overlay when navigating\r\n  };\r\n\r\n  return (\r\n    <div className=\"overlay-container-vendor\" style={{ right: \"30px\" }}>\r\n      {/* Header */}\r\n      <div className=\"overlay-header-vendor\">\r\n        <h3 style={{ fontFamily: \"Horizon\", fontWeight: \"bold\" }}>\r\n          Wishlist ({favoriteProducts.length})\r\n        </h3>\r\n        <button\r\n          className=\"close-button-vendor\"\r\n          onClick={onClose}\r\n          style={{\r\n            color: \"#2d2d2d\",\r\n            backgroundColor: \"transparent\",\r\n            border: \"none\",\r\n            cursor: \"pointer\",\r\n            \":hover\": {\r\n              backgroundColor: \"transparent\",\r\n              color: \"#2d2d2d\",\r\n            },\r\n          }}\r\n        >\r\n          <FaTimes />\r\n        </button>\r\n      </div>\r\n\r\n      {/* Body */}\r\n      <div className=\"overlay-body-vendor\">\r\n        {favoriteProducts.length > 0 ? (\r\n          favoriteProducts.map((product) => (\r\n            <div key={product._id} className=\"notification-item-vendor\">\r\n              <div\r\n                className=\"notification-image-vendor\"\r\n                onClick={() => navigate(`/product/${product._id}`)}\r\n                style={{ cursor: \"pointer\" }}\r\n              >\r\n                {/* Assuming `product.mainImage` is the image path */}\r\n                {product && product.mainImage && (\r\n                  <img\r\n                    src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${product?.mainImage}`}\r\n                    alt={product?.name || \"Product\"}\r\n                    className=\"notification-image-vendor-image\"\r\n                  />\r\n                )}\r\n              </div>\r\n              <div\r\n                className=\"notification-details-vendor\"\r\n                onClick={() => navigate(`/product/${product._id}`)}\r\n                style={{ cursor: \"pointer\" }}\r\n              >\r\n                <h4>{product.name}</h4>\r\n                {product.salePrice ? (\r\n                  <p\r\n                    style={{\r\n                      textDecoration: \"line-through\",\r\n                      marginRight: \"8px\",\r\n                      color: \"#ccc\",\r\n                    }}\r\n                  >\r\n                    {product.price.toLocaleString()} E£\r\n                  </p>\r\n                ) : (\r\n                  <p> {product.price.toLocaleString()} E£</p>\r\n                )}\r\n                {product.salePrice && (\r\n                  <p style={{ color: \"red\" }}>\r\n                    {product.salePrice.toLocaleString()} E£\r\n                  </p>\r\n                )}\r\n                <span>{product.brandId.brandName}</span>\r\n              </div>\r\n              <div className=\"notification-actions-vendor\">\r\n                <div\r\n                  className=\"notification-status-vendor\"\r\n                  style={{\r\n                    backgroundColor:\r\n                      product.stock === 0\r\n                        ? \"#f44336\"\r\n                        : product.stock > 0 && product.stock < 5\r\n                        ? \"#ff9800\"\r\n                        : \"#6c7c59\",\r\n                  }}\r\n                >\r\n                  {product.stock === 0\r\n                    ? \"Unavailable\"\r\n                    : product.stock > 0 && product.stock < 5\r\n                    ? \"Hurry! Only \" + product.stock + \" left\"\r\n                    : \"Available\"}\r\n                </div>\r\n                <button\r\n                  className=\"remove-favorite-button\"\r\n                  onClick={() => removeFavorite(product._id)}\r\n                  aria-label=\"Remove from favorites\"\r\n                >\r\n                  <FaTimes />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ))\r\n        ) : (\r\n          <p style={{ padding: \"16px\", textAlign: \"center\", color: \"#888\" }}>\r\n            No favorites added yet.\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      <div className=\"overlay-footer-vendor\">\r\n        {!userSession ? (\r\n          <button\r\n            className=\"view-all-vendor\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            VIEW WISHLIST\r\n          </button>\r\n        ) : (\r\n          <button className=\"view-all-vendor\" onClick={navigateToWishlistPage}>\r\n            VIEW WISHLIST\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FavoritesOverlay;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,aAAa,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,gBAAgB,gBAAGN,aAAa,CAAC;EAC5CO,gBAAgB,EAAE,KAAK;EACvBC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAC;EACzBC,aAAa,EAAE,CAAC;EAChBC,gBAAgB,EAAEA,CAAA,KAAM,CAAC;AAC3B,CAAC,CAAC;AAEF,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjD,MAAM,CAACN,gBAAgB,EAAEO,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM;IAAEkB;EAAY,CAAC,GAAGhB,UAAU,CAACG,WAAW,CAAC;EAE/C,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BM,mBAAmB,CAAEE,IAAI,IAAK,CAACA,IAAI,CAAC,CAAC,CAAC;EACxC,CAAC;;EAED;EACAlB,SAAS,CAAC,MAAM;IACd,MAAMmB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI,CAACF,WAAW,EAAE;QAChBL,gBAAgB,CAAC,CAAC,CAAC;QACnB;MACF;MAEA,IAAI;QACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+CJ,WAAW,CAACK,EAAE,EAC/D,CAAC;QACD,IAAIF,QAAQ,CAACG,EAAE,EAAE;UACf,MAAMC,aAAa,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UAC3Cb,gBAAgB,CAACY,aAAa,CAACE,MAAM,CAAC;QACxC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;IACF,CAAC;IAEDR,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACF,WAAW,EAAER,gBAAgB,CAAC,CAAC;EAEnC,oBACEF,OAAA,CAACC,gBAAgB,CAACqB,QAAQ;IACxBC,KAAK,EAAE;MACLrB,gBAAgB;MAChBC,eAAe;MACfC,aAAa;MACbC;IACF,CAAE;IAAAE,QAAA,EAEDA;EAAQ;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAACnB,EAAA,CA7CWF,iBAAiB;AAAAsB,EAAA,GAAjBtB,iBAAiB;AA+C9B,MAAMuB,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM;IAAEkB;EAAY,CAAC,GAAGhB,UAAU,CAACG,WAAW,CAAC;EAC/C,MAAMsC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEI,gBAAgB;IAAEC,eAAe;IAAEE;EAAiB,CAAC,GAC3DX,UAAU,CAACO,gBAAgB,CAAC;;EAE9B;EACAR,SAAS,CAAC,MAAM;IACd,MAAM2C,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAAC1B,WAAW,EAAE;MAElB,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+CJ,WAAW,CAACK,EAAE,EAC/D,CAAC;QACD,IAAIF,QAAQ,CAACG,EAAE,EAAE;UACf,MAAMC,aAAa,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;UAC3CgB,mBAAmB,CAACjB,aAAa,CAAC;UAClC;UACAZ,gBAAgB,CAACY,aAAa,CAACE,MAAM,CAAC;QACxC,CAAC,MAAM;UACLE,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;IAED,IAAIV,WAAW,EAAE;MACf0B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC1B,WAAW,EAAER,gBAAgB,EAAE4B,IAAI,EAAEzB,gBAAgB,CAAC,CAAC;;EAE3D;EACA,MAAMgC,cAAc,GAAG,MAAOC,SAAS,IAAK;IAC1C,IAAI,CAAC5B,WAAW,EAAE;IAElB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAC1B,oDAAoD,EACpD;QACEyB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBjC,WAAW;UACX4B;QACF,CAAC;MACH,CACF,CAAC;MAED,IAAIzB,QAAQ,CAACG,EAAE,EAAE;QACf;QACAkB,mBAAmB,CAAEU,YAAY,IAC/BA,YAAY,CAACC,MAAM,CAAEC,OAAO,IAAKA,OAAO,CAACC,GAAG,KAAKT,SAAS,CAC5D,CAAC;;QAED;QACAjC,gBAAgB,CAAEM,IAAI,IAAKqC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEtC,IAAI,GAAG,CAAC,CAAC,CAAC;;QAEjD;QACAR,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACLkB,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,IAAI,CAACU,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;EAExB,MAAMoB,sBAAsB,GAAGA,CAAA,KAAM;IACnCf,QAAQ,CAAC,YAAY,EAAE;MAAEgB,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAW;IAAE,CAAC,CAAC;IAC1DrB,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EAED,oBACE/B,OAAA;IAAKqD,SAAS,EAAC,0BAA0B;IAACC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAhD,QAAA,gBAEjEP,OAAA;MAAKqD,SAAS,EAAC,uBAAuB;MAAA9C,QAAA,gBACpCP,OAAA;QAAIsD,KAAK,EAAE;UAAEE,UAAU,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAlD,QAAA,GAAC,YAC9C,EAAC0B,gBAAgB,CAACd,MAAM,EAAC,GACrC;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3B,OAAA;QACEqD,SAAS,EAAC,qBAAqB;QAC/BK,OAAO,EAAE3B,OAAQ;QACjBuB,KAAK,EAAE;UACLK,KAAK,EAAE,SAAS;UAChBC,eAAe,EAAE,aAAa;UAC9BC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjB,QAAQ,EAAE;YACRF,eAAe,EAAE,aAAa;YAC9BD,KAAK,EAAE;UACT;QACF,CAAE;QAAApD,QAAA,eAEFP,OAAA,CAACJ,OAAO;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN3B,OAAA;MAAKqD,SAAS,EAAC,qBAAqB;MAAA9C,QAAA,EACjC0B,gBAAgB,CAACd,MAAM,GAAG,CAAC,GAC1Bc,gBAAgB,CAAC8B,GAAG,CAAEjB,OAAO,iBAC3B9C,OAAA;QAAuBqD,SAAS,EAAC,0BAA0B;QAAA9C,QAAA,gBACzDP,OAAA;UACEqD,SAAS,EAAC,2BAA2B;UACrCK,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,YAAYW,OAAO,CAACC,GAAG,EAAE,CAAE;UACnDO,KAAK,EAAE;YAAEQ,MAAM,EAAE;UAAU,CAAE;UAAAvD,QAAA,EAG5BuC,OAAO,IAAIA,OAAO,CAACkB,SAAS,iBAC3BhE,OAAA;YACEiE,GAAG,EAAE,uDAAuDnB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkB,SAAS,EAAG;YACjFE,GAAG,EAAE,CAAApB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqB,IAAI,KAAI,SAAU;YAChCd,SAAS,EAAC;UAAiC;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN3B,OAAA;UACEqD,SAAS,EAAC,6BAA6B;UACvCK,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,YAAYW,OAAO,CAACC,GAAG,EAAE,CAAE;UACnDO,KAAK,EAAE;YAAEQ,MAAM,EAAE;UAAU,CAAE;UAAAvD,QAAA,gBAE7BP,OAAA;YAAAO,QAAA,EAAKuC,OAAO,CAACqB;UAAI;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACtBmB,OAAO,CAACsB,SAAS,gBAChBpE,OAAA;YACEsD,KAAK,EAAE;cACLe,cAAc,EAAE,cAAc;cAC9BC,WAAW,EAAE,KAAK;cAClBX,KAAK,EAAE;YACT,CAAE;YAAApD,QAAA,GAEDuC,OAAO,CAACyB,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,QAClC;UAAA;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAEJ3B,OAAA;YAAAO,QAAA,GAAG,GAAC,EAACuC,OAAO,CAACyB,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,QAAG;UAAA;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAC3C,EACAmB,OAAO,CAACsB,SAAS,iBAChBpE,OAAA;YAAGsD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAM,CAAE;YAAApD,QAAA,GACxBuC,OAAO,CAACsB,SAAS,CAACI,cAAc,CAAC,CAAC,EAAC,QACtC;UAAA;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ,eACD3B,OAAA;YAAAO,QAAA,EAAOuC,OAAO,CAAC2B,OAAO,CAACC;UAAS;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACN3B,OAAA;UAAKqD,SAAS,EAAC,6BAA6B;UAAA9C,QAAA,gBAC1CP,OAAA;YACEqD,SAAS,EAAC,4BAA4B;YACtCC,KAAK,EAAE;cACLM,eAAe,EACbd,OAAO,CAAC6B,KAAK,KAAK,CAAC,GACf,SAAS,GACT7B,OAAO,CAAC6B,KAAK,GAAG,CAAC,IAAI7B,OAAO,CAAC6B,KAAK,GAAG,CAAC,GACtC,SAAS,GACT;YACR,CAAE;YAAApE,QAAA,EAEDuC,OAAO,CAAC6B,KAAK,KAAK,CAAC,GAChB,aAAa,GACb7B,OAAO,CAAC6B,KAAK,GAAG,CAAC,IAAI7B,OAAO,CAAC6B,KAAK,GAAG,CAAC,GACtC,cAAc,GAAG7B,OAAO,CAAC6B,KAAK,GAAG,OAAO,GACxC;UAAW;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACN3B,OAAA;YACEqD,SAAS,EAAC,wBAAwB;YAClCK,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAACS,OAAO,CAACC,GAAG,CAAE;YAC3C,cAAW,uBAAuB;YAAAxC,QAAA,eAElCP,OAAA,CAACJ,OAAO;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,GAlEEmB,OAAO,CAACC,GAAG;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmEhB,CACN,CAAC,gBAEF3B,OAAA;QAAGsD,KAAK,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE,QAAQ;UAAElB,KAAK,EAAE;QAAO,CAAE;QAAApD,QAAA,EAAC;MAEnE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACJ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3B,OAAA;MAAKqD,SAAS,EAAC,uBAAuB;MAAA9C,QAAA,EACnC,CAACG,WAAW,gBACXV,OAAA;QACEqD,SAAS,EAAC,iBAAiB;QAC3BK,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,QAAQ,CAAE;QAAA5B,QAAA,EACnC;MAED;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAET3B,OAAA;QAAQqD,SAAS,EAAC,iBAAiB;QAACK,OAAO,EAAER,sBAAuB;QAAA3C,QAAA,EAAC;MAErE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,GAAA,CAzMIH,gBAAgB;EAAA,QAGH/B,WAAW;AAAA;AAAAgF,GAAA,GAHxBjD,gBAAgB;AA2MtB,eAAeA,gBAAgB;AAAC,IAAAD,EAAA,EAAAkD,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}