{"ast": null, "code": "var _jsxFileName = \"D:\\\\TDGweb\\\\TDG\\\\thedesigngrit\\\\src\\\\Components\\\\navBar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext, Fragment } from \"react\";\nimport { Box, Typography, InputBase, IconButton, Avatar, Menu, MenuItem, Badge } from \"@mui/material\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ShoppingCartIcon from \"@mui/icons-material/ShoppingCart\";\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\nimport ConfirmationDialog from \"./confirmationMsg\";\n// import FloatingButton from \"./ChatButton\";\nimport ProfilePopup from \"./profilePopUp\";\n// import Stickedbutton from \"./MoodboardButton\";\nimport ShoppingCartOverlay from \"./Popups/CartOverlay\";\nimport FavoritesOverlay from \"./favoriteOverlay\";\nimport Menudrop from \"./menuhover/Menudrop\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport MenuIcon from \"@mui/icons-material/Menu\";\n// Context for managing user session\nimport { IoLogOutOutline } from \"react-icons/io5\";\nimport { UserContext } from \"../utils/userContext\";\nimport axios from \"axios\";\nimport { useCart } from \"../Context/cartcontext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  _s();\n  var _userData$firstName, _userData$firstName$c;\n  const [popupOpen, setPopupOpen] = useState(false);\n  const [cartOpen, setCartOpen] = useState(false);\n  const [favoritesOpen, setFavoritesOpen] = useState(false);\n  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);\n  const [hoveredCategory, setHoveredCategory] = useState(null);\n  const [categoriesVisible, setCategoriesVisible] = useState(false); // State to toggle categories visibility\n  const [menuData, setMenuData] = useState([{\n    _id: \"static-1\",\n    name: \"Furniture\"\n  }, {\n    _id: \"static-2\",\n    name: \"Kitchen & Dining\"\n  }, {\n    _id: \"static-3\",\n    name: \"Bath\"\n  }, {\n    _id: \"static-4\",\n    name: \"Lighting\"\n  }, {\n    _id: \"static-5\",\n    name: \"Home Decor\"\n  }, {\n    _id: \"static-6\",\n    name: \"Outdoor\"\n  }]);\n  const [isMenuHovered, setIsMenuHovered] = useState(false);\n  const [menuOpen, setMenuOpen] = useState(false);\n  const [isSticky, setIsSticky] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null); // State for the avatar menu\n  const {\n    cartItems\n  } = useCart();\n  const [anchorEls, setAnchorEls] = useState(null); // State for the avatar menu\n  const {\n    userSession,\n    logout\n  } = useContext(UserContext); // Access both userSession and setUserSession\n  const navigate = useNavigate(); // Hook for navigation\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 767);\n  const [userData, setUserData] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    address1: \"\",\n    phoneNumber: \"\"\n  });\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [suggestions, setSuggestions] = useState([]);\n  // Add state for shop dropdown\n  const [setShopDropdownOpen] = useState(false);\n  const [favoriteCount, setFavoriteCount] = useState(0);\n  const [favoriteUpdated, setFavoriteUpdated] = useState(false);\n  const totalCartItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const response = await fetch(\"https://api.thedesigngrit.com/api/categories/categories\");\n        if (!response.ok) {\n          throw new Error(\"Failed to load categories\");\n        }\n        const data = await response.json();\n        setMenuData(data.slice(0, 6)); // Update with real categories\n      } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        // No need to set static categories here anymore, because it's already initialized\n      }\n    };\n    fetchCategories();\n  }, []);\n  const fetchSuggestions = async query => {\n    if (!query) {\n      setSuggestions([]); // Clear suggestions if input is emptyyyy.\n      return;\n    }\n    try {\n      const response = await axios.get(`https://api.thedesigngrit.com/api/products/search-suggestions?query=${query}`);\n      setSuggestions(response.data);\n    } catch (error) {\n      console.error(\"Error fetching suggestions:\", error);\n    }\n  };\n  const handleSearchChange = event => {\n    const value = event.target.value.trim();\n    setSearchQuery(value);\n    console.log(\"Search Query:\", value);\n    if (value === \"\") {\n      setSuggestions([]);\n      console.log(\"Suggestions cleared!\");\n      return;\n    }\n    fetchSuggestions(value);\n  };\n  const handleSuggestionClick = suggestion => {\n    setSearchQuery(suggestion.resultType === \"brand\" ? suggestion.brandName : suggestion.name);\n    setSuggestions([]);\n\n    // Navigate to different pages based on result type\n    if (suggestion.resultType === \"brand\") {\n      navigate(`/vendor/${suggestion._id}`); // Navigate to brand page\n    } else {\n      navigate(`/product/${suggestion._id}`); // Navigate to product page\n    }\n  };\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsSticky(window.scrollY > 80);\n    };\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 767); // Update state on window resize\n    };\n    window.addEventListener(\"resize\", handleResize); // Add resize event listener\n    return () => {\n      window.removeEventListener(\"resize\", handleResize); // Cleanup on unmount\n    };\n  }, []);\n  useEffect(() => {\n    // Fetch user data\n    const fetchData = async () => {\n      try {\n        const response = await axios.get(`https://api.thedesigngrit.com/api/getUserById/${userSession.id}`, {\n          withCredentials: true\n        });\n        console.log(\"userSession in Header:\", userSession);\n        setUserData(response.data);\n      } catch (error) {\n        console.error(\"Error fetching user data:\", error.response || error);\n      }\n    };\n    fetchData();\n  }, [userSession]);\n  useEffect(() => {\n    if (menuOpen) {\n      document.body.style.overflow = \"hidden\";\n      document.body.style.position = \"fixed\"; // Lock scrolling on iOS\n      document.body.style.width = \"100%\";\n    } else {\n      document.body.style.overflow = \"\";\n      document.body.style.position = \"\";\n      document.body.style.width = \"\";\n    }\n    return () => {\n      document.body.style.overflow = \"\";\n      document.body.style.position = \"\";\n      document.body.style.width = \"\";\n    };\n  }, [menuOpen]);\n  useEffect(() => {\n    const fetchFavoriteCount = async () => {\n      if (!userSession) {\n        setFavoriteCount(0);\n        return;\n      }\n      try {\n        const response = await fetch(`https://api.thedesigngrit.com/api/favorites/${userSession.id}`);\n        if (response.ok) {\n          const favoritesData = await response.json();\n          setFavoriteCount(favoritesData.length);\n          // Reset the update flag after fetching\n          if (favoriteUpdated) {\n            setFavoriteUpdated(false);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error fetching favorite count:\", error);\n      }\n    };\n    fetchFavoriteCount();\n  }, [userSession, favoriteUpdated, favoriteCount]);\n  const handleCartToggle = () => {\n    setCartOpen(prev => {\n      if (!prev) setFavoritesOpen(false); // Close favorites if cart is being opened\n      return !prev;\n    });\n  };\n  const handleFavoritesToggle = () => {\n    setFavoritesOpen(prev => {\n      if (!prev) setCartOpen(false); // Close cart if favorites is being opened\n      return !prev;\n    });\n  };\n  const handlePopupToggle = () => {\n    setPopupOpen(!popupOpen);\n  };\n  const handleMouseEnterCategory = category => {\n    setHoveredCategory(category._id);\n  };\n  const handleMouseLeaveCategory = () => {\n    if (!isMenuHovered) {\n      setHoveredCategory(null);\n    }\n  };\n  const handleMenuHover = () => {\n    setIsMenuHovered(true);\n  };\n  const handleMenuLeave = () => {\n    setIsMenuHovered(false);\n    setHoveredCategory(null); // Always close when leaving menu\n  };\n\n  // Update your toggleMenu function to this:\n  const openMenu = () => {\n    setMenuOpen(true);\n  };\n  const closeMenu = () => {\n    setMenuOpen(false);\n    closeShopDropdown();\n  };\n  const handleLoginClick = () => {\n    navigate(\"/login\"); // Redirect to login page\n  };\n  // Menu Toggle Handler\n  const handleAvatarClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const handleLogoutClick = () => {\n    setLogoutConfirmOpen(true);\n    handleMenuClose();\n  };\n  const handleLogoutConfirm = () => {\n    logout(); // Call logout from context\n    navigate(\"/home\"); // Redirect to home or login page\n    setLogoutConfirmOpen(false);\n  };\n  const handleLogoutCancel = () => {\n    setLogoutConfirmOpen(false);\n  };\n  const handleMyAccount = () => {\n    navigate(\"/myaccount\"); // Navigate to MyAccount page\n    handleMenuClose(); // Close the menu after clicking\n  };\n  const handleResize = () => {\n    setIsMobile(window.innerWidth < 767);\n  };\n  // Sticky Bounce Navbar Script\n\n  useEffect(() => {\n    window.addEventListener(\"resize\", handleResize);\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n  const handleShopClick = () => {\n    setCategoriesVisible(!categoriesVisible); // Toggle visibility of categories\n  };\n  const handleShopClose = () => {\n    setAnchorEls(null);\n  };\n\n  // Close shop dropdown\n  const closeShopDropdown = () => {\n    setShopDropdownOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      width: \"100%\"\n    },\n    className: `header-container ${isSticky ? \"sticky\" : \"\"}`,\n    children: [isMobile && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        width: \"100%\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/home\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/Assets/TDG_Logo_Black.webp\",\n          alt: \"Logo\",\n          width: 69,\n          height: 69 // Adjust based on actual aspect ratio\n          ,\n          priority: true // Ensures it loads as early as possible\n          ,\n          className: \"menu-logo\",\n          loading: \"lazy\",\n          style: {\n            width: \"69px\",\n            padding: \"12px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          gap: \"1rem\",\n          flexDirection: \"row\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleFavoritesToggle,\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              color: \"error\",\n              sx: {\n                \"& .MuiBadge-badge\": {\n                  backgroundColor: \"red\",\n                  animation: favoriteUpdated ? \"pulse 1s\" : \"none\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n                fontSize: \"20px\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleCartToggle,\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: totalCartItems,\n              color: \"error\",\n              children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                fontSize: \"20px\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: openMenu,\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: `header ${isSticky ? \"sticky\" : \"\"}`,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"header-top\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/home\",\n          style: {\n            textDecoration: \"none\",\n            color: \"#2d2d2d\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            className: \"logo\",\n            variant: \"h4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/Assets/TDG_Logo_Black.webp\",\n              alt: \"Logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), menuOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: `backdrop ${menuOpen ? \"open\" : \"\"}`,\n            onClick: openMenu,\n            sx: {\n              position: \"fixed\",\n              top: 0,\n              left: 0,\n              width: \"100%\",\n              height: \"100vh\",\n              // Make sure it spans the full screen\n              backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n              zIndex: 9998\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: `full-page-menu ${menuOpen ? \"open\" : \"\"}`,\n            sx: {\n              backgroundColor: \"white\",\n              height: \"100vh\",\n              // make sure it covers full height\n              touchAction: \"none\",\n              // <- Important to prevent scroll gestures\n              width: \"100%\",\n              maxHeight: \"100vh\",\n              overflowY: \"auto\",\n              zIndex: 9999,\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              justifyContent: \"flex-start\",\n              fontFamily: \"Montserrat\",\n              \"& .menu-content\": {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                gap: \"1rem\",\n                padding: \"1rem\"\n              },\n              \"& .menu-categories\": {\n                display: categoriesVisible ? \"flex\" : \"none\",\n                // Show or hide categories based on state\n                flexDirection: \"column\",\n                gap: \"0.5rem\",\n                marginTop: \"1rem\",\n                \"@media (max-width: 767px)\": {\n                  alignItems: \"center\"\n                }\n              }\n            },\n            onTouchMove: e => e.stopPropagation() // Prevent scroll propagation\n            ,\n            onClick: e => e.stopPropagation() // This prevents bubbling to outer Box\n            ,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"menu-header\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/home\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/Assets/TDG_Logo_Black.webp\",\n                  alt: \"Logo\",\n                  className: \"menu-logo\",\n                  style: {\n                    width: \"69px\",\n                    padding: \"12px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: closeMenu,\n                className: \"close-button\",\n                children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n                  fontSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"menu-content\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                onClick: () => navigate(\"/home\"),\n                className: \"menu-item\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                className: \"menu-item\",\n                \"aria-controls\": anchorEls ? \"shop-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                onClick: handleShopClick // Toggle categories visibility on click\n                ,\n                children: \"Shop\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: `menu-categories ${categoriesVisible ? \"open\" : \"\"}`,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"category\",\n                  onClick: () => navigate(\"/vendors\"),\n                  children: \"All Brands\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), menuData.length > 0 ? menuData.map(category => /*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"menu-category-item\",\n                  onClick: () => {\n                    navigate(`/category/${category._id}/subcategories`);\n                    handleShopClose();\n                  },\n                  children: category.name\n                }, category._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 25\n                }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n                  children: \"No Categories Available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"category\",\n                  onClick: () => navigate(\"/products/readytoship\"),\n                  children: \"Ready To Ship\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"category\",\n                  onClick: () => navigate(\"/products/onsale\"),\n                  children: \"On Sale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                onClick: () => navigate(\"/about\"),\n                className: \"menu-item\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                onClick: () => navigate(\"/contactus\"),\n                className: \"menu-item\",\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), userSession ? /*#__PURE__*/_jsxDEV(Typography, {\n                onClick: () => navigate(\"/myaccount\"),\n                className: \"menu-item\",\n                children: \"Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), userSession ? /*#__PURE__*/_jsxDEV(Typography, {\n                onClick: handleLogoutClick,\n                className: \"menu-item\",\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                onClick: handleLoginClick,\n                className: \"menu-item\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"shop-menu\",\n          anchorEls: anchorEls,\n          open: Boolean(anchorEls),\n          onClose: handleShopClose,\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          children: menuData.length > 0 ? menuData.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => {\n              navigate(`/shop/${category.slug}`);\n              handleShopClose();\n            },\n            children: category.name\n          }, category._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(MenuItem, {\n            disabled: true,\n            children: \"No Categories Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Box, {\n          className: \"search-bar\",\n          children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n            sx: {\n              color: \"#999\",\n              cursor: \"pointer\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InputBase, {\n            placeholder: \"Search by category, brand, product type, or name\",\n            fullWidth: true,\n            value: searchQuery,\n            onChange: handleSearchChange,\n            onBlur: () => setTimeout(() => setSuggestions([]), 200)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this), Array.isArray(suggestions) && suggestions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            className: \"suggestions-dropdown\",\n            children: suggestions.map(suggestion => /*#__PURE__*/_jsxDEV(Box, {\n              className: \"suggestion-item suggestion-item-large\",\n              onClick: () => handleSuggestionClick(suggestion),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"suggestion-image-large-container\",\n                children: suggestion.resultType === \"product\" && suggestion.mainImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${suggestion.mainImage}`,\n                  alt: suggestion.name,\n                  className: \"suggestion-image-large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 25\n                }, this) : suggestion.resultType === \"brand\" && suggestion.brandlogo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${suggestion.brandlogo}`,\n                  alt: suggestion.brandName,\n                  className: \"suggestion-image-large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                  className: \"suggestion-image-placeholder-large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"suggestion-info-large\",\n                children: suggestion.resultType === \"product\" ?\n                /*#__PURE__*/\n                // Product suggestion\n                _jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"suggestion-name-large\",\n                    children: suggestion.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"suggestion-meta-large\",\n                    children: [suggestion.category && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"suggestion-category-large\",\n                      children: suggestion.category.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 31\n                    }, this), suggestion.brandId && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"suggestion-brand-large\",\n                      children: suggestion.brandId.brandName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"suggestion-price-row-large\",\n                    children: suggestion.salePrice ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"suggestion-old-price-large\",\n                        children: [\"E\\xA3\", suggestion.price > 1000 ? new Intl.NumberFormat(\"en-US\", {\n                          minimumFractionDigits: 2,\n                          maximumFractionDigits: 2\n                        }).format(suggestion.price) : suggestion.price.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 656,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"suggestion-sale-price-large\",\n                        children: [\"E\\xA3\", suggestion.salePrice > 1000 ? new Intl.NumberFormat(\"en-US\", {\n                          minimumFractionDigits: 2,\n                          maximumFractionDigits: 2\n                        }).format(suggestion.salePrice) : suggestion.salePrice.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 665,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"suggestion-regular-price-large\",\n                      children: [\"E\\xA3\", suggestion.price > 1000 ? new Intl.NumberFormat(\"en-US\", {\n                        minimumFractionDigits: 2,\n                        maximumFractionDigits: 2\n                      }).format(suggestion.price) : suggestion.price.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) :\n                /*#__PURE__*/\n                // Brand suggestion\n                _jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"suggestion-name-large\",\n                    style: {\n                      fontWeight: \"bold\"\n                    },\n                    children: suggestion.brandName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"suggestion-category-large\",\n                    style: {\n                      fontStyle: \"italic\"\n                    },\n                    children: \"Brand\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this), suggestion.brandDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"suggestion-info-large\",\n                    style: {\n                      fontSize: \"0.9rem\"\n                    },\n                    children: [suggestion.brandDescription.substring(0, 60), suggestion.brandDescription.length > 60 ? \"...\" : \"\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 21\n              }, this)]\n            }, suggestion._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"icon-container\",\n          children: userSession ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: handleFavoritesToggle,\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: favoriteCount,\n                  color: \"error\",\n                  max: 99,\n                  sx: {\n                    \"& .MuiBadge-badge\": {\n                      backgroundColor: \"#f44336\",\n                      color: \"#fff\",\n                      fontWeight: \"bold\",\n                      fontSize: \"10px\",\n                      minWidth: \"18px\",\n                      height: \"18px\",\n                      padding: \"0 4px\",\n                      animation: favoriteUpdated ? \"pulse 1s\" : \"none\",\n                      \"@keyframes pulse\": {\n                        \"0%\": {\n                          transform: \"scale(1)\"\n                        },\n                        \"50%\": {\n                          transform: \"scale(1.2)\"\n                        },\n                        \"100%\": {\n                          transform: \"scale(1)\"\n                        }\n                      }\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n                    fontSize: \"20px\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: handleCartToggle,\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: totalCartItems,\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                    fontSize: \"20px\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              className: \"avatar\",\n              onClick: handleAvatarClick,\n              sx: {\n                cursor: \"pointer\",\n                width: \"30px\",\n                height: \"30px\",\n                marginTop: \"0px\"\n              },\n              children: (userData === null || userData === void 0 ? void 0 : (_userData$firstName = userData.firstName) === null || _userData$firstName === void 0 ? void 0 : (_userData$firstName$c = _userData$firstName.charAt(0)) === null || _userData$firstName$c === void 0 ? void 0 : _userData$firstName$c.toUpperCase()) || \"TDG\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Menu, {\n              anchorEl: anchorEl,\n              open: Boolean(anchorEl),\n              onClose: handleMenuClose,\n              anchorOrigin: {\n                vertical: \"bottom\",\n                horizontal: \"center\"\n              },\n              transformOrigin: {\n                vertical: \"top\",\n                horizontal: \"center\"\n              },\n              PaperProps: {\n                sx: {\n                  width: \"9%\",\n                  \"@media (min-width: 768px) and (max-width: 1199px)\": {\n                    width: \"20%\"\n                  },\n                  backdropFilter: \"blur(10px)\",\n                  backgroundColor: \"rgba(45, 45, 45, 0.1)\",\n                  borderRadius: 2,\n                  border: \"1px solid #fff\",\n                  boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.2)\",\n                  overflow: \"hidden\",\n                  position: \"relative\",\n                  \"::before\": {\n                    content: '\"\"',\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    border: \"1px solid #2d2d2d\",\n                    backgroundColor: \"#2f3a21\",\n                    opacity: 0.2,\n                    zIndex: 0\n                  },\n                  \"& .MuiMenuItem-root\": {\n                    position: \"relative\",\n                    zIndex: 1,\n                    color: \"#fff\"\n                  }\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                onClick: handleMyAccount,\n                children: \"My Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n                style: {\n                  color: \"rgba(255, 255, 255, 0.5)\",\n                  width: \"90%\",\n                  margin: \"0 auto\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                onClick: handleLogoutClick,\n                children: [/*#__PURE__*/_jsxDEV(IoLogOutOutline, {\n                  style: {\n                    marginRight: \"10px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 21\n                }, this), \" Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexDirection: \"row\",\n              alignItems: \"baseline\",\n              gap: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: handleFavoritesToggle,\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: favoriteCount,\n                  color: \"error\",\n                  max: 99,\n                  sx: {\n                    \"& .MuiBadge-badge\": {\n                      backgroundColor: \"#f44336\",\n                      color: \"#fff\",\n                      fontWeight: \"bold\",\n                      fontSize: \"10px\",\n                      minWidth: \"18px\",\n                      height: \"18px\",\n                      padding: \"0 4px\",\n                      animation: favoriteUpdated ? \"pulse 1s\" : \"none\",\n                      \"@keyframes pulse\": {\n                        \"0%\": {\n                          transform: \"scale(1)\"\n                        },\n                        \"50%\": {\n                          transform: \"scale(1.2)\"\n                        },\n                        \"100%\": {\n                          transform: \"scale(1)\"\n                        }\n                      }\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n                    fontSize: \"20px\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: handleCartToggle,\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: totalCartItems,\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {\n                    fontSize: \"20px\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              variant: \"contained\",\n              onClick: handleLoginClick,\n              className: \"Signup-btn-navbar\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"header-bottom\",\n      onMouseLeave: handleMouseLeaveCategory,\n      children: menuData.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No categories available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 890,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          className: \"category\",\n          onClick: () => navigate(\"/vendors\"),\n          children: \"All Brands\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 13\n        }, this), menuData.map(category => /*#__PURE__*/_jsxDEV(Typography, {\n          className: `category ${hoveredCategory === category._id ? \"highlighted\" : \"\"}`,\n          onMouseEnter: () => handleMouseEnterCategory(category),\n          children: category.name\n        }, category._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 15\n        }, this)), /*#__PURE__*/_jsxDEV(Typography, {\n          className: \"category\",\n          onClick: () => navigate(\"/products/readytoship\"),\n          children: \"Ready To Ship\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          className: \"category\",\n          onClick: () => navigate(\"/products/onsale\"),\n          children: \"On Sale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 917,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 892,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 7\n    }, this), hoveredCategory && /*#__PURE__*/_jsxDEV(Menudrop, {\n      category: menuData.find(item => item._id === hoveredCategory),\n      onMouseEnter: handleMenuHover,\n      onMouseLeave: handleMenuLeave\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 928,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ProfilePopup, {\n      open: popupOpen,\n      onClose: handlePopupToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 937,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ShoppingCartOverlay, {\n      open: cartOpen,\n      onClose: handleCartToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FavoritesOverlay, {\n      open: favoritesOpen,\n      onClose: handleFavoritesToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 939,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationDialog, {\n      open: logoutConfirmOpen,\n      title: \"Confirm Logout\",\n      content: \"Are you sure you want to logout?\",\n      onConfirm: handleLogoutConfirm,\n      onCancel: handleLogoutCancel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"MpGiICsLy3nMxlOoBATJojIMcJE=\", false, function () {\n  return [useCart, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Fragment", "Box", "Typography", "InputBase", "IconButton", "Avatar", "<PERSON><PERSON>", "MenuItem", "Badge", "SearchIcon", "CloseIcon", "ShoppingCartIcon", "FavoriteBorderIcon", "ConfirmationDialog", "ProfilePopup", "ShoppingCartOverlay", "FavoritesOverlay", "Menudrop", "Link", "useNavigate", "MenuIcon", "IoLogOutOutline", "UserContext", "axios", "useCart", "jsxDEV", "_jsxDEV", "_Fragment", "Header", "_s", "_userData$firstName", "_userData$firstName$c", "popupOpen", "setPopupOpen", "cartOpen", "setCartOpen", "favoritesOpen", "setFavoritesOpen", "logoutConfirmOpen", "setLogoutConfirmOpen", "hoveredCategory", "setHoveredCategory", "categoriesVisible", "setCategoriesVisible", "menuData", "setMenuData", "_id", "name", "isMenuHovered", "setIsMenuHovered", "menuOpen", "setMenuOpen", "isSticky", "setIsSticky", "anchorEl", "setAnchorEl", "cartItems", "anchorEls", "setAnchorEls", "userSession", "logout", "navigate", "isMobile", "setIsMobile", "window", "innerWidth", "userData", "setUserData", "firstName", "lastName", "email", "address1", "phoneNumber", "searchQuery", "setSearch<PERSON>uery", "suggestions", "setSuggestions", "setShopDropdownOpen", "favoriteCount", "setFavoriteCount", "favoriteUpdated", "setFavoriteUpdated", "totalCartItems", "reduce", "sum", "item", "quantity", "fetchCategories", "response", "fetch", "ok", "Error", "data", "json", "slice", "error", "console", "fetchSuggestions", "query", "get", "handleSearchChange", "event", "value", "target", "trim", "log", "handleSuggestionClick", "suggestion", "resultType", "brandName", "handleScroll", "scrollY", "addEventListener", "removeEventListener", "handleResize", "fetchData", "id", "withCredentials", "document", "body", "style", "overflow", "position", "width", "fetchFavoriteCount", "favoritesData", "length", "handleCartToggle", "prev", "handleFavoritesToggle", "handlePopupToggle", "handleMouseEnterCategory", "category", "handleMouseLeaveCategory", "handleMenuHover", "handleMenuLeave", "openMenu", "closeMenu", "closeShopDropdown", "handleLoginClick", "handleAvatarClick", "currentTarget", "handleMenuClose", "handleLogoutClick", "handleLogoutConfirm", "handleLogoutCancel", "handleMyAccount", "handleShopClick", "handleShopClose", "sx", "display", "flexDirection", "alignItems", "className", "children", "justifyContent", "to", "src", "alt", "height", "priority", "loading", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "onClick", "color", "backgroundColor", "animation", "fontSize", "badgeContent", "textDecoration", "variant", "top", "left", "zIndex", "touchAction", "maxHeight", "overflowY", "fontFamily", "marginTop", "onTouchMove", "e", "stopPropagation", "undefined", "map", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slug", "disabled", "cursor", "placeholder", "fullWidth", "onChange", "onBlur", "setTimeout", "Array", "isArray", "mainImage", "brandlogo", "brandId", "salePrice", "price", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "toFixed", "fontWeight", "fontStyle", "brandDescription", "substring", "max", "min<PERSON><PERSON><PERSON>", "transform", "char<PERSON>t", "toUpperCase", "PaperProps", "<PERSON><PERSON>ilter", "borderRadius", "border", "boxShadow", "content", "right", "bottom", "opacity", "margin", "marginRight", "onMouseLeave", "onMouseEnter", "find", "title", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/TDGweb/TDG/thedesigngrit/src/Components/navBar.jsx"], "sourcesContent": ["import React, { useState, useEffect, useContext, Fragment } from \"react\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  InputBase,\r\n  IconButton,\r\n  Avatar,\r\n  Menu,\r\n  MenuItem,\r\n  Badge,\r\n} from \"@mui/material\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ShoppingCartIcon from \"@mui/icons-material/ShoppingCart\";\r\nimport FavoriteBorderIcon from \"@mui/icons-material/FavoriteBorder\";\r\nimport ConfirmationDialog from \"./confirmationMsg\";\r\n// import FloatingButton from \"./ChatButton\";\r\nimport ProfilePopup from \"./profilePopUp\";\r\n// import Stickedbutton from \"./MoodboardButton\";\r\nimport ShoppingCartOverlay from \"./Popups/CartOverlay\";\r\nimport FavoritesOverlay from \"./favoriteOverlay\";\r\nimport Menudrop from \"./menuhover/Menudrop\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport MenuIcon from \"@mui/icons-material/Menu\";\r\n// Context for managing user session\r\nimport { IoLogOutOutline } from \"react-icons/io5\";\r\n\r\nimport { UserContext } from \"../utils/userContext\";\r\nimport axios from \"axios\";\r\nimport { useCart } from \"../Context/cartcontext\";\r\n\r\nfunction Header() {\r\n  const [popupOpen, setPopupOpen] = useState(false);\r\n  const [cartOpen, setCartOpen] = useState(false);\r\n  const [favoritesOpen, setFavoritesOpen] = useState(false);\r\n  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);\r\n  const [hoveredCategory, setHoveredCategory] = useState(null);\r\n  const [categoriesVisible, setCategoriesVisible] = useState(false); // State to toggle categories visibility\r\n  const [menuData, setMenuData] = useState([\r\n    { _id: \"static-1\", name: \"Furniture\" },\r\n    { _id: \"static-2\", name: \"Kitchen & Dining\" },\r\n    { _id: \"static-3\", name: \"Bath\" },\r\n    { _id: \"static-4\", name: \"Lighting\" },\r\n    { _id: \"static-5\", name: \"Home Decor\" },\r\n    { _id: \"static-6\", name: \"Outdoor\" },\r\n  ]);\r\n  const [isMenuHovered, setIsMenuHovered] = useState(false);\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [isSticky, setIsSticky] = useState(false);\r\n  const [anchorEl, setAnchorEl] = useState(null); // State for the avatar menu\r\n  const { cartItems } = useCart();\r\n  const [anchorEls, setAnchorEls] = useState(null); // State for the avatar menu\r\n  const { userSession, logout } = useContext(UserContext); // Access both userSession and setUserSession\r\n  const navigate = useNavigate(); // Hook for navigation\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 767);\r\n  const [userData, setUserData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    email: \"\",\r\n    address1: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [suggestions, setSuggestions] = useState([]);\r\n  // Add state for shop dropdown\r\n  const [setShopDropdownOpen] = useState(false);\r\n  const [favoriteCount, setFavoriteCount] = useState(0);\r\n  const [favoriteUpdated, setFavoriteUpdated] = useState(false);\r\n\r\n  const totalCartItems = cartItems.reduce(\r\n    (sum, item) => sum + item.quantity,\r\n    0\r\n  );\r\n\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          \"https://api.thedesigngrit.com/api/categories/categories\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to load categories\");\r\n        }\r\n        const data = await response.json();\r\n        setMenuData(data.slice(0, 6)); // Update with real categories\r\n      } catch (error) {\r\n        console.error(\"Error fetching categories:\", error);\r\n        // No need to set static categories here anymore, because it's already initialized\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  const fetchSuggestions = async (query) => {\r\n    if (!query) {\r\n      setSuggestions([]); // Clear suggestions if input is emptyyyy.\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await axios.get(\r\n        `https://api.thedesigngrit.com/api/products/search-suggestions?query=${query}`\r\n      );\r\n      setSuggestions(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching suggestions:\", error);\r\n    }\r\n  };\r\n\r\n  const handleSearchChange = (event) => {\r\n    const value = event.target.value.trim();\r\n    setSearchQuery(value);\r\n    console.log(\"Search Query:\", value);\r\n\r\n    if (value === \"\") {\r\n      setSuggestions([]);\r\n      console.log(\"Suggestions cleared!\");\r\n      return;\r\n    }\r\n\r\n    fetchSuggestions(value);\r\n  };\r\n\r\n  const handleSuggestionClick = (suggestion) => {\r\n    setSearchQuery(\r\n      suggestion.resultType === \"brand\" ? suggestion.brandName : suggestion.name\r\n    );\r\n    setSuggestions([]);\r\n\r\n    // Navigate to different pages based on result type\r\n    if (suggestion.resultType === \"brand\") {\r\n      navigate(`/vendor/${suggestion._id}`); // Navigate to brand page\r\n    } else {\r\n      navigate(`/product/${suggestion._id}`); // Navigate to product page\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsSticky(window.scrollY > 80);\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", handleScroll);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"scroll\", handleScroll);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 767); // Update state on window resize\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize); // Add resize event listener\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize); // Cleanup on unmount\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Fetch user data\r\n    const fetchData = async () => {\r\n      try {\r\n        const response = await axios.get(\r\n          `https://api.thedesigngrit.com/api/getUserById/${userSession.id}`,\r\n          {\r\n            withCredentials: true,\r\n          }\r\n        );\r\n        console.log(\"userSession in Header:\", userSession);\r\n        setUserData(response.data);\r\n      } catch (error) {\r\n        console.error(\"Error fetching user data:\", error.response || error);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [userSession]);\r\n  useEffect(() => {\r\n    if (menuOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n      document.body.style.position = \"fixed\"; // Lock scrolling on iOS\r\n      document.body.style.width = \"100%\";\r\n    } else {\r\n      document.body.style.overflow = \"\";\r\n      document.body.style.position = \"\";\r\n      document.body.style.width = \"\";\r\n    }\r\n\r\n    return () => {\r\n      document.body.style.overflow = \"\";\r\n      document.body.style.position = \"\";\r\n      document.body.style.width = \"\";\r\n    };\r\n  }, [menuOpen]);\r\n  useEffect(() => {\r\n    const fetchFavoriteCount = async () => {\r\n      if (!userSession) {\r\n        setFavoriteCount(0);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await fetch(\r\n          `https://api.thedesigngrit.com/api/favorites/${userSession.id}`\r\n        );\r\n        if (response.ok) {\r\n          const favoritesData = await response.json();\r\n          setFavoriteCount(favoritesData.length);\r\n          // Reset the update flag after fetching\r\n          if (favoriteUpdated) {\r\n            setFavoriteUpdated(false);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching favorite count:\", error);\r\n      }\r\n    };\r\n\r\n    fetchFavoriteCount();\r\n  }, [userSession, favoriteUpdated, favoriteCount]);\r\n\r\n  const handleCartToggle = () => {\r\n    setCartOpen((prev) => {\r\n      if (!prev) setFavoritesOpen(false); // Close favorites if cart is being opened\r\n      return !prev;\r\n    });\r\n  };\r\n\r\n  const handleFavoritesToggle = () => {\r\n    setFavoritesOpen((prev) => {\r\n      if (!prev) setCartOpen(false); // Close cart if favorites is being opened\r\n      return !prev;\r\n    });\r\n  };\r\n\r\n  const handlePopupToggle = () => {\r\n    setPopupOpen(!popupOpen);\r\n  };\r\n\r\n  const handleMouseEnterCategory = (category) => {\r\n    setHoveredCategory(category._id);\r\n  };\r\n\r\n  const handleMouseLeaveCategory = () => {\r\n    if (!isMenuHovered) {\r\n      setHoveredCategory(null);\r\n    }\r\n  };\r\n  const handleMenuHover = () => {\r\n    setIsMenuHovered(true);\r\n  };\r\n\r\n  const handleMenuLeave = () => {\r\n    setIsMenuHovered(false);\r\n    setHoveredCategory(null); // Always close when leaving menu\r\n  };\r\n\r\n  // Update your toggleMenu function to this:\r\n  const openMenu = () => {\r\n    setMenuOpen(true);\r\n  };\r\n\r\n  const closeMenu = () => {\r\n    setMenuOpen(false);\r\n    closeShopDropdown();\r\n  };\r\n  const handleLoginClick = () => {\r\n    navigate(\"/login\"); // Redirect to login page\r\n  };\r\n  // Menu Toggle Handler\r\n  const handleAvatarClick = (event) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleMenuClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  const handleLogoutClick = () => {\r\n    setLogoutConfirmOpen(true);\r\n    handleMenuClose();\r\n  };\r\n\r\n  const handleLogoutConfirm = () => {\r\n    logout(); // Call logout from context\r\n    navigate(\"/home\"); // Redirect to home or login page\r\n    setLogoutConfirmOpen(false);\r\n  };\r\n\r\n  const handleLogoutCancel = () => {\r\n    setLogoutConfirmOpen(false);\r\n  };\r\n\r\n  const handleMyAccount = () => {\r\n    navigate(\"/myaccount\"); // Navigate to MyAccount page\r\n    handleMenuClose(); // Close the menu after clicking\r\n  };\r\n\r\n  const handleResize = () => {\r\n    setIsMobile(window.innerWidth < 767);\r\n  };\r\n  // Sticky Bounce Navbar Script\r\n\r\n  useEffect(() => {\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n  const handleShopClick = () => {\r\n    setCategoriesVisible(!categoriesVisible); // Toggle visibility of categories\r\n  };\r\n\r\n  const handleShopClose = () => {\r\n    setAnchorEls(null);\r\n  };\r\n\r\n  // Close shop dropdown\r\n  const closeShopDropdown = () => {\r\n    setShopDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"center\",\r\n        width: \"100%\",\r\n      }}\r\n      className={`header-container ${isSticky ? \"sticky\" : \"\"}`}\r\n    >\r\n      {isMobile && (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"space-between\",\r\n            width: \"100%\",\r\n          }}\r\n        >\r\n          <Link to=\"/home\">\r\n            <img\r\n              src=\"/Assets/TDG_Logo_Black.webp\"\r\n              alt=\"Logo\"\r\n              width={69}\r\n              height={69} // Adjust based on actual aspect ratio\r\n              priority // Ensures it loads as early as possible\r\n              className=\"menu-logo\"\r\n              loading=\"lazy\"\r\n              style={{ width: \"69px\", padding: \"12px\" }}\r\n            />\r\n          </Link>\r\n          <Box sx={{ display: \"flex\", gap: \"1rem\", flexDirection: \"row\" }}>\r\n            <div>\r\n              <IconButton onClick={handleFavoritesToggle}>\r\n                <Badge\r\n                  color=\"error\"\r\n                  sx={{\r\n                    \"& .MuiBadge-badge\": {\r\n                      backgroundColor: \"red\",\r\n                      animation: favoriteUpdated ? \"pulse 1s\" : \"none\",\r\n                    },\r\n                  }}\r\n                >\r\n                  <FavoriteBorderIcon fontSize=\"20px\" />\r\n                </Badge>\r\n              </IconButton>\r\n              <IconButton onClick={handleCartToggle}>\r\n                <Badge badgeContent={totalCartItems} color=\"error\">\r\n                  <ShoppingCartIcon fontSize=\"20px\" />\r\n                </Badge>\r\n              </IconButton>\r\n            </div>\r\n            <IconButton onClick={openMenu}>\r\n              <MenuIcon />\r\n            </IconButton>\r\n          </Box>\r\n        </Box>\r\n      )}\r\n      {/* Top Header */}\r\n      <Box className={`header ${isSticky ? \"sticky\" : \"\"}`}>\r\n        <Box className=\"header-top\">\r\n          {/* Logo */}\r\n          <Link to=\"/home\" style={{ textDecoration: \"none\", color: \"#2d2d2d\" }}>\r\n            <Typography className=\"logo\" variant=\"h4\">\r\n              <img src=\"/Assets/TDG_Logo_Black.webp\" alt=\"Logo\" />\r\n            </Typography>\r\n          </Link>\r\n          {/* Full-Screen Mobile Menu */}\r\n          {/* Full-Screen Mobile Menu */}\r\n          {menuOpen && (\r\n            <>\r\n              {/* Backdrop */}\r\n              <Box\r\n                className={`backdrop ${menuOpen ? \"open\" : \"\"}`}\r\n                onClick={openMenu}\r\n                sx={{\r\n                  position: \"fixed\",\r\n                  top: 0,\r\n                  left: 0,\r\n                  width: \"100%\",\r\n                  height: \"100vh\", // Make sure it spans the full screen\r\n                  backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n                  zIndex: 9998,\r\n                }}\r\n              />\r\n              <Box\r\n                className={`full-page-menu ${menuOpen ? \"open\" : \"\"}`}\r\n                sx={{\r\n                  backgroundColor: \"white\",\r\n                  height: \"100vh\", // make sure it covers full height\r\n                  touchAction: \"none\", // <- Important to prevent scroll gestures\r\n                  width: \"100%\",\r\n                  maxHeight: \"100vh\",\r\n                  overflowY: \"auto\",\r\n                  zIndex: 9999,\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"flex-start\",\r\n                  fontFamily: \"Montserrat\",\r\n                  \"& .menu-content\": {\r\n                    display: \"flex\",\r\n                    flexDirection: \"column\",\r\n                    alignItems: \"center\",\r\n                    gap: \"1rem\",\r\n                    padding: \"1rem\",\r\n                  },\r\n                  \"& .menu-categories\": {\r\n                    display: categoriesVisible ? \"flex\" : \"none\", // Show or hide categories based on state\r\n                    flexDirection: \"column\",\r\n                    gap: \"0.5rem\",\r\n                    marginTop: \"1rem\",\r\n                    \"@media (max-width: 767px)\": {\r\n                      alignItems: \"center\",\r\n                    },\r\n                  },\r\n                }}\r\n                onTouchMove={(e) => e.stopPropagation()} // Prevent scroll propagation\r\n                onClick={(e) => e.stopPropagation()} // This prevents bubbling to outer Box\r\n              >\r\n                <Box className=\"menu-header\">\r\n                  <Link to=\"/home\">\r\n                    <img\r\n                      src=\"/Assets/TDG_Logo_Black.webp\"\r\n                      alt=\"Logo\"\r\n                      className=\"menu-logo\"\r\n                      style={{ width: \"69px\", padding: \"12px\" }}\r\n                    />\r\n                  </Link>\r\n                  <IconButton onClick={closeMenu} className=\"close-button\">\r\n                    <CloseIcon fontSize=\"large\" />\r\n                  </IconButton>\r\n                </Box>\r\n\r\n                <Box className=\"menu-content\">\r\n                  <Typography\r\n                    onClick={() => navigate(\"/home\")}\r\n                    className=\"menu-item\"\r\n                  >\r\n                    Home\r\n                  </Typography>\r\n                  <Typography\r\n                    className=\"menu-item\"\r\n                    aria-controls={anchorEls ? \"shop-menu\" : undefined}\r\n                    aria-haspopup=\"true\"\r\n                    onClick={handleShopClick} // Toggle categories visibility on click\r\n                  >\r\n                    Shop\r\n                  </Typography>\r\n\r\n                  {/* Categories */}\r\n                  <Box\r\n                    className={`menu-categories ${\r\n                      categoriesVisible ? \"open\" : \"\"\r\n                    }`}\r\n                  >\r\n                    <Typography\r\n                      className=\"category\"\r\n                      onClick={() => navigate(\"/vendors\")}\r\n                    >\r\n                      All Brands\r\n                    </Typography>\r\n\r\n                    {menuData.length > 0 ? (\r\n                      menuData.map((category) => (\r\n                        <Typography\r\n                          key={category._id}\r\n                          className=\"menu-category-item\"\r\n                          onClick={() => {\r\n                            navigate(`/category/${category._id}/subcategories`);\r\n                            handleShopClose();\r\n                          }}\r\n                        >\r\n                          {category.name}\r\n                        </Typography>\r\n                      ))\r\n                    ) : (\r\n                      <Typography>No Categories Available</Typography>\r\n                    )}\r\n                    <Typography\r\n                      className=\"category\"\r\n                      onClick={() => navigate(\"/products/readytoship\")}\r\n                    >\r\n                      Ready To Ship\r\n                    </Typography>\r\n                    <Typography\r\n                      className=\"category\"\r\n                      onClick={() => navigate(\"/products/onsale\")}\r\n                    >\r\n                      On Sale\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  <Typography\r\n                    onClick={() => navigate(\"/about\")}\r\n                    className=\"menu-item\"\r\n                  >\r\n                    About\r\n                  </Typography>\r\n                  <Typography\r\n                    onClick={() => navigate(\"/contactus\")}\r\n                    className=\"menu-item\"\r\n                  >\r\n                    Contact\r\n                  </Typography>\r\n                  {userSession ? (\r\n                    <Typography\r\n                      onClick={() => navigate(\"/myaccount\")}\r\n                      className=\"menu-item\"\r\n                    >\r\n                      Account\r\n                    </Typography>\r\n                  ) : (\r\n                    <></>\r\n                  )}\r\n\r\n                  {userSession ? (\r\n                    <Typography\r\n                      onClick={handleLogoutClick}\r\n                      className=\"menu-item\"\r\n                    >\r\n                      Logout\r\n                    </Typography>\r\n                  ) : (\r\n                    <Typography\r\n                      onClick={handleLoginClick}\r\n                      className=\"menu-item\"\r\n                    >\r\n                      Login\r\n                    </Typography>\r\n                  )}\r\n                </Box>\r\n              </Box>\r\n            </>\r\n          )}\r\n          {/* Dropdown Menu for Shop */}\r\n          <Menu\r\n            id=\"shop-menu\"\r\n            anchorEls={anchorEls}\r\n            open={Boolean(anchorEls)}\r\n            onClose={handleShopClose}\r\n            anchorOrigin={{\r\n              vertical: \"bottom\",\r\n              horizontal: \"left\",\r\n            }}\r\n            transformOrigin={{\r\n              vertical: \"top\",\r\n              horizontal: \"left\",\r\n            }}\r\n          >\r\n            {menuData.length > 0 ? (\r\n              menuData.map((category) => (\r\n                <MenuItem\r\n                  key={category._id}\r\n                  onClick={() => {\r\n                    navigate(`/shop/${category.slug}`);\r\n                    handleShopClose();\r\n                  }}\r\n                >\r\n                  {category.name}\r\n                </MenuItem>\r\n              ))\r\n            ) : (\r\n              <MenuItem disabled>No Categories Available</MenuItem>\r\n            )}\r\n          </Menu>{\" \"}\r\n          {/* Search */}\r\n          <Box className=\"search-bar\">\r\n            <SearchIcon sx={{ color: \"#999\", cursor: \"pointer\" }} />\r\n            <InputBase\r\n              placeholder=\"Search by category, brand, product type, or name\"\r\n              fullWidth\r\n              value={searchQuery}\r\n              onChange={handleSearchChange}\r\n              onBlur={() => setTimeout(() => setSuggestions([]), 200)}\r\n            />\r\n\r\n            {/* Suggestion Dropdown */}\r\n            {Array.isArray(suggestions) && suggestions.length > 0 && (\r\n              <Box className=\"suggestions-dropdown\">\r\n                {suggestions.map((suggestion) => (\r\n                  <Box\r\n                    key={suggestion._id}\r\n                    className=\"suggestion-item suggestion-item-large\"\r\n                    onClick={() => handleSuggestionClick(suggestion)}\r\n                  >\r\n                    {/* Image - handle both product and brand */}\r\n                    <div className=\"suggestion-image-large-container\">\r\n                      {suggestion.resultType === \"product\" &&\r\n                      suggestion.mainImage ? (\r\n                        <img\r\n                          src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${suggestion.mainImage}`}\r\n                          alt={suggestion.name}\r\n                          className=\"suggestion-image-large\"\r\n                        />\r\n                      ) : suggestion.resultType === \"brand\" &&\r\n                        suggestion.brandlogo ? (\r\n                        <img\r\n                          src={`https://pub-03f15f93661b46629dc2abcc2c668d72.r2.dev/${suggestion.brandlogo}`}\r\n                          alt={suggestion.brandName}\r\n                          className=\"suggestion-image-large\"\r\n                        />\r\n                      ) : (\r\n                        <Box className=\"suggestion-image-placeholder-large\"></Box>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Content - handle both product and brand */}\r\n                    <Box className=\"suggestion-info-large\">\r\n                      {suggestion.resultType === \"product\" ? (\r\n                        // Product suggestion\r\n                        <>\r\n                          <div className=\"suggestion-name-large\">\r\n                            {suggestion.name}\r\n                          </div>\r\n                          <div className=\"suggestion-meta-large\">\r\n                            {suggestion.category && (\r\n                              <span className=\"suggestion-category-large\">\r\n                                {suggestion.category.name}\r\n                              </span>\r\n                            )}\r\n                            {suggestion.brandId && (\r\n                              <span className=\"suggestion-brand-large\">\r\n                                {suggestion.brandId.brandName}\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                          <div className=\"suggestion-price-row-large\">\r\n                            {suggestion.salePrice ? (\r\n                              <>\r\n                                <span className=\"suggestion-old-price-large\">\r\n                                  E£\r\n                                  {suggestion.price > 1000\r\n                                    ? new Intl.NumberFormat(\"en-US\", {\r\n                                        minimumFractionDigits: 2,\r\n                                        maximumFractionDigits: 2,\r\n                                      }).format(suggestion.price)\r\n                                    : suggestion.price.toFixed(2)}\r\n                                </span>\r\n                                <span className=\"suggestion-sale-price-large\">\r\n                                  E£\r\n                                  {suggestion.salePrice > 1000\r\n                                    ? new Intl.NumberFormat(\"en-US\", {\r\n                                        minimumFractionDigits: 2,\r\n                                        maximumFractionDigits: 2,\r\n                                      }).format(suggestion.salePrice)\r\n                                    : suggestion.salePrice.toFixed(2)}\r\n                                </span>\r\n                              </>\r\n                            ) : (\r\n                              <span className=\"suggestion-regular-price-large\">\r\n                                E£\r\n                                {suggestion.price > 1000\r\n                                  ? new Intl.NumberFormat(\"en-US\", {\r\n                                      minimumFractionDigits: 2,\r\n                                      maximumFractionDigits: 2,\r\n                                    }).format(suggestion.price)\r\n                                  : suggestion.price.toFixed(2)}\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        </>\r\n                      ) : (\r\n                        // Brand suggestion\r\n                        <>\r\n                          <div\r\n                            className=\"suggestion-name-large\"\r\n                            style={{ fontWeight: \"bold\" }}\r\n                          >\r\n                            {suggestion.brandName}\r\n                          </div>\r\n                          <div\r\n                            className=\"suggestion-category-large\"\r\n                            style={{ fontStyle: \"italic\" }}\r\n                          >\r\n                            Brand\r\n                          </div>\r\n                          {suggestion.brandDescription && (\r\n                            <div\r\n                              className=\"suggestion-info-large\"\r\n                              style={{ fontSize: \"0.9rem\" }}\r\n                            >\r\n                              {suggestion.brandDescription.substring(0, 60)}\r\n                              {suggestion.brandDescription.length > 60\r\n                                ? \"...\"\r\n                                : \"\"}\r\n                            </div>\r\n                          )}\r\n                        </>\r\n                      )}\r\n                    </Box>\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            )}\r\n          </Box>\r\n          {/* Icons */}\r\n          <Box className=\"icon-container\">\r\n            {userSession ? (\r\n              <>\r\n                <div>\r\n                  <IconButton onClick={handleFavoritesToggle}>\r\n                    <Badge\r\n                      badgeContent={favoriteCount}\r\n                      color=\"error\"\r\n                      max={99}\r\n                      sx={{\r\n                        \"& .MuiBadge-badge\": {\r\n                          backgroundColor: \"#f44336\",\r\n                          color: \"#fff\",\r\n                          fontWeight: \"bold\",\r\n                          fontSize: \"10px\",\r\n                          minWidth: \"18px\",\r\n                          height: \"18px\",\r\n                          padding: \"0 4px\",\r\n                          animation: favoriteUpdated ? \"pulse 1s\" : \"none\",\r\n                          \"@keyframes pulse\": {\r\n                            \"0%\": { transform: \"scale(1)\" },\r\n                            \"50%\": { transform: \"scale(1.2)\" },\r\n                            \"100%\": { transform: \"scale(1)\" },\r\n                          },\r\n                        },\r\n                      }}\r\n                    >\r\n                      <FavoriteBorderIcon fontSize=\"20px\" />\r\n                    </Badge>\r\n                  </IconButton>\r\n                  <IconButton onClick={handleCartToggle}>\r\n                    <Badge badgeContent={totalCartItems} color=\"error\">\r\n                      <ShoppingCartIcon fontSize=\"20px\" />\r\n                    </Badge>\r\n                  </IconButton>\r\n                </div>\r\n                <Avatar\r\n                  className=\"avatar\"\r\n                  onClick={handleAvatarClick}\r\n                  sx={{\r\n                    cursor: \"pointer\",\r\n                    width: \"30px\",\r\n                    height: \"30px\",\r\n                    marginTop: \"0px\",\r\n                  }}\r\n                >\r\n                  {userData?.firstName?.charAt(0)?.toUpperCase() || \"TDG\"}\r\n                </Avatar>\r\n\r\n                {/* Avatar Menu */}\r\n                <Menu\r\n                  anchorEl={anchorEl}\r\n                  open={Boolean(anchorEl)}\r\n                  onClose={handleMenuClose}\r\n                  anchorOrigin={{\r\n                    vertical: \"bottom\",\r\n                    horizontal: \"center\",\r\n                  }}\r\n                  transformOrigin={{\r\n                    vertical: \"top\",\r\n                    horizontal: \"center\",\r\n                  }}\r\n                  PaperProps={{\r\n                    sx: {\r\n                      width: \"9%\",\r\n                      \"@media (min-width: 768px) and (max-width: 1199px)\": {\r\n                        width: \"20%\",\r\n                      },\r\n                      backdropFilter: \"blur(10px)\",\r\n                      backgroundColor: \"rgba(45, 45, 45, 0.1)\",\r\n                      borderRadius: 2,\r\n                      border: \"1px solid #fff\",\r\n                      boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.2)\",\r\n                      overflow: \"hidden\",\r\n                      position: \"relative\",\r\n                      \"::before\": {\r\n                        content: '\"\"',\r\n                        position: \"absolute\",\r\n                        top: 0,\r\n                        left: 0,\r\n                        right: 0,\r\n                        bottom: 0,\r\n                        border: \"1px solid #2d2d2d\",\r\n                        backgroundColor: \"#2f3a21\",\r\n\r\n                        opacity: 0.2,\r\n                        zIndex: 0,\r\n                      },\r\n                      \"& .MuiMenuItem-root\": {\r\n                        position: \"relative\",\r\n                        zIndex: 1,\r\n                        color: \"#fff\",\r\n                      },\r\n                    },\r\n                  }}\r\n                >\r\n                  <MenuItem onClick={handleMyAccount}>My Account</MenuItem>\r\n                  <hr\r\n                    style={{\r\n                      color: \"rgba(255, 255, 255, 0.5)\",\r\n                      width: \"90%\",\r\n                      margin: \"0 auto\",\r\n                    }}\r\n                  />\r\n                  <MenuItem onClick={handleLogoutClick}>\r\n                    <IoLogOutOutline style={{ marginRight: \"10px\" }} /> Logout\r\n                  </MenuItem>\r\n                </Menu>\r\n              </>\r\n            ) : (\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"row\",\r\n                  alignItems: \"baseline\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                <div>\r\n                  <IconButton onClick={handleFavoritesToggle}>\r\n                    <Badge\r\n                      badgeContent={favoriteCount}\r\n                      color=\"error\"\r\n                      max={99}\r\n                      sx={{\r\n                        \"& .MuiBadge-badge\": {\r\n                          backgroundColor: \"#f44336\",\r\n                          color: \"#fff\",\r\n                          fontWeight: \"bold\",\r\n                          fontSize: \"10px\",\r\n                          minWidth: \"18px\",\r\n                          height: \"18px\",\r\n                          padding: \"0 4px\",\r\n                          animation: favoriteUpdated ? \"pulse 1s\" : \"none\",\r\n                          \"@keyframes pulse\": {\r\n                            \"0%\": { transform: \"scale(1)\" },\r\n                            \"50%\": { transform: \"scale(1.2)\" },\r\n                            \"100%\": { transform: \"scale(1)\" },\r\n                          },\r\n                        },\r\n                      }}\r\n                    >\r\n                      <FavoriteBorderIcon fontSize=\"20px\" />\r\n                    </Badge>\r\n                  </IconButton>\r\n                  <IconButton onClick={handleCartToggle}>\r\n                    <Badge badgeContent={totalCartItems} color=\"error\">\r\n                      <ShoppingCartIcon fontSize=\"20px\" />\r\n                    </Badge>\r\n                  </IconButton>\r\n                </div>\r\n                <button\r\n                  variant=\"contained\"\r\n                  onClick={handleLoginClick}\r\n                  className=\"Signup-btn-navbar\"\r\n                >\r\n                  Login\r\n                </button>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Categories */}\r\n      <Box className=\"header-bottom\" onMouseLeave={handleMouseLeaveCategory}>\r\n        {menuData.length === 0 ? (\r\n          <Typography>No categories available</Typography>\r\n        ) : (\r\n          <Fragment>\r\n            <Typography\r\n              className=\"category\"\r\n              onClick={() => navigate(\"/vendors\")}\r\n            >\r\n              All Brands\r\n            </Typography>\r\n\r\n            {menuData.map((category) => (\r\n              <Typography\r\n                key={category._id}\r\n                className={`category ${\r\n                  hoveredCategory === category._id ? \"highlighted\" : \"\"\r\n                }`}\r\n                onMouseEnter={() => handleMouseEnterCategory(category)}\r\n              >\r\n                {category.name}\r\n              </Typography>\r\n            ))}\r\n            <Typography\r\n              className=\"category\"\r\n              onClick={() => navigate(\"/products/readytoship\")}\r\n            >\r\n              Ready To Ship\r\n            </Typography>\r\n            <Typography\r\n              className=\"category\"\r\n              onClick={() => navigate(\"/products/onsale\")}\r\n            >\r\n              On Sale\r\n            </Typography>\r\n          </Fragment>\r\n        )}\r\n      </Box>\r\n\r\n      {hoveredCategory && (\r\n        <Menudrop\r\n          category={menuData.find((item) => item._id === hoveredCategory)}\r\n          onMouseEnter={handleMenuHover}\r\n          onMouseLeave={handleMenuLeave}\r\n        />\r\n      )}\r\n\r\n      {/* <FloatingButton />\r\n      <Stickedbutton className=\"moodboard-btn\" /> */}\r\n      <ProfilePopup open={popupOpen} onClose={handlePopupToggle} />\r\n      <ShoppingCartOverlay open={cartOpen} onClose={handleCartToggle} />\r\n      <FavoritesOverlay open={favoritesOpen} onClose={handleFavoritesToggle} />\r\n      <ConfirmationDialog\r\n        open={logoutConfirmOpen}\r\n        title=\"Confirm Logout\"\r\n        content=\"Are you sure you want to logout?\"\r\n        onConfirm={handleLogoutConfirm}\r\n        onCancel={handleLogoutCancel}\r\n      />\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACxE,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,kBAAkB,MAAM,mBAAmB;AAClD;AACA,OAAOC,YAAY,MAAM,gBAAgB;AACzC;AACA,OAAOC,mBAAmB,MAAM,sBAAsB;AACtD,OAAOC,gBAAgB,MAAM,mBAAmB;AAChD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C;AACA,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAA1B,QAAA,IAAA2B,SAAA;AAEjD,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,qBAAA;EAChB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,CACvC;IAAEiD,GAAG,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACtC;IAAED,GAAG,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAmB,CAAC,EAC7C;IAAED,GAAG,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAO,CAAC,EACjC;IAAED,GAAG,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACrC;IAAED,GAAG,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAa,CAAC,EACvC;IAAED,GAAG,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAU,CAAC,CACrC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM;IAAE2D;EAAU,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAC/B,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM;IAAE8D,WAAW;IAAEC;EAAO,CAAC,GAAG7D,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC;EACzD,MAAMuC,QAAQ,GAAG1C,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAACmE,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;EACjE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC;IACvCuE,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAACgF,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMqF,cAAc,GAAG1B,SAAS,CAAC2B,MAAM,CACrC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,QAAQ,EAClC,CACF,CAAC;EAEDxF,SAAS,CAAC,MAAM;IACd,MAAMyF,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,yDACF,CAAC;QACD,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;QAC9C;QACA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClChD,WAAW,CAAC+C,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;MACF;IACF,CAAC;IAEDR,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,gBAAgB,GAAG,MAAOC,KAAK,IAAK;IACxC,IAAI,CAACA,KAAK,EAAE;MACVtB,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;IACF;IAEA,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMjE,KAAK,CAAC4E,GAAG,CAC9B,uEAAuED,KAAK,EAC9E,CAAC;MACDtB,cAAc,CAACY,QAAQ,CAACI,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC;IACvC9B,cAAc,CAAC4B,KAAK,CAAC;IACrBN,OAAO,CAACS,GAAG,CAAC,eAAe,EAAEH,KAAK,CAAC;IAEnC,IAAIA,KAAK,KAAK,EAAE,EAAE;MAChB1B,cAAc,CAAC,EAAE,CAAC;MAClBoB,OAAO,CAACS,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEAR,gBAAgB,CAACK,KAAK,CAAC;EACzB,CAAC;EAED,MAAMI,qBAAqB,GAAIC,UAAU,IAAK;IAC5CjC,cAAc,CACZiC,UAAU,CAACC,UAAU,KAAK,OAAO,GAAGD,UAAU,CAACE,SAAS,GAAGF,UAAU,CAAC5D,IACxE,CAAC;IACD6B,cAAc,CAAC,EAAE,CAAC;;IAElB;IACA,IAAI+B,UAAU,CAACC,UAAU,KAAK,OAAO,EAAE;MACrC/C,QAAQ,CAAC,WAAW8C,UAAU,CAAC7D,GAAG,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC,MAAM;MACLe,QAAQ,CAAC,YAAY8C,UAAU,CAAC7D,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C;EACF,CAAC;EAEDhD,SAAS,CAAC,MAAM;IACd,MAAMgH,YAAY,GAAGA,CAAA,KAAM;MACzBzD,WAAW,CAACW,MAAM,CAAC+C,OAAO,GAAG,EAAE,CAAC;IAClC,CAAC;IAED/C,MAAM,CAACgD,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAE/C,OAAO,MAAM;MACX9C,MAAM,CAACiD,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENhH,SAAS,CAAC,MAAM;IACd,MAAMoH,YAAY,GAAGA,CAAA,KAAM;MACzBnD,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC;IAEDD,MAAM,CAACgD,gBAAgB,CAAC,QAAQ,EAAEE,YAAY,CAAC,CAAC,CAAC;IACjD,OAAO,MAAM;MACXlD,MAAM,CAACiD,mBAAmB,CAAC,QAAQ,EAAEC,YAAY,CAAC,CAAC,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENpH,SAAS,CAAC,MAAM;IACd;IACA,MAAMqH,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAM3B,QAAQ,GAAG,MAAMjE,KAAK,CAAC4E,GAAG,CAC9B,iDAAiDxC,WAAW,CAACyD,EAAE,EAAE,EACjE;UACEC,eAAe,EAAE;QACnB,CACF,CAAC;QACDrB,OAAO,CAACS,GAAG,CAAC,wBAAwB,EAAE9C,WAAW,CAAC;QAClDQ,WAAW,CAACqB,QAAQ,CAACI,IAAI,CAAC;MAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACP,QAAQ,IAAIO,KAAK,CAAC;MACrE;IACF,CAAC;IAEDoB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACxD,WAAW,CAAC,CAAC;EACjB7D,SAAS,CAAC,MAAM;IACd,IAAIoD,QAAQ,EAAE;MACZoE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;MACvCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,OAAO,CAAC,CAAC;MACxCJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,MAAM;IACpC,CAAC,MAAM;MACLL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;MACjCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,EAAE;MACjCJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,EAAE;IAChC;IAEA,OAAO,MAAM;MACXL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;MACjCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,EAAE;MACjCJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,EAAE;IAChC,CAAC;EACH,CAAC,EAAE,CAACzE,QAAQ,CAAC,CAAC;EACdpD,SAAS,CAAC,MAAM;IACd,MAAM8H,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI,CAACjE,WAAW,EAAE;QAChBoB,gBAAgB,CAAC,CAAC,CAAC;QACnB;MACF;MAEA,IAAI;QACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAC1B,+CAA+C9B,WAAW,CAACyD,EAAE,EAC/D,CAAC;QACD,IAAI5B,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMmC,aAAa,GAAG,MAAMrC,QAAQ,CAACK,IAAI,CAAC,CAAC;UAC3Cd,gBAAgB,CAAC8C,aAAa,CAACC,MAAM,CAAC;UACtC;UACA,IAAI9C,eAAe,EAAE;YACnBC,kBAAkB,CAAC,KAAK,CAAC;UAC3B;QACF;MACF,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;IACF,CAAC;IAED6B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACjE,WAAW,EAAEqB,eAAe,EAAEF,aAAa,CAAC,CAAC;EAEjD,MAAMiD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5F,WAAW,CAAE6F,IAAI,IAAK;MACpB,IAAI,CAACA,IAAI,EAAE3F,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;MACpC,OAAO,CAAC2F,IAAI;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC5F,gBAAgB,CAAE2F,IAAI,IAAK;MACzB,IAAI,CAACA,IAAI,EAAE7F,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;MAC/B,OAAO,CAAC6F,IAAI;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjG,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAMmG,wBAAwB,GAAIC,QAAQ,IAAK;IAC7C3F,kBAAkB,CAAC2F,QAAQ,CAACtF,GAAG,CAAC;EAClC,CAAC;EAED,MAAMuF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAACrF,aAAa,EAAE;MAClBP,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EACD,MAAM6F,eAAe,GAAGA,CAAA,KAAM;IAC5BrF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMsF,eAAe,GAAGA,CAAA,KAAM;IAC5BtF,gBAAgB,CAAC,KAAK,CAAC;IACvBR,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM+F,QAAQ,GAAGA,CAAA,KAAM;IACrBrF,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMsF,SAAS,GAAGA,CAAA,KAAM;IACtBtF,WAAW,CAAC,KAAK,CAAC;IAClBuF,iBAAiB,CAAC,CAAC;EACrB,CAAC;EACD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9E,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtB,CAAC;EACD;EACA,MAAM+E,iBAAiB,GAAIvC,KAAK,IAAK;IACnC9C,WAAW,CAAC8C,KAAK,CAACwC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BvF,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMwF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxG,oBAAoB,CAAC,IAAI,CAAC;IAC1BuG,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChCpF,MAAM,CAAC,CAAC,CAAC,CAAC;IACVC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACnBtB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM0G,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1G,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM2G,eAAe,GAAGA,CAAA,KAAM;IAC5BrF,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IACxBiF,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM5B,YAAY,GAAGA,CAAA,KAAM;IACzBnD,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;EACtC,CAAC;EACD;;EAEAnE,SAAS,CAAC,MAAM;IACdkE,MAAM,CAACgD,gBAAgB,CAAC,QAAQ,EAAEE,YAAY,CAAC;IAC/C,OAAO,MAAM;MACXlD,MAAM,CAACiD,mBAAmB,CAAC,QAAQ,EAAEC,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMiC,eAAe,GAAGA,CAAA,KAAM;IAC5BxG,oBAAoB,CAAC,CAACD,iBAAiB,CAAC,CAAC,CAAC;EAC5C,CAAC;EAED,MAAM0G,eAAe,GAAGA,CAAA,KAAM;IAC5B1F,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMgF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7D,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,oBACEnD,OAAA,CAACzB,GAAG;IACFoJ,EAAE,EAAE;MACFC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpB7B,KAAK,EAAE;IACT,CAAE;IACF8B,SAAS,EAAE,oBAAoBrG,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IAAAsG,QAAA,GAEzD5F,QAAQ,iBACPpC,OAAA,CAACzB,GAAG;MACFoJ,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBG,cAAc,EAAE,eAAe;QAC/BhC,KAAK,EAAE;MACT,CAAE;MAAA+B,QAAA,gBAEFhI,OAAA,CAACR,IAAI;QAAC0I,EAAE,EAAC,OAAO;QAAAF,QAAA,eACdhI,OAAA;UACEmI,GAAG,EAAC,6BAA6B;UACjCC,GAAG,EAAC,MAAM;UACVnC,KAAK,EAAE,EAAG;UACVoC,MAAM,EAAE,EAAG,CAAC;UAAA;UACZC,QAAQ,OAAC;UAAA;UACTP,SAAS,EAAC,WAAW;UACrBQ,OAAO,EAAC,MAAM;UACdzC,KAAK,EAAE;YAAEG,KAAK,EAAE,MAAM;YAAEuC,OAAO,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP5I,OAAA,CAACzB,GAAG;QAACoJ,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEiB,GAAG,EAAE,MAAM;UAAEhB,aAAa,EAAE;QAAM,CAAE;QAAAG,QAAA,gBAC9DhI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA,CAACtB,UAAU;YAACoK,OAAO,EAAEvC,qBAAsB;YAAAyB,QAAA,eACzChI,OAAA,CAAClB,KAAK;cACJiK,KAAK,EAAC,OAAO;cACbpB,EAAE,EAAE;gBACF,mBAAmB,EAAE;kBACnBqB,eAAe,EAAE,KAAK;kBACtBC,SAAS,EAAE3F,eAAe,GAAG,UAAU,GAAG;gBAC5C;cACF,CAAE;cAAA0E,QAAA,eAEFhI,OAAA,CAACd,kBAAkB;gBAACgK,QAAQ,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACb5I,OAAA,CAACtB,UAAU;YAACoK,OAAO,EAAEzC,gBAAiB;YAAA2B,QAAA,eACpChI,OAAA,CAAClB,KAAK;cAACqK,YAAY,EAAE3F,cAAe;cAACuF,KAAK,EAAC,OAAO;cAAAf,QAAA,eAChDhI,OAAA,CAACf,gBAAgB;gBAACiK,QAAQ,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN5I,OAAA,CAACtB,UAAU;UAACoK,OAAO,EAAEhC,QAAS;UAAAkB,QAAA,eAC5BhI,OAAA,CAACN,QAAQ;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED5I,OAAA,CAACzB,GAAG;MAACwJ,SAAS,EAAE,UAAUrG,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAsG,QAAA,eACnDhI,OAAA,CAACzB,GAAG;QAACwJ,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBhI,OAAA,CAACR,IAAI;UAAC0I,EAAE,EAAC,OAAO;UAACpC,KAAK,EAAE;YAAEsD,cAAc,EAAE,MAAM;YAAEL,KAAK,EAAE;UAAU,CAAE;UAAAf,QAAA,eACnEhI,OAAA,CAACxB,UAAU;YAACuJ,SAAS,EAAC,MAAM;YAACsB,OAAO,EAAC,IAAI;YAAArB,QAAA,eACvChI,OAAA;cAAKmI,GAAG,EAAC,6BAA6B;cAACC,GAAG,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EAGNpH,QAAQ,iBACPxB,OAAA,CAAAC,SAAA;UAAA+H,QAAA,gBAEEhI,OAAA,CAACzB,GAAG;YACFwJ,SAAS,EAAE,YAAYvG,QAAQ,GAAG,MAAM,GAAG,EAAE,EAAG;YAChDsH,OAAO,EAAEhC,QAAS;YAClBa,EAAE,EAAE;cACF3B,QAAQ,EAAE,OAAO;cACjBsD,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPtD,KAAK,EAAE,MAAM;cACboC,MAAM,EAAE,OAAO;cAAE;cACjBW,eAAe,EAAE,oBAAoB;cACrCQ,MAAM,EAAE;YACV;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF5I,OAAA,CAACzB,GAAG;YACFwJ,SAAS,EAAE,kBAAkBvG,QAAQ,GAAG,MAAM,GAAG,EAAE,EAAG;YACtDmG,EAAE,EAAE;cACFqB,eAAe,EAAE,OAAO;cACxBX,MAAM,EAAE,OAAO;cAAE;cACjBoB,WAAW,EAAE,MAAM;cAAE;cACrBxD,KAAK,EAAE,MAAM;cACbyD,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,MAAM;cACjBH,MAAM,EAAE,IAAI;cACZ5B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBG,cAAc,EAAE,YAAY;cAC5B2B,UAAU,EAAE,YAAY;cACxB,iBAAiB,EAAE;gBACjBhC,OAAO,EAAE,MAAM;gBACfC,aAAa,EAAE,QAAQ;gBACvBC,UAAU,EAAE,QAAQ;gBACpBe,GAAG,EAAE,MAAM;gBACXL,OAAO,EAAE;cACX,CAAC;cACD,oBAAoB,EAAE;gBACpBZ,OAAO,EAAE5G,iBAAiB,GAAG,MAAM,GAAG,MAAM;gBAAE;gBAC9C6G,aAAa,EAAE,QAAQ;gBACvBgB,GAAG,EAAE,QAAQ;gBACbgB,SAAS,EAAE,MAAM;gBACjB,2BAA2B,EAAE;kBAC3B/B,UAAU,EAAE;gBACd;cACF;YACF,CAAE;YACFgC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;YAAA;YACzClB,OAAO,EAAGiB,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;YAAA;YAAAhC,QAAA,gBAErChI,OAAA,CAACzB,GAAG;cAACwJ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhI,OAAA,CAACR,IAAI;gBAAC0I,EAAE,EAAC,OAAO;gBAAAF,QAAA,eACdhI,OAAA;kBACEmI,GAAG,EAAC,6BAA6B;kBACjCC,GAAG,EAAC,MAAM;kBACVL,SAAS,EAAC,WAAW;kBACrBjC,KAAK,EAAE;oBAAEG,KAAK,EAAE,MAAM;oBAAEuC,OAAO,EAAE;kBAAO;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP5I,OAAA,CAACtB,UAAU;gBAACoK,OAAO,EAAE/B,SAAU;gBAACgB,SAAS,EAAC,cAAc;gBAAAC,QAAA,eACtDhI,OAAA,CAAChB,SAAS;kBAACkK,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN5I,OAAA,CAACzB,GAAG;cAACwJ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhI,OAAA,CAACxB,UAAU;gBACTsK,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,OAAO,CAAE;gBACjC4F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5I,OAAA,CAACxB,UAAU;gBACTuJ,SAAS,EAAC,WAAW;gBACrB,iBAAehG,SAAS,GAAG,WAAW,GAAGkI,SAAU;gBACnD,iBAAc,MAAM;gBACpBnB,OAAO,EAAErB,eAAgB,CAAC;gBAAA;gBAAAO,QAAA,EAC3B;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAGb5I,OAAA,CAACzB,GAAG;gBACFwJ,SAAS,EAAE,mBACT/G,iBAAiB,GAAG,MAAM,GAAG,EAAE,EAC9B;gBAAAgH,QAAA,gBAEHhI,OAAA,CAACxB,UAAU;kBACTuJ,SAAS,EAAC,UAAU;kBACpBe,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,UAAU,CAAE;kBAAA6F,QAAA,EACrC;gBAED;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZ1H,QAAQ,CAACkF,MAAM,GAAG,CAAC,GAClBlF,QAAQ,CAACgJ,GAAG,CAAExD,QAAQ,iBACpB1G,OAAA,CAACxB,UAAU;kBAETuJ,SAAS,EAAC,oBAAoB;kBAC9Be,OAAO,EAAEA,CAAA,KAAM;oBACb3G,QAAQ,CAAC,aAAauE,QAAQ,CAACtF,GAAG,gBAAgB,CAAC;oBACnDsG,eAAe,CAAC,CAAC;kBACnB,CAAE;kBAAAM,QAAA,EAEDtB,QAAQ,CAACrF;gBAAI,GAPTqF,QAAQ,CAACtF,GAAG;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQP,CACb,CAAC,gBAEF5I,OAAA,CAACxB,UAAU;kBAAAwJ,QAAA,EAAC;gBAAuB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAChD,eACD5I,OAAA,CAACxB,UAAU;kBACTuJ,SAAS,EAAC,UAAU;kBACpBe,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,uBAAuB,CAAE;kBAAA6F,QAAA,EAClD;gBAED;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5I,OAAA,CAACxB,UAAU;kBACTuJ,SAAS,EAAC,UAAU;kBACpBe,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,kBAAkB,CAAE;kBAAA6F,QAAA,EAC7C;gBAED;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEN5I,OAAA,CAACxB,UAAU;gBACTsK,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,QAAQ,CAAE;gBAClC4F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5I,OAAA,CAACxB,UAAU;gBACTsK,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,YAAY,CAAE;gBACtC4F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ3G,WAAW,gBACVjC,OAAA,CAACxB,UAAU;gBACTsK,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,YAAY,CAAE;gBACtC4F,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,gBAEb5I,OAAA,CAAAC,SAAA,mBAAI,CACL,EAEAgC,WAAW,gBACVjC,OAAA,CAACxB,UAAU;gBACTsK,OAAO,EAAEzB,iBAAkB;gBAC3BU,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,gBAEb5I,OAAA,CAACxB,UAAU;gBACTsK,OAAO,EAAE7B,gBAAiB;gBAC1Bc,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB;cAED;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN,CACH,eAED5I,OAAA,CAACpB,IAAI;UACH8G,EAAE,EAAC,WAAW;UACd3D,SAAS,EAAEA,SAAU;UACrBoI,IAAI,EAAEC,OAAO,CAACrI,SAAS,CAAE;UACzBsI,OAAO,EAAE3C,eAAgB;UACzB4C,YAAY,EAAE;YACZC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UACFC,eAAe,EAAE;YACfF,QAAQ,EAAE,KAAK;YACfC,UAAU,EAAE;UACd,CAAE;UAAAxC,QAAA,EAED9G,QAAQ,CAACkF,MAAM,GAAG,CAAC,GAClBlF,QAAQ,CAACgJ,GAAG,CAAExD,QAAQ,iBACpB1G,OAAA,CAACnB,QAAQ;YAEPiK,OAAO,EAAEA,CAAA,KAAM;cACb3G,QAAQ,CAAC,SAASuE,QAAQ,CAACgE,IAAI,EAAE,CAAC;cAClChD,eAAe,CAAC,CAAC;YACnB,CAAE;YAAAM,QAAA,EAEDtB,QAAQ,CAACrF;UAAI,GANTqF,QAAQ,CAACtF,GAAG;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOT,CACX,CAAC,gBAEF5I,OAAA,CAACnB,QAAQ;YAAC8L,QAAQ;YAAA3C,QAAA,EAAC;UAAuB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QACrD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAAC,GAAG,eAEX5I,OAAA,CAACzB,GAAG;UAACwJ,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhI,OAAA,CAACjB,UAAU;YAAC4I,EAAE,EAAE;cAAEoB,KAAK,EAAE,MAAM;cAAE6B,MAAM,EAAE;YAAU;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxD5I,OAAA,CAACvB,SAAS;YACRoM,WAAW,EAAC,kDAAkD;YAC9DC,SAAS;YACTlG,KAAK,EAAE7B,WAAY;YACnBgI,QAAQ,EAAErG,kBAAmB;YAC7BsG,MAAM,EAAEA,CAAA,KAAMC,UAAU,CAAC,MAAM/H,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG;UAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,EAGDsC,KAAK,CAACC,OAAO,CAAClI,WAAW,CAAC,IAAIA,WAAW,CAACmD,MAAM,GAAG,CAAC,iBACnDpG,OAAA,CAACzB,GAAG;YAACwJ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClC/E,WAAW,CAACiH,GAAG,CAAEjF,UAAU,iBAC1BjF,OAAA,CAACzB,GAAG;cAEFwJ,SAAS,EAAC,uCAAuC;cACjDe,OAAO,EAAEA,CAAA,KAAM9D,qBAAqB,CAACC,UAAU,CAAE;cAAA+C,QAAA,gBAGjDhI,OAAA;gBAAK+H,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9C/C,UAAU,CAACC,UAAU,KAAK,SAAS,IACpCD,UAAU,CAACmG,SAAS,gBAClBpL,OAAA;kBACEmI,GAAG,EAAE,uDAAuDlD,UAAU,CAACmG,SAAS,EAAG;kBACnFhD,GAAG,EAAEnD,UAAU,CAAC5D,IAAK;kBACrB0G,SAAS,EAAC;gBAAwB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,GACA3D,UAAU,CAACC,UAAU,KAAK,OAAO,IACnCD,UAAU,CAACoG,SAAS,gBACpBrL,OAAA;kBACEmI,GAAG,EAAE,uDAAuDlD,UAAU,CAACoG,SAAS,EAAG;kBACnFjD,GAAG,EAAEnD,UAAU,CAACE,SAAU;kBAC1B4C,SAAS,EAAC;gBAAwB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,gBAEF5I,OAAA,CAACzB,GAAG;kBAACwJ,SAAS,EAAC;gBAAoC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAC1D;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGN5I,OAAA,CAACzB,GAAG;gBAACwJ,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnC/C,UAAU,CAACC,UAAU,KAAK,SAAS;gBAAA;gBAClC;gBACAlF,OAAA,CAAAC,SAAA;kBAAA+H,QAAA,gBACEhI,OAAA;oBAAK+H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACnC/C,UAAU,CAAC5D;kBAAI;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACN5I,OAAA;oBAAK+H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnC/C,UAAU,CAACyB,QAAQ,iBAClB1G,OAAA;sBAAM+H,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EACxC/C,UAAU,CAACyB,QAAQ,CAACrF;oBAAI;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CACP,EACA3D,UAAU,CAACqG,OAAO,iBACjBtL,OAAA;sBAAM+H,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EACrC/C,UAAU,CAACqG,OAAO,CAACnG;oBAAS;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN5I,OAAA;oBAAK+H,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACxC/C,UAAU,CAACsG,SAAS,gBACnBvL,OAAA,CAAAC,SAAA;sBAAA+H,QAAA,gBACEhI,OAAA;wBAAM+H,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,GAAC,OAE3C,EAAC/C,UAAU,CAACuG,KAAK,GAAG,IAAI,GACpB,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;0BAC7BC,qBAAqB,EAAE,CAAC;0BACxBC,qBAAqB,EAAE;wBACzB,CAAC,CAAC,CAACC,MAAM,CAAC5G,UAAU,CAACuG,KAAK,CAAC,GAC3BvG,UAAU,CAACuG,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC,eACP5I,OAAA;wBAAM+H,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,OAE5C,EAAC/C,UAAU,CAACsG,SAAS,GAAG,IAAI,GACxB,IAAIE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;0BAC7BC,qBAAqB,EAAE,CAAC;0BACxBC,qBAAqB,EAAE;wBACzB,CAAC,CAAC,CAACC,MAAM,CAAC5G,UAAU,CAACsG,SAAS,CAAC,GAC/BtG,UAAU,CAACsG,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA,eACP,CAAC,gBAEH5I,OAAA;sBAAM+H,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,GAAC,OAE/C,EAAC/C,UAAU,CAACuG,KAAK,GAAG,IAAI,GACpB,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;wBAC7BC,qBAAqB,EAAE,CAAC;wBACxBC,qBAAqB,EAAE;sBACzB,CAAC,CAAC,CAACC,MAAM,CAAC5G,UAAU,CAACuG,KAAK,CAAC,GAC3BvG,UAAU,CAACuG,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBACP;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,eACN,CAAC;gBAAA;gBAEH;gBACA5I,OAAA,CAAAC,SAAA;kBAAA+H,QAAA,gBACEhI,OAAA;oBACE+H,SAAS,EAAC,uBAAuB;oBACjCjC,KAAK,EAAE;sBAAEiG,UAAU,EAAE;oBAAO,CAAE;oBAAA/D,QAAA,EAE7B/C,UAAU,CAACE;kBAAS;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACN5I,OAAA;oBACE+H,SAAS,EAAC,2BAA2B;oBACrCjC,KAAK,EAAE;sBAAEkG,SAAS,EAAE;oBAAS,CAAE;oBAAAhE,QAAA,EAChC;kBAED;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACL3D,UAAU,CAACgH,gBAAgB,iBAC1BjM,OAAA;oBACE+H,SAAS,EAAC,uBAAuB;oBACjCjC,KAAK,EAAE;sBAAEoD,QAAQ,EAAE;oBAAS,CAAE;oBAAAlB,QAAA,GAE7B/C,UAAU,CAACgH,gBAAgB,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAC5CjH,UAAU,CAACgH,gBAAgB,CAAC7F,MAAM,GAAG,EAAE,GACpC,KAAK,GACL,EAAE;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA,eACD;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA5GD3D,UAAU,CAAC7D,GAAG;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6GhB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN5I,OAAA,CAACzB,GAAG;UAACwJ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5B/F,WAAW,gBACVjC,OAAA,CAAAC,SAAA;YAAA+H,QAAA,gBACEhI,OAAA;cAAAgI,QAAA,gBACEhI,OAAA,CAACtB,UAAU;gBAACoK,OAAO,EAAEvC,qBAAsB;gBAAAyB,QAAA,eACzChI,OAAA,CAAClB,KAAK;kBACJqK,YAAY,EAAE/F,aAAc;kBAC5B2F,KAAK,EAAC,OAAO;kBACboD,GAAG,EAAE,EAAG;kBACRxE,EAAE,EAAE;oBACF,mBAAmB,EAAE;sBACnBqB,eAAe,EAAE,SAAS;sBAC1BD,KAAK,EAAE,MAAM;sBACbgD,UAAU,EAAE,MAAM;sBAClB7C,QAAQ,EAAE,MAAM;sBAChBkD,QAAQ,EAAE,MAAM;sBAChB/D,MAAM,EAAE,MAAM;sBACdG,OAAO,EAAE,OAAO;sBAChBS,SAAS,EAAE3F,eAAe,GAAG,UAAU,GAAG,MAAM;sBAChD,kBAAkB,EAAE;wBAClB,IAAI,EAAE;0BAAE+I,SAAS,EAAE;wBAAW,CAAC;wBAC/B,KAAK,EAAE;0BAAEA,SAAS,EAAE;wBAAa,CAAC;wBAClC,MAAM,EAAE;0BAAEA,SAAS,EAAE;wBAAW;sBAClC;oBACF;kBACF,CAAE;kBAAArE,QAAA,eAEFhI,OAAA,CAACd,kBAAkB;oBAACgK,QAAQ,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACb5I,OAAA,CAACtB,UAAU;gBAACoK,OAAO,EAAEzC,gBAAiB;gBAAA2B,QAAA,eACpChI,OAAA,CAAClB,KAAK;kBAACqK,YAAY,EAAE3F,cAAe;kBAACuF,KAAK,EAAC,OAAO;kBAAAf,QAAA,eAChDhI,OAAA,CAACf,gBAAgB;oBAACiK,QAAQ,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5I,OAAA,CAACrB,MAAM;cACLoJ,SAAS,EAAC,QAAQ;cAClBe,OAAO,EAAE5B,iBAAkB;cAC3BS,EAAE,EAAE;gBACFiD,MAAM,EAAE,SAAS;gBACjB3E,KAAK,EAAE,MAAM;gBACboC,MAAM,EAAE,MAAM;gBACdwB,SAAS,EAAE;cACb,CAAE;cAAA7B,QAAA,EAED,CAAAxF,QAAQ,aAARA,QAAQ,wBAAApC,mBAAA,GAARoC,QAAQ,CAAEE,SAAS,cAAAtC,mBAAA,wBAAAC,qBAAA,GAAnBD,mBAAA,CAAqBkM,MAAM,CAAC,CAAC,CAAC,cAAAjM,qBAAA,uBAA9BA,qBAAA,CAAgCkM,WAAW,CAAC,CAAC,KAAI;YAAK;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eAGT5I,OAAA,CAACpB,IAAI;cACHgD,QAAQ,EAAEA,QAAS;cACnBuI,IAAI,EAAEC,OAAO,CAACxI,QAAQ,CAAE;cACxByI,OAAO,EAAEjD,eAAgB;cACzBkD,YAAY,EAAE;gBACZC,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACd,CAAE;cACFC,eAAe,EAAE;gBACfF,QAAQ,EAAE,KAAK;gBACfC,UAAU,EAAE;cACd,CAAE;cACFgC,UAAU,EAAE;gBACV7E,EAAE,EAAE;kBACF1B,KAAK,EAAE,IAAI;kBACX,mDAAmD,EAAE;oBACnDA,KAAK,EAAE;kBACT,CAAC;kBACDwG,cAAc,EAAE,YAAY;kBAC5BzD,eAAe,EAAE,uBAAuB;kBACxC0D,YAAY,EAAE,CAAC;kBACfC,MAAM,EAAE,gBAAgB;kBACxBC,SAAS,EAAE,+BAA+B;kBAC1C7G,QAAQ,EAAE,QAAQ;kBAClBC,QAAQ,EAAE,UAAU;kBACpB,UAAU,EAAE;oBACV6G,OAAO,EAAE,IAAI;oBACb7G,QAAQ,EAAE,UAAU;oBACpBsD,GAAG,EAAE,CAAC;oBACNC,IAAI,EAAE,CAAC;oBACPuD,KAAK,EAAE,CAAC;oBACRC,MAAM,EAAE,CAAC;oBACTJ,MAAM,EAAE,mBAAmB;oBAC3B3D,eAAe,EAAE,SAAS;oBAE1BgE,OAAO,EAAE,GAAG;oBACZxD,MAAM,EAAE;kBACV,CAAC;kBACD,qBAAqB,EAAE;oBACrBxD,QAAQ,EAAE,UAAU;oBACpBwD,MAAM,EAAE,CAAC;oBACTT,KAAK,EAAE;kBACT;gBACF;cACF,CAAE;cAAAf,QAAA,gBAEFhI,OAAA,CAACnB,QAAQ;gBAACiK,OAAO,EAAEtB,eAAgB;gBAAAQ,QAAA,EAAC;cAAU;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACzD5I,OAAA;gBACE8F,KAAK,EAAE;kBACLiD,KAAK,EAAE,0BAA0B;kBACjC9C,KAAK,EAAE,KAAK;kBACZgH,MAAM,EAAE;gBACV;cAAE;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5I,OAAA,CAACnB,QAAQ;gBAACiK,OAAO,EAAEzB,iBAAkB;gBAAAW,QAAA,gBACnChI,OAAA,CAACL,eAAe;kBAACmG,KAAK,EAAE;oBAAEoH,WAAW,EAAE;kBAAO;gBAAE;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WACrD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,eACP,CAAC,gBAEH5I,OAAA,CAACzB,GAAG;YACFoJ,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,KAAK;cACpBC,UAAU,EAAE,UAAU;cACtBe,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,gBAEFhI,OAAA;cAAAgI,QAAA,gBACEhI,OAAA,CAACtB,UAAU;gBAACoK,OAAO,EAAEvC,qBAAsB;gBAAAyB,QAAA,eACzChI,OAAA,CAAClB,KAAK;kBACJqK,YAAY,EAAE/F,aAAc;kBAC5B2F,KAAK,EAAC,OAAO;kBACboD,GAAG,EAAE,EAAG;kBACRxE,EAAE,EAAE;oBACF,mBAAmB,EAAE;sBACnBqB,eAAe,EAAE,SAAS;sBAC1BD,KAAK,EAAE,MAAM;sBACbgD,UAAU,EAAE,MAAM;sBAClB7C,QAAQ,EAAE,MAAM;sBAChBkD,QAAQ,EAAE,MAAM;sBAChB/D,MAAM,EAAE,MAAM;sBACdG,OAAO,EAAE,OAAO;sBAChBS,SAAS,EAAE3F,eAAe,GAAG,UAAU,GAAG,MAAM;sBAChD,kBAAkB,EAAE;wBAClB,IAAI,EAAE;0BAAE+I,SAAS,EAAE;wBAAW,CAAC;wBAC/B,KAAK,EAAE;0BAAEA,SAAS,EAAE;wBAAa,CAAC;wBAClC,MAAM,EAAE;0BAAEA,SAAS,EAAE;wBAAW;sBAClC;oBACF;kBACF,CAAE;kBAAArE,QAAA,eAEFhI,OAAA,CAACd,kBAAkB;oBAACgK,QAAQ,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACb5I,OAAA,CAACtB,UAAU;gBAACoK,OAAO,EAAEzC,gBAAiB;gBAAA2B,QAAA,eACpChI,OAAA,CAAClB,KAAK;kBAACqK,YAAY,EAAE3F,cAAe;kBAACuF,KAAK,EAAC,OAAO;kBAAAf,QAAA,eAChDhI,OAAA,CAACf,gBAAgB;oBAACiK,QAAQ,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5I,OAAA;cACEqJ,OAAO,EAAC,WAAW;cACnBP,OAAO,EAAE7B,gBAAiB;cAC1Bc,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC9B;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5I,OAAA,CAACzB,GAAG;MAACwJ,SAAS,EAAC,eAAe;MAACoF,YAAY,EAAExG,wBAAyB;MAAAqB,QAAA,EACnE9G,QAAQ,CAACkF,MAAM,KAAK,CAAC,gBACpBpG,OAAA,CAACxB,UAAU;QAAAwJ,QAAA,EAAC;MAAuB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEhD5I,OAAA,CAAC1B,QAAQ;QAAA0J,QAAA,gBACPhI,OAAA,CAACxB,UAAU;UACTuJ,SAAS,EAAC,UAAU;UACpBe,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,UAAU,CAAE;UAAA6F,QAAA,EACrC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ1H,QAAQ,CAACgJ,GAAG,CAAExD,QAAQ,iBACrB1G,OAAA,CAACxB,UAAU;UAETuJ,SAAS,EAAE,YACTjH,eAAe,KAAK4F,QAAQ,CAACtF,GAAG,GAAG,aAAa,GAAG,EAAE,EACpD;UACHgM,YAAY,EAAEA,CAAA,KAAM3G,wBAAwB,CAACC,QAAQ,CAAE;UAAAsB,QAAA,EAEtDtB,QAAQ,CAACrF;QAAI,GANTqF,QAAQ,CAACtF,GAAG;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOP,CACb,CAAC,eACF5I,OAAA,CAACxB,UAAU;UACTuJ,SAAS,EAAC,UAAU;UACpBe,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,uBAAuB,CAAE;UAAA6F,QAAA,EAClD;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5I,OAAA,CAACxB,UAAU;UACTuJ,SAAS,EAAC,UAAU;UACpBe,OAAO,EAAEA,CAAA,KAAM3G,QAAQ,CAAC,kBAAkB,CAAE;UAAA6F,QAAA,EAC7C;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL9H,eAAe,iBACdd,OAAA,CAACT,QAAQ;MACPmH,QAAQ,EAAExF,QAAQ,CAACmM,IAAI,CAAE1J,IAAI,IAAKA,IAAI,CAACvC,GAAG,KAAKN,eAAe,CAAE;MAChEsM,YAAY,EAAExG,eAAgB;MAC9BuG,YAAY,EAAEtG;IAAgB;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CACF,eAID5I,OAAA,CAACZ,YAAY;MAAC+K,IAAI,EAAE7J,SAAU;MAAC+J,OAAO,EAAE7D;IAAkB;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7D5I,OAAA,CAACX,mBAAmB;MAAC8K,IAAI,EAAE3J,QAAS;MAAC6J,OAAO,EAAEhE;IAAiB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClE5I,OAAA,CAACV,gBAAgB;MAAC6K,IAAI,EAAEzJ,aAAc;MAAC2J,OAAO,EAAE9D;IAAsB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzE5I,OAAA,CAACb,kBAAkB;MACjBgL,IAAI,EAAEvJ,iBAAkB;MACxB0M,KAAK,EAAC,gBAAgB;MACtBT,OAAO,EAAC,kCAAkC;MAC1CU,SAAS,EAAEjG,mBAAoB;MAC/BkG,QAAQ,EAAEjG;IAAmB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACzI,EAAA,CAr5BQD,MAAM;EAAA,QAmBSJ,OAAO,EAGZL,WAAW;AAAA;AAAAgO,EAAA,GAtBrBvN,MAAM;AAu5Bf,eAAeA,MAAM;AAAC,IAAAuN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}