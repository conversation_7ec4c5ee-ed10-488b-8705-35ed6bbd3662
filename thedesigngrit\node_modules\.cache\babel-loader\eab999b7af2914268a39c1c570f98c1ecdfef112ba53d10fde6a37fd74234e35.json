{"ast": null, "code": "import { urlAlphabet } from './url-alphabet/index.js';\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes));\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log(alphabet.length - 1) / Math.LN2) - 1;\n  let step = -~(1.6 * mask * defaultSize / alphabet.length);\n  return (size = defaultSize) => {\n    let id = '';\n    while (true) {\n      let bytes = getRandom(step);\n      let j = step | 0;\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || '';\n        if (id.length === size) return id;\n      }\n    }\n  };\n};\nlet customAlphabet = (alphabet, size = 21) => customRandom(alphabet, size, random);\nlet nanoid = (size = 21) => crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n  byte &= 63;\n  if (byte < 36) {\n    id += byte.toString(36);\n  } else if (byte < 62) {\n    id += (byte - 26).toString(36).toUpperCase();\n  } else if (byte > 62) {\n    id += '-';\n  } else {\n    id += '_';\n  }\n  return id;\n}, '');\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random };", "map": {"version": 3, "names": ["url<PERSON>l<PERSON><PERSON>", "random", "bytes", "crypto", "getRandomValues", "Uint8Array", "customRandom", "alphabet", "defaultSize", "getRandom", "mask", "Math", "log", "length", "LN2", "step", "size", "id", "j", "customAlphabet", "nanoid", "reduce", "byte", "toString", "toUpperCase"], "sources": ["D:/TDGweb/TDG/thedesigngrit/node_modules/nanoid/index.browser.js"], "sourcesContent": ["import { urlAlphabet } from './url-alphabet/index.js'\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    byte &= 63\n    if (byte < 36) {\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,yBAAyB;AACrD,IAAIC,MAAM,GAAGC,KAAK,IAAIC,MAAM,CAACC,eAAe,CAAC,IAAIC,UAAU,CAACH,KAAK,CAAC,CAAC;AACnE,IAAII,YAAY,GAAGA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,KAAK;EACvD,IAAIC,IAAI,GAAG,CAAC,CAAC,IAAKC,IAAI,CAACC,GAAG,CAACL,QAAQ,CAACM,MAAM,GAAG,CAAC,CAAC,GAAGF,IAAI,CAACG,GAAI,IAAI,CAAC;EAChE,IAAIC,IAAI,GAAG,CAAC,EAAG,GAAG,GAAGL,IAAI,GAAGF,WAAW,GAAID,QAAQ,CAACM,MAAM,CAAC;EAC3D,OAAO,CAACG,IAAI,GAAGR,WAAW,KAAK;IAC7B,IAAIS,EAAE,GAAG,EAAE;IACX,OAAO,IAAI,EAAE;MACX,IAAIf,KAAK,GAAGO,SAAS,CAACM,IAAI,CAAC;MAC3B,IAAIG,CAAC,GAAGH,IAAI,GAAG,CAAC;MAChB,OAAOG,CAAC,EAAE,EAAE;QACVD,EAAE,IAAIV,QAAQ,CAACL,KAAK,CAACgB,CAAC,CAAC,GAAGR,IAAI,CAAC,IAAI,EAAE;QACrC,IAAIO,EAAE,CAACJ,MAAM,KAAKG,IAAI,EAAE,OAAOC,EAAE;MACnC;IACF;EACF,CAAC;AACH,CAAC;AACD,IAAIE,cAAc,GAAGA,CAACZ,QAAQ,EAAES,IAAI,GAAG,EAAE,KACvCV,YAAY,CAACC,QAAQ,EAAES,IAAI,EAAEf,MAAM,CAAC;AACtC,IAAImB,MAAM,GAAGA,CAACJ,IAAI,GAAG,EAAE,KACrBb,MAAM,CAACC,eAAe,CAAC,IAAIC,UAAU,CAACW,IAAI,CAAC,CAAC,CAACK,MAAM,CAAC,CAACJ,EAAE,EAAEK,IAAI,KAAK;EAChEA,IAAI,IAAI,EAAE;EACV,IAAIA,IAAI,GAAG,EAAE,EAAE;IACbL,EAAE,IAAIK,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC,MAAM,IAAID,IAAI,GAAG,EAAE,EAAE;IACpBL,EAAE,IAAI,CAACK,IAAI,GAAG,EAAE,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9C,CAAC,MAAM,IAAIF,IAAI,GAAG,EAAE,EAAE;IACpBL,EAAE,IAAI,GAAG;EACX,CAAC,MAAM;IACLA,EAAE,IAAI,GAAG;EACX;EACA,OAAOA,EAAE;AACX,CAAC,EAAE,EAAE,CAAC;AACR,SAASG,MAAM,EAAED,cAAc,EAAEb,YAAY,EAAEN,WAAW,EAAEC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}