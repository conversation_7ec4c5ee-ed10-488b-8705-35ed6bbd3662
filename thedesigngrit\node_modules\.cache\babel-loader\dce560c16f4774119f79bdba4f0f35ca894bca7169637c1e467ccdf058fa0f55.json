{"ast": null, "code": "'use client';\n\nimport React, { useState, useRef, useEffect, createContext, useMemo, useContext, useCallback } from 'react';\nfunction useLoadGsiScript(options = {}) {\n  const {\n    nonce,\n    onScriptLoadSuccess,\n    onScriptLoadError\n  } = options;\n  const [scriptLoadedSuccessfully, setScriptLoadedSuccessfully] = useState(false);\n  const onScriptLoadSuccessRef = useRef(onScriptLoadSuccess);\n  onScriptLoadSuccessRef.current = onScriptLoadSuccess;\n  const onScriptLoadErrorRef = useRef(onScriptLoadError);\n  onScriptLoadErrorRef.current = onScriptLoadError;\n  useEffect(() => {\n    const scriptTag = document.createElement('script');\n    scriptTag.src = 'https://accounts.google.com/gsi/client';\n    scriptTag.async = true;\n    scriptTag.defer = true;\n    scriptTag.nonce = nonce;\n    scriptTag.onload = () => {\n      var _a;\n      setScriptLoadedSuccessfully(true);\n      (_a = onScriptLoadSuccessRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadSuccessRef);\n    };\n    scriptTag.onerror = () => {\n      var _a;\n      setScriptLoadedSuccessfully(false);\n      (_a = onScriptLoadErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadErrorRef);\n    };\n    document.body.appendChild(scriptTag);\n    return () => {\n      document.body.removeChild(scriptTag);\n    };\n  }, [nonce]);\n  return scriptLoadedSuccessfully;\n}\nconst GoogleOAuthContext = createContext(null);\nfunction GoogleOAuthProvider({\n  clientId,\n  nonce,\n  onScriptLoadSuccess,\n  onScriptLoadError,\n  children\n}) {\n  const scriptLoadedSuccessfully = useLoadGsiScript({\n    nonce,\n    onScriptLoadSuccess,\n    onScriptLoadError\n  });\n  const contextValue = useMemo(() => ({\n    clientId,\n    scriptLoadedSuccessfully\n  }), [clientId, scriptLoadedSuccessfully]);\n  return React.createElement(GoogleOAuthContext.Provider, {\n    value: contextValue\n  }, children);\n}\nfunction useGoogleOAuth() {\n  const context = useContext(GoogleOAuthContext);\n  if (!context) {\n    throw new Error('Google OAuth components must be used within GoogleOAuthProvider');\n  }\n  return context;\n}\nfunction extractClientId(credentialResponse) {\n  var _a;\n  const clientId = (_a = credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.clientId) !== null && _a !== void 0 ? _a : credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.client_id;\n  return clientId;\n}\nconst containerHeightMap = {\n  large: 40,\n  medium: 32,\n  small: 20\n};\nfunction GoogleLogin({\n  onSuccess,\n  onError,\n  useOneTap,\n  promptMomentNotification,\n  type = 'standard',\n  theme = 'outline',\n  size = 'large',\n  text,\n  shape,\n  logo_alignment,\n  width,\n  locale,\n  click_listener,\n  containerProps,\n  ...props\n}) {\n  const btnContainerRef = useRef(null);\n  const {\n    clientId,\n    scriptLoadedSuccessfully\n  } = useGoogleOAuth();\n  const onSuccessRef = useRef(onSuccess);\n  onSuccessRef.current = onSuccess;\n  const onErrorRef = useRef(onError);\n  onErrorRef.current = onError;\n  const promptMomentNotificationRef = useRef(promptMomentNotification);\n  promptMomentNotificationRef.current = promptMomentNotification;\n  useEffect(() => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n    if (!scriptLoadedSuccessfully) return;\n    (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.initialize({\n      client_id: clientId,\n      callback: credentialResponse => {\n        var _a;\n        if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\n          return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\n        }\n        const {\n          credential,\n          select_by\n        } = credentialResponse;\n        onSuccessRef.current({\n          credential,\n          clientId: extractClientId(credentialResponse),\n          select_by\n        });\n      },\n      ...props\n    });\n    (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.renderButton(btnContainerRef.current, {\n      type,\n      theme,\n      size,\n      text,\n      shape,\n      logo_alignment,\n      width,\n      locale,\n      click_listener\n    });\n    if (useOneTap) (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\n    return () => {\n      var _a, _b, _c;\n      if (useOneTap) (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [clientId, scriptLoadedSuccessfully, useOneTap, type, theme, size, text, shape, logo_alignment, width, locale]);\n  return React.createElement(\"div\", {\n    ...containerProps,\n    ref: btnContainerRef,\n    style: {\n      height: containerHeightMap[size],\n      ...(containerProps === null || containerProps === void 0 ? void 0 : containerProps.style)\n    }\n  });\n}\nfunction googleLogout() {\n  var _a, _b, _c;\n  (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.disableAutoSelect();\n}\n\n/* eslint-disable import/export */\nfunction useGoogleLogin({\n  flow = 'implicit',\n  scope = '',\n  onSuccess,\n  onError,\n  onNonOAuthError,\n  overrideScope,\n  state,\n  ...props\n}) {\n  const {\n    clientId,\n    scriptLoadedSuccessfully\n  } = useGoogleOAuth();\n  const clientRef = useRef();\n  const onSuccessRef = useRef(onSuccess);\n  onSuccessRef.current = onSuccess;\n  const onErrorRef = useRef(onError);\n  onErrorRef.current = onError;\n  const onNonOAuthErrorRef = useRef(onNonOAuthError);\n  onNonOAuthErrorRef.current = onNonOAuthError;\n  useEffect(() => {\n    var _a, _b;\n    if (!scriptLoadedSuccessfully) return;\n    const clientMethod = flow === 'implicit' ? 'initTokenClient' : 'initCodeClient';\n    const client = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2[clientMethod]({\n      client_id: clientId,\n      scope: overrideScope ? scope : `openid profile email ${scope}`,\n      callback: response => {\n        var _a, _b;\n        if (response.error) return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, response);\n        (_b = onSuccessRef.current) === null || _b === void 0 ? void 0 : _b.call(onSuccessRef, response);\n      },\n      error_callback: nonOAuthError => {\n        var _a;\n        (_a = onNonOAuthErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onNonOAuthErrorRef, nonOAuthError);\n      },\n      state,\n      ...props\n    });\n    clientRef.current = client;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [clientId, scriptLoadedSuccessfully, flow, scope, state]);\n  const loginImplicitFlow = useCallback(overrideConfig => {\n    var _a;\n    return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestAccessToken(overrideConfig);\n  }, []);\n  const loginAuthCodeFlow = useCallback(() => {\n    var _a;\n    return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestCode();\n  }, []);\n  return flow === 'implicit' ? loginImplicitFlow : loginAuthCodeFlow;\n}\nfunction useGoogleOneTapLogin({\n  onSuccess,\n  onError,\n  promptMomentNotification,\n  cancel_on_tap_outside,\n  prompt_parent_id,\n  state_cookie_domain,\n  hosted_domain,\n  use_fedcm_for_prompt = false,\n  use_fedcm_for_button = false,\n  disabled,\n  auto_select\n}) {\n  const {\n    clientId,\n    scriptLoadedSuccessfully\n  } = useGoogleOAuth();\n  const onSuccessRef = useRef(onSuccess);\n  onSuccessRef.current = onSuccess;\n  const onErrorRef = useRef(onError);\n  onErrorRef.current = onError;\n  const promptMomentNotificationRef = useRef(promptMomentNotification);\n  promptMomentNotificationRef.current = promptMomentNotification;\n  useEffect(() => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n    if (!scriptLoadedSuccessfully) return;\n    if (disabled) {\n      (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n      return;\n    }\n    (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.initialize({\n      client_id: clientId,\n      callback: credentialResponse => {\n        var _a;\n        if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\n          return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\n        }\n        const {\n          credential,\n          select_by\n        } = credentialResponse;\n        onSuccessRef.current({\n          credential,\n          clientId: extractClientId(credentialResponse),\n          select_by\n        });\n      },\n      hosted_domain,\n      cancel_on_tap_outside,\n      prompt_parent_id,\n      state_cookie_domain,\n      use_fedcm_for_prompt,\n      use_fedcm_for_button,\n      auto_select\n    });\n    (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\n    return () => {\n      var _a, _b, _c;\n      (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n    };\n  }, [clientId, scriptLoadedSuccessfully, cancel_on_tap_outside, prompt_parent_id, state_cookie_domain, hosted_domain, use_fedcm_for_prompt, use_fedcm_for_button, disabled, auto_select]);\n}\n\n/**\r\n * Checks if the user granted all the specified scope or scopes\r\n * @returns True if all the scopes are granted\r\n */\nfunction hasGrantedAllScopesGoogle(tokenResponse, firstScope, ...restScopes) {\n  var _a, _b, _c;\n  if (!(window === null || window === void 0 ? void 0 : window.google)) return false;\n  return ((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAllScopes(tokenResponse, firstScope, ...restScopes)) || false;\n}\n\n/**\r\n * Checks if the user granted any of the specified scope or scopes.\r\n * @returns True if any of the scopes are granted\r\n */\nfunction hasGrantedAnyScopeGoogle(tokenResponse, firstScope, ...restScopes) {\n  var _a, _b, _c;\n  if (!(window === null || window === void 0 ? void 0 : window.google)) return false;\n  return ((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAnyScope(tokenResponse, firstScope, ...restScopes)) || false;\n}\nexport { GoogleLogin, GoogleOAuthProvider, googleLogout, hasGrantedAllScopesGoogle, hasGrantedAnyScopeGoogle, useGoogleLogin, useGoogleOAuth, useGoogleOneTapLogin };", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "createContext", "useMemo", "useContext", "useCallback", "useLoadGsiScript", "options", "nonce", "onScriptLoadSuccess", "onScriptLoadError", "scriptLoadedSuccessfully", "setScriptLoadedSuccessfully", "onScriptLoadSuccessRef", "current", "onScriptLoadErrorRef", "scriptTag", "document", "createElement", "src", "async", "defer", "onload", "_a", "call", "onerror", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "GoogleOAuthContext", "GoogleOAuthProvider", "clientId", "children", "contextValue", "Provider", "value", "useGoogleOAuth", "context", "Error", "extractClientId", "credentialResponse", "client_id", "containerHeightMap", "large", "medium", "small", "GoogleLogin", "onSuccess", "onError", "useOneTap", "promptMomentNotification", "type", "theme", "size", "text", "shape", "logo_alignment", "width", "locale", "click_listener", "containerProps", "props", "btnContainerRef", "onSuccessRef", "onErrorRef", "promptMomentNotificationRef", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_j", "window", "google", "accounts", "id", "initialize", "callback", "credential", "select_by", "renderButton", "prompt", "cancel", "ref", "style", "height", "googleLogout", "disableAutoSelect", "useGoogleLogin", "flow", "scope", "onNonOAuthError", "overrideScope", "state", "clientRef", "onNonOAuthErrorRef", "clientMethod", "client", "oauth2", "response", "error", "error_callback", "nonOAuthError", "loginImplicitFlow", "overrideConfig", "requestAccessToken", "loginAuthCodeFlow", "requestCode", "useGoogleOneTapLogin", "cancel_on_tap_outside", "prompt_parent_id", "state_cookie_domain", "hosted_domain", "use_fedcm_for_prompt", "use_fedcm_for_button", "disabled", "auto_select", "hasGrantedAllScopesGoogle", "tokenResponse", "firstScope", "restScopes", "hasGrantedAllScopes", "hasGrantedAnyScopeGoogle", "hasGrantedAnyScope"], "sources": ["D:/TDGweb/TDG/thedesigngrit/node_modules/@react-oauth/google/dist/index.esm.js"], "sourcesContent": ["'use client'\nimport React, { useState, useRef, useEffect, createContext, useMemo, useContext, useCallback } from 'react';\n\nfunction useLoadGsiScript(options = {}) {\r\n    const { nonce, onScriptLoadSuccess, onScriptLoadError } = options;\r\n    const [scriptLoadedSuccessfully, setScriptLoadedSuccessfully] = useState(false);\r\n    const onScriptLoadSuccessRef = useRef(onScriptLoadSuccess);\r\n    onScriptLoadSuccessRef.current = onScriptLoadSuccess;\r\n    const onScriptLoadErrorRef = useRef(onScriptLoadError);\r\n    onScriptLoadErrorRef.current = onScriptLoadError;\r\n    useEffect(() => {\r\n        const scriptTag = document.createElement('script');\r\n        scriptTag.src = 'https://accounts.google.com/gsi/client';\r\n        scriptTag.async = true;\r\n        scriptTag.defer = true;\r\n        scriptTag.nonce = nonce;\r\n        scriptTag.onload = () => {\r\n            var _a;\r\n            setScriptLoadedSuccessfully(true);\r\n            (_a = onScriptLoadSuccessRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadSuccessRef);\r\n        };\r\n        scriptTag.onerror = () => {\r\n            var _a;\r\n            setScriptLoadedSuccessfully(false);\r\n            (_a = onScriptLoadErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadErrorRef);\r\n        };\r\n        document.body.appendChild(scriptTag);\r\n        return () => {\r\n            document.body.removeChild(scriptTag);\r\n        };\r\n    }, [nonce]);\r\n    return scriptLoadedSuccessfully;\r\n}\n\nconst GoogleOAuthContext = createContext(null);\r\nfunction GoogleOAuthProvider({ clientId, nonce, onScriptLoadSuccess, onScriptLoadError, children, }) {\r\n    const scriptLoadedSuccessfully = useLoadGsiScript({\r\n        nonce,\r\n        onScriptLoadSuccess,\r\n        onScriptLoadError,\r\n    });\r\n    const contextValue = useMemo(() => ({\r\n        clientId,\r\n        scriptLoadedSuccessfully,\r\n    }), [clientId, scriptLoadedSuccessfully]);\r\n    return (React.createElement(GoogleOAuthContext.Provider, { value: contextValue }, children));\r\n}\r\nfunction useGoogleOAuth() {\r\n    const context = useContext(GoogleOAuthContext);\r\n    if (!context) {\r\n        throw new Error('Google OAuth components must be used within GoogleOAuthProvider');\r\n    }\r\n    return context;\r\n}\n\nfunction extractClientId(credentialResponse) {\r\n    var _a;\r\n    const clientId = (_a = credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.clientId) !== null && _a !== void 0 ? _a : credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.client_id;\r\n    return clientId;\r\n}\n\nconst containerHeightMap = { large: 40, medium: 32, small: 20 };\r\nfunction GoogleLogin({ onSuccess, onError, useOneTap, promptMomentNotification, type = 'standard', theme = 'outline', size = 'large', text, shape, logo_alignment, width, locale, click_listener, containerProps, ...props }) {\r\n    const btnContainerRef = useRef(null);\r\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\r\n    const onSuccessRef = useRef(onSuccess);\r\n    onSuccessRef.current = onSuccess;\r\n    const onErrorRef = useRef(onError);\r\n    onErrorRef.current = onError;\r\n    const promptMomentNotificationRef = useRef(promptMomentNotification);\r\n    promptMomentNotificationRef.current = promptMomentNotification;\r\n    useEffect(() => {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\r\n        if (!scriptLoadedSuccessfully)\r\n            return;\r\n        (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.initialize({\r\n            client_id: clientId,\r\n            callback: (credentialResponse) => {\r\n                var _a;\r\n                if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\r\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\r\n                }\r\n                const { credential, select_by } = credentialResponse;\r\n                onSuccessRef.current({\r\n                    credential,\r\n                    clientId: extractClientId(credentialResponse),\r\n                    select_by,\r\n                });\r\n            },\r\n            ...props,\r\n        });\r\n        (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.renderButton(btnContainerRef.current, {\r\n            type,\r\n            theme,\r\n            size,\r\n            text,\r\n            shape,\r\n            logo_alignment,\r\n            width,\r\n            locale,\r\n            click_listener,\r\n        });\r\n        if (useOneTap)\r\n            (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\r\n        return () => {\r\n            var _a, _b, _c;\r\n            if (useOneTap)\r\n                (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\r\n        };\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [\r\n        clientId,\r\n        scriptLoadedSuccessfully,\r\n        useOneTap,\r\n        type,\r\n        theme,\r\n        size,\r\n        text,\r\n        shape,\r\n        logo_alignment,\r\n        width,\r\n        locale,\r\n    ]);\r\n    return (React.createElement(\"div\", { ...containerProps, ref: btnContainerRef, style: { height: containerHeightMap[size], ...containerProps === null || containerProps === void 0 ? void 0 : containerProps.style } }));\r\n}\n\nfunction googleLogout() {\r\n    var _a, _b, _c;\r\n    (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.disableAutoSelect();\r\n}\n\n/* eslint-disable import/export */\r\nfunction useGoogleLogin({ flow = 'implicit', scope = '', onSuccess, onError, onNonOAuthError, overrideScope, state, ...props }) {\r\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\r\n    const clientRef = useRef();\r\n    const onSuccessRef = useRef(onSuccess);\r\n    onSuccessRef.current = onSuccess;\r\n    const onErrorRef = useRef(onError);\r\n    onErrorRef.current = onError;\r\n    const onNonOAuthErrorRef = useRef(onNonOAuthError);\r\n    onNonOAuthErrorRef.current = onNonOAuthError;\r\n    useEffect(() => {\r\n        var _a, _b;\r\n        if (!scriptLoadedSuccessfully)\r\n            return;\r\n        const clientMethod = flow === 'implicit' ? 'initTokenClient' : 'initCodeClient';\r\n        const client = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2[clientMethod]({\r\n            client_id: clientId,\r\n            scope: overrideScope ? scope : `openid profile email ${scope}`,\r\n            callback: (response) => {\r\n                var _a, _b;\r\n                if (response.error)\r\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, response);\r\n                (_b = onSuccessRef.current) === null || _b === void 0 ? void 0 : _b.call(onSuccessRef, response);\r\n            },\r\n            error_callback: (nonOAuthError) => {\r\n                var _a;\r\n                (_a = onNonOAuthErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onNonOAuthErrorRef, nonOAuthError);\r\n            },\r\n            state,\r\n            ...props,\r\n        });\r\n        clientRef.current = client;\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [clientId, scriptLoadedSuccessfully, flow, scope, state]);\r\n    const loginImplicitFlow = useCallback((overrideConfig) => { var _a; return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestAccessToken(overrideConfig); }, []);\r\n    const loginAuthCodeFlow = useCallback(() => { var _a; return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestCode(); }, []);\r\n    return flow === 'implicit' ? loginImplicitFlow : loginAuthCodeFlow;\r\n}\n\nfunction useGoogleOneTapLogin({ onSuccess, onError, promptMomentNotification, cancel_on_tap_outside, prompt_parent_id, state_cookie_domain, hosted_domain, use_fedcm_for_prompt = false, use_fedcm_for_button = false, disabled, auto_select, }) {\r\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\r\n    const onSuccessRef = useRef(onSuccess);\r\n    onSuccessRef.current = onSuccess;\r\n    const onErrorRef = useRef(onError);\r\n    onErrorRef.current = onError;\r\n    const promptMomentNotificationRef = useRef(promptMomentNotification);\r\n    promptMomentNotificationRef.current = promptMomentNotification;\r\n    useEffect(() => {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\r\n        if (!scriptLoadedSuccessfully)\r\n            return;\r\n        if (disabled) {\r\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\r\n            return;\r\n        }\r\n        (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.initialize({\r\n            client_id: clientId,\r\n            callback: (credentialResponse) => {\r\n                var _a;\r\n                if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\r\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\r\n                }\r\n                const { credential, select_by } = credentialResponse;\r\n                onSuccessRef.current({\r\n                    credential,\r\n                    clientId: extractClientId(credentialResponse),\r\n                    select_by,\r\n                });\r\n            },\r\n            hosted_domain,\r\n            cancel_on_tap_outside,\r\n            prompt_parent_id,\r\n            state_cookie_domain,\r\n            use_fedcm_for_prompt,\r\n            use_fedcm_for_button,\r\n            auto_select,\r\n        });\r\n        (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\r\n        return () => {\r\n            var _a, _b, _c;\r\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\r\n        };\r\n    }, [\r\n        clientId,\r\n        scriptLoadedSuccessfully,\r\n        cancel_on_tap_outside,\r\n        prompt_parent_id,\r\n        state_cookie_domain,\r\n        hosted_domain,\r\n        use_fedcm_for_prompt,\r\n        use_fedcm_for_button,\r\n        disabled,\r\n        auto_select,\r\n    ]);\r\n}\n\n/**\r\n * Checks if the user granted all the specified scope or scopes\r\n * @returns True if all the scopes are granted\r\n */\r\nfunction hasGrantedAllScopesGoogle(tokenResponse, firstScope, ...restScopes) {\r\n    var _a, _b, _c;\r\n    if (!(window === null || window === void 0 ? void 0 : window.google))\r\n        return false;\r\n    return (((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAllScopes(tokenResponse, firstScope, ...restScopes)) || false);\r\n}\n\n/**\r\n * Checks if the user granted any of the specified scope or scopes.\r\n * @returns True if any of the scopes are granted\r\n */\r\nfunction hasGrantedAnyScopeGoogle(tokenResponse, firstScope, ...restScopes) {\r\n    var _a, _b, _c;\r\n    if (!(window === null || window === void 0 ? void 0 : window.google))\r\n        return false;\r\n    return (((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAnyScope(tokenResponse, firstScope, ...restScopes)) || false);\r\n}\n\nexport { GoogleLogin, GoogleOAuthProvider, googleLogout, hasGrantedAllScopesGoogle, hasGrantedAnyScopeGoogle, useGoogleLogin, useGoogleOAuth, useGoogleOneTapLogin };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,aAAa,EAAEC,OAAO,EAAEC,UAAU,EAAEC,WAAW,QAAQ,OAAO;AAE3G,SAASC,gBAAgBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpC,MAAM;IAAEC,KAAK;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAAGH,OAAO;EACjE,MAAM,CAACI,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAMc,sBAAsB,GAAGb,MAAM,CAACS,mBAAmB,CAAC;EAC1DI,sBAAsB,CAACC,OAAO,GAAGL,mBAAmB;EACpD,MAAMM,oBAAoB,GAAGf,MAAM,CAACU,iBAAiB,CAAC;EACtDK,oBAAoB,CAACD,OAAO,GAAGJ,iBAAiB;EAChDT,SAAS,CAAC,MAAM;IACZ,MAAMe,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAClDF,SAAS,CAACG,GAAG,GAAG,wCAAwC;IACxDH,SAAS,CAACI,KAAK,GAAG,IAAI;IACtBJ,SAAS,CAACK,KAAK,GAAG,IAAI;IACtBL,SAAS,CAACR,KAAK,GAAGA,KAAK;IACvBQ,SAAS,CAACM,MAAM,GAAG,MAAM;MACrB,IAAIC,EAAE;MACNX,2BAA2B,CAAC,IAAI,CAAC;MACjC,CAACW,EAAE,GAAGV,sBAAsB,CAACC,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACX,sBAAsB,CAAC;IAC9G,CAAC;IACDG,SAAS,CAACS,OAAO,GAAG,MAAM;MACtB,IAAIF,EAAE;MACNX,2BAA2B,CAAC,KAAK,CAAC;MAClC,CAACW,EAAE,GAAGR,oBAAoB,CAACD,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACT,oBAAoB,CAAC;IAC1G,CAAC;IACDE,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACX,SAAS,CAAC;IACpC,OAAO,MAAM;MACTC,QAAQ,CAACS,IAAI,CAACE,WAAW,CAACZ,SAAS,CAAC;IACxC,CAAC;EACL,CAAC,EAAE,CAACR,KAAK,CAAC,CAAC;EACX,OAAOG,wBAAwB;AACnC;AAEA,MAAMkB,kBAAkB,GAAG3B,aAAa,CAAC,IAAI,CAAC;AAC9C,SAAS4B,mBAAmBA,CAAC;EAAEC,QAAQ;EAAEvB,KAAK;EAAEC,mBAAmB;EAAEC,iBAAiB;EAAEsB;AAAU,CAAC,EAAE;EACjG,MAAMrB,wBAAwB,GAAGL,gBAAgB,CAAC;IAC9CE,KAAK;IACLC,mBAAmB;IACnBC;EACJ,CAAC,CAAC;EACF,MAAMuB,YAAY,GAAG9B,OAAO,CAAC,OAAO;IAChC4B,QAAQ;IACRpB;EACJ,CAAC,CAAC,EAAE,CAACoB,QAAQ,EAAEpB,wBAAwB,CAAC,CAAC;EACzC,OAAQb,KAAK,CAACoB,aAAa,CAACW,kBAAkB,CAACK,QAAQ,EAAE;IAAEC,KAAK,EAAEF;EAAa,CAAC,EAAED,QAAQ,CAAC;AAC/F;AACA,SAASI,cAAcA,CAAA,EAAG;EACtB,MAAMC,OAAO,GAAGjC,UAAU,CAACyB,kBAAkB,CAAC;EAC9C,IAAI,CAACQ,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,iEAAiE,CAAC;EACtF;EACA,OAAOD,OAAO;AAClB;AAEA,SAASE,eAAeA,CAACC,kBAAkB,EAAE;EACzC,IAAIjB,EAAE;EACN,MAAMQ,QAAQ,GAAG,CAACR,EAAE,GAAGiB,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACT,QAAQ,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGiB,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,SAAS;EACjQ,OAAOV,QAAQ;AACnB;AAEA,MAAMW,kBAAkB,GAAG;EAAEC,KAAK,EAAE,EAAE;EAAEC,MAAM,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAG,CAAC;AAC/D,SAASC,WAAWA,CAAC;EAAEC,SAAS;EAAEC,OAAO;EAAEC,SAAS;EAAEC,wBAAwB;EAAEC,IAAI,GAAG,UAAU;EAAEC,KAAK,GAAG,SAAS;EAAEC,IAAI,GAAG,OAAO;EAAEC,IAAI;EAAEC,KAAK;EAAEC,cAAc;EAAEC,KAAK;EAAEC,MAAM;EAAEC,cAAc;EAAEC,cAAc;EAAE,GAAGC;AAAM,CAAC,EAAE;EAC1N,MAAMC,eAAe,GAAG9D,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM;IAAE+B,QAAQ;IAAEpB;EAAyB,CAAC,GAAGyB,cAAc,CAAC,CAAC;EAC/D,MAAM2B,YAAY,GAAG/D,MAAM,CAAC+C,SAAS,CAAC;EACtCgB,YAAY,CAACjD,OAAO,GAAGiC,SAAS;EAChC,MAAMiB,UAAU,GAAGhE,MAAM,CAACgD,OAAO,CAAC;EAClCgB,UAAU,CAAClD,OAAO,GAAGkC,OAAO;EAC5B,MAAMiB,2BAA2B,GAAGjE,MAAM,CAACkD,wBAAwB,CAAC;EACpEe,2BAA2B,CAACnD,OAAO,GAAGoC,wBAAwB;EAC9DjD,SAAS,CAAC,MAAM;IACZ,IAAIsB,EAAE,EAAE2C,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACtC,IAAI,CAAC9D,wBAAwB,EACzB;IACJ,CAACwD,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,UAAU,CAAC;MAC9NrC,SAAS,EAAEV,QAAQ;MACnBgD,QAAQ,EAAGvC,kBAAkB,IAAK;QAC9B,IAAIjB,EAAE;QACN,IAAI,EAAEiB,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACwC,UAAU,CAAC,EAAE;UAC1G,OAAO,CAACzD,EAAE,GAAGyC,UAAU,CAAClD,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACwC,UAAU,CAAC;QAC7F;QACA,MAAM;UAAEgB,UAAU;UAAEC;QAAU,CAAC,GAAGzC,kBAAkB;QACpDuB,YAAY,CAACjD,OAAO,CAAC;UACjBkE,UAAU;UACVjD,QAAQ,EAAEQ,eAAe,CAACC,kBAAkB,CAAC;UAC7CyC;QACJ,CAAC,CAAC;MACN,CAAC;MACD,GAAGpB;IACP,CAAC,CAAC;IACF,CAACS,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,QAAQ,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,EAAE,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,YAAY,CAACpB,eAAe,CAAChD,OAAO,EAAE;MACzPqC,IAAI;MACJC,KAAK;MACLC,IAAI;MACJC,IAAI;MACJC,KAAK;MACLC,cAAc;MACdC,KAAK;MACLC,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,IAAIV,SAAS,EACT,CAACwB,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,QAAQ,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,EAAE,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,MAAM,CAAClB,2BAA2B,CAACnD,OAAO,CAAC;IACtQ,OAAO,MAAM;MACT,IAAIS,EAAE,EAAE2C,EAAE,EAAEC,EAAE;MACd,IAAIlB,SAAS,EACT,CAACkB,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,MAAM,CAAC,CAAC;IACvO,CAAC;IACD;EACJ,CAAC,EAAE,CACCrD,QAAQ,EACRpB,wBAAwB,EACxBsC,SAAS,EACTE,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,cAAc,EACdC,KAAK,EACLC,MAAM,CACT,CAAC;EACF,OAAQ5D,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAAE,GAAG0C,cAAc;IAAEyB,GAAG,EAAEvB,eAAe;IAAEwB,KAAK,EAAE;MAAEC,MAAM,EAAE7C,kBAAkB,CAACW,IAAI,CAAC;MAAE,IAAGO,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC0B,KAAK;IAAC;EAAE,CAAC,CAAC;AACzN;AAEA,SAASE,YAAYA,CAAA,EAAG;EACpB,IAAIjE,EAAE,EAAE2C,EAAE,EAAEC,EAAE;EACd,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,iBAAiB,CAAC,CAAC;AAC9O;;AAEA;AACA,SAASC,cAAcA,CAAC;EAAEC,IAAI,GAAG,UAAU;EAAEC,KAAK,GAAG,EAAE;EAAE7C,SAAS;EAAEC,OAAO;EAAE6C,eAAe;EAAEC,aAAa;EAAEC,KAAK;EAAE,GAAGlC;AAAM,CAAC,EAAE;EAC5H,MAAM;IAAE9B,QAAQ;IAAEpB;EAAyB,CAAC,GAAGyB,cAAc,CAAC,CAAC;EAC/D,MAAM4D,SAAS,GAAGhG,MAAM,CAAC,CAAC;EAC1B,MAAM+D,YAAY,GAAG/D,MAAM,CAAC+C,SAAS,CAAC;EACtCgB,YAAY,CAACjD,OAAO,GAAGiC,SAAS;EAChC,MAAMiB,UAAU,GAAGhE,MAAM,CAACgD,OAAO,CAAC;EAClCgB,UAAU,CAAClD,OAAO,GAAGkC,OAAO;EAC5B,MAAMiD,kBAAkB,GAAGjG,MAAM,CAAC6F,eAAe,CAAC;EAClDI,kBAAkB,CAACnF,OAAO,GAAG+E,eAAe;EAC5C5F,SAAS,CAAC,MAAM;IACZ,IAAIsB,EAAE,EAAE2C,EAAE;IACV,IAAI,CAACvD,wBAAwB,EACzB;IACJ,MAAMuF,YAAY,GAAGP,IAAI,KAAK,UAAU,GAAG,iBAAiB,GAAG,gBAAgB;IAC/E,MAAMQ,MAAM,GAAG,CAACjC,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkC,MAAM,CAACF,YAAY,CAAC,CAAC;MACrMzD,SAAS,EAAEV,QAAQ;MACnB6D,KAAK,EAAEE,aAAa,GAAGF,KAAK,GAAG,wBAAwBA,KAAK,EAAE;MAC9Db,QAAQ,EAAGsB,QAAQ,IAAK;QACpB,IAAI9E,EAAE,EAAE2C,EAAE;QACV,IAAImC,QAAQ,CAACC,KAAK,EACd,OAAO,CAAC/E,EAAE,GAAGyC,UAAU,CAAClD,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACwC,UAAU,EAAEqC,QAAQ,CAAC;QACvG,CAACnC,EAAE,GAAGH,YAAY,CAACjD,OAAO,MAAM,IAAI,IAAIoD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,IAAI,CAACuC,YAAY,EAAEsC,QAAQ,CAAC;MACpG,CAAC;MACDE,cAAc,EAAGC,aAAa,IAAK;QAC/B,IAAIjF,EAAE;QACN,CAACA,EAAE,GAAG0E,kBAAkB,CAACnF,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACyE,kBAAkB,EAAEO,aAAa,CAAC;MACrH,CAAC;MACDT,KAAK;MACL,GAAGlC;IACP,CAAC,CAAC;IACFmC,SAAS,CAAClF,OAAO,GAAGqF,MAAM;IAC1B;EACJ,CAAC,EAAE,CAACpE,QAAQ,EAAEpB,wBAAwB,EAAEgF,IAAI,EAAEC,KAAK,EAAEG,KAAK,CAAC,CAAC;EAC5D,MAAMU,iBAAiB,GAAGpG,WAAW,CAAEqG,cAAc,IAAK;IAAE,IAAInF,EAAE;IAAE,OAAO,CAACA,EAAE,GAAGyE,SAAS,CAAClF,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoF,kBAAkB,CAACD,cAAc,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;EACtL,MAAME,iBAAiB,GAAGvG,WAAW,CAAC,MAAM;IAAE,IAAIkB,EAAE;IAAE,OAAO,CAACA,EAAE,GAAGyE,SAAS,CAAClF,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsF,WAAW,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;EACnJ,OAAOlB,IAAI,KAAK,UAAU,GAAGc,iBAAiB,GAAGG,iBAAiB;AACtE;AAEA,SAASE,oBAAoBA,CAAC;EAAE/D,SAAS;EAAEC,OAAO;EAAEE,wBAAwB;EAAE6D,qBAAqB;EAAEC,gBAAgB;EAAEC,mBAAmB;EAAEC,aAAa;EAAEC,oBAAoB,GAAG,KAAK;EAAEC,oBAAoB,GAAG,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAC7O,MAAM;IAAEvF,QAAQ;IAAEpB;EAAyB,CAAC,GAAGyB,cAAc,CAAC,CAAC;EAC/D,MAAM2B,YAAY,GAAG/D,MAAM,CAAC+C,SAAS,CAAC;EACtCgB,YAAY,CAACjD,OAAO,GAAGiC,SAAS;EAChC,MAAMiB,UAAU,GAAGhE,MAAM,CAACgD,OAAO,CAAC;EAClCgB,UAAU,CAAClD,OAAO,GAAGkC,OAAO;EAC5B,MAAMiB,2BAA2B,GAAGjE,MAAM,CAACkD,wBAAwB,CAAC;EACpEe,2BAA2B,CAACnD,OAAO,GAAGoC,wBAAwB;EAC9DjD,SAAS,CAAC,MAAM;IACZ,IAAIsB,EAAE,EAAE2C,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACtC,IAAI,CAAC9D,wBAAwB,EACzB;IACJ,IAAI0G,QAAQ,EAAE;MACV,CAAClD,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,MAAM,CAAC,CAAC;MAC/N;IACJ;IACA,CAACd,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,QAAQ,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,EAAE,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,UAAU,CAAC;MAC9NrC,SAAS,EAAEV,QAAQ;MACnBgD,QAAQ,EAAGvC,kBAAkB,IAAK;QAC9B,IAAIjB,EAAE;QACN,IAAI,EAAEiB,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACwC,UAAU,CAAC,EAAE;UAC1G,OAAO,CAACzD,EAAE,GAAGyC,UAAU,CAAClD,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACwC,UAAU,CAAC;QAC7F;QACA,MAAM;UAAEgB,UAAU;UAAEC;QAAU,CAAC,GAAGzC,kBAAkB;QACpDuB,YAAY,CAACjD,OAAO,CAAC;UACjBkE,UAAU;UACVjD,QAAQ,EAAEQ,eAAe,CAACC,kBAAkB,CAAC;UAC7CyC;QACJ,CAAC,CAAC;MACN,CAAC;MACDiC,aAAa;MACbH,qBAAqB;MACrBC,gBAAgB;MAChBC,mBAAmB;MACnBE,oBAAoB;MACpBC,oBAAoB;MACpBE;IACJ,CAAC,CAAC;IACF,CAAC7C,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,QAAQ,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,EAAE,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,MAAM,CAAClB,2BAA2B,CAACnD,OAAO,CAAC;IAClQ,OAAO,MAAM;MACT,IAAIS,EAAE,EAAE2C,EAAE,EAAEC,EAAE;MACd,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,MAAM,CAAC,CAAC;IACnO,CAAC;EACL,CAAC,EAAE,CACCrD,QAAQ,EACRpB,wBAAwB,EACxBoG,qBAAqB,EACrBC,gBAAgB,EAChBC,mBAAmB,EACnBC,aAAa,EACbC,oBAAoB,EACpBC,oBAAoB,EACpBC,QAAQ,EACRC,WAAW,CACd,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAACC,aAAa,EAAEC,UAAU,EAAE,GAAGC,UAAU,EAAE;EACzE,IAAInG,EAAE,EAAE2C,EAAE,EAAEC,EAAE;EACd,IAAI,EAAEO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,CAAC,EAChE,OAAO,KAAK;EAChB,OAAQ,CAAC,CAACR,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkC,MAAM,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwD,mBAAmB,CAACH,aAAa,EAAEC,UAAU,EAAE,GAAGC,UAAU,CAAC,KAAK,KAAK;AAC/S;;AAEA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAACJ,aAAa,EAAEC,UAAU,EAAE,GAAGC,UAAU,EAAE;EACxE,IAAInG,EAAE,EAAE2C,EAAE,EAAEC,EAAE;EACd,IAAI,EAAEO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,CAAC,EAChE,OAAO,KAAK;EAChB,OAAQ,CAAC,CAACR,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC3C,EAAE,GAAGmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,MAAM,IAAI,IAAIpD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkC,MAAM,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0D,kBAAkB,CAACL,aAAa,EAAEC,UAAU,EAAE,GAAGC,UAAU,CAAC,KAAK,KAAK;AAC9S;AAEA,SAAS5E,WAAW,EAAEhB,mBAAmB,EAAE0D,YAAY,EAAE+B,yBAAyB,EAAEK,wBAAwB,EAAElC,cAAc,EAAEtD,cAAc,EAAE0E,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}